// Code generated by quicksilver-cli. DO NOT EDIT.
package account

import (
	context "context"
	"golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	"google.golang.org/grpc"
)

//go:generate mockgen --destination=../mocks/account/iclient.go --package=account golang.52tt.com/clients/account IClient
type IClient interface {
	client.BaseClient
	AutoBatchCreateUser(ctx context.Context, cnt uint32, source uint32, pwdMd5List []string) ([]*accountPB.UserResp, error)
	BatGetUserByUid(ctx context.Context, uidList ...uint32) (map[uint32]*accountPB.UserResp, protocol.ServerError)
	BatchQueryUidList(ctx context.Context, accounts []string) ([]*accountPB.KeyToUidPair, protocol.ServerError)
	BatchQueryUidListByAccount(ctx context.Context, accounts []string) ([]*accountPB.KeyToUidPair, protocol.ServerError)
	BatchQueryUidListByAlias(ctx context.Context, alias []string) ([]*accountPB.KeyToUidPair, protocol.ServerError)
	BatchQueryUidListByType(ctx context.Context, queryType uint32, accounts []string) ([]*accountPB.KeyToUidPair, protocol.ServerError)
	BindPhoneWithUid(ctx context.Context, req *accountPB.BindPhoneWithUidReq) protocol.ServerError
	CheckUserHaveGuild(ctx context.Context, uid uint32) (uint32, protocol.ServerError)
	CreateUser(ctx context.Context, req *accountPB.CreateUserReq) (*accountPB.UidResp, error)
	CreateUserForOpenid(ctx context.Context, req *accountPB.CreateUserForOpenidReq) (*accountPB.CreateUserForOpenidResp, error)
	DetachThirdpartyIDs(ctx context.Context, uid uint32) ([]string, error)
	GetLastLoginTimeAndNickname(ctx context.Context, uid uint32) (string, string, protocol.ServerError)
	GetNextUid(ctx context.Context) (uint64, protocol.ServerError)
	GetNicknameByUid(ctx context.Context, uid uint32) (string, protocol.ServerError)
	GetRegInviteHistory(ctx context.Context, uid uint32, isInvited bool) (*accountPB.GetRegInviteHistoryResp, protocol.ServerError)
	GetTodayUserBehavior(ctx context.Context, uid, behaviorType uint32) (*accountPB.GetTodayUserBehaviorResp, protocol.ServerError)
	GetUidByAlias(ctx context.Context, name string) (uint32, string, protocol.ServerError)
	GetUidByName(ctx context.Context, name string) (uint32, string, protocol.ServerError)
	GetUidByOpenidInOrderedScenes(ctx context.Context, thirdPartyType accountPB.THIRD_PARTY_TYPE, openId string, scenes []string) (uint32, protocol.ServerError)
	GetUidByOpenidWithScene(ctx context.Context, thirdPartyType accountPB.THIRD_PARTY_TYPE, openId string, scene string) (uint32, protocol.ServerError)
	GetUidByPhone(ctx context.Context, phone string) (uint32, string, protocol.ServerError)
	GetUidByPhoneInOrderedScenes(ctx context.Context, phone string, scenes []string) (uint32, protocol.ServerError)
	GetUidByPhoneWithScene(ctx context.Context, phone, scene string) (uint32, protocol.ServerError)
	GetUser(ctx context.Context, uid uint32) (*User, protocol.ServerError)
	GetUserActivateInfo(ctx context.Context, uid uint32) (string, string, protocol.ServerError)
	GetUserByUid(ctx context.Context, uid uint32) (*accountPB.UserResp, protocol.ServerError)
	GetUserGuild(ctx context.Context, uid uint32, guildId *uint32) protocol.ServerError
	GetUserThirdpartyIDs(ctx context.Context, uid uint32) ([]string, error)
	GetUserWithUin(ctx context.Context, uin, uid uint32) (*User, protocol.ServerError)
	GetUsersByAccs(ctx context.Context, uid uint32, req *accountPB.GetUsersByAccsReq) (*accountPB.GetUsersByAccsResp, protocol.ServerError)
	GetUsersMap(ctx context.Context, uidList []uint32) (map[uint32]*User, protocol.ServerError)
	LogUserBehavior(ctx context.Context, uid, behaviorType uint32, detail string) (*accountPB.LogUserBehaviorResp, protocol.ServerError)
	RebindPhoneWithUid(ctx context.Context, uid uint32, phone string, isBind bool, bindType accountPB.PhoneBindType) protocol.ServerError
	RecycleUsername(ctx context.Context, username string) (bool, bool, string, error)
	UnbindPhoneWithUid(ctx context.Context, uid uint32, phone string) protocol.ServerError
	UpdateAlias(ctx context.Context, uid uint32, newAlias string, isForce bool) error
	UpdateLastLoginAt(ctx context.Context, uid uint32, lastLoginAt uint64) error
	UpdateNickname(ctx context.Context, uid uint32, nickname string) protocol.ServerError
	UpdatePassword(ctx context.Context, uid uint32, password string) protocol.ServerError
	UpdateSex(ctx context.Context, uid, sex uint32) protocol.ServerError
	UpdateSignature(ctx context.Context, uid uint32, signature string) protocol.ServerError
	UpdateUserCurrentGuild(ctx context.Context, uid uint32, guildId uint32) protocol.ServerError
	UpdateUserGuildPrefixByGuildId(ctx context.Context, guildId uint32, prefix string) protocol.ServerError
	UpdateUserPrefixValid(ctx context.Context, uid uint32, prefix bool) protocol.ServerError
	UpdateUserTickDeviceId(ctx context.Context, uid uint32, deviceId []byte) error
	UpdateUserType(ctx context.Context, uid uint32, newType uint32) error
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
