package account

import (
	"context"
	"strconv"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"

	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"

	// "golang.52tt.com/pkg/foundation/grpc/load_balance"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
)

const (
	serviceName = "account"

	Male   = int32(accountPB.USER_SEX_USER_SEX_MALE)
	Female = int32(accountPB.USER_SEX_USER_SEX_FEMALE)
)

type User = accountPB.UserResp

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return accountPB.NewAccountClient(cc)
		}, dopts...),
	}, nil
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() accountPB.AccountClient { return c.Stub().(accountPB.AccountClient) }

func (c *Client) GetUser(ctx context.Context, uid uint32) (*User, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().GetUserByUid(ctx, &accountPB.UidReq{Uid: uid})
	return r, protocol.ToServerError(err)
}

func (c *Client) GetUserWithUin(ctx context.Context, uin, uid uint32) (*User, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().GetUserByUid(ctx, &accountPB.UidReq{Uid: uid})
	return r, protocol.ToServerError(err)
}

func (c *Client) GetUidByName(ctx context.Context, name string) (uint32, string, protocol.ServerError) {
	r, err := c.typedStub().GetUidByName(ctx, &accountPB.GetUidByNameReq{Username: name})
	return r.GetUid(), r.GetUsername(), protocol.ToServerError(err)
}

func (c *Client) GetUidByAlias(ctx context.Context, name string) (uint32, string, protocol.ServerError) {
	r, err := c.typedStub().GetUidByName(ctx, &accountPB.GetUidByNameReq{Username: name, NameType: uint32(accountPB.GetUidByNameReq_TYPE_ALIAS)})
	return r.GetUid(), r.GetUsername(), protocol.ToServerError(err)
}

//func (c *Client) GetNameByUid(ctx context.Context, uid uint32) (string, protocol.ServerError) {
//	r, err := c.typedStub.GetUsernameByUid(ctx, 0, &accountPB.UidReq{Uid: uid})
//	return r.GetString_(), protocol.ToServerError(err)
//}

func (c *Client) GetUidByPhone(ctx context.Context, phone string) (uint32, string, protocol.ServerError) {
	r, err := c.typedStub().GetUidByPhone(ctx, &accountPB.GetUidByPhoneReq{Phone: phone})
	return r.GetUid(), r.GetUsername(), protocol.ToServerError(err)
}

func (c *Client) GetUidByPhoneWithScene(ctx context.Context, phone, scene string) (uint32, protocol.ServerError) {
	req := &accountPB.GetUidByPhoneReq{
		Phone: phone,
		Scene: scene,
	}
	resp, err := c.typedStub().GetUidByPhone(ctx, req)
	return resp.GetUid(), protocol.ToServerError(err)
}

func (c *Client) GetUidByPhoneInOrderedScenes(ctx context.Context, phone string, scenes []string) (uint32, protocol.ServerError) {
	req := &accountPB.GetUidListByPhoneReq{
		Phone:     phone,
		SceneList: scenes,
	}
	resp, err := c.typedStub().GetUidListByPhone(ctx, req)
	if err != nil {
		return 0, protocol.ToServerError(err)
	}

	for _, reqScene := range scenes {
		for _, respScene := range resp.GetList() {
			if reqScene == respScene.Scene {
				log.DebugWithCtx(ctx, "GetUidByPhoneInOrderedScenes phone:%s, find scene:%s, resp:%+v", phone, reqScene, resp)
				return uint32(respScene.GetUid()), nil
			}
		}
	}

	return 0, protocol.ToServerError(err)
}

func (c *Client) GetUidByOpenidWithScene(ctx context.Context, thirdPartyType accountPB.THIRD_PARTY_TYPE, openId string,
	scene string) (uint32, protocol.ServerError) {
	req := &accountPB.GetUidByOpenidReq{
		ThirdPartyType: uint32(thirdPartyType),
		Openid:         openId,
		Scene:          scene,
	}
	resp, err := c.typedStub().GetUidByOpenid(ctx, req)
	return resp.GetUid(), protocol.ToServerError(err)
}

func (c *Client) GetUidByOpenidInOrderedScenes(ctx context.Context, thirdPartyType accountPB.THIRD_PARTY_TYPE, openId string,
	scenes []string) (uint32, protocol.ServerError) {
	req := &accountPB.GetUidListByOpenidReq{
		ThirdPartyType: uint32(thirdPartyType),
		Openid:         openId,
		SceneList:      scenes,
	}
	resp, err := c.typedStub().GetUidListByOpenid(ctx, req)
	if err != nil {
		return 0, protocol.ToServerError(err)
	}

	for _, reqScene := range scenes {
		for _, respScene := range resp.GetList() {
			if reqScene == respScene.Scene {
				log.DebugWithCtx(ctx, "GetUidByOpenidInOrderedScenes type:%v, openid:%s, find scene:%s, resp:%+v",
					thirdPartyType, openId, reqScene, resp)
				return uint32(respScene.GetUid()), nil
			}
		}
	}

	return 0, protocol.ToServerError(err)
}

func (c *Client) GetUsersMap(ctx context.Context, uidList []uint32) (map[uint32]*User, protocol.ServerError) {
	userMap := make(map[uint32]*User)
	if len(uidList) > 0 {
		r, err := c.typedStub().GetUsersByUids(ctx, &accountPB.UidsReq{UidList: uidList})
		if err != nil {
			return userMap, protocol.ToServerError(err)
		}
		for _, u := range r.UserList {
			userMap[u.GetUid()] = u
		}
	}
	return userMap, nil
}

/*
// OfficialCertify相关接口 已经挪动到 officialcert 服务
func (c *Client) GetUserOfficialCertifyByUids(ctx context.Context, uids []uint32) ([]*accountPB.OfficialCertifyInfo, protocol.ServerError) {
	r, err := c.typedStub().GetUserOfficialCertifyByUids(ctx, &accountPB.GetUserOfficialCertifyByUidsReq{Uids: uids})
	return r.GetInfoList(), protocol.ToServerError(err)
}

func (c *Client) SetUserOfficialCertify(ctx context.Context, in *accountPB.OfficialCertifyInfo) protocol.ServerError {
	_, err := c.typedStub().SetUserOfficialCertify(ctx, in)
	return protocol.ToServerError(err)
}

func (c *Client) DelUserOfficialCertify(ctx context.Context, in *accountPB.GetUserOfficialCertifyReq) protocol.ServerError {
	_, err := c.typedStub().DelUserOfficialCertify(ctx, in)
	return protocol.ToServerError(err)
}
*/

func (c *Client) GetUserByUid(ctx context.Context, uid uint32) (*accountPB.UserResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().GetUserByUid(ctx, &accountPB.UidReq{
		Uid: uid,
	})

	return r, protocol.ToServerError(err)
}
func (c *Client) BatGetUserByUid(ctx context.Context, uidList ...uint32) (map[uint32]*accountPB.UserResp, protocol.ServerError) {

	r, err := c.typedStub().GetUsersByUids(ctx, &accountPB.UidsReq{
		UidList: uidList,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	result := make(map[uint32]*accountPB.UserResp)
	for _, user := range r.UserList {
		result[user.Uid] = user
	}

	return result, nil
}

func (c *Client) BatchQueryUidListByType(ctx context.Context, queryType uint32, accounts []string) ([]*accountPB.KeyToUidPair, protocol.ServerError) {

	req := accountPB.BatchQueryUidListReq{KeyList: make([]string, 0)}
	req.KeyList = accounts
	req.QueryType = queryType
	r, err := c.typedStub().BatchQueryUidList(ctx, &req)

	return r.GetResultList(), protocol.ToServerError(err)
}

func (c *Client) BatchQueryUidList(ctx context.Context, accounts []string) ([]*accountPB.KeyToUidPair, protocol.ServerError) {

	req := accountPB.BatchQueryUidListReq{KeyList: make([]string, 0)}
	req.KeyList = accounts
	req.QueryType = uint32(accountPB.BatchQueryUidListReq_QUERY_BY_ACCOUNT)
	r, err := c.typedStub().BatchQueryUidList(ctx, &req)

	return r.GetResultList(), protocol.ToServerError(err)
}

func (c *Client) BatchQueryUidListByAlias(ctx context.Context, alias []string) ([]*accountPB.KeyToUidPair, protocol.ServerError) {

	req := accountPB.BatchQueryUidListReq{KeyList: make([]string, 0)}
	req.KeyList = alias
	req.QueryType = uint32(accountPB.BatchQueryUidListReq_QUERY_BY_ALIAS)
	r, err := c.typedStub().BatchQueryUidList(ctx, &req)

	return r.GetResultList(), protocol.ToServerError(err)
}

func (c *Client) BatchQueryUidListByAccount(ctx context.Context, accounts []string) ([]*accountPB.KeyToUidPair, protocol.ServerError) {

	req := accountPB.BatchQueryUidListReq{KeyList: make([]string, 0)}
	req.KeyList = accounts
	req.QueryType = uint32(accountPB.BatchQueryUidListReq_QUERY_BY_ACCOUNT)
	r, err := c.typedStub().BatchQueryUidList(ctx, &req)

	return r.GetResultList(), protocol.ToServerError(err)
}

func (c *Client) UpdateNickname(ctx context.Context, uid uint32, nickname string) protocol.ServerError {
	log.InfoWithCtx(ctx, "UpdateNickname uid:%d, nickname:%s", uid, nickname)
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().UpdateUserNickname(ctx, &accountPB.UpdateUserNicknameReq{
		Uid:      uid,
		Nickname: nickname,
	})

	return protocol.ToServerError(err)
}

func (c *Client) UpdateSignature(ctx context.Context, uid uint32, signature string) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().UpdateUserSignature(ctx, &accountPB.UpdateUserSignatureReq{
		Uid:       uid,
		Signature: signature,
	})

	return protocol.ToServerError(err)
}

func (c *Client) UpdatePassword(ctx context.Context, uid uint32, password string) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().UpdatePassword(ctx, &accountPB.UpdatePasswordReq{
		Uid:      uid,
		Password: password,
	})

	return protocol.ToServerError(err)
}

func (c *Client) RebindPhoneWithUid(ctx context.Context, uid uint32, phone string, isBind bool, bindType accountPB.PhoneBindType) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().RebindPhoneWithUid(ctx, &accountPB.RebindPhoneWithUidReq{
		Uid:      uid,
		Phone:    phone,
		IsBind:   isBind,
		BindType: bindType,
	})

	return protocol.ToServerError(err)
}

func (c *Client) UnbindPhoneWithUid(ctx context.Context, uid uint32, phone string) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().UnbindPhoneWithUid(ctx, &accountPB.UnbindPhoneWithUidReq{
		Uid:   uid,
		Phone: phone,
	})

	return protocol.ToServerError(err)
}

func (c *Client) GetLastLoginTimeAndNickname(ctx context.Context, uid uint32) (string, string, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().GetLastLoginTimeAndNickname(ctx, &accountPB.UidReq{
		Uid: uid,
	})

	return r.GetLogintime(), r.GetNickname(), protocol.ToServerError(err)
}

func (c *Client) GetUserActivateInfo(ctx context.Context, uid uint32) (string, string, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().GetUserActivateInfo(ctx, &accountPB.GetUserActivateInfoReq{
		Uid: uid,
	})

	return r.GetInfo().GetChannel(), r.GetInfo().GetSource(), protocol.ToServerError(err)
}

func (c *Client) CreateUser(ctx context.Context, req *accountPB.CreateUserReq) (*accountPB.UidResp, error) {
	r, err := c.typedStub().CreateUser(ctx, req)
	return r, protocol.ToServerError(err)
}

func (c *Client) CreateUserForOpenid(ctx context.Context, req *accountPB.CreateUserForOpenidReq) (*accountPB.CreateUserForOpenidResp, error) {
	r, err := c.typedStub().CreateUserForOpenid(ctx, req)
	return r, protocol.ToServerError(err)
}

/*
// 相册接口 已经挪动到 photoalbumsvr 服务
func (c *Client) GetPhotoAlbum(ctx context.Context, uid uint32) (*accountPB.GetPhotoAlbumResp, error) {
	r, err := c.typedStub().GetPhotoAlbum(ctx, &accountPB.GetPhotoAlbumReq{
		Uid: uid,
	})
	return r, protocol.ToServerError(err)
}
*/

func (c *Client) GetUserThirdpartyIDs(ctx context.Context, uid uint32) ([]string, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().GetUserThirdpartyIDs(ctx, &accountPB.GetUserThirdpartyIDsReq{
		Uid: uid,
	})
	return r.GetThirdpartyIdList(), protocol.ToServerError(err)
}

func (c *Client) DetachThirdpartyIDs(ctx context.Context, uid uint32) ([]string, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().DetachThirdParty(ctx, &accountPB.DetachThirdPartyReq{
		Uid: uid,
	})
	return r.GetThirdpartyIdList(), protocol.ToServerError(err)
}

// RecycleUsername 回收掉指定Username的TTID
// 回收掉之后 如果之前帐号是存在的，那么会分配一个新的TTID给那个帐号
// 返回值 是否是userName被回收，是否是TTID（aliens）被回收，新的Username是什么，错误码
func (c *Client) RecycleUsername(ctx context.Context, username string) (bool, bool, string, error) {
	r, err := c.typedStub().RecycleUsername(ctx, &accountPB.RecycleUsernameReq{
		Username: username,
	})
	if err != nil {
		return false, false, "", protocol.ToServerError(err)
	}
	return r.AccountRecycled, r.AliasRecycled, r.NewUsername, nil
}

// UpdateAlias 更新指定UID的TTID
func (c *Client) UpdateAlias(ctx context.Context, uid uint32, newAlias string, isForce bool) error {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().UpdateAlias(ctx, &accountPB.UpdateAliasReq{
		Uid:         uid,
		Alias:       newAlias,
		ForceUpdate: isForce,
	})
	return protocol.ToServerError(err)
}

// UpdateUserType 更新指定UID的UserType
func (c *Client) UpdateUserType(ctx context.Context, uid uint32, newType uint32) error {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().UpdateUserType(ctx, &accountPB.UpdateTypeReq{
		Uid:  uid,
		Type: newType,
	})
	return protocol.ToServerError(err)
}

func (c *Client) UpdateLastLoginAt(ctx context.Context, uid uint32, lastLoginAt uint64) error {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().UpdateLastLoginAt(ctx, &accountPB.UpdateLastLoginAtReq{
		Uid:         uid,
		LastLoginAt: lastLoginAt,
	})
	return protocol.ToServerError(err)
}

// AutoBatchCreateUser cnt 标识需要创建的帐号数量 每次不要超过50
// source 表示帐号来源 比如 accountPB.USER_SOURCE_USER_SOURCE_TT
// pwdMd5List 密码列表列表数量必须比cnt大，按照pwdMd5List顺序挨个设置密码
func (c *Client) AutoBatchCreateUser(ctx context.Context,
	cnt uint32, source uint32, pwdMd5List []string) ([]*accountPB.UserResp, error) {
	r, err := c.typedStub().AutoBatchCreateUser(ctx, &accountPB.AutoBatchCreateUserReq{
		Count:           cnt,
		Source:          source,
		PasswordMd5List: pwdMd5List,
	})
	return r.UserList, protocol.ToServerError(err)
}

func (c *Client) GetUserGuild(ctx context.Context, uid uint32, guildId *uint32) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().CheckUserHaveGuild(ctx, &accountPB.CheckUserHaveGuildReq{
		Uid: uid,
	})
	if err == nil {
		*guildId = r.GuildId
	}
	return protocol.ToServerError(err)
}

func (c *Client) GetRegInviteHistory(ctx context.Context, uid uint32, isInvited bool) (*accountPB.GetRegInviteHistoryResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().GetRegInviteHistory(ctx, &accountPB.GetRegInviteHistoryReq{
		IsInvitee: isInvited,
	})
	return r, protocol.ToServerError(err)
}

func (c *Client) GetNicknameByUid(ctx context.Context, uid uint32) (string, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().GetNicknameByUid(ctx, &accountPB.UidReq{Uid: uid})
	if err != nil {
		return "", protocol.ToServerError(err)
	}
	return *r.String_, nil
}

func (c *Client) GetTodayUserBehavior(ctx context.Context, uid, behaviorType uint32) (*accountPB.GetTodayUserBehaviorResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().GetTodayUserBehavior(ctx, &accountPB.GetTodayUserBehaviorReq{
		Type: behaviorType,
	})
	return r, protocol.ToServerError(err)
}

func (c *Client) LogUserBehavior(ctx context.Context, uid, behaviorType uint32, detail string) (*accountPB.LogUserBehaviorResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().LogUserBehavior(ctx, &accountPB.LogUserBehaviorReq{
		Type:   behaviorType,
		Detail: detail,
	})
	return r, protocol.ToServerError(err)
}

func (c *Client) GetUsersByAccs(ctx context.Context, uid uint32, req *accountPB.GetUsersByAccsReq) (*accountPB.GetUsersByAccsResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().GetUsersByAccs(ctx, req)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetNextUid(ctx context.Context) (uint64, protocol.ServerError) {
	r, err := c.typedStub().GetNextUid(ctx, &accountPB.GetNextUidReq{})
	return r.GetUid(), protocol.ToServerError(err)
}

func (c *Client) UpdateUserTickDeviceId(ctx context.Context, uid uint32, deviceId []byte) error {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().UpdateUserTickDeviceId(ctx, &accountPB.UpdateUserTickDeviceIdReq{
		Uid:      uid,
		DeviceId: deviceId,
	})
	return protocol.ToServerError(err)
}

func (c *Client) UpdateUserCurrentGuild(ctx context.Context, uid uint32, guildId uint32) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().UpdateUserCurrentGuild(ctx, &accountPB.UpdateUserCurrentGuildReq{
		Uid:     uid,
		GuildId: guildId,
	})
	return protocol.ToServerError(err)
}

func (c *Client) CheckUserHaveGuild(ctx context.Context, uid uint32) (uint32, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().CheckUserHaveGuild(ctx, &accountPB.CheckUserHaveGuildReq{
		Uid: uid,
	})
	if err != nil {
		return 0, protocol.ToServerError(err)
	}
	return resp.GuildId, nil
}

func (c *Client) UpdateUserGuildPrefixByGuildId(ctx context.Context, guildId uint32, prefix string) protocol.ServerError {
	_, err := c.typedStub().UpdateUserGuildPrefixByGuildId(ctx, &accountPB.UpdateUserGuildPrefixByGuildIdReq{
		GuildId:     guildId,
		GuildPrefix: prefix,
	})
	if err != nil {
		return protocol.ToServerError(err)
	}
	return nil
}

func (c *Client) UpdateUserPrefixValid(ctx context.Context, uid uint32, prefix bool) protocol.ServerError {
	_, err := c.typedStub().UpdateUserPrefixValid(ctx, &accountPB.UpdateUserPrefixValidReq{
		Uid: uid,
		PrefixValid: func() uint32 {
			if prefix {
				return 1
			}
			return 0
		}(),
	})

	if err != nil {
		return protocol.ToServerError(err)
	}
	return nil
}

func (c *Client) UpdateSex(ctx context.Context, uid, sex uint32) protocol.ServerError {
	_, err := c.typedStub().UpdateSex(ctx, &accountPB.UpdateSexReq{
		Uid: uid,
		Sex: sex,
	})

	if err != nil {
		return protocol.ToServerError(err)
	}
	return nil
}

func (c *Client) BindPhoneWithUid(ctx context.Context, req *accountPB.BindPhoneWithUidReq) protocol.ServerError {
	_, err := c.typedStub().BindPhoneWithUid(ctx, req)
	if err != nil {
		return protocol.ToServerError(err)
	}
	return nil
}
