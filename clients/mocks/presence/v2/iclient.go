// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/presence/v2 (interfaces: IClient)

// Package presence_v2 is a generated GoMock package.
package presence_v2

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	terrors "gitlab.ttyuyin.com/tyr/x/terrors"
	Presence "golang.52tt.com/protocol/services/presencesvr"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchDelUserPres mocks base method.
func (m *MockIClient) BatchDelUserPres(arg0 context.Context, arg1 *Presence.ProxyNode, arg2 map[uint32]*Presence.PresInfoList) (map[uint32]bool, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDelUserPres", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]bool)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// BatchDelUserPres indicates an expected call of BatchDelUserPres.
func (mr *MockIClientMockRecorder) BatchDelUserPres(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDelUserPres", reflect.TypeOf((*MockIClient)(nil).BatchDelUserPres), arg0, arg1, arg2)
}

// BatchGetUserPres mocks base method.
func (m *MockIClient) BatchGetUserPres(arg0 context.Context, arg1 []uint32) (map[uint32]*Presence.PresInfoList, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserPres", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*Presence.PresInfoList)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// BatchGetUserPres indicates an expected call of BatchGetUserPres.
func (mr *MockIClientMockRecorder) BatchGetUserPres(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserPres", reflect.TypeOf((*MockIClient)(nil).BatchGetUserPres), arg0, arg1)
}

// BatchGetUserPresReadable mocks base method.
func (m *MockIClient) BatchGetUserPresReadable(arg0 context.Context, arg1 []uint32) (map[uint32]*Presence.PresInfoListReadable, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserPresReadable", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*Presence.PresInfoListReadable)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// BatchGetUserPresReadable indicates an expected call of BatchGetUserPresReadable.
func (mr *MockIClientMockRecorder) BatchGetUserPresReadable(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserPresReadable", reflect.TypeOf((*MockIClient)(nil).BatchGetUserPresReadable), arg0, arg1)
}

// BatchPeekUserPres mocks base method.
func (m *MockIClient) BatchPeekUserPres(arg0 context.Context, arg1 []uint32) (map[uint32]bool, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchPeekUserPres", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]bool)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// BatchPeekUserPres indicates an expected call of BatchPeekUserPres.
func (mr *MockIClientMockRecorder) BatchPeekUserPres(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchPeekUserPres", reflect.TypeOf((*MockIClient)(nil).BatchPeekUserPres), arg0, arg1)
}

// BatchSetUserPres mocks base method.
func (m *MockIClient) BatchSetUserPres(arg0 context.Context, arg1 *Presence.ProxyNode, arg2 map[uint32]*Presence.PresInfoList) (map[uint32]bool, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSetUserPres", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]bool)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// BatchSetUserPres indicates an expected call of BatchSetUserPres.
func (mr *MockIClientMockRecorder) BatchSetUserPres(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetUserPres", reflect.TypeOf((*MockIClient)(nil).BatchSetUserPres), arg0, arg1, arg2)
}

// DelUserPres mocks base method.
func (m *MockIClient) DelUserPres(arg0 context.Context, arg1 uint32, arg2 *Presence.ProxyNode, arg3 []*Presence.PresInfo) (bool, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserPres", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// DelUserPres indicates an expected call of DelUserPres.
func (mr *MockIClientMockRecorder) DelUserPres(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserPres", reflect.TypeOf((*MockIClient)(nil).DelUserPres), arg0, arg1, arg2, arg3)
}

// GetOnlineMap mocks base method.
func (m *MockIClient) GetOnlineMap(arg0 context.Context, arg1 []uint32) (map[uint32]bool, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOnlineMap", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]bool)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetOnlineMap indicates an expected call of GetOnlineMap.
func (mr *MockIClientMockRecorder) GetOnlineMap(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOnlineMap", reflect.TypeOf((*MockIClient)(nil).GetOnlineMap), arg0, arg1)
}

// GetPresence mocks base method.
func (m *MockIClient) GetPresence(arg0 context.Context, arg1 uint32) ([]*Presence.Pres, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresence", arg0, arg1)
	ret0, _ := ret[0].([]*Presence.Pres)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetPresence indicates an expected call of GetPresence.
func (mr *MockIClientMockRecorder) GetPresence(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresence", reflect.TypeOf((*MockIClient)(nil).GetPresence), arg0, arg1)
}

// GetPresences mocks base method.
func (m *MockIClient) GetPresences(arg0 context.Context, arg1 []uint32) ([]*Presence.Pres, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresences", arg0, arg1)
	ret0, _ := ret[0].([]*Presence.Pres)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetPresences indicates an expected call of GetPresences.
func (mr *MockIClientMockRecorder) GetPresences(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresences", reflect.TypeOf((*MockIClient)(nil).GetPresences), arg0, arg1)
}

// GetPresencesMap mocks base method.
func (m *MockIClient) GetPresencesMap(arg0 context.Context, arg1 []uint32) (map[uint32][]*Presence.Pres, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresencesMap", arg0, arg1)
	ret0, _ := ret[0].(map[uint32][]*Presence.Pres)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetPresencesMap indicates an expected call of GetPresencesMap.
func (mr *MockIClientMockRecorder) GetPresencesMap(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresencesMap", reflect.TypeOf((*MockIClient)(nil).GetPresencesMap), arg0, arg1)
}

// GetUserPres mocks base method.
func (m *MockIClient) GetUserPres(arg0 context.Context, arg1 uint32) (*Presence.PresInfoList, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPres", arg0, arg1)
	ret0, _ := ret[0].(*Presence.PresInfoList)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetUserPres indicates an expected call of GetUserPres.
func (mr *MockIClientMockRecorder) GetUserPres(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPres", reflect.TypeOf((*MockIClient)(nil).GetUserPres), arg0, arg1)
}

// GetUserPresReadable mocks base method.
func (m *MockIClient) GetUserPresReadable(arg0 context.Context, arg1 uint32) (*Presence.PresInfoListReadable, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresReadable", arg0, arg1)
	ret0, _ := ret[0].(*Presence.PresInfoListReadable)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// GetUserPresReadable indicates an expected call of GetUserPresReadable.
func (mr *MockIClientMockRecorder) GetUserPresReadable(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresReadable", reflect.TypeOf((*MockIClient)(nil).GetUserPresReadable), arg0, arg1)
}

// PeekUserPres mocks base method.
func (m *MockIClient) PeekUserPres(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PeekUserPres", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PeekUserPres indicates an expected call of PeekUserPres.
func (mr *MockIClientMockRecorder) PeekUserPres(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PeekUserPres", reflect.TypeOf((*MockIClient)(nil).PeekUserPres), arg0, arg1)
}

// SetUserPres mocks base method.
func (m *MockIClient) SetUserPres(arg0 context.Context, arg1 uint32, arg2 *Presence.ProxyNode, arg3 []*Presence.PresInfo) (bool, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserPres", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// SetUserPres indicates an expected call of SetUserPres.
func (mr *MockIClientMockRecorder) SetUserPres(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserPres", reflect.TypeOf((*MockIClient)(nil).SetUserPres), arg0, arg1, arg2, arg3)
}

// StatPres mocks base method.
func (m *MockIClient) StatPres(arg0 context.Context, arg1 *Presence.StatPresReq) (*Presence.StatPresResp, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StatPres", arg0, arg1)
	ret0, _ := ret[0].(*Presence.StatPresResp)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// StatPres indicates an expected call of StatPres.
func (mr *MockIClientMockRecorder) StatPres(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StatPres", reflect.TypeOf((*MockIClient)(nil).StatPres), arg0, arg1)
}

// StatProxy mocks base method.
func (m *MockIClient) StatProxy(arg0 context.Context) ([]*Presence.ProxyNodeStat, terrors.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StatProxy", arg0)
	ret0, _ := ret[0].([]*Presence.ProxyNodeStat)
	ret1, _ := ret[1].(terrors.ServerError)
	return ret0, ret1
}

// StatProxy indicates an expected call of StatProxy.
func (mr *MockIClientMockRecorder) StatProxy(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StatProxy", reflect.TypeOf((*MockIClient)(nil).StatProxy), arg0)
}

// UpdatePresence mocks base method.
func (m *MockIClient) UpdatePresence(arg0 context.Context, arg1 uint32, arg2 *Presence.Proxy, arg3 []*Presence.Pres) terrors.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePresence", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(terrors.ServerError)
	return ret0
}

// UpdatePresence indicates an expected call of UpdatePresence.
func (mr *MockIClientMockRecorder) UpdatePresence(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresence", reflect.TypeOf((*MockIClient)(nil).UpdatePresence), arg0, arg1, arg2, arg3)
}

// UpdatePresences mocks base method.
func (m *MockIClient) UpdatePresences(arg0 context.Context, arg1 *Presence.Proxy, arg2 []*Presence.Pres) terrors.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePresences", arg0, arg1, arg2)
	ret0, _ := ret[0].(terrors.ServerError)
	return ret0
}

// UpdatePresences indicates an expected call of UpdatePresences.
func (mr *MockIClientMockRecorder) UpdatePresences(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresences", reflect.TypeOf((*MockIClient)(nil).UpdatePresences), arg0, arg1, arg2)
}

// UserKeepAlive mocks base method.
func (m *MockIClient) UserKeepAlive(arg0 context.Context, arg1 *Presence.ProxyInfo, arg2 []*Presence.Pres) terrors.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserKeepAlive", arg0, arg1, arg2)
	ret0, _ := ret[0].(terrors.ServerError)
	return ret0
}

// UserKeepAlive indicates an expected call of UserKeepAlive.
func (mr *MockIClientMockRecorder) UserKeepAlive(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserKeepAlive", reflect.TypeOf((*MockIClient)(nil).UserKeepAlive), arg0, arg1, arg2)
}
