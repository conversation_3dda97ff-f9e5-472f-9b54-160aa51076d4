// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/rcmd/rcmd-game-pal-user (interfaces: IClient)

// Package rcmd_game_pal_user is a generated GoMock package.
package rcmd_game_pal_user

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	rcmd_game_pal_user "golang.52tt.com/protocol/services/topic_channel/rcmd_game_pal_user"
	grpc "google.golang.org/grpc"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchGetUserIMLabel mocks base method.
func (m *MockIClient) BatchGetUserIMLabel(arg0 context.Context, arg1 *rcmd_game_pal_user.BatchGetUserIMLabelReq, arg2 ...grpc.CallOption) (*rcmd_game_pal_user.BatchGetUserIMLabelResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetUserIMLabel", varargs...)
	ret0, _ := ret[0].(*rcmd_game_pal_user.BatchGetUserIMLabelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserIMLabel indicates an expected call of BatchGetUserIMLabel.
func (mr *MockIClientMockRecorder) BatchGetUserIMLabel(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserIMLabel", reflect.TypeOf((*MockIClient)(nil).BatchGetUserIMLabel), varargs...)
}

// GetGreetingUser mocks base method.
func (m *MockIClient) GetGreetingUser(arg0 context.Context, arg1 *rcmd_game_pal_user.GetGreetingUserReq, arg2 ...grpc.CallOption) (*rcmd_game_pal_user.GetGreetingUserResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGreetingUser", varargs...)
	ret0, _ := ret[0].(*rcmd_game_pal_user.GetGreetingUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGreetingUser indicates an expected call of GetGreetingUser.
func (mr *MockIClientMockRecorder) GetGreetingUser(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGreetingUser", reflect.TypeOf((*MockIClient)(nil).GetGreetingUser), varargs...)
}

// GetRCMDGamePalUser mocks base method.
func (m *MockIClient) GetRCMDGamePalUser(arg0 context.Context, arg1 *rcmd_game_pal_user.GetRCMDGamePalUserReq, arg2 ...grpc.CallOption) (*rcmd_game_pal_user.GetRCMDGamePalUserResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRCMDGamePalUser", varargs...)
	ret0, _ := ret[0].(*rcmd_game_pal_user.GetRCMDGamePalUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRCMDGamePalUser indicates an expected call of GetRCMDGamePalUser.
func (mr *MockIClientMockRecorder) GetRCMDGamePalUser(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRCMDGamePalUser", reflect.TypeOf((*MockIClient)(nil).GetRCMDGamePalUser), varargs...)
}

// GetRegularReminderFriends mocks base method.
func (m *MockIClient) GetRegularReminderFriends(arg0 context.Context, arg1 *rcmd_game_pal_user.GetRegularReminderFriendsReq, arg2 ...grpc.CallOption) (*rcmd_game_pal_user.GetRegularReminderFriendsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRegularReminderFriends", varargs...)
	ret0, _ := ret[0].(*rcmd_game_pal_user.GetRegularReminderFriendsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRegularReminderFriends indicates an expected call of GetRegularReminderFriends.
func (mr *MockIClientMockRecorder) GetRegularReminderFriends(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRegularReminderFriends", reflect.TypeOf((*MockIClient)(nil).GetRegularReminderFriends), varargs...)
}

// GetUserHistoryFriends mocks base method.
func (m *MockIClient) GetUserHistoryFriends(arg0 context.Context, arg1 *rcmd_game_pal_user.GetUserHistoryFriendsReq, arg2 ...grpc.CallOption) (*rcmd_game_pal_user.GetUserHistoryFriendsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserHistoryFriends", varargs...)
	ret0, _ := ret[0].(*rcmd_game_pal_user.GetUserHistoryFriendsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserHistoryFriends indicates an expected call of GetUserHistoryFriends.
func (mr *MockIClientMockRecorder) GetUserHistoryFriends(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserHistoryFriends", reflect.TypeOf((*MockIClient)(nil).GetUserHistoryFriends), varargs...)
}
