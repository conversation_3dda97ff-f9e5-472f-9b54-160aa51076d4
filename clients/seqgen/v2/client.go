package seqgen

import (
	"context"
	"time"

	grpcMiddleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/seqgen/v2"
	"google.golang.org/grpc"
)

const (
	serviceName = "seqgen-v2" // seqgen: go version
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewSeqGenClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.SeqGenClient { return c.Stub().(pb.SeqGenClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpcMiddleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GenerateSequence(ctx context.Context, id uint32, ns, key string, incr uint32) (uint64, protocol.ServerError) {
	r, err := c.typedStub().GenerateSeq(ctx, &pb.GenerateSeqReq{
		Id: id, Namespace: ns, Key: key, Incr: incr,
	})
	if err != nil {
		return 0, protocol.ToServerError(err)
	}
	return r.GetSeq(), nil
}

func (c *Client) BatchGenerateSequence(ctx context.Context, idList []uint32, ns, key string, incr uint32) protocol.ServerError {
	_, err := c.typedStub().BatchGenerateSeq(ctx, &pb.BatchGenerateSeqReq{
		IdList: idList, Namespace: ns, Key: key, Incr: incr,
	})
	if err != nil {
		return protocol.ToServerError(err)
	}
	return nil
}

func (c *Client) RetrieveSequence(ctx context.Context, id uint32, ns, key string) (uint64, protocol.ServerError) {
	r, err := c.typedStub().RetrieveSeq(ctx, &pb.RetrieveSeqReq{
		Id: id, Namespace: ns, Key: key,
	})
	if err != nil {
		return 0, protocol.ToServerError(err)
	}
	return r.GetSeq(), nil
}

func (c *Client) BatchRetrieveSequence(ctx context.Context, idList []uint32, ns, key string) (map[uint32]uint64, protocol.ServerError) {
	r, err := c.typedStub().BatchRetrieveSeq(ctx, &pb.BatchRetrieveSeqReq{
		IdList: idList, Namespace: ns, Key: key,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r.GetSeqMap(), nil
}

func (c *Client) RetrieveAllSequence(ctx context.Context, id uint32, ns string, keys []string) (map[string]uint64, protocol.ServerError) {
	r, err := c.typedStub().RetrieveMultiSeq(ctx, &pb.RetrieveMultiSeqReq{
		Id:        id,
		Namespace: ns,
		Keys:      keys,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r.GetSeqMap(), nil
}

func (c *Client) GenSnowflakeId(ctx context.Context) (int64, protocol.ServerError) {
	r, err := c.typedStub().GenSnowflakeId(ctx, &pb.GenSnowflakeReq{})
	if err != nil {
		return 0, protocol.ToServerError(err)
	}
	return r.Id, nil
}

func (c *Client) DecodeSnowflakeId(ctx context.Context, id int64) (time.Time, int64, int64, protocol.ServerError) {
	r, err := c.typedStub().DecodeSnowflakeId(ctx, &pb.DecodeSnowflakeReq{
		Id: id,
	})
	if err != nil {
		return time.Now(), 0, 0, protocol.ToServerError(err)
	}
	return time.Unix(r.Timestamp/1e3, (r.Timestamp%1e3)*1e6), r.WorkerId, r.Sequence, nil
}

func (c *Client) SetSeq(ctx context.Context, id uint32, ns, key string, seq uint64) protocol.ServerError {
	_, err := c.typedStub().SetSeq(ctx, &pb.SetSeqReq{
		Id:        id,
		Namespace: ns,
		Key:       key,
		Seq:       seq,
	})
	if err != nil {
		return protocol.ToServerError(err)
	}
	return nil
}

func (c *Client) RetrieveMultiSequences(ctx context.Context, id uint32, ns string, keys []string) (map[string]uint64, protocol.ServerError) {
	r, err := c.typedStub().RetrieveMultiSeq(ctx, &pb.RetrieveMultiSeqReq{
		Id:        id,
		Namespace: ns,
		Keys:      keys,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r.GetSeqMap(), nil
}
