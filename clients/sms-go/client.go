package sms_go

import (
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/sms-go"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

const (
	serviceName = "sms-go"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewSmsClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.SmsClient { return c.Stub().(pb.SmsClient) }

func (c *Client) SendOneCommonSms(ctx context.Context, usage, phone, sign string, uid uint32, paramList []string) protocol.ServerError {
	userInfo := &pb.UserInfo{
		Phone:     phone,
		Uid:       uid,
		Sign:      sign,
		ParamList: paramList,
	}
	_, err := c.typedStub().SendCommonSms(ctx, &pb.SendCommonSmsReq{
		UserInfoList: []*pb.UserInfo{userInfo},
		Usage:        usage,
	}, grpc.WaitForReady(true))
	return protocol.ToServerError(err)
}

func (c *Client) SendSms(ctx context.Context, phone, verifyCodeKey, verifyCodeUsage string, smsType, marketId uint32, paramList []string, withoutCooldown bool) protocol.ServerError {
	_, err := c.typedStub().SendSms(ctx, &pb.SendSmsReq{
		Phone:           phone,
		SmsType:         smsType,
		ParamList:       paramList,
		WithoutCooldown: withoutCooldown,
		VerifyCodeKey:   verifyCodeKey,
		VerifyCodeUsage: verifyCodeUsage,
		MarketId:        marketId,
	}, grpc.WaitForReady(true))
	return protocol.ToServerError(err)
}

func (c *Client) SendVoiceVerifyCode(ctx context.Context, phone, verifyCode string, uid uint32) protocol.ServerError {
	_, err := c.typedStub().SendVoiceVerifyCode(ctx, &pb.SendVoiceVerifyCodeReq{
		Phone:      phone,
		VerifyCode: verifyCode,
		Uid:        uid,
	}, grpc.WaitForReady(true))
	return protocol.ToServerError(err)
}

func (c *Client) SendSmsV2(ctx context.Context, req *pb.SendSmsReq) protocol.ServerError {
	_, err := c.typedStub().SendSms(ctx, req, grpc.WaitForReady(true))
	return protocol.ToServerError(err)
}

func (c *Client) SendVoiceVerifyCodeV2(ctx context.Context, req *pb.SendVoiceVerifyCodeReq) protocol.ServerError {
	_, err := c.typedStub().SendVoiceVerifyCode(ctx, req, grpc.WaitForReady(true))
	return protocol.ToServerError(err)
}

func (c *Client) SendMarketingSms(ctx context.Context, req *pb.SendMarketingSmsReq) protocol.ServerError {
	_, err := c.typedStub().SendMarketingSms(ctx, req, grpc.WaitForReady(true))
	return protocol.ToServerError(err)
}

func (c *Client) SendMarketingSmsV2(ctx context.Context, req *pb.SendMarketingSmsReq) (*pb.SendMarketingSmsResp, protocol.ServerError) {
	resp, err := c.typedStub().SendMarketingSms(ctx, req, grpc.WaitForReady(true))
	return resp, protocol.ToServerError(err)
}

func (c *Client) RecordVerifyCodePass(ctx context.Context, req *pb.RecordVerifyCodePassReq) protocol.ServerError {
	_, err := c.typedStub().RecordVerifyCodePass(ctx, req, grpc.WaitForReady(true))
	return protocol.ToServerError(err)
}
