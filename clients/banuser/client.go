package banuser

import (
	"context"
	"strconv"
	"time"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/banusersvr"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

const (
	serviceName = "banuser"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewBanUserClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.BanUserClient { return c.Stub().(pb.BanUserClient) }

func (c *Client) GetUserBannedStatuses(ctx context.Context, uin uint32, uidList []uint32) (map[uint32]*pb.BannedStatus, protocol.ServerError) {
	banMap := make(map[uint32]*pb.BannedStatus)
	if len(uidList) == 0 {
		return banMap, nil
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().BatchGetUserBannedStatus(ctx, &pb.BatchGetUserBannedStatusReq{UidList: uidList})
	if err != nil {
		return banMap, protocol.ToServerError(err)
	}

	for _, s := range r.GetStatusList() {
		banMap[s.Uid] = s
	}
	return banMap, nil
}

// deviceID, clientIP, phone 三选一填
func (c *Client) GetBannedStatus(ctx context.Context, uin uint32, UID uint32, deviceID string,
	clientIP string, phone string) (*pb.BannedStatus, protocol.ServerError) {

	req := &pb.GetBannedStatusReq{
		Uid:      UID,
		DeviceId: deviceID,
		ClientIp: clientIP,
		Phone:    phone,
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	resp, err := c.typedStub().GetBannedStatus(ctx, req, grpc.WaitForReady(true))

	return resp.GetBannedStatus(), protocol.ToServerError(err)
}

func (c *Client) UpdateBanedStatus(ctx context.Context, uin uint32, opType uint32, UID uint32,
	deviceID string, clientIP string, at uint32, recoveryAt uint32, reason string, operatorID string,
	proofPic string, extInfo string, reasonDetail string) protocol.ServerError {

	req := &pb.UpdateBanStatusReq{
		OpType:       opType,
		Uid:          UID,
		DeviceId:     deviceID,
		ClientIp:     clientIP,
		At:           at,
		RecoveryAt:   recoveryAt,
		Reason:       reason,
		ReasonDetail: reasonDetail,
		OperatorId:   operatorID,
		ProofPic:     proofPic,
		ExtInfo:      extInfo,
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	_, err := c.typedStub().UpdateBanedStatus(ctx, req)

	return protocol.ToServerError(err)
}

func (c *Client) GetBannedHistory(ctx context.Context, uid uint32, req *pb.GetBannedHistoryReq) (*pb.GetBannedHistoryResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().GetBannedHistory(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetBannedAppealRecord(ctx context.Context, uid uint32, req *pb.GetBannedAppealRecordReq) (*pb.GetBannedAppealRecordResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().GetBannedAppealRecord(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetBannedAppealRecord(ctx context.Context, uid uint32, req *pb.SetBannedAppealRecordReq) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().SetBannedAppealRecord(ctx, req)
	return protocol.ToServerError(err)
}

func (c *Client) UpdateBannedAppealRecord(ctx context.Context, uid uint32, req *pb.UpdateBannedAppealRecordReq) (bool, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	resp, err := c.typedStub().UpdateBannedAppealRecord(ctx, req)
	if err != nil {
		return false, protocol.ToServerError(err)
	}
	return resp.Updated, nil
}

func (c *Client) GetBannedOperator(ctx context.Context, uin uint32, operatorName string, limit uint32) (*pb.GetBannedOperatorResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	req := &pb.GetBannedOperatorReq{
		OperatorName: operatorName,
		Limit:        limit,
	}
	resp, err := c.typedStub().GetBannedOperator(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetUserBannedStatus(ctx context.Context, uids []uint32) (map[uint32]bool, []uint32, protocol.ServerError) {

	uid2Banned := map[uint32]bool{}
	bannedUids := []uint32{}
	req := &pb.BatchGetUserBannedStatusReq{
		UidList: uids,
	}
	resp, err := c.typedStub().BatchGetUserBannedStatus(ctx, req)
	if err != nil {
		return uid2Banned, bannedUids, protocol.ToServerError(err)
	}

	for _, info := range resp.GetStatusList() {
		if info.Status == uint32(pb.BAN_STATUS_BAN_ST_BANNED) {
			uid2Banned[info.Uid] = true
			bannedUids = append(bannedUids, info.Uid)
		}
	}

	return uid2Banned, bannedUids, nil
}

// 批量查询设备ID封禁状态，所有封禁状态查询时请使用全大写字母的设备ID进行查询
func (c *Client) BatchGetDeviceBannedStatus(ctx context.Context, deviceList []string) (map[string]bool, []string, protocol.ServerError) {

	device2Banned := make(map[string]bool)
	bannedDeviceIds := make([]string, 0)
	req := &pb.BatchGetDeviceBannedStatusReq{
		DeviceIds: deviceList,
	}
	resp, err := c.typedStub().BatchGetDeviceBannedStatus(ctx, req)
	if err != nil {
		return device2Banned, bannedDeviceIds, protocol.ToServerError(err)
	}

	for _, info := range resp.GetStatusList() {
		if info.Status == uint32(pb.BAN_STATUS_BAN_ST_BANNED) {
			device2Banned[info.GetDeviceId()] = true
			bannedDeviceIds = append(bannedDeviceIds, info.GetDeviceId())
		}
	}

	return device2Banned, bannedDeviceIds, nil
}

func (c *Client) BatchUpdateBanedStatus(ctx context.Context, list []*pb.UpdateBanedStatus) protocol.ServerError {
	req := &pb.BatchUpdateBanedStatusReq{
		List: list,
	}
	_, err := c.typedStub().BatchUpdateBanedStatus(ctx, req)
	return protocol.ToServerError(err)
}

func (c *Client) IsUserBannedForever(ctx context.Context, uid uint32) (bool, protocol.ServerError) {
	resp, err := c.GetBannedStatus(ctx, uid, uid, "", "", "")
	if err != nil {
		return false, err
	}

	if resp.Status == 0 {
		return false, nil
	}

	//超过该封禁年数为永久封禁
	foreverTs := time.Now().AddDate(1, 0, 0)

	return foreverTs.Unix() <= int64(resp.RecoveryAt), nil
}
