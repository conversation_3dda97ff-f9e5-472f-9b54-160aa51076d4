package checkin

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"google.golang.org/grpc"

	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc" //
	pb "golang.52tt.com/protocol/services/checkinsvr"
)

const (
	serviceName = "checkin"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewCheckinClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.CheckinClient { return c.Stub().(pb.CheckinClient) }

func (c *Client) GetUserCheckInTime(ctx context.Context, uid, checkInType uint32) (*pb.GetUserCheckInTimeResp, error) {
	resp, err := c.typedStub().GetUserCheckInTime(ctx, &pb.GetUserCheckInTimeReq{
		Uid:         uid,
		CheckInType: checkInType,
	})

	return resp, protocol.ToServerError(err)
}

func (c *Client) DoCheckIn(ctx context.Context, uid, checkInType, checkInValue uint32, deviceId string) error {
	_, err := c.typedStub().DoCheckIn(ctx, &pb.DoCheckInReq{
		Uid:          uid,
		CheckInType:  checkInType,
		CheckInValue: checkInValue,
		DeviceId:     deviceId,
	})

	return protocol.ToServerError(err)
}

func (c *Client) FirstRechargeAward(ctx context.Context, uid, rechargeNum uint32, rechargeType, orderId string) (*pb.FirstRechargeAwardResp, error) {
	resp, err := c.typedStub().FirstRechargeAward(ctx, &pb.FirstRechargeAwardReq{
		Uid:             uid,
		RechargeNum:     rechargeNum,
		RechargeType:    rechargeType,
		RechargeOrderId: orderId,
	})

	return resp, protocol.ToServerError(err)
}
