package channel

import (
	"context"
	"strconv"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc" //
	pb "golang.52tt.com/protocol/services/channelsvr"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

const (
	serviceName = "channel"

	ChannelTypeGuild          = pb.ChannelBindType_GUILD_BIND_CHANNEL
	ChannelTypeFree           = pb.ChannelBindType_FREE_BIND_CHANNEL
	ChannelTypeUser           = pb.ChannelBindType_USER_BIND_CHANNEL
	ChnanelTypeGuildPublicFun = pb.ChannelBindType_GUILD_PUBLIC_FUN_BIND_CHANNEL
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewClientTo(target string, dopts ...grpc.DialOption) *Client {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewGrpcClientTo(
			target,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelClient(cc)
			}, dopts...),
	}
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewChannelClient(cc)
		}, dopts...),
	}
}

func (c *Client) typedStub() pb.ChannelClient { return c.Stub().(pb.ChannelClient) }

func (c *Client) AllocTempChannel(ctx context.Context, channelType uint32) (*pb.AllocTempChannelResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", "0"))
	r, err := c.typedStub().AllocTempChannel(ctx, &pb.AllocTempChannelReq{
		ChannelType: &channelType,
	})
	return r, protocol.ToServerError(err)
}

func (c *Client) CheckTmpChannelIsAlloced(ctx context.Context, channelId, channelType uint32) (*pb.CheckTmpChannelIsAllocedResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", "0"))

	uid := uint32(0)
	r, err := c.typedStub().CheckTmpChannelIsAlloced(ctx, &pb.CheckTmpChannelIsAllocedReq{
		Uid:         &uid,
		ChannelId:   &channelId,
		ChannelType: &channelType,
	})
	return r, protocol.ToServerError(err)
}

func (c *Client) ReleaseTempChannel(ctx context.Context, channelId, channelType uint32) (*pb.ReleaseTempChannelResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", "0"))
	r, err := c.typedStub().ReleaseTempChannel(ctx, &pb.ReleaseTempChannelReq{
		ChannelId: &channelId,
		BindType:  &channelType,
	})
	return r, protocol.ToServerError(err)
}

func (c *Client) BatchGetChannelSimpleInfo(ctx context.Context, uin uint32, channelIDs []uint32) (map[uint32]*pb.ChannelSimpleInfo, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().BatchGetChannelSimpleInfo(ctx, &pb.BatchGetChannelSimpleInfoReq{OpUid: &uin, ChannelIdList: channelIDs})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	result := make(map[uint32]*pb.ChannelSimpleInfo)
	for _, ch := range r.GetChannelSimpleList() {
		result[ch.GetChannelId()] = ch
	}
	return result, nil
}
func (c *Client) BatchGetChannelSimpleInfoByDisplayId(ctx context.Context, uin uint32, displayIds []uint32) (map[uint32]*pb.ChannelSimpleInfo, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().BatchGetChannelSimpleInfo(ctx, &pb.BatchGetChannelSimpleInfoReq{OpUid: &uin, DisplayIdList: displayIds})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	result := make(map[uint32]*pb.ChannelSimpleInfo)
	for _, ch := range r.GetChannelSimpleList() {
		result[ch.GetChannelId()] = ch
	}
	return result, nil
}

func (c *Client) GetChannelSimpleInfo(ctx context.Context, uin uint32, channelId uint32) (*pb.ChannelSimpleInfo, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().GetChannelSimpleInfo(ctx,
		&pb.GetChannelSimpleInfoReq{ChannelId: &channelId, OpUid: &uin})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return r.ChannelSimple, nil
}

func (c *Client) GetUserChannelAdmin(ctx context.Context, uin, optUid uint32, channelId uint32) ([]*pb.ChannelAdmin, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().GetChannelAdmin(ctx, &pb.GetChannelAdminReq{OptUid: &optUid, ChannelId: &channelId})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return r.AdminList, nil
}

func (c *Client) GetChannelAdmin(ctx context.Context, uin uint32, channelId uint32) ([]*pb.ChannelAdmin, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().GetChannelAdmin(ctx, &pb.GetChannelAdminReq{ChannelId: &channelId})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return r.AdminList, nil
}

func (c *Client) AddChannelAdmin(ctx context.Context, uin, channelId, uid, role uint32) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	_, err := c.typedStub().AddChannelAdmin(ctx, &pb.AddChannelAdminReq{ChannelId: &channelId, Uid: &uid, Role: &role})
	if err != nil {
		return protocol.ToServerError(err)
	}
	return nil
}

func (c *Client) RemoveChannelAdmin(ctx context.Context, uin, channelId, uid uint32) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	_, err := c.typedStub().RemoveChannelAdmin(ctx, &pb.RemoveChannelAdminReq{ChannelId: &channelId, Uid: &uid})
	if err != nil {
		return protocol.ToServerError(err)
	}
	return nil
}

func (c *Client) GetChannelDetailInfo(ctx context.Context, uin uint32, channelId uint32) (*pb.GetChannelDetailInfoResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().GetChannelDetailInfo(ctx, &pb.GetChannelDetailInfoReq{ChannelId: &channelId, OpUid: &uin})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return r, nil
}

func (c *Client) GetUserChannelRoleList(ctx context.Context, uin uint32, uid uint32, adminRole uint32) (
	*pb.GetUserChannelRoleListResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().GetUserChannelRoleList(ctx, &pb.GetUserChannelRoleListReq{
		Uid:       &uid,
		AdminRole: &adminRole,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return r, nil
}

func (c *Client) CreateChannel(ctx context.Context, uin uint32, uid, channelTy, appid uint32, channelName, passwd string) (*pb.CreateChannelLiteResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	var hasPass = false
	r, err := c.typedStub().CreateChannelLite(ctx, &pb.CreateChannelLiteReq{
		CreateReq: &pb.CreateChannelReq{
			Name:            &channelName,
			CreaterUid:      &uid,
			ChannelBindType: &channelTy,
			BindId:          &uid,
			Appid:           &appid,
			HasPwd:          &hasPass,
			Passwd:          &passwd,
		},
	})

	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r, nil
}

// ModifyChannel : 修改房间信息
// (修改房间名称 建议使用 channelApi服务提供的 ModifyName 系列接口)
func (c *Client) ModifyChannel(ctx context.Context, uin uint32, in *pb.ModifyChannelReq) (*pb.ModifyChannelResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().ModifyChannel(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r, nil
}

// Deprecated: GetChannelSimpleInfoByDisplayId instead
// (废弃，改用 GetChannelSimpleInfoByDisplayId)
func (c *Client) GetChannelByDisplayId(ctx context.Context, uin uint32, in *pb.GetChannelByDisplayIdReq) (*pb.GetChannelByDisplayIdResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().GetChannelByDisplayId(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r, nil
}
func (c *Client) GetChannelSimpleInfoByDisplayId(ctx context.Context, uin uint32, displayId uint32) (*pb.ChannelSimpleInfo, protocol.ServerError) {
	var tmpChannel uint32 = 0
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().GetChannelSimpleInfo(ctx,
		&pb.GetChannelSimpleInfoReq{ChannelId: &tmpChannel, DisplayId: &displayId, OpUid: &uin})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return r.ChannelSimple, nil
}

func (c *Client) DissolveChannel(ctx context.Context, opuid, channelId uint32) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opuid))))
	_, err := c.typedStub().DissolveChannel(ctx, &pb.DissolveChannelReq{ChannelId: &channelId, OpUid: &opuid})
	if err != nil {
		return protocol.ToServerError(err)
	}

	return nil
}

func (c *Client) GetChannelTopicDetail(ctx context.Context, uin uint32,
	channelID uint32) (string, protocol.ServerError) {

	req := &pb.GetChannelTopicDetailReq{
		ChannelId: &channelID,
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	resp, err := c.typedStub().GetChannelTopicDetail(ctx, req)
	if err != nil {
		return "", protocol.ToServerError(err)
	}
	return resp.GetTopicDetail(), nil
}

func (c *Client) GetChannelWelcomeMsg(ctx context.Context, uin uint32,
	channelID uint32) (string, protocol.ServerError) {

	req := &pb.GetChannelWelcomeMsgReq{
		ChannelId: &channelID,
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	resp, err := c.typedStub().GetChannelWelcomeMsg(ctx, req)
	if err != nil {
		return "", protocol.ToServerError(err)
	}
	return resp.GetWelcomeMsg(), nil
}

func (c *Client) UnmuteChannelMember(ctx context.Context, uin uint32,
	channelID uint32, uids []uint32) protocol.ServerError {

	req := &pb.UnmuteChannelMemberReq{
		ChannelId:  &channelID,
		TargetUids: uids,
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	_, err := c.typedStub().UnmuteChannelMember(ctx, req)
	if err != nil {
		return protocol.ToServerError(err)
	}

	return nil
}

func (c *Client) MuteChannelMember(ctx context.Context, uin uint32,
	channelID uint32, uids []uint32) ([]uint32, protocol.ServerError) {

	req := &pb.MuteChannelMemberReq{
		ChannelId:  &channelID,
		TargetUids: uids,
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	resp, err := c.typedStub().MuteChannelMember(ctx, req)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return resp.MutedUids, nil
}

func (c *Client) CheckUserIsMute(ctx context.Context, uin uint32, channelID uint32, UID uint32) (
	bool, protocol.ServerError) {

	req := &pb.CheckUserIsMuteReq{
		Uid:       &UID,
		ChannelId: &channelID,
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	resp, err := c.typedStub().CheckUserIsMute(ctx, req)
	if err != nil {
		return resp.GetIsMute(), protocol.ToServerError(err)
	}

	return resp.GetIsMute(), nil
}

func (c *Client) CreateChannelByDisplayID(ctx context.Context,
	displayId uint32,
	channelName string,
	channelType uint32,
	bindId uint32,
	appid uint32,
	passwd string,
	creatorUid uint32,
	marketId uint32) (uint32, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.FormatUint(uint64(creatorUid), 10)))
	req := &pb.CreateChannelByDisplayIDReq{
		DisplayId:       &displayId,
		Name:            &channelName,
		ChannelBindType: &channelType,
		BindId:          &bindId,
		Appid:           &appid,
		Passwd:          &passwd,
		CreatorUid:      &creatorUid,
		MarketId:        &marketId,
	}

	resp, err := c.typedStub().CreateChannelByDisplayID(ctx, req)
	if nil != err {
		return 0, protocol.ToServerError(err)
	}
	return resp.GetChannelId(), nil
}

func (c *Client) CheckUserKickoutFromChannel(ctx context.Context, uid uint32, cid uint32) (bKicked bool, err protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.FormatUint(uint64(uid), 10)))

	req := &pb.CheckUserKickoutFromChannelReq{ChannelId: proto.Uint32(cid), Uid: proto.Uint32(uid)}
	resp, checkErr := c.typedStub().CheckUserKickoutFromChannel(ctx, req)
	if nil != checkErr {
		return false, protocol.ToServerError(checkErr)
	}

	return resp.GetIsKicked(), nil
}

// 新建频道简化版: 只创建频道，不进入频道
func (c *Client) CreateChannelLite(ctx context.Context, opuid uint32, req *pb.CreateChannelLiteReq) (*pb.CreateChannelLiteResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.FormatUint(uint64(opuid), 10)))
	resp, checkErr := c.typedStub().CreateChannelLite(ctx, req)
	return resp, protocol.ToServerError(checkErr)
}

// 接口废弃，改用ChangeViewID接口
func (c *Client) ChangeDisplayID(ctx context.Context, opuid uint32, req *pb.ChangeDisplayIDReq, opts ...grpc.CallOption) (*pb.ChangeDisplayIDResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.FormatUint(uint64(opuid), 10)))
	resp, checkErr := c.typedStub().ChangeDisplayID(ctx, req)
	return resp, protocol.ToServerError(checkErr)
}

func (c *Client) ModifyChannelEnterControlType(ctx context.Context, uin uint32, in *pb.ModifyChannelEnterControlTypeReq) (*pb.ModifyChannelEnterControlTypeResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().ModifyChannelEnterControlType(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r, nil
}

func (c *Client) BatchAllocTempChannels(ctx context.Context, in *pb.BatchAllocTempChannelReq) (*pb.BatchAllocTempChannelResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", "0"))
	r, err := c.typedStub().BatchAllocTempChannels(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) BatchReleaseTempChannels(ctx context.Context, in *pb.BatchReleaseTempChannelReq) (*pb.BatchReleaseTempChannelResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", "0"))
	r, err := c.typedStub().BatchReleaseTempChannels(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) QueryTempChannelsNum(ctx context.Context, in *pb.QueryTempChannelsNumReq) (*pb.QueryTempChannelsNumResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", "0"))
	r, err := c.typedStub().QueryTempChannelsNum(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetChannelSimpleInfoByViewId(ctx context.Context, uin uint32, channelViewId string) (*pb.ChannelSimpleInfo, protocol.ServerError) {
	var tmpChannel uint32 = 0
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().GetChannelSimpleInfo(ctx,
		&pb.GetChannelSimpleInfoReq{ChannelId: &tmpChannel, ChannelViewId: &channelViewId, OpUid: &uin})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return r.ChannelSimple, nil
}
func (c *Client) BatchGetChannelSimpleInfoByViewId(ctx context.Context, uin uint32, channelViewIds []string) (map[uint32]*pb.ChannelSimpleInfo, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().BatchGetChannelSimpleInfo(ctx, &pb.BatchGetChannelSimpleInfoReq{OpUid: &uin, ChannelViewIdList: channelViewIds})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	result := make(map[uint32]*pb.ChannelSimpleInfo)
	for _, ch := range r.GetChannelSimpleList() {
		result[ch.GetChannelId()] = ch
	}
	return result, nil
}
func (c *Client) ChangeChannelViewID(ctx context.Context, uin uint32, req *pb.ChangeChannelViewIdReq) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	_, checkErr := c.typedStub().ChangeChannelViewId(ctx, req)
	return protocol.ToServerError(checkErr)
}

func (c *Client) KickoutChannelMemberLite(ctx context.Context, uin uint32, req *pb.KickoutChannelMemberLiteReq) (*pb.KickoutChannelMemberLiteResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	r, err := c.typedStub().KickoutChannelMemberLite(ctx, req)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return r, nil
}
