// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/channel-play-tab/channel-play-tab.proto

package channel_play_tab // import "golang.52tt.com/protocol/services/channel-play-tab"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type TabSubType int32

const (
	TabSubType_GAME                  TabSubType = 0
	TabSubType_COMPREHENSIVE_CHANNEL TabSubType = 1
)

var TabSubType_name = map[int32]string{
	0: "GAME",
	1: "COMPREHENSIVE_CHANNEL",
}
var TabSubType_value = map[string]int32{
	"GAME":                  0,
	"COMPREHENSIVE_CHANNEL": 1,
}

func (x TabSubType) String() string {
	return proto.EnumName(TabSubType_name, int32(x))
}
func (TabSubType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{0}
}

type MinorSupervisionScene int32

const (
	MinorSupervisionScene_None      MinorSupervisionScene = 0
	MinorSupervisionScene_EnterRoom MinorSupervisionScene = 1
	MinorSupervisionScene_JoinGame  MinorSupervisionScene = 2
)

var MinorSupervisionScene_name = map[int32]string{
	0: "None",
	1: "EnterRoom",
	2: "JoinGame",
}
var MinorSupervisionScene_value = map[string]int32{
	"None":      0,
	"EnterRoom": 1,
	"JoinGame":  2,
}

func (x MinorSupervisionScene) String() string {
	return proto.EnumName(MinorSupervisionScene_name, int32(x))
}
func (MinorSupervisionScene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{1}
}

type MinorSupervisionConfigType int32

const (
	MinorSupervisionConfigType_ConfigTypeNone     MinorSupervisionConfigType = 0
	MinorSupervisionConfigType_ConfigTypeTab      MinorSupervisionConfigType = 1
	MinorSupervisionConfigType_ConfigTypeCategory MinorSupervisionConfigType = 2
)

var MinorSupervisionConfigType_name = map[int32]string{
	0: "ConfigTypeNone",
	1: "ConfigTypeTab",
	2: "ConfigTypeCategory",
}
var MinorSupervisionConfigType_value = map[string]int32{
	"ConfigTypeNone":     0,
	"ConfigTypeTab":      1,
	"ConfigTypeCategory": 2,
}

func (x MinorSupervisionConfigType) String() string {
	return proto.EnumName(MinorSupervisionConfigType_name, int32(x))
}
func (MinorSupervisionConfigType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{2}
}

// 快速匹配相关的
type QuickMatchConfigType int32

const (
	QuickMatchConfigType_Invalid                      QuickMatchConfigType = 0
	QuickMatchConfigType_NormalQuickMatchList         QuickMatchConfigType = 1
	QuickMatchConfigType_MoreCardTabList              QuickMatchConfigType = 2
	QuickMatchConfigType_NewQuickMatch                QuickMatchConfigType = 3
	QuickMatchConfigType_MiniZoneExposeQuickMatchList QuickMatchConfigType = 4
	QuickMatchConfigType_MiniZoneMoreTabList          QuickMatchConfigType = 5
	QuickMatchConfigType_MiniZoneHotAreaGameList      QuickMatchConfigType = 6
)

var QuickMatchConfigType_name = map[int32]string{
	0: "Invalid",
	1: "NormalQuickMatchList",
	2: "MoreCardTabList",
	3: "NewQuickMatch",
	4: "MiniZoneExposeQuickMatchList",
	5: "MiniZoneMoreTabList",
	6: "MiniZoneHotAreaGameList",
}
var QuickMatchConfigType_value = map[string]int32{
	"Invalid":                      0,
	"NormalQuickMatchList":         1,
	"MoreCardTabList":              2,
	"NewQuickMatch":                3,
	"MiniZoneExposeQuickMatchList": 4,
	"MiniZoneMoreTabList":          5,
	"MiniZoneHotAreaGameList":      6,
}

func (x QuickMatchConfigType) String() string {
	return proto.EnumName(QuickMatchConfigType_name, int32(x))
}
func (QuickMatchConfigType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{3}
}

type FastPCCategoryType int32

const (
	FastPCCategoryType_FAST_PC_CATEGORY_TYPE_NORMAL    FastPCCategoryType = 0
	FastPCCategoryType_FAST_PC_CATEGORY_TYPE_MINI_GAME FastPCCategoryType = 1
	FastPCCategoryType_FAST_PC_CATEGORY_TYPE_MELEE     FastPCCategoryType = 2
)

var FastPCCategoryType_name = map[int32]string{
	0: "FAST_PC_CATEGORY_TYPE_NORMAL",
	1: "FAST_PC_CATEGORY_TYPE_MINI_GAME",
	2: "FAST_PC_CATEGORY_TYPE_MELEE",
}
var FastPCCategoryType_value = map[string]int32{
	"FAST_PC_CATEGORY_TYPE_NORMAL":    0,
	"FAST_PC_CATEGORY_TYPE_MINI_GAME": 1,
	"FAST_PC_CATEGORY_TYPE_MELEE":     2,
}

func (x FastPCCategoryType) String() string {
	return proto.EnumName(FastPCCategoryType_name, int32(x))
}
func (FastPCCategoryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{4}
}

// 配置玩法信息扩展内容，不同业务使用不同的结构体映射处理，往后新增
type TabInfoExtEnum int32

const (
	TabInfoExtEnum_TAB_INFO_EXT_NO_TYPE        TabInfoExtEnum = 0
	TabInfoExtEnum_TAB_INFO_EXT_FAST_PC_CONFIG TabInfoExtEnum = 1
)

var TabInfoExtEnum_name = map[int32]string{
	0: "TAB_INFO_EXT_NO_TYPE",
	1: "TAB_INFO_EXT_FAST_PC_CONFIG",
}
var TabInfoExtEnum_value = map[string]int32{
	"TAB_INFO_EXT_NO_TYPE":        0,
	"TAB_INFO_EXT_FAST_PC_CONFIG": 1,
}

func (x TabInfoExtEnum) String() string {
	return proto.EnumName(TabInfoExtEnum_name, int32(x))
}
func (TabInfoExtEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{5}
}

// 禁止配置对象类型
type BanConfigType int32

const (
	BanConfigType_BanConfigTypeNone       BanConfigType = 0
	BanConfigType_BanConfigTypeUser       BanConfigType = 1
	BanConfigType_BanConfigTypeCrowdGroup BanConfigType = 2
	BanConfigType_BanConfigTypeMultiUser  BanConfigType = 3
)

var BanConfigType_name = map[int32]string{
	0: "BanConfigTypeNone",
	1: "BanConfigTypeUser",
	2: "BanConfigTypeCrowdGroup",
	3: "BanConfigTypeMultiUser",
}
var BanConfigType_value = map[string]int32{
	"BanConfigTypeNone":       0,
	"BanConfigTypeUser":       1,
	"BanConfigTypeCrowdGroup": 2,
	"BanConfigTypeMultiUser":  3,
}

func (x BanConfigType) String() string {
	return proto.EnumName(BanConfigType_name, int32(x))
}
func (BanConfigType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{6}
}

// 配置生效状态
type Status int32

const (
	Status_StatusNone        Status = 0
	Status_StatusActive      Status = 1
	Status_StatusInEffective Status = 2
	Status_StatusExpired     Status = 3
)

var Status_name = map[int32]string{
	0: "StatusNone",
	1: "StatusActive",
	2: "StatusInEffective",
	3: "StatusExpired",
}
var Status_value = map[string]int32{
	"StatusNone":        0,
	"StatusActive":      1,
	"StatusInEffective": 2,
	"StatusExpired":     3,
}

func (x Status) String() string {
	return proto.EnumName(Status_name, int32(x))
}
func (Status) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{7}
}

// 禁止配置业务类型
type BanPostType int32

const (
	BanPostType_BanPostTypePost            BanPostType = 0
	BanPostType_BanPostTypeGamePalCard     BanPostType = 1
	BanPostType_BanPostTypeHideGamePalCard BanPostType = 2
	BanPostType_BanGameHallSay             BanPostType = 3
)

var BanPostType_name = map[int32]string{
	0: "BanPostTypePost",
	1: "BanPostTypeGamePalCard",
	2: "BanPostTypeHideGamePalCard",
	3: "BanGameHallSay",
}
var BanPostType_value = map[string]int32{
	"BanPostTypePost":            0,
	"BanPostTypeGamePalCard":     1,
	"BanPostTypeHideGamePalCard": 2,
	"BanGameHallSay":             3,
}

func (x BanPostType) String() string {
	return proto.EnumName(BanPostType_name, int32(x))
}
func (BanPostType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{8}
}

// 批量获取玩法可见用户白名单
type BatchGetWhiteUidListByTabIdsReq struct {
	TabIds               []uint32 `protobuf:"varint,1,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	Source               string   `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	NoCache              bool     `protobuf:"varint,3,opt,name=no_cache,json=noCache,proto3" json:"no_cache,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetWhiteUidListByTabIdsReq) Reset()         { *m = BatchGetWhiteUidListByTabIdsReq{} }
func (m *BatchGetWhiteUidListByTabIdsReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetWhiteUidListByTabIdsReq) ProtoMessage()    {}
func (*BatchGetWhiteUidListByTabIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{0}
}
func (m *BatchGetWhiteUidListByTabIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetWhiteUidListByTabIdsReq.Unmarshal(m, b)
}
func (m *BatchGetWhiteUidListByTabIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetWhiteUidListByTabIdsReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetWhiteUidListByTabIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetWhiteUidListByTabIdsReq.Merge(dst, src)
}
func (m *BatchGetWhiteUidListByTabIdsReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetWhiteUidListByTabIdsReq.Size(m)
}
func (m *BatchGetWhiteUidListByTabIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetWhiteUidListByTabIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetWhiteUidListByTabIdsReq proto.InternalMessageInfo

func (m *BatchGetWhiteUidListByTabIdsReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *BatchGetWhiteUidListByTabIdsReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *BatchGetWhiteUidListByTabIdsReq) GetNoCache() bool {
	if m != nil {
		return m.NoCache
	}
	return false
}

type BatchGetWhiteUidListByTabIdsResp struct {
	// map中没有对应玩法key是没有配置白名单,只返回有白名单列表不为空的玩法白名单列表
	TabListMap           map[uint32]*BatchGetWhiteUidListByTabIdsResp_WhiteUidList `protobuf:"bytes,1,rep,name=tab_list_map,json=tabListMap,proto3" json:"tab_list_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                                  `json:"-"`
	XXX_unrecognized     []byte                                                    `json:"-"`
	XXX_sizecache        int32                                                     `json:"-"`
}

func (m *BatchGetWhiteUidListByTabIdsResp) Reset()         { *m = BatchGetWhiteUidListByTabIdsResp{} }
func (m *BatchGetWhiteUidListByTabIdsResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetWhiteUidListByTabIdsResp) ProtoMessage()    {}
func (*BatchGetWhiteUidListByTabIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{1}
}
func (m *BatchGetWhiteUidListByTabIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp.Unmarshal(m, b)
}
func (m *BatchGetWhiteUidListByTabIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetWhiteUidListByTabIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp.Merge(dst, src)
}
func (m *BatchGetWhiteUidListByTabIdsResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp.Size(m)
}
func (m *BatchGetWhiteUidListByTabIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp proto.InternalMessageInfo

func (m *BatchGetWhiteUidListByTabIdsResp) GetTabListMap() map[uint32]*BatchGetWhiteUidListByTabIdsResp_WhiteUidList {
	if m != nil {
		return m.TabListMap
	}
	return nil
}

type BatchGetWhiteUidListByTabIdsResp_WhiteUidList struct {
	List                 []uint32 `protobuf:"varint,1,rep,packed,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetWhiteUidListByTabIdsResp_WhiteUidList) Reset() {
	*m = BatchGetWhiteUidListByTabIdsResp_WhiteUidList{}
}
func (m *BatchGetWhiteUidListByTabIdsResp_WhiteUidList) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetWhiteUidListByTabIdsResp_WhiteUidList) ProtoMessage() {}
func (*BatchGetWhiteUidListByTabIdsResp_WhiteUidList) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{1, 0}
}
func (m *BatchGetWhiteUidListByTabIdsResp_WhiteUidList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp_WhiteUidList.Unmarshal(m, b)
}
func (m *BatchGetWhiteUidListByTabIdsResp_WhiteUidList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp_WhiteUidList.Marshal(b, m, deterministic)
}
func (dst *BatchGetWhiteUidListByTabIdsResp_WhiteUidList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp_WhiteUidList.Merge(dst, src)
}
func (m *BatchGetWhiteUidListByTabIdsResp_WhiteUidList) XXX_Size() int {
	return xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp_WhiteUidList.Size(m)
}
func (m *BatchGetWhiteUidListByTabIdsResp_WhiteUidList) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp_WhiteUidList.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp_WhiteUidList proto.InternalMessageInfo

func (m *BatchGetWhiteUidListByTabIdsResp_WhiteUidList) GetList() []uint32 {
	if m != nil {
		return m.List
	}
	return nil
}

// 保存玩法白名单,仅运营后台用
type SetWhiteUidListByTabIdReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Source               string   `protobuf:"bytes,3,opt,name=source,proto3" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetWhiteUidListByTabIdReq) Reset()         { *m = SetWhiteUidListByTabIdReq{} }
func (m *SetWhiteUidListByTabIdReq) String() string { return proto.CompactTextString(m) }
func (*SetWhiteUidListByTabIdReq) ProtoMessage()    {}
func (*SetWhiteUidListByTabIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{2}
}
func (m *SetWhiteUidListByTabIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetWhiteUidListByTabIdReq.Unmarshal(m, b)
}
func (m *SetWhiteUidListByTabIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetWhiteUidListByTabIdReq.Marshal(b, m, deterministic)
}
func (dst *SetWhiteUidListByTabIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetWhiteUidListByTabIdReq.Merge(dst, src)
}
func (m *SetWhiteUidListByTabIdReq) XXX_Size() int {
	return xxx_messageInfo_SetWhiteUidListByTabIdReq.Size(m)
}
func (m *SetWhiteUidListByTabIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetWhiteUidListByTabIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetWhiteUidListByTabIdReq proto.InternalMessageInfo

func (m *SetWhiteUidListByTabIdReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *SetWhiteUidListByTabIdReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *SetWhiteUidListByTabIdReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

type SetWhiteUidListByTabIdResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetWhiteUidListByTabIdResp) Reset()         { *m = SetWhiteUidListByTabIdResp{} }
func (m *SetWhiteUidListByTabIdResp) String() string { return proto.CompactTextString(m) }
func (*SetWhiteUidListByTabIdResp) ProtoMessage()    {}
func (*SetWhiteUidListByTabIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{3}
}
func (m *SetWhiteUidListByTabIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetWhiteUidListByTabIdResp.Unmarshal(m, b)
}
func (m *SetWhiteUidListByTabIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetWhiteUidListByTabIdResp.Marshal(b, m, deterministic)
}
func (dst *SetWhiteUidListByTabIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetWhiteUidListByTabIdResp.Merge(dst, src)
}
func (m *SetWhiteUidListByTabIdResp) XXX_Size() int {
	return xxx_messageInfo_SetWhiteUidListByTabIdResp.Size(m)
}
func (m *SetWhiteUidListByTabIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetWhiteUidListByTabIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetWhiteUidListByTabIdResp proto.InternalMessageInfo

type Tab struct {
	TabId                uint32     `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string     `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	ImageUrl             string     `protobuf:"bytes,3,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	TabSubType           TabSubType `protobuf:"varint,4,opt,name=tab_sub_type,json=tabSubType,proto3,enum=channel_play_tab.TabSubType" json:"tab_sub_type,omitempty"`
	IsTabVisible         bool       `protobuf:"varint,5,opt,name=is_tab_visible,json=isTabVisible,proto3" json:"is_tab_visible,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *Tab) Reset()         { *m = Tab{} }
func (m *Tab) String() string { return proto.CompactTextString(m) }
func (*Tab) ProtoMessage()    {}
func (*Tab) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{4}
}
func (m *Tab) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Tab.Unmarshal(m, b)
}
func (m *Tab) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Tab.Marshal(b, m, deterministic)
}
func (dst *Tab) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Tab.Merge(dst, src)
}
func (m *Tab) XXX_Size() int {
	return xxx_messageInfo_Tab.Size(m)
}
func (m *Tab) XXX_DiscardUnknown() {
	xxx_messageInfo_Tab.DiscardUnknown(m)
}

var xxx_messageInfo_Tab proto.InternalMessageInfo

func (m *Tab) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *Tab) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *Tab) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *Tab) GetTabSubType() TabSubType {
	if m != nil {
		return m.TabSubType
	}
	return TabSubType_GAME
}

func (m *Tab) GetIsTabVisible() bool {
	if m != nil {
		return m.IsTabVisible
	}
	return false
}

type UpsertTabReq struct {
	Tab                  *Tab     `protobuf:"bytes,1,opt,name=tab,proto3" json:"tab,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertTabReq) Reset()         { *m = UpsertTabReq{} }
func (m *UpsertTabReq) String() string { return proto.CompactTextString(m) }
func (*UpsertTabReq) ProtoMessage()    {}
func (*UpsertTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{5}
}
func (m *UpsertTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertTabReq.Unmarshal(m, b)
}
func (m *UpsertTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertTabReq.Marshal(b, m, deterministic)
}
func (dst *UpsertTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertTabReq.Merge(dst, src)
}
func (m *UpsertTabReq) XXX_Size() int {
	return xxx_messageInfo_UpsertTabReq.Size(m)
}
func (m *UpsertTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertTabReq proto.InternalMessageInfo

func (m *UpsertTabReq) GetTab() *Tab {
	if m != nil {
		return m.Tab
	}
	return nil
}

type UpsertTabResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertTabResp) Reset()         { *m = UpsertTabResp{} }
func (m *UpsertTabResp) String() string { return proto.CompactTextString(m) }
func (*UpsertTabResp) ProtoMessage()    {}
func (*UpsertTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{6}
}
func (m *UpsertTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertTabResp.Unmarshal(m, b)
}
func (m *UpsertTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertTabResp.Marshal(b, m, deterministic)
}
func (dst *UpsertTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertTabResp.Merge(dst, src)
}
func (m *UpsertTabResp) XXX_Size() int {
	return xxx_messageInfo_UpsertTabResp.Size(m)
}
func (m *UpsertTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertTabResp proto.InternalMessageInfo

type GetTabsByTabSubTypeReq struct {
	TabSubType           TabSubType `protobuf:"varint,1,opt,name=tab_sub_type,json=tabSubType,proto3,enum=channel_play_tab.TabSubType" json:"tab_sub_type,omitempty"`
	Source               string     `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	NoCache              bool       `protobuf:"varint,3,opt,name=no_cache,json=noCache,proto3" json:"no_cache,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetTabsByTabSubTypeReq) Reset()         { *m = GetTabsByTabSubTypeReq{} }
func (m *GetTabsByTabSubTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetTabsByTabSubTypeReq) ProtoMessage()    {}
func (*GetTabsByTabSubTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{7}
}
func (m *GetTabsByTabSubTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabsByTabSubTypeReq.Unmarshal(m, b)
}
func (m *GetTabsByTabSubTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabsByTabSubTypeReq.Marshal(b, m, deterministic)
}
func (dst *GetTabsByTabSubTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabsByTabSubTypeReq.Merge(dst, src)
}
func (m *GetTabsByTabSubTypeReq) XXX_Size() int {
	return xxx_messageInfo_GetTabsByTabSubTypeReq.Size(m)
}
func (m *GetTabsByTabSubTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabsByTabSubTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabsByTabSubTypeReq proto.InternalMessageInfo

func (m *GetTabsByTabSubTypeReq) GetTabSubType() TabSubType {
	if m != nil {
		return m.TabSubType
	}
	return TabSubType_GAME
}

func (m *GetTabsByTabSubTypeReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *GetTabsByTabSubTypeReq) GetNoCache() bool {
	if m != nil {
		return m.NoCache
	}
	return false
}

type GetTabsByTabSubTypeResp struct {
	Tabs                 []*Tab   `protobuf:"bytes,1,rep,name=tabs,proto3" json:"tabs,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTabsByTabSubTypeResp) Reset()         { *m = GetTabsByTabSubTypeResp{} }
func (m *GetTabsByTabSubTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetTabsByTabSubTypeResp) ProtoMessage()    {}
func (*GetTabsByTabSubTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{8}
}
func (m *GetTabsByTabSubTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabsByTabSubTypeResp.Unmarshal(m, b)
}
func (m *GetTabsByTabSubTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabsByTabSubTypeResp.Marshal(b, m, deterministic)
}
func (dst *GetTabsByTabSubTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabsByTabSubTypeResp.Merge(dst, src)
}
func (m *GetTabsByTabSubTypeResp) XXX_Size() int {
	return xxx_messageInfo_GetTabsByTabSubTypeResp.Size(m)
}
func (m *GetTabsByTabSubTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabsByTabSubTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabsByTabSubTypeResp proto.InternalMessageInfo

func (m *GetTabsByTabSubTypeResp) GetTabs() []*Tab {
	if m != nil {
		return m.Tabs
	}
	return nil
}

type DeleteTabReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteTabReq) Reset()         { *m = DeleteTabReq{} }
func (m *DeleteTabReq) String() string { return proto.CompactTextString(m) }
func (*DeleteTabReq) ProtoMessage()    {}
func (*DeleteTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{9}
}
func (m *DeleteTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteTabReq.Unmarshal(m, b)
}
func (m *DeleteTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteTabReq.Marshal(b, m, deterministic)
}
func (dst *DeleteTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteTabReq.Merge(dst, src)
}
func (m *DeleteTabReq) XXX_Size() int {
	return xxx_messageInfo_DeleteTabReq.Size(m)
}
func (m *DeleteTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteTabReq proto.InternalMessageInfo

func (m *DeleteTabReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type DeleteTabResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteTabResp) Reset()         { *m = DeleteTabResp{} }
func (m *DeleteTabResp) String() string { return proto.CompactTextString(m) }
func (*DeleteTabResp) ProtoMessage()    {}
func (*DeleteTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{10}
}
func (m *DeleteTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteTabResp.Unmarshal(m, b)
}
func (m *DeleteTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteTabResp.Marshal(b, m, deterministic)
}
func (dst *DeleteTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteTabResp.Merge(dst, src)
}
func (m *DeleteTabResp) XXX_Size() int {
	return xxx_messageInfo_DeleteTabResp.Size(m)
}
func (m *DeleteTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteTabResp proto.InternalMessageInfo

type GetTabsReq struct {
	NoCache              bool     `protobuf:"varint,1,opt,name=no_cache,json=noCache,proto3" json:"no_cache,omitempty"`
	OnlyVisible          bool     `protobuf:"varint,2,opt,name=only_visible,json=onlyVisible,proto3" json:"only_visible,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTabsReq) Reset()         { *m = GetTabsReq{} }
func (m *GetTabsReq) String() string { return proto.CompactTextString(m) }
func (*GetTabsReq) ProtoMessage()    {}
func (*GetTabsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{11}
}
func (m *GetTabsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabsReq.Unmarshal(m, b)
}
func (m *GetTabsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabsReq.Marshal(b, m, deterministic)
}
func (dst *GetTabsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabsReq.Merge(dst, src)
}
func (m *GetTabsReq) XXX_Size() int {
	return xxx_messageInfo_GetTabsReq.Size(m)
}
func (m *GetTabsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabsReq proto.InternalMessageInfo

func (m *GetTabsReq) GetNoCache() bool {
	if m != nil {
		return m.NoCache
	}
	return false
}

func (m *GetTabsReq) GetOnlyVisible() bool {
	if m != nil {
		return m.OnlyVisible
	}
	return false
}

type GetTabsResp struct {
	Tabs                 []*Tab   `protobuf:"bytes,1,rep,name=tabs,proto3" json:"tabs,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTabsResp) Reset()         { *m = GetTabsResp{} }
func (m *GetTabsResp) String() string { return proto.CompactTextString(m) }
func (*GetTabsResp) ProtoMessage()    {}
func (*GetTabsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{12}
}
func (m *GetTabsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabsResp.Unmarshal(m, b)
}
func (m *GetTabsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabsResp.Marshal(b, m, deterministic)
}
func (dst *GetTabsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabsResp.Merge(dst, src)
}
func (m *GetTabsResp) XXX_Size() int {
	return xxx_messageInfo_GetTabsResp.Size(m)
}
func (m *GetTabsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabsResp proto.InternalMessageInfo

func (m *GetTabsResp) GetTabs() []*Tab {
	if m != nil {
		return m.Tabs
	}
	return nil
}

type MinorSupervisionConfig struct {
	Id                    string                     `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	TabId                 uint32                     `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	CategoryId            uint32                     `protobuf:"varint,3,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	MarketIds             []uint32                   `protobuf:"varint,4,rep,packed,name=market_ids,json=marketIds,proto3" json:"market_ids,omitempty"`
	SwitchOnScenes        []MinorSupervisionScene    `protobuf:"varint,5,rep,packed,name=switch_on_scenes,json=switchOnScenes,proto3,enum=channel_play_tab.MinorSupervisionScene" json:"switch_on_scenes,omitempty"`
	NeedCheckRegisterTime int64                      `protobuf:"varint,6,opt,name=need_check_register_time,json=needCheckRegisterTime,proto3" json:"need_check_register_time,omitempty"`
	ConfigType            MinorSupervisionConfigType `protobuf:"varint,7,opt,name=config_type,json=configType,proto3,enum=channel_play_tab.MinorSupervisionConfigType" json:"config_type,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                   `json:"-"`
	XXX_unrecognized      []byte                     `json:"-"`
	XXX_sizecache         int32                      `json:"-"`
}

func (m *MinorSupervisionConfig) Reset()         { *m = MinorSupervisionConfig{} }
func (m *MinorSupervisionConfig) String() string { return proto.CompactTextString(m) }
func (*MinorSupervisionConfig) ProtoMessage()    {}
func (*MinorSupervisionConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{13}
}
func (m *MinorSupervisionConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MinorSupervisionConfig.Unmarshal(m, b)
}
func (m *MinorSupervisionConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MinorSupervisionConfig.Marshal(b, m, deterministic)
}
func (dst *MinorSupervisionConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MinorSupervisionConfig.Merge(dst, src)
}
func (m *MinorSupervisionConfig) XXX_Size() int {
	return xxx_messageInfo_MinorSupervisionConfig.Size(m)
}
func (m *MinorSupervisionConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_MinorSupervisionConfig.DiscardUnknown(m)
}

var xxx_messageInfo_MinorSupervisionConfig proto.InternalMessageInfo

func (m *MinorSupervisionConfig) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *MinorSupervisionConfig) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *MinorSupervisionConfig) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *MinorSupervisionConfig) GetMarketIds() []uint32 {
	if m != nil {
		return m.MarketIds
	}
	return nil
}

func (m *MinorSupervisionConfig) GetSwitchOnScenes() []MinorSupervisionScene {
	if m != nil {
		return m.SwitchOnScenes
	}
	return nil
}

func (m *MinorSupervisionConfig) GetNeedCheckRegisterTime() int64 {
	if m != nil {
		return m.NeedCheckRegisterTime
	}
	return 0
}

func (m *MinorSupervisionConfig) GetConfigType() MinorSupervisionConfigType {
	if m != nil {
		return m.ConfigType
	}
	return MinorSupervisionConfigType_ConfigTypeNone
}

type UpsertMinorSupervisionConfigReq struct {
	Config               *MinorSupervisionConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *UpsertMinorSupervisionConfigReq) Reset()         { *m = UpsertMinorSupervisionConfigReq{} }
func (m *UpsertMinorSupervisionConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpsertMinorSupervisionConfigReq) ProtoMessage()    {}
func (*UpsertMinorSupervisionConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{14}
}
func (m *UpsertMinorSupervisionConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertMinorSupervisionConfigReq.Unmarshal(m, b)
}
func (m *UpsertMinorSupervisionConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertMinorSupervisionConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpsertMinorSupervisionConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertMinorSupervisionConfigReq.Merge(dst, src)
}
func (m *UpsertMinorSupervisionConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpsertMinorSupervisionConfigReq.Size(m)
}
func (m *UpsertMinorSupervisionConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertMinorSupervisionConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertMinorSupervisionConfigReq proto.InternalMessageInfo

func (m *UpsertMinorSupervisionConfigReq) GetConfig() *MinorSupervisionConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type UpsertMinorSupervisionConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertMinorSupervisionConfigResp) Reset()         { *m = UpsertMinorSupervisionConfigResp{} }
func (m *UpsertMinorSupervisionConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpsertMinorSupervisionConfigResp) ProtoMessage()    {}
func (*UpsertMinorSupervisionConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{15}
}
func (m *UpsertMinorSupervisionConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertMinorSupervisionConfigResp.Unmarshal(m, b)
}
func (m *UpsertMinorSupervisionConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertMinorSupervisionConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpsertMinorSupervisionConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertMinorSupervisionConfigResp.Merge(dst, src)
}
func (m *UpsertMinorSupervisionConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpsertMinorSupervisionConfigResp.Size(m)
}
func (m *UpsertMinorSupervisionConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertMinorSupervisionConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertMinorSupervisionConfigResp proto.InternalMessageInfo

type BatchGetMinorSupervisionConfigReq struct {
	TabIds               []uint32 `protobuf:"varint,1,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	CategoryIds          []uint32 `protobuf:"varint,2,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	Source               string   `protobuf:"bytes,3,opt,name=source,proto3" json:"source,omitempty"`
	NoCache              bool     `protobuf:"varint,4,opt,name=no_cache,json=noCache,proto3" json:"no_cache,omitempty"`
	ReturnAll            bool     `protobuf:"varint,5,opt,name=return_all,json=returnAll,proto3" json:"return_all,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetMinorSupervisionConfigReq) Reset()         { *m = BatchGetMinorSupervisionConfigReq{} }
func (m *BatchGetMinorSupervisionConfigReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetMinorSupervisionConfigReq) ProtoMessage()    {}
func (*BatchGetMinorSupervisionConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{16}
}
func (m *BatchGetMinorSupervisionConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetMinorSupervisionConfigReq.Unmarshal(m, b)
}
func (m *BatchGetMinorSupervisionConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetMinorSupervisionConfigReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetMinorSupervisionConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetMinorSupervisionConfigReq.Merge(dst, src)
}
func (m *BatchGetMinorSupervisionConfigReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetMinorSupervisionConfigReq.Size(m)
}
func (m *BatchGetMinorSupervisionConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetMinorSupervisionConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetMinorSupervisionConfigReq proto.InternalMessageInfo

func (m *BatchGetMinorSupervisionConfigReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *BatchGetMinorSupervisionConfigReq) GetCategoryIds() []uint32 {
	if m != nil {
		return m.CategoryIds
	}
	return nil
}

func (m *BatchGetMinorSupervisionConfigReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *BatchGetMinorSupervisionConfigReq) GetNoCache() bool {
	if m != nil {
		return m.NoCache
	}
	return false
}

func (m *BatchGetMinorSupervisionConfigReq) GetReturnAll() bool {
	if m != nil {
		return m.ReturnAll
	}
	return false
}

type BatchGetMinorSupervisionConfigResp struct {
	TabConfigs           map[uint32]*MinorSupervisionConfig `protobuf:"bytes,1,rep,name=tab_configs,json=tabConfigs,proto3" json:"tab_configs,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	CategoryConfigs      map[uint32]*MinorSupervisionConfig `protobuf:"bytes,2,rep,name=category_configs,json=categoryConfigs,proto3" json:"category_configs,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *BatchGetMinorSupervisionConfigResp) Reset()         { *m = BatchGetMinorSupervisionConfigResp{} }
func (m *BatchGetMinorSupervisionConfigResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetMinorSupervisionConfigResp) ProtoMessage()    {}
func (*BatchGetMinorSupervisionConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{17}
}
func (m *BatchGetMinorSupervisionConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetMinorSupervisionConfigResp.Unmarshal(m, b)
}
func (m *BatchGetMinorSupervisionConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetMinorSupervisionConfigResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetMinorSupervisionConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetMinorSupervisionConfigResp.Merge(dst, src)
}
func (m *BatchGetMinorSupervisionConfigResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetMinorSupervisionConfigResp.Size(m)
}
func (m *BatchGetMinorSupervisionConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetMinorSupervisionConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetMinorSupervisionConfigResp proto.InternalMessageInfo

func (m *BatchGetMinorSupervisionConfigResp) GetTabConfigs() map[uint32]*MinorSupervisionConfig {
	if m != nil {
		return m.TabConfigs
	}
	return nil
}

func (m *BatchGetMinorSupervisionConfigResp) GetCategoryConfigs() map[uint32]*MinorSupervisionConfig {
	if m != nil {
		return m.CategoryConfigs
	}
	return nil
}

type GetTabsRealNameConfigsReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTabsRealNameConfigsReq) Reset()         { *m = GetTabsRealNameConfigsReq{} }
func (m *GetTabsRealNameConfigsReq) String() string { return proto.CompactTextString(m) }
func (*GetTabsRealNameConfigsReq) ProtoMessage()    {}
func (*GetTabsRealNameConfigsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{18}
}
func (m *GetTabsRealNameConfigsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabsRealNameConfigsReq.Unmarshal(m, b)
}
func (m *GetTabsRealNameConfigsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabsRealNameConfigsReq.Marshal(b, m, deterministic)
}
func (dst *GetTabsRealNameConfigsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabsRealNameConfigsReq.Merge(dst, src)
}
func (m *GetTabsRealNameConfigsReq) XXX_Size() int {
	return xxx_messageInfo_GetTabsRealNameConfigsReq.Size(m)
}
func (m *GetTabsRealNameConfigsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabsRealNameConfigsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabsRealNameConfigsReq proto.InternalMessageInfo

func (m *GetTabsRealNameConfigsReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetTabsRealNameConfigsReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type TabsRealNameConfig struct {
	MarketId             uint32   `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ChannelPkg           string   `protobuf:"bytes,2,opt,name=channel_pkg,json=channelPkg,proto3" json:"channel_pkg,omitempty"`
	CliVer               string   `protobuf:"bytes,3,opt,name=cli_ver,json=cliVer,proto3" json:"cli_ver,omitempty"`
	SwitchStatus         bool     `protobuf:"varint,4,opt,name=switch_status,json=switchStatus,proto3" json:"switch_status,omitempty"`
	TabIds               []uint32 `protobuf:"varint,5,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	CategoryIds          []uint32 `protobuf:"varint,6,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TabsRealNameConfig) Reset()         { *m = TabsRealNameConfig{} }
func (m *TabsRealNameConfig) String() string { return proto.CompactTextString(m) }
func (*TabsRealNameConfig) ProtoMessage()    {}
func (*TabsRealNameConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{19}
}
func (m *TabsRealNameConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TabsRealNameConfig.Unmarshal(m, b)
}
func (m *TabsRealNameConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TabsRealNameConfig.Marshal(b, m, deterministic)
}
func (dst *TabsRealNameConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabsRealNameConfig.Merge(dst, src)
}
func (m *TabsRealNameConfig) XXX_Size() int {
	return xxx_messageInfo_TabsRealNameConfig.Size(m)
}
func (m *TabsRealNameConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_TabsRealNameConfig.DiscardUnknown(m)
}

var xxx_messageInfo_TabsRealNameConfig proto.InternalMessageInfo

func (m *TabsRealNameConfig) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *TabsRealNameConfig) GetChannelPkg() string {
	if m != nil {
		return m.ChannelPkg
	}
	return ""
}

func (m *TabsRealNameConfig) GetCliVer() string {
	if m != nil {
		return m.CliVer
	}
	return ""
}

func (m *TabsRealNameConfig) GetSwitchStatus() bool {
	if m != nil {
		return m.SwitchStatus
	}
	return false
}

func (m *TabsRealNameConfig) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *TabsRealNameConfig) GetCategoryIds() []uint32 {
	if m != nil {
		return m.CategoryIds
	}
	return nil
}

type GetTabsRealNameConfigsResp struct {
	Configs              []*TabsRealNameConfig `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	Total                uint32                `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetTabsRealNameConfigsResp) Reset()         { *m = GetTabsRealNameConfigsResp{} }
func (m *GetTabsRealNameConfigsResp) String() string { return proto.CompactTextString(m) }
func (*GetTabsRealNameConfigsResp) ProtoMessage()    {}
func (*GetTabsRealNameConfigsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{20}
}
func (m *GetTabsRealNameConfigsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabsRealNameConfigsResp.Unmarshal(m, b)
}
func (m *GetTabsRealNameConfigsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabsRealNameConfigsResp.Marshal(b, m, deterministic)
}
func (dst *GetTabsRealNameConfigsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabsRealNameConfigsResp.Merge(dst, src)
}
func (m *GetTabsRealNameConfigsResp) XXX_Size() int {
	return xxx_messageInfo_GetTabsRealNameConfigsResp.Size(m)
}
func (m *GetTabsRealNameConfigsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabsRealNameConfigsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabsRealNameConfigsResp proto.InternalMessageInfo

func (m *GetTabsRealNameConfigsResp) GetConfigs() []*TabsRealNameConfig {
	if m != nil {
		return m.Configs
	}
	return nil
}

func (m *GetTabsRealNameConfigsResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type UpdateTabsRealNameConfigReq struct {
	Config               *TabsRealNameConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UpdateTabsRealNameConfigReq) Reset()         { *m = UpdateTabsRealNameConfigReq{} }
func (m *UpdateTabsRealNameConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdateTabsRealNameConfigReq) ProtoMessage()    {}
func (*UpdateTabsRealNameConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{21}
}
func (m *UpdateTabsRealNameConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTabsRealNameConfigReq.Unmarshal(m, b)
}
func (m *UpdateTabsRealNameConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTabsRealNameConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdateTabsRealNameConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTabsRealNameConfigReq.Merge(dst, src)
}
func (m *UpdateTabsRealNameConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdateTabsRealNameConfigReq.Size(m)
}
func (m *UpdateTabsRealNameConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTabsRealNameConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTabsRealNameConfigReq proto.InternalMessageInfo

func (m *UpdateTabsRealNameConfigReq) GetConfig() *TabsRealNameConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type UpdateTabsRealNameConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateTabsRealNameConfigResp) Reset()         { *m = UpdateTabsRealNameConfigResp{} }
func (m *UpdateTabsRealNameConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdateTabsRealNameConfigResp) ProtoMessage()    {}
func (*UpdateTabsRealNameConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{22}
}
func (m *UpdateTabsRealNameConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTabsRealNameConfigResp.Unmarshal(m, b)
}
func (m *UpdateTabsRealNameConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTabsRealNameConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdateTabsRealNameConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTabsRealNameConfigResp.Merge(dst, src)
}
func (m *UpdateTabsRealNameConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdateTabsRealNameConfigResp.Size(m)
}
func (m *UpdateTabsRealNameConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTabsRealNameConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTabsRealNameConfigResp proto.InternalMessageInfo

type AddTabsRealNameConfigReq struct {
	Config               *TabsRealNameConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *AddTabsRealNameConfigReq) Reset()         { *m = AddTabsRealNameConfigReq{} }
func (m *AddTabsRealNameConfigReq) String() string { return proto.CompactTextString(m) }
func (*AddTabsRealNameConfigReq) ProtoMessage()    {}
func (*AddTabsRealNameConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{23}
}
func (m *AddTabsRealNameConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTabsRealNameConfigReq.Unmarshal(m, b)
}
func (m *AddTabsRealNameConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTabsRealNameConfigReq.Marshal(b, m, deterministic)
}
func (dst *AddTabsRealNameConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTabsRealNameConfigReq.Merge(dst, src)
}
func (m *AddTabsRealNameConfigReq) XXX_Size() int {
	return xxx_messageInfo_AddTabsRealNameConfigReq.Size(m)
}
func (m *AddTabsRealNameConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTabsRealNameConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddTabsRealNameConfigReq proto.InternalMessageInfo

func (m *AddTabsRealNameConfigReq) GetConfig() *TabsRealNameConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type AddTabsRealNameConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddTabsRealNameConfigResp) Reset()         { *m = AddTabsRealNameConfigResp{} }
func (m *AddTabsRealNameConfigResp) String() string { return proto.CompactTextString(m) }
func (*AddTabsRealNameConfigResp) ProtoMessage()    {}
func (*AddTabsRealNameConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{24}
}
func (m *AddTabsRealNameConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTabsRealNameConfigResp.Unmarshal(m, b)
}
func (m *AddTabsRealNameConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTabsRealNameConfigResp.Marshal(b, m, deterministic)
}
func (dst *AddTabsRealNameConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTabsRealNameConfigResp.Merge(dst, src)
}
func (m *AddTabsRealNameConfigResp) XXX_Size() int {
	return xxx_messageInfo_AddTabsRealNameConfigResp.Size(m)
}
func (m *AddTabsRealNameConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTabsRealNameConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddTabsRealNameConfigResp proto.InternalMessageInfo

type DeleteTabsRealNameConfigReq struct {
	MarketId             uint32   `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ChannelPkg           string   `protobuf:"bytes,2,opt,name=channel_pkg,json=channelPkg,proto3" json:"channel_pkg,omitempty"`
	CliVer               string   `protobuf:"bytes,3,opt,name=cli_ver,json=cliVer,proto3" json:"cli_ver,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteTabsRealNameConfigReq) Reset()         { *m = DeleteTabsRealNameConfigReq{} }
func (m *DeleteTabsRealNameConfigReq) String() string { return proto.CompactTextString(m) }
func (*DeleteTabsRealNameConfigReq) ProtoMessage()    {}
func (*DeleteTabsRealNameConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{25}
}
func (m *DeleteTabsRealNameConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteTabsRealNameConfigReq.Unmarshal(m, b)
}
func (m *DeleteTabsRealNameConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteTabsRealNameConfigReq.Marshal(b, m, deterministic)
}
func (dst *DeleteTabsRealNameConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteTabsRealNameConfigReq.Merge(dst, src)
}
func (m *DeleteTabsRealNameConfigReq) XXX_Size() int {
	return xxx_messageInfo_DeleteTabsRealNameConfigReq.Size(m)
}
func (m *DeleteTabsRealNameConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteTabsRealNameConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteTabsRealNameConfigReq proto.InternalMessageInfo

func (m *DeleteTabsRealNameConfigReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *DeleteTabsRealNameConfigReq) GetChannelPkg() string {
	if m != nil {
		return m.ChannelPkg
	}
	return ""
}

func (m *DeleteTabsRealNameConfigReq) GetCliVer() string {
	if m != nil {
		return m.CliVer
	}
	return ""
}

type DeleteTabsRealNameConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteTabsRealNameConfigResp) Reset()         { *m = DeleteTabsRealNameConfigResp{} }
func (m *DeleteTabsRealNameConfigResp) String() string { return proto.CompactTextString(m) }
func (*DeleteTabsRealNameConfigResp) ProtoMessage()    {}
func (*DeleteTabsRealNameConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{26}
}
func (m *DeleteTabsRealNameConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteTabsRealNameConfigResp.Unmarshal(m, b)
}
func (m *DeleteTabsRealNameConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteTabsRealNameConfigResp.Marshal(b, m, deterministic)
}
func (dst *DeleteTabsRealNameConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteTabsRealNameConfigResp.Merge(dst, src)
}
func (m *DeleteTabsRealNameConfigResp) XXX_Size() int {
	return xxx_messageInfo_DeleteTabsRealNameConfigResp.Size(m)
}
func (m *DeleteTabsRealNameConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteTabsRealNameConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteTabsRealNameConfigResp proto.InternalMessageInfo

type GetUserTabsRealNameConfigReq struct {
	MarketId             uint32   `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ChannelPkg           string   `protobuf:"bytes,2,opt,name=channel_pkg,json=channelPkg,proto3" json:"channel_pkg,omitempty"`
	CliVer               uint32   `protobuf:"varint,3,opt,name=cli_ver,json=cliVer,proto3" json:"cli_ver,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserTabsRealNameConfigReq) Reset()         { *m = GetUserTabsRealNameConfigReq{} }
func (m *GetUserTabsRealNameConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetUserTabsRealNameConfigReq) ProtoMessage()    {}
func (*GetUserTabsRealNameConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{27}
}
func (m *GetUserTabsRealNameConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTabsRealNameConfigReq.Unmarshal(m, b)
}
func (m *GetUserTabsRealNameConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTabsRealNameConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetUserTabsRealNameConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTabsRealNameConfigReq.Merge(dst, src)
}
func (m *GetUserTabsRealNameConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetUserTabsRealNameConfigReq.Size(m)
}
func (m *GetUserTabsRealNameConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTabsRealNameConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTabsRealNameConfigReq proto.InternalMessageInfo

func (m *GetUserTabsRealNameConfigReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetUserTabsRealNameConfigReq) GetChannelPkg() string {
	if m != nil {
		return m.ChannelPkg
	}
	return ""
}

func (m *GetUserTabsRealNameConfigReq) GetCliVer() uint32 {
	if m != nil {
		return m.CliVer
	}
	return 0
}

type GetUserTabsRealNameConfigResp struct {
	Configs              *TabsRealNameConfig `protobuf:"bytes,1,opt,name=configs,proto3" json:"configs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetUserTabsRealNameConfigResp) Reset()         { *m = GetUserTabsRealNameConfigResp{} }
func (m *GetUserTabsRealNameConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetUserTabsRealNameConfigResp) ProtoMessage()    {}
func (*GetUserTabsRealNameConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{28}
}
func (m *GetUserTabsRealNameConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTabsRealNameConfigResp.Unmarshal(m, b)
}
func (m *GetUserTabsRealNameConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTabsRealNameConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetUserTabsRealNameConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTabsRealNameConfigResp.Merge(dst, src)
}
func (m *GetUserTabsRealNameConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetUserTabsRealNameConfigResp.Size(m)
}
func (m *GetUserTabsRealNameConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTabsRealNameConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTabsRealNameConfigResp proto.InternalMessageInfo

func (m *GetUserTabsRealNameConfigResp) GetConfigs() *TabsRealNameConfig {
	if m != nil {
		return m.Configs
	}
	return nil
}

type SceneInfo struct {
	Id         string               `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	TabId      uint32               `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabType    uint32               `protobuf:"varint,3,opt,name=tab_type,json=tabType,proto3" json:"tab_type,omitempty"`
	CategoryId uint32               `protobuf:"varint,4,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	ConfigType QuickMatchConfigType `protobuf:"varint,5,opt,name=config_type,json=configType,proto3,enum=channel_play_tab.QuickMatchConfigType" json:"config_type,omitempty"`
	// 休闲互动专区快速匹配配置字段
	GameDisplayName string   `protobuf:"bytes,6,opt,name=game_display_name,json=gameDisplayName,proto3" json:"game_display_name,omitempty"`
	BgColors        []string `protobuf:"bytes,7,rep,name=bg_colors,json=bgColors,proto3" json:"bg_colors,omitempty"`
	ButtonEffect    uint32   `protobuf:"varint,8,opt,name=button_effect,json=buttonEffect,proto3" json:"button_effect,omitempty"`
	JumpLink        string   `protobuf:"bytes,9,opt,name=jump_link,json=jumpLink,proto3" json:"jump_link,omitempty"`
	// 休闲互动专区重点区域配置额外字段
	MiniGameExtraInfo    *HotMiniGameExtraInfo `protobuf:"bytes,10,opt,name=mini_game_extra_info,json=miniGameExtraInfo,proto3" json:"mini_game_extra_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *SceneInfo) Reset()         { *m = SceneInfo{} }
func (m *SceneInfo) String() string { return proto.CompactTextString(m) }
func (*SceneInfo) ProtoMessage()    {}
func (*SceneInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{29}
}
func (m *SceneInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SceneInfo.Unmarshal(m, b)
}
func (m *SceneInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SceneInfo.Marshal(b, m, deterministic)
}
func (dst *SceneInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SceneInfo.Merge(dst, src)
}
func (m *SceneInfo) XXX_Size() int {
	return xxx_messageInfo_SceneInfo.Size(m)
}
func (m *SceneInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SceneInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SceneInfo proto.InternalMessageInfo

func (m *SceneInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *SceneInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *SceneInfo) GetTabType() uint32 {
	if m != nil {
		return m.TabType
	}
	return 0
}

func (m *SceneInfo) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *SceneInfo) GetConfigType() QuickMatchConfigType {
	if m != nil {
		return m.ConfigType
	}
	return QuickMatchConfigType_Invalid
}

func (m *SceneInfo) GetGameDisplayName() string {
	if m != nil {
		return m.GameDisplayName
	}
	return ""
}

func (m *SceneInfo) GetBgColors() []string {
	if m != nil {
		return m.BgColors
	}
	return nil
}

func (m *SceneInfo) GetButtonEffect() uint32 {
	if m != nil {
		return m.ButtonEffect
	}
	return 0
}

func (m *SceneInfo) GetJumpLink() string {
	if m != nil {
		return m.JumpLink
	}
	return ""
}

func (m *SceneInfo) GetMiniGameExtraInfo() *HotMiniGameExtraInfo {
	if m != nil {
		return m.MiniGameExtraInfo
	}
	return nil
}

type HotMiniGameExtraInfo struct {
	// 介绍文案
	IntroText string `protobuf:"bytes,1,opt,name=intro_text,json=introText,proto3" json:"intro_text,omitempty"`
	// 背景图 url
	BgUrl string `protobuf:"bytes,2,opt,name=bg_url,json=bgUrl,proto3" json:"bg_url,omitempty"`
	// 背景颜色
	BgColor string `protobuf:"bytes,3,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// 按钮文案
	ButtonText string `protobuf:"bytes,4,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"`
	// 按钮颜色组
	ButtonColors []string `protobuf:"bytes,5,rep,name=button_colors,json=buttonColors,proto3" json:"button_colors,omitempty"`
	// 动图 lottie url
	LottieUrl            string   `protobuf:"bytes,6,opt,name=lottie_url,json=lottieUrl,proto3" json:"lottie_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HotMiniGameExtraInfo) Reset()         { *m = HotMiniGameExtraInfo{} }
func (m *HotMiniGameExtraInfo) String() string { return proto.CompactTextString(m) }
func (*HotMiniGameExtraInfo) ProtoMessage()    {}
func (*HotMiniGameExtraInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{30}
}
func (m *HotMiniGameExtraInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HotMiniGameExtraInfo.Unmarshal(m, b)
}
func (m *HotMiniGameExtraInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HotMiniGameExtraInfo.Marshal(b, m, deterministic)
}
func (dst *HotMiniGameExtraInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HotMiniGameExtraInfo.Merge(dst, src)
}
func (m *HotMiniGameExtraInfo) XXX_Size() int {
	return xxx_messageInfo_HotMiniGameExtraInfo.Size(m)
}
func (m *HotMiniGameExtraInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_HotMiniGameExtraInfo.DiscardUnknown(m)
}

var xxx_messageInfo_HotMiniGameExtraInfo proto.InternalMessageInfo

func (m *HotMiniGameExtraInfo) GetIntroText() string {
	if m != nil {
		return m.IntroText
	}
	return ""
}

func (m *HotMiniGameExtraInfo) GetBgUrl() string {
	if m != nil {
		return m.BgUrl
	}
	return ""
}

func (m *HotMiniGameExtraInfo) GetBgColor() string {
	if m != nil {
		return m.BgColor
	}
	return ""
}

func (m *HotMiniGameExtraInfo) GetButtonText() string {
	if m != nil {
		return m.ButtonText
	}
	return ""
}

func (m *HotMiniGameExtraInfo) GetButtonColors() []string {
	if m != nil {
		return m.ButtonColors
	}
	return nil
}

func (m *HotMiniGameExtraInfo) GetLottieUrl() string {
	if m != nil {
		return m.LottieUrl
	}
	return ""
}

type GetQuickMatchConfigReq struct {
	ConfigType           QuickMatchConfigType `protobuf:"varint,1,opt,name=config_type,json=configType,proto3,enum=channel_play_tab.QuickMatchConfigType" json:"config_type,omitempty"`
	Page                 uint32               `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Limit                uint32               `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetQuickMatchConfigReq) Reset()         { *m = GetQuickMatchConfigReq{} }
func (m *GetQuickMatchConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetQuickMatchConfigReq) ProtoMessage()    {}
func (*GetQuickMatchConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{31}
}
func (m *GetQuickMatchConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickMatchConfigReq.Unmarshal(m, b)
}
func (m *GetQuickMatchConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickMatchConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetQuickMatchConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickMatchConfigReq.Merge(dst, src)
}
func (m *GetQuickMatchConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetQuickMatchConfigReq.Size(m)
}
func (m *GetQuickMatchConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickMatchConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickMatchConfigReq proto.InternalMessageInfo

func (m *GetQuickMatchConfigReq) GetConfigType() QuickMatchConfigType {
	if m != nil {
		return m.ConfigType
	}
	return QuickMatchConfigType_Invalid
}

func (m *GetQuickMatchConfigReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetQuickMatchConfigReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetQuickMatchConfigResp struct {
	SceneInfo            []*SceneInfo `protobuf:"bytes,1,rep,name=scene_info,json=sceneInfo,proto3" json:"scene_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetQuickMatchConfigResp) Reset()         { *m = GetQuickMatchConfigResp{} }
func (m *GetQuickMatchConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetQuickMatchConfigResp) ProtoMessage()    {}
func (*GetQuickMatchConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{32}
}
func (m *GetQuickMatchConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickMatchConfigResp.Unmarshal(m, b)
}
func (m *GetQuickMatchConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickMatchConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetQuickMatchConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickMatchConfigResp.Merge(dst, src)
}
func (m *GetQuickMatchConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetQuickMatchConfigResp.Size(m)
}
func (m *GetQuickMatchConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickMatchConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickMatchConfigResp proto.InternalMessageInfo

func (m *GetQuickMatchConfigResp) GetSceneInfo() []*SceneInfo {
	if m != nil {
		return m.SceneInfo
	}
	return nil
}

type UpdateQuickMatchConfigReq struct {
	SceneInfo            *SceneInfo `protobuf:"bytes,1,opt,name=scene_info,json=sceneInfo,proto3" json:"scene_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *UpdateQuickMatchConfigReq) Reset()         { *m = UpdateQuickMatchConfigReq{} }
func (m *UpdateQuickMatchConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdateQuickMatchConfigReq) ProtoMessage()    {}
func (*UpdateQuickMatchConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{33}
}
func (m *UpdateQuickMatchConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateQuickMatchConfigReq.Unmarshal(m, b)
}
func (m *UpdateQuickMatchConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateQuickMatchConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdateQuickMatchConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateQuickMatchConfigReq.Merge(dst, src)
}
func (m *UpdateQuickMatchConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdateQuickMatchConfigReq.Size(m)
}
func (m *UpdateQuickMatchConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateQuickMatchConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateQuickMatchConfigReq proto.InternalMessageInfo

func (m *UpdateQuickMatchConfigReq) GetSceneInfo() *SceneInfo {
	if m != nil {
		return m.SceneInfo
	}
	return nil
}

type UpdateQuickMatchConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateQuickMatchConfigResp) Reset()         { *m = UpdateQuickMatchConfigResp{} }
func (m *UpdateQuickMatchConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdateQuickMatchConfigResp) ProtoMessage()    {}
func (*UpdateQuickMatchConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{34}
}
func (m *UpdateQuickMatchConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateQuickMatchConfigResp.Unmarshal(m, b)
}
func (m *UpdateQuickMatchConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateQuickMatchConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdateQuickMatchConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateQuickMatchConfigResp.Merge(dst, src)
}
func (m *UpdateQuickMatchConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdateQuickMatchConfigResp.Size(m)
}
func (m *UpdateQuickMatchConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateQuickMatchConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateQuickMatchConfigResp proto.InternalMessageInfo

type DeleteQuickMatchConfigReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteQuickMatchConfigReq) Reset()         { *m = DeleteQuickMatchConfigReq{} }
func (m *DeleteQuickMatchConfigReq) String() string { return proto.CompactTextString(m) }
func (*DeleteQuickMatchConfigReq) ProtoMessage()    {}
func (*DeleteQuickMatchConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{35}
}
func (m *DeleteQuickMatchConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteQuickMatchConfigReq.Unmarshal(m, b)
}
func (m *DeleteQuickMatchConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteQuickMatchConfigReq.Marshal(b, m, deterministic)
}
func (dst *DeleteQuickMatchConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteQuickMatchConfigReq.Merge(dst, src)
}
func (m *DeleteQuickMatchConfigReq) XXX_Size() int {
	return xxx_messageInfo_DeleteQuickMatchConfigReq.Size(m)
}
func (m *DeleteQuickMatchConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteQuickMatchConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteQuickMatchConfigReq proto.InternalMessageInfo

func (m *DeleteQuickMatchConfigReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DeleteQuickMatchConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteQuickMatchConfigResp) Reset()         { *m = DeleteQuickMatchConfigResp{} }
func (m *DeleteQuickMatchConfigResp) String() string { return proto.CompactTextString(m) }
func (*DeleteQuickMatchConfigResp) ProtoMessage()    {}
func (*DeleteQuickMatchConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{36}
}
func (m *DeleteQuickMatchConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteQuickMatchConfigResp.Unmarshal(m, b)
}
func (m *DeleteQuickMatchConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteQuickMatchConfigResp.Marshal(b, m, deterministic)
}
func (dst *DeleteQuickMatchConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteQuickMatchConfigResp.Merge(dst, src)
}
func (m *DeleteQuickMatchConfigResp) XXX_Size() int {
	return xxx_messageInfo_DeleteQuickMatchConfigResp.Size(m)
}
func (m *DeleteQuickMatchConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteQuickMatchConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteQuickMatchConfigResp proto.InternalMessageInfo

type ResortQuickMatchConfigReq struct {
	Ids                  []string `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResortQuickMatchConfigReq) Reset()         { *m = ResortQuickMatchConfigReq{} }
func (m *ResortQuickMatchConfigReq) String() string { return proto.CompactTextString(m) }
func (*ResortQuickMatchConfigReq) ProtoMessage()    {}
func (*ResortQuickMatchConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{37}
}
func (m *ResortQuickMatchConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResortQuickMatchConfigReq.Unmarshal(m, b)
}
func (m *ResortQuickMatchConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResortQuickMatchConfigReq.Marshal(b, m, deterministic)
}
func (dst *ResortQuickMatchConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResortQuickMatchConfigReq.Merge(dst, src)
}
func (m *ResortQuickMatchConfigReq) XXX_Size() int {
	return xxx_messageInfo_ResortQuickMatchConfigReq.Size(m)
}
func (m *ResortQuickMatchConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ResortQuickMatchConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_ResortQuickMatchConfigReq proto.InternalMessageInfo

func (m *ResortQuickMatchConfigReq) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

type ResortQuickMatchConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResortQuickMatchConfigResp) Reset()         { *m = ResortQuickMatchConfigResp{} }
func (m *ResortQuickMatchConfigResp) String() string { return proto.CompactTextString(m) }
func (*ResortQuickMatchConfigResp) ProtoMessage()    {}
func (*ResortQuickMatchConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{38}
}
func (m *ResortQuickMatchConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResortQuickMatchConfigResp.Unmarshal(m, b)
}
func (m *ResortQuickMatchConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResortQuickMatchConfigResp.Marshal(b, m, deterministic)
}
func (dst *ResortQuickMatchConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResortQuickMatchConfigResp.Merge(dst, src)
}
func (m *ResortQuickMatchConfigResp) XXX_Size() int {
	return xxx_messageInfo_ResortQuickMatchConfigResp.Size(m)
}
func (m *ResortQuickMatchConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ResortQuickMatchConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_ResortQuickMatchConfigResp proto.InternalMessageInfo

type NewQuickMatchConfig struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string   `protobuf:"bytes,3,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	Title                string   `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	ButtonText           string   `protobuf:"bytes,5,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"`
	Position             uint32   `protobuf:"varint,6,opt,name=position,proto3" json:"position,omitempty"`
	UpdatedAt            int64    `protobuf:"varint,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewQuickMatchConfig) Reset()         { *m = NewQuickMatchConfig{} }
func (m *NewQuickMatchConfig) String() string { return proto.CompactTextString(m) }
func (*NewQuickMatchConfig) ProtoMessage()    {}
func (*NewQuickMatchConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{39}
}
func (m *NewQuickMatchConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewQuickMatchConfig.Unmarshal(m, b)
}
func (m *NewQuickMatchConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewQuickMatchConfig.Marshal(b, m, deterministic)
}
func (dst *NewQuickMatchConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewQuickMatchConfig.Merge(dst, src)
}
func (m *NewQuickMatchConfig) XXX_Size() int {
	return xxx_messageInfo_NewQuickMatchConfig.Size(m)
}
func (m *NewQuickMatchConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_NewQuickMatchConfig.DiscardUnknown(m)
}

var xxx_messageInfo_NewQuickMatchConfig proto.InternalMessageInfo

func (m *NewQuickMatchConfig) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *NewQuickMatchConfig) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *NewQuickMatchConfig) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *NewQuickMatchConfig) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *NewQuickMatchConfig) GetButtonText() string {
	if m != nil {
		return m.ButtonText
	}
	return ""
}

func (m *NewQuickMatchConfig) GetPosition() uint32 {
	if m != nil {
		return m.Position
	}
	return 0
}

func (m *NewQuickMatchConfig) GetUpdatedAt() int64 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

type UpsertNewQuickMatchConfigReq struct {
	Config               *NewQuickMatchConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UpsertNewQuickMatchConfigReq) Reset()         { *m = UpsertNewQuickMatchConfigReq{} }
func (m *UpsertNewQuickMatchConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpsertNewQuickMatchConfigReq) ProtoMessage()    {}
func (*UpsertNewQuickMatchConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{40}
}
func (m *UpsertNewQuickMatchConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertNewQuickMatchConfigReq.Unmarshal(m, b)
}
func (m *UpsertNewQuickMatchConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertNewQuickMatchConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpsertNewQuickMatchConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertNewQuickMatchConfigReq.Merge(dst, src)
}
func (m *UpsertNewQuickMatchConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpsertNewQuickMatchConfigReq.Size(m)
}
func (m *UpsertNewQuickMatchConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertNewQuickMatchConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertNewQuickMatchConfigReq proto.InternalMessageInfo

func (m *UpsertNewQuickMatchConfigReq) GetConfig() *NewQuickMatchConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type UpsertNewQuickMatchConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertNewQuickMatchConfigResp) Reset()         { *m = UpsertNewQuickMatchConfigResp{} }
func (m *UpsertNewQuickMatchConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpsertNewQuickMatchConfigResp) ProtoMessage()    {}
func (*UpsertNewQuickMatchConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{41}
}
func (m *UpsertNewQuickMatchConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertNewQuickMatchConfigResp.Unmarshal(m, b)
}
func (m *UpsertNewQuickMatchConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertNewQuickMatchConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpsertNewQuickMatchConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertNewQuickMatchConfigResp.Merge(dst, src)
}
func (m *UpsertNewQuickMatchConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpsertNewQuickMatchConfigResp.Size(m)
}
func (m *UpsertNewQuickMatchConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertNewQuickMatchConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertNewQuickMatchConfigResp proto.InternalMessageInfo

type BatchGetNewQuickMatchConfigReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabIds               []uint32 `protobuf:"varint,2,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetNewQuickMatchConfigReq) Reset()         { *m = BatchGetNewQuickMatchConfigReq{} }
func (m *BatchGetNewQuickMatchConfigReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetNewQuickMatchConfigReq) ProtoMessage()    {}
func (*BatchGetNewQuickMatchConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{42}
}
func (m *BatchGetNewQuickMatchConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetNewQuickMatchConfigReq.Unmarshal(m, b)
}
func (m *BatchGetNewQuickMatchConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetNewQuickMatchConfigReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetNewQuickMatchConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetNewQuickMatchConfigReq.Merge(dst, src)
}
func (m *BatchGetNewQuickMatchConfigReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetNewQuickMatchConfigReq.Size(m)
}
func (m *BatchGetNewQuickMatchConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetNewQuickMatchConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetNewQuickMatchConfigReq proto.InternalMessageInfo

func (m *BatchGetNewQuickMatchConfigReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *BatchGetNewQuickMatchConfigReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

type BatchGetNewQuickMatchConfigResp struct {
	Configs              []*NewQuickMatchConfig `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *BatchGetNewQuickMatchConfigResp) Reset()         { *m = BatchGetNewQuickMatchConfigResp{} }
func (m *BatchGetNewQuickMatchConfigResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetNewQuickMatchConfigResp) ProtoMessage()    {}
func (*BatchGetNewQuickMatchConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{43}
}
func (m *BatchGetNewQuickMatchConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetNewQuickMatchConfigResp.Unmarshal(m, b)
}
func (m *BatchGetNewQuickMatchConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetNewQuickMatchConfigResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetNewQuickMatchConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetNewQuickMatchConfigResp.Merge(dst, src)
}
func (m *BatchGetNewQuickMatchConfigResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetNewQuickMatchConfigResp.Size(m)
}
func (m *BatchGetNewQuickMatchConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetNewQuickMatchConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetNewQuickMatchConfigResp proto.InternalMessageInfo

func (m *BatchGetNewQuickMatchConfigResp) GetConfigs() []*NewQuickMatchConfig {
	if m != nil {
		return m.Configs
	}
	return nil
}

type DelNewQuickMatchConfigReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelNewQuickMatchConfigReq) Reset()         { *m = DelNewQuickMatchConfigReq{} }
func (m *DelNewQuickMatchConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelNewQuickMatchConfigReq) ProtoMessage()    {}
func (*DelNewQuickMatchConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{44}
}
func (m *DelNewQuickMatchConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelNewQuickMatchConfigReq.Unmarshal(m, b)
}
func (m *DelNewQuickMatchConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelNewQuickMatchConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelNewQuickMatchConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelNewQuickMatchConfigReq.Merge(dst, src)
}
func (m *DelNewQuickMatchConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelNewQuickMatchConfigReq.Size(m)
}
func (m *DelNewQuickMatchConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelNewQuickMatchConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelNewQuickMatchConfigReq proto.InternalMessageInfo

func (m *DelNewQuickMatchConfigReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DelNewQuickMatchConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelNewQuickMatchConfigResp) Reset()         { *m = DelNewQuickMatchConfigResp{} }
func (m *DelNewQuickMatchConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelNewQuickMatchConfigResp) ProtoMessage()    {}
func (*DelNewQuickMatchConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{45}
}
func (m *DelNewQuickMatchConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelNewQuickMatchConfigResp.Unmarshal(m, b)
}
func (m *DelNewQuickMatchConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelNewQuickMatchConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelNewQuickMatchConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelNewQuickMatchConfigResp.Merge(dst, src)
}
func (m *DelNewQuickMatchConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelNewQuickMatchConfigResp.Size(m)
}
func (m *DelNewQuickMatchConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelNewQuickMatchConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelNewQuickMatchConfigResp proto.InternalMessageInfo

type GetNewQuickMatchConfigReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewQuickMatchConfigReq) Reset()         { *m = GetNewQuickMatchConfigReq{} }
func (m *GetNewQuickMatchConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetNewQuickMatchConfigReq) ProtoMessage()    {}
func (*GetNewQuickMatchConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{46}
}
func (m *GetNewQuickMatchConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewQuickMatchConfigReq.Unmarshal(m, b)
}
func (m *GetNewQuickMatchConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewQuickMatchConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetNewQuickMatchConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewQuickMatchConfigReq.Merge(dst, src)
}
func (m *GetNewQuickMatchConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetNewQuickMatchConfigReq.Size(m)
}
func (m *GetNewQuickMatchConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewQuickMatchConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewQuickMatchConfigReq proto.InternalMessageInfo

func (m *GetNewQuickMatchConfigReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetNewQuickMatchConfigResp struct {
	Config               *NewQuickMatchConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetNewQuickMatchConfigResp) Reset()         { *m = GetNewQuickMatchConfigResp{} }
func (m *GetNewQuickMatchConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetNewQuickMatchConfigResp) ProtoMessage()    {}
func (*GetNewQuickMatchConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{47}
}
func (m *GetNewQuickMatchConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewQuickMatchConfigResp.Unmarshal(m, b)
}
func (m *GetNewQuickMatchConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewQuickMatchConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetNewQuickMatchConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewQuickMatchConfigResp.Merge(dst, src)
}
func (m *GetNewQuickMatchConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetNewQuickMatchConfigResp.Size(m)
}
func (m *GetNewQuickMatchConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewQuickMatchConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewQuickMatchConfigResp proto.InternalMessageInfo

func (m *GetNewQuickMatchConfigResp) GetConfig() *NewQuickMatchConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type GetFastPCCategoryConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFastPCCategoryConfigReq) Reset()         { *m = GetFastPCCategoryConfigReq{} }
func (m *GetFastPCCategoryConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetFastPCCategoryConfigReq) ProtoMessage()    {}
func (*GetFastPCCategoryConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{48}
}
func (m *GetFastPCCategoryConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFastPCCategoryConfigReq.Unmarshal(m, b)
}
func (m *GetFastPCCategoryConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFastPCCategoryConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetFastPCCategoryConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFastPCCategoryConfigReq.Merge(dst, src)
}
func (m *GetFastPCCategoryConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetFastPCCategoryConfigReq.Size(m)
}
func (m *GetFastPCCategoryConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFastPCCategoryConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFastPCCategoryConfigReq proto.InternalMessageInfo

type FastPCCategoryConfig struct {
	Id   uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 分类下玩法排序按数组位序
	TabIds []uint32 `protobuf:"varint,3,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	// 分类排序
	Sort      uint32 `protobuf:"varint,4,opt,name=sort,proto3" json:"sort,omitempty"`
	UpdatedAt int64  `protobuf:"varint,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// 分类图标
	Icon string `protobuf:"bytes,6,opt,name=icon,proto3" json:"icon,omitempty"`
	// 分类类型
	CategoryType         FastPCCategoryType `protobuf:"varint,7,opt,name=category_type,json=categoryType,proto3,enum=channel_play_tab.FastPCCategoryType" json:"category_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *FastPCCategoryConfig) Reset()         { *m = FastPCCategoryConfig{} }
func (m *FastPCCategoryConfig) String() string { return proto.CompactTextString(m) }
func (*FastPCCategoryConfig) ProtoMessage()    {}
func (*FastPCCategoryConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{49}
}
func (m *FastPCCategoryConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FastPCCategoryConfig.Unmarshal(m, b)
}
func (m *FastPCCategoryConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FastPCCategoryConfig.Marshal(b, m, deterministic)
}
func (dst *FastPCCategoryConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FastPCCategoryConfig.Merge(dst, src)
}
func (m *FastPCCategoryConfig) XXX_Size() int {
	return xxx_messageInfo_FastPCCategoryConfig.Size(m)
}
func (m *FastPCCategoryConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_FastPCCategoryConfig.DiscardUnknown(m)
}

var xxx_messageInfo_FastPCCategoryConfig proto.InternalMessageInfo

func (m *FastPCCategoryConfig) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *FastPCCategoryConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *FastPCCategoryConfig) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *FastPCCategoryConfig) GetSort() uint32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func (m *FastPCCategoryConfig) GetUpdatedAt() int64 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

func (m *FastPCCategoryConfig) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *FastPCCategoryConfig) GetCategoryType() FastPCCategoryType {
	if m != nil {
		return m.CategoryType
	}
	return FastPCCategoryType_FAST_PC_CATEGORY_TYPE_NORMAL
}

type GetFastPCCategoryConfigResp struct {
	Configs              []*FastPCCategoryConfig `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetFastPCCategoryConfigResp) Reset()         { *m = GetFastPCCategoryConfigResp{} }
func (m *GetFastPCCategoryConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetFastPCCategoryConfigResp) ProtoMessage()    {}
func (*GetFastPCCategoryConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{50}
}
func (m *GetFastPCCategoryConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFastPCCategoryConfigResp.Unmarshal(m, b)
}
func (m *GetFastPCCategoryConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFastPCCategoryConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetFastPCCategoryConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFastPCCategoryConfigResp.Merge(dst, src)
}
func (m *GetFastPCCategoryConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetFastPCCategoryConfigResp.Size(m)
}
func (m *GetFastPCCategoryConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFastPCCategoryConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFastPCCategoryConfigResp proto.InternalMessageInfo

func (m *GetFastPCCategoryConfigResp) GetConfigs() []*FastPCCategoryConfig {
	if m != nil {
		return m.Configs
	}
	return nil
}

type UpsertFastPCCategoryConfigReq struct {
	Config               *FastPCCategoryConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *UpsertFastPCCategoryConfigReq) Reset()         { *m = UpsertFastPCCategoryConfigReq{} }
func (m *UpsertFastPCCategoryConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpsertFastPCCategoryConfigReq) ProtoMessage()    {}
func (*UpsertFastPCCategoryConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{51}
}
func (m *UpsertFastPCCategoryConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertFastPCCategoryConfigReq.Unmarshal(m, b)
}
func (m *UpsertFastPCCategoryConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertFastPCCategoryConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpsertFastPCCategoryConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertFastPCCategoryConfigReq.Merge(dst, src)
}
func (m *UpsertFastPCCategoryConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpsertFastPCCategoryConfigReq.Size(m)
}
func (m *UpsertFastPCCategoryConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertFastPCCategoryConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertFastPCCategoryConfigReq proto.InternalMessageInfo

func (m *UpsertFastPCCategoryConfigReq) GetConfig() *FastPCCategoryConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type UpsertFastPCCategoryConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertFastPCCategoryConfigResp) Reset()         { *m = UpsertFastPCCategoryConfigResp{} }
func (m *UpsertFastPCCategoryConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpsertFastPCCategoryConfigResp) ProtoMessage()    {}
func (*UpsertFastPCCategoryConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{52}
}
func (m *UpsertFastPCCategoryConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertFastPCCategoryConfigResp.Unmarshal(m, b)
}
func (m *UpsertFastPCCategoryConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertFastPCCategoryConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpsertFastPCCategoryConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertFastPCCategoryConfigResp.Merge(dst, src)
}
func (m *UpsertFastPCCategoryConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpsertFastPCCategoryConfigResp.Size(m)
}
func (m *UpsertFastPCCategoryConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertFastPCCategoryConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertFastPCCategoryConfigResp proto.InternalMessageInfo

type DelFastPCCategoryConfigReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelFastPCCategoryConfigReq) Reset()         { *m = DelFastPCCategoryConfigReq{} }
func (m *DelFastPCCategoryConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelFastPCCategoryConfigReq) ProtoMessage()    {}
func (*DelFastPCCategoryConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{53}
}
func (m *DelFastPCCategoryConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelFastPCCategoryConfigReq.Unmarshal(m, b)
}
func (m *DelFastPCCategoryConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelFastPCCategoryConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelFastPCCategoryConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelFastPCCategoryConfigReq.Merge(dst, src)
}
func (m *DelFastPCCategoryConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelFastPCCategoryConfigReq.Size(m)
}
func (m *DelFastPCCategoryConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelFastPCCategoryConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelFastPCCategoryConfigReq proto.InternalMessageInfo

func (m *DelFastPCCategoryConfigReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelFastPCCategoryConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelFastPCCategoryConfigResp) Reset()         { *m = DelFastPCCategoryConfigResp{} }
func (m *DelFastPCCategoryConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelFastPCCategoryConfigResp) ProtoMessage()    {}
func (*DelFastPCCategoryConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{54}
}
func (m *DelFastPCCategoryConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelFastPCCategoryConfigResp.Unmarshal(m, b)
}
func (m *DelFastPCCategoryConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelFastPCCategoryConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelFastPCCategoryConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelFastPCCategoryConfigResp.Merge(dst, src)
}
func (m *DelFastPCCategoryConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelFastPCCategoryConfigResp.Size(m)
}
func (m *DelFastPCCategoryConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelFastPCCategoryConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelFastPCCategoryConfigResp proto.InternalMessageInfo

type SortFastPCCategoryConfigReq struct {
	Ids                  []uint32 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SortFastPCCategoryConfigReq) Reset()         { *m = SortFastPCCategoryConfigReq{} }
func (m *SortFastPCCategoryConfigReq) String() string { return proto.CompactTextString(m) }
func (*SortFastPCCategoryConfigReq) ProtoMessage()    {}
func (*SortFastPCCategoryConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{55}
}
func (m *SortFastPCCategoryConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SortFastPCCategoryConfigReq.Unmarshal(m, b)
}
func (m *SortFastPCCategoryConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SortFastPCCategoryConfigReq.Marshal(b, m, deterministic)
}
func (dst *SortFastPCCategoryConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SortFastPCCategoryConfigReq.Merge(dst, src)
}
func (m *SortFastPCCategoryConfigReq) XXX_Size() int {
	return xxx_messageInfo_SortFastPCCategoryConfigReq.Size(m)
}
func (m *SortFastPCCategoryConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SortFastPCCategoryConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_SortFastPCCategoryConfigReq proto.InternalMessageInfo

func (m *SortFastPCCategoryConfigReq) GetIds() []uint32 {
	if m != nil {
		return m.Ids
	}
	return nil
}

type SortFastPCCategoryConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SortFastPCCategoryConfigResp) Reset()         { *m = SortFastPCCategoryConfigResp{} }
func (m *SortFastPCCategoryConfigResp) String() string { return proto.CompactTextString(m) }
func (*SortFastPCCategoryConfigResp) ProtoMessage()    {}
func (*SortFastPCCategoryConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{56}
}
func (m *SortFastPCCategoryConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SortFastPCCategoryConfigResp.Unmarshal(m, b)
}
func (m *SortFastPCCategoryConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SortFastPCCategoryConfigResp.Marshal(b, m, deterministic)
}
func (dst *SortFastPCCategoryConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SortFastPCCategoryConfigResp.Merge(dst, src)
}
func (m *SortFastPCCategoryConfigResp) XXX_Size() int {
	return xxx_messageInfo_SortFastPCCategoryConfigResp.Size(m)
}
func (m *SortFastPCCategoryConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SortFastPCCategoryConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_SortFastPCCategoryConfigResp proto.InternalMessageInfo

type GetAllTabInfoExtReq struct {
	ExtType              TabInfoExtEnum `protobuf:"varint,1,opt,name=ext_type,json=extType,proto3,enum=channel_play_tab.TabInfoExtEnum" json:"ext_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetAllTabInfoExtReq) Reset()         { *m = GetAllTabInfoExtReq{} }
func (m *GetAllTabInfoExtReq) String() string { return proto.CompactTextString(m) }
func (*GetAllTabInfoExtReq) ProtoMessage()    {}
func (*GetAllTabInfoExtReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{57}
}
func (m *GetAllTabInfoExtReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllTabInfoExtReq.Unmarshal(m, b)
}
func (m *GetAllTabInfoExtReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllTabInfoExtReq.Marshal(b, m, deterministic)
}
func (dst *GetAllTabInfoExtReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllTabInfoExtReq.Merge(dst, src)
}
func (m *GetAllTabInfoExtReq) XXX_Size() int {
	return xxx_messageInfo_GetAllTabInfoExtReq.Size(m)
}
func (m *GetAllTabInfoExtReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllTabInfoExtReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllTabInfoExtReq proto.InternalMessageInfo

func (m *GetAllTabInfoExtReq) GetExtType() TabInfoExtEnum {
	if m != nil {
		return m.ExtType
	}
	return TabInfoExtEnum_TAB_INFO_EXT_NO_TYPE
}

type TabInfoExtItem struct {
	TabId    uint32         `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	ExtType  TabInfoExtEnum `protobuf:"varint,2,opt,name=ext_type,json=extType,proto3,enum=channel_play_tab.TabInfoExtEnum" json:"ext_type,omitempty"`
	UpdateTs int64          `protobuf:"varint,4,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	// Types that are valid to be assigned to ExtContent:
	//	*TabInfoExtItem_FastPcConfig
	ExtContent           isTabInfoExtItem_ExtContent `protobuf_oneof:"ext_content"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *TabInfoExtItem) Reset()         { *m = TabInfoExtItem{} }
func (m *TabInfoExtItem) String() string { return proto.CompactTextString(m) }
func (*TabInfoExtItem) ProtoMessage()    {}
func (*TabInfoExtItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{58}
}
func (m *TabInfoExtItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TabInfoExtItem.Unmarshal(m, b)
}
func (m *TabInfoExtItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TabInfoExtItem.Marshal(b, m, deterministic)
}
func (dst *TabInfoExtItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabInfoExtItem.Merge(dst, src)
}
func (m *TabInfoExtItem) XXX_Size() int {
	return xxx_messageInfo_TabInfoExtItem.Size(m)
}
func (m *TabInfoExtItem) XXX_DiscardUnknown() {
	xxx_messageInfo_TabInfoExtItem.DiscardUnknown(m)
}

var xxx_messageInfo_TabInfoExtItem proto.InternalMessageInfo

func (m *TabInfoExtItem) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *TabInfoExtItem) GetExtType() TabInfoExtEnum {
	if m != nil {
		return m.ExtType
	}
	return TabInfoExtEnum_TAB_INFO_EXT_NO_TYPE
}

func (m *TabInfoExtItem) GetUpdateTs() int64 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

type isTabInfoExtItem_ExtContent interface {
	isTabInfoExtItem_ExtContent()
}

type TabInfoExtItem_FastPcConfig struct {
	FastPcConfig *TabInfoExtFastPcConfig `protobuf:"bytes,6,opt,name=fast_pc_config,json=fastPcConfig,proto3,oneof"`
}

func (*TabInfoExtItem_FastPcConfig) isTabInfoExtItem_ExtContent() {}

func (m *TabInfoExtItem) GetExtContent() isTabInfoExtItem_ExtContent {
	if m != nil {
		return m.ExtContent
	}
	return nil
}

func (m *TabInfoExtItem) GetFastPcConfig() *TabInfoExtFastPcConfig {
	if x, ok := m.GetExtContent().(*TabInfoExtItem_FastPcConfig); ok {
		return x.FastPcConfig
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*TabInfoExtItem) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _TabInfoExtItem_OneofMarshaler, _TabInfoExtItem_OneofUnmarshaler, _TabInfoExtItem_OneofSizer, []interface{}{
		(*TabInfoExtItem_FastPcConfig)(nil),
	}
}

func _TabInfoExtItem_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*TabInfoExtItem)
	// ext_content
	switch x := m.ExtContent.(type) {
	case *TabInfoExtItem_FastPcConfig:
		b.EncodeVarint(6<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.FastPcConfig); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("TabInfoExtItem.ExtContent has unexpected type %T", x)
	}
	return nil
}

func _TabInfoExtItem_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*TabInfoExtItem)
	switch tag {
	case 6: // ext_content.fast_pc_config
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(TabInfoExtFastPcConfig)
		err := b.DecodeMessage(msg)
		m.ExtContent = &TabInfoExtItem_FastPcConfig{msg}
		return true, err
	default:
		return false, nil
	}
}

func _TabInfoExtItem_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*TabInfoExtItem)
	// ext_content
	switch x := m.ExtContent.(type) {
	case *TabInfoExtItem_FastPcConfig:
		s := proto.Size(x.FastPcConfig)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type TabInfoExtFastPcConfig struct {
	// 新增PC极速版背景图
	FastPcHomePageBackgroundImgUrl string `protobuf:"bytes,1,opt,name=fast_pc_home_page_background_img_url,json=fastPcHomePageBackgroundImgUrl,proto3" json:"fast_pc_home_page_background_img_url,omitempty"`
	// 新增PC极速版大厅房间背景图
	FastPcRoomBackgroundImgUrl string   `protobuf:"bytes,2,opt,name=fast_pc_room_background_img_url,json=fastPcRoomBackgroundImgUrl,proto3" json:"fast_pc_room_background_img_url,omitempty"`
	XXX_NoUnkeyedLiteral       struct{} `json:"-"`
	XXX_unrecognized           []byte   `json:"-"`
	XXX_sizecache              int32    `json:"-"`
}

func (m *TabInfoExtFastPcConfig) Reset()         { *m = TabInfoExtFastPcConfig{} }
func (m *TabInfoExtFastPcConfig) String() string { return proto.CompactTextString(m) }
func (*TabInfoExtFastPcConfig) ProtoMessage()    {}
func (*TabInfoExtFastPcConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{59}
}
func (m *TabInfoExtFastPcConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TabInfoExtFastPcConfig.Unmarshal(m, b)
}
func (m *TabInfoExtFastPcConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TabInfoExtFastPcConfig.Marshal(b, m, deterministic)
}
func (dst *TabInfoExtFastPcConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabInfoExtFastPcConfig.Merge(dst, src)
}
func (m *TabInfoExtFastPcConfig) XXX_Size() int {
	return xxx_messageInfo_TabInfoExtFastPcConfig.Size(m)
}
func (m *TabInfoExtFastPcConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_TabInfoExtFastPcConfig.DiscardUnknown(m)
}

var xxx_messageInfo_TabInfoExtFastPcConfig proto.InternalMessageInfo

func (m *TabInfoExtFastPcConfig) GetFastPcHomePageBackgroundImgUrl() string {
	if m != nil {
		return m.FastPcHomePageBackgroundImgUrl
	}
	return ""
}

func (m *TabInfoExtFastPcConfig) GetFastPcRoomBackgroundImgUrl() string {
	if m != nil {
		return m.FastPcRoomBackgroundImgUrl
	}
	return ""
}

type GetAllTabInfoExtResp struct {
	Items                []*TabInfoExtItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetAllTabInfoExtResp) Reset()         { *m = GetAllTabInfoExtResp{} }
func (m *GetAllTabInfoExtResp) String() string { return proto.CompactTextString(m) }
func (*GetAllTabInfoExtResp) ProtoMessage()    {}
func (*GetAllTabInfoExtResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{60}
}
func (m *GetAllTabInfoExtResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllTabInfoExtResp.Unmarshal(m, b)
}
func (m *GetAllTabInfoExtResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllTabInfoExtResp.Marshal(b, m, deterministic)
}
func (dst *GetAllTabInfoExtResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllTabInfoExtResp.Merge(dst, src)
}
func (m *GetAllTabInfoExtResp) XXX_Size() int {
	return xxx_messageInfo_GetAllTabInfoExtResp.Size(m)
}
func (m *GetAllTabInfoExtResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllTabInfoExtResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllTabInfoExtResp proto.InternalMessageInfo

func (m *GetAllTabInfoExtResp) GetItems() []*TabInfoExtItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type UpsertTabInfoExtReq struct {
	Item                 *TabInfoExtItem `protobuf:"bytes,1,opt,name=item,proto3" json:"item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UpsertTabInfoExtReq) Reset()         { *m = UpsertTabInfoExtReq{} }
func (m *UpsertTabInfoExtReq) String() string { return proto.CompactTextString(m) }
func (*UpsertTabInfoExtReq) ProtoMessage()    {}
func (*UpsertTabInfoExtReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{61}
}
func (m *UpsertTabInfoExtReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertTabInfoExtReq.Unmarshal(m, b)
}
func (m *UpsertTabInfoExtReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertTabInfoExtReq.Marshal(b, m, deterministic)
}
func (dst *UpsertTabInfoExtReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertTabInfoExtReq.Merge(dst, src)
}
func (m *UpsertTabInfoExtReq) XXX_Size() int {
	return xxx_messageInfo_UpsertTabInfoExtReq.Size(m)
}
func (m *UpsertTabInfoExtReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertTabInfoExtReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertTabInfoExtReq proto.InternalMessageInfo

func (m *UpsertTabInfoExtReq) GetItem() *TabInfoExtItem {
	if m != nil {
		return m.Item
	}
	return nil
}

type UpsertTabInfoExtResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertTabInfoExtResp) Reset()         { *m = UpsertTabInfoExtResp{} }
func (m *UpsertTabInfoExtResp) String() string { return proto.CompactTextString(m) }
func (*UpsertTabInfoExtResp) ProtoMessage()    {}
func (*UpsertTabInfoExtResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{62}
}
func (m *UpsertTabInfoExtResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertTabInfoExtResp.Unmarshal(m, b)
}
func (m *UpsertTabInfoExtResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertTabInfoExtResp.Marshal(b, m, deterministic)
}
func (dst *UpsertTabInfoExtResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertTabInfoExtResp.Merge(dst, src)
}
func (m *UpsertTabInfoExtResp) XXX_Size() int {
	return xxx_messageInfo_UpsertTabInfoExtResp.Size(m)
}
func (m *UpsertTabInfoExtResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertTabInfoExtResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertTabInfoExtResp proto.InternalMessageInfo

type DelTabInfoExtReq struct {
	TabId                uint32         `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	ExtType              TabInfoExtEnum `protobuf:"varint,2,opt,name=ext_type,json=extType,proto3,enum=channel_play_tab.TabInfoExtEnum" json:"ext_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *DelTabInfoExtReq) Reset()         { *m = DelTabInfoExtReq{} }
func (m *DelTabInfoExtReq) String() string { return proto.CompactTextString(m) }
func (*DelTabInfoExtReq) ProtoMessage()    {}
func (*DelTabInfoExtReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{63}
}
func (m *DelTabInfoExtReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTabInfoExtReq.Unmarshal(m, b)
}
func (m *DelTabInfoExtReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTabInfoExtReq.Marshal(b, m, deterministic)
}
func (dst *DelTabInfoExtReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTabInfoExtReq.Merge(dst, src)
}
func (m *DelTabInfoExtReq) XXX_Size() int {
	return xxx_messageInfo_DelTabInfoExtReq.Size(m)
}
func (m *DelTabInfoExtReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTabInfoExtReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelTabInfoExtReq proto.InternalMessageInfo

func (m *DelTabInfoExtReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *DelTabInfoExtReq) GetExtType() TabInfoExtEnum {
	if m != nil {
		return m.ExtType
	}
	return TabInfoExtEnum_TAB_INFO_EXT_NO_TYPE
}

type DelTabInfoExtResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelTabInfoExtResp) Reset()         { *m = DelTabInfoExtResp{} }
func (m *DelTabInfoExtResp) String() string { return proto.CompactTextString(m) }
func (*DelTabInfoExtResp) ProtoMessage()    {}
func (*DelTabInfoExtResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{64}
}
func (m *DelTabInfoExtResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTabInfoExtResp.Unmarshal(m, b)
}
func (m *DelTabInfoExtResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTabInfoExtResp.Marshal(b, m, deterministic)
}
func (dst *DelTabInfoExtResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTabInfoExtResp.Merge(dst, src)
}
func (m *DelTabInfoExtResp) XXX_Size() int {
	return xxx_messageInfo_DelTabInfoExtResp.Size(m)
}
func (m *DelTabInfoExtResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTabInfoExtResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelTabInfoExtResp proto.InternalMessageInfo

type MultiUserInfo struct {
	Ttid                 string   `protobuf:"bytes,1,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	UserNickname         string   `protobuf:"bytes,3,opt,name=user_nickname,json=userNickname,proto3" json:"user_nickname,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MultiUserInfo) Reset()         { *m = MultiUserInfo{} }
func (m *MultiUserInfo) String() string { return proto.CompactTextString(m) }
func (*MultiUserInfo) ProtoMessage()    {}
func (*MultiUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{65}
}
func (m *MultiUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiUserInfo.Unmarshal(m, b)
}
func (m *MultiUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiUserInfo.Marshal(b, m, deterministic)
}
func (dst *MultiUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiUserInfo.Merge(dst, src)
}
func (m *MultiUserInfo) XXX_Size() int {
	return xxx_messageInfo_MultiUserInfo.Size(m)
}
func (m *MultiUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MultiUserInfo proto.InternalMessageInfo

func (m *MultiUserInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *MultiUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MultiUserInfo) GetUserNickname() string {
	if m != nil {
		return m.UserNickname
	}
	return ""
}

type BanUserPostConfig struct {
	Id                   string                    `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ConfigType           BanConfigType             `protobuf:"varint,2,opt,name=config_type,json=configType,proto3,enum=channel_play_tab.BanConfigType" json:"config_type,omitempty"`
	Ttid                 string                    `protobuf:"bytes,3,opt,name=ttid,proto3" json:"ttid,omitempty"`
	CrowdGroupId         string                    `protobuf:"bytes,4,opt,name=crowd_group_id,json=crowdGroupId,proto3" json:"crowd_group_id,omitempty"`
	TabIds               []uint32                  `protobuf:"varint,5,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	StartTime            int64                     `protobuf:"varint,6,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64                     `protobuf:"varint,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	BanReason            string                    `protobuf:"bytes,8,opt,name=ban_reason,json=banReason,proto3" json:"ban_reason,omitempty"`
	OperateTime          int64                     `protobuf:"varint,9,opt,name=operate_time,json=operateTime,proto3" json:"operate_time,omitempty"`
	Operator             string                    `protobuf:"bytes,10,opt,name=operator,proto3" json:"operator,omitempty"`
	Status               Status                    `protobuf:"varint,11,opt,name=status,proto3,enum=channel_play_tab.Status" json:"status,omitempty"`
	CrowdGroupName       string                    `protobuf:"bytes,12,opt,name=crowd_group_name,json=crowdGroupName,proto3" json:"crowd_group_name,omitempty"`
	CrowdGroupUserCount  uint32                    `protobuf:"varint,13,opt,name=crowd_group_user_count,json=crowdGroupUserCount,proto3" json:"crowd_group_user_count,omitempty"`
	UserNickname         string                    `protobuf:"bytes,14,opt,name=user_nickname,json=userNickname,proto3" json:"user_nickname,omitempty"`
	BanPostType          BanPostType               `protobuf:"varint,15,opt,name=ban_post_type,json=banPostType,proto3,enum=channel_play_tab.BanPostType" json:"ban_post_type,omitempty"`
	Uid                  uint32                    `protobuf:"varint,16,opt,name=uid,proto3" json:"uid,omitempty"`
	WarningMessage       string                    `protobuf:"bytes,17,opt,name=warning_message,json=warningMessage,proto3" json:"warning_message,omitempty"`
	Ttids                []string                  `protobuf:"bytes,18,rep,name=ttids,proto3" json:"ttids,omitempty"`
	Uids                 []uint32                  `protobuf:"varint,19,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	UserInfoMap          map[string]*MultiUserInfo `protobuf:"bytes,20,rep,name=user_info_map,json=userInfoMap,proto3" json:"user_info_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *BanUserPostConfig) Reset()         { *m = BanUserPostConfig{} }
func (m *BanUserPostConfig) String() string { return proto.CompactTextString(m) }
func (*BanUserPostConfig) ProtoMessage()    {}
func (*BanUserPostConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{66}
}
func (m *BanUserPostConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BanUserPostConfig.Unmarshal(m, b)
}
func (m *BanUserPostConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BanUserPostConfig.Marshal(b, m, deterministic)
}
func (dst *BanUserPostConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BanUserPostConfig.Merge(dst, src)
}
func (m *BanUserPostConfig) XXX_Size() int {
	return xxx_messageInfo_BanUserPostConfig.Size(m)
}
func (m *BanUserPostConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_BanUserPostConfig.DiscardUnknown(m)
}

var xxx_messageInfo_BanUserPostConfig proto.InternalMessageInfo

func (m *BanUserPostConfig) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *BanUserPostConfig) GetConfigType() BanConfigType {
	if m != nil {
		return m.ConfigType
	}
	return BanConfigType_BanConfigTypeNone
}

func (m *BanUserPostConfig) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *BanUserPostConfig) GetCrowdGroupId() string {
	if m != nil {
		return m.CrowdGroupId
	}
	return ""
}

func (m *BanUserPostConfig) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *BanUserPostConfig) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *BanUserPostConfig) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *BanUserPostConfig) GetBanReason() string {
	if m != nil {
		return m.BanReason
	}
	return ""
}

func (m *BanUserPostConfig) GetOperateTime() int64 {
	if m != nil {
		return m.OperateTime
	}
	return 0
}

func (m *BanUserPostConfig) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *BanUserPostConfig) GetStatus() Status {
	if m != nil {
		return m.Status
	}
	return Status_StatusNone
}

func (m *BanUserPostConfig) GetCrowdGroupName() string {
	if m != nil {
		return m.CrowdGroupName
	}
	return ""
}

func (m *BanUserPostConfig) GetCrowdGroupUserCount() uint32 {
	if m != nil {
		return m.CrowdGroupUserCount
	}
	return 0
}

func (m *BanUserPostConfig) GetUserNickname() string {
	if m != nil {
		return m.UserNickname
	}
	return ""
}

func (m *BanUserPostConfig) GetBanPostType() BanPostType {
	if m != nil {
		return m.BanPostType
	}
	return BanPostType_BanPostTypePost
}

func (m *BanUserPostConfig) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BanUserPostConfig) GetWarningMessage() string {
	if m != nil {
		return m.WarningMessage
	}
	return ""
}

func (m *BanUserPostConfig) GetTtids() []string {
	if m != nil {
		return m.Ttids
	}
	return nil
}

func (m *BanUserPostConfig) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *BanUserPostConfig) GetUserInfoMap() map[string]*MultiUserInfo {
	if m != nil {
		return m.UserInfoMap
	}
	return nil
}

// 批量添加禁止用户使用搭子卡功能/屏蔽搭子卡配置
type BatchAddBanUserConfigReq struct {
	Ttids                []string      `protobuf:"bytes,1,rep,name=ttids,proto3" json:"ttids,omitempty"`
	StartTime            int64         `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64         `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	BanReason            string        `protobuf:"bytes,4,opt,name=ban_reason,json=banReason,proto3" json:"ban_reason,omitempty"`
	WarningMessage       string        `protobuf:"bytes,5,opt,name=warning_message,json=warningMessage,proto3" json:"warning_message,omitempty"`
	BanPostTypes         []BanPostType `protobuf:"varint,6,rep,packed,name=ban_post_types,json=banPostTypes,proto3,enum=channel_play_tab.BanPostType" json:"ban_post_types,omitempty"`
	Operator             string        `protobuf:"bytes,7,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *BatchAddBanUserConfigReq) Reset()         { *m = BatchAddBanUserConfigReq{} }
func (m *BatchAddBanUserConfigReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddBanUserConfigReq) ProtoMessage()    {}
func (*BatchAddBanUserConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{67}
}
func (m *BatchAddBanUserConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddBanUserConfigReq.Unmarshal(m, b)
}
func (m *BatchAddBanUserConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddBanUserConfigReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddBanUserConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddBanUserConfigReq.Merge(dst, src)
}
func (m *BatchAddBanUserConfigReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddBanUserConfigReq.Size(m)
}
func (m *BatchAddBanUserConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddBanUserConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddBanUserConfigReq proto.InternalMessageInfo

func (m *BatchAddBanUserConfigReq) GetTtids() []string {
	if m != nil {
		return m.Ttids
	}
	return nil
}

func (m *BatchAddBanUserConfigReq) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *BatchAddBanUserConfigReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *BatchAddBanUserConfigReq) GetBanReason() string {
	if m != nil {
		return m.BanReason
	}
	return ""
}

func (m *BatchAddBanUserConfigReq) GetWarningMessage() string {
	if m != nil {
		return m.WarningMessage
	}
	return ""
}

func (m *BatchAddBanUserConfigReq) GetBanPostTypes() []BanPostType {
	if m != nil {
		return m.BanPostTypes
	}
	return nil
}

func (m *BatchAddBanUserConfigReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type BatchAddBanUserConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddBanUserConfigResp) Reset()         { *m = BatchAddBanUserConfigResp{} }
func (m *BatchAddBanUserConfigResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddBanUserConfigResp) ProtoMessage()    {}
func (*BatchAddBanUserConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{68}
}
func (m *BatchAddBanUserConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddBanUserConfigResp.Unmarshal(m, b)
}
func (m *BatchAddBanUserConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddBanUserConfigResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddBanUserConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddBanUserConfigResp.Merge(dst, src)
}
func (m *BatchAddBanUserConfigResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddBanUserConfigResp.Size(m)
}
func (m *BatchAddBanUserConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddBanUserConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddBanUserConfigResp proto.InternalMessageInfo

// 创建/更新用户禁止配置
type UpsertBanUserConfigReq struct {
	Config               *BanUserPostConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UpsertBanUserConfigReq) Reset()         { *m = UpsertBanUserConfigReq{} }
func (m *UpsertBanUserConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpsertBanUserConfigReq) ProtoMessage()    {}
func (*UpsertBanUserConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{69}
}
func (m *UpsertBanUserConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertBanUserConfigReq.Unmarshal(m, b)
}
func (m *UpsertBanUserConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertBanUserConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpsertBanUserConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertBanUserConfigReq.Merge(dst, src)
}
func (m *UpsertBanUserConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpsertBanUserConfigReq.Size(m)
}
func (m *UpsertBanUserConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertBanUserConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertBanUserConfigReq proto.InternalMessageInfo

func (m *UpsertBanUserConfigReq) GetConfig() *BanUserPostConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type UpsertBanUserConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertBanUserConfigResp) Reset()         { *m = UpsertBanUserConfigResp{} }
func (m *UpsertBanUserConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpsertBanUserConfigResp) ProtoMessage()    {}
func (*UpsertBanUserConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{70}
}
func (m *UpsertBanUserConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertBanUserConfigResp.Unmarshal(m, b)
}
func (m *UpsertBanUserConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertBanUserConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpsertBanUserConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertBanUserConfigResp.Merge(dst, src)
}
func (m *UpsertBanUserConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpsertBanUserConfigResp.Size(m)
}
func (m *UpsertBanUserConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertBanUserConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertBanUserConfigResp proto.InternalMessageInfo

// 获取禁止配置列表
type GetBanUserConfigListReq struct {
	Page                 uint32        `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size                 uint32        `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	NeedCount            bool          `protobuf:"varint,3,opt,name=need_count,json=needCount,proto3" json:"need_count,omitempty"`
	Ttid                 string        `protobuf:"bytes,4,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Status               Status        `protobuf:"varint,5,opt,name=status,proto3,enum=channel_play_tab.Status" json:"status,omitempty"`
	BanPostTypes         []BanPostType `protobuf:"varint,6,rep,packed,name=ban_post_types,json=banPostTypes,proto3,enum=channel_play_tab.BanPostType" json:"ban_post_types,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetBanUserConfigListReq) Reset()         { *m = GetBanUserConfigListReq{} }
func (m *GetBanUserConfigListReq) String() string { return proto.CompactTextString(m) }
func (*GetBanUserConfigListReq) ProtoMessage()    {}
func (*GetBanUserConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{71}
}
func (m *GetBanUserConfigListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBanUserConfigListReq.Unmarshal(m, b)
}
func (m *GetBanUserConfigListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBanUserConfigListReq.Marshal(b, m, deterministic)
}
func (dst *GetBanUserConfigListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBanUserConfigListReq.Merge(dst, src)
}
func (m *GetBanUserConfigListReq) XXX_Size() int {
	return xxx_messageInfo_GetBanUserConfigListReq.Size(m)
}
func (m *GetBanUserConfigListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBanUserConfigListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBanUserConfigListReq proto.InternalMessageInfo

func (m *GetBanUserConfigListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetBanUserConfigListReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetBanUserConfigListReq) GetNeedCount() bool {
	if m != nil {
		return m.NeedCount
	}
	return false
}

func (m *GetBanUserConfigListReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *GetBanUserConfigListReq) GetStatus() Status {
	if m != nil {
		return m.Status
	}
	return Status_StatusNone
}

func (m *GetBanUserConfigListReq) GetBanPostTypes() []BanPostType {
	if m != nil {
		return m.BanPostTypes
	}
	return nil
}

type GetBanUserConfigListResp struct {
	Configs              []*BanUserPostConfig `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	Total                uint32               `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetBanUserConfigListResp) Reset()         { *m = GetBanUserConfigListResp{} }
func (m *GetBanUserConfigListResp) String() string { return proto.CompactTextString(m) }
func (*GetBanUserConfigListResp) ProtoMessage()    {}
func (*GetBanUserConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{72}
}
func (m *GetBanUserConfigListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBanUserConfigListResp.Unmarshal(m, b)
}
func (m *GetBanUserConfigListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBanUserConfigListResp.Marshal(b, m, deterministic)
}
func (dst *GetBanUserConfigListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBanUserConfigListResp.Merge(dst, src)
}
func (m *GetBanUserConfigListResp) XXX_Size() int {
	return xxx_messageInfo_GetBanUserConfigListResp.Size(m)
}
func (m *GetBanUserConfigListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBanUserConfigListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBanUserConfigListResp proto.InternalMessageInfo

func (m *GetBanUserConfigListResp) GetConfigs() []*BanUserPostConfig {
	if m != nil {
		return m.Configs
	}
	return nil
}

func (m *GetBanUserConfigListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 删除禁止配置
type DelBanUserConfigReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBanUserConfigReq) Reset()         { *m = DelBanUserConfigReq{} }
func (m *DelBanUserConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelBanUserConfigReq) ProtoMessage()    {}
func (*DelBanUserConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{73}
}
func (m *DelBanUserConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBanUserConfigReq.Unmarshal(m, b)
}
func (m *DelBanUserConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBanUserConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelBanUserConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBanUserConfigReq.Merge(dst, src)
}
func (m *DelBanUserConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelBanUserConfigReq.Size(m)
}
func (m *DelBanUserConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBanUserConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelBanUserConfigReq proto.InternalMessageInfo

func (m *DelBanUserConfigReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DelBanUserConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBanUserConfigResp) Reset()         { *m = DelBanUserConfigResp{} }
func (m *DelBanUserConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelBanUserConfigResp) ProtoMessage()    {}
func (*DelBanUserConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{74}
}
func (m *DelBanUserConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBanUserConfigResp.Unmarshal(m, b)
}
func (m *DelBanUserConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBanUserConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelBanUserConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBanUserConfigResp.Merge(dst, src)
}
func (m *DelBanUserConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelBanUserConfigResp.Size(m)
}
func (m *DelBanUserConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBanUserConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelBanUserConfigResp proto.InternalMessageInfo

type ActiveBanUserConfig struct {
	ConfigType           BanConfigType `protobuf:"varint,1,opt,name=config_type,json=configType,proto3,enum=channel_play_tab.BanConfigType" json:"config_type,omitempty"`
	Uid                  uint32        `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	CrowdGroupId         string        `protobuf:"bytes,3,opt,name=crowd_group_id,json=crowdGroupId,proto3" json:"crowd_group_id,omitempty"`
	TabId                uint32        `protobuf:"varint,4,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BanReason            string        `protobuf:"bytes,5,opt,name=ban_reason,json=banReason,proto3" json:"ban_reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ActiveBanUserConfig) Reset()         { *m = ActiveBanUserConfig{} }
func (m *ActiveBanUserConfig) String() string { return proto.CompactTextString(m) }
func (*ActiveBanUserConfig) ProtoMessage()    {}
func (*ActiveBanUserConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{75}
}
func (m *ActiveBanUserConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActiveBanUserConfig.Unmarshal(m, b)
}
func (m *ActiveBanUserConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActiveBanUserConfig.Marshal(b, m, deterministic)
}
func (dst *ActiveBanUserConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActiveBanUserConfig.Merge(dst, src)
}
func (m *ActiveBanUserConfig) XXX_Size() int {
	return xxx_messageInfo_ActiveBanUserConfig.Size(m)
}
func (m *ActiveBanUserConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_ActiveBanUserConfig.DiscardUnknown(m)
}

var xxx_messageInfo_ActiveBanUserConfig proto.InternalMessageInfo

func (m *ActiveBanUserConfig) GetConfigType() BanConfigType {
	if m != nil {
		return m.ConfigType
	}
	return BanConfigType_BanConfigTypeNone
}

func (m *ActiveBanUserConfig) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ActiveBanUserConfig) GetCrowdGroupId() string {
	if m != nil {
		return m.CrowdGroupId
	}
	return ""
}

func (m *ActiveBanUserConfig) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ActiveBanUserConfig) GetBanReason() string {
	if m != nil {
		return m.BanReason
	}
	return ""
}

// 获取生效中的禁止用户配置
type GetActiveBanUserConfigWithCacheReq struct {
	TabId                uint32      `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Uid                  uint32      `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	BanPostType          BanPostType `protobuf:"varint,3,opt,name=ban_post_type,json=banPostType,proto3,enum=channel_play_tab.BanPostType" json:"ban_post_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetActiveBanUserConfigWithCacheReq) Reset()         { *m = GetActiveBanUserConfigWithCacheReq{} }
func (m *GetActiveBanUserConfigWithCacheReq) String() string { return proto.CompactTextString(m) }
func (*GetActiveBanUserConfigWithCacheReq) ProtoMessage()    {}
func (*GetActiveBanUserConfigWithCacheReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{76}
}
func (m *GetActiveBanUserConfigWithCacheReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActiveBanUserConfigWithCacheReq.Unmarshal(m, b)
}
func (m *GetActiveBanUserConfigWithCacheReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActiveBanUserConfigWithCacheReq.Marshal(b, m, deterministic)
}
func (dst *GetActiveBanUserConfigWithCacheReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActiveBanUserConfigWithCacheReq.Merge(dst, src)
}
func (m *GetActiveBanUserConfigWithCacheReq) XXX_Size() int {
	return xxx_messageInfo_GetActiveBanUserConfigWithCacheReq.Size(m)
}
func (m *GetActiveBanUserConfigWithCacheReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActiveBanUserConfigWithCacheReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetActiveBanUserConfigWithCacheReq proto.InternalMessageInfo

func (m *GetActiveBanUserConfigWithCacheReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetActiveBanUserConfigWithCacheReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetActiveBanUserConfigWithCacheReq) GetBanPostType() BanPostType {
	if m != nil {
		return m.BanPostType
	}
	return BanPostType_BanPostTypePost
}

type GetActiveBanUserConfigWithCacheResp struct {
	Configs              []*ActiveBanUserConfig `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetActiveBanUserConfigWithCacheResp) Reset()         { *m = GetActiveBanUserConfigWithCacheResp{} }
func (m *GetActiveBanUserConfigWithCacheResp) String() string { return proto.CompactTextString(m) }
func (*GetActiveBanUserConfigWithCacheResp) ProtoMessage()    {}
func (*GetActiveBanUserConfigWithCacheResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{77}
}
func (m *GetActiveBanUserConfigWithCacheResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActiveBanUserConfigWithCacheResp.Unmarshal(m, b)
}
func (m *GetActiveBanUserConfigWithCacheResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActiveBanUserConfigWithCacheResp.Marshal(b, m, deterministic)
}
func (dst *GetActiveBanUserConfigWithCacheResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActiveBanUserConfigWithCacheResp.Merge(dst, src)
}
func (m *GetActiveBanUserConfigWithCacheResp) XXX_Size() int {
	return xxx_messageInfo_GetActiveBanUserConfigWithCacheResp.Size(m)
}
func (m *GetActiveBanUserConfigWithCacheResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActiveBanUserConfigWithCacheResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetActiveBanUserConfigWithCacheResp proto.InternalMessageInfo

func (m *GetActiveBanUserConfigWithCacheResp) GetConfigs() []*ActiveBanUserConfig {
	if m != nil {
		return m.Configs
	}
	return nil
}

type GetBanGameHallUserReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBanGameHallUserReq) Reset()         { *m = GetBanGameHallUserReq{} }
func (m *GetBanGameHallUserReq) String() string { return proto.CompactTextString(m) }
func (*GetBanGameHallUserReq) ProtoMessage()    {}
func (*GetBanGameHallUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{78}
}
func (m *GetBanGameHallUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBanGameHallUserReq.Unmarshal(m, b)
}
func (m *GetBanGameHallUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBanGameHallUserReq.Marshal(b, m, deterministic)
}
func (dst *GetBanGameHallUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBanGameHallUserReq.Merge(dst, src)
}
func (m *GetBanGameHallUserReq) XXX_Size() int {
	return xxx_messageInfo_GetBanGameHallUserReq.Size(m)
}
func (m *GetBanGameHallUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBanGameHallUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBanGameHallUserReq proto.InternalMessageInfo

type GetBanGameHallUserResp struct {
	Configs              []*BanUserPostConfig `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetBanGameHallUserResp) Reset()         { *m = GetBanGameHallUserResp{} }
func (m *GetBanGameHallUserResp) String() string { return proto.CompactTextString(m) }
func (*GetBanGameHallUserResp) ProtoMessage()    {}
func (*GetBanGameHallUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{79}
}
func (m *GetBanGameHallUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBanGameHallUserResp.Unmarshal(m, b)
}
func (m *GetBanGameHallUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBanGameHallUserResp.Marshal(b, m, deterministic)
}
func (dst *GetBanGameHallUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBanGameHallUserResp.Merge(dst, src)
}
func (m *GetBanGameHallUserResp) XXX_Size() int {
	return xxx_messageInfo_GetBanGameHallUserResp.Size(m)
}
func (m *GetBanGameHallUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBanGameHallUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBanGameHallUserResp proto.InternalMessageInfo

func (m *GetBanGameHallUserResp) GetConfigs() []*BanUserPostConfig {
	if m != nil {
		return m.Configs
	}
	return nil
}

type UpdateBanGameHallGetStatusReq struct {
	Ids                  []string `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateBanGameHallGetStatusReq) Reset()         { *m = UpdateBanGameHallGetStatusReq{} }
func (m *UpdateBanGameHallGetStatusReq) String() string { return proto.CompactTextString(m) }
func (*UpdateBanGameHallGetStatusReq) ProtoMessage()    {}
func (*UpdateBanGameHallGetStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{80}
}
func (m *UpdateBanGameHallGetStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBanGameHallGetStatusReq.Unmarshal(m, b)
}
func (m *UpdateBanGameHallGetStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBanGameHallGetStatusReq.Marshal(b, m, deterministic)
}
func (dst *UpdateBanGameHallGetStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBanGameHallGetStatusReq.Merge(dst, src)
}
func (m *UpdateBanGameHallGetStatusReq) XXX_Size() int {
	return xxx_messageInfo_UpdateBanGameHallGetStatusReq.Size(m)
}
func (m *UpdateBanGameHallGetStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBanGameHallGetStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBanGameHallGetStatusReq proto.InternalMessageInfo

func (m *UpdateBanGameHallGetStatusReq) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

type UpdateBanGameHallGetStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateBanGameHallGetStatusResp) Reset()         { *m = UpdateBanGameHallGetStatusResp{} }
func (m *UpdateBanGameHallGetStatusResp) String() string { return proto.CompactTextString(m) }
func (*UpdateBanGameHallGetStatusResp) ProtoMessage()    {}
func (*UpdateBanGameHallGetStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_8ca61c1d87a331a2, []int{81}
}
func (m *UpdateBanGameHallGetStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBanGameHallGetStatusResp.Unmarshal(m, b)
}
func (m *UpdateBanGameHallGetStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBanGameHallGetStatusResp.Marshal(b, m, deterministic)
}
func (dst *UpdateBanGameHallGetStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBanGameHallGetStatusResp.Merge(dst, src)
}
func (m *UpdateBanGameHallGetStatusResp) XXX_Size() int {
	return xxx_messageInfo_UpdateBanGameHallGetStatusResp.Size(m)
}
func (m *UpdateBanGameHallGetStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBanGameHallGetStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBanGameHallGetStatusResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*BatchGetWhiteUidListByTabIdsReq)(nil), "channel_play_tab.BatchGetWhiteUidListByTabIdsReq")
	proto.RegisterType((*BatchGetWhiteUidListByTabIdsResp)(nil), "channel_play_tab.BatchGetWhiteUidListByTabIdsResp")
	proto.RegisterMapType((map[uint32]*BatchGetWhiteUidListByTabIdsResp_WhiteUidList)(nil), "channel_play_tab.BatchGetWhiteUidListByTabIdsResp.TabListMapEntry")
	proto.RegisterType((*BatchGetWhiteUidListByTabIdsResp_WhiteUidList)(nil), "channel_play_tab.BatchGetWhiteUidListByTabIdsResp.WhiteUidList")
	proto.RegisterType((*SetWhiteUidListByTabIdReq)(nil), "channel_play_tab.SetWhiteUidListByTabIdReq")
	proto.RegisterType((*SetWhiteUidListByTabIdResp)(nil), "channel_play_tab.SetWhiteUidListByTabIdResp")
	proto.RegisterType((*Tab)(nil), "channel_play_tab.Tab")
	proto.RegisterType((*UpsertTabReq)(nil), "channel_play_tab.UpsertTabReq")
	proto.RegisterType((*UpsertTabResp)(nil), "channel_play_tab.UpsertTabResp")
	proto.RegisterType((*GetTabsByTabSubTypeReq)(nil), "channel_play_tab.GetTabsByTabSubTypeReq")
	proto.RegisterType((*GetTabsByTabSubTypeResp)(nil), "channel_play_tab.GetTabsByTabSubTypeResp")
	proto.RegisterType((*DeleteTabReq)(nil), "channel_play_tab.DeleteTabReq")
	proto.RegisterType((*DeleteTabResp)(nil), "channel_play_tab.DeleteTabResp")
	proto.RegisterType((*GetTabsReq)(nil), "channel_play_tab.GetTabsReq")
	proto.RegisterType((*GetTabsResp)(nil), "channel_play_tab.GetTabsResp")
	proto.RegisterType((*MinorSupervisionConfig)(nil), "channel_play_tab.MinorSupervisionConfig")
	proto.RegisterType((*UpsertMinorSupervisionConfigReq)(nil), "channel_play_tab.UpsertMinorSupervisionConfigReq")
	proto.RegisterType((*UpsertMinorSupervisionConfigResp)(nil), "channel_play_tab.UpsertMinorSupervisionConfigResp")
	proto.RegisterType((*BatchGetMinorSupervisionConfigReq)(nil), "channel_play_tab.BatchGetMinorSupervisionConfigReq")
	proto.RegisterType((*BatchGetMinorSupervisionConfigResp)(nil), "channel_play_tab.BatchGetMinorSupervisionConfigResp")
	proto.RegisterMapType((map[uint32]*MinorSupervisionConfig)(nil), "channel_play_tab.BatchGetMinorSupervisionConfigResp.CategoryConfigsEntry")
	proto.RegisterMapType((map[uint32]*MinorSupervisionConfig)(nil), "channel_play_tab.BatchGetMinorSupervisionConfigResp.TabConfigsEntry")
	proto.RegisterType((*GetTabsRealNameConfigsReq)(nil), "channel_play_tab.GetTabsRealNameConfigsReq")
	proto.RegisterType((*TabsRealNameConfig)(nil), "channel_play_tab.TabsRealNameConfig")
	proto.RegisterType((*GetTabsRealNameConfigsResp)(nil), "channel_play_tab.GetTabsRealNameConfigsResp")
	proto.RegisterType((*UpdateTabsRealNameConfigReq)(nil), "channel_play_tab.UpdateTabsRealNameConfigReq")
	proto.RegisterType((*UpdateTabsRealNameConfigResp)(nil), "channel_play_tab.UpdateTabsRealNameConfigResp")
	proto.RegisterType((*AddTabsRealNameConfigReq)(nil), "channel_play_tab.AddTabsRealNameConfigReq")
	proto.RegisterType((*AddTabsRealNameConfigResp)(nil), "channel_play_tab.AddTabsRealNameConfigResp")
	proto.RegisterType((*DeleteTabsRealNameConfigReq)(nil), "channel_play_tab.DeleteTabsRealNameConfigReq")
	proto.RegisterType((*DeleteTabsRealNameConfigResp)(nil), "channel_play_tab.DeleteTabsRealNameConfigResp")
	proto.RegisterType((*GetUserTabsRealNameConfigReq)(nil), "channel_play_tab.GetUserTabsRealNameConfigReq")
	proto.RegisterType((*GetUserTabsRealNameConfigResp)(nil), "channel_play_tab.GetUserTabsRealNameConfigResp")
	proto.RegisterType((*SceneInfo)(nil), "channel_play_tab.SceneInfo")
	proto.RegisterType((*HotMiniGameExtraInfo)(nil), "channel_play_tab.HotMiniGameExtraInfo")
	proto.RegisterType((*GetQuickMatchConfigReq)(nil), "channel_play_tab.GetQuickMatchConfigReq")
	proto.RegisterType((*GetQuickMatchConfigResp)(nil), "channel_play_tab.GetQuickMatchConfigResp")
	proto.RegisterType((*UpdateQuickMatchConfigReq)(nil), "channel_play_tab.UpdateQuickMatchConfigReq")
	proto.RegisterType((*UpdateQuickMatchConfigResp)(nil), "channel_play_tab.UpdateQuickMatchConfigResp")
	proto.RegisterType((*DeleteQuickMatchConfigReq)(nil), "channel_play_tab.DeleteQuickMatchConfigReq")
	proto.RegisterType((*DeleteQuickMatchConfigResp)(nil), "channel_play_tab.DeleteQuickMatchConfigResp")
	proto.RegisterType((*ResortQuickMatchConfigReq)(nil), "channel_play_tab.ResortQuickMatchConfigReq")
	proto.RegisterType((*ResortQuickMatchConfigResp)(nil), "channel_play_tab.ResortQuickMatchConfigResp")
	proto.RegisterType((*NewQuickMatchConfig)(nil), "channel_play_tab.NewQuickMatchConfig")
	proto.RegisterType((*UpsertNewQuickMatchConfigReq)(nil), "channel_play_tab.UpsertNewQuickMatchConfigReq")
	proto.RegisterType((*UpsertNewQuickMatchConfigResp)(nil), "channel_play_tab.UpsertNewQuickMatchConfigResp")
	proto.RegisterType((*BatchGetNewQuickMatchConfigReq)(nil), "channel_play_tab.BatchGetNewQuickMatchConfigReq")
	proto.RegisterType((*BatchGetNewQuickMatchConfigResp)(nil), "channel_play_tab.BatchGetNewQuickMatchConfigResp")
	proto.RegisterType((*DelNewQuickMatchConfigReq)(nil), "channel_play_tab.DelNewQuickMatchConfigReq")
	proto.RegisterType((*DelNewQuickMatchConfigResp)(nil), "channel_play_tab.DelNewQuickMatchConfigResp")
	proto.RegisterType((*GetNewQuickMatchConfigReq)(nil), "channel_play_tab.GetNewQuickMatchConfigReq")
	proto.RegisterType((*GetNewQuickMatchConfigResp)(nil), "channel_play_tab.GetNewQuickMatchConfigResp")
	proto.RegisterType((*GetFastPCCategoryConfigReq)(nil), "channel_play_tab.GetFastPCCategoryConfigReq")
	proto.RegisterType((*FastPCCategoryConfig)(nil), "channel_play_tab.FastPCCategoryConfig")
	proto.RegisterType((*GetFastPCCategoryConfigResp)(nil), "channel_play_tab.GetFastPCCategoryConfigResp")
	proto.RegisterType((*UpsertFastPCCategoryConfigReq)(nil), "channel_play_tab.UpsertFastPCCategoryConfigReq")
	proto.RegisterType((*UpsertFastPCCategoryConfigResp)(nil), "channel_play_tab.UpsertFastPCCategoryConfigResp")
	proto.RegisterType((*DelFastPCCategoryConfigReq)(nil), "channel_play_tab.DelFastPCCategoryConfigReq")
	proto.RegisterType((*DelFastPCCategoryConfigResp)(nil), "channel_play_tab.DelFastPCCategoryConfigResp")
	proto.RegisterType((*SortFastPCCategoryConfigReq)(nil), "channel_play_tab.SortFastPCCategoryConfigReq")
	proto.RegisterType((*SortFastPCCategoryConfigResp)(nil), "channel_play_tab.SortFastPCCategoryConfigResp")
	proto.RegisterType((*GetAllTabInfoExtReq)(nil), "channel_play_tab.GetAllTabInfoExtReq")
	proto.RegisterType((*TabInfoExtItem)(nil), "channel_play_tab.TabInfoExtItem")
	proto.RegisterType((*TabInfoExtFastPcConfig)(nil), "channel_play_tab.TabInfoExtFastPcConfig")
	proto.RegisterType((*GetAllTabInfoExtResp)(nil), "channel_play_tab.GetAllTabInfoExtResp")
	proto.RegisterType((*UpsertTabInfoExtReq)(nil), "channel_play_tab.UpsertTabInfoExtReq")
	proto.RegisterType((*UpsertTabInfoExtResp)(nil), "channel_play_tab.UpsertTabInfoExtResp")
	proto.RegisterType((*DelTabInfoExtReq)(nil), "channel_play_tab.DelTabInfoExtReq")
	proto.RegisterType((*DelTabInfoExtResp)(nil), "channel_play_tab.DelTabInfoExtResp")
	proto.RegisterType((*MultiUserInfo)(nil), "channel_play_tab.MultiUserInfo")
	proto.RegisterType((*BanUserPostConfig)(nil), "channel_play_tab.BanUserPostConfig")
	proto.RegisterMapType((map[string]*MultiUserInfo)(nil), "channel_play_tab.BanUserPostConfig.UserInfoMapEntry")
	proto.RegisterType((*BatchAddBanUserConfigReq)(nil), "channel_play_tab.BatchAddBanUserConfigReq")
	proto.RegisterType((*BatchAddBanUserConfigResp)(nil), "channel_play_tab.BatchAddBanUserConfigResp")
	proto.RegisterType((*UpsertBanUserConfigReq)(nil), "channel_play_tab.UpsertBanUserConfigReq")
	proto.RegisterType((*UpsertBanUserConfigResp)(nil), "channel_play_tab.UpsertBanUserConfigResp")
	proto.RegisterType((*GetBanUserConfigListReq)(nil), "channel_play_tab.GetBanUserConfigListReq")
	proto.RegisterType((*GetBanUserConfigListResp)(nil), "channel_play_tab.GetBanUserConfigListResp")
	proto.RegisterType((*DelBanUserConfigReq)(nil), "channel_play_tab.DelBanUserConfigReq")
	proto.RegisterType((*DelBanUserConfigResp)(nil), "channel_play_tab.DelBanUserConfigResp")
	proto.RegisterType((*ActiveBanUserConfig)(nil), "channel_play_tab.ActiveBanUserConfig")
	proto.RegisterType((*GetActiveBanUserConfigWithCacheReq)(nil), "channel_play_tab.GetActiveBanUserConfigWithCacheReq")
	proto.RegisterType((*GetActiveBanUserConfigWithCacheResp)(nil), "channel_play_tab.GetActiveBanUserConfigWithCacheResp")
	proto.RegisterType((*GetBanGameHallUserReq)(nil), "channel_play_tab.GetBanGameHallUserReq")
	proto.RegisterType((*GetBanGameHallUserResp)(nil), "channel_play_tab.GetBanGameHallUserResp")
	proto.RegisterType((*UpdateBanGameHallGetStatusReq)(nil), "channel_play_tab.UpdateBanGameHallGetStatusReq")
	proto.RegisterType((*UpdateBanGameHallGetStatusResp)(nil), "channel_play_tab.UpdateBanGameHallGetStatusResp")
	proto.RegisterEnum("channel_play_tab.TabSubType", TabSubType_name, TabSubType_value)
	proto.RegisterEnum("channel_play_tab.MinorSupervisionScene", MinorSupervisionScene_name, MinorSupervisionScene_value)
	proto.RegisterEnum("channel_play_tab.MinorSupervisionConfigType", MinorSupervisionConfigType_name, MinorSupervisionConfigType_value)
	proto.RegisterEnum("channel_play_tab.QuickMatchConfigType", QuickMatchConfigType_name, QuickMatchConfigType_value)
	proto.RegisterEnum("channel_play_tab.FastPCCategoryType", FastPCCategoryType_name, FastPCCategoryType_value)
	proto.RegisterEnum("channel_play_tab.TabInfoExtEnum", TabInfoExtEnum_name, TabInfoExtEnum_value)
	proto.RegisterEnum("channel_play_tab.BanConfigType", BanConfigType_name, BanConfigType_value)
	proto.RegisterEnum("channel_play_tab.Status", Status_name, Status_value)
	proto.RegisterEnum("channel_play_tab.BanPostType", BanPostType_name, BanPostType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelPlayTabClient is the client API for ChannelPlayTab service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelPlayTabClient interface {
	// 批量获取玩法可见用户白名单
	BatchGetWhiteUidListByTabIds(ctx context.Context, in *BatchGetWhiteUidListByTabIdsReq, opts ...grpc.CallOption) (*BatchGetWhiteUidListByTabIdsResp, error)
	// 保存玩法白名单,仅运营后台用
	SetWhiteUidListByTabId(ctx context.Context, in *SetWhiteUidListByTabIdReq, opts ...grpc.CallOption) (*SetWhiteUidListByTabIdResp, error)
	// 获取指定类型的tab
	GetTabsByTabSubType(ctx context.Context, in *GetTabsByTabSubTypeReq, opts ...grpc.CallOption) (*GetTabsByTabSubTypeResp, error)
	// 插入/更新tab,仅运营后台用
	UpsertTab(ctx context.Context, in *UpsertTabReq, opts ...grpc.CallOption) (*UpsertTabResp, error)
	// 删除tab,仅运营后台用
	DeleteTab(ctx context.Context, in *DeleteTabReq, opts ...grpc.CallOption) (*DeleteTabResp, error)
	// 获取所有tab,读缓存
	GetTabs(ctx context.Context, in *GetTabsReq, opts ...grpc.CallOption) (*GetTabsResp, error)
	// 插入/更新玩法或者分类的未成年监管控制开关
	UpsertMinorSupervisionConfig(ctx context.Context, in *UpsertMinorSupervisionConfigReq, opts ...grpc.CallOption) (*UpsertMinorSupervisionConfigResp, error)
	// 批量获取玩法或者分类的未成年监管控制开关
	BatchGetMinorSupervisionConfig(ctx context.Context, in *BatchGetMinorSupervisionConfigReq, opts ...grpc.CallOption) (*BatchGetMinorSupervisionConfigResp, error)
	// （未成年）玩法开关协议配置 运营后台
	GetTabsRealNameConfigs(ctx context.Context, in *GetTabsRealNameConfigsReq, opts ...grpc.CallOption) (*GetTabsRealNameConfigsResp, error)
	UpdateTabsRealNameConfig(ctx context.Context, in *UpdateTabsRealNameConfigReq, opts ...grpc.CallOption) (*UpdateTabsRealNameConfigResp, error)
	AddTabsRealNameConfig(ctx context.Context, in *AddTabsRealNameConfigReq, opts ...grpc.CallOption) (*AddTabsRealNameConfigResp, error)
	DeleteTabsRealNameConfig(ctx context.Context, in *DeleteTabsRealNameConfigReq, opts ...grpc.CallOption) (*DeleteTabsRealNameConfigResp, error)
	// 用户入口筛选tab开关展示
	GetUserTabsRealNameConfig(ctx context.Context, in *GetUserTabsRealNameConfigReq, opts ...grpc.CallOption) (*GetUserTabsRealNameConfigResp, error)
	// 快速匹配相关新接口 旧的首页玩法卡GetHomePagePlayCards相关和GetDetailTabInfoOfScene，GetSceneTabsForTT都不用了，后面统一使用新的
	GetQuickMatchConfig(ctx context.Context, in *GetQuickMatchConfigReq, opts ...grpc.CallOption) (*GetQuickMatchConfigResp, error)
	UpdateQuickMatchConfig(ctx context.Context, in *UpdateQuickMatchConfigReq, opts ...grpc.CallOption) (*UpdateQuickMatchConfigResp, error)
	DeleteQuickMatchConfig(ctx context.Context, in *DeleteQuickMatchConfigReq, opts ...grpc.CallOption) (*DeleteQuickMatchConfigResp, error)
	ResortQuickMatchConfig(ctx context.Context, in *ResortQuickMatchConfigReq, opts ...grpc.CallOption) (*ResortQuickMatchConfigResp, error)
	// 新版首页快速匹配入口配置 运营后台
	UpsertNewQuickMatchConfig(ctx context.Context, in *UpsertNewQuickMatchConfigReq, opts ...grpc.CallOption) (*UpsertNewQuickMatchConfigResp, error)
	BatchGetNewQuickMatchConfig(ctx context.Context, in *BatchGetNewQuickMatchConfigReq, opts ...grpc.CallOption) (*BatchGetNewQuickMatchConfigResp, error)
	DelNewQuickMatchConfig(ctx context.Context, in *DelNewQuickMatchConfigReq, opts ...grpc.CallOption) (*DelNewQuickMatchConfigResp, error)
	// 客户端获取配置
	GetNewQuickMatchConfig(ctx context.Context, in *GetNewQuickMatchConfigReq, opts ...grpc.CallOption) (*GetNewQuickMatchConfigResp, error)
	// 极速版PC分类运营后台接口
	GetFastPCCategoryConfig(ctx context.Context, in *GetFastPCCategoryConfigReq, opts ...grpc.CallOption) (*GetFastPCCategoryConfigResp, error)
	UpsertFastPCCategoryConfig(ctx context.Context, in *UpsertFastPCCategoryConfigReq, opts ...grpc.CallOption) (*UpsertFastPCCategoryConfigResp, error)
	DelFastPCCategoryConfig(ctx context.Context, in *DelFastPCCategoryConfigReq, opts ...grpc.CallOption) (*DelFastPCCategoryConfigResp, error)
	SortFastPCCategoryConfig(ctx context.Context, in *SortFastPCCategoryConfigReq, opts ...grpc.CallOption) (*SortFastPCCategoryConfigResp, error)
	// 全局玩法数据扩展
	GetAllTabInfoExt(ctx context.Context, in *GetAllTabInfoExtReq, opts ...grpc.CallOption) (*GetAllTabInfoExtResp, error)
	UpsertTabInfoExt(ctx context.Context, in *UpsertTabInfoExtReq, opts ...grpc.CallOption) (*UpsertTabInfoExtResp, error)
	DelTabInfoExt(ctx context.Context, in *DelTabInfoExtReq, opts ...grpc.CallOption) (*DelTabInfoExtResp, error)
	// 用户禁止配置 运营后台
	UpsertBanUserConfig(ctx context.Context, in *UpsertBanUserConfigReq, opts ...grpc.CallOption) (*UpsertBanUserConfigResp, error)
	GetBanUserConfigList(ctx context.Context, in *GetBanUserConfigListReq, opts ...grpc.CallOption) (*GetBanUserConfigListResp, error)
	DelBanUserConfig(ctx context.Context, in *DelBanUserConfigReq, opts ...grpc.CallOption) (*DelBanUserConfigResp, error)
	BatchAddBanUserConfig(ctx context.Context, in *BatchAddBanUserConfigReq, opts ...grpc.CallOption) (*BatchAddBanUserConfigResp, error)
	// 客户端获取配置
	GetActiveBanUserConfigWithCache(ctx context.Context, in *GetActiveBanUserConfigWithCacheReq, opts ...grpc.CallOption) (*GetActiveBanUserConfigWithCacheResp, error)
	GetBanGameHallUser(ctx context.Context, in *GetBanGameHallUserReq, opts ...grpc.CallOption) (*GetBanGameHallUserResp, error)
	UpdateBanGameHallGetStatus(ctx context.Context, in *UpdateBanGameHallGetStatusReq, opts ...grpc.CallOption) (*UpdateBanGameHallGetStatusResp, error)
}

type channelPlayTabClient struct {
	cc *grpc.ClientConn
}

func NewChannelPlayTabClient(cc *grpc.ClientConn) ChannelPlayTabClient {
	return &channelPlayTabClient{cc}
}

func (c *channelPlayTabClient) BatchGetWhiteUidListByTabIds(ctx context.Context, in *BatchGetWhiteUidListByTabIdsReq, opts ...grpc.CallOption) (*BatchGetWhiteUidListByTabIdsResp, error) {
	out := new(BatchGetWhiteUidListByTabIdsResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/BatchGetWhiteUidListByTabIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) SetWhiteUidListByTabId(ctx context.Context, in *SetWhiteUidListByTabIdReq, opts ...grpc.CallOption) (*SetWhiteUidListByTabIdResp, error) {
	out := new(SetWhiteUidListByTabIdResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/SetWhiteUidListByTabId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) GetTabsByTabSubType(ctx context.Context, in *GetTabsByTabSubTypeReq, opts ...grpc.CallOption) (*GetTabsByTabSubTypeResp, error) {
	out := new(GetTabsByTabSubTypeResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/GetTabsByTabSubType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) UpsertTab(ctx context.Context, in *UpsertTabReq, opts ...grpc.CallOption) (*UpsertTabResp, error) {
	out := new(UpsertTabResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/UpsertTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) DeleteTab(ctx context.Context, in *DeleteTabReq, opts ...grpc.CallOption) (*DeleteTabResp, error) {
	out := new(DeleteTabResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/DeleteTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) GetTabs(ctx context.Context, in *GetTabsReq, opts ...grpc.CallOption) (*GetTabsResp, error) {
	out := new(GetTabsResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/GetTabs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) UpsertMinorSupervisionConfig(ctx context.Context, in *UpsertMinorSupervisionConfigReq, opts ...grpc.CallOption) (*UpsertMinorSupervisionConfigResp, error) {
	out := new(UpsertMinorSupervisionConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/UpsertMinorSupervisionConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) BatchGetMinorSupervisionConfig(ctx context.Context, in *BatchGetMinorSupervisionConfigReq, opts ...grpc.CallOption) (*BatchGetMinorSupervisionConfigResp, error) {
	out := new(BatchGetMinorSupervisionConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/BatchGetMinorSupervisionConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) GetTabsRealNameConfigs(ctx context.Context, in *GetTabsRealNameConfigsReq, opts ...grpc.CallOption) (*GetTabsRealNameConfigsResp, error) {
	out := new(GetTabsRealNameConfigsResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/GetTabsRealNameConfigs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) UpdateTabsRealNameConfig(ctx context.Context, in *UpdateTabsRealNameConfigReq, opts ...grpc.CallOption) (*UpdateTabsRealNameConfigResp, error) {
	out := new(UpdateTabsRealNameConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/UpdateTabsRealNameConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) AddTabsRealNameConfig(ctx context.Context, in *AddTabsRealNameConfigReq, opts ...grpc.CallOption) (*AddTabsRealNameConfigResp, error) {
	out := new(AddTabsRealNameConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/AddTabsRealNameConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) DeleteTabsRealNameConfig(ctx context.Context, in *DeleteTabsRealNameConfigReq, opts ...grpc.CallOption) (*DeleteTabsRealNameConfigResp, error) {
	out := new(DeleteTabsRealNameConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/DeleteTabsRealNameConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) GetUserTabsRealNameConfig(ctx context.Context, in *GetUserTabsRealNameConfigReq, opts ...grpc.CallOption) (*GetUserTabsRealNameConfigResp, error) {
	out := new(GetUserTabsRealNameConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/GetUserTabsRealNameConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) GetQuickMatchConfig(ctx context.Context, in *GetQuickMatchConfigReq, opts ...grpc.CallOption) (*GetQuickMatchConfigResp, error) {
	out := new(GetQuickMatchConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/GetQuickMatchConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) UpdateQuickMatchConfig(ctx context.Context, in *UpdateQuickMatchConfigReq, opts ...grpc.CallOption) (*UpdateQuickMatchConfigResp, error) {
	out := new(UpdateQuickMatchConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/UpdateQuickMatchConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) DeleteQuickMatchConfig(ctx context.Context, in *DeleteQuickMatchConfigReq, opts ...grpc.CallOption) (*DeleteQuickMatchConfigResp, error) {
	out := new(DeleteQuickMatchConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/DeleteQuickMatchConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) ResortQuickMatchConfig(ctx context.Context, in *ResortQuickMatchConfigReq, opts ...grpc.CallOption) (*ResortQuickMatchConfigResp, error) {
	out := new(ResortQuickMatchConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/ResortQuickMatchConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) UpsertNewQuickMatchConfig(ctx context.Context, in *UpsertNewQuickMatchConfigReq, opts ...grpc.CallOption) (*UpsertNewQuickMatchConfigResp, error) {
	out := new(UpsertNewQuickMatchConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/UpsertNewQuickMatchConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) BatchGetNewQuickMatchConfig(ctx context.Context, in *BatchGetNewQuickMatchConfigReq, opts ...grpc.CallOption) (*BatchGetNewQuickMatchConfigResp, error) {
	out := new(BatchGetNewQuickMatchConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/BatchGetNewQuickMatchConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) DelNewQuickMatchConfig(ctx context.Context, in *DelNewQuickMatchConfigReq, opts ...grpc.CallOption) (*DelNewQuickMatchConfigResp, error) {
	out := new(DelNewQuickMatchConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/DelNewQuickMatchConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) GetNewQuickMatchConfig(ctx context.Context, in *GetNewQuickMatchConfigReq, opts ...grpc.CallOption) (*GetNewQuickMatchConfigResp, error) {
	out := new(GetNewQuickMatchConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/GetNewQuickMatchConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) GetFastPCCategoryConfig(ctx context.Context, in *GetFastPCCategoryConfigReq, opts ...grpc.CallOption) (*GetFastPCCategoryConfigResp, error) {
	out := new(GetFastPCCategoryConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/GetFastPCCategoryConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) UpsertFastPCCategoryConfig(ctx context.Context, in *UpsertFastPCCategoryConfigReq, opts ...grpc.CallOption) (*UpsertFastPCCategoryConfigResp, error) {
	out := new(UpsertFastPCCategoryConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/UpsertFastPCCategoryConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) DelFastPCCategoryConfig(ctx context.Context, in *DelFastPCCategoryConfigReq, opts ...grpc.CallOption) (*DelFastPCCategoryConfigResp, error) {
	out := new(DelFastPCCategoryConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/DelFastPCCategoryConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) SortFastPCCategoryConfig(ctx context.Context, in *SortFastPCCategoryConfigReq, opts ...grpc.CallOption) (*SortFastPCCategoryConfigResp, error) {
	out := new(SortFastPCCategoryConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/SortFastPCCategoryConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) GetAllTabInfoExt(ctx context.Context, in *GetAllTabInfoExtReq, opts ...grpc.CallOption) (*GetAllTabInfoExtResp, error) {
	out := new(GetAllTabInfoExtResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/GetAllTabInfoExt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) UpsertTabInfoExt(ctx context.Context, in *UpsertTabInfoExtReq, opts ...grpc.CallOption) (*UpsertTabInfoExtResp, error) {
	out := new(UpsertTabInfoExtResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/UpsertTabInfoExt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) DelTabInfoExt(ctx context.Context, in *DelTabInfoExtReq, opts ...grpc.CallOption) (*DelTabInfoExtResp, error) {
	out := new(DelTabInfoExtResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/DelTabInfoExt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) UpsertBanUserConfig(ctx context.Context, in *UpsertBanUserConfigReq, opts ...grpc.CallOption) (*UpsertBanUserConfigResp, error) {
	out := new(UpsertBanUserConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/UpsertBanUserConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) GetBanUserConfigList(ctx context.Context, in *GetBanUserConfigListReq, opts ...grpc.CallOption) (*GetBanUserConfigListResp, error) {
	out := new(GetBanUserConfigListResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/GetBanUserConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) DelBanUserConfig(ctx context.Context, in *DelBanUserConfigReq, opts ...grpc.CallOption) (*DelBanUserConfigResp, error) {
	out := new(DelBanUserConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/DelBanUserConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) BatchAddBanUserConfig(ctx context.Context, in *BatchAddBanUserConfigReq, opts ...grpc.CallOption) (*BatchAddBanUserConfigResp, error) {
	out := new(BatchAddBanUserConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/BatchAddBanUserConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) GetActiveBanUserConfigWithCache(ctx context.Context, in *GetActiveBanUserConfigWithCacheReq, opts ...grpc.CallOption) (*GetActiveBanUserConfigWithCacheResp, error) {
	out := new(GetActiveBanUserConfigWithCacheResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/GetActiveBanUserConfigWithCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) GetBanGameHallUser(ctx context.Context, in *GetBanGameHallUserReq, opts ...grpc.CallOption) (*GetBanGameHallUserResp, error) {
	out := new(GetBanGameHallUserResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/GetBanGameHallUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) UpdateBanGameHallGetStatus(ctx context.Context, in *UpdateBanGameHallGetStatusReq, opts ...grpc.CallOption) (*UpdateBanGameHallGetStatusResp, error) {
	out := new(UpdateBanGameHallGetStatusResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/UpdateBanGameHallGetStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelPlayTabServer is the server API for ChannelPlayTab service.
type ChannelPlayTabServer interface {
	// 批量获取玩法可见用户白名单
	BatchGetWhiteUidListByTabIds(context.Context, *BatchGetWhiteUidListByTabIdsReq) (*BatchGetWhiteUidListByTabIdsResp, error)
	// 保存玩法白名单,仅运营后台用
	SetWhiteUidListByTabId(context.Context, *SetWhiteUidListByTabIdReq) (*SetWhiteUidListByTabIdResp, error)
	// 获取指定类型的tab
	GetTabsByTabSubType(context.Context, *GetTabsByTabSubTypeReq) (*GetTabsByTabSubTypeResp, error)
	// 插入/更新tab,仅运营后台用
	UpsertTab(context.Context, *UpsertTabReq) (*UpsertTabResp, error)
	// 删除tab,仅运营后台用
	DeleteTab(context.Context, *DeleteTabReq) (*DeleteTabResp, error)
	// 获取所有tab,读缓存
	GetTabs(context.Context, *GetTabsReq) (*GetTabsResp, error)
	// 插入/更新玩法或者分类的未成年监管控制开关
	UpsertMinorSupervisionConfig(context.Context, *UpsertMinorSupervisionConfigReq) (*UpsertMinorSupervisionConfigResp, error)
	// 批量获取玩法或者分类的未成年监管控制开关
	BatchGetMinorSupervisionConfig(context.Context, *BatchGetMinorSupervisionConfigReq) (*BatchGetMinorSupervisionConfigResp, error)
	// （未成年）玩法开关协议配置 运营后台
	GetTabsRealNameConfigs(context.Context, *GetTabsRealNameConfigsReq) (*GetTabsRealNameConfigsResp, error)
	UpdateTabsRealNameConfig(context.Context, *UpdateTabsRealNameConfigReq) (*UpdateTabsRealNameConfigResp, error)
	AddTabsRealNameConfig(context.Context, *AddTabsRealNameConfigReq) (*AddTabsRealNameConfigResp, error)
	DeleteTabsRealNameConfig(context.Context, *DeleteTabsRealNameConfigReq) (*DeleteTabsRealNameConfigResp, error)
	// 用户入口筛选tab开关展示
	GetUserTabsRealNameConfig(context.Context, *GetUserTabsRealNameConfigReq) (*GetUserTabsRealNameConfigResp, error)
	// 快速匹配相关新接口 旧的首页玩法卡GetHomePagePlayCards相关和GetDetailTabInfoOfScene，GetSceneTabsForTT都不用了，后面统一使用新的
	GetQuickMatchConfig(context.Context, *GetQuickMatchConfigReq) (*GetQuickMatchConfigResp, error)
	UpdateQuickMatchConfig(context.Context, *UpdateQuickMatchConfigReq) (*UpdateQuickMatchConfigResp, error)
	DeleteQuickMatchConfig(context.Context, *DeleteQuickMatchConfigReq) (*DeleteQuickMatchConfigResp, error)
	ResortQuickMatchConfig(context.Context, *ResortQuickMatchConfigReq) (*ResortQuickMatchConfigResp, error)
	// 新版首页快速匹配入口配置 运营后台
	UpsertNewQuickMatchConfig(context.Context, *UpsertNewQuickMatchConfigReq) (*UpsertNewQuickMatchConfigResp, error)
	BatchGetNewQuickMatchConfig(context.Context, *BatchGetNewQuickMatchConfigReq) (*BatchGetNewQuickMatchConfigResp, error)
	DelNewQuickMatchConfig(context.Context, *DelNewQuickMatchConfigReq) (*DelNewQuickMatchConfigResp, error)
	// 客户端获取配置
	GetNewQuickMatchConfig(context.Context, *GetNewQuickMatchConfigReq) (*GetNewQuickMatchConfigResp, error)
	// 极速版PC分类运营后台接口
	GetFastPCCategoryConfig(context.Context, *GetFastPCCategoryConfigReq) (*GetFastPCCategoryConfigResp, error)
	UpsertFastPCCategoryConfig(context.Context, *UpsertFastPCCategoryConfigReq) (*UpsertFastPCCategoryConfigResp, error)
	DelFastPCCategoryConfig(context.Context, *DelFastPCCategoryConfigReq) (*DelFastPCCategoryConfigResp, error)
	SortFastPCCategoryConfig(context.Context, *SortFastPCCategoryConfigReq) (*SortFastPCCategoryConfigResp, error)
	// 全局玩法数据扩展
	GetAllTabInfoExt(context.Context, *GetAllTabInfoExtReq) (*GetAllTabInfoExtResp, error)
	UpsertTabInfoExt(context.Context, *UpsertTabInfoExtReq) (*UpsertTabInfoExtResp, error)
	DelTabInfoExt(context.Context, *DelTabInfoExtReq) (*DelTabInfoExtResp, error)
	// 用户禁止配置 运营后台
	UpsertBanUserConfig(context.Context, *UpsertBanUserConfigReq) (*UpsertBanUserConfigResp, error)
	GetBanUserConfigList(context.Context, *GetBanUserConfigListReq) (*GetBanUserConfigListResp, error)
	DelBanUserConfig(context.Context, *DelBanUserConfigReq) (*DelBanUserConfigResp, error)
	BatchAddBanUserConfig(context.Context, *BatchAddBanUserConfigReq) (*BatchAddBanUserConfigResp, error)
	// 客户端获取配置
	GetActiveBanUserConfigWithCache(context.Context, *GetActiveBanUserConfigWithCacheReq) (*GetActiveBanUserConfigWithCacheResp, error)
	GetBanGameHallUser(context.Context, *GetBanGameHallUserReq) (*GetBanGameHallUserResp, error)
	UpdateBanGameHallGetStatus(context.Context, *UpdateBanGameHallGetStatusReq) (*UpdateBanGameHallGetStatusResp, error)
}

func RegisterChannelPlayTabServer(s *grpc.Server, srv ChannelPlayTabServer) {
	s.RegisterService(&_ChannelPlayTab_serviceDesc, srv)
}

func _ChannelPlayTab_BatchGetWhiteUidListByTabIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetWhiteUidListByTabIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).BatchGetWhiteUidListByTabIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/BatchGetWhiteUidListByTabIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).BatchGetWhiteUidListByTabIds(ctx, req.(*BatchGetWhiteUidListByTabIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_SetWhiteUidListByTabId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetWhiteUidListByTabIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).SetWhiteUidListByTabId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/SetWhiteUidListByTabId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).SetWhiteUidListByTabId(ctx, req.(*SetWhiteUidListByTabIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_GetTabsByTabSubType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTabsByTabSubTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).GetTabsByTabSubType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/GetTabsByTabSubType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).GetTabsByTabSubType(ctx, req.(*GetTabsByTabSubTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_UpsertTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).UpsertTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/UpsertTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).UpsertTab(ctx, req.(*UpsertTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_DeleteTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).DeleteTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/DeleteTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).DeleteTab(ctx, req.(*DeleteTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_GetTabs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTabsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).GetTabs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/GetTabs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).GetTabs(ctx, req.(*GetTabsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_UpsertMinorSupervisionConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertMinorSupervisionConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).UpsertMinorSupervisionConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/UpsertMinorSupervisionConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).UpsertMinorSupervisionConfig(ctx, req.(*UpsertMinorSupervisionConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_BatchGetMinorSupervisionConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetMinorSupervisionConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).BatchGetMinorSupervisionConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/BatchGetMinorSupervisionConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).BatchGetMinorSupervisionConfig(ctx, req.(*BatchGetMinorSupervisionConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_GetTabsRealNameConfigs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTabsRealNameConfigsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).GetTabsRealNameConfigs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/GetTabsRealNameConfigs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).GetTabsRealNameConfigs(ctx, req.(*GetTabsRealNameConfigsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_UpdateTabsRealNameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTabsRealNameConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).UpdateTabsRealNameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/UpdateTabsRealNameConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).UpdateTabsRealNameConfig(ctx, req.(*UpdateTabsRealNameConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_AddTabsRealNameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTabsRealNameConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).AddTabsRealNameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/AddTabsRealNameConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).AddTabsRealNameConfig(ctx, req.(*AddTabsRealNameConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_DeleteTabsRealNameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTabsRealNameConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).DeleteTabsRealNameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/DeleteTabsRealNameConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).DeleteTabsRealNameConfig(ctx, req.(*DeleteTabsRealNameConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_GetUserTabsRealNameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserTabsRealNameConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).GetUserTabsRealNameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/GetUserTabsRealNameConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).GetUserTabsRealNameConfig(ctx, req.(*GetUserTabsRealNameConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_GetQuickMatchConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQuickMatchConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).GetQuickMatchConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/GetQuickMatchConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).GetQuickMatchConfig(ctx, req.(*GetQuickMatchConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_UpdateQuickMatchConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateQuickMatchConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).UpdateQuickMatchConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/UpdateQuickMatchConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).UpdateQuickMatchConfig(ctx, req.(*UpdateQuickMatchConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_DeleteQuickMatchConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteQuickMatchConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).DeleteQuickMatchConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/DeleteQuickMatchConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).DeleteQuickMatchConfig(ctx, req.(*DeleteQuickMatchConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_ResortQuickMatchConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResortQuickMatchConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).ResortQuickMatchConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/ResortQuickMatchConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).ResortQuickMatchConfig(ctx, req.(*ResortQuickMatchConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_UpsertNewQuickMatchConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertNewQuickMatchConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).UpsertNewQuickMatchConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/UpsertNewQuickMatchConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).UpsertNewQuickMatchConfig(ctx, req.(*UpsertNewQuickMatchConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_BatchGetNewQuickMatchConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetNewQuickMatchConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).BatchGetNewQuickMatchConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/BatchGetNewQuickMatchConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).BatchGetNewQuickMatchConfig(ctx, req.(*BatchGetNewQuickMatchConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_DelNewQuickMatchConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelNewQuickMatchConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).DelNewQuickMatchConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/DelNewQuickMatchConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).DelNewQuickMatchConfig(ctx, req.(*DelNewQuickMatchConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_GetNewQuickMatchConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewQuickMatchConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).GetNewQuickMatchConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/GetNewQuickMatchConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).GetNewQuickMatchConfig(ctx, req.(*GetNewQuickMatchConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_GetFastPCCategoryConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFastPCCategoryConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).GetFastPCCategoryConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/GetFastPCCategoryConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).GetFastPCCategoryConfig(ctx, req.(*GetFastPCCategoryConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_UpsertFastPCCategoryConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertFastPCCategoryConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).UpsertFastPCCategoryConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/UpsertFastPCCategoryConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).UpsertFastPCCategoryConfig(ctx, req.(*UpsertFastPCCategoryConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_DelFastPCCategoryConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelFastPCCategoryConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).DelFastPCCategoryConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/DelFastPCCategoryConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).DelFastPCCategoryConfig(ctx, req.(*DelFastPCCategoryConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_SortFastPCCategoryConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortFastPCCategoryConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).SortFastPCCategoryConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/SortFastPCCategoryConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).SortFastPCCategoryConfig(ctx, req.(*SortFastPCCategoryConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_GetAllTabInfoExt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllTabInfoExtReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).GetAllTabInfoExt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/GetAllTabInfoExt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).GetAllTabInfoExt(ctx, req.(*GetAllTabInfoExtReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_UpsertTabInfoExt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertTabInfoExtReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).UpsertTabInfoExt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/UpsertTabInfoExt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).UpsertTabInfoExt(ctx, req.(*UpsertTabInfoExtReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_DelTabInfoExt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelTabInfoExtReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).DelTabInfoExt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/DelTabInfoExt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).DelTabInfoExt(ctx, req.(*DelTabInfoExtReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_UpsertBanUserConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertBanUserConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).UpsertBanUserConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/UpsertBanUserConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).UpsertBanUserConfig(ctx, req.(*UpsertBanUserConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_GetBanUserConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBanUserConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).GetBanUserConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/GetBanUserConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).GetBanUserConfigList(ctx, req.(*GetBanUserConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_DelBanUserConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelBanUserConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).DelBanUserConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/DelBanUserConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).DelBanUserConfig(ctx, req.(*DelBanUserConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_BatchAddBanUserConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddBanUserConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).BatchAddBanUserConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/BatchAddBanUserConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).BatchAddBanUserConfig(ctx, req.(*BatchAddBanUserConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_GetActiveBanUserConfigWithCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActiveBanUserConfigWithCacheReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).GetActiveBanUserConfigWithCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/GetActiveBanUserConfigWithCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).GetActiveBanUserConfigWithCache(ctx, req.(*GetActiveBanUserConfigWithCacheReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_GetBanGameHallUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBanGameHallUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).GetBanGameHallUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/GetBanGameHallUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).GetBanGameHallUser(ctx, req.(*GetBanGameHallUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_UpdateBanGameHallGetStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBanGameHallGetStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).UpdateBanGameHallGetStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/UpdateBanGameHallGetStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).UpdateBanGameHallGetStatus(ctx, req.(*UpdateBanGameHallGetStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelPlayTab_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_play_tab.ChannelPlayTab",
	HandlerType: (*ChannelPlayTabServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BatchGetWhiteUidListByTabIds",
			Handler:    _ChannelPlayTab_BatchGetWhiteUidListByTabIds_Handler,
		},
		{
			MethodName: "SetWhiteUidListByTabId",
			Handler:    _ChannelPlayTab_SetWhiteUidListByTabId_Handler,
		},
		{
			MethodName: "GetTabsByTabSubType",
			Handler:    _ChannelPlayTab_GetTabsByTabSubType_Handler,
		},
		{
			MethodName: "UpsertTab",
			Handler:    _ChannelPlayTab_UpsertTab_Handler,
		},
		{
			MethodName: "DeleteTab",
			Handler:    _ChannelPlayTab_DeleteTab_Handler,
		},
		{
			MethodName: "GetTabs",
			Handler:    _ChannelPlayTab_GetTabs_Handler,
		},
		{
			MethodName: "UpsertMinorSupervisionConfig",
			Handler:    _ChannelPlayTab_UpsertMinorSupervisionConfig_Handler,
		},
		{
			MethodName: "BatchGetMinorSupervisionConfig",
			Handler:    _ChannelPlayTab_BatchGetMinorSupervisionConfig_Handler,
		},
		{
			MethodName: "GetTabsRealNameConfigs",
			Handler:    _ChannelPlayTab_GetTabsRealNameConfigs_Handler,
		},
		{
			MethodName: "UpdateTabsRealNameConfig",
			Handler:    _ChannelPlayTab_UpdateTabsRealNameConfig_Handler,
		},
		{
			MethodName: "AddTabsRealNameConfig",
			Handler:    _ChannelPlayTab_AddTabsRealNameConfig_Handler,
		},
		{
			MethodName: "DeleteTabsRealNameConfig",
			Handler:    _ChannelPlayTab_DeleteTabsRealNameConfig_Handler,
		},
		{
			MethodName: "GetUserTabsRealNameConfig",
			Handler:    _ChannelPlayTab_GetUserTabsRealNameConfig_Handler,
		},
		{
			MethodName: "GetQuickMatchConfig",
			Handler:    _ChannelPlayTab_GetQuickMatchConfig_Handler,
		},
		{
			MethodName: "UpdateQuickMatchConfig",
			Handler:    _ChannelPlayTab_UpdateQuickMatchConfig_Handler,
		},
		{
			MethodName: "DeleteQuickMatchConfig",
			Handler:    _ChannelPlayTab_DeleteQuickMatchConfig_Handler,
		},
		{
			MethodName: "ResortQuickMatchConfig",
			Handler:    _ChannelPlayTab_ResortQuickMatchConfig_Handler,
		},
		{
			MethodName: "UpsertNewQuickMatchConfig",
			Handler:    _ChannelPlayTab_UpsertNewQuickMatchConfig_Handler,
		},
		{
			MethodName: "BatchGetNewQuickMatchConfig",
			Handler:    _ChannelPlayTab_BatchGetNewQuickMatchConfig_Handler,
		},
		{
			MethodName: "DelNewQuickMatchConfig",
			Handler:    _ChannelPlayTab_DelNewQuickMatchConfig_Handler,
		},
		{
			MethodName: "GetNewQuickMatchConfig",
			Handler:    _ChannelPlayTab_GetNewQuickMatchConfig_Handler,
		},
		{
			MethodName: "GetFastPCCategoryConfig",
			Handler:    _ChannelPlayTab_GetFastPCCategoryConfig_Handler,
		},
		{
			MethodName: "UpsertFastPCCategoryConfig",
			Handler:    _ChannelPlayTab_UpsertFastPCCategoryConfig_Handler,
		},
		{
			MethodName: "DelFastPCCategoryConfig",
			Handler:    _ChannelPlayTab_DelFastPCCategoryConfig_Handler,
		},
		{
			MethodName: "SortFastPCCategoryConfig",
			Handler:    _ChannelPlayTab_SortFastPCCategoryConfig_Handler,
		},
		{
			MethodName: "GetAllTabInfoExt",
			Handler:    _ChannelPlayTab_GetAllTabInfoExt_Handler,
		},
		{
			MethodName: "UpsertTabInfoExt",
			Handler:    _ChannelPlayTab_UpsertTabInfoExt_Handler,
		},
		{
			MethodName: "DelTabInfoExt",
			Handler:    _ChannelPlayTab_DelTabInfoExt_Handler,
		},
		{
			MethodName: "UpsertBanUserConfig",
			Handler:    _ChannelPlayTab_UpsertBanUserConfig_Handler,
		},
		{
			MethodName: "GetBanUserConfigList",
			Handler:    _ChannelPlayTab_GetBanUserConfigList_Handler,
		},
		{
			MethodName: "DelBanUserConfig",
			Handler:    _ChannelPlayTab_DelBanUserConfig_Handler,
		},
		{
			MethodName: "BatchAddBanUserConfig",
			Handler:    _ChannelPlayTab_BatchAddBanUserConfig_Handler,
		},
		{
			MethodName: "GetActiveBanUserConfigWithCache",
			Handler:    _ChannelPlayTab_GetActiveBanUserConfigWithCache_Handler,
		},
		{
			MethodName: "GetBanGameHallUser",
			Handler:    _ChannelPlayTab_GetBanGameHallUser_Handler,
		},
		{
			MethodName: "UpdateBanGameHallGetStatus",
			Handler:    _ChannelPlayTab_UpdateBanGameHallGetStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/channel-play-tab/channel-play-tab.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/channel-play-tab/channel-play-tab.proto", fileDescriptor_channel_play_tab_8ca61c1d87a331a2)
}

var fileDescriptor_channel_play_tab_8ca61c1d87a331a2 = []byte{
	// 3787 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x3b, 0x5d, 0x6f, 0x1c, 0x47,
	0x72, 0x9c, 0xdd, 0xe5, 0xc7, 0x16, 0x3f, 0x34, 0x6a, 0x7e, 0x68, 0xb9, 0x24, 0x45, 0x7a, 0x64,
	0xd9, 0x3c, 0xda, 0xa2, 0x6c, 0x5a, 0xbe, 0x3b, 0xdc, 0x25, 0x3a, 0x53, 0xd4, 0x9a, 0xe4, 0x9d,
	0x48, 0xf1, 0x86, 0x4b, 0xcb, 0x67, 0x23, 0x18, 0xcc, 0xce, 0x36, 0x57, 0x73, 0x9c, 0x9d, 0x19,
	0x4d, 0xf7, 0xca, 0x64, 0x70, 0x48, 0x90, 0xbc, 0xe4, 0x21, 0x08, 0x70, 0x40, 0x1e, 0xf3, 0x13,
	0x92, 0x97, 0xbc, 0x06, 0xc8, 0x63, 0x1e, 0x02, 0xe4, 0x07, 0x04, 0x41, 0x9e, 0x02, 0xe4, 0x21,
	0xc8, 0x9f, 0x08, 0xba, 0x7b, 0x66, 0x77, 0x3e, 0xba, 0x97, 0xbb, 0x72, 0x7c, 0x4f, 0xdc, 0xa9,
	0xee, 0xae, 0xaa, 0xae, 0xaa, 0xae, 0xaa, 0xae, 0x6a, 0xc2, 0x8f, 0x29, 0x7d, 0xfc, 0xa6, 0xe7,
	0x3a, 0x57, 0xc4, 0xf5, 0xde, 0xe2, 0xe8, 0xb1, 0xf3, 0xda, 0xf6, 0x7d, 0xec, 0x3d, 0x0a, 0x3d,
	0xfb, 0xe6, 0x11, 0xb5, 0x5b, 0x05, 0xc0, 0x6e, 0x18, 0x05, 0x34, 0x40, 0x7a, 0x0c, 0xb7, 0x18,
	0xdc, 0xa2, 0x76, 0xcb, 0xe8, 0xc2, 0xe6, 0x33, 0x9b, 0x3a, 0xaf, 0x0f, 0x31, 0x7d, 0xf5, 0xda,
	0xa5, 0xf8, 0xc2, 0x6d, 0xbf, 0x70, 0x09, 0x7d, 0x76, 0xd3, 0xb4, 0x5b, 0xc7, 0x6d, 0x62, 0xe2,
	0x37, 0xe8, 0x1e, 0x4c, 0x53, 0xbb, 0x65, 0xb9, 0x6d, 0x52, 0xd3, 0xb6, 0xca, 0xdb, 0xf3, 0xe6,
	0x14, 0xe5, 0x63, 0x68, 0x05, 0xa6, 0x48, 0xd0, 0x8b, 0x1c, 0x5c, 0x2b, 0x6d, 0x69, 0xdb, 0x55,
	0x33, 0xfe, 0x42, 0xab, 0x30, 0xe3, 0x07, 0x96, 0x63, 0x3b, 0xaf, 0x71, 0xad, 0xbc, 0xa5, 0x6d,
	0xcf, 0x98, 0xd3, 0x7e, 0x70, 0xc0, 0x3e, 0x8d, 0x7f, 0x28, 0xc1, 0xd6, 0x70, 0x7a, 0x24, 0x44,
	0x6d, 0x98, 0x63, 0x04, 0x3d, 0x97, 0x50, 0xab, 0x6b, 0x87, 0x9c, 0xea, 0xec, 0xde, 0xb3, 0xdd,
	0x3c, 0xf3, 0xbb, 0xb7, 0x61, 0xda, 0x6d, 0xda, 0x2d, 0x06, 0x3b, 0xb1, 0xc3, 0x86, 0x4f, 0xa3,
	0x1b, 0x13, 0x68, 0x1f, 0x50, 0x37, 0x60, 0x2e, 0xbd, 0x0e, 0x21, 0xa8, 0x30, 0x8a, 0xf1, 0x1e,
	0xf9, 0xef, 0xfa, 0x9f, 0xc1, 0x9d, 0x1c, 0x0a, 0xa4, 0x43, 0xf9, 0x0a, 0xdf, 0xd4, 0xb4, 0x2d,
	0x6d, 0x7b, 0xde, 0x64, 0x3f, 0xd1, 0x05, 0x4c, 0xbe, 0xb5, 0xbd, 0x9e, 0x90, 0xc2, 0xec, 0xde,
	0x2f, 0xde, 0x81, 0xcf, 0xf4, 0x80, 0x29, 0xb0, 0xfd, 0xac, 0xf4, 0x53, 0xcd, 0xc0, 0xb0, 0x7a,
	0x2e, 0x5d, 0xc6, 0xf4, 0xb2, 0x0c, 0x53, 0x42, 0x2f, 0x31, 0x33, 0x93, 0x5c, 0x2d, 0x4c, 0xfa,
	0x3d, 0xb7, 0xcd, 0xa5, 0x57, 0x2b, 0xf1, 0xbd, 0x4c, 0xf7, 0xe2, 0x2d, 0x0e, 0x14, 0x56, 0x4e,
	0x2b, 0xcc, 0x58, 0x87, 0xba, 0x8a, 0x0c, 0x09, 0x8d, 0x7f, 0xd2, 0xa0, 0xdc, 0xb4, 0x5b, 0x43,
	0xe8, 0x31, 0xb0, 0x6f, 0x77, 0x13, 0x3b, 0x60, 0xe6, 0x72, 0x6a, 0x77, 0x31, 0x5a, 0x83, 0xaa,
	0xdb, 0xb5, 0x3b, 0xd8, 0xea, 0x45, 0x5e, 0x4c, 0x72, 0x86, 0x03, 0x2e, 0x22, 0x0f, 0x3d, 0x15,
	0x5a, 0x26, 0xbd, 0x96, 0x45, 0x6f, 0x42, 0x5c, 0xab, 0x6c, 0x69, 0xdb, 0x0b, 0x7b, 0xeb, 0x45,
	0xe9, 0x35, 0xed, 0xd6, 0x79, 0xaf, 0xd5, 0xbc, 0x09, 0x31, 0xd7, 0x5f, 0xfc, 0x1b, 0xbd, 0x0f,
	0x0b, 0x2e, 0x61, 0x13, 0xac, 0xb7, 0x2e, 0x71, 0x5b, 0x1e, 0xae, 0x4d, 0x72, 0x5b, 0x9b, 0x73,
	0x49, 0xd3, 0x6e, 0x7d, 0x25, 0x60, 0xc6, 0x4f, 0x60, 0xee, 0x22, 0x24, 0x38, 0xa2, 0x4d, 0xbb,
	0xc5, 0x84, 0xf6, 0x21, 0x94, 0xa9, 0xdd, 0xe2, 0x3b, 0x98, 0xdd, 0x5b, 0x96, 0x12, 0x33, 0xd9,
	0x0c, 0xe3, 0x0e, 0xcc, 0xa7, 0x16, 0x92, 0xd0, 0xf8, 0x6b, 0x0d, 0x56, 0x0e, 0x31, 0xfb, 0x24,
	0x5c, 0x3a, 0x09, 0x4f, 0xf8, 0x4d, 0x61, 0x2b, 0xda, 0x98, 0x5b, 0x79, 0x87, 0x83, 0xf4, 0x1c,
	0xee, 0x49, 0x99, 0x21, 0x21, 0xfa, 0x11, 0x54, 0xa8, 0xdd, 0x22, 0xf1, 0xb1, 0x51, 0xec, 0x91,
	0x4f, 0x31, 0x1e, 0xc2, 0xdc, 0x73, 0xec, 0x61, 0x8a, 0x63, 0xe9, 0xc8, 0x55, 0xcc, 0x64, 0x91,
	0x9a, 0x46, 0x42, 0xe3, 0x97, 0x00, 0x31, 0x75, 0xb6, 0x2a, 0xcd, 0xa6, 0x96, 0x61, 0x13, 0xbd,
	0x07, 0x73, 0x81, 0xef, 0xdd, 0xf4, 0x55, 0x54, 0xe2, 0xc3, 0xb3, 0x0c, 0x96, 0x68, 0xe8, 0xa7,
	0x30, 0xdb, 0xc7, 0x35, 0x1e, 0xf7, 0xff, 0x51, 0x82, 0x95, 0x13, 0xd7, 0x0f, 0xa2, 0xf3, 0x5e,
	0x88, 0x23, 0x46, 0x22, 0xf0, 0x0f, 0x02, 0xff, 0xd2, 0xed, 0xa0, 0x05, 0x28, 0xc5, 0x9b, 0xa8,
	0x9a, 0x25, 0xb7, 0x9d, 0xda, 0x58, 0x29, 0x6d, 0xbb, 0x9b, 0x30, 0xeb, 0xd8, 0x14, 0x77, 0x82,
	0xe8, 0x86, 0x8d, 0x95, 0xf9, 0x18, 0x24, 0xa0, 0xe3, 0x36, 0xda, 0x00, 0xe8, 0xda, 0xd1, 0x15,
	0xa6, 0xdc, 0xfd, 0x55, 0xf8, 0x71, 0xaa, 0x0a, 0x08, 0xf3, 0x80, 0xbf, 0x06, 0x9d, 0x7c, 0xe7,
	0x52, 0xe7, 0xb5, 0x15, 0xf8, 0x16, 0x71, 0xb0, 0x8f, 0x49, 0x6d, 0x72, 0xab, 0xbc, 0xbd, 0xb0,
	0xf7, 0x61, 0x91, 0xf1, 0x3c, 0xab, 0xe7, 0x6c, 0xbe, 0xb9, 0x20, 0x10, 0xbc, 0x14, 0x9f, 0x04,
	0xfd, 0x04, 0x6a, 0x3e, 0xc6, 0x6d, 0xcb, 0x79, 0x8d, 0x9d, 0x2b, 0x2b, 0xc2, 0x1d, 0x97, 0x50,
	0x1c, 0x59, 0xd4, 0xed, 0xe2, 0xda, 0xd4, 0x96, 0xb6, 0x5d, 0x36, 0x97, 0xd9, 0xf8, 0x01, 0x1b,
	0x36, 0xe3, 0xd1, 0xa6, 0xdb, 0xc5, 0xe8, 0x04, 0x66, 0x1d, 0xbe, 0x79, 0x61, 0x83, 0xd3, 0xdc,
	0x06, 0x3f, 0xbe, 0x9d, 0x0d, 0x21, 0x31, 0x61, 0x93, 0x4e, 0xff, 0xb7, 0xe1, 0xc0, 0xa6, 0xb0,
	0x7f, 0xf9, 0x7c, 0xa6, 0xf7, 0x2f, 0x60, 0x4a, 0x2c, 0x88, 0x8f, 0xd3, 0xf6, 0xa8, 0xc4, 0xcc,
	0x78, 0x9d, 0x61, 0xc0, 0xd6, 0x70, 0x22, 0x24, 0x34, 0xfe, 0x51, 0x83, 0xf7, 0x12, 0x07, 0xaa,
	0xe6, 0x45, 0x19, 0xa4, 0xde, 0x83, 0xb9, 0x94, 0x8a, 0x49, 0xec, 0x12, 0x67, 0x07, 0x3a, 0x26,
	0x2a, 0xb7, 0x98, 0xb1, 0xeb, 0x4a, 0xd6, 0xae, 0x37, 0x00, 0x22, 0x4c, 0x7b, 0x91, 0x6f, 0xd9,
	0x9e, 0x17, 0x3b, 0x9e, 0xaa, 0x80, 0xec, 0x7b, 0x9e, 0xf1, 0xef, 0x65, 0x30, 0x6e, 0xe3, 0x99,
	0x84, 0x08, 0xc3, 0x2c, 0x63, 0x5a, 0x08, 0x23, 0x31, 0xf9, 0xe7, 0xea, 0xf8, 0xa1, 0x46, 0xc5,
	0x4e, 0x85, 0xf8, 0x22, 0x83, 0x48, 0x17, 0x03, 0x10, 0x05, 0xbd, 0x2f, 0x82, 0x84, 0x56, 0x89,
	0xd3, 0x3a, 0x7e, 0x27, 0x5a, 0x07, 0x31, 0xb2, 0x0c, 0xc1, 0x3b, 0x4e, 0x16, 0x5a, 0xef, 0xf0,
	0xd8, 0x99, 0x9e, 0x23, 0x89, 0x9d, 0x4f, 0xb3, 0xb1, 0x73, 0x74, 0x0b, 0x1a, 0x04, 0xc9, 0xba,
	0x07, 0x4b, 0x32, 0x8e, 0x7e, 0x18, 0x6a, 0x46, 0x03, 0x56, 0xfb, 0xee, 0xca, 0xf6, 0x58, 0x98,
	0x8b, 0x89, 0x32, 0x2b, 0x44, 0x50, 0x09, 0xed, 0x0e, 0x8e, 0x69, 0xf2, 0xdf, 0x68, 0x09, 0x26,
	0x9d, 0xa0, 0xe7, 0xd3, 0xc4, 0xf3, 0xf0, 0x0f, 0xe3, 0xdf, 0x34, 0x40, 0x45, 0x24, 0x2c, 0x62,
	0xf6, 0xfd, 0x4d, 0x8c, 0x65, 0x26, 0x71, 0x37, 0xdc, 0x5b, 0x25, 0x0c, 0x5f, 0x75, 0xe2, 0x58,
	0x01, 0x31, 0xe8, 0xec, 0xaa, 0xc3, 0x0e, 0x81, 0xe3, 0xb9, 0xd6, 0x5b, 0x1c, 0x25, 0x96, 0xec,
	0x78, 0xee, 0x57, 0x38, 0x42, 0x0f, 0x60, 0x3e, 0xf6, 0x53, 0x84, 0xda, 0xb4, 0x47, 0x62, 0x73,
	0x9e, 0x13, 0xc0, 0x73, 0x0e, 0x4b, 0x1f, 0xa1, 0xc9, 0xa1, 0x47, 0x68, 0xaa, 0x70, 0x84, 0x8c,
	0x08, 0xea, 0x2a, 0xa9, 0x90, 0x10, 0x3d, 0x85, 0xe9, 0xac, 0x8d, 0xbf, 0x2f, 0x75, 0xeb, 0xb9,
	0xb5, 0x66, 0xb2, 0x88, 0x89, 0x90, 0x06, 0xd4, 0xf6, 0xfa, 0xce, 0x9b, 0x7d, 0x18, 0xdf, 0xc2,
	0xda, 0x45, 0xd8, 0xb6, 0x79, 0x54, 0xca, 0x2f, 0xc5, 0x6f, 0xd0, 0x1f, 0xe5, 0xbc, 0xd3, 0x68,
	0x34, 0x13, 0xcf, 0x74, 0x1f, 0xd6, 0xd5, 0xc8, 0x49, 0x68, 0x7c, 0x0d, 0xb5, 0xfd, 0x76, 0xfb,
	0x87, 0xa0, 0xbc, 0x06, 0xab, 0x0a, 0xcc, 0x24, 0x34, 0x28, 0xac, 0xf5, 0x23, 0xb1, 0x84, 0xf2,
	0x0f, 0x63, 0x3e, 0x4c, 0x18, 0x6a, 0xaa, 0x24, 0x34, 0x7a, 0xb0, 0x7e, 0x88, 0xe9, 0x05, 0xc1,
	0xd1, 0x1f, 0x80, 0xad, 0xf9, 0x3e, 0x5b, 0x16, 0x6c, 0x0c, 0x21, 0x9b, 0xb7, 0x3b, 0x6d, 0x6c,
	0xbb, 0x33, 0xfe, 0xae, 0x0c, 0x55, 0x1e, 0x96, 0x8f, 0xfd, 0xcb, 0x60, 0xd4, 0x9c, 0x22, 0xce,
	0x87, 0x79, 0x10, 0x16, 0xfc, 0xb2, 0x63, 0xc5, 0xf3, 0xbc, 0x5c, 0xba, 0x51, 0x29, 0xa4, 0x1b,
	0x87, 0xd9, 0x18, 0x3e, 0xc9, 0x63, 0xf8, 0x07, 0x45, 0xa6, 0x7f, 0xcd, 0xee, 0x7e, 0x27, 0xcc,
	0x53, 0xcb, 0xa3, 0x37, 0xda, 0x81, 0xbb, 0x1d, 0xbb, 0x8b, 0xad, 0xb6, 0x4b, 0xf8, 0x22, 0x9e,
	0x9d, 0x4f, 0x71, 0xd6, 0xef, 0xb0, 0x81, 0xe7, 0x02, 0x9e, 0x64, 0xe9, 0xad, 0x8e, 0xe5, 0x04,
	0x5e, 0x10, 0x91, 0xda, 0xf4, 0x56, 0x99, 0x65, 0xe9, 0xad, 0xce, 0x01, 0xff, 0x66, 0x9e, 0xa3,
	0xd5, 0xa3, 0x34, 0xf0, 0x2d, 0x7c, 0x79, 0x89, 0x1d, 0x5a, 0x9b, 0xe1, 0x4c, 0xcf, 0x09, 0x60,
	0x83, 0xc3, 0x18, 0x86, 0xdf, 0xf6, 0xba, 0xa1, 0xe5, 0xb9, 0xfe, 0x55, 0xad, 0x2a, 0xf2, 0x7c,
	0x06, 0x78, 0xe1, 0xfa, 0x57, 0xe8, 0x15, 0x2c, 0x75, 0x5d, 0xdf, 0xb5, 0x38, 0x3f, 0xf8, 0x9a,
	0x46, 0xb6, 0xe5, 0xfa, 0x97, 0x41, 0x0d, 0xb8, 0x46, 0x24, 0x9b, 0x3b, 0x0a, 0x58, 0xf0, 0x71,
	0x0f, 0xed, 0x2e, 0x6e, 0xb0, 0xe9, 0x4c, 0xf8, 0xe6, 0xdd, 0x6e, 0x1e, 0xc4, 0x5c, 0xe8, 0x92,
	0x6c, 0x2e, 0x0b, 0xce, 0xae, 0x4f, 0xa3, 0xc0, 0xa2, 0xf8, 0x9a, 0xc6, 0x0a, 0xab, 0x72, 0x48,
	0x13, 0x5f, 0x53, 0xa6, 0xb7, 0x56, 0x87, 0x5f, 0x49, 0x84, 0xad, 0x4d, 0xb6, 0x3a, 0xec, 0x3e,
	0xb2, 0x0a, 0x33, 0x89, 0x18, 0x62, 0xf3, 0x9f, 0x8e, 0xa5, 0xc0, 0xf4, 0x16, 0x0b, 0x81, 0x63,
	0xac, 0x08, 0x13, 0x15, 0x20, 0x8e, 0x72, 0x20, 0xa5, 0x58, 0x8c, 0x93, 0x5c, 0x8c, 0xb1, 0x94,
	0x62, 0x51, 0x6e, 0x00, 0x78, 0x01, 0xa5, 0xae, 0xb8, 0x0e, 0x09, 0x65, 0x54, 0x05, 0xe4, 0x22,
	0xf2, 0x92, 0xfb, 0x45, 0x5e, 0xb5, 0xec, 0xfc, 0xe4, 0xcc, 0x42, 0x7b, 0x67, 0xb3, 0x48, 0xe2,
	0x53, 0x29, 0x1b, 0x9f, 0x3c, 0xb7, 0xeb, 0xd2, 0xd8, 0x58, 0xc5, 0x87, 0x71, 0xc1, 0xef, 0x17,
	0x45, 0x66, 0x48, 0x88, 0x7e, 0x06, 0xc0, 0x53, 0x5d, 0xa1, 0x46, 0xe1, 0xd0, 0xd7, 0x8a, 0xcc,
	0xf4, 0x0f, 0x8e, 0x59, 0x25, 0xc9, 0x4f, 0xe3, 0x15, 0xac, 0x0a, 0xb7, 0x2a, 0xdb, 0x66, 0x1e,
	0xb1, 0x36, 0x06, 0xe2, 0x75, 0xa8, 0xab, 0x10, 0x93, 0xd0, 0xf8, 0x08, 0x56, 0x85, 0x03, 0x93,
	0x91, 0xcd, 0x9d, 0x6b, 0x86, 0x4a, 0x35, 0x99, 0x84, 0xc6, 0x23, 0x58, 0x35, 0x31, 0x09, 0x22,
	0xa9, 0xa2, 0x74, 0x28, 0x27, 0x19, 0x68, 0xd5, 0x64, 0x3f, 0x19, 0x32, 0xd5, 0x74, 0x12, 0x1a,
	0xff, 0xaa, 0xc1, 0xe2, 0x29, 0xfe, 0x2e, 0x3f, 0x36, 0xa6, 0xab, 0xe1, 0x87, 0xbb, 0x9c, 0xbd,
	0x7a, 0xb3, 0x90, 0xe9, 0x52, 0x0f, 0xc7, 0xc6, 0x2a, 0x3e, 0xf2, 0x86, 0x3c, 0x59, 0x30, 0xe4,
	0x3a, 0xcc, 0x84, 0x01, 0x71, 0xa9, 0x1b, 0xf8, 0xdc, 0x42, 0xe7, 0xcd, 0xfe, 0x37, 0xb3, 0xdf,
	0x1e, 0x17, 0x71, 0xdb, 0xb2, 0x29, 0xbf, 0x5f, 0x94, 0xcd, 0x6a, 0x0c, 0xd9, 0xa7, 0xc6, 0x9f,
	0xb0, 0x88, 0xc9, 0x72, 0x79, 0xc9, 0x86, 0x98, 0x6c, 0xfe, 0x38, 0x17, 0x15, 0x1f, 0x16, 0x35,
	0x2b, 0x5b, 0x99, 0x84, 0xc5, 0x4d, 0xd8, 0x18, 0x82, 0x9e, 0x84, 0xc6, 0x19, 0xdc, 0x4f, 0x72,
	0x57, 0x05, 0x07, 0x8a, 0x02, 0x46, 0x2a, 0xef, 0x29, 0xa5, 0xf3, 0x1e, 0xa3, 0x35, 0xa8, 0x8d,
	0x29, 0x88, 0xa2, 0x5f, 0xe4, 0x33, 0x9b, 0x11, 0x77, 0xd5, 0x0f, 0x31, 0xc2, 0x32, 0x15, 0x0c,
	0xcb, 0x2d, 0x53, 0x25, 0x80, 0x3d, 0x9e, 0x99, 0x8e, 0xb5, 0x77, 0xe3, 0x5b, 0x9e, 0xb7, 0xa9,
	0x76, 0xf7, 0x3d, 0x55, 0xb6, 0xce, 0x91, 0x7f, 0x69, 0x13, 0x7a, 0x76, 0x90, 0xcd, 0xd0, 0x4d,
	0xfc, 0xc6, 0xf8, 0x6f, 0x0d, 0x96, 0x64, 0x63, 0xa9, 0x5d, 0xcf, 0x73, 0xe3, 0x47, 0x50, 0x49,
	0x15, 0x97, 0xf8, 0xef, 0xb4, 0xce, 0xca, 0x99, 0x5c, 0x15, 0x41, 0x85, 0x9d, 0xb6, 0x38, 0xb6,
	0xf2, 0xdf, 0x39, 0xc3, 0x9d, 0xcc, 0x19, 0x2e, 0x5b, 0xe2, 0x3a, 0xb1, 0xbd, 0x57, 0x4d, 0xfe,
	0x1b, 0x1d, 0xc3, 0x7c, 0x3f, 0x52, 0xa7, 0xae, 0xd3, 0x92, 0xfc, 0x21, 0xbb, 0x05, 0xee, 0x71,
	0xfb, 0xd9, 0x32, 0xbf, 0x48, 0x5b, 0xb0, 0xa6, 0x94, 0x02, 0x09, 0xd1, 0x17, 0x79, 0x0b, 0xfa,
	0xe0, 0x36, 0x1a, 0x79, 0x13, 0xb2, 0x92, 0x93, 0xa1, 0x90, 0x34, 0x7a, 0x9a, 0x53, 0xe3, 0xa8,
	0x14, 0x12, 0x3d, 0x6e, 0xc1, 0xfd, 0x61, 0x04, 0x48, 0x68, 0x7c, 0xcc, 0x0d, 0x53, 0x45, 0x3f,
	0xa7, 0x50, 0x63, 0x83, 0x27, 0xb1, 0x4a, 0x64, 0x8f, 0x61, 0xed, 0x3c, 0x50, 0xef, 0x26, 0xe5,
	0x63, 0xe7, 0x85, 0x8f, 0xbd, 0x0f, 0xeb, 0xea, 0x05, 0x24, 0x34, 0x4c, 0x58, 0x3c, 0xc4, 0x74,
	0xdf, 0xf3, 0x9a, 0x76, 0x8b, 0x05, 0x8b, 0xc6, 0x35, 0x65, 0x88, 0x7e, 0x0e, 0x33, 0xf8, 0x9a,
	0xa6, 0x43, 0xea, 0x96, 0x34, 0x3d, 0x8c, 0x97, 0x34, 0xfc, 0x5e, 0xd7, 0x9c, 0xc6, 0xd7, 0x94,
	0x6b, 0xf5, 0xbf, 0x34, 0x58, 0x18, 0x8c, 0x1d, 0x53, 0xdc, 0x55, 0xb9, 0x97, 0x34, 0x99, 0xd2,
	0x98, 0x64, 0x58, 0x66, 0x25, 0x0c, 0xd5, 0xa2, 0xe2, 0xd2, 0x56, 0x36, 0x67, 0x04, 0xa0, 0x49,
	0xd0, 0x19, 0x2c, 0x5c, 0xda, 0x84, 0x5a, 0xa1, 0x13, 0x5f, 0xeb, 0xb9, 0x09, 0x4b, 0xef, 0xb5,
	0x03, 0xfc, 0x5c, 0x4a, 0x8e, 0x90, 0xce, 0xd1, 0x84, 0x39, 0x77, 0x99, 0xfa, 0x7e, 0x36, 0x0f,
	0xb3, 0x8c, 0x57, 0x27, 0xf0, 0x29, 0xf6, 0xa9, 0xf1, 0xf7, 0x1a, 0xac, 0xc8, 0x57, 0xa2, 0x17,
	0xf0, 0x7e, 0x42, 0xfb, 0x75, 0xd0, 0xc5, 0x16, 0x4b, 0x25, 0xac, 0x96, 0xed, 0x5c, 0x75, 0xa2,
	0xa0, 0xe7, 0xb7, 0x2d, 0xb7, 0x2b, 0x52, 0x2c, 0xe1, 0xbc, 0xee, 0x0b, 0x2a, 0x47, 0x41, 0x17,
	0x9f, 0xd9, 0x1d, 0xfc, 0xac, 0x3f, 0xef, 0xb8, 0xcb, 0x73, 0xaf, 0x03, 0xd8, 0x4c, 0xb0, 0x45,
	0x41, 0xd0, 0x95, 0x21, 0x12, 0xa7, 0xbf, 0x2e, 0x10, 0x99, 0x41, 0xd0, 0xcd, 0x23, 0x31, 0x4e,
	0x61, 0xa9, 0xa8, 0x66, 0x12, 0xa2, 0x1f, 0xc3, 0xa4, 0x4b, 0x71, 0x37, 0x39, 0x5f, 0x43, 0xa5,
	0xcf, 0x14, 0x69, 0x8a, 0xe9, 0xc6, 0xaf, 0x60, 0xb1, 0x5f, 0x01, 0x4e, 0x99, 0xcd, 0x13, 0xa8,
	0xb0, 0xf1, 0xf8, 0x2c, 0xdd, 0x8e, 0x8d, 0xcf, 0x36, 0x56, 0x60, 0xa9, 0x88, 0x8c, 0x84, 0xc6,
	0x25, 0xe8, 0xcf, 0x71, 0xce, 0x30, 0x7f, 0x00, 0x43, 0x32, 0x16, 0xe1, 0x6e, 0x8e, 0x0e, 0x09,
	0x8d, 0x6f, 0x60, 0xfe, 0xa4, 0xe7, 0x51, 0x97, 0x5d, 0xa1, 0x78, 0xe6, 0x8c, 0xa0, 0x42, 0x69,
	0x3f, 0xe4, 0xf0, 0xdf, 0xec, 0xbc, 0xf5, 0xfa, 0x89, 0x07, 0xfb, 0xc9, 0xb2, 0xdd, 0x1e, 0xc1,
	0x91, 0xe5, 0xbb, 0xce, 0x55, 0x2a, 0xf7, 0x98, 0x63, 0xc0, 0xd3, 0x18, 0x66, 0xfc, 0xcf, 0x14,
	0xdc, 0x7d, 0x66, 0xfb, 0x0c, 0xf5, 0x59, 0x40, 0xa8, 0x22, 0xb1, 0xf9, 0x22, 0x9b, 0xd9, 0x8a,
	0x6d, 0x6d, 0xca, 0xaa, 0x52, 0xbe, 0x3a, 0xa5, 0xe5, 0x2c, 0x97, 0x53, 0x2c, 0xbf, 0x0f, 0x0b,
	0x4e, 0x14, 0x7c, 0xd7, 0xb6, 0x98, 0x7d, 0x84, 0xc9, 0x55, 0xab, 0x6a, 0xce, 0x71, 0xe8, 0x21,
	0x03, 0x66, 0xe3, 0x7e, 0xb6, 0xde, 0xb1, 0x01, 0x40, 0xa8, 0x1d, 0xd1, 0x74, 0xd1, 0xb5, 0xca,
	0x21, 0xbc, 0xd0, 0xba, 0x0a, 0x33, 0xd8, 0x6f, 0x8b, 0x41, 0x91, 0x05, 0x4d, 0x63, 0xbf, 0xcd,
	0x87, 0x36, 0x00, 0x5a, 0xb6, 0x6f, 0x45, 0xd8, 0x26, 0x81, 0xcf, 0xaf, 0x4a, 0x55, 0xb3, 0xda,
	0xb2, 0x7d, 0x93, 0x03, 0x78, 0x35, 0x3c, 0xc4, 0x11, 0x3f, 0xce, 0x6c, 0x75, 0x95, 0xaf, 0x9e,
	0x8d, 0x61, 0x1c, 0x43, 0x1d, 0x66, 0xc4, 0x67, 0x10, 0xf1, 0x1b, 0x52, 0xd5, 0xec, 0x7f, 0xa3,
	0x4f, 0x60, 0x2a, 0x2e, 0xdf, 0xcc, 0x72, 0x39, 0xd5, 0x24, 0xb9, 0x31, 0x1f, 0x37, 0xe3, 0x79,
	0x68, 0x1b, 0xf4, 0xb4, 0x20, 0xb8, 0xb2, 0xe6, 0x38, 0xd6, 0x85, 0x81, 0x28, 0x78, 0xbe, 0xf8,
	0x19, 0xac, 0xa4, 0x67, 0x72, 0xfd, 0x8a, 0xb2, 0xd5, 0x3c, 0x57, 0xfc, 0xe2, 0x60, 0x3e, 0x53,
	0xe9, 0x01, 0x1b, 0x2a, 0x1a, 0xc2, 0x42, 0xd1, 0x10, 0xd0, 0x3e, 0xcc, 0x33, 0x99, 0x84, 0x01,
	0x89, 0x6d, 0xf7, 0x0e, 0x67, 0x7e, 0x43, 0xaa, 0x64, 0x66, 0x2a, 0x5c, 0xc5, 0xb3, 0xad, 0xc1,
	0x47, 0x62, 0x82, 0xfa, 0xc0, 0x04, 0x3f, 0x84, 0x3b, 0xdf, 0xd9, 0x91, 0xef, 0xfa, 0x1d, 0xab,
	0x8b, 0x09, 0x61, 0x77, 0x9a, 0xbb, 0x62, 0x5f, 0x31, 0xf8, 0x44, 0x40, 0x79, 0x1e, 0x4c, 0x99,
	0x8a, 0x11, 0xcf, 0xc9, 0xc5, 0x07, 0x33, 0x9a, 0x1e, 0x03, 0x2e, 0x8a, 0x5e, 0x1f, 0xfb, 0x8d,
	0xbe, 0x8e, 0x37, 0xc3, 0x2e, 0x1f, 0xbc, 0xed, 0xb8, 0xc4, 0xdd, 0xc5, 0x13, 0x29, 0x9f, 0x59,
	0xb3, 0xde, 0x4d, 0x0e, 0x50, 0xbf, 0xd1, 0x38, 0xdb, 0x1b, 0x40, 0xea, 0x16, 0xe8, 0xf9, 0x09,
	0xe9, 0xe2, 0x64, 0x55, 0x14, 0x27, 0x3f, 0xcf, 0x16, 0x27, 0x25, 0x87, 0x20, 0x73, 0x56, 0xd3,
	0x35, 0xc9, 0xbf, 0x2d, 0x41, 0x8d, 0x67, 0xaa, 0xfb, 0xed, 0x76, 0xcc, 0xdc, 0x20, 0x5e, 0xf6,
	0x25, 0xa0, 0xa5, 0x25, 0x90, 0xb5, 0xf1, 0xd2, 0x30, 0x1b, 0x2f, 0x0f, 0xb3, 0xf1, 0x4a, 0xde,
	0xc6, 0x25, 0x9a, 0x99, 0x94, 0x6a, 0xe6, 0x00, 0x16, 0x32, 0x76, 0x21, 0xea, 0x8a, 0xb7, 0x1a,
	0xc6, 0x5c, 0xca, 0x30, 0x48, 0xe6, 0xb8, 0x4c, 0x67, 0x8f, 0x8b, 0xb1, 0x06, 0xab, 0x0a, 0xa1,
	0x90, 0xd0, 0xb8, 0x80, 0x15, 0xe1, 0x8f, 0x0b, 0xf2, 0xfa, 0x79, 0x2e, 0x5b, 0x7a, 0x30, 0x82,
	0x01, 0xf4, 0x53, 0xa5, 0x55, 0xb8, 0x27, 0x45, 0x4b, 0x42, 0xe3, 0x7f, 0x35, 0x7e, 0xa5, 0xce,
	0x0c, 0xf0, 0x7e, 0xaf, 0xa2, 0x6e, 0xcc, 0x32, 0x59, 0xf7, 0x4f, 0xfb, 0x77, 0x75, 0xf6, 0x9b,
	0xc9, 0x5e, 0x34, 0x87, 0xf8, 0xc9, 0x14, 0x2d, 0xc1, 0x2a, 0x6f, 0x07, 0xf1, 0xf3, 0x98, 0xf8,
	0xc2, 0x4a, 0xca, 0x17, 0x0e, 0x9c, 0xc6, 0xe4, 0x88, 0x4e, 0xe3, 0xff, 0x43, 0x31, 0x46, 0x00,
	0x35, 0xf9, 0x66, 0xf9, 0xb5, 0x22, 0x97, 0xf2, 0x8e, 0x24, 0xe2, 0x5b, 0xaa, 0xc1, 0x0f, 0x61,
	0xf1, 0x39, 0xf6, 0x0a, 0xda, 0xcc, 0x5f, 0xa1, 0x56, 0x60, 0xa9, 0x38, 0x8d, 0x84, 0xc6, 0xbf,
	0x68, 0xb0, 0xb8, 0xef, 0x50, 0xf7, 0x2d, 0xce, 0x8c, 0xe5, 0x03, 0x94, 0x36, 0x7e, 0x80, 0x2a,
	0xc6, 0xcf, 0x62, 0x78, 0x2a, 0x4b, 0xc2, 0xd3, 0x20, 0x0b, 0xa8, 0xa4, 0xb3, 0x80, 0xec, 0xf1,
	0x9b, 0xcc, 0x1d, 0x3f, 0xe3, 0xf7, 0x1a, 0x18, 0x2c, 0x0b, 0x2a, 0x6e, 0xe5, 0x95, 0x4b, 0x5f,
	0xf3, 0xe6, 0xd5, 0x90, 0x14, 0xa3, 0xc8, 0x6b, 0xc1, 0x7b, 0x97, 0xc7, 0xf5, 0xde, 0xc6, 0x25,
	0x3c, 0xb8, 0x95, 0xa3, 0x11, 0xaf, 0xd2, 0x12, 0x24, 0x83, 0x7b, 0xd0, 0x3d, 0x58, 0x16, 0x26,
	0x77, 0x68, 0x77, 0xf1, 0x91, 0xed, 0x79, 0x6c, 0x12, 0xbb, 0x69, 0xbe, 0xe2, 0x85, 0xb5, 0xc2,
	0xc0, 0xf7, 0xb6, 0x44, 0xe3, 0x53, 0x76, 0xf3, 0x62, 0xc9, 0x78, 0x0a, 0xf7, 0x21, 0xa6, 0xf1,
	0x79, 0x92, 0xd6, 0x83, 0xf8, 0x5d, 0x4a, 0xbd, 0x84, 0x84, 0x3b, 0x9f, 0x02, 0x0c, 0x1a, 0xfa,
	0x68, 0x06, 0x2a, 0x87, 0xfb, 0x27, 0x0d, 0x7d, 0x02, 0xad, 0xc2, 0xf2, 0xc1, 0xcb, 0x93, 0x33,
	0xb3, 0x71, 0xd4, 0x38, 0x3d, 0x3f, 0xfe, 0xaa, 0x61, 0x1d, 0x1c, 0xed, 0x9f, 0x9e, 0x36, 0x5e,
	0xe8, 0xda, 0xce, 0x53, 0x58, 0x96, 0x36, 0x97, 0xd9, 0xea, 0xd3, 0xc0, 0xc7, 0xfa, 0x04, 0x9a,
	0x87, 0x6a, 0xc3, 0xa7, 0x38, 0x62, 0x99, 0xb3, 0xae, 0xa1, 0x39, 0x98, 0xf9, 0x65, 0xe0, 0x72,
	0x0e, 0xf4, 0xd2, 0xce, 0xb7, 0x50, 0x57, 0x77, 0x85, 0x11, 0x82, 0x85, 0xc1, 0x57, 0x8c, 0xee,
	0x2e, 0xcc, 0x0f, 0x60, 0x4d, 0xbb, 0xa5, 0x6b, 0x68, 0x05, 0xd0, 0x00, 0x94, 0xdc, 0xc2, 0xf4,
	0xd2, 0xce, 0x3f, 0x6b, 0xb0, 0x24, 0x2b, 0x4c, 0xa2, 0x59, 0x98, 0x3e, 0xf6, 0xdf, 0xda, 0x9e,
	0xdb, 0xd6, 0x27, 0x50, 0x0d, 0x96, 0x4e, 0x83, 0xa8, 0x6b, 0x7b, 0x83, 0xa9, 0xcc, 0x5f, 0xe8,
	0x1a, 0x5a, 0x84, 0x3b, 0x27, 0x41, 0x84, 0x0f, 0xec, 0xa8, 0x1d, 0xbf, 0xc5, 0xd1, 0x4b, 0x8c,
	0x7e, 0xa6, 0xf2, 0xa0, 0x97, 0xd1, 0x16, 0xac, 0x9f, 0xb8, 0xbe, 0xfb, 0x4d, 0xe0, 0xe3, 0xc6,
	0x75, 0x18, 0x10, 0x9c, 0xc3, 0x54, 0x41, 0xf7, 0x60, 0x31, 0x99, 0xc1, 0x30, 0x26, 0xd8, 0x26,
	0xd1, 0x1a, 0xdc, 0x4b, 0x06, 0x8e, 0x02, 0xba, 0x1f, 0x61, 0x9b, 0x09, 0x86, 0x0f, 0x4e, 0xed,
	0xfc, 0x0e, 0x50, 0xf1, 0x8e, 0xcf, 0xa8, 0x7d, 0xb9, 0x7f, 0xde, 0xb4, 0xce, 0x0e, 0xac, 0x83,
	0xfd, 0x66, 0xe3, 0xf0, 0xa5, 0xf9, 0x1b, 0xab, 0xf9, 0x9b, 0xb3, 0x86, 0x75, 0xfa, 0xd2, 0x3c,
	0xd9, 0x7f, 0xa1, 0x4f, 0xa0, 0x07, 0xb0, 0x29, 0x9f, 0x71, 0x72, 0x7c, 0x7a, 0x6c, 0x71, 0xa5,
	0x6a, 0x68, 0x13, 0xd6, 0x14, 0x93, 0x1a, 0x2f, 0x1a, 0x0d, 0xbd, 0xb4, 0xf3, 0xab, 0xf4, 0x35,
	0x93, 0xa5, 0xf4, 0x4c, 0x52, 0xcd, 0xfd, 0x67, 0xd6, 0xf1, 0xe9, 0x97, 0x2f, 0xad, 0xc6, 0xd7,
	0x4d, 0xeb, 0xf4, 0x25, 0x5f, 0xa1, 0x4f, 0x30, 0x64, 0x99, 0x91, 0x3e, 0xe6, 0x97, 0xa7, 0x5f,
	0x1e, 0x1f, 0xea, 0xda, 0xce, 0x5b, 0x98, 0xcf, 0xf8, 0x29, 0xb4, 0xcc, 0x73, 0xf4, 0x82, 0x76,
	0xf3, 0x60, 0x66, 0xff, 0xba, 0xc6, 0xc4, 0x94, 0x01, 0x1f, 0xf4, 0xdd, 0x95, 0x5e, 0x42, 0x75,
	0x58, 0xc9, 0x0c, 0xf6, 0x93, 0x15, 0xbd, 0xbc, 0xd3, 0x84, 0xa9, 0xb8, 0xc7, 0xb8, 0x00, 0x20,
	0x7e, 0xc5, 0x94, 0x74, 0x98, 0x13, 0xdf, 0xe2, 0x64, 0xeb, 0x1a, 0xa3, 0x2d, 0x20, 0xc7, 0x71,
	0x77, 0x81, 0x81, 0xb9, 0xc2, 0x05, 0xb8, 0x71, 0x1d, 0xba, 0x11, 0x6e, 0xeb, 0xe5, 0x9d, 0x08,
	0x66, 0x53, 0x3e, 0x87, 0xd9, 0x49, 0xea, 0x93, 0xfd, 0xd5, 0x27, 0x62, 0xae, 0x12, 0x20, 0xd3,
	0xea, 0x99, 0xed, 0x31, 0x53, 0xd2, 0x35, 0x74, 0x1f, 0xea, 0xa9, 0xb1, 0x23, 0xb7, 0x9d, 0x19,
	0x2f, 0x31, 0xbb, 0x4f, 0x1d, 0xd2, 0x73, 0xfb, 0x46, 0x2f, 0xef, 0xfd, 0xe7, 0x26, 0x2c, 0x1c,
	0xc4, 0xad, 0x29, 0xcf, 0xbe, 0x69, 0xda, 0x2d, 0xf4, 0x57, 0x1a, 0xac, 0x0f, 0x7b, 0xe0, 0x85,
	0x3e, 0x1d, 0xf7, 0x41, 0xd8, 0x9b, 0xfa, 0xde, 0xf8, 0x6f, 0xc8, 0x8c, 0x09, 0xd4, 0x83, 0x15,
	0xf9, 0x33, 0x2e, 0xf4, 0x91, 0x24, 0xe8, 0xab, 0xde, 0x95, 0xd5, 0x3f, 0x1e, 0x7d, 0x32, 0x27,
	0xeb, 0xf1, 0xf2, 0x4a, 0xfe, 0x29, 0x12, 0x92, 0x54, 0x21, 0xe4, 0xcf, 0xa7, 0xea, 0x3f, 0x1a,
	0x71, 0x26, 0xa7, 0x76, 0x0a, 0xd5, 0xfe, 0x45, 0x1a, 0xdd, 0x2f, 0xae, 0x4c, 0xbf, 0xf6, 0xaa,
	0x6f, 0x0e, 0x1d, 0x4f, 0xf0, 0xf5, 0x7b, 0x9b, 0x32, 0x7c, 0xe9, 0xf7, 0x51, 0x32, 0x7c, 0xd9,
	0x87, 0x51, 0x13, 0xe8, 0x08, 0xa6, 0x63, 0xe6, 0xd1, 0xba, 0x72, 0x5f, 0x0c, 0xd7, 0xc6, 0x90,
	0x51, 0x8e, 0x89, 0x19, 0xd6, 0xb0, 0xd7, 0x31, 0x32, 0xc3, 0xba, 0xe5, 0xc9, 0x8e, 0xcc, 0xb0,
	0x6e, 0x7d, 0x80, 0x33, 0x81, 0xfe, 0x46, 0x1b, 0xd4, 0xd6, 0x15, 0xbc, 0x7c, 0x36, 0xfe, 0x4b,
	0x92, 0x37, 0xf5, 0x27, 0xef, 0xf2, 0xfc, 0xc4, 0x98, 0x40, 0xa4, 0xff, 0x12, 0x2f, 0xf7, 0xda,
	0x40, 0x66, 0xe8, 0xca, 0xd7, 0x1a, 0x32, 0x43, 0x1f, 0xf2, 0x88, 0xe1, 0x06, 0x6a, 0xaa, 0x17,
	0x01, 0xe8, 0x91, 0x4c, 0xac, 0xca, 0xa7, 0x09, 0xf5, 0xdd, 0x71, 0xa6, 0x93, 0x10, 0x85, 0xb0,
	0x2c, 0x7d, 0x12, 0x80, 0x76, 0x24, 0x29, 0x92, 0xe2, 0x55, 0x42, 0xfd, 0xa3, 0x91, 0xe7, 0x8a,
	0xcd, 0xaa, 0x3a, 0xfe, 0xb2, 0xcd, 0x0e, 0x79, 0x93, 0x20, 0xdb, 0xec, 0xb0, 0xc7, 0x04, 0xe8,
	0x77, 0xbc, 0x8d, 0x21, 0xef, 0xea, 0xa3, 0x5d, 0xa9, 0xca, 0x94, 0x2f, 0x0f, 0xea, 0x8f, 0xc7,
	0x9a, 0x4f, 0x42, 0xf4, 0x5b, 0xee, 0xcc, 0x0a, 0x0d, 0x39, 0xb9, 0x33, 0x93, 0x34, 0x5a, 0x14,
	0xce, 0x4c, 0xda, 0x5e, 0x21, 0xec, 0x0e, 0x2a, 0xeb, 0x59, 0xca, 0xcc, 0x58, 0xd9, 0x36, 0x95,
	0x99, 0xb1, 0xba, 0x15, 0xca, 0x88, 0xca, 0xbb, 0x9b, 0x32, 0xa2, 0xca, 0xa6, 0xa9, 0x8c, 0xa8,
	0xba, 0x69, 0xca, 0x88, 0xca, 0xbb, 0xa0, 0x32, 0xa2, 0xca, 0xf6, 0xaa, 0x8c, 0xa8, 0xba, 0xb9,
	0xca, 0x0c, 0x49, 0xd9, 0x31, 0x44, 0xbb, 0x2a, 0x47, 0x28, 0xef, 0x9f, 0xc9, 0x0c, 0x69, 0x68,
	0x3b, 0x12, 0xfd, 0xa5, 0x06, 0x6b, 0x43, 0xba, 0x87, 0xe8, 0x13, 0xb5, 0xef, 0x53, 0xb0, 0xf0,
	0xe9, 0x98, 0x2b, 0xfa, 0xca, 0x96, 0x91, 0x97, 0x2b, 0x5b, 0x41, 0xf9, 0xe3, 0xd1, 0x27, 0x0b,
	0xa2, 0x8a, 0x3d, 0xcb, 0xbd, 0xf3, 0xe8, 0x44, 0x87, 0xec, 0xf4, 0x2d, 0x2f, 0xae, 0x48, 0xfb,
	0x89, 0x72, 0x44, 0x8a, 0xf6, 0x52, 0xfd, 0xd1, 0x18, 0xb3, 0x49, 0x88, 0xfe, 0x1c, 0xea, 0xea,
	0xde, 0x18, 0x52, 0x5a, 0x8d, 0x8a, 0xfa, 0x27, 0xe3, 0x2d, 0x10, 0x1b, 0x57, 0x34, 0xd3, 0x90,
	0x5c, 0x6d, 0x63, 0x6c, 0x7c, 0x48, 0x97, 0x8e, 0x45, 0x08, 0x55, 0xd3, 0x4d, 0x16, 0x21, 0x86,
	0x74, 0xf4, 0x64, 0x11, 0x62, 0x58, 0x3f, 0x0f, 0xd9, 0xa0, 0xe7, 0x1b, 0x3d, 0xe8, 0xa1, 0x54,
	0x6d, 0xf9, 0x9e, 0x5f, 0xfd, 0x83, 0x51, 0xa6, 0x09, 0x12, 0xf9, 0x76, 0x8d, 0x8c, 0x84, 0xa4,
	0x3f, 0x24, 0x23, 0x21, 0xeb, 0xfc, 0xa0, 0xaf, 0xf9, 0xa3, 0xfa, 0x14, 0x7e, 0x43, 0xaa, 0x80,
	0x2c, 0xf2, 0x07, 0xb7, 0xce, 0x21, 0x21, 0x4b, 0xc8, 0x25, 0x45, 0x48, 0x59, 0x0c, 0x93, 0x97,
	0x40, 0x65, 0x31, 0x4c, 0x55, 0xd5, 0x9c, 0x40, 0x01, 0x6f, 0xbb, 0x15, 0x2a, 0x7d, 0x48, 0x1e,
	0x08, 0x65, 0xe5, 0xcf, 0xfa, 0xce, 0xa8, 0x53, 0x39, 0x41, 0x87, 0xb7, 0xcc, 0xb2, 0x7b, 0x7b,
	0x28, 0x95, 0x4b, 0x61, 0x63, 0x1f, 0x8c, 0x32, 0x8d, 0x13, 0x89, 0x60, 0x59, 0x5a, 0x3c, 0x96,
	0xa5, 0x5c, 0xaa, 0xd2, 0xbb, 0x2c, 0xe5, 0x52, 0x57, 0xa4, 0x27, 0xd0, 0xef, 0x35, 0xd8, 0xbc,
	0xa5, 0x52, 0x86, 0x9e, 0xc8, 0x0d, 0x78, 0x78, 0xb9, 0xaf, 0xfe, 0xf9, 0x3b, 0xac, 0xe2, 0x2c,
	0xb9, 0x80, 0x8a, 0xa5, 0x33, 0xf4, 0xa1, 0x4a, 0x5f, 0xb9, 0xca, 0x5b, 0x7d, 0x7b, 0xb4, 0x89,
	0x9c, 0xd4, 0x5f, 0x68, 0xc9, 0x13, 0x2e, 0x59, 0x69, 0x4c, 0xee, 0x4a, 0x87, 0xd4, 0xde, 0xe4,
	0xae, 0x74, 0x58, 0xe5, 0xcd, 0x98, 0x78, 0xf6, 0xe4, 0x9b, 0xbd, 0x4e, 0xe0, 0xd9, 0x7e, 0x67,
	0xf7, 0xf3, 0x3d, 0x4a, 0x77, 0x9d, 0xa0, 0xfb, 0x98, 0xff, 0xe3, 0x9c, 0x13, 0x78, 0x8f, 0x09,
	0xbb, 0x8a, 0x38, 0x98, 0x14, 0xfe, 0xb7, 0xae, 0x35, 0xc5, 0xe7, 0x7c, 0xf6, 0x7f, 0x01, 0x00,
	0x00, 0xff, 0xff, 0xfb, 0xd3, 0xda, 0xfd, 0x96, 0x37, 0x00, 0x00,
}
