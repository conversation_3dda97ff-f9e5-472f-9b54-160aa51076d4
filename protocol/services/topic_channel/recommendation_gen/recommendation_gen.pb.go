// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/topic_channel/recommendation_gen.proto

package recommendation_gen // import "golang.52tt.com/protocol/services/topic_channel/recommendation_gen"

/*
buf:lint:ignore DIRECTORY_SAME_PACKAGE
*/

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import common "golang.52tt.com/protocol/services/rcmd/common"
import rcmd_channel_label "golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
import recommendation_common "golang.52tt.com/protocol/services/topic_channel/recommendation_common"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// IM页房间推荐分组
type IMChannelListABGroup int32

const (
	IMChannelListABGroup_DEFAULT             IMChannelListABGroup = 0
	IMChannelListABGroup_RegLess72Hour_Exp_A IMChannelListABGroup = 1
	IMChannelListABGroup_RegLess72Hour_Exp_B IMChannelListABGroup = 2
	IMChannelListABGroup_RegMore72Hour_Exp   IMChannelListABGroup = 3
)

var IMChannelListABGroup_name = map[int32]string{
	0: "DEFAULT",
	1: "RegLess72Hour_Exp_A",
	2: "RegLess72Hour_Exp_B",
	3: "RegMore72Hour_Exp",
}
var IMChannelListABGroup_value = map[string]int32{
	"DEFAULT":             0,
	"RegLess72Hour_Exp_A": 1,
	"RegLess72Hour_Exp_B": 2,
	"RegMore72Hour_Exp":   3,
}

func (x IMChannelListABGroup) String() string {
	return proto.EnumName(IMChannelListABGroup_name, int32(x))
}
func (IMChannelListABGroup) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{0}
}

// buf:lint:ignore ENUM_PASCAL_CASE
type REGULATORY_LEVEL int32

const (
	REGULATORY_LEVEL_FREE         REGULATORY_LEVEL = 0
	REGULATORY_LEVEL_SIMPLE_MINOR REGULATORY_LEVEL = 1
)

var REGULATORY_LEVEL_name = map[int32]string{
	0: "FREE",
	1: "SIMPLE_MINOR",
}
var REGULATORY_LEVEL_value = map[string]int32{
	"FREE":         0,
	"SIMPLE_MINOR": 1,
}

func (x REGULATORY_LEVEL) String() string {
	return proto.EnumName(REGULATORY_LEVEL_name, int32(x))
}
func (REGULATORY_LEVEL) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{1}
}

type PrefGameLabelType int32

const (
	PrefGameLabelType_SYSTEM PrefGameLabelType = 0
	PrefGameLabelType_CUSTOM PrefGameLabelType = 1
)

var PrefGameLabelType_name = map[int32]string{
	0: "SYSTEM",
	1: "CUSTOM",
}
var PrefGameLabelType_value = map[string]int32{
	"SYSTEM": 0,
	"CUSTOM": 1,
}

func (x PrefGameLabelType) String() string {
	return proto.EnumName(PrefGameLabelType_name, int32(x))
}
func (PrefGameLabelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{2}
}

type BusinessBlockEnum int32

const (
	BusinessBlockEnum_RoomMode      BusinessBlockEnum = 0
	BusinessBlockEnum_GameCondition BusinessBlockEnum = 1
)

var BusinessBlockEnum_name = map[int32]string{
	0: "RoomMode",
	1: "GameCondition",
}
var BusinessBlockEnum_value = map[string]int32{
	"RoomMode":      0,
	"GameCondition": 1,
}

func (x BusinessBlockEnum) String() string {
	return proto.EnumName(BusinessBlockEnum_name, int32(x))
}
func (BusinessBlockEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{3}
}

type BusinessElemEnum int32

const (
	BusinessElemEnum_NoLimit BusinessElemEnum = 0
	BusinessElemEnum_Single  BusinessElemEnum = 1
	BusinessElemEnum_Double  BusinessElemEnum = 2
	BusinessElemEnum_Waiting BusinessElemEnum = 3
	BusinessElemEnum_Started BusinessElemEnum = 4
)

var BusinessElemEnum_name = map[int32]string{
	0: "NoLimit",
	1: "Single",
	2: "Double",
	3: "Waiting",
	4: "Started",
}
var BusinessElemEnum_value = map[string]int32{
	"NoLimit": 0,
	"Single":  1,
	"Double":  2,
	"Waiting": 3,
	"Started": 4,
}

func (x BusinessElemEnum) String() string {
	return proto.EnumName(BusinessElemEnum_name, int32(x))
}
func (BusinessElemEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{4}
}

// 关注的人＞一起玩过＞同城标签
type RCMDLabel int32

const (
	RCMDLabel_None                          RCMDLabel = 0
	RCMDLabel_GangUpWithHomeOwner           RCMDLabel = 1
	RCMDLabel_ChatWithHomeOwner             RCMDLabel = 2
	RCMDLabel_FollowUserInChannel           RCMDLabel = 3
	RCMDLabel_LocShow                       RCMDLabel = 4
	RCMDLabel_MtFollowUserInChannel         RCMDLabel = 5
	RCMDLabel_MtEverEnterChannel            RCMDLabel = 6
	RCMDLabel_MtManyUsersInChannel          RCMDLabel = 7
	RCMDLabel_MtManyUsersFollowChannelOwner RCMDLabel = 8
	RCMDLabel_MtGuessYouLikeChannelOwner    RCMDLabel = 9
	RCMDLabel_MtRecentLikeTabChannelDouDi   RCMDLabel = 10
	RCMDLabel_MtHotGameHotChannelDouDi      RCMDLabel = 11
	RCMDLabel_MtFriendOfFriend              RCMDLabel = 12
	RCMDLabel_MtRecentFollow                RCMDLabel = 13
)

var RCMDLabel_name = map[int32]string{
	0:  "None",
	1:  "GangUpWithHomeOwner",
	2:  "ChatWithHomeOwner",
	3:  "FollowUserInChannel",
	4:  "LocShow",
	5:  "MtFollowUserInChannel",
	6:  "MtEverEnterChannel",
	7:  "MtManyUsersInChannel",
	8:  "MtManyUsersFollowChannelOwner",
	9:  "MtGuessYouLikeChannelOwner",
	10: "MtRecentLikeTabChannelDouDi",
	11: "MtHotGameHotChannelDouDi",
	12: "MtFriendOfFriend",
	13: "MtRecentFollow",
}
var RCMDLabel_value = map[string]int32{
	"None":                          0,
	"GangUpWithHomeOwner":           1,
	"ChatWithHomeOwner":             2,
	"FollowUserInChannel":           3,
	"LocShow":                       4,
	"MtFollowUserInChannel":         5,
	"MtEverEnterChannel":            6,
	"MtManyUsersInChannel":          7,
	"MtManyUsersFollowChannelOwner": 8,
	"MtGuessYouLikeChannelOwner":    9,
	"MtRecentLikeTabChannelDouDi":   10,
	"MtHotGameHotChannelDouDi":      11,
	"MtFriendOfFriend":              12,
	"MtRecentFollow":                13,
}

func (x RCMDLabel) String() string {
	return proto.EnumName(RCMDLabel_name, int32(x))
}
func (RCMDLabel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{5}
}

type RCMDReason int32

const (
	RCMDReason_Reason_None       RCMDReason = 0
	RCMDReason_Reason_HotGameTop RCMDReason = 1
)

var RCMDReason_name = map[int32]string{
	0: "Reason_None",
	1: "Reason_HotGameTop",
}
var RCMDReason_value = map[string]int32{
	"Reason_None":       0,
	"Reason_HotGameTop": 1,
}

func (x RCMDReason) String() string {
	return proto.EnumName(RCMDReason_name, int32(x))
}
func (RCMDReason) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{6}
}

type GetRCMDCfgReq_SOURCE int32

const (
	GetRCMDCfgReq_SOURCE_DEFAULT GetRCMDCfgReq_SOURCE = 0
	GetRCMDCfgReq_SOURCE_TT      GetRCMDCfgReq_SOURCE = 1
)

var GetRCMDCfgReq_SOURCE_name = map[int32]string{
	0: "SOURCE_DEFAULT",
	1: "SOURCE_TT",
}
var GetRCMDCfgReq_SOURCE_value = map[string]int32{
	"SOURCE_DEFAULT": 0,
	"SOURCE_TT":      1,
}

func (x GetRCMDCfgReq_SOURCE) String() string {
	return proto.EnumName(GetRCMDCfgReq_SOURCE_name, int32(x))
}
func (GetRCMDCfgReq_SOURCE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{2, 0}
}

type GetRecommendationListReq_GetListMode int32

const (
	GetRecommendationListReq_DEFAULT  GetRecommendationListReq_GetListMode = 0
	GetRecommendationListReq_NEXTPAGE GetRecommendationListReq_GetListMode = 1
	GetRecommendationListReq_REFRESH  GetRecommendationListReq_GetListMode = 2
	GetRecommendationListReq_INSERT   GetRecommendationListReq_GetListMode = 3
)

var GetRecommendationListReq_GetListMode_name = map[int32]string{
	0: "DEFAULT",
	1: "NEXTPAGE",
	2: "REFRESH",
	3: "INSERT",
}
var GetRecommendationListReq_GetListMode_value = map[string]int32{
	"DEFAULT":  0,
	"NEXTPAGE": 1,
	"REFRESH":  2,
	"INSERT":   3,
}

func (x GetRecommendationListReq_GetListMode) String() string {
	return proto.EnumName(GetRecommendationListReq_GetListMode_name, int32(x))
}
func (GetRecommendationListReq_GetListMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{8, 0}
}

type GetRecommendationListReq_Environment int32

const (
	GetRecommendationListReq_production GetRecommendationListReq_Environment = 0
	GetRecommendationListReq_staging    GetRecommendationListReq_Environment = 1
	GetRecommendationListReq_test       GetRecommendationListReq_Environment = 2
)

var GetRecommendationListReq_Environment_name = map[int32]string{
	0: "production",
	1: "staging",
	2: "test",
}
var GetRecommendationListReq_Environment_value = map[string]int32{
	"production": 0,
	"staging":    1,
	"test":       2,
}

func (x GetRecommendationListReq_Environment) String() string {
	return proto.EnumName(GetRecommendationListReq_Environment_name, int32(x))
}
func (GetRecommendationListReq_Environment) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{8, 1}
}

type GetHomeMixedListReq_GetListMode int32

const (
	GetHomeMixedListReq_DEFAULT  GetHomeMixedListReq_GetListMode = 0
	GetHomeMixedListReq_NEXTPAGE GetHomeMixedListReq_GetListMode = 1
	GetHomeMixedListReq_REFRESH  GetHomeMixedListReq_GetListMode = 2
	GetHomeMixedListReq_INSERT   GetHomeMixedListReq_GetListMode = 3
)

var GetHomeMixedListReq_GetListMode_name = map[int32]string{
	0: "DEFAULT",
	1: "NEXTPAGE",
	2: "REFRESH",
	3: "INSERT",
}
var GetHomeMixedListReq_GetListMode_value = map[string]int32{
	"DEFAULT":  0,
	"NEXTPAGE": 1,
	"REFRESH":  2,
	"INSERT":   3,
}

func (x GetHomeMixedListReq_GetListMode) String() string {
	return proto.EnumName(GetHomeMixedListReq_GetListMode_name, int32(x))
}
func (GetHomeMixedListReq_GetListMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{9, 0}
}

type GetHomeMixedListReq_Environment int32

const (
	GetHomeMixedListReq_production GetHomeMixedListReq_Environment = 0
	GetHomeMixedListReq_staging    GetHomeMixedListReq_Environment = 1
	GetHomeMixedListReq_test       GetHomeMixedListReq_Environment = 2
)

var GetHomeMixedListReq_Environment_name = map[int32]string{
	0: "production",
	1: "staging",
	2: "test",
}
var GetHomeMixedListReq_Environment_value = map[string]int32{
	"production": 0,
	"staging":    1,
	"test":       2,
}

func (x GetHomeMixedListReq_Environment) String() string {
	return proto.EnumName(GetHomeMixedListReq_Environment_name, int32(x))
}
func (GetHomeMixedListReq_Environment) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{9, 1}
}

type ChannelInfo_LocShowType int32

const (
	ChannelInfo_LocShowType_DEFAULT  ChannelInfo_LocShowType = 0
	ChannelInfo_LocShowType_PROVINCE ChannelInfo_LocShowType = 2
	ChannelInfo_LocShowType_CITY     ChannelInfo_LocShowType = 3
)

var ChannelInfo_LocShowType_name = map[int32]string{
	0: "LocShowType_DEFAULT",
	2: "LocShowType_PROVINCE",
	3: "LocShowType_CITY",
}
var ChannelInfo_LocShowType_value = map[string]int32{
	"LocShowType_DEFAULT":  0,
	"LocShowType_PROVINCE": 2,
	"LocShowType_CITY":     3,
}

func (x ChannelInfo_LocShowType) String() string {
	return proto.EnumName(ChannelInfo_LocShowType_name, int32(x))
}
func (ChannelInfo_LocShowType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{10, 0}
}

type GetRecommendationListResp_NotifyType int32

const (
	GetRecommendationListResp_DEFAULT     GetRecommendationListResp_NotifyType = 0
	GetRecommendationListResp_APPOINTMENT GetRecommendationListResp_NotifyType = 1
	GetRecommendationListResp_RefreshSucc GetRecommendationListResp_NotifyType = 2
)

var GetRecommendationListResp_NotifyType_name = map[int32]string{
	0: "DEFAULT",
	1: "APPOINTMENT",
	2: "RefreshSucc",
}
var GetRecommendationListResp_NotifyType_value = map[string]int32{
	"DEFAULT":     0,
	"APPOINTMENT": 1,
	"RefreshSucc": 2,
}

func (x GetRecommendationListResp_NotifyType) String() string {
	return proto.EnumName(GetRecommendationListResp_NotifyType_name, int32(x))
}
func (GetRecommendationListResp_NotifyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{11, 0}
}

type GetHomeMixedListResp_NotifyType int32

const (
	GetHomeMixedListResp_DEFAULT     GetHomeMixedListResp_NotifyType = 0
	GetHomeMixedListResp_APPOINTMENT GetHomeMixedListResp_NotifyType = 1
	GetHomeMixedListResp_RefreshSucc GetHomeMixedListResp_NotifyType = 2
)

var GetHomeMixedListResp_NotifyType_name = map[int32]string{
	0: "DEFAULT",
	1: "APPOINTMENT",
	2: "RefreshSucc",
}
var GetHomeMixedListResp_NotifyType_value = map[string]int32{
	"DEFAULT":     0,
	"APPOINTMENT": 1,
	"RefreshSucc": 2,
}

func (x GetHomeMixedListResp_NotifyType) String() string {
	return proto.EnumName(GetHomeMixedListResp_NotifyType_name, int32(x))
}
func (GetHomeMixedListResp_NotifyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{12, 0}
}

type BaseRCMDResp_PlanBStrategy int32

const (
	BaseRCMDResp_PlanBStrategy_INVALID BaseRCMDResp_PlanBStrategy = 0
	BaseRCMDResp_PlanBStrategy_TT      BaseRCMDResp_PlanBStrategy = 1
	BaseRCMDResp_PlanBStrategy_RANDOM  BaseRCMDResp_PlanBStrategy = 2
)

var BaseRCMDResp_PlanBStrategy_name = map[int32]string{
	0: "PlanBStrategy_INVALID",
	1: "PlanBStrategy_TT",
	2: "PlanBStrategy_RANDOM",
}
var BaseRCMDResp_PlanBStrategy_value = map[string]int32{
	"PlanBStrategy_INVALID": 0,
	"PlanBStrategy_TT":      1,
	"PlanBStrategy_RANDOM":  2,
}

func (x BaseRCMDResp_PlanBStrategy) String() string {
	return proto.EnumName(BaseRCMDResp_PlanBStrategy_name, int32(x))
}
func (BaseRCMDResp_PlanBStrategy) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{13, 0}
}

type SetTopicChannelQualityReq_TopicChannelQuality int32

const (
	SetTopicChannelQualityReq_DEFAULT SetTopicChannelQualityReq_TopicChannelQuality = 0
	SetTopicChannelQualityReq_LOW     SetTopicChannelQualityReq_TopicChannelQuality = 1
)

var SetTopicChannelQualityReq_TopicChannelQuality_name = map[int32]string{
	0: "DEFAULT",
	1: "LOW",
}
var SetTopicChannelQualityReq_TopicChannelQuality_value = map[string]int32{
	"DEFAULT": 0,
	"LOW":     1,
}

func (x SetTopicChannelQualityReq_TopicChannelQuality) String() string {
	return proto.EnumName(SetTopicChannelQualityReq_TopicChannelQuality_name, int32(x))
}
func (SetTopicChannelQualityReq_TopicChannelQuality) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{14, 0}
}

type NegativeFeedEvent_UpdateType int32

const (
	NegativeFeedEvent_Invalid     NegativeFeedEvent_UpdateType = 0
	NegativeFeedEvent_User        NegativeFeedEvent_UpdateType = 1
	NegativeFeedEvent_Tab         NegativeFeedEvent_UpdateType = 2
	NegativeFeedEvent_ChannelCond NegativeFeedEvent_UpdateType = 3
	NegativeFeedEvent_ChannelName NegativeFeedEvent_UpdateType = 4
)

var NegativeFeedEvent_UpdateType_name = map[int32]string{
	0: "Invalid",
	1: "User",
	2: "Tab",
	3: "ChannelCond",
	4: "ChannelName",
}
var NegativeFeedEvent_UpdateType_value = map[string]int32{
	"Invalid":     0,
	"User":        1,
	"Tab":         2,
	"ChannelCond": 3,
	"ChannelName": 4,
}

func (x NegativeFeedEvent_UpdateType) String() string {
	return proto.EnumName(NegativeFeedEvent_UpdateType_name, int32(x))
}
func (NegativeFeedEvent_UpdateType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{17, 0}
}

type ResetFilterReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResetFilterReq) Reset()         { *m = ResetFilterReq{} }
func (m *ResetFilterReq) String() string { return proto.CompactTextString(m) }
func (*ResetFilterReq) ProtoMessage()    {}
func (*ResetFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{0}
}
func (m *ResetFilterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResetFilterReq.Unmarshal(m, b)
}
func (m *ResetFilterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResetFilterReq.Marshal(b, m, deterministic)
}
func (dst *ResetFilterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResetFilterReq.Merge(dst, src)
}
func (m *ResetFilterReq) XXX_Size() int {
	return xxx_messageInfo_ResetFilterReq.Size(m)
}
func (m *ResetFilterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ResetFilterReq.DiscardUnknown(m)
}

var xxx_messageInfo_ResetFilterReq proto.InternalMessageInfo

func (m *ResetFilterReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ResetFilterResp struct {
	Code                 uint32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResetFilterResp) Reset()         { *m = ResetFilterResp{} }
func (m *ResetFilterResp) String() string { return proto.CompactTextString(m) }
func (*ResetFilterResp) ProtoMessage()    {}
func (*ResetFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{1}
}
func (m *ResetFilterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResetFilterResp.Unmarshal(m, b)
}
func (m *ResetFilterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResetFilterResp.Marshal(b, m, deterministic)
}
func (dst *ResetFilterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResetFilterResp.Merge(dst, src)
}
func (m *ResetFilterResp) XXX_Size() int {
	return xxx_messageInfo_ResetFilterResp.Size(m)
}
func (m *ResetFilterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ResetFilterResp.DiscardUnknown(m)
}

var xxx_messageInfo_ResetFilterResp proto.InternalMessageInfo

func (m *ResetFilterResp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ResetFilterResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type GetRCMDCfgReq struct {
	Source               GetRCMDCfgReq_SOURCE `protobuf:"varint,1,opt,name=source,proto3,enum=topic_channel.recommendation_gen.GetRCMDCfgReq_SOURCE" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetRCMDCfgReq) Reset()         { *m = GetRCMDCfgReq{} }
func (m *GetRCMDCfgReq) String() string { return proto.CompactTextString(m) }
func (*GetRCMDCfgReq) ProtoMessage()    {}
func (*GetRCMDCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{2}
}
func (m *GetRCMDCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRCMDCfgReq.Unmarshal(m, b)
}
func (m *GetRCMDCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRCMDCfgReq.Marshal(b, m, deterministic)
}
func (dst *GetRCMDCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRCMDCfgReq.Merge(dst, src)
}
func (m *GetRCMDCfgReq) XXX_Size() int {
	return xxx_messageInfo_GetRCMDCfgReq.Size(m)
}
func (m *GetRCMDCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRCMDCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRCMDCfgReq proto.InternalMessageInfo

func (m *GetRCMDCfgReq) GetSource() GetRCMDCfgReq_SOURCE {
	if m != nil {
		return m.Source
	}
	return GetRCMDCfgReq_SOURCE_DEFAULT
}

type GetRCMDCfgResp struct {
	SupervisorWhiteList  []uint32 `protobuf:"varint,1,rep,packed,name=supervisor_white_list,json=supervisorWhiteList,proto3" json:"supervisor_white_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRCMDCfgResp) Reset()         { *m = GetRCMDCfgResp{} }
func (m *GetRCMDCfgResp) String() string { return proto.CompactTextString(m) }
func (*GetRCMDCfgResp) ProtoMessage()    {}
func (*GetRCMDCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{3}
}
func (m *GetRCMDCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRCMDCfgResp.Unmarshal(m, b)
}
func (m *GetRCMDCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRCMDCfgResp.Marshal(b, m, deterministic)
}
func (dst *GetRCMDCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRCMDCfgResp.Merge(dst, src)
}
func (m *GetRCMDCfgResp) XXX_Size() int {
	return xxx_messageInfo_GetRCMDCfgResp.Size(m)
}
func (m *GetRCMDCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRCMDCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRCMDCfgResp proto.InternalMessageInfo

func (m *GetRCMDCfgResp) GetSupervisorWhiteList() []uint32 {
	if m != nil {
		return m.SupervisorWhiteList
	}
	return nil
}

type BlockOption struct {
	BlockId              uint32   `protobuf:"varint,1,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
	ElemId               uint32   `protobuf:"varint,2,opt,name=elem_id,json=elemId,proto3" json:"elem_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlockOption) Reset()         { *m = BlockOption{} }
func (m *BlockOption) String() string { return proto.CompactTextString(m) }
func (*BlockOption) ProtoMessage()    {}
func (*BlockOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{4}
}
func (m *BlockOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlockOption.Unmarshal(m, b)
}
func (m *BlockOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlockOption.Marshal(b, m, deterministic)
}
func (dst *BlockOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlockOption.Merge(dst, src)
}
func (m *BlockOption) XXX_Size() int {
	return xxx_messageInfo_BlockOption.Size(m)
}
func (m *BlockOption) XXX_DiscardUnknown() {
	xxx_messageInfo_BlockOption.DiscardUnknown(m)
}

var xxx_messageInfo_BlockOption proto.InternalMessageInfo

func (m *BlockOption) GetBlockId() uint32 {
	if m != nil {
		return m.BlockId
	}
	return 0
}

func (m *BlockOption) GetElemId() uint32 {
	if m != nil {
		return m.ElemId
	}
	return 0
}

type RecommendationReqReserve struct {
	ImListExpGroup       IMChannelListABGroup `protobuf:"varint,1,opt,name=im_list_exp_group,json=imListExpGroup,proto3,enum=topic_channel.recommendation_gen.IMChannelListABGroup" json:"im_list_exp_group,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *RecommendationReqReserve) Reset()         { *m = RecommendationReqReserve{} }
func (m *RecommendationReqReserve) String() string { return proto.CompactTextString(m) }
func (*RecommendationReqReserve) ProtoMessage()    {}
func (*RecommendationReqReserve) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{5}
}
func (m *RecommendationReqReserve) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendationReqReserve.Unmarshal(m, b)
}
func (m *RecommendationReqReserve) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendationReqReserve.Marshal(b, m, deterministic)
}
func (dst *RecommendationReqReserve) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendationReqReserve.Merge(dst, src)
}
func (m *RecommendationReqReserve) XXX_Size() int {
	return xxx_messageInfo_RecommendationReqReserve.Size(m)
}
func (m *RecommendationReqReserve) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendationReqReserve.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendationReqReserve proto.InternalMessageInfo

func (m *RecommendationReqReserve) GetImListExpGroup() IMChannelListABGroup {
	if m != nil {
		return m.ImListExpGroup
	}
	return IMChannelListABGroup_DEFAULT
}

type PrefGameLabel struct {
	Id                   uint32            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Val                  string            `protobuf:"bytes,2,opt,name=val,proto3" json:"val,omitempty"`
	Type                 PrefGameLabelType `protobuf:"varint,3,opt,name=type,proto3,enum=topic_channel.recommendation_gen.PrefGameLabelType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PrefGameLabel) Reset()         { *m = PrefGameLabel{} }
func (m *PrefGameLabel) String() string { return proto.CompactTextString(m) }
func (*PrefGameLabel) ProtoMessage()    {}
func (*PrefGameLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{6}
}
func (m *PrefGameLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PrefGameLabel.Unmarshal(m, b)
}
func (m *PrefGameLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PrefGameLabel.Marshal(b, m, deterministic)
}
func (dst *PrefGameLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrefGameLabel.Merge(dst, src)
}
func (m *PrefGameLabel) XXX_Size() int {
	return xxx_messageInfo_PrefGameLabel.Size(m)
}
func (m *PrefGameLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_PrefGameLabel.DiscardUnknown(m)
}

var xxx_messageInfo_PrefGameLabel proto.InternalMessageInfo

func (m *PrefGameLabel) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PrefGameLabel) GetVal() string {
	if m != nil {
		return m.Val
	}
	return ""
}

func (m *PrefGameLabel) GetType() PrefGameLabelType {
	if m != nil {
		return m.Type
	}
	return PrefGameLabelType_SYSTEM
}

type PrefGame struct {
	TabId                uint32           `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Labels               []*PrefGameLabel `protobuf:"bytes,2,rep,name=labels,proto3" json:"labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PrefGame) Reset()         { *m = PrefGame{} }
func (m *PrefGame) String() string { return proto.CompactTextString(m) }
func (*PrefGame) ProtoMessage()    {}
func (*PrefGame) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{7}
}
func (m *PrefGame) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PrefGame.Unmarshal(m, b)
}
func (m *PrefGame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PrefGame.Marshal(b, m, deterministic)
}
func (dst *PrefGame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrefGame.Merge(dst, src)
}
func (m *PrefGame) XXX_Size() int {
	return xxx_messageInfo_PrefGame.Size(m)
}
func (m *PrefGame) XXX_DiscardUnknown() {
	xxx_messageInfo_PrefGame.DiscardUnknown(m)
}

var xxx_messageInfo_PrefGame proto.InternalMessageInfo

func (m *PrefGame) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *PrefGame) GetLabels() []*PrefGameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

type GetRecommendationListReq struct {
	Uid                uint32                               `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit              uint32                               `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	TabId              uint32                               `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BlockOptions       []*BlockOption                       `protobuf:"bytes,4,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	GetMode            GetRecommendationListReq_GetListMode `protobuf:"varint,5,opt,name=get_mode,json=getMode,proto3,enum=topic_channel.recommendation_gen.GetRecommendationListReq_GetListMode" json:"get_mode,omitempty"`
	ChannelEnterSource uint32                               `protobuf:"varint,6,opt,name=channel_enter_source,json=channelEnterSource,proto3" json:"channel_enter_source,omitempty"`
	ClientType         uint32                               `protobuf:"varint,7,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientVersion      uint32                               `protobuf:"varint,8,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	MarketId           uint32                               `protobuf:"varint,9,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	// Deprecated
	Env              GetRecommendationListReq_Environment `protobuf:"varint,10,opt,name=env,proto3,enum=topic_channel.recommendation_gen.GetRecommendationListReq_Environment" json:"env,omitempty"`
	ChannelPackageId string                               `protobuf:"bytes,11,opt,name=channel_package_id,json=channelPackageId,proto3" json:"channel_package_id,omitempty"`
	// Deprecated
	TraceId string `protobuf:"bytes,12,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	// Deprecated
	ModelVersion string `protobuf:"bytes,13,opt,name=model_version,json=modelVersion,proto3" json:"model_version,omitempty"`
	// Deprecated
	DebugFlag uint32 `protobuf:"varint,14,opt,name=debug_flag,json=debugFlag,proto3" json:"debug_flag,omitempty"`
	// Deprecated
	Reserve             string                 `protobuf:"bytes,15,opt,name=reserve,proto3" json:"reserve,omitempty"`
	BaseReq             *common.RcmdBaseReq    `protobuf:"bytes,16,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Sex                 uint32                 `protobuf:"varint,17,opt,name=sex,proto3" json:"sex,omitempty"`
	BrowseList          *common.RcmdBrowseInfo `protobuf:"bytes,18,opt,name=browse_list,json=browseList,proto3" json:"browse_list,omitempty"`
	RegulatoryLevel     REGULATORY_LEVEL       `protobuf:"varint,19,opt,name=regulatory_level,json=regulatoryLevel,proto3,enum=topic_channel.recommendation_gen.REGULATORY_LEVEL" json:"regulatory_level,omitempty"`
	IsMinorityParentTab bool                   `protobuf:"varint,20,opt,name=is_minority_parent_tab,json=isMinorityParentTab,proto3" json:"is_minority_parent_tab,omitempty"`
	CategoryIds         []uint32               `protobuf:"varint,22,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	TabIds              []uint32               `protobuf:"varint,23,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	// Deprecated
	IsQmFramework bool `protobuf:"varint,24,opt,name=is_qm_framework,json=isQmFramework,proto3" json:"is_qm_framework,omitempty"`
	// Deprecated: 功能已下线
	PrefGames      []*PrefGame                                `protobuf:"bytes,25,rep,name=pref_games,json=prefGames,proto3" json:"pref_games,omitempty"`
	Labels         []*rcmd_channel_label.GameLabel            `protobuf:"bytes,26,rep,name=labels,proto3" json:"labels,omitempty"`
	BusinessFilter []*GetRecommendationListReq_BusinessFilter `protobuf:"bytes,27,rep,name=business_filter,json=businessFilter,proto3" json:"business_filter,omitempty"`
	GameSettings   []*recommendation_common.TcGameSetting     `protobuf:"bytes,28,rep,name=game_settings,json=gameSettings,proto3" json:"game_settings,omitempty"`
	// Deprecated
	IsFallbackReq  bool     `protobuf:"varint,29,opt,name=is_fallback_req,json=isFallbackReq,proto3" json:"is_fallback_req,omitempty"`
	InterestLabels []string `protobuf:"bytes,30,rep,name=interest_labels,json=interestLabels,proto3" json:"interest_labels,omitempty"`
	DeliveryType   string   `protobuf:"bytes,31,opt,name=delivery_type,json=deliveryType,proto3" json:"delivery_type,omitempty"`
	// Deprecated
	IsEnableGameLabel    bool                                    `protobuf:"varint,32,opt,name=is_enable_game_label,json=isEnableGameLabel,proto3" json:"is_enable_game_label,omitempty"`
	ShieldFilterWords    []string                                `protobuf:"bytes,33,rep,name=shield_filter_words,json=shieldFilterWords,proto3" json:"shield_filter_words,omitempty"`
	ClassifyLabels       []*rcmd_channel_label.ClassifyLabelList `protobuf:"bytes,34,rep,name=classify_labels,json=classifyLabels,proto3" json:"classify_labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                `json:"-"`
	XXX_unrecognized     []byte                                  `json:"-"`
	XXX_sizecache        int32                                   `json:"-"`
}

func (m *GetRecommendationListReq) Reset()         { *m = GetRecommendationListReq{} }
func (m *GetRecommendationListReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendationListReq) ProtoMessage()    {}
func (*GetRecommendationListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{8}
}
func (m *GetRecommendationListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendationListReq.Unmarshal(m, b)
}
func (m *GetRecommendationListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendationListReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendationListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendationListReq.Merge(dst, src)
}
func (m *GetRecommendationListReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendationListReq.Size(m)
}
func (m *GetRecommendationListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendationListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendationListReq proto.InternalMessageInfo

func (m *GetRecommendationListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRecommendationListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetRecommendationListReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetRecommendationListReq) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *GetRecommendationListReq) GetGetMode() GetRecommendationListReq_GetListMode {
	if m != nil {
		return m.GetMode
	}
	return GetRecommendationListReq_DEFAULT
}

func (m *GetRecommendationListReq) GetChannelEnterSource() uint32 {
	if m != nil {
		return m.ChannelEnterSource
	}
	return 0
}

func (m *GetRecommendationListReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *GetRecommendationListReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *GetRecommendationListReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetRecommendationListReq) GetEnv() GetRecommendationListReq_Environment {
	if m != nil {
		return m.Env
	}
	return GetRecommendationListReq_production
}

func (m *GetRecommendationListReq) GetChannelPackageId() string {
	if m != nil {
		return m.ChannelPackageId
	}
	return ""
}

func (m *GetRecommendationListReq) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

func (m *GetRecommendationListReq) GetModelVersion() string {
	if m != nil {
		return m.ModelVersion
	}
	return ""
}

func (m *GetRecommendationListReq) GetDebugFlag() uint32 {
	if m != nil {
		return m.DebugFlag
	}
	return 0
}

func (m *GetRecommendationListReq) GetReserve() string {
	if m != nil {
		return m.Reserve
	}
	return ""
}

func (m *GetRecommendationListReq) GetBaseReq() *common.RcmdBaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetRecommendationListReq) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GetRecommendationListReq) GetBrowseList() *common.RcmdBrowseInfo {
	if m != nil {
		return m.BrowseList
	}
	return nil
}

func (m *GetRecommendationListReq) GetRegulatoryLevel() REGULATORY_LEVEL {
	if m != nil {
		return m.RegulatoryLevel
	}
	return REGULATORY_LEVEL_FREE
}

func (m *GetRecommendationListReq) GetIsMinorityParentTab() bool {
	if m != nil {
		return m.IsMinorityParentTab
	}
	return false
}

func (m *GetRecommendationListReq) GetCategoryIds() []uint32 {
	if m != nil {
		return m.CategoryIds
	}
	return nil
}

func (m *GetRecommendationListReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *GetRecommendationListReq) GetIsQmFramework() bool {
	if m != nil {
		return m.IsQmFramework
	}
	return false
}

func (m *GetRecommendationListReq) GetPrefGames() []*PrefGame {
	if m != nil {
		return m.PrefGames
	}
	return nil
}

func (m *GetRecommendationListReq) GetLabels() []*rcmd_channel_label.GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *GetRecommendationListReq) GetBusinessFilter() []*GetRecommendationListReq_BusinessFilter {
	if m != nil {
		return m.BusinessFilter
	}
	return nil
}

func (m *GetRecommendationListReq) GetGameSettings() []*recommendation_common.TcGameSetting {
	if m != nil {
		return m.GameSettings
	}
	return nil
}

func (m *GetRecommendationListReq) GetIsFallbackReq() bool {
	if m != nil {
		return m.IsFallbackReq
	}
	return false
}

func (m *GetRecommendationListReq) GetInterestLabels() []string {
	if m != nil {
		return m.InterestLabels
	}
	return nil
}

func (m *GetRecommendationListReq) GetDeliveryType() string {
	if m != nil {
		return m.DeliveryType
	}
	return ""
}

func (m *GetRecommendationListReq) GetIsEnableGameLabel() bool {
	if m != nil {
		return m.IsEnableGameLabel
	}
	return false
}

func (m *GetRecommendationListReq) GetShieldFilterWords() []string {
	if m != nil {
		return m.ShieldFilterWords
	}
	return nil
}

func (m *GetRecommendationListReq) GetClassifyLabels() []*rcmd_channel_label.ClassifyLabelList {
	if m != nil {
		return m.ClassifyLabels
	}
	return nil
}

// 业务筛选字段
type GetRecommendationListReq_BusinessFilter struct {
	// 业务block
	BlockType BusinessBlockEnum `protobuf:"varint,1,opt,name=block_type,json=blockType,proto3,enum=topic_channel.recommendation_gen.BusinessBlockEnum" json:"block_type,omitempty"`
	// 业务elem
	ElemType             []BusinessElemEnum `protobuf:"varint,2,rep,packed,name=elem_type,json=elemType,proto3,enum=topic_channel.recommendation_gen.BusinessElemEnum" json:"elem_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetRecommendationListReq_BusinessFilter) Reset() {
	*m = GetRecommendationListReq_BusinessFilter{}
}
func (m *GetRecommendationListReq_BusinessFilter) String() string { return proto.CompactTextString(m) }
func (*GetRecommendationListReq_BusinessFilter) ProtoMessage()    {}
func (*GetRecommendationListReq_BusinessFilter) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{8, 0}
}
func (m *GetRecommendationListReq_BusinessFilter) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendationListReq_BusinessFilter.Unmarshal(m, b)
}
func (m *GetRecommendationListReq_BusinessFilter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendationListReq_BusinessFilter.Marshal(b, m, deterministic)
}
func (dst *GetRecommendationListReq_BusinessFilter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendationListReq_BusinessFilter.Merge(dst, src)
}
func (m *GetRecommendationListReq_BusinessFilter) XXX_Size() int {
	return xxx_messageInfo_GetRecommendationListReq_BusinessFilter.Size(m)
}
func (m *GetRecommendationListReq_BusinessFilter) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendationListReq_BusinessFilter.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendationListReq_BusinessFilter proto.InternalMessageInfo

func (m *GetRecommendationListReq_BusinessFilter) GetBlockType() BusinessBlockEnum {
	if m != nil {
		return m.BlockType
	}
	return BusinessBlockEnum_RoomMode
}

func (m *GetRecommendationListReq_BusinessFilter) GetElemType() []BusinessElemEnum {
	if m != nil {
		return m.ElemType
	}
	return nil
}

type GetHomeMixedListReq struct {
	Uid                  uint32                                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit                uint32                                 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	TabId                uint32                                 `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BlockOptions         []*BlockOption                         `protobuf:"bytes,4,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	GetMode              GetHomeMixedListReq_GetListMode        `protobuf:"varint,5,opt,name=get_mode,json=getMode,proto3,enum=topic_channel.recommendation_gen.GetHomeMixedListReq_GetListMode" json:"get_mode,omitempty"`
	ChannelEnterSource   uint32                                 `protobuf:"varint,6,opt,name=channel_enter_source,json=channelEnterSource,proto3" json:"channel_enter_source,omitempty"`
	ClientType           uint32                                 `protobuf:"varint,7,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientVersion        uint32                                 `protobuf:"varint,8,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	MarketId             uint32                                 `protobuf:"varint,9,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	Env                  GetHomeMixedListReq_Environment        `protobuf:"varint,10,opt,name=env,proto3,enum=topic_channel.recommendation_gen.GetHomeMixedListReq_Environment" json:"env,omitempty"`
	ChannelPackageId     string                                 `protobuf:"bytes,11,opt,name=channel_package_id,json=channelPackageId,proto3" json:"channel_package_id,omitempty"`
	TraceId              string                                 `protobuf:"bytes,12,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	ModelVersion         string                                 `protobuf:"bytes,13,opt,name=model_version,json=modelVersion,proto3" json:"model_version,omitempty"`
	DebugFlag            uint32                                 `protobuf:"varint,14,opt,name=debug_flag,json=debugFlag,proto3" json:"debug_flag,omitempty"`
	Reserve              string                                 `protobuf:"bytes,15,opt,name=reserve,proto3" json:"reserve,omitempty"`
	BaseReq              *common.RcmdBaseReq                    `protobuf:"bytes,16,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Sex                  uint32                                 `protobuf:"varint,17,opt,name=sex,proto3" json:"sex,omitempty"`
	BrowseList           *common.RcmdBrowseInfo                 `protobuf:"bytes,18,opt,name=browse_list,json=browseList,proto3" json:"browse_list,omitempty"`
	RegulatoryLevel      REGULATORY_LEVEL                       `protobuf:"varint,19,opt,name=regulatory_level,json=regulatoryLevel,proto3,enum=topic_channel.recommendation_gen.REGULATORY_LEVEL" json:"regulatory_level,omitempty"`
	IsMinorityParentTab  bool                                   `protobuf:"varint,20,opt,name=is_minority_parent_tab,json=isMinorityParentTab,proto3" json:"is_minority_parent_tab,omitempty"`
	CategoryIds          []uint32                               `protobuf:"varint,22,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	TabIds               []uint32                               `protobuf:"varint,23,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	IsQmFramework        bool                                   `protobuf:"varint,24,opt,name=is_qm_framework,json=isQmFramework,proto3" json:"is_qm_framework,omitempty"`
	PrefGames            []*PrefGame                            `protobuf:"bytes,25,rep,name=pref_games,json=prefGames,proto3" json:"pref_games,omitempty"`
	Labels               []*rcmd_channel_label.GameLabel        `protobuf:"bytes,26,rep,name=labels,proto3" json:"labels,omitempty"`
	BusinessFilter       []*GetHomeMixedListReq_BusinessFilter  `protobuf:"bytes,27,rep,name=business_filter,json=businessFilter,proto3" json:"business_filter,omitempty"`
	GameSettings         []*recommendation_common.TcGameSetting `protobuf:"bytes,28,rep,name=game_settings,json=gameSettings,proto3" json:"game_settings,omitempty"`
	IsFallbackReq        bool                                   `protobuf:"varint,29,opt,name=is_fallback_req,json=isFallbackReq,proto3" json:"is_fallback_req,omitempty"`
	InterestLabels       []string                               `protobuf:"bytes,30,rep,name=interest_labels,json=interestLabels,proto3" json:"interest_labels,omitempty"`
	DeliveryType         string                                 `protobuf:"bytes,31,opt,name=delivery_type,json=deliveryType,proto3" json:"delivery_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *GetHomeMixedListReq) Reset()         { *m = GetHomeMixedListReq{} }
func (m *GetHomeMixedListReq) String() string { return proto.CompactTextString(m) }
func (*GetHomeMixedListReq) ProtoMessage()    {}
func (*GetHomeMixedListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{9}
}
func (m *GetHomeMixedListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHomeMixedListReq.Unmarshal(m, b)
}
func (m *GetHomeMixedListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHomeMixedListReq.Marshal(b, m, deterministic)
}
func (dst *GetHomeMixedListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHomeMixedListReq.Merge(dst, src)
}
func (m *GetHomeMixedListReq) XXX_Size() int {
	return xxx_messageInfo_GetHomeMixedListReq.Size(m)
}
func (m *GetHomeMixedListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHomeMixedListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetHomeMixedListReq proto.InternalMessageInfo

func (m *GetHomeMixedListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetHomeMixedListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetHomeMixedListReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetHomeMixedListReq) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *GetHomeMixedListReq) GetGetMode() GetHomeMixedListReq_GetListMode {
	if m != nil {
		return m.GetMode
	}
	return GetHomeMixedListReq_DEFAULT
}

func (m *GetHomeMixedListReq) GetChannelEnterSource() uint32 {
	if m != nil {
		return m.ChannelEnterSource
	}
	return 0
}

func (m *GetHomeMixedListReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *GetHomeMixedListReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *GetHomeMixedListReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetHomeMixedListReq) GetEnv() GetHomeMixedListReq_Environment {
	if m != nil {
		return m.Env
	}
	return GetHomeMixedListReq_production
}

func (m *GetHomeMixedListReq) GetChannelPackageId() string {
	if m != nil {
		return m.ChannelPackageId
	}
	return ""
}

func (m *GetHomeMixedListReq) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

func (m *GetHomeMixedListReq) GetModelVersion() string {
	if m != nil {
		return m.ModelVersion
	}
	return ""
}

func (m *GetHomeMixedListReq) GetDebugFlag() uint32 {
	if m != nil {
		return m.DebugFlag
	}
	return 0
}

func (m *GetHomeMixedListReq) GetReserve() string {
	if m != nil {
		return m.Reserve
	}
	return ""
}

func (m *GetHomeMixedListReq) GetBaseReq() *common.RcmdBaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetHomeMixedListReq) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GetHomeMixedListReq) GetBrowseList() *common.RcmdBrowseInfo {
	if m != nil {
		return m.BrowseList
	}
	return nil
}

func (m *GetHomeMixedListReq) GetRegulatoryLevel() REGULATORY_LEVEL {
	if m != nil {
		return m.RegulatoryLevel
	}
	return REGULATORY_LEVEL_FREE
}

func (m *GetHomeMixedListReq) GetIsMinorityParentTab() bool {
	if m != nil {
		return m.IsMinorityParentTab
	}
	return false
}

func (m *GetHomeMixedListReq) GetCategoryIds() []uint32 {
	if m != nil {
		return m.CategoryIds
	}
	return nil
}

func (m *GetHomeMixedListReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *GetHomeMixedListReq) GetIsQmFramework() bool {
	if m != nil {
		return m.IsQmFramework
	}
	return false
}

func (m *GetHomeMixedListReq) GetPrefGames() []*PrefGame {
	if m != nil {
		return m.PrefGames
	}
	return nil
}

func (m *GetHomeMixedListReq) GetLabels() []*rcmd_channel_label.GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *GetHomeMixedListReq) GetBusinessFilter() []*GetHomeMixedListReq_BusinessFilter {
	if m != nil {
		return m.BusinessFilter
	}
	return nil
}

func (m *GetHomeMixedListReq) GetGameSettings() []*recommendation_common.TcGameSetting {
	if m != nil {
		return m.GameSettings
	}
	return nil
}

func (m *GetHomeMixedListReq) GetIsFallbackReq() bool {
	if m != nil {
		return m.IsFallbackReq
	}
	return false
}

func (m *GetHomeMixedListReq) GetInterestLabels() []string {
	if m != nil {
		return m.InterestLabels
	}
	return nil
}

func (m *GetHomeMixedListReq) GetDeliveryType() string {
	if m != nil {
		return m.DeliveryType
	}
	return ""
}

// 业务筛选字段
type GetHomeMixedListReq_BusinessFilter struct {
	// 业务block
	BlockType BusinessBlockEnum `protobuf:"varint,1,opt,name=block_type,json=blockType,proto3,enum=topic_channel.recommendation_gen.BusinessBlockEnum" json:"block_type,omitempty"`
	// 业务elem
	ElemType             []BusinessElemEnum `protobuf:"varint,2,rep,packed,name=elem_type,json=elemType,proto3,enum=topic_channel.recommendation_gen.BusinessElemEnum" json:"elem_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetHomeMixedListReq_BusinessFilter) Reset()         { *m = GetHomeMixedListReq_BusinessFilter{} }
func (m *GetHomeMixedListReq_BusinessFilter) String() string { return proto.CompactTextString(m) }
func (*GetHomeMixedListReq_BusinessFilter) ProtoMessage()    {}
func (*GetHomeMixedListReq_BusinessFilter) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{9, 0}
}
func (m *GetHomeMixedListReq_BusinessFilter) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHomeMixedListReq_BusinessFilter.Unmarshal(m, b)
}
func (m *GetHomeMixedListReq_BusinessFilter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHomeMixedListReq_BusinessFilter.Marshal(b, m, deterministic)
}
func (dst *GetHomeMixedListReq_BusinessFilter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHomeMixedListReq_BusinessFilter.Merge(dst, src)
}
func (m *GetHomeMixedListReq_BusinessFilter) XXX_Size() int {
	return xxx_messageInfo_GetHomeMixedListReq_BusinessFilter.Size(m)
}
func (m *GetHomeMixedListReq_BusinessFilter) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHomeMixedListReq_BusinessFilter.DiscardUnknown(m)
}

var xxx_messageInfo_GetHomeMixedListReq_BusinessFilter proto.InternalMessageInfo

func (m *GetHomeMixedListReq_BusinessFilter) GetBlockType() BusinessBlockEnum {
	if m != nil {
		return m.BlockType
	}
	return BusinessBlockEnum_RoomMode
}

func (m *GetHomeMixedListReq_BusinessFilter) GetElemType() []BusinessElemEnum {
	if m != nil {
		return m.ElemType
	}
	return nil
}

type ChannelInfo struct {
	TagId       uint32                  `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	RecallFlag  uint32                  `protobuf:"varint,2,opt,name=recall_flag,json=recallFlag,proto3" json:"recall_flag,omitempty"`
	Loc         *common.LocationInfo    `protobuf:"bytes,3,opt,name=loc,proto3" json:"loc,omitempty"`
	RcmdLabels  []RCMDLabel             `protobuf:"varint,4,rep,packed,name=rcmd_labels,json=rcmdLabels,proto3,enum=topic_channel.recommendation_gen.RCMDLabel" json:"rcmd_labels,omitempty"`
	LocShowType ChannelInfo_LocShowType `protobuf:"varint,5,opt,name=loc_show_type,json=locShowType,proto3,enum=topic_channel.recommendation_gen.ChannelInfo_LocShowType" json:"loc_show_type,omitempty"`
	// Deprecated: 萌新承接房逻辑已下线
	IsNewUserUndertake   bool     `protobuf:"varint,6,opt,name=is_new_user_undertake,json=isNewUserUndertake,proto3" json:"is_new_user_undertake,omitempty"`
	FollowUidList        []uint32 `protobuf:"varint,7,rep,packed,name=follow_uid_list,json=followUidList,proto3" json:"follow_uid_list,omitempty"`
	PlayUidList          []uint32 `protobuf:"varint,8,rep,packed,name=play_uid_list,json=playUidList,proto3" json:"play_uid_list,omitempty"`
	FollowUidListV2      []uint32 `protobuf:"varint,9,rep,packed,name=follow_uid_list_v2,json=followUidListV2,proto3" json:"follow_uid_list_v2,omitempty"`
	IsEverEnter          bool     `protobuf:"varint,10,opt,name=is_ever_enter,json=isEverEnter,proto3" json:"is_ever_enter,omitempty"`
	DeliveryType         string   `protobuf:"bytes,11,opt,name=delivery_type,json=deliveryType,proto3" json:"delivery_type,omitempty"`
	DisplayContent       string   `protobuf:"bytes,12,opt,name=display_content,json=displayContent,proto3" json:"display_content,omitempty"`
	DisplayUidList       []uint32 `protobuf:"varint,13,rep,packed,name=display_uid_list,json=displayUidList,proto3" json:"display_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelInfo) Reset()         { *m = ChannelInfo{} }
func (m *ChannelInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelInfo) ProtoMessage()    {}
func (*ChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{10}
}
func (m *ChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelInfo.Unmarshal(m, b)
}
func (m *ChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelInfo.Merge(dst, src)
}
func (m *ChannelInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelInfo.Size(m)
}
func (m *ChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelInfo proto.InternalMessageInfo

func (m *ChannelInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *ChannelInfo) GetRecallFlag() uint32 {
	if m != nil {
		return m.RecallFlag
	}
	return 0
}

func (m *ChannelInfo) GetLoc() *common.LocationInfo {
	if m != nil {
		return m.Loc
	}
	return nil
}

func (m *ChannelInfo) GetRcmdLabels() []RCMDLabel {
	if m != nil {
		return m.RcmdLabels
	}
	return nil
}

func (m *ChannelInfo) GetLocShowType() ChannelInfo_LocShowType {
	if m != nil {
		return m.LocShowType
	}
	return ChannelInfo_LocShowType_DEFAULT
}

func (m *ChannelInfo) GetIsNewUserUndertake() bool {
	if m != nil {
		return m.IsNewUserUndertake
	}
	return false
}

func (m *ChannelInfo) GetFollowUidList() []uint32 {
	if m != nil {
		return m.FollowUidList
	}
	return nil
}

func (m *ChannelInfo) GetPlayUidList() []uint32 {
	if m != nil {
		return m.PlayUidList
	}
	return nil
}

func (m *ChannelInfo) GetFollowUidListV2() []uint32 {
	if m != nil {
		return m.FollowUidListV2
	}
	return nil
}

func (m *ChannelInfo) GetIsEverEnter() bool {
	if m != nil {
		return m.IsEverEnter
	}
	return false
}

func (m *ChannelInfo) GetDeliveryType() string {
	if m != nil {
		return m.DeliveryType
	}
	return ""
}

func (m *ChannelInfo) GetDisplayContent() string {
	if m != nil {
		return m.DisplayContent
	}
	return ""
}

func (m *ChannelInfo) GetDisplayUidList() []uint32 {
	if m != nil {
		return m.DisplayUidList
	}
	return nil
}

type GetRecommendationListResp struct {
	ChannelId            []uint32                               `protobuf:"varint,1,rep,packed,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BottomReached        bool                                   `protobuf:"varint,2,opt,name=bottom_reached,json=bottomReached,proto3" json:"bottom_reached,omitempty"`
	ChannelInfoMap       map[uint32]*ChannelInfo                `protobuf:"bytes,3,rep,name=channel_info_map,json=channelInfoMap,proto3" json:"channel_info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	TraceId              string                                 `protobuf:"bytes,4,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	DebugInfoMap         map[string]string                      `protobuf:"bytes,5,rep,name=debug_info_map,json=debugInfoMap,proto3" json:"debug_info_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	SelfLoc              *common.LocationInfo                   `protobuf:"bytes,6,opt,name=self_loc,json=selfLoc,proto3" json:"self_loc,omitempty"`
	PrefGames            []*PrefGame                            `protobuf:"bytes,7,rep,name=pref_games,json=prefGames,proto3" json:"pref_games,omitempty"`
	InsertPos            uint32                                 `protobuf:"varint,8,opt,name=insert_pos,json=insertPos,proto3" json:"insert_pos,omitempty"`
	NotifyList           []GetRecommendationListResp_NotifyType `protobuf:"varint,9,rep,packed,name=notify_list,json=notifyList,proto3,enum=topic_channel.recommendation_gen.GetRecommendationListResp_NotifyType" json:"notify_list,omitempty"`
	Footprint            string                                 `protobuf:"bytes,10,opt,name=footprint,proto3" json:"footprint,omitempty"`
	BaseResp             *BaseRCMDResp                          `protobuf:"bytes,11,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CommonChannelInfoMap map[uint32]*common.ChannelInfo         `protobuf:"bytes,12,rep,name=common_channel_info_map,json=commonChannelInfoMap,proto3" json:"common_channel_info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Labels               []*rcmd_channel_label.GameLabel        `protobuf:"bytes,13,rep,name=labels,proto3" json:"labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *GetRecommendationListResp) Reset()         { *m = GetRecommendationListResp{} }
func (m *GetRecommendationListResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendationListResp) ProtoMessage()    {}
func (*GetRecommendationListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{11}
}
func (m *GetRecommendationListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendationListResp.Unmarshal(m, b)
}
func (m *GetRecommendationListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendationListResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendationListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendationListResp.Merge(dst, src)
}
func (m *GetRecommendationListResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendationListResp.Size(m)
}
func (m *GetRecommendationListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendationListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendationListResp proto.InternalMessageInfo

func (m *GetRecommendationListResp) GetChannelId() []uint32 {
	if m != nil {
		return m.ChannelId
	}
	return nil
}

func (m *GetRecommendationListResp) GetBottomReached() bool {
	if m != nil {
		return m.BottomReached
	}
	return false
}

func (m *GetRecommendationListResp) GetChannelInfoMap() map[uint32]*ChannelInfo {
	if m != nil {
		return m.ChannelInfoMap
	}
	return nil
}

func (m *GetRecommendationListResp) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

func (m *GetRecommendationListResp) GetDebugInfoMap() map[string]string {
	if m != nil {
		return m.DebugInfoMap
	}
	return nil
}

func (m *GetRecommendationListResp) GetSelfLoc() *common.LocationInfo {
	if m != nil {
		return m.SelfLoc
	}
	return nil
}

func (m *GetRecommendationListResp) GetPrefGames() []*PrefGame {
	if m != nil {
		return m.PrefGames
	}
	return nil
}

func (m *GetRecommendationListResp) GetInsertPos() uint32 {
	if m != nil {
		return m.InsertPos
	}
	return 0
}

func (m *GetRecommendationListResp) GetNotifyList() []GetRecommendationListResp_NotifyType {
	if m != nil {
		return m.NotifyList
	}
	return nil
}

func (m *GetRecommendationListResp) GetFootprint() string {
	if m != nil {
		return m.Footprint
	}
	return ""
}

func (m *GetRecommendationListResp) GetBaseResp() *BaseRCMDResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRecommendationListResp) GetCommonChannelInfoMap() map[uint32]*common.ChannelInfo {
	if m != nil {
		return m.CommonChannelInfoMap
	}
	return nil
}

func (m *GetRecommendationListResp) GetLabels() []*rcmd_channel_label.GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

type GetHomeMixedListResp struct {
	ChannelId            []uint32                          `protobuf:"varint,1,rep,packed,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BottomReached        bool                              `protobuf:"varint,2,opt,name=bottom_reached,json=bottomReached,proto3" json:"bottom_reached,omitempty"`
	ChannelInfoMap       map[uint32]*ChannelInfo           `protobuf:"bytes,3,rep,name=channel_info_map,json=channelInfoMap,proto3" json:"channel_info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	TraceId              string                            `protobuf:"bytes,4,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	DebugInfoMap         map[string]string                 `protobuf:"bytes,5,rep,name=debug_info_map,json=debugInfoMap,proto3" json:"debug_info_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	SelfLoc              *common.LocationInfo              `protobuf:"bytes,6,opt,name=self_loc,json=selfLoc,proto3" json:"self_loc,omitempty"`
	PrefGames            []*PrefGame                       `protobuf:"bytes,7,rep,name=pref_games,json=prefGames,proto3" json:"pref_games,omitempty"`
	InsertPos            uint32                            `protobuf:"varint,8,opt,name=insert_pos,json=insertPos,proto3" json:"insert_pos,omitempty"`
	NotifyList           []GetHomeMixedListResp_NotifyType `protobuf:"varint,9,rep,packed,name=notify_list,json=notifyList,proto3,enum=topic_channel.recommendation_gen.GetHomeMixedListResp_NotifyType" json:"notify_list,omitempty"`
	Footprint            string                            `protobuf:"bytes,10,opt,name=footprint,proto3" json:"footprint,omitempty"`
	BaseResp             *BaseRCMDResp                     `protobuf:"bytes,11,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *GetHomeMixedListResp) Reset()         { *m = GetHomeMixedListResp{} }
func (m *GetHomeMixedListResp) String() string { return proto.CompactTextString(m) }
func (*GetHomeMixedListResp) ProtoMessage()    {}
func (*GetHomeMixedListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{12}
}
func (m *GetHomeMixedListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHomeMixedListResp.Unmarshal(m, b)
}
func (m *GetHomeMixedListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHomeMixedListResp.Marshal(b, m, deterministic)
}
func (dst *GetHomeMixedListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHomeMixedListResp.Merge(dst, src)
}
func (m *GetHomeMixedListResp) XXX_Size() int {
	return xxx_messageInfo_GetHomeMixedListResp.Size(m)
}
func (m *GetHomeMixedListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHomeMixedListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHomeMixedListResp proto.InternalMessageInfo

func (m *GetHomeMixedListResp) GetChannelId() []uint32 {
	if m != nil {
		return m.ChannelId
	}
	return nil
}

func (m *GetHomeMixedListResp) GetBottomReached() bool {
	if m != nil {
		return m.BottomReached
	}
	return false
}

func (m *GetHomeMixedListResp) GetChannelInfoMap() map[uint32]*ChannelInfo {
	if m != nil {
		return m.ChannelInfoMap
	}
	return nil
}

func (m *GetHomeMixedListResp) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

func (m *GetHomeMixedListResp) GetDebugInfoMap() map[string]string {
	if m != nil {
		return m.DebugInfoMap
	}
	return nil
}

func (m *GetHomeMixedListResp) GetSelfLoc() *common.LocationInfo {
	if m != nil {
		return m.SelfLoc
	}
	return nil
}

func (m *GetHomeMixedListResp) GetPrefGames() []*PrefGame {
	if m != nil {
		return m.PrefGames
	}
	return nil
}

func (m *GetHomeMixedListResp) GetInsertPos() uint32 {
	if m != nil {
		return m.InsertPos
	}
	return 0
}

func (m *GetHomeMixedListResp) GetNotifyList() []GetHomeMixedListResp_NotifyType {
	if m != nil {
		return m.NotifyList
	}
	return nil
}

func (m *GetHomeMixedListResp) GetFootprint() string {
	if m != nil {
		return m.Footprint
	}
	return ""
}

func (m *GetHomeMixedListResp) GetBaseResp() *BaseRCMDResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type BaseRCMDResp struct {
	Ok                   bool                       `protobuf:"varint,1,opt,name=ok,proto3" json:"ok,omitempty"`
	Code                 uint64                     `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string                     `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg,omitempty"`
	PlanBStrategy        BaseRCMDResp_PlanBStrategy `protobuf:"varint,4,opt,name=plan_b_strategy,json=planBStrategy,proto3,enum=topic_channel.recommendation_gen.BaseRCMDResp_PlanBStrategy" json:"plan_b_strategy,omitempty"`
	IsAlgo               bool                       `protobuf:"varint,5,opt,name=is_algo,json=isAlgo,proto3" json:"is_algo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *BaseRCMDResp) Reset()         { *m = BaseRCMDResp{} }
func (m *BaseRCMDResp) String() string { return proto.CompactTextString(m) }
func (*BaseRCMDResp) ProtoMessage()    {}
func (*BaseRCMDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{13}
}
func (m *BaseRCMDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseRCMDResp.Unmarshal(m, b)
}
func (m *BaseRCMDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseRCMDResp.Marshal(b, m, deterministic)
}
func (dst *BaseRCMDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseRCMDResp.Merge(dst, src)
}
func (m *BaseRCMDResp) XXX_Size() int {
	return xxx_messageInfo_BaseRCMDResp.Size(m)
}
func (m *BaseRCMDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseRCMDResp.DiscardUnknown(m)
}

var xxx_messageInfo_BaseRCMDResp proto.InternalMessageInfo

func (m *BaseRCMDResp) GetOk() bool {
	if m != nil {
		return m.Ok
	}
	return false
}

func (m *BaseRCMDResp) GetCode() uint64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseRCMDResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *BaseRCMDResp) GetPlanBStrategy() BaseRCMDResp_PlanBStrategy {
	if m != nil {
		return m.PlanBStrategy
	}
	return BaseRCMDResp_PlanBStrategy_INVALID
}

func (m *BaseRCMDResp) GetIsAlgo() bool {
	if m != nil {
		return m.IsAlgo
	}
	return false
}

type SetTopicChannelQualityReq struct {
	ChannelId            []uint32                                      `protobuf:"varint,1,rep,packed,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Quality              SetTopicChannelQualityReq_TopicChannelQuality `protobuf:"varint,2,opt,name=quality,proto3,enum=topic_channel.recommendation_gen.SetTopicChannelQualityReq_TopicChannelQuality" json:"quality,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-"`
	XXX_unrecognized     []byte                                        `json:"-"`
	XXX_sizecache        int32                                         `json:"-"`
}

func (m *SetTopicChannelQualityReq) Reset()         { *m = SetTopicChannelQualityReq{} }
func (m *SetTopicChannelQualityReq) String() string { return proto.CompactTextString(m) }
func (*SetTopicChannelQualityReq) ProtoMessage()    {}
func (*SetTopicChannelQualityReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{14}
}
func (m *SetTopicChannelQualityReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTopicChannelQualityReq.Unmarshal(m, b)
}
func (m *SetTopicChannelQualityReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTopicChannelQualityReq.Marshal(b, m, deterministic)
}
func (dst *SetTopicChannelQualityReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTopicChannelQualityReq.Merge(dst, src)
}
func (m *SetTopicChannelQualityReq) XXX_Size() int {
	return xxx_messageInfo_SetTopicChannelQualityReq.Size(m)
}
func (m *SetTopicChannelQualityReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTopicChannelQualityReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetTopicChannelQualityReq proto.InternalMessageInfo

func (m *SetTopicChannelQualityReq) GetChannelId() []uint32 {
	if m != nil {
		return m.ChannelId
	}
	return nil
}

func (m *SetTopicChannelQualityReq) GetQuality() SetTopicChannelQualityReq_TopicChannelQuality {
	if m != nil {
		return m.Quality
	}
	return SetTopicChannelQualityReq_DEFAULT
}

type SetTopicChannelQualityResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetTopicChannelQualityResp) Reset()         { *m = SetTopicChannelQualityResp{} }
func (m *SetTopicChannelQualityResp) String() string { return proto.CompactTextString(m) }
func (*SetTopicChannelQualityResp) ProtoMessage()    {}
func (*SetTopicChannelQualityResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{15}
}
func (m *SetTopicChannelQualityResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTopicChannelQualityResp.Unmarshal(m, b)
}
func (m *SetTopicChannelQualityResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTopicChannelQualityResp.Marshal(b, m, deterministic)
}
func (dst *SetTopicChannelQualityResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTopicChannelQualityResp.Merge(dst, src)
}
func (m *SetTopicChannelQualityResp) XXX_Size() int {
	return xxx_messageInfo_SetTopicChannelQualityResp.Size(m)
}
func (m *SetTopicChannelQualityResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTopicChannelQualityResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetTopicChannelQualityResp proto.InternalMessageInfo

type CompanionChannelEvent struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CompanionChannelEvent) Reset()         { *m = CompanionChannelEvent{} }
func (m *CompanionChannelEvent) String() string { return proto.CompactTextString(m) }
func (*CompanionChannelEvent) ProtoMessage()    {}
func (*CompanionChannelEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{16}
}
func (m *CompanionChannelEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompanionChannelEvent.Unmarshal(m, b)
}
func (m *CompanionChannelEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompanionChannelEvent.Marshal(b, m, deterministic)
}
func (dst *CompanionChannelEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompanionChannelEvent.Merge(dst, src)
}
func (m *CompanionChannelEvent) XXX_Size() int {
	return xxx_messageInfo_CompanionChannelEvent.Size(m)
}
func (m *CompanionChannelEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_CompanionChannelEvent.DiscardUnknown(m)
}

var xxx_messageInfo_CompanionChannelEvent proto.InternalMessageInfo

func (m *CompanionChannelEvent) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type NegativeFeedEvent struct {
	Uid                  uint32                         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UserMap              map[uint32]int64               `protobuf:"bytes,2,rep,name=user_map,json=userMap,proto3" json:"user_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	TabMap               map[uint32]int64               `protobuf:"bytes,3,rep,name=tab_map,json=tabMap,proto3" json:"tab_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ChannelCondMap       map[string]int64               `protobuf:"bytes,4,rep,name=channel_cond_map,json=channelCondMap,proto3" json:"channel_cond_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ChannelNameMap       map[string]int64               `protobuf:"bytes,5,rep,name=channel_name_map,json=channelNameMap,proto3" json:"channel_name_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	UpdateTypeList       []NegativeFeedEvent_UpdateType `protobuf:"varint,6,rep,packed,name=update_type_list,json=updateTypeList,proto3,enum=topic_channel.recommendation_gen.NegativeFeedEvent_UpdateType" json:"update_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *NegativeFeedEvent) Reset()         { *m = NegativeFeedEvent{} }
func (m *NegativeFeedEvent) String() string { return proto.CompactTextString(m) }
func (*NegativeFeedEvent) ProtoMessage()    {}
func (*NegativeFeedEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{17}
}
func (m *NegativeFeedEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NegativeFeedEvent.Unmarshal(m, b)
}
func (m *NegativeFeedEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NegativeFeedEvent.Marshal(b, m, deterministic)
}
func (dst *NegativeFeedEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NegativeFeedEvent.Merge(dst, src)
}
func (m *NegativeFeedEvent) XXX_Size() int {
	return xxx_messageInfo_NegativeFeedEvent.Size(m)
}
func (m *NegativeFeedEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_NegativeFeedEvent.DiscardUnknown(m)
}

var xxx_messageInfo_NegativeFeedEvent proto.InternalMessageInfo

func (m *NegativeFeedEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NegativeFeedEvent) GetUserMap() map[uint32]int64 {
	if m != nil {
		return m.UserMap
	}
	return nil
}

func (m *NegativeFeedEvent) GetTabMap() map[uint32]int64 {
	if m != nil {
		return m.TabMap
	}
	return nil
}

func (m *NegativeFeedEvent) GetChannelCondMap() map[string]int64 {
	if m != nil {
		return m.ChannelCondMap
	}
	return nil
}

func (m *NegativeFeedEvent) GetChannelNameMap() map[string]int64 {
	if m != nil {
		return m.ChannelNameMap
	}
	return nil
}

func (m *NegativeFeedEvent) GetUpdateTypeList() []NegativeFeedEvent_UpdateType {
	if m != nil {
		return m.UpdateTypeList
	}
	return nil
}

type UserNegativeFeed struct {
	UserMap              map[uint32]int64 `protobuf:"bytes,1,rep,name=user_map,json=userMap,proto3" json:"user_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	TabMap               map[uint32]int64 `protobuf:"bytes,2,rep,name=tab_map,json=tabMap,proto3" json:"tab_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ChannelCondMap       map[string]int64 `protobuf:"bytes,3,rep,name=channel_cond_map,json=channelCondMap,proto3" json:"channel_cond_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ChannelNameMap       map[string]int64 `protobuf:"bytes,4,rep,name=channel_name_map,json=channelNameMap,proto3" json:"channel_name_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UserNegativeFeed) Reset()         { *m = UserNegativeFeed{} }
func (m *UserNegativeFeed) String() string { return proto.CompactTextString(m) }
func (*UserNegativeFeed) ProtoMessage()    {}
func (*UserNegativeFeed) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{18}
}
func (m *UserNegativeFeed) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserNegativeFeed.Unmarshal(m, b)
}
func (m *UserNegativeFeed) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserNegativeFeed.Marshal(b, m, deterministic)
}
func (dst *UserNegativeFeed) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserNegativeFeed.Merge(dst, src)
}
func (m *UserNegativeFeed) XXX_Size() int {
	return xxx_messageInfo_UserNegativeFeed.Size(m)
}
func (m *UserNegativeFeed) XXX_DiscardUnknown() {
	xxx_messageInfo_UserNegativeFeed.DiscardUnknown(m)
}

var xxx_messageInfo_UserNegativeFeed proto.InternalMessageInfo

func (m *UserNegativeFeed) GetUserMap() map[uint32]int64 {
	if m != nil {
		return m.UserMap
	}
	return nil
}

func (m *UserNegativeFeed) GetTabMap() map[uint32]int64 {
	if m != nil {
		return m.TabMap
	}
	return nil
}

func (m *UserNegativeFeed) GetChannelCondMap() map[string]int64 {
	if m != nil {
		return m.ChannelCondMap
	}
	return nil
}

func (m *UserNegativeFeed) GetChannelNameMap() map[string]int64 {
	if m != nil {
		return m.ChannelNameMap
	}
	return nil
}

type ChannelTitleTokenizeEvent struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	Tags                 []string `protobuf:"bytes,2,rep,name=tags,proto3" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelTitleTokenizeEvent) Reset()         { *m = ChannelTitleTokenizeEvent{} }
func (m *ChannelTitleTokenizeEvent) String() string { return proto.CompactTextString(m) }
func (*ChannelTitleTokenizeEvent) ProtoMessage()    {}
func (*ChannelTitleTokenizeEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{19}
}
func (m *ChannelTitleTokenizeEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelTitleTokenizeEvent.Unmarshal(m, b)
}
func (m *ChannelTitleTokenizeEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelTitleTokenizeEvent.Marshal(b, m, deterministic)
}
func (dst *ChannelTitleTokenizeEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelTitleTokenizeEvent.Merge(dst, src)
}
func (m *ChannelTitleTokenizeEvent) XXX_Size() int {
	return xxx_messageInfo_ChannelTitleTokenizeEvent.Size(m)
}
func (m *ChannelTitleTokenizeEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelTitleTokenizeEvent.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelTitleTokenizeEvent proto.InternalMessageInfo

func (m *ChannelTitleTokenizeEvent) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *ChannelTitleTokenizeEvent) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

type PlaymateIntentionLimitEvent struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LimitTime            int64    `protobuf:"varint,2,opt,name=limit_time,json=limitTime,proto3" json:"limit_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlaymateIntentionLimitEvent) Reset()         { *m = PlaymateIntentionLimitEvent{} }
func (m *PlaymateIntentionLimitEvent) String() string { return proto.CompactTextString(m) }
func (*PlaymateIntentionLimitEvent) ProtoMessage()    {}
func (*PlaymateIntentionLimitEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{20}
}
func (m *PlaymateIntentionLimitEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaymateIntentionLimitEvent.Unmarshal(m, b)
}
func (m *PlaymateIntentionLimitEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaymateIntentionLimitEvent.Marshal(b, m, deterministic)
}
func (dst *PlaymateIntentionLimitEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaymateIntentionLimitEvent.Merge(dst, src)
}
func (m *PlaymateIntentionLimitEvent) XXX_Size() int {
	return xxx_messageInfo_PlaymateIntentionLimitEvent.Size(m)
}
func (m *PlaymateIntentionLimitEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaymateIntentionLimitEvent.DiscardUnknown(m)
}

var xxx_messageInfo_PlaymateIntentionLimitEvent proto.InternalMessageInfo

func (m *PlaymateIntentionLimitEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PlaymateIntentionLimitEvent) GetLimitTime() int64 {
	if m != nil {
		return m.LimitTime
	}
	return 0
}

type GetRecommendationReasonReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendationReasonReq) Reset()         { *m = GetRecommendationReasonReq{} }
func (m *GetRecommendationReasonReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendationReasonReq) ProtoMessage()    {}
func (*GetRecommendationReasonReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{21}
}
func (m *GetRecommendationReasonReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendationReasonReq.Unmarshal(m, b)
}
func (m *GetRecommendationReasonReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendationReasonReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendationReasonReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendationReasonReq.Merge(dst, src)
}
func (m *GetRecommendationReasonReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendationReasonReq.Size(m)
}
func (m *GetRecommendationReasonReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendationReasonReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendationReasonReq proto.InternalMessageInfo

func (m *GetRecommendationReasonReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRecommendationReasonReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetRecommendationReasonResp struct {
	Reason               uint32   `protobuf:"varint,1,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendationReasonResp) Reset()         { *m = GetRecommendationReasonResp{} }
func (m *GetRecommendationReasonResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendationReasonResp) ProtoMessage()    {}
func (*GetRecommendationReasonResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_776ef2040ff8c575, []int{22}
}
func (m *GetRecommendationReasonResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendationReasonResp.Unmarshal(m, b)
}
func (m *GetRecommendationReasonResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendationReasonResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendationReasonResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendationReasonResp.Merge(dst, src)
}
func (m *GetRecommendationReasonResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendationReasonResp.Size(m)
}
func (m *GetRecommendationReasonResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendationReasonResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendationReasonResp proto.InternalMessageInfo

func (m *GetRecommendationReasonResp) GetReason() uint32 {
	if m != nil {
		return m.Reason
	}
	return 0
}

func init() {
	proto.RegisterType((*ResetFilterReq)(nil), "topic_channel.recommendation_gen.ResetFilterReq")
	proto.RegisterType((*ResetFilterResp)(nil), "topic_channel.recommendation_gen.ResetFilterResp")
	proto.RegisterType((*GetRCMDCfgReq)(nil), "topic_channel.recommendation_gen.GetRCMDCfgReq")
	proto.RegisterType((*GetRCMDCfgResp)(nil), "topic_channel.recommendation_gen.GetRCMDCfgResp")
	proto.RegisterType((*BlockOption)(nil), "topic_channel.recommendation_gen.BlockOption")
	proto.RegisterType((*RecommendationReqReserve)(nil), "topic_channel.recommendation_gen.RecommendationReqReserve")
	proto.RegisterType((*PrefGameLabel)(nil), "topic_channel.recommendation_gen.PrefGameLabel")
	proto.RegisterType((*PrefGame)(nil), "topic_channel.recommendation_gen.PrefGame")
	proto.RegisterType((*GetRecommendationListReq)(nil), "topic_channel.recommendation_gen.GetRecommendationListReq")
	proto.RegisterType((*GetRecommendationListReq_BusinessFilter)(nil), "topic_channel.recommendation_gen.GetRecommendationListReq.BusinessFilter")
	proto.RegisterType((*GetHomeMixedListReq)(nil), "topic_channel.recommendation_gen.GetHomeMixedListReq")
	proto.RegisterType((*GetHomeMixedListReq_BusinessFilter)(nil), "topic_channel.recommendation_gen.GetHomeMixedListReq.BusinessFilter")
	proto.RegisterType((*ChannelInfo)(nil), "topic_channel.recommendation_gen.ChannelInfo")
	proto.RegisterType((*GetRecommendationListResp)(nil), "topic_channel.recommendation_gen.GetRecommendationListResp")
	proto.RegisterMapType((map[uint32]*ChannelInfo)(nil), "topic_channel.recommendation_gen.GetRecommendationListResp.ChannelInfoMapEntry")
	proto.RegisterMapType((map[uint32]*common.ChannelInfo)(nil), "topic_channel.recommendation_gen.GetRecommendationListResp.CommonChannelInfoMapEntry")
	proto.RegisterMapType((map[string]string)(nil), "topic_channel.recommendation_gen.GetRecommendationListResp.DebugInfoMapEntry")
	proto.RegisterType((*GetHomeMixedListResp)(nil), "topic_channel.recommendation_gen.GetHomeMixedListResp")
	proto.RegisterMapType((map[uint32]*ChannelInfo)(nil), "topic_channel.recommendation_gen.GetHomeMixedListResp.ChannelInfoMapEntry")
	proto.RegisterMapType((map[string]string)(nil), "topic_channel.recommendation_gen.GetHomeMixedListResp.DebugInfoMapEntry")
	proto.RegisterType((*BaseRCMDResp)(nil), "topic_channel.recommendation_gen.BaseRCMDResp")
	proto.RegisterType((*SetTopicChannelQualityReq)(nil), "topic_channel.recommendation_gen.SetTopicChannelQualityReq")
	proto.RegisterType((*SetTopicChannelQualityResp)(nil), "topic_channel.recommendation_gen.SetTopicChannelQualityResp")
	proto.RegisterType((*CompanionChannelEvent)(nil), "topic_channel.recommendation_gen.CompanionChannelEvent")
	proto.RegisterType((*NegativeFeedEvent)(nil), "topic_channel.recommendation_gen.NegativeFeedEvent")
	proto.RegisterMapType((map[string]int64)(nil), "topic_channel.recommendation_gen.NegativeFeedEvent.ChannelCondMapEntry")
	proto.RegisterMapType((map[string]int64)(nil), "topic_channel.recommendation_gen.NegativeFeedEvent.ChannelNameMapEntry")
	proto.RegisterMapType((map[uint32]int64)(nil), "topic_channel.recommendation_gen.NegativeFeedEvent.TabMapEntry")
	proto.RegisterMapType((map[uint32]int64)(nil), "topic_channel.recommendation_gen.NegativeFeedEvent.UserMapEntry")
	proto.RegisterType((*UserNegativeFeed)(nil), "topic_channel.recommendation_gen.UserNegativeFeed")
	proto.RegisterMapType((map[string]int64)(nil), "topic_channel.recommendation_gen.UserNegativeFeed.ChannelCondMapEntry")
	proto.RegisterMapType((map[string]int64)(nil), "topic_channel.recommendation_gen.UserNegativeFeed.ChannelNameMapEntry")
	proto.RegisterMapType((map[uint32]int64)(nil), "topic_channel.recommendation_gen.UserNegativeFeed.TabMapEntry")
	proto.RegisterMapType((map[uint32]int64)(nil), "topic_channel.recommendation_gen.UserNegativeFeed.UserMapEntry")
	proto.RegisterType((*ChannelTitleTokenizeEvent)(nil), "topic_channel.recommendation_gen.ChannelTitleTokenizeEvent")
	proto.RegisterType((*PlaymateIntentionLimitEvent)(nil), "topic_channel.recommendation_gen.PlaymateIntentionLimitEvent")
	proto.RegisterType((*GetRecommendationReasonReq)(nil), "topic_channel.recommendation_gen.GetRecommendationReasonReq")
	proto.RegisterType((*GetRecommendationReasonResp)(nil), "topic_channel.recommendation_gen.GetRecommendationReasonResp")
	proto.RegisterEnum("topic_channel.recommendation_gen.IMChannelListABGroup", IMChannelListABGroup_name, IMChannelListABGroup_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.REGULATORY_LEVEL", REGULATORY_LEVEL_name, REGULATORY_LEVEL_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.PrefGameLabelType", PrefGameLabelType_name, PrefGameLabelType_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.BusinessBlockEnum", BusinessBlockEnum_name, BusinessBlockEnum_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.BusinessElemEnum", BusinessElemEnum_name, BusinessElemEnum_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.RCMDLabel", RCMDLabel_name, RCMDLabel_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.RCMDReason", RCMDReason_name, RCMDReason_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.GetRCMDCfgReq_SOURCE", GetRCMDCfgReq_SOURCE_name, GetRCMDCfgReq_SOURCE_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.GetRecommendationListReq_GetListMode", GetRecommendationListReq_GetListMode_name, GetRecommendationListReq_GetListMode_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.GetRecommendationListReq_Environment", GetRecommendationListReq_Environment_name, GetRecommendationListReq_Environment_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.GetHomeMixedListReq_GetListMode", GetHomeMixedListReq_GetListMode_name, GetHomeMixedListReq_GetListMode_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.GetHomeMixedListReq_Environment", GetHomeMixedListReq_Environment_name, GetHomeMixedListReq_Environment_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.ChannelInfo_LocShowType", ChannelInfo_LocShowType_name, ChannelInfo_LocShowType_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.GetRecommendationListResp_NotifyType", GetRecommendationListResp_NotifyType_name, GetRecommendationListResp_NotifyType_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.GetHomeMixedListResp_NotifyType", GetHomeMixedListResp_NotifyType_name, GetHomeMixedListResp_NotifyType_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.BaseRCMDResp_PlanBStrategy", BaseRCMDResp_PlanBStrategy_name, BaseRCMDResp_PlanBStrategy_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.SetTopicChannelQualityReq_TopicChannelQuality", SetTopicChannelQualityReq_TopicChannelQuality_name, SetTopicChannelQualityReq_TopicChannelQuality_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.NegativeFeedEvent_UpdateType", NegativeFeedEvent_UpdateType_name, NegativeFeedEvent_UpdateType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GenRecommendationClient is the client API for GenRecommendation service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GenRecommendationClient interface {
	// 获取推荐房列表（个数为1的时候特殊处理，获取最合适的/home/<USER>/go_project/tt19/rcmd/api/rcmd/topic_channel）
	GetRecommendationList(ctx context.Context, in *GetRecommendationListReq, opts ...grpc.CallOption) (*GetRecommendationListResp, error)
	// expose for algorithm 0.2
	RecallChannelList(ctx context.Context, in *GetRecommendationListReq, opts ...grpc.CallOption) (*GetRecommendationListResp, error)
	SetTopicChannelQuality(ctx context.Context, in *SetTopicChannelQualityReq, opts ...grpc.CallOption) (*SetTopicChannelQualityResp, error)
	// rcmd-common config
	GetRCMDCfg(ctx context.Context, in *GetRCMDCfgReq, opts ...grpc.CallOption) (*GetRCMDCfgResp, error)
	// 清空下发过滤器
	ResetFilter(ctx context.Context, in *ResetFilterReq, opts ...grpc.CallOption) (*ResetFilterResp, error)
	// 首页混推新接口 -- 从上面原接口GetRecommendationList复制出来的
	GetHomeMixedList(ctx context.Context, in *GetHomeMixedListReq, opts ...grpc.CallOption) (*GetHomeMixedListResp, error)
	// 异步获取推荐理由
	GetRecommendationReason(ctx context.Context, in *GetRecommendationReasonReq, opts ...grpc.CallOption) (*GetRecommendationReasonResp, error)
}

type genRecommendationClient struct {
	cc *grpc.ClientConn
}

func NewGenRecommendationClient(cc *grpc.ClientConn) GenRecommendationClient {
	return &genRecommendationClient{cc}
}

func (c *genRecommendationClient) GetRecommendationList(ctx context.Context, in *GetRecommendationListReq, opts ...grpc.CallOption) (*GetRecommendationListResp, error) {
	out := new(GetRecommendationListResp)
	err := c.cc.Invoke(ctx, "/topic_channel.recommendation_gen.GenRecommendation/GetRecommendationList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genRecommendationClient) RecallChannelList(ctx context.Context, in *GetRecommendationListReq, opts ...grpc.CallOption) (*GetRecommendationListResp, error) {
	out := new(GetRecommendationListResp)
	err := c.cc.Invoke(ctx, "/topic_channel.recommendation_gen.GenRecommendation/RecallChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genRecommendationClient) SetTopicChannelQuality(ctx context.Context, in *SetTopicChannelQualityReq, opts ...grpc.CallOption) (*SetTopicChannelQualityResp, error) {
	out := new(SetTopicChannelQualityResp)
	err := c.cc.Invoke(ctx, "/topic_channel.recommendation_gen.GenRecommendation/SetTopicChannelQuality", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genRecommendationClient) GetRCMDCfg(ctx context.Context, in *GetRCMDCfgReq, opts ...grpc.CallOption) (*GetRCMDCfgResp, error) {
	out := new(GetRCMDCfgResp)
	err := c.cc.Invoke(ctx, "/topic_channel.recommendation_gen.GenRecommendation/GetRCMDCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genRecommendationClient) ResetFilter(ctx context.Context, in *ResetFilterReq, opts ...grpc.CallOption) (*ResetFilterResp, error) {
	out := new(ResetFilterResp)
	err := c.cc.Invoke(ctx, "/topic_channel.recommendation_gen.GenRecommendation/ResetFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genRecommendationClient) GetHomeMixedList(ctx context.Context, in *GetHomeMixedListReq, opts ...grpc.CallOption) (*GetHomeMixedListResp, error) {
	out := new(GetHomeMixedListResp)
	err := c.cc.Invoke(ctx, "/topic_channel.recommendation_gen.GenRecommendation/GetHomeMixedList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genRecommendationClient) GetRecommendationReason(ctx context.Context, in *GetRecommendationReasonReq, opts ...grpc.CallOption) (*GetRecommendationReasonResp, error) {
	out := new(GetRecommendationReasonResp)
	err := c.cc.Invoke(ctx, "/topic_channel.recommendation_gen.GenRecommendation/GetRecommendationReason", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GenRecommendationServer is the server API for GenRecommendation service.
type GenRecommendationServer interface {
	// 获取推荐房列表（个数为1的时候特殊处理，获取最合适的/home/<USER>/go_project/tt19/rcmd/api/rcmd/topic_channel）
	GetRecommendationList(context.Context, *GetRecommendationListReq) (*GetRecommendationListResp, error)
	// expose for algorithm 0.2
	RecallChannelList(context.Context, *GetRecommendationListReq) (*GetRecommendationListResp, error)
	SetTopicChannelQuality(context.Context, *SetTopicChannelQualityReq) (*SetTopicChannelQualityResp, error)
	// rcmd-common config
	GetRCMDCfg(context.Context, *GetRCMDCfgReq) (*GetRCMDCfgResp, error)
	// 清空下发过滤器
	ResetFilter(context.Context, *ResetFilterReq) (*ResetFilterResp, error)
	// 首页混推新接口 -- 从上面原接口GetRecommendationList复制出来的
	GetHomeMixedList(context.Context, *GetHomeMixedListReq) (*GetHomeMixedListResp, error)
	// 异步获取推荐理由
	GetRecommendationReason(context.Context, *GetRecommendationReasonReq) (*GetRecommendationReasonResp, error)
}

func RegisterGenRecommendationServer(s *grpc.Server, srv GenRecommendationServer) {
	s.RegisterService(&_GenRecommendation_serviceDesc, srv)
}

func _GenRecommendation_GetRecommendationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendationListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenRecommendationServer).GetRecommendationList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.recommendation_gen.GenRecommendation/GetRecommendationList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenRecommendationServer).GetRecommendationList(ctx, req.(*GetRecommendationListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GenRecommendation_RecallChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendationListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenRecommendationServer).RecallChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.recommendation_gen.GenRecommendation/RecallChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenRecommendationServer).RecallChannelList(ctx, req.(*GetRecommendationListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GenRecommendation_SetTopicChannelQuality_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetTopicChannelQualityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenRecommendationServer).SetTopicChannelQuality(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.recommendation_gen.GenRecommendation/SetTopicChannelQuality",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenRecommendationServer).SetTopicChannelQuality(ctx, req.(*SetTopicChannelQualityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GenRecommendation_GetRCMDCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRCMDCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenRecommendationServer).GetRCMDCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.recommendation_gen.GenRecommendation/GetRCMDCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenRecommendationServer).GetRCMDCfg(ctx, req.(*GetRCMDCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GenRecommendation_ResetFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetFilterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenRecommendationServer).ResetFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.recommendation_gen.GenRecommendation/ResetFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenRecommendationServer).ResetFilter(ctx, req.(*ResetFilterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GenRecommendation_GetHomeMixedList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHomeMixedListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenRecommendationServer).GetHomeMixedList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.recommendation_gen.GenRecommendation/GetHomeMixedList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenRecommendationServer).GetHomeMixedList(ctx, req.(*GetHomeMixedListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GenRecommendation_GetRecommendationReason_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendationReasonReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenRecommendationServer).GetRecommendationReason(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.recommendation_gen.GenRecommendation/GetRecommendationReason",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenRecommendationServer).GetRecommendationReason(ctx, req.(*GetRecommendationReasonReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GenRecommendation_serviceDesc = grpc.ServiceDesc{
	ServiceName: "topic_channel.recommendation_gen.GenRecommendation",
	HandlerType: (*GenRecommendationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRecommendationList",
			Handler:    _GenRecommendation_GetRecommendationList_Handler,
		},
		{
			MethodName: "RecallChannelList",
			Handler:    _GenRecommendation_RecallChannelList_Handler,
		},
		{
			MethodName: "SetTopicChannelQuality",
			Handler:    _GenRecommendation_SetTopicChannelQuality_Handler,
		},
		{
			MethodName: "GetRCMDCfg",
			Handler:    _GenRecommendation_GetRCMDCfg_Handler,
		},
		{
			MethodName: "ResetFilter",
			Handler:    _GenRecommendation_ResetFilter_Handler,
		},
		{
			MethodName: "GetHomeMixedList",
			Handler:    _GenRecommendation_GetHomeMixedList_Handler,
		},
		{
			MethodName: "GetRecommendationReason",
			Handler:    _GenRecommendation_GetRecommendationReason_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/topic_channel/recommendation_gen.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/topic_channel/recommendation_gen.proto", fileDescriptor_recommendation_gen_776ef2040ff8c575)
}

var fileDescriptor_recommendation_gen_776ef2040ff8c575 = []byte{
	// 3247 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x3a, 0x4d, 0x73, 0x1b, 0x47,
	0x76, 0x02, 0x40, 0xe1, 0xe3, 0x81, 0x00, 0x87, 0x4d, 0x4a, 0x1a, 0x41, 0xd6, 0x9a, 0xc6, 0x56,
	0x76, 0x59, 0x54, 0x0c, 0xee, 0x52, 0xf2, 0x7e, 0xd8, 0xce, 0x3a, 0x24, 0x08, 0x52, 0x48, 0x00,
	0x90, 0x1e, 0x80, 0x92, 0xed, 0xdd, 0xad, 0xa9, 0xc1, 0x4c, 0x13, 0xec, 0x70, 0x66, 0x7a, 0x34,
	0xdd, 0x00, 0xc5, 0xad, 0xca, 0x21, 0xa7, 0x5c, 0x7c, 0x48, 0x55, 0x2a, 0xb9, 0xe6, 0x9a, 0x3f,
	0x90, 0x5b, 0xaa, 0xf2, 0x0b, 0x72, 0xc9, 0x6f, 0xc8, 0x21, 0x7f, 0x21, 0xb7, 0x54, 0x77, 0x0f,
	0x80, 0x19, 0x02, 0x0c, 0x49, 0x30, 0x55, 0x1b, 0x97, 0x7d, 0x42, 0xf7, 0x7b, 0xdd, 0xef, 0xbd,
	0x7e, 0xdf, 0xdd, 0x18, 0xf8, 0x84, 0xf3, 0xed, 0x77, 0x43, 0x62, 0x9f, 0x33, 0xe2, 0x8e, 0x70,
	0xb8, 0xcd, 0x69, 0x40, 0x6c, 0xd3, 0x3e, 0xb3, 0x7c, 0x1f, 0xbb, 0xdb, 0x21, 0xb6, 0xa9, 0xe7,
	0x61, 0xdf, 0xb1, 0x38, 0xa1, 0xbe, 0x39, 0xc0, 0x7e, 0x2d, 0x08, 0x29, 0xa7, 0x68, 0x23, 0xb1,
	0xae, 0x36, 0xbb, 0xae, 0xf2, 0xf1, 0x15, 0xc2, 0xa1, 0xed, 0x39, 0x1f, 0xbb, 0xd4, 0xb6, 0xdc,
	0x6d, 0xb1, 0x96, 0xfa, 0xd1, 0x8f, 0x22, 0x58, 0x79, 0x75, 0xe3, 0xf2, 0x24, 0x47, 0xb5, 0xeb,
	0x06, 0xe9, 0x6d, 0xcf, 0x19, 0x4f, 0x4c, 0xd7, 0xea, 0x4f, 0xb6, 0xfd, 0xea, 0x2e, 0x87, 0x8e,
	0x8b, 0x59, 0xad, 0x42, 0xd9, 0xc0, 0x0c, 0xf3, 0x03, 0xe2, 0x72, 0x1c, 0x1a, 0xf8, 0x1d, 0xd2,
	0x20, 0x33, 0x24, 0x8e, 0x9e, 0xda, 0x48, 0x6d, 0x96, 0x0c, 0x31, 0xac, 0xfe, 0x12, 0x56, 0x12,
	0x6b, 0x58, 0x80, 0x10, 0x2c, 0xd9, 0xd4, 0xc1, 0xd1, 0x2a, 0x39, 0x16, 0x1b, 0x3d, 0x36, 0xd0,
	0xd3, 0x1b, 0xa9, 0xcd, 0x82, 0x21, 0x86, 0xd5, 0x6f, 0x53, 0x50, 0x3a, 0xc4, 0xdc, 0xa8, 0xb7,
	0xf7, 0xeb, 0xa7, 0x03, 0x41, 0xbc, 0x03, 0x59, 0x46, 0x87, 0xa1, 0xad, 0x76, 0x96, 0x77, 0x7e,
	0x51, 0xbb, 0x49, 0xef, 0xb5, 0x04, 0x81, 0x5a, 0xf7, 0xe8, 0xc4, 0xa8, 0x37, 0x8c, 0x88, 0x4a,
	0xf5, 0x05, 0x64, 0x15, 0x04, 0x21, 0x28, 0xab, 0x91, 0xb9, 0xdf, 0x38, 0xd8, 0x3d, 0x69, 0xf5,
	0xb4, 0x07, 0xa8, 0x04, 0x85, 0x08, 0xd6, 0xeb, 0x69, 0xa9, 0xea, 0x3e, 0x94, 0xe3, 0xc4, 0x58,
	0x80, 0x76, 0xe0, 0x11, 0x1b, 0x06, 0x38, 0x1c, 0x11, 0x46, 0x43, 0xf3, 0xe2, 0x8c, 0x70, 0x6c,
	0xba, 0x84, 0x71, 0x3d, 0xb5, 0x91, 0xd9, 0x2c, 0x19, 0x6b, 0x53, 0xe4, 0x5b, 0x81, 0x6b, 0x11,
	0xc6, 0xab, 0xbb, 0x50, 0xdc, 0x73, 0xa9, 0x7d, 0x7e, 0x14, 0x08, 0x11, 0xd1, 0x53, 0xc8, 0xf7,
	0xc5, 0xd4, 0x9c, 0xe8, 0x2c, 0x27, 0xe7, 0x4d, 0x07, 0x3d, 0x81, 0x1c, 0x76, 0xb1, 0x27, 0x30,
	0x69, 0x89, 0xc9, 0x8a, 0x69, 0xd3, 0xa9, 0xfe, 0x35, 0xe8, 0x46, 0xe2, 0xa0, 0x06, 0x7e, 0x27,
	0x34, 0x1c, 0x8e, 0x30, 0xb2, 0x60, 0x95, 0x78, 0x52, 0x08, 0x13, 0xbf, 0x0f, 0xcc, 0x41, 0x48,
	0x87, 0xc1, 0xed, 0x95, 0xd5, 0x6c, 0xd7, 0x15, 0x52, 0x88, 0xba, 0xbb, 0x77, 0x28, 0x76, 0x1b,
	0x65, 0xe2, 0x89, 0x69, 0xe3, 0x7d, 0x20, 0xe7, 0xd5, 0x3f, 0x40, 0xe9, 0x38, 0xc4, 0xa7, 0x87,
	0x96, 0x87, 0x5b, 0xc2, 0x89, 0x50, 0x19, 0xd2, 0x13, 0xe9, 0xd3, 0xc4, 0x11, 0x96, 0x1c, 0x59,
	0xee, 0xd8, 0x92, 0x23, 0xcb, 0x45, 0x87, 0xb0, 0xc4, 0x2f, 0x03, 0xac, 0x67, 0xa4, 0x20, 0x2f,
	0x6f, 0x16, 0x24, 0xc1, 0xa0, 0x77, 0x19, 0x60, 0x43, 0x12, 0xa8, 0xfe, 0x15, 0xe4, 0xc7, 0x28,
	0xf4, 0x08, 0xb2, 0xdc, 0xea, 0x4f, 0x15, 0xf7, 0x90, 0x5b, 0xfd, 0xa6, 0x83, 0x0e, 0x21, 0x2b,
	0x7d, 0x9b, 0xe9, 0xe9, 0x8d, 0xcc, 0x66, 0x71, 0x67, 0xfb, 0x8e, 0xdc, 0x8c, 0x68, 0x7b, 0xf5,
	0xbf, 0x56, 0x40, 0x17, 0x06, 0x4f, 0xac, 0x17, 0x9a, 0x98, 0xeb, 0xe6, 0x68, 0x1d, 0x1e, 0xba,
	0xc4, 0x23, 0x3c, 0x32, 0x96, 0x9a, 0xc4, 0x84, 0xcc, 0xc4, 0x85, 0x34, 0xa0, 0xa4, 0xcc, 0x4e,
	0xa5, 0x1b, 0x30, 0x7d, 0x49, 0xca, 0xfa, 0xf1, 0xcd, 0xb2, 0xc6, 0x9c, 0xc7, 0x58, 0xee, 0x4f,
	0x27, 0x0c, 0x59, 0x90, 0x1f, 0x60, 0x6e, 0x7a, 0x22, 0xb0, 0x1e, 0x4a, 0x45, 0x1f, 0xdc, 0x2e,
	0x3c, 0xe6, 0x1d, 0x50, 0x20, 0xc4, 0xb0, 0x4d, 0x1d, 0x6c, 0xe4, 0x06, 0x58, 0x0e, 0xd0, 0xcf,
	0x60, 0x7d, 0x9c, 0x3f, 0xb0, 0xcf, 0x71, 0x68, 0x46, 0xd1, 0x98, 0x95, 0x67, 0x43, 0x11, 0xae,
	0x21, 0x50, 0x5d, 0x89, 0x41, 0x1f, 0x42, 0xd1, 0x76, 0x09, 0xf6, 0xb9, 0x29, 0x1d, 0x20, 0x27,
	0x17, 0x82, 0x02, 0x09, 0xbb, 0xa2, 0x3f, 0x81, 0x72, 0xb4, 0x60, 0x84, 0x43, 0x46, 0xa8, 0xaf,
	0xe7, 0xe5, 0x9a, 0x92, 0x82, 0xbe, 0x51, 0x40, 0xf4, 0x0c, 0x0a, 0x9e, 0x15, 0x9e, 0x63, 0x2e,
	0x54, 0x59, 0x90, 0x2b, 0xf2, 0x0a, 0xd0, 0x74, 0xd0, 0x57, 0x90, 0xc1, 0xfe, 0x48, 0x87, 0x7b,
	0x1f, 0xba, 0xe1, 0x8f, 0x48, 0x48, 0x7d, 0x0f, 0xfb, 0xdc, 0x10, 0x24, 0xd1, 0x9f, 0xc2, 0xf8,
	0x50, 0x66, 0x60, 0xd9, 0xe7, 0xd6, 0x00, 0x0b, 0xfe, 0x45, 0xe9, 0xd9, 0x5a, 0x84, 0x39, 0x56,
	0x88, 0xa6, 0x23, 0x82, 0x99, 0x87, 0x96, 0x2d, 0xd7, 0x2c, 0xcb, 0x35, 0x39, 0x39, 0x6f, 0x3a,
	0xe8, 0xc7, 0x50, 0x12, 0x86, 0x71, 0x27, 0xa7, 0x2c, 0x49, 0xfc, 0xb2, 0x04, 0x8e, 0x0f, 0xf9,
	0x1c, 0xc0, 0xc1, 0xfd, 0xe1, 0xc0, 0x3c, 0x75, 0xad, 0x81, 0x5e, 0x96, 0xa7, 0x2c, 0x48, 0xc8,
	0x81, 0x6b, 0x0d, 0x90, 0x0e, 0xb9, 0x50, 0x85, 0xb9, 0xbe, 0xa2, 0xa8, 0x47, 0x53, 0xf4, 0x12,
	0xf2, 0x7d, 0x8b, 0x61, 0x33, 0xc4, 0xef, 0x74, 0x6d, 0x23, 0xb5, 0x59, 0xdc, 0xd1, 0x6b, 0x22,
	0xdb, 0xd7, 0xa2, 0x64, 0x6d, 0xd8, 0x9e, 0xb3, 0x67, 0x31, 0x2c, 0x12, 0x45, 0xae, 0xaf, 0x06,
	0xc2, 0x85, 0x19, 0x7e, 0xaf, 0xaf, 0x2a, 0x17, 0x66, 0xf8, 0x3d, 0xfa, 0x1c, 0x8a, 0xfd, 0x90,
	0x5e, 0xb0, 0x28, 0x8b, 0x21, 0x49, 0xe9, 0xd9, 0x2c, 0x25, 0xb9, 0xa6, 0xe9, 0x9f, 0x52, 0x03,
	0xd4, 0x7a, 0xa1, 0x3f, 0xf4, 0x7b, 0xd0, 0x42, 0x3c, 0x18, 0xba, 0x16, 0xa7, 0xe1, 0xa5, 0xe9,
	0xe2, 0x11, 0x76, 0xf5, 0x35, 0x69, 0x92, 0x9d, 0x9b, 0x4d, 0x62, 0x34, 0x0e, 0x4f, 0x5a, 0xbb,
	0xbd, 0x23, 0xe3, 0x6b, 0xb3, 0xd5, 0x78, 0xd3, 0x68, 0x19, 0x2b, 0x53, 0x5a, 0x2d, 0x41, 0x0a,
	0xbd, 0x84, 0xc7, 0x84, 0x99, 0x1e, 0xf1, 0x69, 0x48, 0xf8, 0xa5, 0x19, 0x58, 0xa1, 0xf4, 0x2a,
	0xab, 0xaf, 0xaf, 0x6f, 0xa4, 0x36, 0xf3, 0xc6, 0x1a, 0x61, 0xed, 0x08, 0x79, 0x2c, 0x71, 0x3d,
	0xab, 0x8f, 0x3e, 0x82, 0x65, 0xdb, 0xe2, 0x78, 0x20, 0x24, 0x22, 0x0e, 0xd3, 0x1f, 0xcb, 0xc4,
	0x5c, 0x1c, 0xc3, 0x9a, 0x0e, 0x13, 0x69, 0x56, 0x45, 0x28, 0xd3, 0x9f, 0x48, 0x6c, 0x56, 0x86,
	0x28, 0x43, 0x3f, 0x81, 0x15, 0xc2, 0xcc, 0x77, 0x9e, 0x79, 0x1a, 0x5a, 0x1e, 0xbe, 0xa0, 0xe1,
	0xb9, 0xae, 0x4b, 0x4e, 0x25, 0xc2, 0xbe, 0xf4, 0x0e, 0xc6, 0x40, 0xd4, 0x04, 0x08, 0x42, 0x7c,
	0x6a, 0x0e, 0x2c, 0x0f, 0x33, 0xfd, 0xa9, 0x0c, 0xe4, 0xad, 0xdb, 0x27, 0x1d, 0xa3, 0x10, 0x44,
	0x23, 0x86, 0xea, 0x93, 0xdc, 0x55, 0x91, 0x64, 0x5e, 0x5c, 0x25, 0x33, 0x5b, 0xc1, 0x67, 0xf2,
	0x16, 0x0a, 0x61, 0xa5, 0x3f, 0x64, 0xc4, 0xc7, 0x8c, 0x99, 0xa7, 0xb2, 0xe6, 0xea, 0xcf, 0x24,
	0xb5, 0xe6, 0x3d, 0x22, 0x63, 0x2f, 0xa2, 0x18, 0x15, 0xf1, 0x72, 0x3f, 0x31, 0x47, 0x6f, 0xa1,
	0x24, 0x8e, 0x6f, 0x32, 0xcc, 0x39, 0xf1, 0x07, 0x4c, 0xff, 0x40, 0x72, 0xbc, 0xc1, 0xf0, 0x91,
	0x4f, 0xf5, 0x6c, 0x71, 0x88, 0xae, 0xda, 0x6a, 0x2c, 0x0f, 0xa6, 0x93, 0xb1, 0x11, 0x4e, 0x2d,
	0xd7, 0xed, 0x5b, 0xf6, 0xb9, 0x74, 0xf0, 0xe7, 0x63, 0x23, 0x1c, 0x44, 0x50, 0xe1, 0xcc, 0x3f,
	0x85, 0x15, 0x22, 0xd2, 0x0e, 0x66, 0xdc, 0x8c, 0x54, 0xf8, 0xa3, 0x8d, 0xcc, 0x66, 0xc1, 0x28,
	0x8f, 0xc1, 0x2d, 0xa5, 0x9d, 0x1f, 0x43, 0xc9, 0xc1, 0x2e, 0x19, 0xe1, 0xf0, 0x52, 0xa5, 0xa4,
	0x0f, 0x55, 0x20, 0x8e, 0x81, 0x32, 0x29, 0x6d, 0xc3, 0x3a, 0x61, 0x26, 0xf6, 0xad, 0xbe, 0x8b,
	0xa5, 0x5d, 0x15, 0x4d, 0x7d, 0x43, 0xb2, 0x5e, 0x25, 0xac, 0x21, 0x51, 0xd3, 0x12, 0x58, 0x83,
	0x35, 0x76, 0x46, 0xb0, 0xeb, 0x44, 0x1a, 0x37, 0x2f, 0x68, 0xe8, 0x30, 0xfd, 0x23, 0x29, 0xc2,
	0xaa, 0x42, 0x29, 0x55, 0xbd, 0x15, 0x08, 0xf4, 0x3b, 0x58, 0xb1, 0x5d, 0x8b, 0x31, 0x72, 0x7a,
	0x39, 0x16, 0xb7, 0x2a, 0x35, 0xf6, 0xf2, 0x66, 0x8b, 0xd7, 0xa3, 0x8d, 0x92, 0xb3, 0xb4, 0x4f,
	0xd9, 0x8e, 0x83, 0x58, 0xe5, 0x5f, 0x52, 0x50, 0x4e, 0x1a, 0x0c, 0x19, 0x00, 0xaa, 0xe0, 0xc8,
	0x33, 0xa7, 0x6e, 0x5b, 0x87, 0xc7, 0x54, 0x64, 0xd5, 0x69, 0xf8, 0x43, 0xcf, 0x28, 0x48, 0x32,
	0x52, 0x4b, 0x47, 0x50, 0x90, 0x0d, 0x8a, 0x24, 0x29, 0x8a, 0xed, 0xad, 0x22, 0x7d, 0x4c, 0xb2,
	0xe1, 0x62, 0x4f, 0x52, 0xcc, 0x0b, 0x22, 0x82, 0xa0, 0xe8, 0x8d, 0x62, 0x65, 0x07, 0x15, 0x21,
	0x37, 0x6d, 0xc6, 0x96, 0x21, 0xdf, 0x69, 0x7c, 0xd5, 0x3b, 0xde, 0x3d, 0x6c, 0x68, 0x29, 0x81,
	0x32, 0x1a, 0x07, 0x46, 0xa3, 0xfb, 0x5a, 0x4b, 0x23, 0x80, 0x6c, 0xb3, 0xd3, 0x6d, 0x18, 0x3d,
	0x2d, 0x53, 0x7d, 0x05, 0xc5, 0x58, 0x12, 0x47, 0x65, 0x11, 0x9b, 0xd4, 0x19, 0xda, 0x82, 0xbd,
	0xf6, 0x40, 0xec, 0x63, 0xdc, 0x1a, 0x10, 0x7f, 0xa0, 0xa5, 0x50, 0x1e, 0x96, 0x38, 0x66, 0x5c,
	0x4b, 0x57, 0xff, 0xa1, 0x0c, 0x6b, 0x87, 0x98, 0xbf, 0xa6, 0x1e, 0x6e, 0x93, 0xf7, 0xd8, 0xf9,
	0x7f, 0x5c, 0xe5, 0x7f, 0x37, 0x53, 0xe5, 0x77, 0x6f, 0x15, 0xd6, 0x57, 0xcf, 0xf6, 0x9d, 0x2a,
	0xf0, 0xdd, 0x78, 0x81, 0x5f, 0xf0, 0xbc, 0x3f, 0xd4, 0xf6, 0x1f, 0x6a, 0xfb, 0x77, 0xbe, 0xb6,
	0x7b, 0xd7, 0xd5, 0xf6, 0xfd, 0xc5, 0x82, 0xe2, 0x7b, 0x55, 0xd6, 0x7f, 0xa8, 0x8b, 0x77, 0xad,
	0x8b, 0xff, 0xf6, 0x10, 0x8a, 0xd1, 0x8b, 0x80, 0x48, 0x09, 0xaa, 0xce, 0x0d, 0x12, 0x57, 0xee,
	0x41, 0xd3, 0x11, 0x35, 0x20, 0xc4, 0xb6, 0xe5, 0xba, 0x2a, 0xb9, 0xa9, 0xd2, 0x08, 0x0a, 0x24,
	0xb3, 0xdb, 0x0b, 0xc8, 0xb8, 0xd4, 0x96, 0xc5, 0xb1, 0xb8, 0xf3, 0x34, 0x91, 0x74, 0x5a, 0xd4,
	0x96, 0x3a, 0x90, 0x29, 0x47, 0xac, 0x42, 0x2d, 0x28, 0xca, 0x58, 0x88, 0xec, 0xbd, 0x24, 0x15,
	0xf8, 0xe2, 0x16, 0x69, 0xa6, 0xde, 0xde, 0x57, 0xd1, 0x02, 0x62, 0x7f, 0xe4, 0x18, 0xbf, 0x87,
	0x92, 0x4b, 0x6d, 0x93, 0x9d, 0xd1, 0x0b, 0x65, 0x10, 0x55, 0x34, 0x7f, 0x7d, 0x33, 0xbd, 0xd8,
	0xc1, 0x85, 0x94, 0xdd, 0x33, 0x7a, 0x21, 0x5f, 0x22, 0x8a, 0xee, 0x74, 0x82, 0x7e, 0x0e, 0x8f,
	0x08, 0x33, 0x7d, 0x7c, 0x61, 0x0e, 0x19, 0x0e, 0xcd, 0xa1, 0xef, 0xe0, 0x90, 0x5b, 0xe7, 0xaa,
	0x62, 0xe6, 0x0d, 0x44, 0x58, 0x07, 0x5f, 0x9c, 0x30, 0x1c, 0x9e, 0x8c, 0x31, 0xc2, 0xf7, 0x4f,
	0xa9, 0xeb, 0xd2, 0x0b, 0x73, 0x48, 0x1c, 0x95, 0x8d, 0x73, 0x32, 0x39, 0x95, 0x14, 0xf8, 0x84,
	0xc8, 0xf0, 0x44, 0x55, 0x28, 0x05, 0xae, 0x75, 0x39, 0x5d, 0x95, 0x57, 0x09, 0x4e, 0x00, 0xc7,
	0x6b, 0x5e, 0x00, 0xba, 0x42, 0xcb, 0x1c, 0xed, 0xe8, 0x05, 0xb9, 0x70, 0x25, 0x41, 0xee, 0xcd,
	0x8e, 0x20, 0x28, 0xba, 0xda, 0x11, 0x0e, 0x55, 0x71, 0x97, 0xf5, 0x34, 0x6f, 0x14, 0x09, 0x6b,
	0x8c, 0x70, 0x28, 0x8b, 0xfa, 0x6c, 0x1c, 0x15, 0xe7, 0xb4, 0xc7, 0x3f, 0x85, 0x15, 0x87, 0x30,
	0x29, 0x9c, 0x4d, 0x7d, 0x8e, 0x7d, 0x1e, 0x95, 0xc4, 0x72, 0x04, 0xae, 0x2b, 0x28, 0xda, 0x04,
	0x6d, 0xbc, 0x70, 0x72, 0x8a, 0x92, 0x14, 0x6e, 0xbc, 0x32, 0x92, 0xae, 0xfa, 0x06, 0x8a, 0x31,
	0x1d, 0xa3, 0x27, 0xb0, 0x16, 0x9b, 0xc6, 0xde, 0xe4, 0x74, 0x58, 0x8f, 0x23, 0x8e, 0x8d, 0xa3,
	0x37, 0xcd, 0x4e, 0xbd, 0xa1, 0xa5, 0xd1, 0x3a, 0x68, 0x71, 0x4c, 0xbd, 0xd9, 0xfb, 0x5a, 0xcb,
	0x54, 0xff, 0x16, 0xe0, 0xe9, 0x35, 0x97, 0x1a, 0x16, 0x88, 0xa2, 0x3c, 0xce, 0xb8, 0xd2, 0xa7,
	0x85, 0x64, 0x85, 0x08, 0xd2, 0x74, 0x44, 0xeb, 0xd2, 0xa7, 0x9c, 0x53, 0xcf, 0x0c, 0xb1, 0x65,
	0x9f, 0x61, 0xf5, 0x10, 0x97, 0x37, 0x4a, 0x0a, 0x6a, 0x28, 0x20, 0xba, 0x04, 0x6d, 0x42, 0xc5,
	0x3f, 0xa5, 0xa6, 0x67, 0x05, 0x7a, 0x46, 0x26, 0xca, 0xa3, 0x85, 0x6f, 0x5c, 0x2c, 0x88, 0xfb,
	0x5f, 0xdb, 0x0a, 0x1a, 0x3e, 0x0f, 0x2f, 0x8d, 0xb2, 0x9d, 0x00, 0x26, 0xba, 0x92, 0xa5, 0x64,
	0x57, 0xc2, 0xa0, 0xac, 0x1a, 0x8e, 0x89, 0x4c, 0x0f, 0xa5, 0x4c, 0xed, 0xfb, 0xc8, 0xb4, 0x2f,
	0x28, 0x26, 0x24, 0x5a, 0x76, 0x62, 0x20, 0xf4, 0x0a, 0xf2, 0x0c, 0xbb, 0xa7, 0xa6, 0x88, 0xf6,
	0xec, 0x4d, 0xd1, 0x9e, 0x13, 0x4b, 0x5b, 0xd4, 0xbe, 0x52, 0x65, 0x73, 0xf7, 0xa9, 0xb2, 0xcf,
	0x01, 0x88, 0xcf, 0x70, 0xc8, 0xcd, 0x80, 0xb2, 0xa8, 0xd3, 0x2c, 0x28, 0xc8, 0x31, 0x65, 0x68,
	0x00, 0x45, 0x9f, 0x72, 0x79, 0xeb, 0x12, 0xbe, 0x58, 0x90, 0xb9, 0xe5, 0xe0, 0x3e, 0x1a, 0xe9,
	0x48, 0x72, 0x32, 0x31, 0x80, 0x22, 0x2d, 0x03, 0xf3, 0x03, 0x28, 0x9c, 0x52, 0xca, 0x83, 0x90,
	0xf8, 0x5c, 0xc6, 0x59, 0xc1, 0x98, 0x02, 0xd0, 0x5f, 0x42, 0x21, 0xea, 0xe9, 0x58, 0x20, 0x23,
	0xac, 0xb8, 0x53, 0xbb, 0x45, 0x85, 0x10, 0xcd, 0x5d, 0xbd, 0xbd, 0x2f, 0xf8, 0x1a, 0x79, 0xd5,
	0xea, 0xb1, 0x00, 0x7d, 0x9b, 0x82, 0x27, 0x4a, 0xbd, 0xe6, 0x8c, 0x1b, 0x2e, 0x4b, 0x5d, 0x9e,
	0xdc, 0xcb, 0x0d, 0x25, 0xe9, 0x79, 0xce, 0xb8, 0x6e, 0xcf, 0x41, 0xc5, 0xfa, 0x9c, 0xd2, 0xc2,
	0x7d, 0x4e, 0x25, 0x80, 0xb5, 0x39, 0x1c, 0x45, 0x5b, 0x7b, 0x8e, 0x2f, 0xc7, 0xf7, 0xb1, 0x73,
	0x7c, 0x89, 0xea, 0xf0, 0x70, 0x64, 0xb9, 0x43, 0x2c, 0x23, 0xf3, 0x56, 0x57, 0xab, 0x18, 0x5d,
	0x43, 0xed, 0xfd, 0x34, 0xfd, 0xab, 0x54, 0xe5, 0x0b, 0x58, 0x9d, 0x71, 0xee, 0x38, 0xbf, 0x82,
	0xe2, 0xb7, 0x1e, 0xe7, 0x57, 0x88, 0x13, 0xb0, 0xe0, 0xe9, 0xb5, 0xaa, 0x9a, 0x23, 0x78, 0x2d,
	0x29, 0x78, 0xb2, 0xa7, 0x9f, 0x2f, 0x63, 0xf5, 0x33, 0x80, 0xa9, 0xbb, 0x25, 0xdb, 0x80, 0x15,
	0x28, 0xee, 0x1e, 0x1f, 0x1f, 0x35, 0x3b, 0xbd, 0x76, 0xa3, 0xd3, 0xd3, 0x52, 0x02, 0x60, 0xe0,
	0xd3, 0x10, 0xb3, 0xb3, 0xee, 0xd0, 0xb6, 0xb5, 0x74, 0xf5, 0x5f, 0x73, 0xb0, 0x3e, 0xdb, 0x02,
	0xfe, 0x9f, 0x25, 0x41, 0x7e, 0x6d, 0x12, 0xfc, 0x8b, 0x45, 0x5a, 0xd3, 0xfb, 0xe7, 0x3f, 0xff,
	0x9a, 0xfc, 0xf7, 0x7a, 0x41, 0x71, 0xbe, 0xeb, 0xa9, 0xaf, 0x3f, 0x2f, 0xf5, 0xed, 0x2e, 0xa8,
	0x8c, 0x3f, 0x7a, 0xd6, 0xfb, 0x0e, 0x66, 0x88, 0xfb, 0x85, 0xef, 0x3f, 0xa5, 0x61, 0x39, 0xae,
	0x0a, 0x54, 0x86, 0x34, 0x3d, 0x97, 0x8c, 0xf3, 0x46, 0x9a, 0x9e, 0x4f, 0xfe, 0x53, 0x15, 0x6c,
	0x97, 0x92, 0xff, 0xa9, 0x66, 0x26, 0xff, 0xa9, 0x22, 0x07, 0x56, 0x02, 0xd7, 0xf2, 0xcd, 0xbe,
	0xc9, 0x78, 0x28, 0x6e, 0xca, 0x97, 0x32, 0x6e, 0xca, 0x3b, 0x9f, 0xdf, 0xcd, 0x12, 0xb5, 0x63,
	0xd7, 0xf2, 0xf7, 0xba, 0x11, 0x0d, 0x43, 0x74, 0xaa, 0xd3, 0xa9, 0xb8, 0x77, 0x13, 0x66, 0x5a,
	0xee, 0x80, 0xca, 0x76, 0x3b, 0x6f, 0x64, 0x09, 0xdb, 0x75, 0x07, 0xb4, 0xfa, 0x0d, 0x94, 0x12,
	0x1b, 0xd1, 0x53, 0x78, 0x94, 0x00, 0x98, 0xcd, 0xce, 0x9b, 0xdd, 0x56, 0x73, 0x5f, 0x7b, 0x20,
	0x1a, 0xba, 0x24, 0xaa, 0x27, 0x14, 0xa3, 0xc3, 0x7a, 0x12, 0x6a, 0xec, 0x76, 0xf6, 0x8f, 0xda,
	0x5a, 0xba, 0xfa, 0x1f, 0x29, 0x78, 0xda, 0xc5, 0xbc, 0x27, 0x8e, 0x11, 0x99, 0xf0, 0xcb, 0xa1,
	0xe5, 0x12, 0x7e, 0x29, 0x6e, 0x92, 0x37, 0x64, 0x39, 0x02, 0xb9, 0x77, 0x6a, 0xb1, 0x54, 0x60,
	0xf9, 0x36, 0xad, 0xdb, 0xb5, 0xcc, 0x6a, 0xf3, 0xc0, 0x63, 0xfa, 0xd5, 0x17, 0xb0, 0x36, 0x07,
	0x9f, 0xf4, 0x87, 0x1c, 0x64, 0x5a, 0x47, 0x6f, 0xb5, 0x54, 0xf5, 0x03, 0xa8, 0x5c, 0xc7, 0x86,
	0x05, 0xd5, 0x2f, 0xe0, 0x51, 0x9d, 0x7a, 0x81, 0xe5, 0x93, 0x49, 0xd9, 0x69, 0x8c, 0x44, 0xe3,
	0xfd, 0x13, 0x58, 0x99, 0x9e, 0x36, 0xfe, 0x9f, 0x74, 0x69, 0x72, 0x64, 0xd9, 0x76, 0xff, 0x77,
	0x16, 0x56, 0x3b, 0x78, 0x60, 0x71, 0x32, 0xc2, 0x07, 0x18, 0x3b, 0x6a, 0xf7, 0xec, 0xb3, 0xe7,
	0x6f, 0x21, 0x2f, 0xef, 0x37, 0x22, 0x8d, 0xaa, 0xbf, 0x55, 0xff, 0xfc, 0x66, 0xfd, 0xcc, 0x10,
	0xae, 0x89, 0x8b, 0xd0, 0x24, 0x7d, 0xe6, 0x86, 0x6a, 0x86, 0xbe, 0x52, 0xaf, 0x34, 0xd3, 0x8a,
	0xf1, 0xc5, 0x22, 0xb4, 0x7b, 0x56, 0x7f, 0x42, 0x3a, 0xcb, 0xe5, 0x04, 0xbd, 0x9b, 0x16, 0x25,
	0x9b, 0xfa, 0x8e, 0x64, 0xa1, 0xde, 0x60, 0x0f, 0x17, 0x61, 0x11, 0xa9, 0xb8, 0x4e, 0x7d, 0x67,
	0xa6, 0x22, 0x45, 0xc0, 0x38, 0x4b, 0xdf, 0xf2, 0x70, 0xac, 0xf0, 0xdc, 0x87, 0x65, 0xc7, 0xf2,
	0xf0, 0x0c, 0xcb, 0x08, 0x88, 0xce, 0x40, 0x1b, 0x06, 0x8e, 0xc5, 0xb1, 0xbc, 0xb1, 0x29, 0x6b,
	0x67, 0x65, 0x7a, 0xff, 0xcd, 0x42, 0x46, 0x92, 0xb4, 0x64, 0x6e, 0x2f, 0x0f, 0x27, 0x63, 0xe1,
	0x2e, 0x95, 0x4f, 0x61, 0x39, 0x6e, 0xc2, 0x39, 0xd9, 0x36, 0x91, 0xfd, 0x32, 0xf1, 0xf4, 0xf9,
	0x6b, 0x28, 0xc6, 0x4c, 0x74, 0xa7, 0xad, 0xbb, 0x93, 0x5c, 0x1f, 0x57, 0xfd, 0x4d, 0xb9, 0xf7,
	0x1a, 0x12, 0x71, 0x55, 0xde, 0x85, 0x44, 0xb5, 0x03, 0x30, 0x55, 0x8d, 0x08, 0xd7, 0xa6, 0x3f,
	0xb2, 0x5c, 0xe2, 0x68, 0x0f, 0x50, 0x1e, 0x96, 0x84, 0x5e, 0xb4, 0x94, 0x08, 0xdc, 0x9e, 0xd5,
	0xd7, 0xd2, 0x22, 0x81, 0xc7, 0x64, 0xd6, 0x32, 0x31, 0x80, 0x90, 0x40, 0x5b, 0xaa, 0xfe, 0xf3,
	0x43, 0xd0, 0xc4, 0xae, 0xb8, 0x05, 0xd0, 0x37, 0xb1, 0x40, 0x4b, 0xdd, 0x36, 0x18, 0xae, 0x52,
	0xb9, 0x26, 0xce, 0xde, 0x4e, 0xe3, 0x4c, 0xc5, 0xf0, 0x6f, 0x16, 0x20, 0x3d, 0x2f, 0xcc, 0x82,
	0x39, 0x61, 0xa6, 0x22, 0xf9, 0x60, 0x01, 0x0e, 0xb7, 0x89, 0xb2, 0x60, 0x4e, 0x94, 0x2d, 0xdd,
	0x97, 0xe3, 0xff, 0x16, 0x64, 0xdf, 0x6f, 0xd7, 0xdf, 0x85, 0xa7, 0x11, 0x89, 0x1e, 0xe1, 0x2e,
	0xee, 0xd1, 0x73, 0xec, 0x93, 0x3f, 0xe0, 0x49, 0xb5, 0xb0, 0xa7, 0xd5, 0xc2, 0x26, 0x8e, 0x68,
	0x45, 0xb8, 0x35, 0x50, 0x1f, 0xe0, 0x14, 0x0c, 0x39, 0xae, 0xfe, 0x16, 0x9e, 0x1d, 0xbb, 0xd6,
	0xa5, 0x67, 0x71, 0xdc, 0x94, 0x8f, 0x43, 0xf2, 0x8e, 0xe9, 0x11, 0xae, 0x88, 0x5c, 0x2d, 0xcf,
	0xa9, 0x64, 0x79, 0x7e, 0x0e, 0x20, 0xff, 0x69, 0x33, 0x39, 0xf1, 0xc6, 0xf2, 0x15, 0x24, 0xa4,
	0x47, 0x3c, 0x5c, 0x6d, 0x43, 0x65, 0xe6, 0x02, 0x6b, 0x60, 0x8b, 0xc9, 0x4f, 0xa3, 0xe6, 0x94,
	0xb3, 0x24, 0xb7, 0xf4, 0x15, 0x6e, 0xd5, 0x4f, 0xe0, 0xd9, 0xb5, 0xe4, 0x58, 0x80, 0x1e, 0x43,
	0x36, 0x94, 0xb3, 0x88, 0x64, 0x34, 0xdb, 0x72, 0x61, 0x7d, 0xde, 0x07, 0x54, 0xc9, 0xca, 0xfe,
	0x04, 0xd6, 0x0c, 0x3c, 0x68, 0x61, 0xc6, 0x7e, 0xb9, 0xf3, 0x9a, 0x0e, 0x43, 0xb3, 0xf1, 0x3e,
	0x30, 0x77, 0xb5, 0xd4, 0x7c, 0xc4, 0x9e, 0x96, 0x46, 0x8f, 0x60, 0xd5, 0xc0, 0x83, 0x36, 0x0d,
	0xf1, 0x14, 0xa1, 0x65, 0xb6, 0x6a, 0xa0, 0x5d, 0xfd, 0x63, 0x45, 0xe4, 0xa1, 0x03, 0xa3, 0xd1,
	0xd0, 0x1e, 0x20, 0x0d, 0x96, 0xbb, 0xcd, 0xf6, 0x71, 0xab, 0x61, 0xb6, 0x9b, 0x9d, 0x23, 0x43,
	0x4b, 0x6d, 0xbd, 0x80, 0xd5, 0x99, 0xaf, 0xaa, 0x10, 0x40, 0xb6, 0xfb, 0x75, 0xb7, 0xd7, 0x68,
	0x6b, 0x0f, 0xc4, 0xb8, 0x7e, 0xd2, 0xed, 0x1d, 0xb5, 0xb5, 0xd4, 0xd6, 0x2b, 0x58, 0x9d, 0x79,
	0xe2, 0x46, 0xcb, 0x90, 0x37, 0x28, 0xf5, 0xda, 0xd4, 0xc1, 0xda, 0x03, 0xb4, 0x0a, 0x25, 0x41,
	0x4b, 0xb8, 0x25, 0x91, 0xaf, 0xc8, 0xa9, 0xad, 0x2f, 0x41, 0xbb, 0xfa, 0x8a, 0x2d, 0x0e, 0xdf,
	0xa1, 0xd2, 0xd0, 0x8a, 0x45, 0x97, 0xf8, 0x03, 0x17, 0x6b, 0x29, 0x31, 0xde, 0xa7, 0xc3, 0xbe,
	0x8b, 0xb5, 0xb4, 0x58, 0xf4, 0xd6, 0x22, 0x9c, 0xf8, 0x03, 0x2d, 0x23, 0x26, 0x5d, 0x6e, 0x85,
	0x1c, 0x3b, 0xda, 0xd2, 0xd6, 0x7f, 0xa6, 0xa1, 0x30, 0x79, 0xd8, 0x15, 0xe7, 0xeb, 0x50, 0x1f,
	0x2b, 0x35, 0x1e, 0x5a, 0xfe, 0xe0, 0x24, 0x78, 0x4b, 0xf8, 0x99, 0xb8, 0x9f, 0x1c, 0x5d, 0xf8,
	0x32, 0x01, 0x3f, 0x82, 0xd5, 0xfa, 0x99, 0xc5, 0x93, 0xe0, 0xb4, 0x58, 0x7f, 0xa0, 0x9e, 0x43,
	0x19, 0x0e, 0x9b, 0xe3, 0x66, 0x49, 0x71, 0x8b, 0x9e, 0x0d, 0xb5, 0x25, 0xd1, 0x8d, 0xb6, 0xf9,
	0xbc, 0x75, 0x0f, 0xd1, 0x63, 0x40, 0x6d, 0x3e, 0x79, 0x27, 0x1d, 0xc3, 0xb3, 0xa2, 0x1f, 0x6d,
	0xf3, 0xb6, 0xe5, 0x5f, 0x8a, 0x0d, 0x6c, 0xba, 0x23, 0x87, 0x3e, 0x82, 0xe7, 0x31, 0x8c, 0xa2,
	0x1a, 0x61, 0x95, 0x54, 0x79, 0xf4, 0x23, 0xa8, 0xb4, 0xf9, 0xe1, 0x10, 0x33, 0xf6, 0x35, 0x1d,
	0xb6, 0xc8, 0x39, 0x4e, 0xe0, 0x0b, 0xe8, 0x43, 0x78, 0xd6, 0x16, 0x7e, 0x88, 0x7d, 0x2e, 0xb0,
	0x3d, 0xab, 0x1f, 0x2d, 0xd8, 0xa7, 0xc3, 0x7d, 0xa2, 0x01, 0xfa, 0x00, 0xf4, 0x36, 0x7f, 0x4d,
	0xb9, 0xb0, 0xc4, 0x6b, 0xca, 0x13, 0xd8, 0xa2, 0xe8, 0xa0, 0xdb, 0xfc, 0x20, 0x24, 0xd8, 0x77,
	0x8e, 0x4e, 0xd5, 0xaf, 0xb6, 0x8c, 0x10, 0x94, 0xc7, 0x44, 0x95, 0x50, 0x5a, 0x69, 0xeb, 0x15,
	0x80, 0xea, 0xec, 0x85, 0x23, 0xab, 0xcb, 0x87, 0x18, 0x99, 0x91, 0xb6, 0xa5, 0x0b, 0x4a, 0x40,
	0xc4, 0xab, 0x47, 0x03, 0x2d, 0xb5, 0xf3, 0xef, 0x39, 0x58, 0x3d, 0xc4, 0x7e, 0x32, 0x50, 0xd0,
	0xdf, 0xa5, 0xe0, 0xd1, 0xdc, 0xe7, 0x24, 0xf4, 0xe9, 0xe2, 0x1f, 0xa0, 0x54, 0x3e, 0xbb, 0xc7,
	0x1b, 0x16, 0xfa, 0x36, 0x25, 0x0e, 0x60, 0x5b, 0xae, 0x1b, 0x0b, 0xcf, 0x3f, 0x9e, 0x38, 0x7f,
	0x9f, 0x82, 0xc7, 0xf3, 0xbb, 0x7a, 0xf4, 0xd9, 0x3d, 0xae, 0x1d, 0x95, 0xcf, 0x17, 0xdf, 0xcc,
	0x02, 0x44, 0x01, 0xa6, 0xdf, 0xb7, 0xa2, 0xed, 0x3b, 0x7e, 0x5a, 0x5b, 0xf9, 0xd9, 0xdd, 0x36,
	0xb0, 0x00, 0x85, 0xc2, 0xcd, 0x26, 0x1f, 0x06, 0xa3, 0x5b, 0x10, 0x48, 0x7e, 0x6b, 0x5c, 0xf9,
	0xf9, 0x1d, 0x77, 0xb0, 0x00, 0xfd, 0x4d, 0x0a, 0xb4, 0xab, 0x2f, 0x1a, 0xe8, 0x93, 0x85, 0xfe,
	0x3c, 0xad, 0xfc, 0x62, 0xb1, 0xc7, 0x13, 0xf4, 0x8f, 0x29, 0x78, 0x72, 0x4d, 0x7d, 0x41, 0x9f,
	0x2f, 0xe0, 0x57, 0x93, 0x4a, 0x57, 0xf9, 0xb3, 0x7b, 0xec, 0x66, 0xc1, 0xde, 0xfe, 0x37, 0x7b,
	0x03, 0xea, 0x5a, 0xfe, 0xa0, 0xf6, 0xc9, 0x0e, 0xe7, 0x35, 0x9b, 0x7a, 0xdb, 0xf2, 0x33, 0x6f,
	0x9b, 0xba, 0xdb, 0x0c, 0x87, 0x23, 0x62, 0x63, 0x76, 0xe3, 0x17, 0xf1, 0xfd, 0xac, 0xdc, 0xf3,
	0xf2, 0x7f, 0x02, 0x00, 0x00, 0xff, 0xff, 0x8d, 0x13, 0x14, 0x55, 0x4b, 0x2f, 0x00, 0x00,
}
