// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/channelbox/channelbox.proto

package channelbox

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockChannelBoxClient is a mock of ChannelBoxClient interface.
type MockChannelBoxClient struct {
	ctrl     *gomock.Controller
	recorder *MockChannelBoxClientMockRecorder
}

// MockChannelBoxClientMockRecorder is the mock recorder for MockChannelBoxClient.
type MockChannelBoxClientMockRecorder struct {
	mock *MockChannelBoxClient
}

// NewMockChannelBoxClient creates a new mock instance.
func NewMockChannelBoxClient(ctrl *gomock.Controller) *MockChannelBoxClient {
	mock := &MockChannelBoxClient{ctrl: ctrl}
	mock.recorder = &MockChannelBoxClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelBoxClient) EXPECT() *MockChannelBoxClientMockRecorder {
	return m.recorder
}

// AddMainChannelUser mocks base method.
func (m *MockChannelBoxClient) AddMainChannelUser(ctx context.Context, in *AddMainChannelUserReq, opts ...grpc.CallOption) (*AddMainChannelUserResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddMainChannelUser", varargs...)
	ret0, _ := ret[0].(*AddMainChannelUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddMainChannelUser indicates an expected call of AddMainChannelUser.
func (mr *MockChannelBoxClientMockRecorder) AddMainChannelUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMainChannelUser", reflect.TypeOf((*MockChannelBoxClient)(nil).AddMainChannelUser), varargs...)
}

// ApplyOnMicToken mocks base method.
func (m *MockChannelBoxClient) ApplyOnMicToken(ctx context.Context, in *ApplyOnMicTokenReq, opts ...grpc.CallOption) (*ApplyOnMicTokenResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ApplyOnMicToken", varargs...)
	ret0, _ := ret[0].(*ApplyOnMicTokenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplyOnMicToken indicates an expected call of ApplyOnMicToken.
func (mr *MockChannelBoxClientMockRecorder) ApplyOnMicToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyOnMicToken", reflect.TypeOf((*MockChannelBoxClient)(nil).ApplyOnMicToken), varargs...)
}

// BatchGetBoxInfos mocks base method.
func (m *MockChannelBoxClient) BatchGetBoxInfos(ctx context.Context, in *BatchGetBoxInfosReq, opts ...grpc.CallOption) (*BatchGetBoxInfosResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetBoxInfos", varargs...)
	ret0, _ := ret[0].(*BatchGetBoxInfosResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetBoxInfos indicates an expected call of BatchGetBoxInfos.
func (mr *MockChannelBoxClientMockRecorder) BatchGetBoxInfos(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBoxInfos", reflect.TypeOf((*MockChannelBoxClient)(nil).BatchGetBoxInfos), varargs...)
}

// ClearJoinBox mocks base method.
func (m *MockChannelBoxClient) ClearJoinBox(ctx context.Context, in *ClearJoinBoxReq, opts ...grpc.CallOption) (*ClearJoinBoxResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClearJoinBox", varargs...)
	ret0, _ := ret[0].(*ClearJoinBoxResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearJoinBox indicates an expected call of ClearJoinBox.
func (mr *MockChannelBoxClientMockRecorder) ClearJoinBox(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearJoinBox", reflect.TypeOf((*MockChannelBoxClient)(nil).ClearJoinBox), varargs...)
}

// DelBoxInfo mocks base method.
func (m *MockChannelBoxClient) DelBoxInfo(ctx context.Context, in *DelBoxInfoReq, opts ...grpc.CallOption) (*DelBoxInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelBoxInfo", varargs...)
	ret0, _ := ret[0].(*DelBoxInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelBoxInfo indicates an expected call of DelBoxInfo.
func (mr *MockChannelBoxClientMockRecorder) DelBoxInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelBoxInfo", reflect.TypeOf((*MockChannelBoxClient)(nil).DelBoxInfo), varargs...)
}

// EnterBox mocks base method.
func (m *MockChannelBoxClient) EnterBox(ctx context.Context, in *EnterBoxReq, opts ...grpc.CallOption) (*EnterBoxResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "EnterBox", varargs...)
	ret0, _ := ret[0].(*EnterBoxResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EnterBox indicates an expected call of EnterBox.
func (mr *MockChannelBoxClientMockRecorder) EnterBox(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnterBox", reflect.TypeOf((*MockChannelBoxClient)(nil).EnterBox), varargs...)
}

// ExitBox mocks base method.
func (m *MockChannelBoxClient) ExitBox(ctx context.Context, in *ExitBoxReq, opts ...grpc.CallOption) (*ExitBoxResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ExitBox", varargs...)
	ret0, _ := ret[0].(*ExitBoxResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExitBox indicates an expected call of ExitBox.
func (mr *MockChannelBoxClientMockRecorder) ExitBox(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExitBox", reflect.TypeOf((*MockChannelBoxClient)(nil).ExitBox), varargs...)
}

// ExitChannel mocks base method.
func (m *MockChannelBoxClient) ExitChannel(ctx context.Context, in *ExitChannelReq, opts ...grpc.CallOption) (*ExitChannelResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ExitChannel", varargs...)
	ret0, _ := ret[0].(*ExitChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExitChannel indicates an expected call of ExitChannel.
func (mr *MockChannelBoxClientMockRecorder) ExitChannel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExitChannel", reflect.TypeOf((*MockChannelBoxClient)(nil).ExitChannel), varargs...)
}

// GetBoxInfo mocks base method.
func (m *MockChannelBoxClient) GetBoxInfo(ctx context.Context, in *GetBoxInfoReq, opts ...grpc.CallOption) (*GetBoxInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBoxInfo", varargs...)
	ret0, _ := ret[0].(*GetBoxInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBoxInfo indicates an expected call of GetBoxInfo.
func (mr *MockChannelBoxClientMockRecorder) GetBoxInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBoxInfo", reflect.TypeOf((*MockChannelBoxClient)(nil).GetBoxInfo), varargs...)
}

// GetBoxInfosByLimit mocks base method.
func (m *MockChannelBoxClient) GetBoxInfosByLimit(ctx context.Context, in *GetBoxInfosByLimitReq, opts ...grpc.CallOption) (*GetBoxInfosByLimitResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBoxInfosByLimit", varargs...)
	ret0, _ := ret[0].(*GetBoxInfosByLimitResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBoxInfosByLimit indicates an expected call of GetBoxInfosByLimit.
func (mr *MockChannelBoxClientMockRecorder) GetBoxInfosByLimit(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBoxInfosByLimit", reflect.TypeOf((*MockChannelBoxClient)(nil).GetBoxInfosByLimit), varargs...)
}

// GetBoxUserInMicInfos mocks base method.
func (m *MockChannelBoxClient) GetBoxUserInMicInfos(ctx context.Context, in *GetBoxUserInMicInfosReq, opts ...grpc.CallOption) (*GetBoxUserInMicInfosResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBoxUserInMicInfos", varargs...)
	ret0, _ := ret[0].(*GetBoxUserInMicInfosResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBoxUserInMicInfos indicates an expected call of GetBoxUserInMicInfos.
func (mr *MockChannelBoxClientMockRecorder) GetBoxUserInMicInfos(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBoxUserInMicInfos", reflect.TypeOf((*MockChannelBoxClient)(nil).GetBoxUserInMicInfos), varargs...)
}

// GetMainChannelUsers mocks base method.
func (m *MockChannelBoxClient) GetMainChannelUsers(ctx context.Context, in *GetMainChannelUsersReq, opts ...grpc.CallOption) (*GetMainChannelUsersResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMainChannelUsers", varargs...)
	ret0, _ := ret[0].(*GetMainChannelUsersResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMainChannelUsers indicates an expected call of GetMainChannelUsers.
func (mr *MockChannelBoxClientMockRecorder) GetMainChannelUsers(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMainChannelUsers", reflect.TypeOf((*MockChannelBoxClient)(nil).GetMainChannelUsers), varargs...)
}

// GetMicApplyRecords mocks base method.
func (m *MockChannelBoxClient) GetMicApplyRecords(ctx context.Context, in *GetMicApplyRecordsReq, opts ...grpc.CallOption) (*GetMicApplyRecordsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMicApplyRecords", varargs...)
	ret0, _ := ret[0].(*GetMicApplyRecordsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMicApplyRecords indicates an expected call of GetMicApplyRecords.
func (mr *MockChannelBoxClientMockRecorder) GetMicApplyRecords(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMicApplyRecords", reflect.TypeOf((*MockChannelBoxClient)(nil).GetMicApplyRecords), varargs...)
}

// JoinBox mocks base method.
func (m *MockChannelBoxClient) JoinBox(ctx context.Context, in *JoinBoxReq, opts ...grpc.CallOption) (*JoinBoxResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "JoinBox", varargs...)
	ret0, _ := ret[0].(*JoinBoxResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// JoinBox indicates an expected call of JoinBox.
func (mr *MockChannelBoxClientMockRecorder) JoinBox(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JoinBox", reflect.TypeOf((*MockChannelBoxClient)(nil).JoinBox), varargs...)
}

// SetChannelMicBoxId mocks base method.
func (m *MockChannelBoxClient) SetChannelMicBoxId(ctx context.Context, in *SetChannelMicBoxIdReq, opts ...grpc.CallOption) (*SetChannelMicBoxIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetChannelMicBoxId", varargs...)
	ret0, _ := ret[0].(*SetChannelMicBoxIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChannelMicBoxId indicates an expected call of SetChannelMicBoxId.
func (mr *MockChannelBoxClientMockRecorder) SetChannelMicBoxId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelMicBoxId", reflect.TypeOf((*MockChannelBoxClient)(nil).SetChannelMicBoxId), varargs...)
}

// SetMicApplyRecords mocks base method.
func (m *MockChannelBoxClient) SetMicApplyRecords(ctx context.Context, in *SetMicApplyRecordsReq, opts ...grpc.CallOption) (*SetMicApplyRecordsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetMicApplyRecords", varargs...)
	ret0, _ := ret[0].(*SetMicApplyRecordsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetMicApplyRecords indicates an expected call of SetMicApplyRecords.
func (mr *MockChannelBoxClientMockRecorder) SetMicApplyRecords(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMicApplyRecords", reflect.TypeOf((*MockChannelBoxClient)(nil).SetMicApplyRecords), varargs...)
}

// UpsertBoxInfo mocks base method.
func (m *MockChannelBoxClient) UpsertBoxInfo(ctx context.Context, in *UpsertBoxInfoReq, opts ...grpc.CallOption) (*UpsertBoxInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpsertBoxInfo", varargs...)
	ret0, _ := ret[0].(*UpsertBoxInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertBoxInfo indicates an expected call of UpsertBoxInfo.
func (mr *MockChannelBoxClientMockRecorder) UpsertBoxInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertBoxInfo", reflect.TypeOf((*MockChannelBoxClient)(nil).UpsertBoxInfo), varargs...)
}

// MockChannelBoxServer is a mock of ChannelBoxServer interface.
type MockChannelBoxServer struct {
	ctrl     *gomock.Controller
	recorder *MockChannelBoxServerMockRecorder
}

// MockChannelBoxServerMockRecorder is the mock recorder for MockChannelBoxServer.
type MockChannelBoxServerMockRecorder struct {
	mock *MockChannelBoxServer
}

// NewMockChannelBoxServer creates a new mock instance.
func NewMockChannelBoxServer(ctrl *gomock.Controller) *MockChannelBoxServer {
	mock := &MockChannelBoxServer{ctrl: ctrl}
	mock.recorder = &MockChannelBoxServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelBoxServer) EXPECT() *MockChannelBoxServerMockRecorder {
	return m.recorder
}

// AddMainChannelUser mocks base method.
func (m *MockChannelBoxServer) AddMainChannelUser(ctx context.Context, in *AddMainChannelUserReq) (*AddMainChannelUserResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddMainChannelUser", ctx, in)
	ret0, _ := ret[0].(*AddMainChannelUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddMainChannelUser indicates an expected call of AddMainChannelUser.
func (mr *MockChannelBoxServerMockRecorder) AddMainChannelUser(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMainChannelUser", reflect.TypeOf((*MockChannelBoxServer)(nil).AddMainChannelUser), ctx, in)
}

// ApplyOnMicToken mocks base method.
func (m *MockChannelBoxServer) ApplyOnMicToken(ctx context.Context, in *ApplyOnMicTokenReq) (*ApplyOnMicTokenResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplyOnMicToken", ctx, in)
	ret0, _ := ret[0].(*ApplyOnMicTokenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplyOnMicToken indicates an expected call of ApplyOnMicToken.
func (mr *MockChannelBoxServerMockRecorder) ApplyOnMicToken(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyOnMicToken", reflect.TypeOf((*MockChannelBoxServer)(nil).ApplyOnMicToken), ctx, in)
}

// BatchGetBoxInfos mocks base method.
func (m *MockChannelBoxServer) BatchGetBoxInfos(ctx context.Context, in *BatchGetBoxInfosReq) (*BatchGetBoxInfosResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetBoxInfos", ctx, in)
	ret0, _ := ret[0].(*BatchGetBoxInfosResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetBoxInfos indicates an expected call of BatchGetBoxInfos.
func (mr *MockChannelBoxServerMockRecorder) BatchGetBoxInfos(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBoxInfos", reflect.TypeOf((*MockChannelBoxServer)(nil).BatchGetBoxInfos), ctx, in)
}

// ClearJoinBox mocks base method.
func (m *MockChannelBoxServer) ClearJoinBox(ctx context.Context, in *ClearJoinBoxReq) (*ClearJoinBoxResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearJoinBox", ctx, in)
	ret0, _ := ret[0].(*ClearJoinBoxResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearJoinBox indicates an expected call of ClearJoinBox.
func (mr *MockChannelBoxServerMockRecorder) ClearJoinBox(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearJoinBox", reflect.TypeOf((*MockChannelBoxServer)(nil).ClearJoinBox), ctx, in)
}

// DelBoxInfo mocks base method.
func (m *MockChannelBoxServer) DelBoxInfo(ctx context.Context, in *DelBoxInfoReq) (*DelBoxInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelBoxInfo", ctx, in)
	ret0, _ := ret[0].(*DelBoxInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelBoxInfo indicates an expected call of DelBoxInfo.
func (mr *MockChannelBoxServerMockRecorder) DelBoxInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelBoxInfo", reflect.TypeOf((*MockChannelBoxServer)(nil).DelBoxInfo), ctx, in)
}

// EnterBox mocks base method.
func (m *MockChannelBoxServer) EnterBox(ctx context.Context, in *EnterBoxReq) (*EnterBoxResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnterBox", ctx, in)
	ret0, _ := ret[0].(*EnterBoxResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EnterBox indicates an expected call of EnterBox.
func (mr *MockChannelBoxServerMockRecorder) EnterBox(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnterBox", reflect.TypeOf((*MockChannelBoxServer)(nil).EnterBox), ctx, in)
}

// ExitBox mocks base method.
func (m *MockChannelBoxServer) ExitBox(ctx context.Context, in *ExitBoxReq) (*ExitBoxResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExitBox", ctx, in)
	ret0, _ := ret[0].(*ExitBoxResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExitBox indicates an expected call of ExitBox.
func (mr *MockChannelBoxServerMockRecorder) ExitBox(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExitBox", reflect.TypeOf((*MockChannelBoxServer)(nil).ExitBox), ctx, in)
}

// ExitChannel mocks base method.
func (m *MockChannelBoxServer) ExitChannel(ctx context.Context, in *ExitChannelReq) (*ExitChannelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExitChannel", ctx, in)
	ret0, _ := ret[0].(*ExitChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExitChannel indicates an expected call of ExitChannel.
func (mr *MockChannelBoxServerMockRecorder) ExitChannel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExitChannel", reflect.TypeOf((*MockChannelBoxServer)(nil).ExitChannel), ctx, in)
}

// GetBoxInfo mocks base method.
func (m *MockChannelBoxServer) GetBoxInfo(ctx context.Context, in *GetBoxInfoReq) (*GetBoxInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBoxInfo", ctx, in)
	ret0, _ := ret[0].(*GetBoxInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBoxInfo indicates an expected call of GetBoxInfo.
func (mr *MockChannelBoxServerMockRecorder) GetBoxInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBoxInfo", reflect.TypeOf((*MockChannelBoxServer)(nil).GetBoxInfo), ctx, in)
}

// GetBoxInfosByLimit mocks base method.
func (m *MockChannelBoxServer) GetBoxInfosByLimit(ctx context.Context, in *GetBoxInfosByLimitReq) (*GetBoxInfosByLimitResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBoxInfosByLimit", ctx, in)
	ret0, _ := ret[0].(*GetBoxInfosByLimitResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBoxInfosByLimit indicates an expected call of GetBoxInfosByLimit.
func (mr *MockChannelBoxServerMockRecorder) GetBoxInfosByLimit(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBoxInfosByLimit", reflect.TypeOf((*MockChannelBoxServer)(nil).GetBoxInfosByLimit), ctx, in)
}

// GetBoxUserInMicInfos mocks base method.
func (m *MockChannelBoxServer) GetBoxUserInMicInfos(ctx context.Context, in *GetBoxUserInMicInfosReq) (*GetBoxUserInMicInfosResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBoxUserInMicInfos", ctx, in)
	ret0, _ := ret[0].(*GetBoxUserInMicInfosResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBoxUserInMicInfos indicates an expected call of GetBoxUserInMicInfos.
func (mr *MockChannelBoxServerMockRecorder) GetBoxUserInMicInfos(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBoxUserInMicInfos", reflect.TypeOf((*MockChannelBoxServer)(nil).GetBoxUserInMicInfos), ctx, in)
}

// GetMainChannelUsers mocks base method.
func (m *MockChannelBoxServer) GetMainChannelUsers(ctx context.Context, in *GetMainChannelUsersReq) (*GetMainChannelUsersResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMainChannelUsers", ctx, in)
	ret0, _ := ret[0].(*GetMainChannelUsersResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMainChannelUsers indicates an expected call of GetMainChannelUsers.
func (mr *MockChannelBoxServerMockRecorder) GetMainChannelUsers(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMainChannelUsers", reflect.TypeOf((*MockChannelBoxServer)(nil).GetMainChannelUsers), ctx, in)
}

// GetMicApplyRecords mocks base method.
func (m *MockChannelBoxServer) GetMicApplyRecords(ctx context.Context, in *GetMicApplyRecordsReq) (*GetMicApplyRecordsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMicApplyRecords", ctx, in)
	ret0, _ := ret[0].(*GetMicApplyRecordsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMicApplyRecords indicates an expected call of GetMicApplyRecords.
func (mr *MockChannelBoxServerMockRecorder) GetMicApplyRecords(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMicApplyRecords", reflect.TypeOf((*MockChannelBoxServer)(nil).GetMicApplyRecords), ctx, in)
}

// JoinBox mocks base method.
func (m *MockChannelBoxServer) JoinBox(ctx context.Context, in *JoinBoxReq) (*JoinBoxResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JoinBox", ctx, in)
	ret0, _ := ret[0].(*JoinBoxResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// JoinBox indicates an expected call of JoinBox.
func (mr *MockChannelBoxServerMockRecorder) JoinBox(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JoinBox", reflect.TypeOf((*MockChannelBoxServer)(nil).JoinBox), ctx, in)
}

// SetChannelMicBoxId mocks base method.
func (m *MockChannelBoxServer) SetChannelMicBoxId(ctx context.Context, in *SetChannelMicBoxIdReq) (*SetChannelMicBoxIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelMicBoxId", ctx, in)
	ret0, _ := ret[0].(*SetChannelMicBoxIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChannelMicBoxId indicates an expected call of SetChannelMicBoxId.
func (mr *MockChannelBoxServerMockRecorder) SetChannelMicBoxId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelMicBoxId", reflect.TypeOf((*MockChannelBoxServer)(nil).SetChannelMicBoxId), ctx, in)
}

// SetMicApplyRecords mocks base method.
func (m *MockChannelBoxServer) SetMicApplyRecords(ctx context.Context, in *SetMicApplyRecordsReq) (*SetMicApplyRecordsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMicApplyRecords", ctx, in)
	ret0, _ := ret[0].(*SetMicApplyRecordsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetMicApplyRecords indicates an expected call of SetMicApplyRecords.
func (mr *MockChannelBoxServerMockRecorder) SetMicApplyRecords(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMicApplyRecords", reflect.TypeOf((*MockChannelBoxServer)(nil).SetMicApplyRecords), ctx, in)
}

// UpsertBoxInfo mocks base method.
func (m *MockChannelBoxServer) UpsertBoxInfo(ctx context.Context, in *UpsertBoxInfoReq) (*UpsertBoxInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertBoxInfo", ctx, in)
	ret0, _ := ret[0].(*UpsertBoxInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertBoxInfo indicates an expected call of UpsertBoxInfo.
func (mr *MockChannelBoxServerMockRecorder) UpsertBoxInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertBoxInfo", reflect.TypeOf((*MockChannelBoxServer)(nil).UpsertBoxInfo), ctx, in)
}
