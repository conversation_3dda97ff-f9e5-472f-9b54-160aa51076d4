// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ugc/event.proto

package event // import "golang.52tt.com/protocol/services/ugc/event"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type Action int32

const (
	Action_ACTION_UNDEFINED                 Action = 0
	Action_ACTION_BAN_WITH_NORMAL_STATUS    Action = 1
	Action_ACTION_DELETE_WITH_NORMAL_STATUS Action = 2
	Action_ACTION_CHANGE_PRIVACY_POLICY     Action = 3
	Action_ACTION_PUBLISH                   Action = 4
	Action_ACTION_AUDIT_RESULT              Action = 5
	Action_ACTION_CHANGE_POST               Action = 6
)

var Action_name = map[int32]string{
	0: "ACTION_UNDEFINED",
	1: "ACTION_BAN_WITH_NORMAL_STATUS",
	2: "ACTION_DELETE_WITH_NORMAL_STATUS",
	3: "ACTION_CHANGE_PRIVACY_POLICY",
	4: "ACTION_PUBLISH",
	5: "ACTION_AUDIT_RESULT",
	6: "ACTION_CHANGE_POST",
}
var Action_value = map[string]int32{
	"ACTION_UNDEFINED":                 0,
	"ACTION_BAN_WITH_NORMAL_STATUS":    1,
	"ACTION_DELETE_WITH_NORMAL_STATUS": 2,
	"ACTION_CHANGE_PRIVACY_POLICY":     3,
	"ACTION_PUBLISH":                   4,
	"ACTION_AUDIT_RESULT":              5,
	"ACTION_CHANGE_POST":               6,
}

func (x Action) String() string {
	return proto.EnumName(Action_name, int32(x))
}
func (Action) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{0}
}

type PostEvent_Origin int32

const (
	PostEvent_NORMAL  PostEvent_Origin = 0
	PostEvent_BACKEND PostEvent_Origin = 1
)

var PostEvent_Origin_name = map[int32]string{
	0: "NORMAL",
	1: "BACKEND",
}
var PostEvent_Origin_value = map[string]int32{
	"NORMAL":  0,
	"BACKEND": 1,
}

func (x PostEvent_Origin) String() string {
	return proto.EnumName(PostEvent_Origin_name, int32(x))
}
func (PostEvent_Origin) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{2, 0}
}

type PostEvent_BusinessType int32

const (
	PostEvent_NORMAL_UGC        PostEvent_BusinessType = 0
	PostEvent_CHANNEL_PLAY_GAME PostEvent_BusinessType = 1
)

var PostEvent_BusinessType_name = map[int32]string{
	0: "NORMAL_UGC",
	1: "CHANNEL_PLAY_GAME",
}
var PostEvent_BusinessType_value = map[string]int32{
	"NORMAL_UGC":        0,
	"CHANNEL_PLAY_GAME": 1,
}

func (x PostEvent_BusinessType) String() string {
	return proto.EnumName(PostEvent_BusinessType_name, int32(x))
}
func (PostEvent_BusinessType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{2, 1}
}

type PostEvent_ZonePolicy int32

const (
	PostEvent_DEFAULT   PostEvent_ZonePolicy = 0
	PostEvent_ONLY_ZONE PostEvent_ZonePolicy = 1
)

var PostEvent_ZonePolicy_name = map[int32]string{
	0: "DEFAULT",
	1: "ONLY_ZONE",
}
var PostEvent_ZonePolicy_value = map[string]int32{
	"DEFAULT":   0,
	"ONLY_ZONE": 1,
}

func (x PostEvent_ZonePolicy) String() string {
	return proto.EnumName(PostEvent_ZonePolicy_name, int32(x))
}
func (PostEvent_ZonePolicy) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{2, 2}
}

type FollowEvent_Source int32

const (
	FollowEvent_USER_OPERATION FollowEvent_Source = 0
	FollowEvent_SYNCHORNIZER   FollowEvent_Source = 1
	FollowEvent_FRIEND_VERIFY  FollowEvent_Source = 2
)

var FollowEvent_Source_name = map[int32]string{
	0: "USER_OPERATION",
	1: "SYNCHORNIZER",
	2: "FRIEND_VERIFY",
}
var FollowEvent_Source_value = map[string]int32{
	"USER_OPERATION": 0,
	"SYNCHORNIZER":   1,
	"FRIEND_VERIFY":  2,
}

func (x FollowEvent_Source) String() string {
	return proto.EnumName(FollowEvent_Source_name, int32(x))
}
func (FollowEvent_Source) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{5, 0}
}

type AssociatePostWithTopicEvent_Operation int32

const (
	AssociatePostWithTopicEvent_ADD AssociatePostWithTopicEvent_Operation = 0
	AssociatePostWithTopicEvent_DEL AssociatePostWithTopicEvent_Operation = 1
)

var AssociatePostWithTopicEvent_Operation_name = map[int32]string{
	0: "ADD",
	1: "DEL",
}
var AssociatePostWithTopicEvent_Operation_value = map[string]int32{
	"ADD": 0,
	"DEL": 1,
}

func (x AssociatePostWithTopicEvent_Operation) String() string {
	return proto.EnumName(AssociatePostWithTopicEvent_Operation_name, int32(x))
}
func (AssociatePostWithTopicEvent_Operation) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{13, 0}
}

type CommonTopicInfo struct {
	TopicIds             []string `protobuf:"bytes,1,rep,name=topic_ids,json=topicIds,proto3" json:"topic_ids,omitempty"`
	TopicType            uint32   `protobuf:"varint,2,opt,name=topic_type,json=topicType,proto3" json:"topic_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommonTopicInfo) Reset()         { *m = CommonTopicInfo{} }
func (m *CommonTopicInfo) String() string { return proto.CompactTextString(m) }
func (*CommonTopicInfo) ProtoMessage()    {}
func (*CommonTopicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{0}
}
func (m *CommonTopicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonTopicInfo.Unmarshal(m, b)
}
func (m *CommonTopicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonTopicInfo.Marshal(b, m, deterministic)
}
func (dst *CommonTopicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonTopicInfo.Merge(dst, src)
}
func (m *CommonTopicInfo) XXX_Size() int {
	return xxx_messageInfo_CommonTopicInfo.Size(m)
}
func (m *CommonTopicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonTopicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CommonTopicInfo proto.InternalMessageInfo

func (m *CommonTopicInfo) GetTopicIds() []string {
	if m != nil {
		return m.TopicIds
	}
	return nil
}

func (m *CommonTopicInfo) GetTopicType() uint32 {
	if m != nil {
		return m.TopicType
	}
	return 0
}

type GeneralContent struct {
	// enum UnmarshalType {
	// UNMARSHAL_TYPE_DEFAULT = 0;
	// UNMARSHAL_TYPE_PROTOBUF = 1; //protobuf
	// UNMARSHAL_TYPE_JSON = 2; //json
	// }
	//
	// enum ContentOrigin {
	// ORIGIN_DEFAULT = 0;
	// ORIGIN_GAME_DISTRICT = 1; //游戏专区
	// }
	UnmarshalType        uint32   `protobuf:"varint,1,opt,name=unmarshal_type,json=unmarshalType,proto3" json:"unmarshal_type,omitempty"`
	ContentOrigin        uint32   `protobuf:"varint,2,opt,name=content_origin,json=contentOrigin,proto3" json:"content_origin,omitempty"`
	Content              []byte   `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GeneralContent) Reset()         { *m = GeneralContent{} }
func (m *GeneralContent) String() string { return proto.CompactTextString(m) }
func (*GeneralContent) ProtoMessage()    {}
func (*GeneralContent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{1}
}
func (m *GeneralContent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GeneralContent.Unmarshal(m, b)
}
func (m *GeneralContent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GeneralContent.Marshal(b, m, deterministic)
}
func (dst *GeneralContent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GeneralContent.Merge(dst, src)
}
func (m *GeneralContent) XXX_Size() int {
	return xxx_messageInfo_GeneralContent.Size(m)
}
func (m *GeneralContent) XXX_DiscardUnknown() {
	xxx_messageInfo_GeneralContent.DiscardUnknown(m)
}

var xxx_messageInfo_GeneralContent proto.InternalMessageInfo

func (m *GeneralContent) GetUnmarshalType() uint32 {
	if m != nil {
		return m.UnmarshalType
	}
	return 0
}

func (m *GeneralContent) GetContentOrigin() uint32 {
	if m != nil {
		return m.ContentOrigin
	}
	return 0
}

func (m *GeneralContent) GetContent() []byte {
	if m != nil {
		return m.Content
	}
	return nil
}

// 发帖
type PostEvent struct {
	PostId               string               `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	TopicId              string               `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Status               uint32               `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	UserId               uint32               `protobuf:"varint,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	CreateAt             uint64               `protobuf:"varint,5,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	PostType             uint32               `protobuf:"varint,6,opt,name=post_type,json=postType,proto3" json:"post_type,omitempty"`
	Origin               uint32               `protobuf:"varint,7,opt,name=origin,proto3" json:"origin,omitempty"`
	SubTopicId           string               `protobuf:"bytes,8,opt,name=sub_topic_id,json=subTopicId,proto3" json:"sub_topic_id,omitempty"`
	ContentType          int32                `protobuf:"varint,9,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	PostOrigin           uint32               `protobuf:"varint,10,opt,name=post_origin,json=postOrigin,proto3" json:"post_origin,omitempty"`
	IsSystem             uint32               `protobuf:"varint,11,opt,name=is_system,json=isSystem,proto3" json:"is_system,omitempty"`
	GeoTopicId           string               `protobuf:"bytes,12,opt,name=geo_topic_id,json=geoTopicId,proto3" json:"geo_topic_id,omitempty"`
	Content              string               `protobuf:"bytes,13,opt,name=content,proto3" json:"content,omitempty"`
	PrivacyPolicy        uint32               `protobuf:"varint,14,opt,name=privacy_policy,json=privacyPolicy,proto3" json:"privacy_policy,omitempty"`
	Action               Action               `protobuf:"varint,15,opt,name=action,proto3,enum=ugc.event.Action" json:"action,omitempty"`
	UpdateAt             int64                `protobuf:"varint,16,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`
	DiyTopicIds          []string             `protobuf:"bytes,17,rep,name=diy_topic_ids,json=diyTopicIds,proto3" json:"diy_topic_ids,omitempty"`
	ClientIp             uint32               `protobuf:"varint,18,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	MoodInfo             *MoodInfo            `protobuf:"bytes,19,opt,name=mood_info,json=moodInfo,proto3" json:"mood_info,omitempty"`
	LabelLevel           string               `protobuf:"bytes,20,opt,name=label_level,json=labelLevel,proto3" json:"label_level,omitempty"`
	IsAnchorFeed         bool                 `protobuf:"varint,21,opt,name=is_anchor_feed,json=isAnchorFeed,proto3" json:"is_anchor_feed,omitempty"`
	ManualTopicIds       []string             `protobuf:"bytes,22,rep,name=manual_topic_ids,json=manualTopicIds,proto3" json:"manual_topic_ids,omitempty"`
	BussinessType        uint32               `protobuf:"varint,23,opt,name=bussiness_type,json=bussinessType,proto3" json:"bussiness_type,omitempty"`
	CommonTopicInfos     []*CommonTopicInfo   `protobuf:"bytes,24,rep,name=common_topic_infos,json=commonTopicInfos,proto3" json:"common_topic_infos,omitempty"`
	GeneralContents      []*GeneralContent    `protobuf:"bytes,25,rep,name=general_contents,json=generalContents,proto3" json:"general_contents,omitempty"`
	Province             string               `protobuf:"bytes,26,opt,name=province,proto3" json:"province,omitempty"`
	City                 string               `protobuf:"bytes,27,opt,name=city,proto3" json:"city,omitempty"`
	ZonePolicy           PostEvent_ZonePolicy `protobuf:"varint,28,opt,name=zone_policy,json=zonePolicy,proto3,enum=ugc.event.PostEvent_ZonePolicy" json:"zone_policy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *PostEvent) Reset()         { *m = PostEvent{} }
func (m *PostEvent) String() string { return proto.CompactTextString(m) }
func (*PostEvent) ProtoMessage()    {}
func (*PostEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{2}
}
func (m *PostEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostEvent.Unmarshal(m, b)
}
func (m *PostEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostEvent.Marshal(b, m, deterministic)
}
func (dst *PostEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostEvent.Merge(dst, src)
}
func (m *PostEvent) XXX_Size() int {
	return xxx_messageInfo_PostEvent.Size(m)
}
func (m *PostEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_PostEvent.DiscardUnknown(m)
}

var xxx_messageInfo_PostEvent proto.InternalMessageInfo

func (m *PostEvent) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *PostEvent) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *PostEvent) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *PostEvent) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *PostEvent) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *PostEvent) GetPostType() uint32 {
	if m != nil {
		return m.PostType
	}
	return 0
}

func (m *PostEvent) GetOrigin() uint32 {
	if m != nil {
		return m.Origin
	}
	return 0
}

func (m *PostEvent) GetSubTopicId() string {
	if m != nil {
		return m.SubTopicId
	}
	return ""
}

func (m *PostEvent) GetContentType() int32 {
	if m != nil {
		return m.ContentType
	}
	return 0
}

func (m *PostEvent) GetPostOrigin() uint32 {
	if m != nil {
		return m.PostOrigin
	}
	return 0
}

func (m *PostEvent) GetIsSystem() uint32 {
	if m != nil {
		return m.IsSystem
	}
	return 0
}

func (m *PostEvent) GetGeoTopicId() string {
	if m != nil {
		return m.GeoTopicId
	}
	return ""
}

func (m *PostEvent) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *PostEvent) GetPrivacyPolicy() uint32 {
	if m != nil {
		return m.PrivacyPolicy
	}
	return 0
}

func (m *PostEvent) GetAction() Action {
	if m != nil {
		return m.Action
	}
	return Action_ACTION_UNDEFINED
}

func (m *PostEvent) GetUpdateAt() int64 {
	if m != nil {
		return m.UpdateAt
	}
	return 0
}

func (m *PostEvent) GetDiyTopicIds() []string {
	if m != nil {
		return m.DiyTopicIds
	}
	return nil
}

func (m *PostEvent) GetClientIp() uint32 {
	if m != nil {
		return m.ClientIp
	}
	return 0
}

func (m *PostEvent) GetMoodInfo() *MoodInfo {
	if m != nil {
		return m.MoodInfo
	}
	return nil
}

func (m *PostEvent) GetLabelLevel() string {
	if m != nil {
		return m.LabelLevel
	}
	return ""
}

func (m *PostEvent) GetIsAnchorFeed() bool {
	if m != nil {
		return m.IsAnchorFeed
	}
	return false
}

func (m *PostEvent) GetManualTopicIds() []string {
	if m != nil {
		return m.ManualTopicIds
	}
	return nil
}

func (m *PostEvent) GetBussinessType() uint32 {
	if m != nil {
		return m.BussinessType
	}
	return 0
}

func (m *PostEvent) GetCommonTopicInfos() []*CommonTopicInfo {
	if m != nil {
		return m.CommonTopicInfos
	}
	return nil
}

func (m *PostEvent) GetGeneralContents() []*GeneralContent {
	if m != nil {
		return m.GeneralContents
	}
	return nil
}

func (m *PostEvent) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *PostEvent) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *PostEvent) GetZonePolicy() PostEvent_ZonePolicy {
	if m != nil {
		return m.ZonePolicy
	}
	return PostEvent_DEFAULT
}

// 评论
type CommentEvent struct {
	PostId           string `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	TopicId          string `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	CommentId        string `protobuf:"bytes,3,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	ReplyToCommentId string `protobuf:"bytes,4,opt,name=reply_to_comment_id,json=replyToCommentId,proto3" json:"reply_to_comment_id,omitempty"`
	ConversationId   string `protobuf:"bytes,5,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	FromUserId       uint32 `protobuf:"varint,6,opt,name=from_user_id,json=fromUserId,proto3" json:"from_user_id,omitempty"`
	// 回复哪个人, 可能和下面的reply_to_comment_owner_user_id, post_owner_user_id 一样
	ToUserId                  uint32   `protobuf:"varint,7,opt,name=to_user_id,json=toUserId,proto3" json:"to_user_id,omitempty"`
	Status                    uint32   `protobuf:"varint,8,opt,name=status,proto3" json:"status,omitempty"`
	CreateAt                  uint64   `protobuf:"varint,9,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	PostOwnerUserId           uint32   `protobuf:"varint,10,opt,name=post_owner_user_id,json=postOwnerUserId,proto3" json:"post_owner_user_id,omitempty"`
	ReplyToCommentOwnerUserId uint32   `protobuf:"varint,11,opt,name=reply_to_comment_owner_user_id,json=replyToCommentOwnerUserId,proto3" json:"reply_to_comment_owner_user_id,omitempty"`
	ContentType               int32    `protobuf:"varint,12,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	IsRobot                   bool     `protobuf:"varint,13,opt,name=is_robot,json=isRobot,proto3" json:"is_robot,omitempty"`
	Source                    uint32   `protobuf:"varint,14,opt,name=source,proto3" json:"source,omitempty"`
	IsValidPosterInter        bool     `protobuf:"varint,15,opt,name=is_valid_poster_inter,json=isValidPosterInter,proto3" json:"is_valid_poster_inter,omitempty"`
	IsValidUserInter          bool     `protobuf:"varint,16,opt,name=is_valid_user_inter,json=isValidUserInter,proto3" json:"is_valid_user_inter,omitempty"`
	XXX_NoUnkeyedLiteral      struct{} `json:"-"`
	XXX_unrecognized          []byte   `json:"-"`
	XXX_sizecache             int32    `json:"-"`
}

func (m *CommentEvent) Reset()         { *m = CommentEvent{} }
func (m *CommentEvent) String() string { return proto.CompactTextString(m) }
func (*CommentEvent) ProtoMessage()    {}
func (*CommentEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{3}
}
func (m *CommentEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentEvent.Unmarshal(m, b)
}
func (m *CommentEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentEvent.Marshal(b, m, deterministic)
}
func (dst *CommentEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentEvent.Merge(dst, src)
}
func (m *CommentEvent) XXX_Size() int {
	return xxx_messageInfo_CommentEvent.Size(m)
}
func (m *CommentEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentEvent.DiscardUnknown(m)
}

var xxx_messageInfo_CommentEvent proto.InternalMessageInfo

func (m *CommentEvent) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *CommentEvent) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *CommentEvent) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *CommentEvent) GetReplyToCommentId() string {
	if m != nil {
		return m.ReplyToCommentId
	}
	return ""
}

func (m *CommentEvent) GetConversationId() string {
	if m != nil {
		return m.ConversationId
	}
	return ""
}

func (m *CommentEvent) GetFromUserId() uint32 {
	if m != nil {
		return m.FromUserId
	}
	return 0
}

func (m *CommentEvent) GetToUserId() uint32 {
	if m != nil {
		return m.ToUserId
	}
	return 0
}

func (m *CommentEvent) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CommentEvent) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *CommentEvent) GetPostOwnerUserId() uint32 {
	if m != nil {
		return m.PostOwnerUserId
	}
	return 0
}

func (m *CommentEvent) GetReplyToCommentOwnerUserId() uint32 {
	if m != nil {
		return m.ReplyToCommentOwnerUserId
	}
	return 0
}

func (m *CommentEvent) GetContentType() int32 {
	if m != nil {
		return m.ContentType
	}
	return 0
}

func (m *CommentEvent) GetIsRobot() bool {
	if m != nil {
		return m.IsRobot
	}
	return false
}

func (m *CommentEvent) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *CommentEvent) GetIsValidPosterInter() bool {
	if m != nil {
		return m.IsValidPosterInter
	}
	return false
}

func (m *CommentEvent) GetIsValidUserInter() bool {
	if m != nil {
		return m.IsValidUserInter
	}
	return false
}

// 点赞
type AttitudeEvent struct {
	// Types that are valid to be assigned to Target:
	//	*AttitudeEvent_PostId
	//	*AttitudeEvent_CommentId
	Target               isAttitudeEvent_Target `protobuf_oneof:"target"`
	AttitudeType         uint32                 `protobuf:"varint,3,opt,name=attitude_type,json=attitudeType,proto3" json:"attitude_type,omitempty"`
	CreateAt             uint64                 `protobuf:"varint,4,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	UserId               uint32                 `protobuf:"varint,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	TargetCreatorUid     uint32                 `protobuf:"varint,6,opt,name=target_creator_uid,json=targetCreatorUid,proto3" json:"target_creator_uid,omitempty"`
	IsFirstTime          bool                   `protobuf:"varint,7,opt,name=is_first_time,json=isFirstTime,proto3" json:"is_first_time,omitempty"`
	Source               uint32                 `protobuf:"varint,8,opt,name=source,proto3" json:"source,omitempty"`
	IsRobot              bool                   `protobuf:"varint,20,opt,name=is_robot,json=isRobot,proto3" json:"is_robot,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *AttitudeEvent) Reset()         { *m = AttitudeEvent{} }
func (m *AttitudeEvent) String() string { return proto.CompactTextString(m) }
func (*AttitudeEvent) ProtoMessage()    {}
func (*AttitudeEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{4}
}
func (m *AttitudeEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AttitudeEvent.Unmarshal(m, b)
}
func (m *AttitudeEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AttitudeEvent.Marshal(b, m, deterministic)
}
func (dst *AttitudeEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AttitudeEvent.Merge(dst, src)
}
func (m *AttitudeEvent) XXX_Size() int {
	return xxx_messageInfo_AttitudeEvent.Size(m)
}
func (m *AttitudeEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_AttitudeEvent.DiscardUnknown(m)
}

var xxx_messageInfo_AttitudeEvent proto.InternalMessageInfo

type isAttitudeEvent_Target interface {
	isAttitudeEvent_Target()
}

type AttitudeEvent_PostId struct {
	PostId string `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3,oneof"`
}

type AttitudeEvent_CommentId struct {
	CommentId string `protobuf:"bytes,2,opt,name=comment_id,json=commentId,proto3,oneof"`
}

func (*AttitudeEvent_PostId) isAttitudeEvent_Target() {}

func (*AttitudeEvent_CommentId) isAttitudeEvent_Target() {}

func (m *AttitudeEvent) GetTarget() isAttitudeEvent_Target {
	if m != nil {
		return m.Target
	}
	return nil
}

func (m *AttitudeEvent) GetPostId() string {
	if x, ok := m.GetTarget().(*AttitudeEvent_PostId); ok {
		return x.PostId
	}
	return ""
}

func (m *AttitudeEvent) GetCommentId() string {
	if x, ok := m.GetTarget().(*AttitudeEvent_CommentId); ok {
		return x.CommentId
	}
	return ""
}

func (m *AttitudeEvent) GetAttitudeType() uint32 {
	if m != nil {
		return m.AttitudeType
	}
	return 0
}

func (m *AttitudeEvent) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *AttitudeEvent) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *AttitudeEvent) GetTargetCreatorUid() uint32 {
	if m != nil {
		return m.TargetCreatorUid
	}
	return 0
}

func (m *AttitudeEvent) GetIsFirstTime() bool {
	if m != nil {
		return m.IsFirstTime
	}
	return false
}

func (m *AttitudeEvent) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *AttitudeEvent) GetIsRobot() bool {
	if m != nil {
		return m.IsRobot
	}
	return false
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*AttitudeEvent) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _AttitudeEvent_OneofMarshaler, _AttitudeEvent_OneofUnmarshaler, _AttitudeEvent_OneofSizer, []interface{}{
		(*AttitudeEvent_PostId)(nil),
		(*AttitudeEvent_CommentId)(nil),
	}
}

func _AttitudeEvent_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*AttitudeEvent)
	// target
	switch x := m.Target.(type) {
	case *AttitudeEvent_PostId:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.PostId)
	case *AttitudeEvent_CommentId:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.CommentId)
	case nil:
	default:
		return fmt.Errorf("AttitudeEvent.Target has unexpected type %T", x)
	}
	return nil
}

func _AttitudeEvent_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*AttitudeEvent)
	switch tag {
	case 1: // target.post_id
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.Target = &AttitudeEvent_PostId{x}
		return true, err
	case 2: // target.comment_id
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.Target = &AttitudeEvent_CommentId{x}
		return true, err
	default:
		return false, nil
	}
}

func _AttitudeEvent_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*AttitudeEvent)
	// target
	switch x := m.Target.(type) {
	case *AttitudeEvent_PostId:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(len(x.PostId)))
		n += len(x.PostId)
	case *AttitudeEvent_CommentId:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(len(x.CommentId)))
		n += len(x.CommentId)
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

// 关注 / 取消关注
type FollowEvent struct {
	FromUserId           uint32             `protobuf:"varint,1,opt,name=from_user_id,json=fromUserId,proto3" json:"from_user_id,omitempty"`
	ToUserId             uint32             `protobuf:"varint,2,opt,name=to_user_id,json=toUserId,proto3" json:"to_user_id,omitempty"`
	IsFirstTime          bool               `protobuf:"varint,3,opt,name=is_first_time,json=isFirstTime,proto3" json:"is_first_time,omitempty"`
	CreateAt             uint64             `protobuf:"varint,4,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	IsDeleted            bool               `protobuf:"varint,5,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	Source               FollowEvent_Source `protobuf:"varint,6,opt,name=source,proto3,enum=ugc.event.FollowEvent_Source" json:"source,omitempty"`
	ClientSource         uint32             `protobuf:"varint,7,opt,name=client_source,json=clientSource,proto3" json:"client_source,omitempty"`
	ClientCustomSource   string             `protobuf:"bytes,8,opt,name=client_custom_source,json=clientCustomSource,proto3" json:"client_custom_source,omitempty"`
	IsRobot              bool               `protobuf:"varint,9,opt,name=is_robot,json=isRobot,proto3" json:"is_robot,omitempty"`
	ChannelId            uint32             `protobuf:"varint,10,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType          uint32             `protobuf:"varint,11,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *FollowEvent) Reset()         { *m = FollowEvent{} }
func (m *FollowEvent) String() string { return proto.CompactTextString(m) }
func (*FollowEvent) ProtoMessage()    {}
func (*FollowEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{5}
}
func (m *FollowEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FollowEvent.Unmarshal(m, b)
}
func (m *FollowEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FollowEvent.Marshal(b, m, deterministic)
}
func (dst *FollowEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FollowEvent.Merge(dst, src)
}
func (m *FollowEvent) XXX_Size() int {
	return xxx_messageInfo_FollowEvent.Size(m)
}
func (m *FollowEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_FollowEvent.DiscardUnknown(m)
}

var xxx_messageInfo_FollowEvent proto.InternalMessageInfo

func (m *FollowEvent) GetFromUserId() uint32 {
	if m != nil {
		return m.FromUserId
	}
	return 0
}

func (m *FollowEvent) GetToUserId() uint32 {
	if m != nil {
		return m.ToUserId
	}
	return 0
}

func (m *FollowEvent) GetIsFirstTime() bool {
	if m != nil {
		return m.IsFirstTime
	}
	return false
}

func (m *FollowEvent) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *FollowEvent) GetIsDeleted() bool {
	if m != nil {
		return m.IsDeleted
	}
	return false
}

func (m *FollowEvent) GetSource() FollowEvent_Source {
	if m != nil {
		return m.Source
	}
	return FollowEvent_USER_OPERATION
}

func (m *FollowEvent) GetClientSource() uint32 {
	if m != nil {
		return m.ClientSource
	}
	return 0
}

func (m *FollowEvent) GetClientCustomSource() string {
	if m != nil {
		return m.ClientCustomSource
	}
	return ""
}

func (m *FollowEvent) GetIsRobot() bool {
	if m != nil {
		return m.IsRobot
	}
	return false
}

func (m *FollowEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *FollowEvent) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

//
type PostShareEvent struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	UserId               uint32   `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OwnerId              uint32   `protobuf:"varint,3,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	IsRobot              bool     `protobuf:"varint,10,opt,name=is_robot,json=isRobot,proto3" json:"is_robot,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostShareEvent) Reset()         { *m = PostShareEvent{} }
func (m *PostShareEvent) String() string { return proto.CompactTextString(m) }
func (*PostShareEvent) ProtoMessage()    {}
func (*PostShareEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{6}
}
func (m *PostShareEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostShareEvent.Unmarshal(m, b)
}
func (m *PostShareEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostShareEvent.Marshal(b, m, deterministic)
}
func (dst *PostShareEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostShareEvent.Merge(dst, src)
}
func (m *PostShareEvent) XXX_Size() int {
	return xxx_messageInfo_PostShareEvent.Size(m)
}
func (m *PostShareEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_PostShareEvent.DiscardUnknown(m)
}

var xxx_messageInfo_PostShareEvent proto.InternalMessageInfo

func (m *PostShareEvent) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *PostShareEvent) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *PostShareEvent) GetOwnerId() uint32 {
	if m != nil {
		return m.OwnerId
	}
	return 0
}

func (m *PostShareEvent) GetIsRobot() bool {
	if m != nil {
		return m.IsRobot
	}
	return false
}

// 举报 帖子 / 评论
type UgcContentReport struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string   `protobuf:"bytes,2,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	CreateAt             uint64   `protobuf:"varint,3,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	ReportAt             uint64   `protobuf:"varint,4,opt,name=report_at,json=reportAt,proto3" json:"report_at,omitempty"`
	ReportType           uint32   `protobuf:"varint,5,opt,name=report_type,json=reportType,proto3" json:"report_type,omitempty"`
	OwnerUserId          uint32   `protobuf:"varint,6,opt,name=owner_user_id,json=ownerUserId,proto3" json:"owner_user_id,omitempty"`
	FromUserId           uint32   `protobuf:"varint,7,opt,name=from_user_id,json=fromUserId,proto3" json:"from_user_id,omitempty"`
	AttachmentKeyList    []string `protobuf:"bytes,8,rep,name=attachment_key_list,json=attachmentKeyList,proto3" json:"attachment_key_list,omitempty"`
	Content              string   `protobuf:"bytes,9,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UgcContentReport) Reset()         { *m = UgcContentReport{} }
func (m *UgcContentReport) String() string { return proto.CompactTextString(m) }
func (*UgcContentReport) ProtoMessage()    {}
func (*UgcContentReport) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{7}
}
func (m *UgcContentReport) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UgcContentReport.Unmarshal(m, b)
}
func (m *UgcContentReport) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UgcContentReport.Marshal(b, m, deterministic)
}
func (dst *UgcContentReport) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UgcContentReport.Merge(dst, src)
}
func (m *UgcContentReport) XXX_Size() int {
	return xxx_messageInfo_UgcContentReport.Size(m)
}
func (m *UgcContentReport) XXX_DiscardUnknown() {
	xxx_messageInfo_UgcContentReport.DiscardUnknown(m)
}

var xxx_messageInfo_UgcContentReport proto.InternalMessageInfo

func (m *UgcContentReport) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UgcContentReport) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *UgcContentReport) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *UgcContentReport) GetReportAt() uint64 {
	if m != nil {
		return m.ReportAt
	}
	return 0
}

func (m *UgcContentReport) GetReportType() uint32 {
	if m != nil {
		return m.ReportType
	}
	return 0
}

func (m *UgcContentReport) GetOwnerUserId() uint32 {
	if m != nil {
		return m.OwnerUserId
	}
	return 0
}

func (m *UgcContentReport) GetFromUserId() uint32 {
	if m != nil {
		return m.FromUserId
	}
	return 0
}

func (m *UgcContentReport) GetAttachmentKeyList() []string {
	if m != nil {
		return m.AttachmentKeyList
	}
	return nil
}

func (m *UgcContentReport) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

// 文本过滤拦截
type TextAntiReport struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string   `protobuf:"bytes,2,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	CreateAt             uint64   `protobuf:"varint,3,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	ReportAt             uint64   `protobuf:"varint,4,opt,name=report_at,json=reportAt,proto3" json:"report_at,omitempty"`
	ResultType           uint32   `protobuf:"varint,5,opt,name=result_type,json=resultType,proto3" json:"result_type,omitempty"`
	OwnerUserId          uint32   `protobuf:"varint,6,opt,name=owner_user_id,json=ownerUserId,proto3" json:"owner_user_id,omitempty"`
	LabelInfo            string   `protobuf:"bytes,7,opt,name=label_info,json=labelInfo,proto3" json:"label_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TextAntiReport) Reset()         { *m = TextAntiReport{} }
func (m *TextAntiReport) String() string { return proto.CompactTextString(m) }
func (*TextAntiReport) ProtoMessage()    {}
func (*TextAntiReport) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{8}
}
func (m *TextAntiReport) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TextAntiReport.Unmarshal(m, b)
}
func (m *TextAntiReport) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TextAntiReport.Marshal(b, m, deterministic)
}
func (dst *TextAntiReport) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TextAntiReport.Merge(dst, src)
}
func (m *TextAntiReport) XXX_Size() int {
	return xxx_messageInfo_TextAntiReport.Size(m)
}
func (m *TextAntiReport) XXX_DiscardUnknown() {
	xxx_messageInfo_TextAntiReport.DiscardUnknown(m)
}

var xxx_messageInfo_TextAntiReport proto.InternalMessageInfo

func (m *TextAntiReport) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *TextAntiReport) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *TextAntiReport) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *TextAntiReport) GetReportAt() uint64 {
	if m != nil {
		return m.ReportAt
	}
	return 0
}

func (m *TextAntiReport) GetResultType() uint32 {
	if m != nil {
		return m.ResultType
	}
	return 0
}

func (m *TextAntiReport) GetOwnerUserId() uint32 {
	if m != nil {
		return m.OwnerUserId
	}
	return 0
}

func (m *TextAntiReport) GetLabelInfo() string {
	if m != nil {
		return m.LabelInfo
	}
	return ""
}

type HotPostEvent struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Author               uint32   `protobuf:"varint,2,opt,name=author,proto3" json:"author,omitempty"`
	TopicId              string   `protobuf:"bytes,3,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HotPostEvent) Reset()         { *m = HotPostEvent{} }
func (m *HotPostEvent) String() string { return proto.CompactTextString(m) }
func (*HotPostEvent) ProtoMessage()    {}
func (*HotPostEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{9}
}
func (m *HotPostEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HotPostEvent.Unmarshal(m, b)
}
func (m *HotPostEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HotPostEvent.Marshal(b, m, deterministic)
}
func (dst *HotPostEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HotPostEvent.Merge(dst, src)
}
func (m *HotPostEvent) XXX_Size() int {
	return xxx_messageInfo_HotPostEvent.Size(m)
}
func (m *HotPostEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_HotPostEvent.DiscardUnknown(m)
}

var xxx_messageInfo_HotPostEvent proto.InternalMessageInfo

func (m *HotPostEvent) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *HotPostEvent) GetAuthor() uint32 {
	if m != nil {
		return m.Author
	}
	return 0
}

func (m *HotPostEvent) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

type VisitEvent struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	FromUserId           uint32   `protobuf:"varint,2,opt,name=from_user_id,json=fromUserId,proto3" json:"from_user_id,omitempty"`
	Author               uint32   `protobuf:"varint,3,opt,name=author,proto3" json:"author,omitempty"`
	VisitAt              uint64   `protobuf:"varint,4,opt,name=visit_at,json=visitAt,proto3" json:"visit_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VisitEvent) Reset()         { *m = VisitEvent{} }
func (m *VisitEvent) String() string { return proto.CompactTextString(m) }
func (*VisitEvent) ProtoMessage()    {}
func (*VisitEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{10}
}
func (m *VisitEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VisitEvent.Unmarshal(m, b)
}
func (m *VisitEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VisitEvent.Marshal(b, m, deterministic)
}
func (dst *VisitEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VisitEvent.Merge(dst, src)
}
func (m *VisitEvent) XXX_Size() int {
	return xxx_messageInfo_VisitEvent.Size(m)
}
func (m *VisitEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_VisitEvent.DiscardUnknown(m)
}

var xxx_messageInfo_VisitEvent proto.InternalMessageInfo

func (m *VisitEvent) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *VisitEvent) GetFromUserId() uint32 {
	if m != nil {
		return m.FromUserId
	}
	return 0
}

func (m *VisitEvent) GetAuthor() uint32 {
	if m != nil {
		return m.Author
	}
	return 0
}

func (m *VisitEvent) GetVisitAt() uint64 {
	if m != nil {
		return m.VisitAt
	}
	return 0
}

// 心情信息
type MoodInfo struct {
	MoodId               string   `protobuf:"bytes,1,opt,name=mood_id,json=moodId,proto3" json:"mood_id,omitempty"`
	MoodBindTopicId      string   `protobuf:"bytes,2,opt,name=mood_bind_topic_id,json=moodBindTopicId,proto3" json:"mood_bind_topic_id,omitempty"`
	MoodName             string   `protobuf:"bytes,3,opt,name=mood_name,json=moodName,proto3" json:"mood_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MoodInfo) Reset()         { *m = MoodInfo{} }
func (m *MoodInfo) String() string { return proto.CompactTextString(m) }
func (*MoodInfo) ProtoMessage()    {}
func (*MoodInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{11}
}
func (m *MoodInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoodInfo.Unmarshal(m, b)
}
func (m *MoodInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoodInfo.Marshal(b, m, deterministic)
}
func (dst *MoodInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoodInfo.Merge(dst, src)
}
func (m *MoodInfo) XXX_Size() int {
	return xxx_messageInfo_MoodInfo.Size(m)
}
func (m *MoodInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MoodInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MoodInfo proto.InternalMessageInfo

func (m *MoodInfo) GetMoodId() string {
	if m != nil {
		return m.MoodId
	}
	return ""
}

func (m *MoodInfo) GetMoodBindTopicId() string {
	if m != nil {
		return m.MoodBindTopicId
	}
	return ""
}

func (m *MoodInfo) GetMoodName() string {
	if m != nil {
		return m.MoodName
	}
	return ""
}

// 话题搜索
type TopicSearchHotEvent struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	TopicName            string   `protobuf:"bytes,2,opt,name=topic_name,json=topicName,proto3" json:"topic_name,omitempty"`
	SearchAt             uint64   `protobuf:"varint,3,opt,name=search_at,json=searchAt,proto3" json:"search_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopicSearchHotEvent) Reset()         { *m = TopicSearchHotEvent{} }
func (m *TopicSearchHotEvent) String() string { return proto.CompactTextString(m) }
func (*TopicSearchHotEvent) ProtoMessage()    {}
func (*TopicSearchHotEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{12}
}
func (m *TopicSearchHotEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicSearchHotEvent.Unmarshal(m, b)
}
func (m *TopicSearchHotEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicSearchHotEvent.Marshal(b, m, deterministic)
}
func (dst *TopicSearchHotEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicSearchHotEvent.Merge(dst, src)
}
func (m *TopicSearchHotEvent) XXX_Size() int {
	return xxx_messageInfo_TopicSearchHotEvent.Size(m)
}
func (m *TopicSearchHotEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicSearchHotEvent.DiscardUnknown(m)
}

var xxx_messageInfo_TopicSearchHotEvent proto.InternalMessageInfo

func (m *TopicSearchHotEvent) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *TopicSearchHotEvent) GetTopicName() string {
	if m != nil {
		return m.TopicName
	}
	return ""
}

func (m *TopicSearchHotEvent) GetSearchAt() uint64 {
	if m != nil {
		return m.SearchAt
	}
	return 0
}

// 话题关联事件
type AssociatePostWithTopicEvent struct {
	Operation            AssociatePostWithTopicEvent_Operation `protobuf:"varint,1,opt,name=operation,proto3,enum=ugc.event.AssociatePostWithTopicEvent_Operation" json:"operation,omitempty"`
	PostId               string                                `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	UserId               uint32                                `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	CreateAt             uint64                                `protobuf:"varint,4,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	ManualTopicIds       []string                              `protobuf:"bytes,5,rep,name=manual_topic_ids,json=manualTopicIds,proto3" json:"manual_topic_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *AssociatePostWithTopicEvent) Reset()         { *m = AssociatePostWithTopicEvent{} }
func (m *AssociatePostWithTopicEvent) String() string { return proto.CompactTextString(m) }
func (*AssociatePostWithTopicEvent) ProtoMessage()    {}
func (*AssociatePostWithTopicEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{13}
}
func (m *AssociatePostWithTopicEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AssociatePostWithTopicEvent.Unmarshal(m, b)
}
func (m *AssociatePostWithTopicEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AssociatePostWithTopicEvent.Marshal(b, m, deterministic)
}
func (dst *AssociatePostWithTopicEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AssociatePostWithTopicEvent.Merge(dst, src)
}
func (m *AssociatePostWithTopicEvent) XXX_Size() int {
	return xxx_messageInfo_AssociatePostWithTopicEvent.Size(m)
}
func (m *AssociatePostWithTopicEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_AssociatePostWithTopicEvent.DiscardUnknown(m)
}

var xxx_messageInfo_AssociatePostWithTopicEvent proto.InternalMessageInfo

func (m *AssociatePostWithTopicEvent) GetOperation() AssociatePostWithTopicEvent_Operation {
	if m != nil {
		return m.Operation
	}
	return AssociatePostWithTopicEvent_ADD
}

func (m *AssociatePostWithTopicEvent) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *AssociatePostWithTopicEvent) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *AssociatePostWithTopicEvent) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *AssociatePostWithTopicEvent) GetManualTopicIds() []string {
	if m != nil {
		return m.ManualTopicIds
	}
	return nil
}

// 话题创建/修改事件
type UpdateTopicInfoEvent struct {
	TopicId   string `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	TopicName string `protobuf:"bytes,2,opt,name=topic_name,json=topicName,proto3" json:"topic_name,omitempty"`
	TopicType uint32 `protobuf:"varint,3,opt,name=topic_type,json=topicType,proto3" json:"topic_type,omitempty"`
	Desc      string `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	// 查询详细内容则包括一下字段
	UpdateAt             uint32          `protobuf:"varint,5,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`
	CreateAt             uint32          `protobuf:"varint,6,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	MoodId               string          `protobuf:"bytes,7,opt,name=mood_id,json=moodId,proto3" json:"mood_id,omitempty"`
	RelatedTopicList     []*RelatedTopic `protobuf:"bytes,8,rep,name=related_topic_list,json=relatedTopicList,proto3" json:"related_topic_list,omitempty"`
	Enable               bool            `protobuf:"varint,9,opt,name=enable,proto3" json:"enable,omitempty"`
	IsDelete             bool            `protobuf:"varint,10,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UpdateTopicInfoEvent) Reset()         { *m = UpdateTopicInfoEvent{} }
func (m *UpdateTopicInfoEvent) String() string { return proto.CompactTextString(m) }
func (*UpdateTopicInfoEvent) ProtoMessage()    {}
func (*UpdateTopicInfoEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{14}
}
func (m *UpdateTopicInfoEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTopicInfoEvent.Unmarshal(m, b)
}
func (m *UpdateTopicInfoEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTopicInfoEvent.Marshal(b, m, deterministic)
}
func (dst *UpdateTopicInfoEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTopicInfoEvent.Merge(dst, src)
}
func (m *UpdateTopicInfoEvent) XXX_Size() int {
	return xxx_messageInfo_UpdateTopicInfoEvent.Size(m)
}
func (m *UpdateTopicInfoEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTopicInfoEvent.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTopicInfoEvent proto.InternalMessageInfo

func (m *UpdateTopicInfoEvent) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *UpdateTopicInfoEvent) GetTopicName() string {
	if m != nil {
		return m.TopicName
	}
	return ""
}

func (m *UpdateTopicInfoEvent) GetTopicType() uint32 {
	if m != nil {
		return m.TopicType
	}
	return 0
}

func (m *UpdateTopicInfoEvent) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *UpdateTopicInfoEvent) GetUpdateAt() uint32 {
	if m != nil {
		return m.UpdateAt
	}
	return 0
}

func (m *UpdateTopicInfoEvent) GetCreateAt() uint32 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *UpdateTopicInfoEvent) GetMoodId() string {
	if m != nil {
		return m.MoodId
	}
	return ""
}

func (m *UpdateTopicInfoEvent) GetRelatedTopicList() []*RelatedTopic {
	if m != nil {
		return m.RelatedTopicList
	}
	return nil
}

func (m *UpdateTopicInfoEvent) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

func (m *UpdateTopicInfoEvent) GetIsDelete() bool {
	if m != nil {
		return m.IsDelete
	}
	return false
}

type RelatedTopic struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RelatedTopic) Reset()         { *m = RelatedTopic{} }
func (m *RelatedTopic) String() string { return proto.CompactTextString(m) }
func (*RelatedTopic) ProtoMessage()    {}
func (*RelatedTopic) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{15}
}
func (m *RelatedTopic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RelatedTopic.Unmarshal(m, b)
}
func (m *RelatedTopic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RelatedTopic.Marshal(b, m, deterministic)
}
func (dst *RelatedTopic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RelatedTopic.Merge(dst, src)
}
func (m *RelatedTopic) XXX_Size() int {
	return xxx_messageInfo_RelatedTopic.Size(m)
}
func (m *RelatedTopic) XXX_DiscardUnknown() {
	xxx_messageInfo_RelatedTopic.DiscardUnknown(m)
}

var xxx_messageInfo_RelatedTopic proto.InternalMessageInfo

func (m *RelatedTopic) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *RelatedTopic) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// 好友备注事件
type FriendRemarkEvent struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	FriendUid            uint32   `protobuf:"varint,2,opt,name=friend_uid,json=friendUid,proto3" json:"friend_uid,omitempty"`
	Remark               string   `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark,omitempty"`
	OpTs                 uint64   `protobuf:"varint,4,opt,name=op_ts,json=opTs,proto3" json:"op_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FriendRemarkEvent) Reset()         { *m = FriendRemarkEvent{} }
func (m *FriendRemarkEvent) String() string { return proto.CompactTextString(m) }
func (*FriendRemarkEvent) ProtoMessage()    {}
func (*FriendRemarkEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_5a1e470b8d9ffaa9, []int{16}
}
func (m *FriendRemarkEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FriendRemarkEvent.Unmarshal(m, b)
}
func (m *FriendRemarkEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FriendRemarkEvent.Marshal(b, m, deterministic)
}
func (dst *FriendRemarkEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FriendRemarkEvent.Merge(dst, src)
}
func (m *FriendRemarkEvent) XXX_Size() int {
	return xxx_messageInfo_FriendRemarkEvent.Size(m)
}
func (m *FriendRemarkEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_FriendRemarkEvent.DiscardUnknown(m)
}

var xxx_messageInfo_FriendRemarkEvent proto.InternalMessageInfo

func (m *FriendRemarkEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FriendRemarkEvent) GetFriendUid() uint32 {
	if m != nil {
		return m.FriendUid
	}
	return 0
}

func (m *FriendRemarkEvent) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *FriendRemarkEvent) GetOpTs() uint64 {
	if m != nil {
		return m.OpTs
	}
	return 0
}

func init() {
	proto.RegisterType((*CommonTopicInfo)(nil), "ugc.event.CommonTopicInfo")
	proto.RegisterType((*GeneralContent)(nil), "ugc.event.GeneralContent")
	proto.RegisterType((*PostEvent)(nil), "ugc.event.PostEvent")
	proto.RegisterType((*CommentEvent)(nil), "ugc.event.CommentEvent")
	proto.RegisterType((*AttitudeEvent)(nil), "ugc.event.AttitudeEvent")
	proto.RegisterType((*FollowEvent)(nil), "ugc.event.FollowEvent")
	proto.RegisterType((*PostShareEvent)(nil), "ugc.event.PostShareEvent")
	proto.RegisterType((*UgcContentReport)(nil), "ugc.event.UgcContentReport")
	proto.RegisterType((*TextAntiReport)(nil), "ugc.event.TextAntiReport")
	proto.RegisterType((*HotPostEvent)(nil), "ugc.event.HotPostEvent")
	proto.RegisterType((*VisitEvent)(nil), "ugc.event.VisitEvent")
	proto.RegisterType((*MoodInfo)(nil), "ugc.event.MoodInfo")
	proto.RegisterType((*TopicSearchHotEvent)(nil), "ugc.event.TopicSearchHotEvent")
	proto.RegisterType((*AssociatePostWithTopicEvent)(nil), "ugc.event.AssociatePostWithTopicEvent")
	proto.RegisterType((*UpdateTopicInfoEvent)(nil), "ugc.event.UpdateTopicInfoEvent")
	proto.RegisterType((*RelatedTopic)(nil), "ugc.event.RelatedTopic")
	proto.RegisterType((*FriendRemarkEvent)(nil), "ugc.event.FriendRemarkEvent")
	proto.RegisterEnum("ugc.event.Action", Action_name, Action_value)
	proto.RegisterEnum("ugc.event.PostEvent_Origin", PostEvent_Origin_name, PostEvent_Origin_value)
	proto.RegisterEnum("ugc.event.PostEvent_BusinessType", PostEvent_BusinessType_name, PostEvent_BusinessType_value)
	proto.RegisterEnum("ugc.event.PostEvent_ZonePolicy", PostEvent_ZonePolicy_name, PostEvent_ZonePolicy_value)
	proto.RegisterEnum("ugc.event.FollowEvent_Source", FollowEvent_Source_name, FollowEvent_Source_value)
	proto.RegisterEnum("ugc.event.AssociatePostWithTopicEvent_Operation", AssociatePostWithTopicEvent_Operation_name, AssociatePostWithTopicEvent_Operation_value)
}

func init() { proto.RegisterFile("ugc/event.proto", fileDescriptor_event_5a1e470b8d9ffaa9) }

var fileDescriptor_event_5a1e470b8d9ffaa9 = []byte{
	// 2026 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x58, 0xcd, 0x72, 0xdb, 0xc8,
	0xf1, 0x17, 0x49, 0x89, 0x04, 0x9a, 0x1f, 0x82, 0x46, 0xb6, 0x4c, 0xd9, 0xd6, 0xdf, 0x34, 0x76,
	0xb7, 0xfe, 0x4c, 0x36, 0x2b, 0x3b, 0x4a, 0xf9, 0x1e, 0x88, 0xa4, 0x2c, 0xd6, 0xd2, 0x94, 0x0a,
	0x24, 0xbd, 0x25, 0x5f, 0x50, 0x10, 0x30, 0xa2, 0xa6, 0x16, 0xc4, 0x30, 0x98, 0xa1, 0x76, 0xe9,
	0x5b, 0x0e, 0x79, 0xa4, 0xbc, 0x46, 0x0e, 0x39, 0xe4, 0x92, 0x7b, 0x9e, 0x23, 0x35, 0x1f, 0x04,
	0x01, 0xd2, 0x5e, 0x6f, 0xd5, 0x1e, 0x72, 0xc3, 0xfc, 0xba, 0xa7, 0xbb, 0xa7, 0xbf, 0xa6, 0x07,
	0xb0, 0xbf, 0x98, 0x06, 0xaf, 0xf0, 0x03, 0x8e, 0xf9, 0xe9, 0x3c, 0xa1, 0x9c, 0x22, 0x73, 0x31,
	0x0d, 0x4e, 0x25, 0x60, 0xbf, 0x83, 0xfd, 0x0e, 0x9d, 0xcd, 0x68, 0x3c, 0xa6, 0x73, 0x12, 0xf4,
	0xe3, 0x3b, 0x8a, 0x9e, 0x81, 0xc9, 0xc5, 0xc2, 0x23, 0x21, 0x6b, 0x16, 0x5a, 0xa5, 0xb6, 0xe9,
	0x1a, 0x12, 0xe8, 0x87, 0x0c, 0x9d, 0x00, 0x28, 0x22, 0x5f, 0xce, 0x71, 0xb3, 0xd8, 0x2a, 0xb4,
	0xeb, 0xae, 0x62, 0x1f, 0x2f, 0xe7, 0xd8, 0xfe, 0x19, 0x1a, 0x6f, 0x71, 0x8c, 0x13, 0x3f, 0xea,
	0xd0, 0x98, 0xe3, 0x98, 0xa3, 0x6f, 0xa0, 0xb1, 0x88, 0x67, 0x7e, 0xc2, 0xee, 0xfd, 0x48, 0x6d,
	0x2a, 0xc8, 0x4d, 0xf5, 0x14, 0x15, 0x1b, 0x05, 0x5b, 0xa0, 0x76, 0x78, 0x34, 0x21, 0x53, 0x12,
	0x6b, 0xd9, 0x75, 0x8d, 0x5e, 0x49, 0x10, 0x35, 0xa1, 0xa2, 0x81, 0x66, 0xa9, 0x55, 0x68, 0xd7,
	0xdc, 0xd5, 0xd2, 0xfe, 0xab, 0x09, 0xe6, 0x35, 0x65, 0xbc, 0x27, 0x8e, 0x85, 0x9e, 0x40, 0x65,
	0x4e, 0x19, 0xf7, 0x48, 0x28, 0xd5, 0x99, 0x6e, 0x59, 0x2c, 0xfb, 0x21, 0x3a, 0x06, 0x63, 0x75,
	0x38, 0xa9, 0xc1, 0x74, 0x2b, 0xfa, 0x6c, 0xe8, 0x08, 0xca, 0x8c, 0xfb, 0x7c, 0xc1, 0xa4, 0xe8,
	0xba, 0xab, 0x57, 0x42, 0xd6, 0x82, 0xe1, 0x44, 0xec, 0xd8, 0x55, 0x04, 0xb1, 0xec, 0x87, 0xc2,
	0x51, 0x41, 0x82, 0x7d, 0x8e, 0x3d, 0x9f, 0x37, 0xf7, 0x5a, 0x85, 0xf6, 0xae, 0x6b, 0x28, 0xc0,
	0xe1, 0x82, 0x28, 0x2d, 0x90, 0x47, 0x2e, 0xcb, 0x7d, 0x86, 0x00, 0xe4, 0x69, 0x8f, 0xa0, 0xac,
	0x4f, 0x59, 0x51, 0x12, 0xd5, 0x0a, 0xb5, 0xa0, 0xc6, 0x16, 0xb7, 0x5e, 0x6a, 0xa1, 0x21, 0x2d,
	0x04, 0xb6, 0xb8, 0x1d, 0x6b, 0x23, 0x5f, 0x42, 0x6d, 0xe5, 0x27, 0x29, 0xd9, 0x6c, 0x15, 0xda,
	0x7b, 0x6e, 0x55, 0x63, 0x52, 0xf8, 0x0b, 0xa8, 0x4a, 0xcd, 0x5a, 0x03, 0x48, 0x0d, 0x20, 0x20,
	0xed, 0xc4, 0x67, 0x60, 0x12, 0xe6, 0xb1, 0x25, 0xe3, 0x78, 0xd6, 0xac, 0x2a, 0xd3, 0x08, 0x1b,
	0xc9, 0xb5, 0x30, 0x61, 0x8a, 0xe9, 0xda, 0x84, 0x9a, 0x32, 0x61, 0x8a, 0xe9, 0xca, 0x84, 0x4c,
	0x0c, 0xea, 0xca, 0x83, 0xc1, 0x3a, 0xd6, 0xf3, 0x84, 0x3c, 0xf8, 0xc1, 0xd2, 0x9b, 0xd3, 0x88,
	0x04, 0xcb, 0x66, 0x43, 0x05, 0x51, 0xa3, 0xd7, 0x12, 0x44, 0xbf, 0x83, 0xb2, 0x1f, 0x70, 0x42,
	0xe3, 0xe6, 0x7e, 0xab, 0xd0, 0x6e, 0x9c, 0x1d, 0x9c, 0xa6, 0xf9, 0x78, 0xea, 0x48, 0x82, 0xab,
	0x19, 0x84, 0xa9, 0x8b, 0x79, 0xa8, 0x5d, 0x6c, 0xb5, 0x0a, 0xed, 0x92, 0x6b, 0x28, 0xc0, 0xe1,
	0xc8, 0x86, 0x7a, 0x48, 0x96, 0xde, 0x3a, 0x59, 0x0f, 0x64, 0xb2, 0x56, 0x43, 0xb2, 0x1c, 0xaf,
	0xf2, 0x55, 0xc4, 0x28, 0x22, 0xc2, 0x5d, 0x64, 0xde, 0x44, 0xea, 0xac, 0x0a, 0xe8, 0xcf, 0xd1,
	0x6b, 0x30, 0x67, 0x94, 0x86, 0x1e, 0x89, 0xef, 0x68, 0xf3, 0xb0, 0x55, 0x68, 0x57, 0xcf, 0x0e,
	0x33, 0xb6, 0xbc, 0xa3, 0x34, 0x14, 0x15, 0xe1, 0x1a, 0x33, 0xfd, 0x25, 0x7c, 0x1b, 0xf9, 0xb7,
	0x38, 0xf2, 0x22, 0xfc, 0x80, 0xa3, 0xe6, 0x23, 0xe5, 0x1c, 0x09, 0x0d, 0x04, 0x82, 0xbe, 0x86,
	0x06, 0x61, 0x9e, 0x1f, 0x07, 0xf7, 0x34, 0xf1, 0xee, 0x30, 0x0e, 0x9b, 0x8f, 0x5b, 0x85, 0xb6,
	0xe1, 0xd6, 0x08, 0x73, 0x24, 0x78, 0x81, 0x71, 0x88, 0xda, 0x60, 0xcd, 0xfc, 0x78, 0x21, 0x2a,
	0x22, 0x35, 0xfe, 0x48, 0x1a, 0xdf, 0x50, 0x78, 0x6a, 0xff, 0x37, 0xd0, 0xb8, 0x5d, 0x30, 0x46,
	0x62, 0xcc, 0x98, 0x8a, 0xf8, 0x13, 0xe5, 0xd2, 0x14, 0x95, 0x31, 0xbf, 0x04, 0x14, 0xc8, 0x32,
	0x5e, 0x09, 0x8c, 0xef, 0x28, 0x6b, 0x36, 0x5b, 0xa5, 0x76, 0xf5, 0xec, 0x69, 0xe6, 0x48, 0x1b,
	0xb5, 0xee, 0x5a, 0x41, 0x1e, 0x60, 0xa8, 0x0b, 0xd6, 0x54, 0x55, 0xb0, 0xa7, 0xc3, 0xca, 0x9a,
	0xc7, 0x52, 0xce, 0x71, 0x46, 0x4e, 0xbe, 0xc8, 0xdd, 0xfd, 0x69, 0x6e, 0xcd, 0xd0, 0x53, 0x30,
	0xe6, 0x09, 0x7d, 0x20, 0x71, 0x80, 0x9b, 0x4f, 0xa5, 0x93, 0xd2, 0x35, 0x42, 0xb0, 0x1b, 0x10,
	0xbe, 0x6c, 0x3e, 0x93, 0xb8, 0xfc, 0x46, 0x7f, 0x86, 0xea, 0x47, 0x1a, 0xe3, 0x55, 0xda, 0x3c,
	0x97, 0x79, 0xf1, 0x22, 0xa3, 0x30, 0x2d, 0xed, 0xd3, 0x0f, 0x34, 0xc6, 0x2a, 0x91, 0x5c, 0xf8,
	0x98, 0x7e, 0xdb, 0x2f, 0xa1, 0xac, 0xd3, 0x1b, 0xa0, 0x3c, 0xbc, 0x72, 0xdf, 0x39, 0x03, 0x6b,
	0x07, 0x55, 0xa1, 0x72, 0xee, 0x74, 0xbe, 0xef, 0x0d, 0xbb, 0x56, 0xc1, 0x7e, 0x03, 0xb5, 0xf3,
	0x45, 0xc6, 0x69, 0x0d, 0x00, 0xc5, 0xe8, 0x4d, 0xde, 0x76, 0xac, 0x1d, 0xf4, 0x18, 0x0e, 0x3a,
	0x97, 0xce, 0x70, 0xd8, 0x1b, 0x78, 0xd7, 0x03, 0xe7, 0xc6, 0x7b, 0xeb, 0xbc, 0xeb, 0x59, 0x05,
	0xbb, 0x0d, 0xb0, 0xd6, 0x29, 0x24, 0x76, 0x7b, 0x17, 0xce, 0x64, 0x30, 0xb6, 0x76, 0x50, 0x1d,
	0xcc, 0xab, 0xe1, 0xe0, 0xc6, 0xfb, 0x70, 0x35, 0x14, 0x9c, 0xff, 0xda, 0x85, 0x9a, 0xf0, 0x30,
	0x8e, 0x7f, 0x43, 0x1b, 0x3a, 0x01, 0x08, 0x94, 0x0c, 0x41, 0x2c, 0x49, 0xa2, 0xa9, 0x91, 0x7e,
	0x88, 0xbe, 0x83, 0xc3, 0x04, 0xcf, 0x23, 0x91, 0xf6, 0x5e, 0x86, 0x6f, 0x57, 0xf2, 0x59, 0x92,
	0x34, 0xa6, 0x9d, 0x94, 0xfd, 0xff, 0x61, 0x3f, 0xa0, 0xf1, 0x03, 0x4e, 0x98, 0x2f, 0x0a, 0x4a,
	0xb0, 0xee, 0x49, 0xd6, 0x46, 0x16, 0xee, 0x87, 0xa2, 0xee, 0xef, 0x12, 0x3a, 0xf3, 0x56, 0xad,
	0x4e, 0xb5, 0x2c, 0x10, 0xd8, 0x44, 0xb5, 0xbb, 0xe7, 0xa2, 0xf5, 0xa7, 0x74, 0xd5, 0xb8, 0x0c,
	0x4e, 0x35, 0x75, 0xdd, 0x3d, 0x8d, 0x5c, 0xf7, 0xcc, 0x35, 0x49, 0x73, 0xa3, 0x49, 0x7e, 0x0b,
	0x48, 0xb5, 0xaa, 0x9f, 0x62, 0x9c, 0xa4, 0xa2, 0x55, 0xc7, 0xda, 0x97, 0x1d, 0x4b, 0x10, 0xb4,
	0x06, 0x07, 0xfe, 0x6f, 0xeb, 0xe4, 0xf9, 0x8d, 0xaa, 0x97, 0x1d, 0xe7, 0x9d, 0x90, 0x15, 0xb1,
	0xd9, 0x3d, 0x6b, 0xdb, 0xdd, 0xf3, 0x18, 0x0c, 0xc2, 0xbc, 0x84, 0xde, 0x52, 0xd5, 0xde, 0x0c,
	0xb7, 0x42, 0x98, 0x2b, 0x96, 0xf2, 0x88, 0x74, 0x91, 0x04, 0x58, 0xb7, 0x35, 0xbd, 0x42, 0x7f,
	0x84, 0xc7, 0x84, 0x79, 0x0f, 0x7e, 0x44, 0x42, 0x4f, 0x18, 0x2d, 0x8c, 0x89, 0x39, 0x4e, 0x64,
	0x7b, 0x33, 0x5c, 0x44, 0xd8, 0x7b, 0x41, 0xbb, 0x96, 0xa4, 0xbe, 0xa0, 0x88, 0x28, 0xa6, 0x5b,
	0x94, 0xf5, 0x72, 0x83, 0x25, 0x37, 0x58, 0x7a, 0x83, 0x34, 0x5a, 0xe0, 0xf6, 0xdf, 0x8b, 0x50,
	0x77, 0x38, 0x27, 0x7c, 0x11, 0x62, 0x95, 0x59, 0xc7, 0x1b, 0x99, 0x75, 0xb9, 0x93, 0xe6, 0xd6,
	0x8b, 0x5c, 0x02, 0x15, 0x35, 0x35, 0x93, 0x42, 0x5f, 0x41, 0xdd, 0xd7, 0xc2, 0x94, 0x1b, 0xd4,
	0x7d, 0x57, 0x5b, 0x81, 0xd2, 0x0f, 0xb9, 0xb8, 0xed, 0x6e, 0xc4, 0x2d, 0x73, 0x25, 0xee, 0xe5,
	0xae, 0xc4, 0x3f, 0x00, 0xe2, 0x7e, 0x32, 0xc5, 0xdc, 0x93, 0xbc, 0x34, 0xf1, 0x16, 0x69, 0x2e,
	0x59, 0x8a, 0xd2, 0x51, 0x84, 0x09, 0x09, 0x45, 0x03, 0x27, 0xcc, 0xbb, 0x23, 0x89, 0xb8, 0x27,
	0xc9, 0x0c, 0xcb, 0xa4, 0x32, 0xdc, 0x2a, 0x61, 0x17, 0x02, 0x1b, 0x93, 0x19, 0xce, 0x38, 0xdd,
	0xc8, 0x39, 0x3d, 0x1b, 0xa7, 0x47, 0xb9, 0x38, 0x9d, 0x1b, 0x50, 0x56, 0xaa, 0xec, 0x7f, 0x97,
	0xa0, 0x7a, 0x41, 0xa3, 0x88, 0xfe, 0xa4, 0xbc, 0xb6, 0x99, 0xe4, 0x85, 0x2f, 0x24, 0x79, 0x71,
	0x23, 0xc9, 0xb7, 0x0c, 0x2e, 0x6d, 0x1b, 0xfc, 0x8b, 0x8e, 0x3b, 0x01, 0x20, 0xcc, 0x0b, 0x71,
	0x84, 0x39, 0x56, 0xbe, 0x33, 0x5c, 0x93, 0xb0, 0xae, 0x02, 0xd0, 0x9b, 0xf4, 0xb0, 0x65, 0xd9,
	0x01, 0x4f, 0x32, 0x1d, 0x30, 0x73, 0x8e, 0xd3, 0x91, 0x64, 0x4a, 0x7d, 0xf1, 0x15, 0xd4, 0xf5,
	0x25, 0xa7, 0x77, 0xab, 0xe2, 0xac, 0x29, 0x50, 0x31, 0xa3, 0xd7, 0xf0, 0x48, 0x33, 0x05, 0x0b,
	0xc6, 0xe9, 0xcc, 0xcb, 0xb8, 0xd5, 0x74, 0x91, 0xa2, 0x75, 0x24, 0x69, 0xb4, 0xed, 0x62, 0x33,
	0x5f, 0x0a, 0xa2, 0x49, 0xdd, 0xfb, 0x71, 0x8c, 0xa3, 0x75, 0xc1, 0x9a, 0x1a, 0xd1, 0x75, 0xa6,
	0xc9, 0x32, 0xc1, 0x54, 0x61, 0x56, 0x35, 0x26, 0x27, 0x45, 0x07, 0xca, 0x5a, 0x0d, 0x82, 0xc6,
	0x64, 0xd4, 0x73, 0xbd, 0xab, 0xeb, 0x9e, 0xeb, 0x8c, 0xfb, 0x57, 0x43, 0x6b, 0x07, 0x59, 0x50,
	0x1b, 0xdd, 0x0c, 0x3b, 0x97, 0x57, 0xee, 0xb0, 0xff, 0xa1, 0xe7, 0x5a, 0x05, 0x74, 0x00, 0xf5,
	0x0b, 0xb7, 0xdf, 0x1b, 0x76, 0xbd, 0xf7, 0x3d, 0xb7, 0x7f, 0x71, 0x63, 0x15, 0xc5, 0xb0, 0x29,
	0x6a, 0x6a, 0x74, 0xef, 0x27, 0xf8, 0x0b, 0xfd, 0x36, 0x93, 0xb0, 0xc5, 0x5c, 0xc2, 0x1e, 0x83,
	0xa1, 0x7a, 0x88, 0xee, 0xb5, 0x75, 0xb7, 0x22, 0xd7, 0x8a, 0x94, 0x1e, 0x1f, 0x72, 0xc7, 0x17,
	0xf5, 0x68, 0x4d, 0xa6, 0xc1, 0xea, 0xfa, 0xc3, 0x73, 0x9a, 0xfc, 0x82, 0xf2, 0x93, 0xed, 0x82,
	0xcc, 0x96, 0x63, 0x2e, 0x61, 0x4a, 0xdb, 0x63, 0x64, 0x22, 0xc5, 0x67, 0xb2, 0x49, 0x01, 0x0e,
	0x17, 0xd3, 0x88, 0x26, 0x4a, 0x2f, 0xab, 0x52, 0x04, 0x05, 0xc9, 0x22, 0xb6, 0xa1, 0x9e, 0xef,
	0x90, 0xaa, 0x12, 0xab, 0x34, 0xd3, 0x13, 0x37, 0x6b, 0xa2, 0xb2, 0x55, 0x13, 0xa7, 0x70, 0xe8,
	0x73, 0xee, 0x07, 0xf7, 0xf2, 0x08, 0x3f, 0xe2, 0xa5, 0x17, 0x11, 0xc6, 0x9b, 0x86, 0x1c, 0x58,
	0x0e, 0xd6, 0xa4, 0xef, 0xf1, 0x72, 0x40, 0x18, 0xcf, 0x0e, 0x88, 0x66, 0x6e, 0x40, 0xb4, 0xff,
	0x53, 0x80, 0xc6, 0x18, 0xff, 0xcc, 0x9d, 0x98, 0x93, 0xff, 0xad, 0xd7, 0xd8, 0x22, 0xda, 0xf4,
	0x9a, 0x80, 0x7e, 0xb5, 0xd7, 0x4e, 0x40, 0x4d, 0x7d, 0x6a, 0x76, 0xac, 0x28, 0xeb, 0x24, 0x22,
	0xc6, 0x28, 0xfb, 0x03, 0xd4, 0x2e, 0x29, 0xff, 0x15, 0xef, 0x91, 0x23, 0x28, 0xfb, 0x0b, 0x7e,
	0x4f, 0x93, 0x55, 0x5e, 0xaa, 0x55, 0x6e, 0x40, 0x28, 0xe5, 0x06, 0x04, 0xfb, 0x23, 0xc0, 0x7b,
	0xc2, 0xc8, 0x97, 0x24, 0x6f, 0xc6, 0xb5, 0xb8, 0x15, 0xd7, 0xb5, 0xee, 0xd2, 0xa6, 0xee, 0x07,
	0xa1, 0x60, 0xed, 0xbc, 0x8a, 0x5c, 0x3b, 0xdc, 0x9e, 0x81, 0xb1, 0x9a, 0x8a, 0x85, 0x66, 0x35,
	0x3d, 0xa7, 0x9a, 0xe5, 0x98, 0x1c, 0x8a, 0x5b, 0x5d, 0x12, 0x6e, 0x49, 0x1c, 0x7a, 0x1b, 0x63,
	0xce, 0xbe, 0xa0, 0x9c, 0x93, 0x38, 0x5c, 0xbd, 0x26, 0x9e, 0xe9, 0x19, 0x3c, 0xf6, 0x75, 0x3b,
	0x35, 0xd5, 0xb8, 0x3d, 0xf4, 0x67, 0xd8, 0x8e, 0xe0, 0x50, 0xf2, 0x8d, 0xb0, 0x9f, 0x04, 0xf7,
	0x97, 0x94, 0xaf, 0x2e, 0xbf, 0xb5, 0x73, 0x0a, 0x5b, 0xd3, 0x93, 0x22, 0x49, 0x79, 0x3a, 0x6b,
	0x24, 0x22, 0x04, 0x0a, 0x6d, 0x4c, 0xca, 0xca, 0x64, 0x8d, 0x02, 0x1c, 0x6e, 0xff, 0xad, 0x08,
	0xcf, 0x1c, 0xc6, 0x68, 0x40, 0x7c, 0x8e, 0x45, 0xec, 0x7e, 0x20, 0xfc, 0x5e, 0xea, 0x57, 0x6a,
	0x87, 0x60, 0xd2, 0x39, 0x4e, 0xe4, 0xc4, 0x24, 0xf5, 0x36, 0xce, 0x5e, 0x67, 0x9f, 0x2e, 0x9f,
	0xdf, 0x7a, 0x7a, 0xb5, 0xda, 0xe7, 0xae, 0x45, 0x64, 0x43, 0x57, 0xfc, 0x5c, 0xb7, 0x2a, 0x7d,
	0xfe, 0xc5, 0xb9, 0x79, 0xb7, 0x7c, 0xea, 0x51, 0xb1, 0xf7, 0xa9, 0x47, 0x85, 0x7d, 0x02, 0x66,
	0x6a, 0x10, 0xaa, 0x40, 0xc9, 0xe9, 0x76, 0xad, 0x1d, 0xf1, 0xd1, 0xed, 0x0d, 0xac, 0x82, 0xfd,
	0xcf, 0x22, 0x3c, 0x9a, 0xc8, 0x47, 0x56, 0xfa, 0x2e, 0xf8, 0xad, 0x7e, 0xcf, 0xff, 0x36, 0x28,
	0x6d, 0xfc, 0x36, 0x10, 0x4f, 0x82, 0x10, 0xb3, 0x40, 0x4f, 0xb1, 0xf2, 0x3b, 0xff, 0xf4, 0x53,
	0x45, 0xba, 0x7e, 0xfa, 0xe5, 0x1c, 0xa1, 0x5f, 0xd7, 0xd9, 0xe9, 0x64, 0x95, 0x98, 0x95, 0x5c,
	0x62, 0xf6, 0x00, 0x25, 0x38, 0xf2, 0x39, 0x5e, 0xa5, 0x65, 0xda, 0xc7, 0xaa, 0x67, 0x4f, 0x32,
	0x91, 0x74, 0x15, 0x93, 0x3c, 0xbd, 0x98, 0xa9, 0xd7, 0x2b, 0xd9, 0xdf, 0x8e, 0xa0, 0x8c, 0x63,
	0xff, 0x36, 0xc2, 0xfa, 0x56, 0xd4, 0x2b, 0xfd, 0xae, 0x56, 0x97, 0xbb, 0xbe, 0x31, 0x8c, 0xd5,
	0xdd, 0x6e, 0x9f, 0x41, 0x2d, 0x2b, 0x16, 0x35, 0xa0, 0x98, 0x7a, 0xb1, 0x48, 0x42, 0xe1, 0x82,
	0x8c, 0xeb, 0xe4, 0xb7, 0xfd, 0x17, 0x38, 0xb8, 0x48, 0x08, 0x8e, 0x43, 0x17, 0xcf, 0xfc, 0xe4,
	0x47, 0x15, 0x04, 0x0b, 0x4a, 0x8b, 0x74, 0x74, 0x11, 0x9f, 0xc2, 0xb9, 0x77, 0x92, 0x4d, 0x0e,
	0x5b, 0xfa, 0x9f, 0x8c, 0x42, 0xc4, 0x94, 0x75, 0x04, 0xe5, 0x44, 0xee, 0xd7, 0xe5, 0xa5, 0x57,
	0xe8, 0x10, 0xf6, 0xe8, 0xdc, 0xe3, 0x4c, 0x27, 0xd2, 0x2e, 0x9d, 0x8f, 0xd9, 0xef, 0xff, 0x51,
	0x80, 0xb2, 0x7a, 0x83, 0xa3, 0x47, 0x60, 0x39, 0x1d, 0x71, 0x1f, 0x7b, 0x93, 0x61, 0xb7, 0x77,
	0xd1, 0x1f, 0xf6, 0x44, 0x96, 0xbc, 0x84, 0x13, 0x8d, 0x9e, 0x3b, 0x43, 0xef, 0x87, 0xfe, 0xf8,
	0xd2, 0xd3, 0x8f, 0xa8, 0xd1, 0xd8, 0x19, 0x4f, 0x46, 0x56, 0x01, 0x7d, 0x0d, 0x2d, 0xcd, 0xd2,
	0xed, 0x0d, 0x7a, 0xe3, 0xde, 0xa7, 0xb8, 0x8a, 0xa8, 0x05, 0xcf, 0x35, 0x97, 0x78, 0x74, 0xbd,
	0xed, 0x79, 0xd7, 0x6e, 0xff, 0xbd, 0xd3, 0xb9, 0xf1, 0xae, 0xaf, 0x06, 0xfd, 0xce, 0x8d, 0x55,
	0x12, 0x83, 0x81, 0xe6, 0xb8, 0x9e, 0x9c, 0x0f, 0xfa, 0xa3, 0x4b, 0x6b, 0x17, 0x3d, 0x81, 0x43,
	0x8d, 0x39, 0x93, 0x6e, 0x7f, 0xec, 0xb9, 0xbd, 0x91, 0x78, 0x8a, 0xed, 0xa1, 0x23, 0x40, 0x1b,
	0xe2, 0xae, 0x46, 0x63, 0xab, 0x7c, 0xfe, 0xdd, 0x87, 0x6f, 0xa7, 0x34, 0xf2, 0xe3, 0xe9, 0xe9,
	0x9b, 0x33, 0xce, 0x4f, 0x03, 0x3a, 0x7b, 0x25, 0x7f, 0x82, 0x05, 0x34, 0x7a, 0xc5, 0x70, 0xf2,
	0x40, 0x02, 0xcc, 0x5e, 0xa5, 0x3f, 0xc8, 0x6e, 0xcb, 0x92, 0xf8, 0xa7, 0xff, 0x06, 0x00, 0x00,
	0xff, 0xff, 0xc5, 0x20, 0xeb, 0x63, 0x34, 0x13, 0x00, 0x00,
}
