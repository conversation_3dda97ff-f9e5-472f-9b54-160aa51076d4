// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ugc/content.proto

package content // import "golang.52tt.com/protocol/services/ugc/content"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ContentType int32

const (
	ContentType_FORMATTED   ContentType = 0
	ContentType_ORIGIN_TEXT ContentType = 1
)

var ContentType_name = map[int32]string{
	0: "FORMATTED",
	1: "ORIGIN_TEXT",
}
var ContentType_value = map[string]int32{
	"FORMATTED":   0,
	"ORIGIN_TEXT": 1,
}

func (x ContentType) String() string {
	return proto.EnumName(ContentType_name, int32(x))
}
func (ContentType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{0}
}

type PostOrigin int32

const (
	PostOrigin_POST_ORIG_NORMAL                PostOrigin = 0
	PostOrigin_POST_ORIN_ICE_BREAK             PostOrigin = 1
	PostOrigin_POST_ORIN_ACT                   PostOrigin = 2
	PostOrigin_POST_ORIG_REGISTER              PostOrigin = 3
	PostOrigin_POST_ORIG_KOL                   PostOrigin = 4
	PostOrigin_POST_ORIG_TGL                   PostOrigin = 5
	PostOrigin_POST_ORIG_TGL_COVER             PostOrigin = 6
	PostOrigin_POST_ORIG_AI_RAPER              PostOrigin = 7
	PostOrigin_POST_ORIG_AI_FOREIGN_LANGUAGE   PostOrigin = 8
	PostOrigin_POST_ORIG_GENERAL_CONTENT       PostOrigin = 9
	PostOrigin_POST_ORIG_ANCIENT               PostOrigin = 1000
	PostOrigin_POST_ORIG_NON_PUBLIC_CONVERSION PostOrigin = 1001
)

var PostOrigin_name = map[int32]string{
	0:    "POST_ORIG_NORMAL",
	1:    "POST_ORIN_ICE_BREAK",
	2:    "POST_ORIN_ACT",
	3:    "POST_ORIG_REGISTER",
	4:    "POST_ORIG_KOL",
	5:    "POST_ORIG_TGL",
	6:    "POST_ORIG_TGL_COVER",
	7:    "POST_ORIG_AI_RAPER",
	8:    "POST_ORIG_AI_FOREIGN_LANGUAGE",
	9:    "POST_ORIG_GENERAL_CONTENT",
	1000: "POST_ORIG_ANCIENT",
	1001: "POST_ORIG_NON_PUBLIC_CONVERSION",
}
var PostOrigin_value = map[string]int32{
	"POST_ORIG_NORMAL":                0,
	"POST_ORIN_ICE_BREAK":             1,
	"POST_ORIN_ACT":                   2,
	"POST_ORIG_REGISTER":              3,
	"POST_ORIG_KOL":                   4,
	"POST_ORIG_TGL":                   5,
	"POST_ORIG_TGL_COVER":             6,
	"POST_ORIG_AI_RAPER":              7,
	"POST_ORIG_AI_FOREIGN_LANGUAGE":   8,
	"POST_ORIG_GENERAL_CONTENT":       9,
	"POST_ORIG_ANCIENT":               1000,
	"POST_ORIG_NON_PUBLIC_CONVERSION": 1001,
}

func (x PostOrigin) String() string {
	return proto.EnumName(PostOrigin_name, int32(x))
}
func (PostOrigin) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{1}
}

type IsSystem int32

const (
	IsSystem_POST_BY_PEOPLE      IsSystem = 0
	IsSystem_POST_BY_SETTING_TAG IsSystem = 1
)

var IsSystem_name = map[int32]string{
	0: "POST_BY_PEOPLE",
	1: "POST_BY_SETTING_TAG",
}
var IsSystem_value = map[string]int32{
	"POST_BY_PEOPLE":      0,
	"POST_BY_SETTING_TAG": 1,
}

func (x IsSystem) String() string {
	return proto.EnumName(IsSystem_name, int32(x))
}
func (IsSystem) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{2}
}

type UnmarshalType int32

const (
	UnmarshalType_UNMARSHAL_TYPE_DEFAULT  UnmarshalType = 0
	UnmarshalType_UNMARSHAL_TYPE_PROTOBUF UnmarshalType = 1
	UnmarshalType_UNMARSHAL_TYPE_JSON     UnmarshalType = 2
)

var UnmarshalType_name = map[int32]string{
	0: "UNMARSHAL_TYPE_DEFAULT",
	1: "UNMARSHAL_TYPE_PROTOBUF",
	2: "UNMARSHAL_TYPE_JSON",
}
var UnmarshalType_value = map[string]int32{
	"UNMARSHAL_TYPE_DEFAULT":  0,
	"UNMARSHAL_TYPE_PROTOBUF": 1,
	"UNMARSHAL_TYPE_JSON":     2,
}

func (x UnmarshalType) String() string {
	return proto.EnumName(UnmarshalType_name, int32(x))
}
func (UnmarshalType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{3}
}

type ContentOrigin int32

const (
	ContentOrigin_ORIGIN_DEFAULT                   ContentOrigin = 0
	ContentOrigin_ORIGIN_GAME_DISTRICT             ContentOrigin = 1
	ContentOrigin_ORIGIN_SOCIAL_COMMUNITY_DISTRICT ContentOrigin = 2
)

var ContentOrigin_name = map[int32]string{
	0: "ORIGIN_DEFAULT",
	1: "ORIGIN_GAME_DISTRICT",
	2: "ORIGIN_SOCIAL_COMMUNITY_DISTRICT",
}
var ContentOrigin_value = map[string]int32{
	"ORIGIN_DEFAULT":                   0,
	"ORIGIN_GAME_DISTRICT":             1,
	"ORIGIN_SOCIAL_COMMUNITY_DISTRICT": 2,
}

func (x ContentOrigin) String() string {
	return proto.EnumName(ContentOrigin_name, int32(x))
}
func (ContentOrigin) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{4}
}

type BusinessType int32

const (
	BusinessType_BUSINESS_TYPE_DEFAULT           BusinessType = 0
	BusinessType_BUSINESS_TYPE_CHANNEL_PLAY_GAME BusinessType = 1
)

var BusinessType_name = map[int32]string{
	0: "BUSINESS_TYPE_DEFAULT",
	1: "BUSINESS_TYPE_CHANNEL_PLAY_GAME",
}
var BusinessType_value = map[string]int32{
	"BUSINESS_TYPE_DEFAULT":           0,
	"BUSINESS_TYPE_CHANNEL_PLAY_GAME": 1,
}

func (x BusinessType) String() string {
	return proto.EnumName(BusinessType_name, int32(x))
}
func (BusinessType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{5}
}

type AttachmentDownloadPrivacy int32

const (
	AttachmentDownloadPrivacy_PRIVACY_DEFAULT AttachmentDownloadPrivacy = 0
	AttachmentDownloadPrivacy_PRIVACY_PRIVATE AttachmentDownloadPrivacy = 1
	AttachmentDownloadPrivacy_PRIVACY_PUBLIC  AttachmentDownloadPrivacy = 2
)

var AttachmentDownloadPrivacy_name = map[int32]string{
	0: "PRIVACY_DEFAULT",
	1: "PRIVACY_PRIVATE",
	2: "PRIVACY_PUBLIC",
}
var AttachmentDownloadPrivacy_value = map[string]int32{
	"PRIVACY_DEFAULT": 0,
	"PRIVACY_PRIVATE": 1,
	"PRIVACY_PUBLIC":  2,
}

func (x AttachmentDownloadPrivacy) String() string {
	return proto.EnumName(AttachmentDownloadPrivacy_name, int32(x))
}
func (AttachmentDownloadPrivacy) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{6}
}

type PostPrivacyPolicy int32

const (
	PostPrivacyPolicy_POST_PRIVACY_POLICY_DEFAULT PostPrivacyPolicy = 0
	PostPrivacyPolicy_POST_PRIVACY_POLICY_PRIVATE PostPrivacyPolicy = 1
)

var PostPrivacyPolicy_name = map[int32]string{
	0: "POST_PRIVACY_POLICY_DEFAULT",
	1: "POST_PRIVACY_POLICY_PRIVATE",
}
var PostPrivacyPolicy_value = map[string]int32{
	"POST_PRIVACY_POLICY_DEFAULT": 0,
	"POST_PRIVACY_POLICY_PRIVATE": 1,
}

func (x PostPrivacyPolicy) String() string {
	return proto.EnumName(PostPrivacyPolicy_name, int32(x))
}
func (PostPrivacyPolicy) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{7}
}

type ZonePolicy int32

const (
	ZonePolicy_DEFAULT   ZonePolicy = 0
	ZonePolicy_ONLY_ZONE ZonePolicy = 1
)

var ZonePolicy_name = map[int32]string{
	0: "DEFAULT",
	1: "ONLY_ZONE",
}
var ZonePolicy_value = map[string]int32{
	"DEFAULT":   0,
	"ONLY_ZONE": 1,
}

func (x ZonePolicy) String() string {
	return proto.EnumName(ZonePolicy_name, int32(x))
}
func (ZonePolicy) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{8}
}

type ContentStatus int32

const (
	ContentStatus_CONTENT_STATUS_NONE                      ContentStatus = 0
	ContentStatus_CONTENT_STATUS_UNDER_REVIEW              ContentStatus = 1
	ContentStatus_CONTENT_STATUS_UNDER_REVIEW_AND_PREPARED ContentStatus = 2
	ContentStatus_CONTENT_STATUS_SUSPICIOUS                ContentStatus = 3
	ContentStatus_CONTENT_STATUS_ILLEGAL                   ContentStatus = 4
	ContentStatus_CONTENT_STATUS_NORMAL                    ContentStatus = 5
	ContentStatus_CONTENT_STATUS_DELETED                   ContentStatus = 6
	ContentStatus_CONTENT_STATUS_BANNED                    ContentStatus = 7
)

var ContentStatus_name = map[int32]string{
	0: "CONTENT_STATUS_NONE",
	1: "CONTENT_STATUS_UNDER_REVIEW",
	2: "CONTENT_STATUS_UNDER_REVIEW_AND_PREPARED",
	3: "CONTENT_STATUS_SUSPICIOUS",
	4: "CONTENT_STATUS_ILLEGAL",
	5: "CONTENT_STATUS_NORMAL",
	6: "CONTENT_STATUS_DELETED",
	7: "CONTENT_STATUS_BANNED",
}
var ContentStatus_value = map[string]int32{
	"CONTENT_STATUS_NONE":                      0,
	"CONTENT_STATUS_UNDER_REVIEW":              1,
	"CONTENT_STATUS_UNDER_REVIEW_AND_PREPARED": 2,
	"CONTENT_STATUS_SUSPICIOUS":                3,
	"CONTENT_STATUS_ILLEGAL":                   4,
	"CONTENT_STATUS_NORMAL":                    5,
	"CONTENT_STATUS_DELETED":                   6,
	"CONTENT_STATUS_BANNED":                    7,
}

func (x ContentStatus) String() string {
	return proto.EnumName(ContentStatus_name, int32(x))
}
func (ContentStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{9}
}

type StickyStatus int32

const (
	StickyStatus_STICKY_NONE StickyStatus = 0
	StickyStatus_STICKY      StickyStatus = 1
)

var StickyStatus_name = map[int32]string{
	0: "STICKY_NONE",
	1: "STICKY",
}
var StickyStatus_value = map[string]int32{
	"STICKY_NONE": 0,
	"STICKY":      1,
}

func (x StickyStatus) String() string {
	return proto.EnumName(StickyStatus_name, int32(x))
}
func (StickyStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{10}
}

type WeightStatus int32

const (
	WeightStatus_WeiStDefault WeightStatus = 0
	WeightStatus_WeiStValid   WeightStatus = 1
	WeightStatus_WeiStExpired WeightStatus = 2
	WeightStatus_WeiStFuture  WeightStatus = 3
)

var WeightStatus_name = map[int32]string{
	0: "WeiStDefault",
	1: "WeiStValid",
	2: "WeiStExpired",
	3: "WeiStFuture",
}
var WeightStatus_value = map[string]int32{
	"WeiStDefault": 0,
	"WeiStValid":   1,
	"WeiStExpired": 2,
	"WeiStFuture":  3,
}

func (x WeightStatus) String() string {
	return proto.EnumName(WeightStatus_name, int32(x))
}
func (WeightStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{11}
}

type PostMachineStatus int32

const (
	PostMachineStatus_MachineDefault    PostMachineStatus = 0
	PostMachineStatus_MachineSuspicious PostMachineStatus = 1
	PostMachineStatus_MachineNormal     PostMachineStatus = 2
	PostMachineStatus_MachineReject     PostMachineStatus = 3
)

var PostMachineStatus_name = map[int32]string{
	0: "MachineDefault",
	1: "MachineSuspicious",
	2: "MachineNormal",
	3: "MachineReject",
}
var PostMachineStatus_value = map[string]int32{
	"MachineDefault":    0,
	"MachineSuspicious": 1,
	"MachineNormal":     2,
	"MachineReject":     3,
}

func (x PostMachineStatus) String() string {
	return proto.EnumName(PostMachineStatus_name, int32(x))
}
func (PostMachineStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{12}
}

type ForcePostInUserRcmdFeedStatus int32

const (
	ForcePostInUserRcmdFeedStatus_Total    ForcePostInUserRcmdFeedStatus = 0
	ForcePostInUserRcmdFeedStatus_NotBegin ForcePostInUserRcmdFeedStatus = 1
	ForcePostInUserRcmdFeedStatus_InConfig ForcePostInUserRcmdFeedStatus = 2
	ForcePostInUserRcmdFeedStatus_Expired  ForcePostInUserRcmdFeedStatus = 3
)

var ForcePostInUserRcmdFeedStatus_name = map[int32]string{
	0: "Total",
	1: "NotBegin",
	2: "InConfig",
	3: "Expired",
}
var ForcePostInUserRcmdFeedStatus_value = map[string]int32{
	"Total":    0,
	"NotBegin": 1,
	"InConfig": 2,
	"Expired":  3,
}

func (x ForcePostInUserRcmdFeedStatus) String() string {
	return proto.EnumName(ForcePostInUserRcmdFeedStatus_name, int32(x))
}
func (ForcePostInUserRcmdFeedStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{13}
}

type AttachmentInfo_AttachmentType int32

const (
	AttachmentInfo_NONE              AttachmentInfo_AttachmentType = 0
	AttachmentInfo_IMAGE             AttachmentInfo_AttachmentType = 1
	AttachmentInfo_GIF               AttachmentInfo_AttachmentType = 2
	AttachmentInfo_VIDEO             AttachmentInfo_AttachmentType = 3
	AttachmentInfo_CMS               AttachmentInfo_AttachmentType = 4
	AttachmentInfo_AUDIO             AttachmentInfo_AttachmentType = 5
	AttachmentInfo_TEXT              AttachmentInfo_AttachmentType = 6
	AttachmentInfo_URLCard           AttachmentInfo_AttachmentType = 7
	AttachmentInfo_Vote              AttachmentInfo_AttachmentType = 8
	AttachmentInfo_AtUser            AttachmentInfo_AttachmentType = 9
	AttachmentInfo_AtSocialCommunity AttachmentInfo_AttachmentType = 10
)

var AttachmentInfo_AttachmentType_name = map[int32]string{
	0:  "NONE",
	1:  "IMAGE",
	2:  "GIF",
	3:  "VIDEO",
	4:  "CMS",
	5:  "AUDIO",
	6:  "TEXT",
	7:  "URLCard",
	8:  "Vote",
	9:  "AtUser",
	10: "AtSocialCommunity",
}
var AttachmentInfo_AttachmentType_value = map[string]int32{
	"NONE":              0,
	"IMAGE":             1,
	"GIF":               2,
	"VIDEO":             3,
	"CMS":               4,
	"AUDIO":             5,
	"TEXT":              6,
	"URLCard":           7,
	"Vote":              8,
	"AtUser":            9,
	"AtSocialCommunity": 10,
}

func (x AttachmentInfo_AttachmentType) String() string {
	return proto.EnumName(AttachmentInfo_AttachmentType_name, int32(x))
}
func (AttachmentInfo_AttachmentType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{0, 0}
}

type PostInfo_PostType int32

const (
	PostInfo_NONE  PostInfo_PostType = 0
	PostInfo_TEXT  PostInfo_PostType = 1
	PostInfo_IMAGE PostInfo_PostType = 2
	PostInfo_VIDEO PostInfo_PostType = 3
	PostInfo_CMS   PostInfo_PostType = 4
	PostInfo_AUDIO PostInfo_PostType = 5
)

var PostInfo_PostType_name = map[int32]string{
	0: "NONE",
	1: "TEXT",
	2: "IMAGE",
	3: "VIDEO",
	4: "CMS",
	5: "AUDIO",
}
var PostInfo_PostType_value = map[string]int32{
	"NONE":  0,
	"TEXT":  1,
	"IMAGE": 2,
	"VIDEO": 3,
	"CMS":   4,
	"AUDIO": 5,
}

func (x PostInfo_PostType) String() string {
	return proto.EnumName(PostInfo_PostType_name, int32(x))
}
func (PostInfo_PostType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{1, 0}
}

type AddPostDirectlyReq_Availability int32

const (
	AddPostDirectlyReq_ALL     AddPostDirectlyReq_Availability = 0
	AddPostDirectlyReq_ANDROID AddPostDirectlyReq_Availability = 1
	AddPostDirectlyReq_IOS     AddPostDirectlyReq_Availability = 2
)

var AddPostDirectlyReq_Availability_name = map[int32]string{
	0: "ALL",
	1: "ANDROID",
	2: "IOS",
}
var AddPostDirectlyReq_Availability_value = map[string]int32{
	"ALL":     0,
	"ANDROID": 1,
	"IOS":     2,
}

func (x AddPostDirectlyReq_Availability) String() string {
	return proto.EnumName(AddPostDirectlyReq_Availability_name, int32(x))
}
func (AddPostDirectlyReq_Availability) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{6, 0}
}

type ReportPostViewReq_ViewType int32

const (
	ReportPostViewReq_NONE ReportPostViewReq_ViewType = 0
	ReportPostViewReq_NEW  ReportPostViewReq_ViewType = 1
)

var ReportPostViewReq_ViewType_name = map[int32]string{
	0: "NONE",
	1: "NEW",
}
var ReportPostViewReq_ViewType_value = map[string]int32{
	"NONE": 0,
	"NEW":  1,
}

func (x ReportPostViewReq_ViewType) String() string {
	return proto.EnumName(ReportPostViewReq_ViewType_name, int32(x))
}
func (ReportPostViewReq_ViewType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{34, 0}
}

type AttachmentInfo struct {
	Key       string                        `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Type      AttachmentInfo_AttachmentType `protobuf:"varint,2,opt,name=type,proto3,enum=ugc.content.AttachmentInfo_AttachmentType" json:"type,omitempty"`
	Content   string                        `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	Extra     string                        `protobuf:"bytes,4,opt,name=extra,proto3" json:"extra,omitempty"`
	Status    ContentStatus                 `protobuf:"varint,5,opt,name=status,proto3,enum=ugc.content.ContentStatus" json:"status,omitempty"`
	VmContent string                        `protobuf:"bytes,6,opt,name=vm_content,json=vmContent,proto3" json:"vm_content,omitempty"`
	// 给type == video用的, 转码参数
	Param string `protobuf:"bytes,10,opt,name=param,proto3" json:"param,omitempty"`
	// 原始视频封面url
	OriginVideoCover     string   `protobuf:"bytes,11,opt,name=origin_video_cover,json=originVideoCover,proto3" json:"origin_video_cover,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AttachmentInfo) Reset()         { *m = AttachmentInfo{} }
func (m *AttachmentInfo) String() string { return proto.CompactTextString(m) }
func (*AttachmentInfo) ProtoMessage()    {}
func (*AttachmentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{0}
}
func (m *AttachmentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AttachmentInfo.Unmarshal(m, b)
}
func (m *AttachmentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AttachmentInfo.Marshal(b, m, deterministic)
}
func (dst *AttachmentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AttachmentInfo.Merge(dst, src)
}
func (m *AttachmentInfo) XXX_Size() int {
	return xxx_messageInfo_AttachmentInfo.Size(m)
}
func (m *AttachmentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AttachmentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AttachmentInfo proto.InternalMessageInfo

func (m *AttachmentInfo) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *AttachmentInfo) GetType() AttachmentInfo_AttachmentType {
	if m != nil {
		return m.Type
	}
	return AttachmentInfo_NONE
}

func (m *AttachmentInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *AttachmentInfo) GetExtra() string {
	if m != nil {
		return m.Extra
	}
	return ""
}

func (m *AttachmentInfo) GetStatus() ContentStatus {
	if m != nil {
		return m.Status
	}
	return ContentStatus_CONTENT_STATUS_NONE
}

func (m *AttachmentInfo) GetVmContent() string {
	if m != nil {
		return m.VmContent
	}
	return ""
}

func (m *AttachmentInfo) GetParam() string {
	if m != nil {
		return m.Param
	}
	return ""
}

func (m *AttachmentInfo) GetOriginVideoCover() string {
	if m != nil {
		return m.OriginVideoCover
	}
	return ""
}

type PostInfo struct {
	PostId               string            `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	TopicId              string            `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	PostType             PostInfo_PostType `protobuf:"varint,3,opt,name=post_type,json=postType,proto3,enum=ugc.content.PostInfo_PostType" json:"post_type,omitempty"`
	Content              string            `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	Attachments          []*AttachmentInfo `protobuf:"bytes,5,rep,name=attachments,proto3" json:"attachments,omitempty"`
	CreateAt             uint64            `protobuf:"varint,6,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	CommentCount         uint32            `protobuf:"varint,7,opt,name=comment_count,json=commentCount,proto3" json:"comment_count,omitempty"`
	AttitudeCount        uint32            `protobuf:"varint,8,opt,name=attitude_count,json=attitudeCount,proto3" json:"attitude_count,omitempty"`
	ViewCount            uint32            `protobuf:"varint,9,opt,name=view_count,json=viewCount,proto3" json:"view_count,omitempty"`
	UserId               uint32            `protobuf:"varint,10,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Status               ContentStatus     `protobuf:"varint,11,opt,name=status,proto3,enum=ugc.content.ContentStatus" json:"status,omitempty"`
	TopLevelCommentCount uint32            `protobuf:"varint,12,opt,name=top_level_comment_count,json=topLevelCommentCount,proto3" json:"top_level_comment_count,omitempty"`
	ShareCount           uint32            `protobuf:"varint,13,opt,name=share_count,json=shareCount,proto3" json:"share_count,omitempty"`
	Label                string            `protobuf:"bytes,14,opt,name=label,proto3" json:"label,omitempty"`
	MagnifiedViewCount   uint32            `protobuf:"varint,15,opt,name=magnified_view_count,json=magnifiedViewCount,proto3" json:"magnified_view_count,omitempty"`
	SubTopicId           string            `protobuf:"bytes,16,opt,name=sub_topic_id,json=subTopicId,proto3" json:"sub_topic_id,omitempty"`
	Tags                 []uint32          `protobuf:"varint,17,rep,packed,name=tags,proto3" json:"tags,omitempty"`
	ContentType          int32             `protobuf:"varint,18,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	StickyStatus         StickyStatus      `protobuf:"varint,19,opt,name=sticky_status,json=stickyStatus,proto3,enum=ugc.content.StickyStatus" json:"sticky_status,omitempty"`
	// TT5.4.0 地理位置信息
	GeoTopicId  string                    `protobuf:"bytes,20,opt,name=geo_topic_id,json=geoTopicId,proto3" json:"geo_topic_id,omitempty"`
	LngLat      string                    `protobuf:"bytes,21,opt,name=lng_lat,json=lngLat,proto3" json:"lng_lat,omitempty"`
	FullGeoName string                    `protobuf:"bytes,22,opt,name=full_geo_name,json=fullGeoName,proto3" json:"full_geo_name,omitempty"`
	Privacy     AttachmentDownloadPrivacy `protobuf:"varint,23,opt,name=privacy,proto3,enum=ugc.content.AttachmentDownloadPrivacy" json:"privacy,omitempty"`
	PostOrigin  PostOrigin                `protobuf:"varint,24,opt,name=post_origin,json=postOrigin,proto3,enum=ugc.content.PostOrigin" json:"post_origin,omitempty"`
	// TT5.4.2
	PrivacyPolicy PostPrivacyPolicy `protobuf:"varint,25,opt,name=privacy_policy,json=privacyPolicy,proto3,enum=ugc.content.PostPrivacyPolicy" json:"privacy_policy,omitempty"`
	DiyTopicIds   []string          `protobuf:"bytes,26,rep,name=diy_topic_ids,json=diyTopicIds,proto3" json:"diy_topic_ids,omitempty"`
	// from rcmd_post.proto
	IsInHighInteraction                  bool              `protobuf:"varint,27,opt,name=is_in_high_interaction,json=isInHighInteraction,proto3" json:"is_in_high_interaction,omitempty"`
	StdScore                             float32           `protobuf:"fixed32,28,opt,name=std_score,json=stdScore,proto3" json:"std_score,omitempty"`
	SystemRecommendStreamRankingPosition float32           `protobuf:"fixed32,29,opt,name=system_recommend_stream_ranking_position,json=systemRecommendStreamRankingPosition,proto3" json:"system_recommend_stream_ranking_position,omitempty"`
	MachineStatus                        PostMachineStatus `protobuf:"varint,30,opt,name=machine_status,json=machineStatus,proto3,enum=ugc.content.PostMachineStatus" json:"machine_status,omitempty"`
	MoodInfo                             *MoodInfo         `protobuf:"bytes,31,opt,name=mood_info,json=moodInfo,proto3" json:"mood_info,omitempty"`
	SecondLabelId                        int32             `protobuf:"varint,32,opt,name=second_label_id,json=secondLabelId,proto3" json:"second_label_id,omitempty"`
	LabelLevel                           string            `protobuf:"bytes,33,opt,name=label_level,json=labelLevel,proto3" json:"label_level,omitempty"`
	IsObs                                bool              `protobuf:"varint,34,opt,name=is_obs,json=isObs,proto3" json:"is_obs,omitempty"`
	IsAnchorFeed                         bool              `protobuf:"varint,35,opt,name=is_anchor_feed,json=isAnchorFeed,proto3" json:"is_anchor_feed,omitempty"`
	ManualTopicIds                       []string          `protobuf:"bytes,36,rep,name=manual_topic_ids,json=manualTopicIds,proto3" json:"manual_topic_ids,omitempty"`
	// 帖子是否带有投票信息
	IsVote bool `protobuf:"varint,37,opt,name=is_vote,json=isVote,proto3" json:"is_vote,omitempty"`
	// 发帖人IP归属地
	IpLoc                *Location          `protobuf:"bytes,38,opt,name=ip_loc,json=ipLoc,proto3" json:"ip_loc,omitempty"`
	GeneralContents      []*GeneralContent  `protobuf:"bytes,39,rep,name=general_contents,json=generalContents,proto3" json:"general_contents,omitempty"`
	Title                string             `protobuf:"bytes,40,opt,name=title,proto3" json:"title,omitempty"`
	BusinessType         uint32             `protobuf:"varint,41,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	CommonTopicInfos     []*CommonTopicInfo `protobuf:"bytes,42,rep,name=common_topic_infos,json=commonTopicInfos,proto3" json:"common_topic_infos,omitempty"`
	IsConverted          bool               `protobuf:"varint,43,opt,name=IsConverted,proto3" json:"IsConverted,omitempty"`
	ZonePolicy           ZonePolicy         `protobuf:"varint,44,opt,name=zone_policy,json=zonePolicy,proto3,enum=ugc.content.ZonePolicy" json:"zone_policy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *PostInfo) Reset()         { *m = PostInfo{} }
func (m *PostInfo) String() string { return proto.CompactTextString(m) }
func (*PostInfo) ProtoMessage()    {}
func (*PostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{1}
}
func (m *PostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostInfo.Unmarshal(m, b)
}
func (m *PostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostInfo.Marshal(b, m, deterministic)
}
func (dst *PostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostInfo.Merge(dst, src)
}
func (m *PostInfo) XXX_Size() int {
	return xxx_messageInfo_PostInfo.Size(m)
}
func (m *PostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PostInfo proto.InternalMessageInfo

func (m *PostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *PostInfo) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *PostInfo) GetPostType() PostInfo_PostType {
	if m != nil {
		return m.PostType
	}
	return PostInfo_NONE
}

func (m *PostInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *PostInfo) GetAttachments() []*AttachmentInfo {
	if m != nil {
		return m.Attachments
	}
	return nil
}

func (m *PostInfo) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *PostInfo) GetCommentCount() uint32 {
	if m != nil {
		return m.CommentCount
	}
	return 0
}

func (m *PostInfo) GetAttitudeCount() uint32 {
	if m != nil {
		return m.AttitudeCount
	}
	return 0
}

func (m *PostInfo) GetViewCount() uint32 {
	if m != nil {
		return m.ViewCount
	}
	return 0
}

func (m *PostInfo) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *PostInfo) GetStatus() ContentStatus {
	if m != nil {
		return m.Status
	}
	return ContentStatus_CONTENT_STATUS_NONE
}

func (m *PostInfo) GetTopLevelCommentCount() uint32 {
	if m != nil {
		return m.TopLevelCommentCount
	}
	return 0
}

func (m *PostInfo) GetShareCount() uint32 {
	if m != nil {
		return m.ShareCount
	}
	return 0
}

func (m *PostInfo) GetLabel() string {
	if m != nil {
		return m.Label
	}
	return ""
}

func (m *PostInfo) GetMagnifiedViewCount() uint32 {
	if m != nil {
		return m.MagnifiedViewCount
	}
	return 0
}

func (m *PostInfo) GetSubTopicId() string {
	if m != nil {
		return m.SubTopicId
	}
	return ""
}

func (m *PostInfo) GetTags() []uint32 {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *PostInfo) GetContentType() int32 {
	if m != nil {
		return m.ContentType
	}
	return 0
}

func (m *PostInfo) GetStickyStatus() StickyStatus {
	if m != nil {
		return m.StickyStatus
	}
	return StickyStatus_STICKY_NONE
}

func (m *PostInfo) GetGeoTopicId() string {
	if m != nil {
		return m.GeoTopicId
	}
	return ""
}

func (m *PostInfo) GetLngLat() string {
	if m != nil {
		return m.LngLat
	}
	return ""
}

func (m *PostInfo) GetFullGeoName() string {
	if m != nil {
		return m.FullGeoName
	}
	return ""
}

func (m *PostInfo) GetPrivacy() AttachmentDownloadPrivacy {
	if m != nil {
		return m.Privacy
	}
	return AttachmentDownloadPrivacy_PRIVACY_DEFAULT
}

func (m *PostInfo) GetPostOrigin() PostOrigin {
	if m != nil {
		return m.PostOrigin
	}
	return PostOrigin_POST_ORIG_NORMAL
}

func (m *PostInfo) GetPrivacyPolicy() PostPrivacyPolicy {
	if m != nil {
		return m.PrivacyPolicy
	}
	return PostPrivacyPolicy_POST_PRIVACY_POLICY_DEFAULT
}

func (m *PostInfo) GetDiyTopicIds() []string {
	if m != nil {
		return m.DiyTopicIds
	}
	return nil
}

func (m *PostInfo) GetIsInHighInteraction() bool {
	if m != nil {
		return m.IsInHighInteraction
	}
	return false
}

func (m *PostInfo) GetStdScore() float32 {
	if m != nil {
		return m.StdScore
	}
	return 0
}

func (m *PostInfo) GetSystemRecommendStreamRankingPosition() float32 {
	if m != nil {
		return m.SystemRecommendStreamRankingPosition
	}
	return 0
}

func (m *PostInfo) GetMachineStatus() PostMachineStatus {
	if m != nil {
		return m.MachineStatus
	}
	return PostMachineStatus_MachineDefault
}

func (m *PostInfo) GetMoodInfo() *MoodInfo {
	if m != nil {
		return m.MoodInfo
	}
	return nil
}

func (m *PostInfo) GetSecondLabelId() int32 {
	if m != nil {
		return m.SecondLabelId
	}
	return 0
}

func (m *PostInfo) GetLabelLevel() string {
	if m != nil {
		return m.LabelLevel
	}
	return ""
}

func (m *PostInfo) GetIsObs() bool {
	if m != nil {
		return m.IsObs
	}
	return false
}

func (m *PostInfo) GetIsAnchorFeed() bool {
	if m != nil {
		return m.IsAnchorFeed
	}
	return false
}

func (m *PostInfo) GetManualTopicIds() []string {
	if m != nil {
		return m.ManualTopicIds
	}
	return nil
}

func (m *PostInfo) GetIsVote() bool {
	if m != nil {
		return m.IsVote
	}
	return false
}

func (m *PostInfo) GetIpLoc() *Location {
	if m != nil {
		return m.IpLoc
	}
	return nil
}

func (m *PostInfo) GetGeneralContents() []*GeneralContent {
	if m != nil {
		return m.GeneralContents
	}
	return nil
}

func (m *PostInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *PostInfo) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

func (m *PostInfo) GetCommonTopicInfos() []*CommonTopicInfo {
	if m != nil {
		return m.CommonTopicInfos
	}
	return nil
}

func (m *PostInfo) GetIsConverted() bool {
	if m != nil {
		return m.IsConverted
	}
	return false
}

func (m *PostInfo) GetZonePolicy() ZonePolicy {
	if m != nil {
		return m.ZonePolicy
	}
	return ZonePolicy_DEFAULT
}

type CommentInfo struct {
	CommentId            string            `protobuf:"bytes,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	PostId               string            `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	ConversationId       string            `protobuf:"bytes,3,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	Content              string            `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	UserId               uint32            `protobuf:"varint,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ReplyToUserId        uint32            `protobuf:"varint,6,opt,name=reply_to_user_id,json=replyToUserId,proto3" json:"reply_to_user_id,omitempty"`
	Attachments          []*AttachmentInfo `protobuf:"bytes,7,rep,name=attachments,proto3" json:"attachments,omitempty"`
	CommentCount         uint32            `protobuf:"varint,8,opt,name=comment_count,json=commentCount,proto3" json:"comment_count,omitempty"`
	AttitudeCount        uint32            `protobuf:"varint,9,opt,name=attitude_count,json=attitudeCount,proto3" json:"attitude_count,omitempty"`
	Status               ContentStatus     `protobuf:"varint,10,opt,name=status,proto3,enum=ugc.content.ContentStatus" json:"status,omitempty"`
	SubComments          []*CommentInfo    `protobuf:"bytes,11,rep,name=sub_comments,json=subComments,proto3" json:"sub_comments,omitempty"`
	CreateAt             uint64            `protobuf:"varint,12,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	ReplyToCommentId     string            `protobuf:"bytes,13,opt,name=reply_to_comment_id,json=replyToCommentId,proto3" json:"reply_to_comment_id,omitempty"`
	ContentType          int32             `protobuf:"varint,14,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	StickyStatus         StickyStatus      `protobuf:"varint,15,opt,name=sticky_status,json=stickyStatus,proto3,enum=ugc.content.StickyStatus" json:"sticky_status,omitempty"`
	IsObs                bool              `protobuf:"varint,16,opt,name=is_obs,json=isObs,proto3" json:"is_obs,omitempty"`
	StepCount            uint32            `protobuf:"varint,17,opt,name=step_count,json=stepCount,proto3" json:"step_count,omitempty"`
	IpLoc                *Location         `protobuf:"bytes,18,opt,name=ip_loc,json=ipLoc,proto3" json:"ip_loc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CommentInfo) Reset()         { *m = CommentInfo{} }
func (m *CommentInfo) String() string { return proto.CompactTextString(m) }
func (*CommentInfo) ProtoMessage()    {}
func (*CommentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{2}
}
func (m *CommentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentInfo.Unmarshal(m, b)
}
func (m *CommentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentInfo.Marshal(b, m, deterministic)
}
func (dst *CommentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentInfo.Merge(dst, src)
}
func (m *CommentInfo) XXX_Size() int {
	return xxx_messageInfo_CommentInfo.Size(m)
}
func (m *CommentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CommentInfo proto.InternalMessageInfo

func (m *CommentInfo) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *CommentInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *CommentInfo) GetConversationId() string {
	if m != nil {
		return m.ConversationId
	}
	return ""
}

func (m *CommentInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *CommentInfo) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *CommentInfo) GetReplyToUserId() uint32 {
	if m != nil {
		return m.ReplyToUserId
	}
	return 0
}

func (m *CommentInfo) GetAttachments() []*AttachmentInfo {
	if m != nil {
		return m.Attachments
	}
	return nil
}

func (m *CommentInfo) GetCommentCount() uint32 {
	if m != nil {
		return m.CommentCount
	}
	return 0
}

func (m *CommentInfo) GetAttitudeCount() uint32 {
	if m != nil {
		return m.AttitudeCount
	}
	return 0
}

func (m *CommentInfo) GetStatus() ContentStatus {
	if m != nil {
		return m.Status
	}
	return ContentStatus_CONTENT_STATUS_NONE
}

func (m *CommentInfo) GetSubComments() []*CommentInfo {
	if m != nil {
		return m.SubComments
	}
	return nil
}

func (m *CommentInfo) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *CommentInfo) GetReplyToCommentId() string {
	if m != nil {
		return m.ReplyToCommentId
	}
	return ""
}

func (m *CommentInfo) GetContentType() int32 {
	if m != nil {
		return m.ContentType
	}
	return 0
}

func (m *CommentInfo) GetStickyStatus() StickyStatus {
	if m != nil {
		return m.StickyStatus
	}
	return StickyStatus_STICKY_NONE
}

func (m *CommentInfo) GetIsObs() bool {
	if m != nil {
		return m.IsObs
	}
	return false
}

func (m *CommentInfo) GetStepCount() uint32 {
	if m != nil {
		return m.StepCount
	}
	return 0
}

func (m *CommentInfo) GetIpLoc() *Location {
	if m != nil {
		return m.IpLoc
	}
	return nil
}

// 地理位置信息
type Location struct {
	CountryCode          string   `protobuf:"bytes,1,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	Country              string   `protobuf:"bytes,2,opt,name=country,proto3" json:"country,omitempty"`
	ProvinceCode         string   `protobuf:"bytes,3,opt,name=province_code,json=provinceCode,proto3" json:"province_code,omitempty"`
	Province             string   `protobuf:"bytes,4,opt,name=province,proto3" json:"province,omitempty"`
	CityCode             string   `protobuf:"bytes,5,opt,name=city_code,json=cityCode,proto3" json:"city_code,omitempty"`
	City                 string   `protobuf:"bytes,6,opt,name=city,proto3" json:"city,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Location) Reset()         { *m = Location{} }
func (m *Location) String() string { return proto.CompactTextString(m) }
func (*Location) ProtoMessage()    {}
func (*Location) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{3}
}
func (m *Location) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Location.Unmarshal(m, b)
}
func (m *Location) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Location.Marshal(b, m, deterministic)
}
func (dst *Location) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Location.Merge(dst, src)
}
func (m *Location) XXX_Size() int {
	return xxx_messageInfo_Location.Size(m)
}
func (m *Location) XXX_DiscardUnknown() {
	xxx_messageInfo_Location.DiscardUnknown(m)
}

var xxx_messageInfo_Location proto.InternalMessageInfo

func (m *Location) GetCountryCode() string {
	if m != nil {
		return m.CountryCode
	}
	return ""
}

func (m *Location) GetCountry() string {
	if m != nil {
		return m.Country
	}
	return ""
}

func (m *Location) GetProvinceCode() string {
	if m != nil {
		return m.ProvinceCode
	}
	return ""
}

func (m *Location) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *Location) GetCityCode() string {
	if m != nil {
		return m.CityCode
	}
	return ""
}

func (m *Location) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

type BatchGetPostListByIdReq struct {
	PostIdList           []string    `protobuf:"bytes,1,rep,name=post_id_list,json=postIdList,proto3" json:"post_id_list,omitempty"`
	ContentType          ContentType `protobuf:"varint,2,opt,name=content_type,json=contentType,proto3,enum=ugc.content.ContentType" json:"content_type,omitempty"`
	NeedExactInfo        bool        `protobuf:"varint,3,opt,name=need_exact_info,json=needExactInfo,proto3" json:"need_exact_info,omitempty"`
	MarketId             uint32      `protobuf:"varint,4,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientType           uint32      `protobuf:"varint,5,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *BatchGetPostListByIdReq) Reset()         { *m = BatchGetPostListByIdReq{} }
func (m *BatchGetPostListByIdReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetPostListByIdReq) ProtoMessage()    {}
func (*BatchGetPostListByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{4}
}
func (m *BatchGetPostListByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetPostListByIdReq.Unmarshal(m, b)
}
func (m *BatchGetPostListByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetPostListByIdReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetPostListByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetPostListByIdReq.Merge(dst, src)
}
func (m *BatchGetPostListByIdReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetPostListByIdReq.Size(m)
}
func (m *BatchGetPostListByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetPostListByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetPostListByIdReq proto.InternalMessageInfo

func (m *BatchGetPostListByIdReq) GetPostIdList() []string {
	if m != nil {
		return m.PostIdList
	}
	return nil
}

func (m *BatchGetPostListByIdReq) GetContentType() ContentType {
	if m != nil {
		return m.ContentType
	}
	return ContentType_FORMATTED
}

func (m *BatchGetPostListByIdReq) GetNeedExactInfo() bool {
	if m != nil {
		return m.NeedExactInfo
	}
	return false
}

func (m *BatchGetPostListByIdReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *BatchGetPostListByIdReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type BatchGetPostListByIdResp struct {
	PostList             []*PostInfo `protobuf:"bytes,1,rep,name=post_list,json=postList,proto3" json:"post_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *BatchGetPostListByIdResp) Reset()         { *m = BatchGetPostListByIdResp{} }
func (m *BatchGetPostListByIdResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetPostListByIdResp) ProtoMessage()    {}
func (*BatchGetPostListByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{5}
}
func (m *BatchGetPostListByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetPostListByIdResp.Unmarshal(m, b)
}
func (m *BatchGetPostListByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetPostListByIdResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetPostListByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetPostListByIdResp.Merge(dst, src)
}
func (m *BatchGetPostListByIdResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetPostListByIdResp.Size(m)
}
func (m *BatchGetPostListByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetPostListByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetPostListByIdResp proto.InternalMessageInfo

func (m *BatchGetPostListByIdResp) GetPostList() []*PostInfo {
	if m != nil {
		return m.PostList
	}
	return nil
}

type AddPostDirectlyReq struct {
	UserId               uint32             `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	TopicId              string             `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Type                 PostInfo_PostType  `protobuf:"varint,3,opt,name=type,proto3,enum=ugc.content.PostInfo_PostType" json:"type,omitempty"`
	Content              string             `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	Attachments          []*AttachmentInfo  `protobuf:"bytes,5,rep,name=attachments,proto3" json:"attachments,omitempty"`
	CreateAt             uint64             `protobuf:"varint,6,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	AvailabilityMask     uint32             `protobuf:"varint,7,opt,name=availability_mask,json=availabilityMask,proto3" json:"availability_mask,omitempty"`
	SubTopicId           string             `protobuf:"bytes,8,opt,name=sub_topic_id,json=subTopicId,proto3" json:"sub_topic_id,omitempty"`
	ContentType          int32              `protobuf:"varint,9,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	PostOrigin           PostOrigin         `protobuf:"varint,10,opt,name=post_origin,json=postOrigin,proto3,enum=ugc.content.PostOrigin" json:"post_origin,omitempty"`
	Ttid                 string             `protobuf:"bytes,11,opt,name=ttid,proto3" json:"ttid,omitempty"`
	VmText               string             `protobuf:"bytes,12,opt,name=vm_text,json=vmText,proto3" json:"vm_text,omitempty"`
	BusinessType         uint32             `protobuf:"varint,13,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	GeneralContents      []*GeneralContent  `protobuf:"bytes,14,rep,name=general_contents,json=generalContents,proto3" json:"general_contents,omitempty"`
	CommonTopicInfos     []*CommonTopicInfo `protobuf:"bytes,15,rep,name=common_topic_infos,json=commonTopicInfos,proto3" json:"common_topic_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *AddPostDirectlyReq) Reset()         { *m = AddPostDirectlyReq{} }
func (m *AddPostDirectlyReq) String() string { return proto.CompactTextString(m) }
func (*AddPostDirectlyReq) ProtoMessage()    {}
func (*AddPostDirectlyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{6}
}
func (m *AddPostDirectlyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPostDirectlyReq.Unmarshal(m, b)
}
func (m *AddPostDirectlyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPostDirectlyReq.Marshal(b, m, deterministic)
}
func (dst *AddPostDirectlyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPostDirectlyReq.Merge(dst, src)
}
func (m *AddPostDirectlyReq) XXX_Size() int {
	return xxx_messageInfo_AddPostDirectlyReq.Size(m)
}
func (m *AddPostDirectlyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPostDirectlyReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPostDirectlyReq proto.InternalMessageInfo

func (m *AddPostDirectlyReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *AddPostDirectlyReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *AddPostDirectlyReq) GetType() PostInfo_PostType {
	if m != nil {
		return m.Type
	}
	return PostInfo_NONE
}

func (m *AddPostDirectlyReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *AddPostDirectlyReq) GetAttachments() []*AttachmentInfo {
	if m != nil {
		return m.Attachments
	}
	return nil
}

func (m *AddPostDirectlyReq) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *AddPostDirectlyReq) GetAvailabilityMask() uint32 {
	if m != nil {
		return m.AvailabilityMask
	}
	return 0
}

func (m *AddPostDirectlyReq) GetSubTopicId() string {
	if m != nil {
		return m.SubTopicId
	}
	return ""
}

func (m *AddPostDirectlyReq) GetContentType() int32 {
	if m != nil {
		return m.ContentType
	}
	return 0
}

func (m *AddPostDirectlyReq) GetPostOrigin() PostOrigin {
	if m != nil {
		return m.PostOrigin
	}
	return PostOrigin_POST_ORIG_NORMAL
}

func (m *AddPostDirectlyReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *AddPostDirectlyReq) GetVmText() string {
	if m != nil {
		return m.VmText
	}
	return ""
}

func (m *AddPostDirectlyReq) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

func (m *AddPostDirectlyReq) GetGeneralContents() []*GeneralContent {
	if m != nil {
		return m.GeneralContents
	}
	return nil
}

func (m *AddPostDirectlyReq) GetCommonTopicInfos() []*CommonTopicInfo {
	if m != nil {
		return m.CommonTopicInfos
	}
	return nil
}

type AddPostDirectlyResp struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPostDirectlyResp) Reset()         { *m = AddPostDirectlyResp{} }
func (m *AddPostDirectlyResp) String() string { return proto.CompactTextString(m) }
func (*AddPostDirectlyResp) ProtoMessage()    {}
func (*AddPostDirectlyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{7}
}
func (m *AddPostDirectlyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPostDirectlyResp.Unmarshal(m, b)
}
func (m *AddPostDirectlyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPostDirectlyResp.Marshal(b, m, deterministic)
}
func (dst *AddPostDirectlyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPostDirectlyResp.Merge(dst, src)
}
func (m *AddPostDirectlyResp) XXX_Size() int {
	return xxx_messageInfo_AddPostDirectlyResp.Size(m)
}
func (m *AddPostDirectlyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPostDirectlyResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPostDirectlyResp proto.InternalMessageInfo

func (m *AddPostDirectlyResp) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type GeneralContent struct {
	UnmarshalType        uint32   `protobuf:"varint,1,opt,name=unmarshal_type,json=unmarshalType,proto3" json:"unmarshal_type,omitempty"`
	ContentOrigin        uint32   `protobuf:"varint,2,opt,name=content_origin,json=contentOrigin,proto3" json:"content_origin,omitempty"`
	Content              []byte   `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	OriginName           string   `protobuf:"bytes,4,opt,name=origin_name,json=originName,proto3" json:"origin_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GeneralContent) Reset()         { *m = GeneralContent{} }
func (m *GeneralContent) String() string { return proto.CompactTextString(m) }
func (*GeneralContent) ProtoMessage()    {}
func (*GeneralContent) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{8}
}
func (m *GeneralContent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GeneralContent.Unmarshal(m, b)
}
func (m *GeneralContent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GeneralContent.Marshal(b, m, deterministic)
}
func (dst *GeneralContent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GeneralContent.Merge(dst, src)
}
func (m *GeneralContent) XXX_Size() int {
	return xxx_messageInfo_GeneralContent.Size(m)
}
func (m *GeneralContent) XXX_DiscardUnknown() {
	xxx_messageInfo_GeneralContent.DiscardUnknown(m)
}

var xxx_messageInfo_GeneralContent proto.InternalMessageInfo

func (m *GeneralContent) GetUnmarshalType() uint32 {
	if m != nil {
		return m.UnmarshalType
	}
	return 0
}

func (m *GeneralContent) GetContentOrigin() uint32 {
	if m != nil {
		return m.ContentOrigin
	}
	return 0
}

func (m *GeneralContent) GetContent() []byte {
	if m != nil {
		return m.Content
	}
	return nil
}

func (m *GeneralContent) GetOriginName() string {
	if m != nil {
		return m.OriginName
	}
	return ""
}

// 写帖子通用内容
type AddPostDirectlyByBussReq struct {
	UserId               uint32             `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Type                 PostInfo_PostType  `protobuf:"varint,2,opt,name=type,proto3,enum=ugc.content.PostInfo_PostType" json:"type,omitempty"`
	CreateAt             uint64             `protobuf:"varint,3,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	DiyTopicIds          []string           `protobuf:"bytes,4,rep,name=diy_topic_ids,json=diyTopicIds,proto3" json:"diy_topic_ids,omitempty"`
	MoodInfo             *MoodInfo          `protobuf:"bytes,5,opt,name=mood_info,json=moodInfo,proto3" json:"mood_info,omitempty"`
	GeneralContents      []*GeneralContent  `protobuf:"bytes,6,rep,name=general_contents,json=generalContents,proto3" json:"general_contents,omitempty"`
	BusinessType         uint32             `protobuf:"varint,7,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	CommonTopicInfos     []*CommonTopicInfo `protobuf:"bytes,8,rep,name=common_topic_infos,json=commonTopicInfos,proto3" json:"common_topic_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *AddPostDirectlyByBussReq) Reset()         { *m = AddPostDirectlyByBussReq{} }
func (m *AddPostDirectlyByBussReq) String() string { return proto.CompactTextString(m) }
func (*AddPostDirectlyByBussReq) ProtoMessage()    {}
func (*AddPostDirectlyByBussReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{9}
}
func (m *AddPostDirectlyByBussReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPostDirectlyByBussReq.Unmarshal(m, b)
}
func (m *AddPostDirectlyByBussReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPostDirectlyByBussReq.Marshal(b, m, deterministic)
}
func (dst *AddPostDirectlyByBussReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPostDirectlyByBussReq.Merge(dst, src)
}
func (m *AddPostDirectlyByBussReq) XXX_Size() int {
	return xxx_messageInfo_AddPostDirectlyByBussReq.Size(m)
}
func (m *AddPostDirectlyByBussReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPostDirectlyByBussReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPostDirectlyByBussReq proto.InternalMessageInfo

func (m *AddPostDirectlyByBussReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *AddPostDirectlyByBussReq) GetType() PostInfo_PostType {
	if m != nil {
		return m.Type
	}
	return PostInfo_NONE
}

func (m *AddPostDirectlyByBussReq) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *AddPostDirectlyByBussReq) GetDiyTopicIds() []string {
	if m != nil {
		return m.DiyTopicIds
	}
	return nil
}

func (m *AddPostDirectlyByBussReq) GetMoodInfo() *MoodInfo {
	if m != nil {
		return m.MoodInfo
	}
	return nil
}

func (m *AddPostDirectlyByBussReq) GetGeneralContents() []*GeneralContent {
	if m != nil {
		return m.GeneralContents
	}
	return nil
}

func (m *AddPostDirectlyByBussReq) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

func (m *AddPostDirectlyByBussReq) GetCommonTopicInfos() []*CommonTopicInfo {
	if m != nil {
		return m.CommonTopicInfos
	}
	return nil
}

// 返回帖子通用内容
type AddPostDirectlyByBussResp struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPostDirectlyByBussResp) Reset()         { *m = AddPostDirectlyByBussResp{} }
func (m *AddPostDirectlyByBussResp) String() string { return proto.CompactTextString(m) }
func (*AddPostDirectlyByBussResp) ProtoMessage()    {}
func (*AddPostDirectlyByBussResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{10}
}
func (m *AddPostDirectlyByBussResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPostDirectlyByBussResp.Unmarshal(m, b)
}
func (m *AddPostDirectlyByBussResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPostDirectlyByBussResp.Marshal(b, m, deterministic)
}
func (dst *AddPostDirectlyByBussResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPostDirectlyByBussResp.Merge(dst, src)
}
func (m *AddPostDirectlyByBussResp) XXX_Size() int {
	return xxx_messageInfo_AddPostDirectlyByBussResp.Size(m)
}
func (m *AddPostDirectlyByBussResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPostDirectlyByBussResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPostDirectlyByBussResp proto.InternalMessageInfo

func (m *AddPostDirectlyByBussResp) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

// 更新帖子通用内容
type UpdatePostGeneralContentsReq struct {
	PostId               string            `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Uid                  uint32            `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	GeneralContents      []*GeneralContent `protobuf:"bytes,3,rep,name=general_contents,json=generalContents,proto3" json:"general_contents,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UpdatePostGeneralContentsReq) Reset()         { *m = UpdatePostGeneralContentsReq{} }
func (m *UpdatePostGeneralContentsReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePostGeneralContentsReq) ProtoMessage()    {}
func (*UpdatePostGeneralContentsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{11}
}
func (m *UpdatePostGeneralContentsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePostGeneralContentsReq.Unmarshal(m, b)
}
func (m *UpdatePostGeneralContentsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePostGeneralContentsReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePostGeneralContentsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePostGeneralContentsReq.Merge(dst, src)
}
func (m *UpdatePostGeneralContentsReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePostGeneralContentsReq.Size(m)
}
func (m *UpdatePostGeneralContentsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePostGeneralContentsReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePostGeneralContentsReq proto.InternalMessageInfo

func (m *UpdatePostGeneralContentsReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UpdatePostGeneralContentsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdatePostGeneralContentsReq) GetGeneralContents() []*GeneralContent {
	if m != nil {
		return m.GeneralContents
	}
	return nil
}

// 更新帖子通用内容
type UpdatePostGeneralContentsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePostGeneralContentsResp) Reset()         { *m = UpdatePostGeneralContentsResp{} }
func (m *UpdatePostGeneralContentsResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePostGeneralContentsResp) ProtoMessage()    {}
func (*UpdatePostGeneralContentsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{12}
}
func (m *UpdatePostGeneralContentsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePostGeneralContentsResp.Unmarshal(m, b)
}
func (m *UpdatePostGeneralContentsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePostGeneralContentsResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePostGeneralContentsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePostGeneralContentsResp.Merge(dst, src)
}
func (m *UpdatePostGeneralContentsResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePostGeneralContentsResp.Size(m)
}
func (m *UpdatePostGeneralContentsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePostGeneralContentsResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePostGeneralContentsResp proto.InternalMessageInfo

// 通用话题 通过话题类型来识别业务，不要用心情，城市那套
type CommonTopicInfo struct {
	TopicIds             []string `protobuf:"bytes,1,rep,name=topic_ids,json=topicIds,proto3" json:"topic_ids,omitempty"`
	TopicType            uint32   `protobuf:"varint,2,opt,name=topic_type,json=topicType,proto3" json:"topic_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommonTopicInfo) Reset()         { *m = CommonTopicInfo{} }
func (m *CommonTopicInfo) String() string { return proto.CompactTextString(m) }
func (*CommonTopicInfo) ProtoMessage()    {}
func (*CommonTopicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{13}
}
func (m *CommonTopicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonTopicInfo.Unmarshal(m, b)
}
func (m *CommonTopicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonTopicInfo.Marshal(b, m, deterministic)
}
func (dst *CommonTopicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonTopicInfo.Merge(dst, src)
}
func (m *CommonTopicInfo) XXX_Size() int {
	return xxx_messageInfo_CommonTopicInfo.Size(m)
}
func (m *CommonTopicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonTopicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CommonTopicInfo proto.InternalMessageInfo

func (m *CommonTopicInfo) GetTopicIds() []string {
	if m != nil {
		return m.TopicIds
	}
	return nil
}

func (m *CommonTopicInfo) GetTopicType() uint32 {
	if m != nil {
		return m.TopicType
	}
	return 0
}

type AddPostReq struct {
	UserId                uint32                    `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	TopicId               string                    `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Type                  PostInfo_PostType         `protobuf:"varint,3,opt,name=type,proto3,enum=ugc.content.PostInfo_PostType" json:"type,omitempty"`
	Content               string                    `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	AttachmentImageCount  uint32                    `protobuf:"varint,5,opt,name=attachment_image_count,json=attachmentImageCount,proto3" json:"attachment_image_count,omitempty"`
	AttachmentVideoCount  uint32                    `protobuf:"varint,6,opt,name=attachment_video_count,json=attachmentVideoCount,proto3" json:"attachment_video_count,omitempty"`
	Status                ContentStatus             `protobuf:"varint,7,opt,name=status,proto3,enum=ugc.content.ContentStatus" json:"status,omitempty"`
	AntispamLabelInfo     string                    `protobuf:"bytes,8,opt,name=antispam_label_info,json=antispamLabelInfo,proto3" json:"antispam_label_info,omitempty"`
	SubTopicId            string                    `protobuf:"bytes,9,opt,name=sub_topic_id,json=subTopicId,proto3" json:"sub_topic_id,omitempty"`
	ContentType           int32                     `protobuf:"varint,10,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	PostOrigin            PostOrigin                `protobuf:"varint,11,opt,name=post_origin,json=postOrigin,proto3,enum=ugc.content.PostOrigin" json:"post_origin,omitempty"`
	AttachmentAudioCount  uint32                    `protobuf:"varint,12,opt,name=attachment_audio_count,json=attachmentAudioCount,proto3" json:"attachment_audio_count,omitempty"`
	PredefinedAttachments []*AttachmentInfo         `protobuf:"bytes,13,rep,name=predefined_attachments,json=predefinedAttachments,proto3" json:"predefined_attachments,omitempty"`
	Ttid                  string                    `protobuf:"bytes,14,opt,name=ttid,proto3" json:"ttid,omitempty"`
	GeoTopicId            string                    `protobuf:"bytes,15,opt,name=geo_topic_id,json=geoTopicId,proto3" json:"geo_topic_id,omitempty"`
	LngLat                string                    `protobuf:"bytes,16,opt,name=lng_lat,json=lngLat,proto3" json:"lng_lat,omitempty"`
	FullGeoName           string                    `protobuf:"bytes,17,opt,name=full_geo_name,json=fullGeoName,proto3" json:"full_geo_name,omitempty"`
	Privacy               AttachmentDownloadPrivacy `protobuf:"varint,18,opt,name=privacy,proto3,enum=ugc.content.AttachmentDownloadPrivacy" json:"privacy,omitempty"`
	// 给数据中心用的
	DeviceId string   `protobuf:"bytes,20,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Platform uint32   `protobuf:"varint,21,opt,name=platform,proto3" json:"platform,omitempty"`
	Tags     []uint32 `protobuf:"varint,22,rep,packed,name=tags,proto3" json:"tags,omitempty"`
	// extra
	Extra *Extra `protobuf:"bytes,30,opt,name=extra,proto3" json:"extra,omitempty"`
	// 水印
	VmText string `protobuf:"bytes,31,opt,name=vm_text,json=vmText,proto3" json:"vm_text,omitempty"`
	// 隐私策略
	PrivacyPolicy PostPrivacyPolicy `protobuf:"varint,32,opt,name=privacy_policy,json=privacyPolicy,proto3,enum=ugc.content.PostPrivacyPolicy" json:"privacy_policy,omitempty"`
	DiyTopicIds   []string          `protobuf:"bytes,33,rep,name=diy_topic_ids,json=diyTopicIds,proto3" json:"diy_topic_ids,omitempty"`
	ClientIp      uint32            `protobuf:"varint,34,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	MoodInfo      *MoodInfo         `protobuf:"bytes,35,opt,name=mood_info,json=moodInfo,proto3" json:"mood_info,omitempty"`
	IsObs         bool              `protobuf:"varint,36,opt,name=is_obs,json=isObs,proto3" json:"is_obs,omitempty"`
	IsAnchorFeed  bool              `protobuf:"varint,37,opt,name=is_anchor_feed,json=isAnchorFeed,proto3" json:"is_anchor_feed,omitempty"`
	// 是否带有投票信息的帖子
	IsVote bool `protobuf:"varint,38,opt,name=is_vote,json=isVote,proto3" json:"is_vote,omitempty"`
	// 发帖人的IP归属地
	IpLoc                *Location          `protobuf:"bytes,39,opt,name=ip_loc,json=ipLoc,proto3" json:"ip_loc,omitempty"`
	MarketId             uint32             `protobuf:"varint,40,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	Title                string             `protobuf:"bytes,41,opt,name=title,proto3" json:"title,omitempty"`
	BusinessType         uint32             `protobuf:"varint,42,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	GeneralContents      []*GeneralContent  `protobuf:"bytes,43,rep,name=general_contents,json=generalContents,proto3" json:"general_contents,omitempty"`
	CommonTopicInfos     []*CommonTopicInfo `protobuf:"bytes,44,rep,name=common_topic_infos,json=commonTopicInfos,proto3" json:"common_topic_infos,omitempty"`
	ZonePolicy           ZonePolicy         `protobuf:"varint,45,opt,name=zone_policy,json=zonePolicy,proto3,enum=ugc.content.ZonePolicy" json:"zone_policy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *AddPostReq) Reset()         { *m = AddPostReq{} }
func (m *AddPostReq) String() string { return proto.CompactTextString(m) }
func (*AddPostReq) ProtoMessage()    {}
func (*AddPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{14}
}
func (m *AddPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPostReq.Unmarshal(m, b)
}
func (m *AddPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPostReq.Marshal(b, m, deterministic)
}
func (dst *AddPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPostReq.Merge(dst, src)
}
func (m *AddPostReq) XXX_Size() int {
	return xxx_messageInfo_AddPostReq.Size(m)
}
func (m *AddPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPostReq proto.InternalMessageInfo

func (m *AddPostReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *AddPostReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *AddPostReq) GetType() PostInfo_PostType {
	if m != nil {
		return m.Type
	}
	return PostInfo_NONE
}

func (m *AddPostReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *AddPostReq) GetAttachmentImageCount() uint32 {
	if m != nil {
		return m.AttachmentImageCount
	}
	return 0
}

func (m *AddPostReq) GetAttachmentVideoCount() uint32 {
	if m != nil {
		return m.AttachmentVideoCount
	}
	return 0
}

func (m *AddPostReq) GetStatus() ContentStatus {
	if m != nil {
		return m.Status
	}
	return ContentStatus_CONTENT_STATUS_NONE
}

func (m *AddPostReq) GetAntispamLabelInfo() string {
	if m != nil {
		return m.AntispamLabelInfo
	}
	return ""
}

func (m *AddPostReq) GetSubTopicId() string {
	if m != nil {
		return m.SubTopicId
	}
	return ""
}

func (m *AddPostReq) GetContentType() int32 {
	if m != nil {
		return m.ContentType
	}
	return 0
}

func (m *AddPostReq) GetPostOrigin() PostOrigin {
	if m != nil {
		return m.PostOrigin
	}
	return PostOrigin_POST_ORIG_NORMAL
}

func (m *AddPostReq) GetAttachmentAudioCount() uint32 {
	if m != nil {
		return m.AttachmentAudioCount
	}
	return 0
}

func (m *AddPostReq) GetPredefinedAttachments() []*AttachmentInfo {
	if m != nil {
		return m.PredefinedAttachments
	}
	return nil
}

func (m *AddPostReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *AddPostReq) GetGeoTopicId() string {
	if m != nil {
		return m.GeoTopicId
	}
	return ""
}

func (m *AddPostReq) GetLngLat() string {
	if m != nil {
		return m.LngLat
	}
	return ""
}

func (m *AddPostReq) GetFullGeoName() string {
	if m != nil {
		return m.FullGeoName
	}
	return ""
}

func (m *AddPostReq) GetPrivacy() AttachmentDownloadPrivacy {
	if m != nil {
		return m.Privacy
	}
	return AttachmentDownloadPrivacy_PRIVACY_DEFAULT
}

func (m *AddPostReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *AddPostReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *AddPostReq) GetTags() []uint32 {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *AddPostReq) GetExtra() *Extra {
	if m != nil {
		return m.Extra
	}
	return nil
}

func (m *AddPostReq) GetVmText() string {
	if m != nil {
		return m.VmText
	}
	return ""
}

func (m *AddPostReq) GetPrivacyPolicy() PostPrivacyPolicy {
	if m != nil {
		return m.PrivacyPolicy
	}
	return PostPrivacyPolicy_POST_PRIVACY_POLICY_DEFAULT
}

func (m *AddPostReq) GetDiyTopicIds() []string {
	if m != nil {
		return m.DiyTopicIds
	}
	return nil
}

func (m *AddPostReq) GetClientIp() uint32 {
	if m != nil {
		return m.ClientIp
	}
	return 0
}

func (m *AddPostReq) GetMoodInfo() *MoodInfo {
	if m != nil {
		return m.MoodInfo
	}
	return nil
}

func (m *AddPostReq) GetIsObs() bool {
	if m != nil {
		return m.IsObs
	}
	return false
}

func (m *AddPostReq) GetIsAnchorFeed() bool {
	if m != nil {
		return m.IsAnchorFeed
	}
	return false
}

func (m *AddPostReq) GetIsVote() bool {
	if m != nil {
		return m.IsVote
	}
	return false
}

func (m *AddPostReq) GetIpLoc() *Location {
	if m != nil {
		return m.IpLoc
	}
	return nil
}

func (m *AddPostReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *AddPostReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *AddPostReq) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

func (m *AddPostReq) GetGeneralContents() []*GeneralContent {
	if m != nil {
		return m.GeneralContents
	}
	return nil
}

func (m *AddPostReq) GetCommonTopicInfos() []*CommonTopicInfo {
	if m != nil {
		return m.CommonTopicInfos
	}
	return nil
}

func (m *AddPostReq) GetZonePolicy() ZonePolicy {
	if m != nil {
		return m.ZonePolicy
	}
	return ZonePolicy_DEFAULT
}

type MoodInfo struct {
	MoodId               string   `protobuf:"bytes,1,opt,name=mood_id,json=moodId,proto3" json:"mood_id,omitempty"`
	MoodBindTopicId      string   `protobuf:"bytes,2,opt,name=mood_bind_topic_id,json=moodBindTopicId,proto3" json:"mood_bind_topic_id,omitempty"`
	MoodName             string   `protobuf:"bytes,3,opt,name=mood_name,json=moodName,proto3" json:"mood_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MoodInfo) Reset()         { *m = MoodInfo{} }
func (m *MoodInfo) String() string { return proto.CompactTextString(m) }
func (*MoodInfo) ProtoMessage()    {}
func (*MoodInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{15}
}
func (m *MoodInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoodInfo.Unmarshal(m, b)
}
func (m *MoodInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoodInfo.Marshal(b, m, deterministic)
}
func (dst *MoodInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoodInfo.Merge(dst, src)
}
func (m *MoodInfo) XXX_Size() int {
	return xxx_messageInfo_MoodInfo.Size(m)
}
func (m *MoodInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MoodInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MoodInfo proto.InternalMessageInfo

func (m *MoodInfo) GetMoodId() string {
	if m != nil {
		return m.MoodId
	}
	return ""
}

func (m *MoodInfo) GetMoodBindTopicId() string {
	if m != nil {
		return m.MoodBindTopicId
	}
	return ""
}

func (m *MoodInfo) GetMoodName() string {
	if m != nil {
		return m.MoodName
	}
	return ""
}

type Extra struct {
	Width                uint32   `protobuf:"varint,1,opt,name=width,proto3" json:"width,omitempty"`
	Heigth               uint32   `protobuf:"varint,2,opt,name=heigth,proto3" json:"heigth,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Extra) Reset()         { *m = Extra{} }
func (m *Extra) String() string { return proto.CompactTextString(m) }
func (*Extra) ProtoMessage()    {}
func (*Extra) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{16}
}
func (m *Extra) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Extra.Unmarshal(m, b)
}
func (m *Extra) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Extra.Marshal(b, m, deterministic)
}
func (dst *Extra) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Extra.Merge(dst, src)
}
func (m *Extra) XXX_Size() int {
	return xxx_messageInfo_Extra.Size(m)
}
func (m *Extra) XXX_DiscardUnknown() {
	xxx_messageInfo_Extra.DiscardUnknown(m)
}

var xxx_messageInfo_Extra proto.InternalMessageInfo

func (m *Extra) GetWidth() uint32 {
	if m != nil {
		return m.Width
	}
	return 0
}

func (m *Extra) GetHeigth() uint32 {
	if m != nil {
		return m.Heigth
	}
	return 0
}

type AddPostResp struct {
	PostId               string        `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Status               ContentStatus `protobuf:"varint,2,opt,name=status,proto3,enum=ugc.content.ContentStatus" json:"status,omitempty"`
	PostCreateAt         uint64        `protobuf:"varint,3,opt,name=post_create_at,json=postCreateAt,proto3" json:"post_create_at,omitempty"`
	AttachmentImageKeys  []string      `protobuf:"bytes,10,rep,name=attachment_image_keys,json=attachmentImageKeys,proto3" json:"attachment_image_keys,omitempty"`
	AttachmentVideoKeys  []string      `protobuf:"bytes,11,rep,name=attachment_video_keys,json=attachmentVideoKeys,proto3" json:"attachment_video_keys,omitempty"`
	ImageUploadToken     string        `protobuf:"bytes,12,opt,name=image_upload_token,json=imageUploadToken,proto3" json:"image_upload_token,omitempty"`
	VideoUploadToken     string        `protobuf:"bytes,13,opt,name=video_upload_token,json=videoUploadToken,proto3" json:"video_upload_token,omitempty"`
	AttachmentAudioKeys  []string      `protobuf:"bytes,14,rep,name=attachment_audio_keys,json=attachmentAudioKeys,proto3" json:"attachment_audio_keys,omitempty"`
	AudioUploadToken     string        `protobuf:"bytes,15,opt,name=audio_upload_token,json=audioUploadToken,proto3" json:"audio_upload_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AddPostResp) Reset()         { *m = AddPostResp{} }
func (m *AddPostResp) String() string { return proto.CompactTextString(m) }
func (*AddPostResp) ProtoMessage()    {}
func (*AddPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{17}
}
func (m *AddPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPostResp.Unmarshal(m, b)
}
func (m *AddPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPostResp.Marshal(b, m, deterministic)
}
func (dst *AddPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPostResp.Merge(dst, src)
}
func (m *AddPostResp) XXX_Size() int {
	return xxx_messageInfo_AddPostResp.Size(m)
}
func (m *AddPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPostResp proto.InternalMessageInfo

func (m *AddPostResp) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *AddPostResp) GetStatus() ContentStatus {
	if m != nil {
		return m.Status
	}
	return ContentStatus_CONTENT_STATUS_NONE
}

func (m *AddPostResp) GetPostCreateAt() uint64 {
	if m != nil {
		return m.PostCreateAt
	}
	return 0
}

func (m *AddPostResp) GetAttachmentImageKeys() []string {
	if m != nil {
		return m.AttachmentImageKeys
	}
	return nil
}

func (m *AddPostResp) GetAttachmentVideoKeys() []string {
	if m != nil {
		return m.AttachmentVideoKeys
	}
	return nil
}

func (m *AddPostResp) GetImageUploadToken() string {
	if m != nil {
		return m.ImageUploadToken
	}
	return ""
}

func (m *AddPostResp) GetVideoUploadToken() string {
	if m != nil {
		return m.VideoUploadToken
	}
	return ""
}

func (m *AddPostResp) GetAttachmentAudioKeys() []string {
	if m != nil {
		return m.AttachmentAudioKeys
	}
	return nil
}

func (m *AddPostResp) GetAudioUploadToken() string {
	if m != nil {
		return m.AudioUploadToken
	}
	return ""
}

// 更新帖子/评论 的附件的状态
type MarkAttachmentUploadedReq struct {
	PostId               string            `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string            `protobuf:"bytes,2,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	AttachmentInfoList   []*AttachmentInfo `protobuf:"bytes,3,rep,name=attachment_info_list,json=attachmentInfoList,proto3" json:"attachment_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *MarkAttachmentUploadedReq) Reset()         { *m = MarkAttachmentUploadedReq{} }
func (m *MarkAttachmentUploadedReq) String() string { return proto.CompactTextString(m) }
func (*MarkAttachmentUploadedReq) ProtoMessage()    {}
func (*MarkAttachmentUploadedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{18}
}
func (m *MarkAttachmentUploadedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkAttachmentUploadedReq.Unmarshal(m, b)
}
func (m *MarkAttachmentUploadedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkAttachmentUploadedReq.Marshal(b, m, deterministic)
}
func (dst *MarkAttachmentUploadedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkAttachmentUploadedReq.Merge(dst, src)
}
func (m *MarkAttachmentUploadedReq) XXX_Size() int {
	return xxx_messageInfo_MarkAttachmentUploadedReq.Size(m)
}
func (m *MarkAttachmentUploadedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkAttachmentUploadedReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarkAttachmentUploadedReq proto.InternalMessageInfo

func (m *MarkAttachmentUploadedReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *MarkAttachmentUploadedReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *MarkAttachmentUploadedReq) GetAttachmentInfoList() []*AttachmentInfo {
	if m != nil {
		return m.AttachmentInfoList
	}
	return nil
}

type MarkAttachmentUploadedResp struct {
	PostCreateAt         uint64   `protobuf:"varint,1,opt,name=post_create_at,json=postCreateAt,proto3" json:"post_create_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkAttachmentUploadedResp) Reset()         { *m = MarkAttachmentUploadedResp{} }
func (m *MarkAttachmentUploadedResp) String() string { return proto.CompactTextString(m) }
func (*MarkAttachmentUploadedResp) ProtoMessage()    {}
func (*MarkAttachmentUploadedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{19}
}
func (m *MarkAttachmentUploadedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkAttachmentUploadedResp.Unmarshal(m, b)
}
func (m *MarkAttachmentUploadedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkAttachmentUploadedResp.Marshal(b, m, deterministic)
}
func (dst *MarkAttachmentUploadedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkAttachmentUploadedResp.Merge(dst, src)
}
func (m *MarkAttachmentUploadedResp) XXX_Size() int {
	return xxx_messageInfo_MarkAttachmentUploadedResp.Size(m)
}
func (m *MarkAttachmentUploadedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkAttachmentUploadedResp.DiscardUnknown(m)
}

var xxx_messageInfo_MarkAttachmentUploadedResp proto.InternalMessageInfo

func (m *MarkAttachmentUploadedResp) GetPostCreateAt() uint64 {
	if m != nil {
		return m.PostCreateAt
	}
	return 0
}

type BanPostByIdReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	IsBan                bool     `protobuf:"varint,2,opt,name=is_ban,json=isBan,proto3" json:"is_ban,omitempty"`
	SecondLabelId        int32    `protobuf:"varint,3,opt,name=second_label_id,json=secondLabelId,proto3" json:"second_label_id,omitempty"`
	SecondLabelLevel     string   `protobuf:"bytes,4,opt,name=second_label_level,json=secondLabelLevel,proto3" json:"second_label_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BanPostByIdReq) Reset()         { *m = BanPostByIdReq{} }
func (m *BanPostByIdReq) String() string { return proto.CompactTextString(m) }
func (*BanPostByIdReq) ProtoMessage()    {}
func (*BanPostByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{20}
}
func (m *BanPostByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BanPostByIdReq.Unmarshal(m, b)
}
func (m *BanPostByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BanPostByIdReq.Marshal(b, m, deterministic)
}
func (dst *BanPostByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BanPostByIdReq.Merge(dst, src)
}
func (m *BanPostByIdReq) XXX_Size() int {
	return xxx_messageInfo_BanPostByIdReq.Size(m)
}
func (m *BanPostByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BanPostByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_BanPostByIdReq proto.InternalMessageInfo

func (m *BanPostByIdReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *BanPostByIdReq) GetIsBan() bool {
	if m != nil {
		return m.IsBan
	}
	return false
}

func (m *BanPostByIdReq) GetSecondLabelId() int32 {
	if m != nil {
		return m.SecondLabelId
	}
	return 0
}

func (m *BanPostByIdReq) GetSecondLabelLevel() string {
	if m != nil {
		return m.SecondLabelLevel
	}
	return ""
}

type BanPostByIdResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BanPostByIdResp) Reset()         { *m = BanPostByIdResp{} }
func (m *BanPostByIdResp) String() string { return proto.CompactTextString(m) }
func (*BanPostByIdResp) ProtoMessage()    {}
func (*BanPostByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{21}
}
func (m *BanPostByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BanPostByIdResp.Unmarshal(m, b)
}
func (m *BanPostByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BanPostByIdResp.Marshal(b, m, deterministic)
}
func (dst *BanPostByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BanPostByIdResp.Merge(dst, src)
}
func (m *BanPostByIdResp) XXX_Size() int {
	return xxx_messageInfo_BanPostByIdResp.Size(m)
}
func (m *BanPostByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BanPostByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_BanPostByIdResp proto.InternalMessageInfo

type DelPostReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPostReq) Reset()         { *m = DelPostReq{} }
func (m *DelPostReq) String() string { return proto.CompactTextString(m) }
func (*DelPostReq) ProtoMessage()    {}
func (*DelPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{22}
}
func (m *DelPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPostReq.Unmarshal(m, b)
}
func (m *DelPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPostReq.Marshal(b, m, deterministic)
}
func (dst *DelPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPostReq.Merge(dst, src)
}
func (m *DelPostReq) XXX_Size() int {
	return xxx_messageInfo_DelPostReq.Size(m)
}
func (m *DelPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelPostReq proto.InternalMessageInfo

func (m *DelPostReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type DelPostResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPostResp) Reset()         { *m = DelPostResp{} }
func (m *DelPostResp) String() string { return proto.CompactTextString(m) }
func (*DelPostResp) ProtoMessage()    {}
func (*DelPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{23}
}
func (m *DelPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPostResp.Unmarshal(m, b)
}
func (m *DelPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPostResp.Marshal(b, m, deterministic)
}
func (dst *DelPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPostResp.Merge(dst, src)
}
func (m *DelPostResp) XXX_Size() int {
	return xxx_messageInfo_DelPostResp.Size(m)
}
func (m *DelPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelPostResp proto.InternalMessageInfo

type GetPostByIdReq struct {
	PostId               string      `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	ContentType          ContentType `protobuf:"varint,2,opt,name=content_type,json=contentType,proto3,enum=ugc.content.ContentType" json:"content_type,omitempty"`
	QueryByUserId        uint32      `protobuf:"varint,4,opt,name=query_by_user_id,json=queryByUserId,proto3" json:"query_by_user_id,omitempty"`
	MarketId             uint32      `protobuf:"varint,5,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientType           uint32      `protobuf:"varint,6,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetPostByIdReq) Reset()         { *m = GetPostByIdReq{} }
func (m *GetPostByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetPostByIdReq) ProtoMessage()    {}
func (*GetPostByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{24}
}
func (m *GetPostByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostByIdReq.Unmarshal(m, b)
}
func (m *GetPostByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetPostByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostByIdReq.Merge(dst, src)
}
func (m *GetPostByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetPostByIdReq.Size(m)
}
func (m *GetPostByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostByIdReq proto.InternalMessageInfo

func (m *GetPostByIdReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetPostByIdReq) GetContentType() ContentType {
	if m != nil {
		return m.ContentType
	}
	return ContentType_FORMATTED
}

func (m *GetPostByIdReq) GetQueryByUserId() uint32 {
	if m != nil {
		return m.QueryByUserId
	}
	return 0
}

func (m *GetPostByIdReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetPostByIdReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type GetPostByIdResp struct {
	Post                 *PostInfo `protobuf:"bytes,1,opt,name=post,proto3" json:"post,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetPostByIdResp) Reset()         { *m = GetPostByIdResp{} }
func (m *GetPostByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetPostByIdResp) ProtoMessage()    {}
func (*GetPostByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{25}
}
func (m *GetPostByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostByIdResp.Unmarshal(m, b)
}
func (m *GetPostByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetPostByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostByIdResp.Merge(dst, src)
}
func (m *GetPostByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetPostByIdResp.Size(m)
}
func (m *GetPostByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostByIdResp proto.InternalMessageInfo

func (m *GetPostByIdResp) GetPost() *PostInfo {
	if m != nil {
		return m.Post
	}
	return nil
}

type GetVMVideoExistReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Key                  string   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVMVideoExistReq) Reset()         { *m = GetVMVideoExistReq{} }
func (m *GetVMVideoExistReq) String() string { return proto.CompactTextString(m) }
func (*GetVMVideoExistReq) ProtoMessage()    {}
func (*GetVMVideoExistReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{26}
}
func (m *GetVMVideoExistReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVMVideoExistReq.Unmarshal(m, b)
}
func (m *GetVMVideoExistReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVMVideoExistReq.Marshal(b, m, deterministic)
}
func (dst *GetVMVideoExistReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVMVideoExistReq.Merge(dst, src)
}
func (m *GetVMVideoExistReq) XXX_Size() int {
	return xxx_messageInfo_GetVMVideoExistReq.Size(m)
}
func (m *GetVMVideoExistReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVMVideoExistReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetVMVideoExistReq proto.InternalMessageInfo

func (m *GetVMVideoExistReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetVMVideoExistReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type GetVMVideoExistResp struct {
	VmUrl                string   `protobuf:"bytes,1,opt,name=vm_url,json=vmUrl,proto3" json:"vm_url,omitempty"`
	Exsits               bool     `protobuf:"varint,2,opt,name=exsits,proto3" json:"exsits,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVMVideoExistResp) Reset()         { *m = GetVMVideoExistResp{} }
func (m *GetVMVideoExistResp) String() string { return proto.CompactTextString(m) }
func (*GetVMVideoExistResp) ProtoMessage()    {}
func (*GetVMVideoExistResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{27}
}
func (m *GetVMVideoExistResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVMVideoExistResp.Unmarshal(m, b)
}
func (m *GetVMVideoExistResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVMVideoExistResp.Marshal(b, m, deterministic)
}
func (dst *GetVMVideoExistResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVMVideoExistResp.Merge(dst, src)
}
func (m *GetVMVideoExistResp) XXX_Size() int {
	return xxx_messageInfo_GetVMVideoExistResp.Size(m)
}
func (m *GetVMVideoExistResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVMVideoExistResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetVMVideoExistResp proto.InternalMessageInfo

func (m *GetVMVideoExistResp) GetVmUrl() string {
	if m != nil {
		return m.VmUrl
	}
	return ""
}

func (m *GetVMVideoExistResp) GetExsits() bool {
	if m != nil {
		return m.Exsits
	}
	return false
}

type UpdateAttachmentStatusReq struct {
	PostId               string        `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string        `protobuf:"bytes,2,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	AttachmentKey        string        `protobuf:"bytes,3,opt,name=attachment_key,json=attachmentKey,proto3" json:"attachment_key,omitempty"`
	Status               ContentStatus `protobuf:"varint,4,opt,name=status,proto3,enum=ugc.content.ContentStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpdateAttachmentStatusReq) Reset()         { *m = UpdateAttachmentStatusReq{} }
func (m *UpdateAttachmentStatusReq) String() string { return proto.CompactTextString(m) }
func (*UpdateAttachmentStatusReq) ProtoMessage()    {}
func (*UpdateAttachmentStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{28}
}
func (m *UpdateAttachmentStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAttachmentStatusReq.Unmarshal(m, b)
}
func (m *UpdateAttachmentStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAttachmentStatusReq.Marshal(b, m, deterministic)
}
func (dst *UpdateAttachmentStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAttachmentStatusReq.Merge(dst, src)
}
func (m *UpdateAttachmentStatusReq) XXX_Size() int {
	return xxx_messageInfo_UpdateAttachmentStatusReq.Size(m)
}
func (m *UpdateAttachmentStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAttachmentStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAttachmentStatusReq proto.InternalMessageInfo

func (m *UpdateAttachmentStatusReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UpdateAttachmentStatusReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *UpdateAttachmentStatusReq) GetAttachmentKey() string {
	if m != nil {
		return m.AttachmentKey
	}
	return ""
}

func (m *UpdateAttachmentStatusReq) GetStatus() ContentStatus {
	if m != nil {
		return m.Status
	}
	return ContentStatus_CONTENT_STATUS_NONE
}

type UpdateAttachmentStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAttachmentStatusResp) Reset()         { *m = UpdateAttachmentStatusResp{} }
func (m *UpdateAttachmentStatusResp) String() string { return proto.CompactTextString(m) }
func (*UpdateAttachmentStatusResp) ProtoMessage()    {}
func (*UpdateAttachmentStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{29}
}
func (m *UpdateAttachmentStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAttachmentStatusResp.Unmarshal(m, b)
}
func (m *UpdateAttachmentStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAttachmentStatusResp.Marshal(b, m, deterministic)
}
func (dst *UpdateAttachmentStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAttachmentStatusResp.Merge(dst, src)
}
func (m *UpdateAttachmentStatusResp) XXX_Size() int {
	return xxx_messageInfo_UpdateAttachmentStatusResp.Size(m)
}
func (m *UpdateAttachmentStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAttachmentStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAttachmentStatusResp proto.InternalMessageInfo

// 更新视频的url地址
type UpdateVideoUrlReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string   `protobuf:"bytes,2,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	AttachmentKey        string   `protobuf:"bytes,3,opt,name=attachment_key,json=attachmentKey,proto3" json:"attachment_key,omitempty"`
	NewUrl               string   `protobuf:"bytes,4,opt,name=new_url,json=newUrl,proto3" json:"new_url,omitempty"`
	VmUrl                string   `protobuf:"bytes,5,opt,name=vm_url,json=vmUrl,proto3" json:"vm_url,omitempty"`
	NoVmUrl              string   `protobuf:"bytes,6,opt,name=no_vm_url,json=noVmUrl,proto3" json:"no_vm_url,omitempty"`
	IsObs                bool     `protobuf:"varint,7,opt,name=is_obs,json=isObs,proto3" json:"is_obs,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateVideoUrlReq) Reset()         { *m = UpdateVideoUrlReq{} }
func (m *UpdateVideoUrlReq) String() string { return proto.CompactTextString(m) }
func (*UpdateVideoUrlReq) ProtoMessage()    {}
func (*UpdateVideoUrlReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{30}
}
func (m *UpdateVideoUrlReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateVideoUrlReq.Unmarshal(m, b)
}
func (m *UpdateVideoUrlReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateVideoUrlReq.Marshal(b, m, deterministic)
}
func (dst *UpdateVideoUrlReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateVideoUrlReq.Merge(dst, src)
}
func (m *UpdateVideoUrlReq) XXX_Size() int {
	return xxx_messageInfo_UpdateVideoUrlReq.Size(m)
}
func (m *UpdateVideoUrlReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateVideoUrlReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateVideoUrlReq proto.InternalMessageInfo

func (m *UpdateVideoUrlReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UpdateVideoUrlReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *UpdateVideoUrlReq) GetAttachmentKey() string {
	if m != nil {
		return m.AttachmentKey
	}
	return ""
}

func (m *UpdateVideoUrlReq) GetNewUrl() string {
	if m != nil {
		return m.NewUrl
	}
	return ""
}

func (m *UpdateVideoUrlReq) GetVmUrl() string {
	if m != nil {
		return m.VmUrl
	}
	return ""
}

func (m *UpdateVideoUrlReq) GetNoVmUrl() string {
	if m != nil {
		return m.NoVmUrl
	}
	return ""
}

func (m *UpdateVideoUrlReq) GetIsObs() bool {
	if m != nil {
		return m.IsObs
	}
	return false
}

type UpdateVideoUrlResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateVideoUrlResp) Reset()         { *m = UpdateVideoUrlResp{} }
func (m *UpdateVideoUrlResp) String() string { return proto.CompactTextString(m) }
func (*UpdateVideoUrlResp) ProtoMessage()    {}
func (*UpdateVideoUrlResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{31}
}
func (m *UpdateVideoUrlResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateVideoUrlResp.Unmarshal(m, b)
}
func (m *UpdateVideoUrlResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateVideoUrlResp.Marshal(b, m, deterministic)
}
func (dst *UpdateVideoUrlResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateVideoUrlResp.Merge(dst, src)
}
func (m *UpdateVideoUrlResp) XXX_Size() int {
	return xxx_messageInfo_UpdateVideoUrlResp.Size(m)
}
func (m *UpdateVideoUrlResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateVideoUrlResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateVideoUrlResp proto.InternalMessageInfo

type UpdatePostSpecialLabelReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Label                string   `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePostSpecialLabelReq) Reset()         { *m = UpdatePostSpecialLabelReq{} }
func (m *UpdatePostSpecialLabelReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePostSpecialLabelReq) ProtoMessage()    {}
func (*UpdatePostSpecialLabelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{32}
}
func (m *UpdatePostSpecialLabelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePostSpecialLabelReq.Unmarshal(m, b)
}
func (m *UpdatePostSpecialLabelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePostSpecialLabelReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePostSpecialLabelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePostSpecialLabelReq.Merge(dst, src)
}
func (m *UpdatePostSpecialLabelReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePostSpecialLabelReq.Size(m)
}
func (m *UpdatePostSpecialLabelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePostSpecialLabelReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePostSpecialLabelReq proto.InternalMessageInfo

func (m *UpdatePostSpecialLabelReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UpdatePostSpecialLabelReq) GetLabel() string {
	if m != nil {
		return m.Label
	}
	return ""
}

type UpdatePostSpecialLabelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePostSpecialLabelResp) Reset()         { *m = UpdatePostSpecialLabelResp{} }
func (m *UpdatePostSpecialLabelResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePostSpecialLabelResp) ProtoMessage()    {}
func (*UpdatePostSpecialLabelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{33}
}
func (m *UpdatePostSpecialLabelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePostSpecialLabelResp.Unmarshal(m, b)
}
func (m *UpdatePostSpecialLabelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePostSpecialLabelResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePostSpecialLabelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePostSpecialLabelResp.Merge(dst, src)
}
func (m *UpdatePostSpecialLabelResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePostSpecialLabelResp.Size(m)
}
func (m *UpdatePostSpecialLabelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePostSpecialLabelResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePostSpecialLabelResp proto.InternalMessageInfo

type ReportPostViewReq struct {
	UserId               uint32                                `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PostIds              map[string]ReportPostViewReq_ViewType `protobuf:"bytes,2,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=ugc.content.ReportPostViewReq_ViewType"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *ReportPostViewReq) Reset()         { *m = ReportPostViewReq{} }
func (m *ReportPostViewReq) String() string { return proto.CompactTextString(m) }
func (*ReportPostViewReq) ProtoMessage()    {}
func (*ReportPostViewReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{34}
}
func (m *ReportPostViewReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportPostViewReq.Unmarshal(m, b)
}
func (m *ReportPostViewReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportPostViewReq.Marshal(b, m, deterministic)
}
func (dst *ReportPostViewReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportPostViewReq.Merge(dst, src)
}
func (m *ReportPostViewReq) XXX_Size() int {
	return xxx_messageInfo_ReportPostViewReq.Size(m)
}
func (m *ReportPostViewReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportPostViewReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportPostViewReq proto.InternalMessageInfo

func (m *ReportPostViewReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *ReportPostViewReq) GetPostIds() map[string]ReportPostViewReq_ViewType {
	if m != nil {
		return m.PostIds
	}
	return nil
}

type ReportPostViewResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportPostViewResp) Reset()         { *m = ReportPostViewResp{} }
func (m *ReportPostViewResp) String() string { return proto.CompactTextString(m) }
func (*ReportPostViewResp) ProtoMessage()    {}
func (*ReportPostViewResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{35}
}
func (m *ReportPostViewResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportPostViewResp.Unmarshal(m, b)
}
func (m *ReportPostViewResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportPostViewResp.Marshal(b, m, deterministic)
}
func (dst *ReportPostViewResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportPostViewResp.Merge(dst, src)
}
func (m *ReportPostViewResp) XXX_Size() int {
	return xxx_messageInfo_ReportPostViewResp.Size(m)
}
func (m *ReportPostViewResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportPostViewResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportPostViewResp proto.InternalMessageInfo

type ReportPostViewV2Req struct {
	UserId  uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PostIds []string `protobuf:"bytes,2,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty"`
	// 过滤器, 在resp返回的帖子阅读数为"第一次"大于这个数量的已读帖子的子集, 传0则不返回
	FilterMinCount       uint32   `protobuf:"varint,3,opt,name=filter_min_count,json=filterMinCount,proto3" json:"filter_min_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportPostViewV2Req) Reset()         { *m = ReportPostViewV2Req{} }
func (m *ReportPostViewV2Req) String() string { return proto.CompactTextString(m) }
func (*ReportPostViewV2Req) ProtoMessage()    {}
func (*ReportPostViewV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{36}
}
func (m *ReportPostViewV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportPostViewV2Req.Unmarshal(m, b)
}
func (m *ReportPostViewV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportPostViewV2Req.Marshal(b, m, deterministic)
}
func (dst *ReportPostViewV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportPostViewV2Req.Merge(dst, src)
}
func (m *ReportPostViewV2Req) XXX_Size() int {
	return xxx_messageInfo_ReportPostViewV2Req.Size(m)
}
func (m *ReportPostViewV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportPostViewV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_ReportPostViewV2Req proto.InternalMessageInfo

func (m *ReportPostViewV2Req) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *ReportPostViewV2Req) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

func (m *ReportPostViewV2Req) GetFilterMinCount() uint32 {
	if m != nil {
		return m.FilterMinCount
	}
	return 0
}

type ReportPostViewV2Resp struct {
	SignificantPostIds   []string `protobuf:"bytes,1,rep,name=significant_post_ids,json=significantPostIds,proto3" json:"significant_post_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportPostViewV2Resp) Reset()         { *m = ReportPostViewV2Resp{} }
func (m *ReportPostViewV2Resp) String() string { return proto.CompactTextString(m) }
func (*ReportPostViewV2Resp) ProtoMessage()    {}
func (*ReportPostViewV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{37}
}
func (m *ReportPostViewV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportPostViewV2Resp.Unmarshal(m, b)
}
func (m *ReportPostViewV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportPostViewV2Resp.Marshal(b, m, deterministic)
}
func (dst *ReportPostViewV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportPostViewV2Resp.Merge(dst, src)
}
func (m *ReportPostViewV2Resp) XXX_Size() int {
	return xxx_messageInfo_ReportPostViewV2Resp.Size(m)
}
func (m *ReportPostViewV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportPostViewV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportPostViewV2Resp proto.InternalMessageInfo

func (m *ReportPostViewV2Resp) GetSignificantPostIds() []string {
	if m != nil {
		return m.SignificantPostIds
	}
	return nil
}

type ReportTopicViewCountReq struct {
	TopicIds             []string `protobuf:"bytes,1,rep,name=topic_ids,json=topicIds,proto3" json:"topic_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportTopicViewCountReq) Reset()         { *m = ReportTopicViewCountReq{} }
func (m *ReportTopicViewCountReq) String() string { return proto.CompactTextString(m) }
func (*ReportTopicViewCountReq) ProtoMessage()    {}
func (*ReportTopicViewCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{38}
}
func (m *ReportTopicViewCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportTopicViewCountReq.Unmarshal(m, b)
}
func (m *ReportTopicViewCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportTopicViewCountReq.Marshal(b, m, deterministic)
}
func (dst *ReportTopicViewCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportTopicViewCountReq.Merge(dst, src)
}
func (m *ReportTopicViewCountReq) XXX_Size() int {
	return xxx_messageInfo_ReportTopicViewCountReq.Size(m)
}
func (m *ReportTopicViewCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportTopicViewCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportTopicViewCountReq proto.InternalMessageInfo

func (m *ReportTopicViewCountReq) GetTopicIds() []string {
	if m != nil {
		return m.TopicIds
	}
	return nil
}

type ReportTopicViewCountRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportTopicViewCountRsp) Reset()         { *m = ReportTopicViewCountRsp{} }
func (m *ReportTopicViewCountRsp) String() string { return proto.CompactTextString(m) }
func (*ReportTopicViewCountRsp) ProtoMessage()    {}
func (*ReportTopicViewCountRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{39}
}
func (m *ReportTopicViewCountRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportTopicViewCountRsp.Unmarshal(m, b)
}
func (m *ReportTopicViewCountRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportTopicViewCountRsp.Marshal(b, m, deterministic)
}
func (dst *ReportTopicViewCountRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportTopicViewCountRsp.Merge(dst, src)
}
func (m *ReportTopicViewCountRsp) XXX_Size() int {
	return xxx_messageInfo_ReportTopicViewCountRsp.Size(m)
}
func (m *ReportTopicViewCountRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportTopicViewCountRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportTopicViewCountRsp proto.InternalMessageInfo

type GetTopicViewCountReq struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicViewCountReq) Reset()         { *m = GetTopicViewCountReq{} }
func (m *GetTopicViewCountReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicViewCountReq) ProtoMessage()    {}
func (*GetTopicViewCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{40}
}
func (m *GetTopicViewCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicViewCountReq.Unmarshal(m, b)
}
func (m *GetTopicViewCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicViewCountReq.Marshal(b, m, deterministic)
}
func (dst *GetTopicViewCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicViewCountReq.Merge(dst, src)
}
func (m *GetTopicViewCountReq) XXX_Size() int {
	return xxx_messageInfo_GetTopicViewCountReq.Size(m)
}
func (m *GetTopicViewCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicViewCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicViewCountReq proto.InternalMessageInfo

func (m *GetTopicViewCountReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

type GetTopicViewCountRsp struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicViewCountRsp) Reset()         { *m = GetTopicViewCountRsp{} }
func (m *GetTopicViewCountRsp) String() string { return proto.CompactTextString(m) }
func (*GetTopicViewCountRsp) ProtoMessage()    {}
func (*GetTopicViewCountRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{41}
}
func (m *GetTopicViewCountRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicViewCountRsp.Unmarshal(m, b)
}
func (m *GetTopicViewCountRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicViewCountRsp.Marshal(b, m, deterministic)
}
func (dst *GetTopicViewCountRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicViewCountRsp.Merge(dst, src)
}
func (m *GetTopicViewCountRsp) XXX_Size() int {
	return xxx_messageInfo_GetTopicViewCountRsp.Size(m)
}
func (m *GetTopicViewCountRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicViewCountRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicViewCountRsp proto.InternalMessageInfo

func (m *GetTopicViewCountRsp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type ReportPostShareReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportPostShareReq) Reset()         { *m = ReportPostShareReq{} }
func (m *ReportPostShareReq) String() string { return proto.CompactTextString(m) }
func (*ReportPostShareReq) ProtoMessage()    {}
func (*ReportPostShareReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{42}
}
func (m *ReportPostShareReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportPostShareReq.Unmarshal(m, b)
}
func (m *ReportPostShareReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportPostShareReq.Marshal(b, m, deterministic)
}
func (dst *ReportPostShareReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportPostShareReq.Merge(dst, src)
}
func (m *ReportPostShareReq) XXX_Size() int {
	return xxx_messageInfo_ReportPostShareReq.Size(m)
}
func (m *ReportPostShareReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportPostShareReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportPostShareReq proto.InternalMessageInfo

func (m *ReportPostShareReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *ReportPostShareReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type ReportPostShareResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportPostShareResp) Reset()         { *m = ReportPostShareResp{} }
func (m *ReportPostShareResp) String() string { return proto.CompactTextString(m) }
func (*ReportPostShareResp) ProtoMessage()    {}
func (*ReportPostShareResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{43}
}
func (m *ReportPostShareResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportPostShareResp.Unmarshal(m, b)
}
func (m *ReportPostShareResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportPostShareResp.Marshal(b, m, deterministic)
}
func (dst *ReportPostShareResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportPostShareResp.Merge(dst, src)
}
func (m *ReportPostShareResp) XXX_Size() int {
	return xxx_messageInfo_ReportPostShareResp.Size(m)
}
func (m *ReportPostShareResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportPostShareResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportPostShareResp proto.InternalMessageInfo

// -------- 评论 --------
type AddCommentReq struct {
	UserId               uint32            `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PostId               string            `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	ReplyToCommentId     string            `protobuf:"bytes,3,opt,name=reply_to_comment_id,json=replyToCommentId,proto3" json:"reply_to_comment_id,omitempty"`
	ConversationId       string            `protobuf:"bytes,4,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	Content              string            `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	Status               ContentStatus     `protobuf:"varint,6,opt,name=status,proto3,enum=ugc.content.ContentStatus" json:"status,omitempty"`
	Attachments          []*AttachmentInfo `protobuf:"bytes,7,rep,name=attachments,proto3" json:"attachments,omitempty"`
	AttachmentImageCount uint32            `protobuf:"varint,8,opt,name=attachment_image_count,json=attachmentImageCount,proto3" json:"attachment_image_count,omitempty"`
	ContentType          int32             `protobuf:"varint,9,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	Source               uint32            `protobuf:"varint,10,opt,name=source,proto3" json:"source,omitempty"`
	IsObs                bool              `protobuf:"varint,11,opt,name=is_obs,json=isObs,proto3" json:"is_obs,omitempty"`
	// 评论人的IP归属地
	IpLoc                *Location `protobuf:"bytes,12,opt,name=ip_loc,json=ipLoc,proto3" json:"ip_loc,omitempty"`
	ClientIp             uint32    `protobuf:"varint,13,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *AddCommentReq) Reset()         { *m = AddCommentReq{} }
func (m *AddCommentReq) String() string { return proto.CompactTextString(m) }
func (*AddCommentReq) ProtoMessage()    {}
func (*AddCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{44}
}
func (m *AddCommentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCommentReq.Unmarshal(m, b)
}
func (m *AddCommentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCommentReq.Marshal(b, m, deterministic)
}
func (dst *AddCommentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCommentReq.Merge(dst, src)
}
func (m *AddCommentReq) XXX_Size() int {
	return xxx_messageInfo_AddCommentReq.Size(m)
}
func (m *AddCommentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCommentReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddCommentReq proto.InternalMessageInfo

func (m *AddCommentReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *AddCommentReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *AddCommentReq) GetReplyToCommentId() string {
	if m != nil {
		return m.ReplyToCommentId
	}
	return ""
}

func (m *AddCommentReq) GetConversationId() string {
	if m != nil {
		return m.ConversationId
	}
	return ""
}

func (m *AddCommentReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *AddCommentReq) GetStatus() ContentStatus {
	if m != nil {
		return m.Status
	}
	return ContentStatus_CONTENT_STATUS_NONE
}

func (m *AddCommentReq) GetAttachments() []*AttachmentInfo {
	if m != nil {
		return m.Attachments
	}
	return nil
}

func (m *AddCommentReq) GetAttachmentImageCount() uint32 {
	if m != nil {
		return m.AttachmentImageCount
	}
	return 0
}

func (m *AddCommentReq) GetContentType() int32 {
	if m != nil {
		return m.ContentType
	}
	return 0
}

func (m *AddCommentReq) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *AddCommentReq) GetIsObs() bool {
	if m != nil {
		return m.IsObs
	}
	return false
}

func (m *AddCommentReq) GetIpLoc() *Location {
	if m != nil {
		return m.IpLoc
	}
	return nil
}

func (m *AddCommentReq) GetClientIp() uint32 {
	if m != nil {
		return m.ClientIp
	}
	return 0
}

type AddCommentResp struct {
	CommentId            string       `protobuf:"bytes,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	Comment              *CommentInfo `protobuf:"bytes,2,opt,name=comment,proto3" json:"comment,omitempty"`
	AttachmentImageKeys  []string     `protobuf:"bytes,10,rep,name=attachment_image_keys,json=attachmentImageKeys,proto3" json:"attachment_image_keys,omitempty"`
	ImageUploadToken     string       `protobuf:"bytes,11,opt,name=image_upload_token,json=imageUploadToken,proto3" json:"image_upload_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AddCommentResp) Reset()         { *m = AddCommentResp{} }
func (m *AddCommentResp) String() string { return proto.CompactTextString(m) }
func (*AddCommentResp) ProtoMessage()    {}
func (*AddCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{45}
}
func (m *AddCommentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCommentResp.Unmarshal(m, b)
}
func (m *AddCommentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCommentResp.Marshal(b, m, deterministic)
}
func (dst *AddCommentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCommentResp.Merge(dst, src)
}
func (m *AddCommentResp) XXX_Size() int {
	return xxx_messageInfo_AddCommentResp.Size(m)
}
func (m *AddCommentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCommentResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddCommentResp proto.InternalMessageInfo

func (m *AddCommentResp) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *AddCommentResp) GetComment() *CommentInfo {
	if m != nil {
		return m.Comment
	}
	return nil
}

func (m *AddCommentResp) GetAttachmentImageKeys() []string {
	if m != nil {
		return m.AttachmentImageKeys
	}
	return nil
}

func (m *AddCommentResp) GetImageUploadToken() string {
	if m != nil {
		return m.ImageUploadToken
	}
	return ""
}

type GetCommentListReq struct {
	PostId               string      `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	ConversationId       string      `protobuf:"bytes,2,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	LoadMore             string      `protobuf:"bytes,3,opt,name=loadMore,proto3" json:"loadMore,omitempty"`
	Count                uint32      `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	UserId               uint32      `protobuf:"varint,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Ascending            bool        `protobuf:"varint,6,opt,name=ascending,proto3" json:"ascending,omitempty"`
	SubCommentsAscending bool        `protobuf:"varint,7,opt,name=sub_comments_ascending,json=subCommentsAscending,proto3" json:"sub_comments_ascending,omitempty"`
	ContentType          ContentType `protobuf:"varint,8,opt,name=content_type,json=contentType,proto3,enum=ugc.content.ContentType" json:"content_type,omitempty"`
	MarketId             uint32      `protobuf:"varint,9,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientType           uint32      `protobuf:"varint,10,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetCommentListReq) Reset()         { *m = GetCommentListReq{} }
func (m *GetCommentListReq) String() string { return proto.CompactTextString(m) }
func (*GetCommentListReq) ProtoMessage()    {}
func (*GetCommentListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{46}
}
func (m *GetCommentListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommentListReq.Unmarshal(m, b)
}
func (m *GetCommentListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommentListReq.Marshal(b, m, deterministic)
}
func (dst *GetCommentListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommentListReq.Merge(dst, src)
}
func (m *GetCommentListReq) XXX_Size() int {
	return xxx_messageInfo_GetCommentListReq.Size(m)
}
func (m *GetCommentListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommentListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommentListReq proto.InternalMessageInfo

func (m *GetCommentListReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetCommentListReq) GetConversationId() string {
	if m != nil {
		return m.ConversationId
	}
	return ""
}

func (m *GetCommentListReq) GetLoadMore() string {
	if m != nil {
		return m.LoadMore
	}
	return ""
}

func (m *GetCommentListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetCommentListReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *GetCommentListReq) GetAscending() bool {
	if m != nil {
		return m.Ascending
	}
	return false
}

func (m *GetCommentListReq) GetSubCommentsAscending() bool {
	if m != nil {
		return m.SubCommentsAscending
	}
	return false
}

func (m *GetCommentListReq) GetContentType() ContentType {
	if m != nil {
		return m.ContentType
	}
	return ContentType_FORMATTED
}

func (m *GetCommentListReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetCommentListReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type GetCommentListResp struct {
	CommentList          []*CommentInfo `protobuf:"bytes,1,rep,name=comment_list,json=commentList,proto3" json:"comment_list,omitempty"`
	LoadMore             string         `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetCommentListResp) Reset()         { *m = GetCommentListResp{} }
func (m *GetCommentListResp) String() string { return proto.CompactTextString(m) }
func (*GetCommentListResp) ProtoMessage()    {}
func (*GetCommentListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{47}
}
func (m *GetCommentListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommentListResp.Unmarshal(m, b)
}
func (m *GetCommentListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommentListResp.Marshal(b, m, deterministic)
}
func (dst *GetCommentListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommentListResp.Merge(dst, src)
}
func (m *GetCommentListResp) XXX_Size() int {
	return xxx_messageInfo_GetCommentListResp.Size(m)
}
func (m *GetCommentListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommentListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommentListResp proto.InternalMessageInfo

func (m *GetCommentListResp) GetCommentList() []*CommentInfo {
	if m != nil {
		return m.CommentList
	}
	return nil
}

func (m *GetCommentListResp) GetLoadMore() string {
	if m != nil {
		return m.LoadMore
	}
	return ""
}

type DelCommentReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string   `protobuf:"bytes,2,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	ConversationId       string   `protobuf:"bytes,3,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCommentReq) Reset()         { *m = DelCommentReq{} }
func (m *DelCommentReq) String() string { return proto.CompactTextString(m) }
func (*DelCommentReq) ProtoMessage()    {}
func (*DelCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{48}
}
func (m *DelCommentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCommentReq.Unmarshal(m, b)
}
func (m *DelCommentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCommentReq.Marshal(b, m, deterministic)
}
func (dst *DelCommentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCommentReq.Merge(dst, src)
}
func (m *DelCommentReq) XXX_Size() int {
	return xxx_messageInfo_DelCommentReq.Size(m)
}
func (m *DelCommentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCommentReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelCommentReq proto.InternalMessageInfo

func (m *DelCommentReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *DelCommentReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *DelCommentReq) GetConversationId() string {
	if m != nil {
		return m.ConversationId
	}
	return ""
}

type DelCommentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCommentResp) Reset()         { *m = DelCommentResp{} }
func (m *DelCommentResp) String() string { return proto.CompactTextString(m) }
func (*DelCommentResp) ProtoMessage()    {}
func (*DelCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{49}
}
func (m *DelCommentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCommentResp.Unmarshal(m, b)
}
func (m *DelCommentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCommentResp.Marshal(b, m, deterministic)
}
func (dst *DelCommentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCommentResp.Merge(dst, src)
}
func (m *DelCommentResp) XXX_Size() int {
	return xxx_messageInfo_DelCommentResp.Size(m)
}
func (m *DelCommentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCommentResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelCommentResp proto.InternalMessageInfo

type GetCommentByIdReq struct {
	CommentId            string      `protobuf:"bytes,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	ContentType          ContentType `protobuf:"varint,2,opt,name=content_type,json=contentType,proto3,enum=ugc.content.ContentType" json:"content_type,omitempty"`
	MarketId             uint32      `protobuf:"varint,3,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientType           uint32      `protobuf:"varint,4,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetCommentByIdReq) Reset()         { *m = GetCommentByIdReq{} }
func (m *GetCommentByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetCommentByIdReq) ProtoMessage()    {}
func (*GetCommentByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{50}
}
func (m *GetCommentByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommentByIdReq.Unmarshal(m, b)
}
func (m *GetCommentByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommentByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetCommentByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommentByIdReq.Merge(dst, src)
}
func (m *GetCommentByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetCommentByIdReq.Size(m)
}
func (m *GetCommentByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommentByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommentByIdReq proto.InternalMessageInfo

func (m *GetCommentByIdReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *GetCommentByIdReq) GetContentType() ContentType {
	if m != nil {
		return m.ContentType
	}
	return ContentType_FORMATTED
}

func (m *GetCommentByIdReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetCommentByIdReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type GetCommentByIdResp struct {
	Comment              *CommentInfo `protobuf:"bytes,1,opt,name=comment,proto3" json:"comment,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetCommentByIdResp) Reset()         { *m = GetCommentByIdResp{} }
func (m *GetCommentByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetCommentByIdResp) ProtoMessage()    {}
func (*GetCommentByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{51}
}
func (m *GetCommentByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommentByIdResp.Unmarshal(m, b)
}
func (m *GetCommentByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommentByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetCommentByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommentByIdResp.Merge(dst, src)
}
func (m *GetCommentByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetCommentByIdResp.Size(m)
}
func (m *GetCommentByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommentByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommentByIdResp proto.InternalMessageInfo

func (m *GetCommentByIdResp) GetComment() *CommentInfo {
	if m != nil {
		return m.Comment
	}
	return nil
}

type BatchGetCommentByIdsReq struct {
	CommentIdList        []string    `protobuf:"bytes,1,rep,name=comment_id_list,json=commentIdList,proto3" json:"comment_id_list,omitempty"`
	ContentType          ContentType `protobuf:"varint,2,opt,name=content_type,json=contentType,proto3,enum=ugc.content.ContentType" json:"content_type,omitempty"`
	MarketId             uint32      `protobuf:"varint,3,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientType           uint32      `protobuf:"varint,4,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *BatchGetCommentByIdsReq) Reset()         { *m = BatchGetCommentByIdsReq{} }
func (m *BatchGetCommentByIdsReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetCommentByIdsReq) ProtoMessage()    {}
func (*BatchGetCommentByIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{52}
}
func (m *BatchGetCommentByIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCommentByIdsReq.Unmarshal(m, b)
}
func (m *BatchGetCommentByIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCommentByIdsReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetCommentByIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCommentByIdsReq.Merge(dst, src)
}
func (m *BatchGetCommentByIdsReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetCommentByIdsReq.Size(m)
}
func (m *BatchGetCommentByIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCommentByIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCommentByIdsReq proto.InternalMessageInfo

func (m *BatchGetCommentByIdsReq) GetCommentIdList() []string {
	if m != nil {
		return m.CommentIdList
	}
	return nil
}

func (m *BatchGetCommentByIdsReq) GetContentType() ContentType {
	if m != nil {
		return m.ContentType
	}
	return ContentType_FORMATTED
}

func (m *BatchGetCommentByIdsReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *BatchGetCommentByIdsReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type BatchGetCommentByIdsResp struct {
	CommentInfos         map[string]*CommentInfo `protobuf:"bytes,1,rep,name=comment_infos,json=commentInfos,proto3" json:"comment_infos,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatchGetCommentByIdsResp) Reset()         { *m = BatchGetCommentByIdsResp{} }
func (m *BatchGetCommentByIdsResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetCommentByIdsResp) ProtoMessage()    {}
func (*BatchGetCommentByIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{53}
}
func (m *BatchGetCommentByIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCommentByIdsResp.Unmarshal(m, b)
}
func (m *BatchGetCommentByIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCommentByIdsResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetCommentByIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCommentByIdsResp.Merge(dst, src)
}
func (m *BatchGetCommentByIdsResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetCommentByIdsResp.Size(m)
}
func (m *BatchGetCommentByIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCommentByIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCommentByIdsResp proto.InternalMessageInfo

func (m *BatchGetCommentByIdsResp) GetCommentInfos() map[string]*CommentInfo {
	if m != nil {
		return m.CommentInfos
	}
	return nil
}

type BanCommentByIdReq struct {
	CommentId            string   `protobuf:"bytes,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	IsBan                bool     `protobuf:"varint,2,opt,name=is_ban,json=isBan,proto3" json:"is_ban,omitempty"`
	IsDelete             bool     `protobuf:"varint,3,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BanCommentByIdReq) Reset()         { *m = BanCommentByIdReq{} }
func (m *BanCommentByIdReq) String() string { return proto.CompactTextString(m) }
func (*BanCommentByIdReq) ProtoMessage()    {}
func (*BanCommentByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{54}
}
func (m *BanCommentByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BanCommentByIdReq.Unmarshal(m, b)
}
func (m *BanCommentByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BanCommentByIdReq.Marshal(b, m, deterministic)
}
func (dst *BanCommentByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BanCommentByIdReq.Merge(dst, src)
}
func (m *BanCommentByIdReq) XXX_Size() int {
	return xxx_messageInfo_BanCommentByIdReq.Size(m)
}
func (m *BanCommentByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BanCommentByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_BanCommentByIdReq proto.InternalMessageInfo

func (m *BanCommentByIdReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *BanCommentByIdReq) GetIsBan() bool {
	if m != nil {
		return m.IsBan
	}
	return false
}

func (m *BanCommentByIdReq) GetIsDelete() bool {
	if m != nil {
		return m.IsDelete
	}
	return false
}

type BanCommentByIdResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BanCommentByIdResp) Reset()         { *m = BanCommentByIdResp{} }
func (m *BanCommentByIdResp) String() string { return proto.CompactTextString(m) }
func (*BanCommentByIdResp) ProtoMessage()    {}
func (*BanCommentByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{55}
}
func (m *BanCommentByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BanCommentByIdResp.Unmarshal(m, b)
}
func (m *BanCommentByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BanCommentByIdResp.Marshal(b, m, deterministic)
}
func (dst *BanCommentByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BanCommentByIdResp.Merge(dst, src)
}
func (m *BanCommentByIdResp) XXX_Size() int {
	return xxx_messageInfo_BanCommentByIdResp.Size(m)
}
func (m *BanCommentByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BanCommentByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_BanCommentByIdResp proto.InternalMessageInfo

// -------- 点赞 --------
type AddAttitudeReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string   `protobuf:"bytes,2,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	UserId               uint32   `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	AttitudeType         uint32   `protobuf:"varint,4,opt,name=attitude_type,json=attitudeType,proto3" json:"attitude_type,omitempty"`
	IsFirstTime          bool     `protobuf:"varint,5,opt,name=is_first_time,json=isFirstTime,proto3" json:"is_first_time,omitempty"`
	TargetUserId         uint32   `protobuf:"varint,6,opt,name=target_user_id,json=targetUserId,proto3" json:"target_user_id,omitempty"`
	Source               uint32   `protobuf:"varint,7,opt,name=source,proto3" json:"source,omitempty"`
	StepType             uint32   `protobuf:"varint,8,opt,name=step_type,json=stepType,proto3" json:"step_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAttitudeReq) Reset()         { *m = AddAttitudeReq{} }
func (m *AddAttitudeReq) String() string { return proto.CompactTextString(m) }
func (*AddAttitudeReq) ProtoMessage()    {}
func (*AddAttitudeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{56}
}
func (m *AddAttitudeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAttitudeReq.Unmarshal(m, b)
}
func (m *AddAttitudeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAttitudeReq.Marshal(b, m, deterministic)
}
func (dst *AddAttitudeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAttitudeReq.Merge(dst, src)
}
func (m *AddAttitudeReq) XXX_Size() int {
	return xxx_messageInfo_AddAttitudeReq.Size(m)
}
func (m *AddAttitudeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAttitudeReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddAttitudeReq proto.InternalMessageInfo

func (m *AddAttitudeReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *AddAttitudeReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *AddAttitudeReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *AddAttitudeReq) GetAttitudeType() uint32 {
	if m != nil {
		return m.AttitudeType
	}
	return 0
}

func (m *AddAttitudeReq) GetIsFirstTime() bool {
	if m != nil {
		return m.IsFirstTime
	}
	return false
}

func (m *AddAttitudeReq) GetTargetUserId() uint32 {
	if m != nil {
		return m.TargetUserId
	}
	return 0
}

func (m *AddAttitudeReq) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *AddAttitudeReq) GetStepType() uint32 {
	if m != nil {
		return m.StepType
	}
	return 0
}

type AddAttitudeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAttitudeResp) Reset()         { *m = AddAttitudeResp{} }
func (m *AddAttitudeResp) String() string { return proto.CompactTextString(m) }
func (*AddAttitudeResp) ProtoMessage()    {}
func (*AddAttitudeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{57}
}
func (m *AddAttitudeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAttitudeResp.Unmarshal(m, b)
}
func (m *AddAttitudeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAttitudeResp.Marshal(b, m, deterministic)
}
func (dst *AddAttitudeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAttitudeResp.Merge(dst, src)
}
func (m *AddAttitudeResp) XXX_Size() int {
	return xxx_messageInfo_AddAttitudeResp.Size(m)
}
func (m *AddAttitudeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAttitudeResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddAttitudeResp proto.InternalMessageInfo

// 取消点赞
type DelAttitudeReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string   `protobuf:"bytes,2,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	UserId               uint32   `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	TargetUserId         uint32   `protobuf:"varint,4,opt,name=target_user_id,json=targetUserId,proto3" json:"target_user_id,omitempty"`
	Source               uint32   `protobuf:"varint,5,opt,name=source,proto3" json:"source,omitempty"`
	AttitudeType         uint32   `protobuf:"varint,6,opt,name=attitude_type,json=attitudeType,proto3" json:"attitude_type,omitempty"`
	StepType             uint32   `protobuf:"varint,7,opt,name=step_type,json=stepType,proto3" json:"step_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelAttitudeReq) Reset()         { *m = DelAttitudeReq{} }
func (m *DelAttitudeReq) String() string { return proto.CompactTextString(m) }
func (*DelAttitudeReq) ProtoMessage()    {}
func (*DelAttitudeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{58}
}
func (m *DelAttitudeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelAttitudeReq.Unmarshal(m, b)
}
func (m *DelAttitudeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelAttitudeReq.Marshal(b, m, deterministic)
}
func (dst *DelAttitudeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelAttitudeReq.Merge(dst, src)
}
func (m *DelAttitudeReq) XXX_Size() int {
	return xxx_messageInfo_DelAttitudeReq.Size(m)
}
func (m *DelAttitudeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelAttitudeReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelAttitudeReq proto.InternalMessageInfo

func (m *DelAttitudeReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *DelAttitudeReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *DelAttitudeReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *DelAttitudeReq) GetTargetUserId() uint32 {
	if m != nil {
		return m.TargetUserId
	}
	return 0
}

func (m *DelAttitudeReq) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *DelAttitudeReq) GetAttitudeType() uint32 {
	if m != nil {
		return m.AttitudeType
	}
	return 0
}

func (m *DelAttitudeReq) GetStepType() uint32 {
	if m != nil {
		return m.StepType
	}
	return 0
}

type DelAttitudeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelAttitudeResp) Reset()         { *m = DelAttitudeResp{} }
func (m *DelAttitudeResp) String() string { return proto.CompactTextString(m) }
func (*DelAttitudeResp) ProtoMessage()    {}
func (*DelAttitudeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{59}
}
func (m *DelAttitudeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelAttitudeResp.Unmarshal(m, b)
}
func (m *DelAttitudeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelAttitudeResp.Marshal(b, m, deterministic)
}
func (dst *DelAttitudeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelAttitudeResp.Merge(dst, src)
}
func (m *DelAttitudeResp) XXX_Size() int {
	return xxx_messageInfo_DelAttitudeResp.Size(m)
}
func (m *DelAttitudeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelAttitudeResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelAttitudeResp proto.InternalMessageInfo

type AttitudeUserInfo struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	AttitudeType         uint32   `protobuf:"varint,2,opt,name=attitude_type,json=attitudeType,proto3" json:"attitude_type,omitempty"`
	Time                 uint64   `protobuf:"varint,3,opt,name=time,proto3" json:"time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AttitudeUserInfo) Reset()         { *m = AttitudeUserInfo{} }
func (m *AttitudeUserInfo) String() string { return proto.CompactTextString(m) }
func (*AttitudeUserInfo) ProtoMessage()    {}
func (*AttitudeUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{60}
}
func (m *AttitudeUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AttitudeUserInfo.Unmarshal(m, b)
}
func (m *AttitudeUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AttitudeUserInfo.Marshal(b, m, deterministic)
}
func (dst *AttitudeUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AttitudeUserInfo.Merge(dst, src)
}
func (m *AttitudeUserInfo) XXX_Size() int {
	return xxx_messageInfo_AttitudeUserInfo.Size(m)
}
func (m *AttitudeUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AttitudeUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AttitudeUserInfo proto.InternalMessageInfo

func (m *AttitudeUserInfo) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *AttitudeUserInfo) GetAttitudeType() uint32 {
	if m != nil {
		return m.AttitudeType
	}
	return 0
}

func (m *AttitudeUserInfo) GetTime() uint64 {
	if m != nil {
		return m.Time
	}
	return 0
}

type GetAttitudeUserListReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string   `protobuf:"bytes,2,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAttitudeUserListReq) Reset()         { *m = GetAttitudeUserListReq{} }
func (m *GetAttitudeUserListReq) String() string { return proto.CompactTextString(m) }
func (*GetAttitudeUserListReq) ProtoMessage()    {}
func (*GetAttitudeUserListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{61}
}
func (m *GetAttitudeUserListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAttitudeUserListReq.Unmarshal(m, b)
}
func (m *GetAttitudeUserListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAttitudeUserListReq.Marshal(b, m, deterministic)
}
func (dst *GetAttitudeUserListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAttitudeUserListReq.Merge(dst, src)
}
func (m *GetAttitudeUserListReq) XXX_Size() int {
	return xxx_messageInfo_GetAttitudeUserListReq.Size(m)
}
func (m *GetAttitudeUserListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAttitudeUserListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAttitudeUserListReq proto.InternalMessageInfo

func (m *GetAttitudeUserListReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetAttitudeUserListReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

type GetAttitudeUserListResp struct {
	AttitudeUserList     []*AttitudeUserInfo `protobuf:"bytes,1,rep,name=attitude_user_list,json=attitudeUserList,proto3" json:"attitude_user_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetAttitudeUserListResp) Reset()         { *m = GetAttitudeUserListResp{} }
func (m *GetAttitudeUserListResp) String() string { return proto.CompactTextString(m) }
func (*GetAttitudeUserListResp) ProtoMessage()    {}
func (*GetAttitudeUserListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{62}
}
func (m *GetAttitudeUserListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAttitudeUserListResp.Unmarshal(m, b)
}
func (m *GetAttitudeUserListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAttitudeUserListResp.Marshal(b, m, deterministic)
}
func (dst *GetAttitudeUserListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAttitudeUserListResp.Merge(dst, src)
}
func (m *GetAttitudeUserListResp) XXX_Size() int {
	return xxx_messageInfo_GetAttitudeUserListResp.Size(m)
}
func (m *GetAttitudeUserListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAttitudeUserListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAttitudeUserListResp proto.InternalMessageInfo

func (m *GetAttitudeUserListResp) GetAttitudeUserList() []*AttitudeUserInfo {
	if m != nil {
		return m.AttitudeUserList
	}
	return nil
}

type GetUserWonAttitudeCountReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserWonAttitudeCountReq) Reset()         { *m = GetUserWonAttitudeCountReq{} }
func (m *GetUserWonAttitudeCountReq) String() string { return proto.CompactTextString(m) }
func (*GetUserWonAttitudeCountReq) ProtoMessage()    {}
func (*GetUserWonAttitudeCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{63}
}
func (m *GetUserWonAttitudeCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserWonAttitudeCountReq.Unmarshal(m, b)
}
func (m *GetUserWonAttitudeCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserWonAttitudeCountReq.Marshal(b, m, deterministic)
}
func (dst *GetUserWonAttitudeCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserWonAttitudeCountReq.Merge(dst, src)
}
func (m *GetUserWonAttitudeCountReq) XXX_Size() int {
	return xxx_messageInfo_GetUserWonAttitudeCountReq.Size(m)
}
func (m *GetUserWonAttitudeCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserWonAttitudeCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserWonAttitudeCountReq proto.InternalMessageInfo

func (m *GetUserWonAttitudeCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserWonAttitudeCountResp struct {
	Total                uint32   `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserWonAttitudeCountResp) Reset()         { *m = GetUserWonAttitudeCountResp{} }
func (m *GetUserWonAttitudeCountResp) String() string { return proto.CompactTextString(m) }
func (*GetUserWonAttitudeCountResp) ProtoMessage()    {}
func (*GetUserWonAttitudeCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{64}
}
func (m *GetUserWonAttitudeCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserWonAttitudeCountResp.Unmarshal(m, b)
}
func (m *GetUserWonAttitudeCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserWonAttitudeCountResp.Marshal(b, m, deterministic)
}
func (dst *GetUserWonAttitudeCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserWonAttitudeCountResp.Merge(dst, src)
}
func (m *GetUserWonAttitudeCountResp) XXX_Size() int {
	return xxx_messageInfo_GetUserWonAttitudeCountResp.Size(m)
}
func (m *GetUserWonAttitudeCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserWonAttitudeCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserWonAttitudeCountResp proto.InternalMessageInfo

func (m *GetUserWonAttitudeCountResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 查看用户是否可以点赞
type CheckUserAttitudeAvailableReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserAttitudeAvailableReq) Reset()         { *m = CheckUserAttitudeAvailableReq{} }
func (m *CheckUserAttitudeAvailableReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserAttitudeAvailableReq) ProtoMessage()    {}
func (*CheckUserAttitudeAvailableReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{65}
}
func (m *CheckUserAttitudeAvailableReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserAttitudeAvailableReq.Unmarshal(m, b)
}
func (m *CheckUserAttitudeAvailableReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserAttitudeAvailableReq.Marshal(b, m, deterministic)
}
func (dst *CheckUserAttitudeAvailableReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserAttitudeAvailableReq.Merge(dst, src)
}
func (m *CheckUserAttitudeAvailableReq) XXX_Size() int {
	return xxx_messageInfo_CheckUserAttitudeAvailableReq.Size(m)
}
func (m *CheckUserAttitudeAvailableReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserAttitudeAvailableReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserAttitudeAvailableReq proto.InternalMessageInfo

func (m *CheckUserAttitudeAvailableReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckUserAttitudeAvailableResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserAttitudeAvailableResp) Reset()         { *m = CheckUserAttitudeAvailableResp{} }
func (m *CheckUserAttitudeAvailableResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserAttitudeAvailableResp) ProtoMessage()    {}
func (*CheckUserAttitudeAvailableResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{66}
}
func (m *CheckUserAttitudeAvailableResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserAttitudeAvailableResp.Unmarshal(m, b)
}
func (m *CheckUserAttitudeAvailableResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserAttitudeAvailableResp.Marshal(b, m, deterministic)
}
func (dst *CheckUserAttitudeAvailableResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserAttitudeAvailableResp.Merge(dst, src)
}
func (m *CheckUserAttitudeAvailableResp) XXX_Size() int {
	return xxx_messageInfo_CheckUserAttitudeAvailableResp.Size(m)
}
func (m *CheckUserAttitudeAvailableResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserAttitudeAvailableResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserAttitudeAvailableResp proto.InternalMessageInfo

// 举报
type AppReportReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string   `protobuf:"bytes,2,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	FromUserId           uint32   `protobuf:"varint,3,opt,name=from_user_id,json=fromUserId,proto3" json:"from_user_id,omitempty"`
	ReportType           uint32   `protobuf:"varint,4,opt,name=report_type,json=reportType,proto3" json:"report_type,omitempty"`
	Content              string   `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AppReportReq) Reset()         { *m = AppReportReq{} }
func (m *AppReportReq) String() string { return proto.CompactTextString(m) }
func (*AppReportReq) ProtoMessage()    {}
func (*AppReportReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{67}
}
func (m *AppReportReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AppReportReq.Unmarshal(m, b)
}
func (m *AppReportReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AppReportReq.Marshal(b, m, deterministic)
}
func (dst *AppReportReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppReportReq.Merge(dst, src)
}
func (m *AppReportReq) XXX_Size() int {
	return xxx_messageInfo_AppReportReq.Size(m)
}
func (m *AppReportReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AppReportReq.DiscardUnknown(m)
}

var xxx_messageInfo_AppReportReq proto.InternalMessageInfo

func (m *AppReportReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *AppReportReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *AppReportReq) GetFromUserId() uint32 {
	if m != nil {
		return m.FromUserId
	}
	return 0
}

func (m *AppReportReq) GetReportType() uint32 {
	if m != nil {
		return m.ReportType
	}
	return 0
}

func (m *AppReportReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type AppReportResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AppReportResp) Reset()         { *m = AppReportResp{} }
func (m *AppReportResp) String() string { return proto.CompactTextString(m) }
func (*AppReportResp) ProtoMessage()    {}
func (*AppReportResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{68}
}
func (m *AppReportResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AppReportResp.Unmarshal(m, b)
}
func (m *AppReportResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AppReportResp.Marshal(b, m, deterministic)
}
func (dst *AppReportResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppReportResp.Merge(dst, src)
}
func (m *AppReportResp) XXX_Size() int {
	return xxx_messageInfo_AppReportResp.Size(m)
}
func (m *AppReportResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AppReportResp.DiscardUnknown(m)
}

var xxx_messageInfo_AppReportResp proto.InternalMessageInfo

// 可能是给推荐系统打标签
type UpdatePostTagsReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Tags                 []uint32 `protobuf:"varint,2,rep,packed,name=tags,proto3" json:"tags,omitempty"`
	UserId               uint32   `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePostTagsReq) Reset()         { *m = UpdatePostTagsReq{} }
func (m *UpdatePostTagsReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePostTagsReq) ProtoMessage()    {}
func (*UpdatePostTagsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{69}
}
func (m *UpdatePostTagsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePostTagsReq.Unmarshal(m, b)
}
func (m *UpdatePostTagsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePostTagsReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePostTagsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePostTagsReq.Merge(dst, src)
}
func (m *UpdatePostTagsReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePostTagsReq.Size(m)
}
func (m *UpdatePostTagsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePostTagsReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePostTagsReq proto.InternalMessageInfo

func (m *UpdatePostTagsReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UpdatePostTagsReq) GetTags() []uint32 {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *UpdatePostTagsReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

type UpdatePostTagsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePostTagsResp) Reset()         { *m = UpdatePostTagsResp{} }
func (m *UpdatePostTagsResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePostTagsResp) ProtoMessage()    {}
func (*UpdatePostTagsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{70}
}
func (m *UpdatePostTagsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePostTagsResp.Unmarshal(m, b)
}
func (m *UpdatePostTagsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePostTagsResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePostTagsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePostTagsResp.Merge(dst, src)
}
func (m *UpdatePostTagsResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePostTagsResp.Size(m)
}
func (m *UpdatePostTagsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePostTagsResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePostTagsResp proto.InternalMessageInfo

// 根据条件查帖子列表
type GetPostsByFilterReq struct {
	Limit       uint32              `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"`
	LoadMore    string              `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	PostTypes   []PostInfo_PostType `protobuf:"varint,7,rep,packed,name=post_types,json=postTypes,proto3,enum=ugc.content.PostInfo_PostType" json:"post_types,omitempty"`
	ContentType ContentType         `protobuf:"varint,8,opt,name=content_type,json=contentType,proto3,enum=ugc.content.ContentType" json:"content_type,omitempty"`
	// 下面开始是过滤条件
	UserId uint32 `protobuf:"varint,11,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// *
	// 按圈子过滤；
	// Magic strings:
	// "all": 所有圈子（等同于空值）
	// "exsits": 包含圈子
	// "non-exists": 不包含圈子
	TopicId       string   `protobuf:"bytes,12,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	CreateBegin   int64    `protobuf:"varint,13,opt,name=create_begin,json=createBegin,proto3" json:"create_begin,omitempty"`
	CreateEnd     int64    `protobuf:"varint,14,opt,name=create_end,json=createEnd,proto3" json:"create_end,omitempty"`
	UseTagsFilter bool     `protobuf:"varint,15,opt,name=use_tags_filter,json=useTagsFilter,proto3" json:"use_tags_filter,omitempty"`
	Tags          []uint32 `protobuf:"varint,16,rep,packed,name=tags,proto3" json:"tags,omitempty"`
	// *
	// 按话题过滤；
	// Magic strings:
	// "all": 所有话题（等同于空值）
	// "exsits": 包含话题
	// "non-exists": 不包含话题
	SubTopicId string `protobuf:"bytes,17,opt,name=sub_topic_id,json=subTopicId,proto3" json:"sub_topic_id,omitempty"`
	// *
	// 按特殊标识过滤
	// Magic strings:
	// "all": 所有特殊标识（等同于空值）
	// "exists": 已配置特殊标识
	// "non-exists": 未配置特殊标识
	Label string `protobuf:"bytes,18,opt,name=label,proto3" json:"label,omitempty"`
	// *
	// 按发帖人过滤
	OwnerUidList []uint32 `protobuf:"varint,19,rep,packed,name=owner_uid_list,json=ownerUidList,proto3" json:"owner_uid_list,omitempty"`
	// 帖子状态
	PostStatus           string   `protobuf:"bytes,20,opt,name=post_status,json=postStatus,proto3" json:"post_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPostsByFilterReq) Reset()         { *m = GetPostsByFilterReq{} }
func (m *GetPostsByFilterReq) String() string { return proto.CompactTextString(m) }
func (*GetPostsByFilterReq) ProtoMessage()    {}
func (*GetPostsByFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{71}
}
func (m *GetPostsByFilterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostsByFilterReq.Unmarshal(m, b)
}
func (m *GetPostsByFilterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostsByFilterReq.Marshal(b, m, deterministic)
}
func (dst *GetPostsByFilterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostsByFilterReq.Merge(dst, src)
}
func (m *GetPostsByFilterReq) XXX_Size() int {
	return xxx_messageInfo_GetPostsByFilterReq.Size(m)
}
func (m *GetPostsByFilterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostsByFilterReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostsByFilterReq proto.InternalMessageInfo

func (m *GetPostsByFilterReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetPostsByFilterReq) GetLoadMore() string {
	if m != nil {
		return m.LoadMore
	}
	return ""
}

func (m *GetPostsByFilterReq) GetPostTypes() []PostInfo_PostType {
	if m != nil {
		return m.PostTypes
	}
	return nil
}

func (m *GetPostsByFilterReq) GetContentType() ContentType {
	if m != nil {
		return m.ContentType
	}
	return ContentType_FORMATTED
}

func (m *GetPostsByFilterReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *GetPostsByFilterReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *GetPostsByFilterReq) GetCreateBegin() int64 {
	if m != nil {
		return m.CreateBegin
	}
	return 0
}

func (m *GetPostsByFilterReq) GetCreateEnd() int64 {
	if m != nil {
		return m.CreateEnd
	}
	return 0
}

func (m *GetPostsByFilterReq) GetUseTagsFilter() bool {
	if m != nil {
		return m.UseTagsFilter
	}
	return false
}

func (m *GetPostsByFilterReq) GetTags() []uint32 {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *GetPostsByFilterReq) GetSubTopicId() string {
	if m != nil {
		return m.SubTopicId
	}
	return ""
}

func (m *GetPostsByFilterReq) GetLabel() string {
	if m != nil {
		return m.Label
	}
	return ""
}

func (m *GetPostsByFilterReq) GetOwnerUidList() []uint32 {
	if m != nil {
		return m.OwnerUidList
	}
	return nil
}

func (m *GetPostsByFilterReq) GetPostStatus() string {
	if m != nil {
		return m.PostStatus
	}
	return ""
}

type GetPostsByFilterResp struct {
	LoadMore             string      `protobuf:"bytes,1,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	PostList             []*PostInfo `protobuf:"bytes,2,rep,name=post_list,json=postList,proto3" json:"post_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetPostsByFilterResp) Reset()         { *m = GetPostsByFilterResp{} }
func (m *GetPostsByFilterResp) String() string { return proto.CompactTextString(m) }
func (*GetPostsByFilterResp) ProtoMessage()    {}
func (*GetPostsByFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{72}
}
func (m *GetPostsByFilterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostsByFilterResp.Unmarshal(m, b)
}
func (m *GetPostsByFilterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostsByFilterResp.Marshal(b, m, deterministic)
}
func (dst *GetPostsByFilterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostsByFilterResp.Merge(dst, src)
}
func (m *GetPostsByFilterResp) XXX_Size() int {
	return xxx_messageInfo_GetPostsByFilterResp.Size(m)
}
func (m *GetPostsByFilterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostsByFilterResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostsByFilterResp proto.InternalMessageInfo

func (m *GetPostsByFilterResp) GetLoadMore() string {
	if m != nil {
		return m.LoadMore
	}
	return ""
}

func (m *GetPostsByFilterResp) GetPostList() []*PostInfo {
	if m != nil {
		return m.PostList
	}
	return nil
}

// 获取帖子标签
type GetPostTagsReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPostTagsReq) Reset()         { *m = GetPostTagsReq{} }
func (m *GetPostTagsReq) String() string { return proto.CompactTextString(m) }
func (*GetPostTagsReq) ProtoMessage()    {}
func (*GetPostTagsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{73}
}
func (m *GetPostTagsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostTagsReq.Unmarshal(m, b)
}
func (m *GetPostTagsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostTagsReq.Marshal(b, m, deterministic)
}
func (dst *GetPostTagsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostTagsReq.Merge(dst, src)
}
func (m *GetPostTagsReq) XXX_Size() int {
	return xxx_messageInfo_GetPostTagsReq.Size(m)
}
func (m *GetPostTagsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostTagsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostTagsReq proto.InternalMessageInfo

func (m *GetPostTagsReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type GetPostTagsResp struct {
	Tags                 []uint32 `protobuf:"varint,1,rep,packed,name=tags,proto3" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPostTagsResp) Reset()         { *m = GetPostTagsResp{} }
func (m *GetPostTagsResp) String() string { return proto.CompactTextString(m) }
func (*GetPostTagsResp) ProtoMessage()    {}
func (*GetPostTagsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{74}
}
func (m *GetPostTagsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostTagsResp.Unmarshal(m, b)
}
func (m *GetPostTagsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostTagsResp.Marshal(b, m, deterministic)
}
func (dst *GetPostTagsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostTagsResp.Merge(dst, src)
}
func (m *GetPostTagsResp) XXX_Size() int {
	return xxx_messageInfo_GetPostTagsResp.Size(m)
}
func (m *GetPostTagsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostTagsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostTagsResp proto.InternalMessageInfo

func (m *GetPostTagsResp) GetTags() []uint32 {
	if m != nil {
		return m.Tags
	}
	return nil
}

type PostTag struct {
	Tags                 []uint32 `protobuf:"varint,1,rep,packed,name=tags,proto3" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostTag) Reset()         { *m = PostTag{} }
func (m *PostTag) String() string { return proto.CompactTextString(m) }
func (*PostTag) ProtoMessage()    {}
func (*PostTag) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{75}
}
func (m *PostTag) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostTag.Unmarshal(m, b)
}
func (m *PostTag) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostTag.Marshal(b, m, deterministic)
}
func (dst *PostTag) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostTag.Merge(dst, src)
}
func (m *PostTag) XXX_Size() int {
	return xxx_messageInfo_PostTag.Size(m)
}
func (m *PostTag) XXX_DiscardUnknown() {
	xxx_messageInfo_PostTag.DiscardUnknown(m)
}

var xxx_messageInfo_PostTag proto.InternalMessageInfo

func (m *PostTag) GetTags() []uint32 {
	if m != nil {
		return m.Tags
	}
	return nil
}

type BatchGetPostTagsReq struct {
	PostIdList           []string `protobuf:"bytes,1,rep,name=post_id_list,json=postIdList,proto3" json:"post_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetPostTagsReq) Reset()         { *m = BatchGetPostTagsReq{} }
func (m *BatchGetPostTagsReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetPostTagsReq) ProtoMessage()    {}
func (*BatchGetPostTagsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{76}
}
func (m *BatchGetPostTagsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetPostTagsReq.Unmarshal(m, b)
}
func (m *BatchGetPostTagsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetPostTagsReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetPostTagsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetPostTagsReq.Merge(dst, src)
}
func (m *BatchGetPostTagsReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetPostTagsReq.Size(m)
}
func (m *BatchGetPostTagsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetPostTagsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetPostTagsReq proto.InternalMessageInfo

func (m *BatchGetPostTagsReq) GetPostIdList() []string {
	if m != nil {
		return m.PostIdList
	}
	return nil
}

type BatchGetPostTagsResp struct {
	Tags                 map[string]*PostTag `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BatchGetPostTagsResp) Reset()         { *m = BatchGetPostTagsResp{} }
func (m *BatchGetPostTagsResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetPostTagsResp) ProtoMessage()    {}
func (*BatchGetPostTagsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{77}
}
func (m *BatchGetPostTagsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetPostTagsResp.Unmarshal(m, b)
}
func (m *BatchGetPostTagsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetPostTagsResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetPostTagsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetPostTagsResp.Merge(dst, src)
}
func (m *BatchGetPostTagsResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetPostTagsResp.Size(m)
}
func (m *BatchGetPostTagsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetPostTagsResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetPostTagsResp proto.InternalMessageInfo

func (m *BatchGetPostTagsResp) GetTags() map[string]*PostTag {
	if m != nil {
		return m.Tags
	}
	return nil
}

type MarkFirstPostReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkFirstPostReq) Reset()         { *m = MarkFirstPostReq{} }
func (m *MarkFirstPostReq) String() string { return proto.CompactTextString(m) }
func (*MarkFirstPostReq) ProtoMessage()    {}
func (*MarkFirstPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{78}
}
func (m *MarkFirstPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkFirstPostReq.Unmarshal(m, b)
}
func (m *MarkFirstPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkFirstPostReq.Marshal(b, m, deterministic)
}
func (dst *MarkFirstPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkFirstPostReq.Merge(dst, src)
}
func (m *MarkFirstPostReq) XXX_Size() int {
	return xxx_messageInfo_MarkFirstPostReq.Size(m)
}
func (m *MarkFirstPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkFirstPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarkFirstPostReq proto.InternalMessageInfo

func (m *MarkFirstPostReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *MarkFirstPostReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type MarkFirstPostResp struct {
	FirstPostId          string   `protobuf:"bytes,1,opt,name=first_post_id,json=firstPostId,proto3" json:"first_post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkFirstPostResp) Reset()         { *m = MarkFirstPostResp{} }
func (m *MarkFirstPostResp) String() string { return proto.CompactTextString(m) }
func (*MarkFirstPostResp) ProtoMessage()    {}
func (*MarkFirstPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{79}
}
func (m *MarkFirstPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkFirstPostResp.Unmarshal(m, b)
}
func (m *MarkFirstPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkFirstPostResp.Marshal(b, m, deterministic)
}
func (dst *MarkFirstPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkFirstPostResp.Merge(dst, src)
}
func (m *MarkFirstPostResp) XXX_Size() int {
	return xxx_messageInfo_MarkFirstPostResp.Size(m)
}
func (m *MarkFirstPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkFirstPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_MarkFirstPostResp proto.InternalMessageInfo

func (m *MarkFirstPostResp) GetFirstPostId() string {
	if m != nil {
		return m.FirstPostId
	}
	return ""
}

// 更新帖子隐私策略
type UpdatePostPrivacyPolicyReq struct {
	UserId               uint32            `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PostId               string            `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Policy               PostPrivacyPolicy `protobuf:"varint,3,opt,name=policy,proto3,enum=ugc.content.PostPrivacyPolicy" json:"policy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UpdatePostPrivacyPolicyReq) Reset()         { *m = UpdatePostPrivacyPolicyReq{} }
func (m *UpdatePostPrivacyPolicyReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePostPrivacyPolicyReq) ProtoMessage()    {}
func (*UpdatePostPrivacyPolicyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{80}
}
func (m *UpdatePostPrivacyPolicyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePostPrivacyPolicyReq.Unmarshal(m, b)
}
func (m *UpdatePostPrivacyPolicyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePostPrivacyPolicyReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePostPrivacyPolicyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePostPrivacyPolicyReq.Merge(dst, src)
}
func (m *UpdatePostPrivacyPolicyReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePostPrivacyPolicyReq.Size(m)
}
func (m *UpdatePostPrivacyPolicyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePostPrivacyPolicyReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePostPrivacyPolicyReq proto.InternalMessageInfo

func (m *UpdatePostPrivacyPolicyReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *UpdatePostPrivacyPolicyReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UpdatePostPrivacyPolicyReq) GetPolicy() PostPrivacyPolicy {
	if m != nil {
		return m.Policy
	}
	return PostPrivacyPolicy_POST_PRIVACY_POLICY_DEFAULT
}

type UpdatePostPrivacyPolicyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePostPrivacyPolicyResp) Reset()         { *m = UpdatePostPrivacyPolicyResp{} }
func (m *UpdatePostPrivacyPolicyResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePostPrivacyPolicyResp) ProtoMessage()    {}
func (*UpdatePostPrivacyPolicyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{81}
}
func (m *UpdatePostPrivacyPolicyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePostPrivacyPolicyResp.Unmarshal(m, b)
}
func (m *UpdatePostPrivacyPolicyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePostPrivacyPolicyResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePostPrivacyPolicyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePostPrivacyPolicyResp.Merge(dst, src)
}
func (m *UpdatePostPrivacyPolicyResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePostPrivacyPolicyResp.Size(m)
}
func (m *UpdatePostPrivacyPolicyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePostPrivacyPolicyResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePostPrivacyPolicyResp proto.InternalMessageInfo

// 获取精确帖子数量
type GetStrictUserPostedCountReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStrictUserPostedCountReq) Reset()         { *m = GetStrictUserPostedCountReq{} }
func (m *GetStrictUserPostedCountReq) String() string { return proto.CompactTextString(m) }
func (*GetStrictUserPostedCountReq) ProtoMessage()    {}
func (*GetStrictUserPostedCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{82}
}
func (m *GetStrictUserPostedCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStrictUserPostedCountReq.Unmarshal(m, b)
}
func (m *GetStrictUserPostedCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStrictUserPostedCountReq.Marshal(b, m, deterministic)
}
func (dst *GetStrictUserPostedCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStrictUserPostedCountReq.Merge(dst, src)
}
func (m *GetStrictUserPostedCountReq) XXX_Size() int {
	return xxx_messageInfo_GetStrictUserPostedCountReq.Size(m)
}
func (m *GetStrictUserPostedCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStrictUserPostedCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStrictUserPostedCountReq proto.InternalMessageInfo

func (m *GetStrictUserPostedCountReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

type GetStrictUserPostedCountResp struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	PrivateCount         uint32   `protobuf:"varint,2,opt,name=private_count,json=privateCount,proto3" json:"private_count,omitempty"`
	NormalCount          uint32   `protobuf:"varint,3,opt,name=normal_count,json=normalCount,proto3" json:"normal_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStrictUserPostedCountResp) Reset()         { *m = GetStrictUserPostedCountResp{} }
func (m *GetStrictUserPostedCountResp) String() string { return proto.CompactTextString(m) }
func (*GetStrictUserPostedCountResp) ProtoMessage()    {}
func (*GetStrictUserPostedCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{83}
}
func (m *GetStrictUserPostedCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStrictUserPostedCountResp.Unmarshal(m, b)
}
func (m *GetStrictUserPostedCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStrictUserPostedCountResp.Marshal(b, m, deterministic)
}
func (dst *GetStrictUserPostedCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStrictUserPostedCountResp.Merge(dst, src)
}
func (m *GetStrictUserPostedCountResp) XXX_Size() int {
	return xxx_messageInfo_GetStrictUserPostedCountResp.Size(m)
}
func (m *GetStrictUserPostedCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStrictUserPostedCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStrictUserPostedCountResp proto.InternalMessageInfo

func (m *GetStrictUserPostedCountResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetStrictUserPostedCountResp) GetPrivateCount() uint32 {
	if m != nil {
		return m.PrivateCount
	}
	return 0
}

func (m *GetStrictUserPostedCountResp) GetNormalCount() uint32 {
	if m != nil {
		return m.NormalCount
	}
	return 0
}

// 缓存用户最新帖子
type SetUserNewestPostReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Expiration           int64    `protobuf:"varint,3,opt,name=expiration,proto3" json:"expiration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserNewestPostReq) Reset()         { *m = SetUserNewestPostReq{} }
func (m *SetUserNewestPostReq) String() string { return proto.CompactTextString(m) }
func (*SetUserNewestPostReq) ProtoMessage()    {}
func (*SetUserNewestPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{84}
}
func (m *SetUserNewestPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserNewestPostReq.Unmarshal(m, b)
}
func (m *SetUserNewestPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserNewestPostReq.Marshal(b, m, deterministic)
}
func (dst *SetUserNewestPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserNewestPostReq.Merge(dst, src)
}
func (m *SetUserNewestPostReq) XXX_Size() int {
	return xxx_messageInfo_SetUserNewestPostReq.Size(m)
}
func (m *SetUserNewestPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserNewestPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserNewestPostReq proto.InternalMessageInfo

func (m *SetUserNewestPostReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserNewestPostReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *SetUserNewestPostReq) GetExpiration() int64 {
	if m != nil {
		return m.Expiration
	}
	return 0
}

type SetUserNewestPostResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserNewestPostResp) Reset()         { *m = SetUserNewestPostResp{} }
func (m *SetUserNewestPostResp) String() string { return proto.CompactTextString(m) }
func (*SetUserNewestPostResp) ProtoMessage()    {}
func (*SetUserNewestPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{85}
}
func (m *SetUserNewestPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserNewestPostResp.Unmarshal(m, b)
}
func (m *SetUserNewestPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserNewestPostResp.Marshal(b, m, deterministic)
}
func (dst *SetUserNewestPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserNewestPostResp.Merge(dst, src)
}
func (m *SetUserNewestPostResp) XXX_Size() int {
	return xxx_messageInfo_SetUserNewestPostResp.Size(m)
}
func (m *SetUserNewestPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserNewestPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserNewestPostResp proto.InternalMessageInfo

// 批量获取用户最新帖子
type BatchGetUserNewestPostReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserNewestPostReq) Reset()         { *m = BatchGetUserNewestPostReq{} }
func (m *BatchGetUserNewestPostReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserNewestPostReq) ProtoMessage()    {}
func (*BatchGetUserNewestPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{86}
}
func (m *BatchGetUserNewestPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserNewestPostReq.Unmarshal(m, b)
}
func (m *BatchGetUserNewestPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserNewestPostReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserNewestPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserNewestPostReq.Merge(dst, src)
}
func (m *BatchGetUserNewestPostReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserNewestPostReq.Size(m)
}
func (m *BatchGetUserNewestPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserNewestPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserNewestPostReq proto.InternalMessageInfo

func (m *BatchGetUserNewestPostReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type BatchGetUserNewestPostResp struct {
	PostMap              map[uint32]string `protobuf:"bytes,1,rep,name=post_map,json=postMap,proto3" json:"post_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetUserNewestPostResp) Reset()         { *m = BatchGetUserNewestPostResp{} }
func (m *BatchGetUserNewestPostResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserNewestPostResp) ProtoMessage()    {}
func (*BatchGetUserNewestPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{87}
}
func (m *BatchGetUserNewestPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserNewestPostResp.Unmarshal(m, b)
}
func (m *BatchGetUserNewestPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserNewestPostResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserNewestPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserNewestPostResp.Merge(dst, src)
}
func (m *BatchGetUserNewestPostResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserNewestPostResp.Size(m)
}
func (m *BatchGetUserNewestPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserNewestPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserNewestPostResp proto.InternalMessageInfo

func (m *BatchGetUserNewestPostResp) GetPostMap() map[uint32]string {
	if m != nil {
		return m.PostMap
	}
	return nil
}

type GetCommentsByFilterReq struct {
	Limit       uint32      `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"`
	LoadMore    string      `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	ContentType ContentType `protobuf:"varint,8,opt,name=content_type,json=contentType,proto3,enum=ugc.content.ContentType" json:"content_type,omitempty"`
	// 下面开始是过滤条件
	UserId               uint32   `protobuf:"varint,11,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PostId               string   `protobuf:"bytes,12,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CreateBegin          int64    `protobuf:"varint,13,opt,name=create_begin,json=createBegin,proto3" json:"create_begin,omitempty"`
	CreateEnd            int64    `protobuf:"varint,14,opt,name=create_end,json=createEnd,proto3" json:"create_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCommentsByFilterReq) Reset()         { *m = GetCommentsByFilterReq{} }
func (m *GetCommentsByFilterReq) String() string { return proto.CompactTextString(m) }
func (*GetCommentsByFilterReq) ProtoMessage()    {}
func (*GetCommentsByFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{88}
}
func (m *GetCommentsByFilterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommentsByFilterReq.Unmarshal(m, b)
}
func (m *GetCommentsByFilterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommentsByFilterReq.Marshal(b, m, deterministic)
}
func (dst *GetCommentsByFilterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommentsByFilterReq.Merge(dst, src)
}
func (m *GetCommentsByFilterReq) XXX_Size() int {
	return xxx_messageInfo_GetCommentsByFilterReq.Size(m)
}
func (m *GetCommentsByFilterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommentsByFilterReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommentsByFilterReq proto.InternalMessageInfo

func (m *GetCommentsByFilterReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetCommentsByFilterReq) GetLoadMore() string {
	if m != nil {
		return m.LoadMore
	}
	return ""
}

func (m *GetCommentsByFilterReq) GetContentType() ContentType {
	if m != nil {
		return m.ContentType
	}
	return ContentType_FORMATTED
}

func (m *GetCommentsByFilterReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *GetCommentsByFilterReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetCommentsByFilterReq) GetCreateBegin() int64 {
	if m != nil {
		return m.CreateBegin
	}
	return 0
}

func (m *GetCommentsByFilterReq) GetCreateEnd() int64 {
	if m != nil {
		return m.CreateEnd
	}
	return 0
}

type GetCommentsByFilterResp struct {
	LoadMore             string         `protobuf:"bytes,1,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	CommentList          []*CommentInfo `protobuf:"bytes,2,rep,name=comment_list,json=commentList,proto3" json:"comment_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetCommentsByFilterResp) Reset()         { *m = GetCommentsByFilterResp{} }
func (m *GetCommentsByFilterResp) String() string { return proto.CompactTextString(m) }
func (*GetCommentsByFilterResp) ProtoMessage()    {}
func (*GetCommentsByFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{89}
}
func (m *GetCommentsByFilterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommentsByFilterResp.Unmarshal(m, b)
}
func (m *GetCommentsByFilterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommentsByFilterResp.Marshal(b, m, deterministic)
}
func (dst *GetCommentsByFilterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommentsByFilterResp.Merge(dst, src)
}
func (m *GetCommentsByFilterResp) XXX_Size() int {
	return xxx_messageInfo_GetCommentsByFilterResp.Size(m)
}
func (m *GetCommentsByFilterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommentsByFilterResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommentsByFilterResp proto.InternalMessageInfo

func (m *GetCommentsByFilterResp) GetLoadMore() string {
	if m != nil {
		return m.LoadMore
	}
	return ""
}

func (m *GetCommentsByFilterResp) GetCommentList() []*CommentInfo {
	if m != nil {
		return m.CommentList
	}
	return nil
}

type UpdateContentStickyStatusReq struct {
	UserId               uint32       `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PostId               string       `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string       `protobuf:"bytes,3,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	Status               StickyStatus `protobuf:"varint,4,opt,name=status,proto3,enum=ugc.content.StickyStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpdateContentStickyStatusReq) Reset()         { *m = UpdateContentStickyStatusReq{} }
func (m *UpdateContentStickyStatusReq) String() string { return proto.CompactTextString(m) }
func (*UpdateContentStickyStatusReq) ProtoMessage()    {}
func (*UpdateContentStickyStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{90}
}
func (m *UpdateContentStickyStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateContentStickyStatusReq.Unmarshal(m, b)
}
func (m *UpdateContentStickyStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateContentStickyStatusReq.Marshal(b, m, deterministic)
}
func (dst *UpdateContentStickyStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateContentStickyStatusReq.Merge(dst, src)
}
func (m *UpdateContentStickyStatusReq) XXX_Size() int {
	return xxx_messageInfo_UpdateContentStickyStatusReq.Size(m)
}
func (m *UpdateContentStickyStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateContentStickyStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateContentStickyStatusReq proto.InternalMessageInfo

func (m *UpdateContentStickyStatusReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *UpdateContentStickyStatusReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UpdateContentStickyStatusReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *UpdateContentStickyStatusReq) GetStatus() StickyStatus {
	if m != nil {
		return m.Status
	}
	return StickyStatus_STICKY_NONE
}

type UpdateContentStickyStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateContentStickyStatusResp) Reset()         { *m = UpdateContentStickyStatusResp{} }
func (m *UpdateContentStickyStatusResp) String() string { return proto.CompactTextString(m) }
func (*UpdateContentStickyStatusResp) ProtoMessage()    {}
func (*UpdateContentStickyStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{91}
}
func (m *UpdateContentStickyStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateContentStickyStatusResp.Unmarshal(m, b)
}
func (m *UpdateContentStickyStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateContentStickyStatusResp.Marshal(b, m, deterministic)
}
func (dst *UpdateContentStickyStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateContentStickyStatusResp.Merge(dst, src)
}
func (m *UpdateContentStickyStatusResp) XXX_Size() int {
	return xxx_messageInfo_UpdateContentStickyStatusResp.Size(m)
}
func (m *UpdateContentStickyStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateContentStickyStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateContentStickyStatusResp proto.InternalMessageInfo

type UpdateAttachmentPrivacyReq struct {
	UserId               uint32                    `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PostId               string                    `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string                    `protobuf:"bytes,3,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	Privacy              AttachmentDownloadPrivacy `protobuf:"varint,4,opt,name=privacy,proto3,enum=ugc.content.AttachmentDownloadPrivacy" json:"privacy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *UpdateAttachmentPrivacyReq) Reset()         { *m = UpdateAttachmentPrivacyReq{} }
func (m *UpdateAttachmentPrivacyReq) String() string { return proto.CompactTextString(m) }
func (*UpdateAttachmentPrivacyReq) ProtoMessage()    {}
func (*UpdateAttachmentPrivacyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{92}
}
func (m *UpdateAttachmentPrivacyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAttachmentPrivacyReq.Unmarshal(m, b)
}
func (m *UpdateAttachmentPrivacyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAttachmentPrivacyReq.Marshal(b, m, deterministic)
}
func (dst *UpdateAttachmentPrivacyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAttachmentPrivacyReq.Merge(dst, src)
}
func (m *UpdateAttachmentPrivacyReq) XXX_Size() int {
	return xxx_messageInfo_UpdateAttachmentPrivacyReq.Size(m)
}
func (m *UpdateAttachmentPrivacyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAttachmentPrivacyReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAttachmentPrivacyReq proto.InternalMessageInfo

func (m *UpdateAttachmentPrivacyReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *UpdateAttachmentPrivacyReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UpdateAttachmentPrivacyReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *UpdateAttachmentPrivacyReq) GetPrivacy() AttachmentDownloadPrivacy {
	if m != nil {
		return m.Privacy
	}
	return AttachmentDownloadPrivacy_PRIVACY_DEFAULT
}

type UpdateAttachmentPrivacyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAttachmentPrivacyResp) Reset()         { *m = UpdateAttachmentPrivacyResp{} }
func (m *UpdateAttachmentPrivacyResp) String() string { return proto.CompactTextString(m) }
func (*UpdateAttachmentPrivacyResp) ProtoMessage()    {}
func (*UpdateAttachmentPrivacyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{93}
}
func (m *UpdateAttachmentPrivacyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAttachmentPrivacyResp.Unmarshal(m, b)
}
func (m *UpdateAttachmentPrivacyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAttachmentPrivacyResp.Marshal(b, m, deterministic)
}
func (dst *UpdateAttachmentPrivacyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAttachmentPrivacyResp.Merge(dst, src)
}
func (m *UpdateAttachmentPrivacyResp) XXX_Size() int {
	return xxx_messageInfo_UpdateAttachmentPrivacyResp.Size(m)
}
func (m *UpdateAttachmentPrivacyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAttachmentPrivacyResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAttachmentPrivacyResp proto.InternalMessageInfo

type AddStickyContentReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ParentId             string   `protobuf:"bytes,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	TargetId             string   `protobuf:"bytes,3,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddStickyContentReq) Reset()         { *m = AddStickyContentReq{} }
func (m *AddStickyContentReq) String() string { return proto.CompactTextString(m) }
func (*AddStickyContentReq) ProtoMessage()    {}
func (*AddStickyContentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{94}
}
func (m *AddStickyContentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddStickyContentReq.Unmarshal(m, b)
}
func (m *AddStickyContentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddStickyContentReq.Marshal(b, m, deterministic)
}
func (dst *AddStickyContentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddStickyContentReq.Merge(dst, src)
}
func (m *AddStickyContentReq) XXX_Size() int {
	return xxx_messageInfo_AddStickyContentReq.Size(m)
}
func (m *AddStickyContentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddStickyContentReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddStickyContentReq proto.InternalMessageInfo

func (m *AddStickyContentReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *AddStickyContentReq) GetParentId() string {
	if m != nil {
		return m.ParentId
	}
	return ""
}

func (m *AddStickyContentReq) GetTargetId() string {
	if m != nil {
		return m.TargetId
	}
	return ""
}

type AddStickyContentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddStickyContentResp) Reset()         { *m = AddStickyContentResp{} }
func (m *AddStickyContentResp) String() string { return proto.CompactTextString(m) }
func (*AddStickyContentResp) ProtoMessage()    {}
func (*AddStickyContentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{95}
}
func (m *AddStickyContentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddStickyContentResp.Unmarshal(m, b)
}
func (m *AddStickyContentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddStickyContentResp.Marshal(b, m, deterministic)
}
func (dst *AddStickyContentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddStickyContentResp.Merge(dst, src)
}
func (m *AddStickyContentResp) XXX_Size() int {
	return xxx_messageInfo_AddStickyContentResp.Size(m)
}
func (m *AddStickyContentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddStickyContentResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddStickyContentResp proto.InternalMessageInfo

type RemoveStickyContentReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ParentId             string   `protobuf:"bytes,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	TargetId             string   `protobuf:"bytes,3,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveStickyContentReq) Reset()         { *m = RemoveStickyContentReq{} }
func (m *RemoveStickyContentReq) String() string { return proto.CompactTextString(m) }
func (*RemoveStickyContentReq) ProtoMessage()    {}
func (*RemoveStickyContentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{96}
}
func (m *RemoveStickyContentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveStickyContentReq.Unmarshal(m, b)
}
func (m *RemoveStickyContentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveStickyContentReq.Marshal(b, m, deterministic)
}
func (dst *RemoveStickyContentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveStickyContentReq.Merge(dst, src)
}
func (m *RemoveStickyContentReq) XXX_Size() int {
	return xxx_messageInfo_RemoveStickyContentReq.Size(m)
}
func (m *RemoveStickyContentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveStickyContentReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveStickyContentReq proto.InternalMessageInfo

func (m *RemoveStickyContentReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *RemoveStickyContentReq) GetParentId() string {
	if m != nil {
		return m.ParentId
	}
	return ""
}

func (m *RemoveStickyContentReq) GetTargetId() string {
	if m != nil {
		return m.TargetId
	}
	return ""
}

type RemoveStickyContentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveStickyContentResp) Reset()         { *m = RemoveStickyContentResp{} }
func (m *RemoveStickyContentResp) String() string { return proto.CompactTextString(m) }
func (*RemoveStickyContentResp) ProtoMessage()    {}
func (*RemoveStickyContentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{97}
}
func (m *RemoveStickyContentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveStickyContentResp.Unmarshal(m, b)
}
func (m *RemoveStickyContentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveStickyContentResp.Marshal(b, m, deterministic)
}
func (dst *RemoveStickyContentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveStickyContentResp.Merge(dst, src)
}
func (m *RemoveStickyContentResp) XXX_Size() int {
	return xxx_messageInfo_RemoveStickyContentResp.Size(m)
}
func (m *RemoveStickyContentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveStickyContentResp.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveStickyContentResp proto.InternalMessageInfo

type GetStickyContentReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ParentId             string   `protobuf:"bytes,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStickyContentReq) Reset()         { *m = GetStickyContentReq{} }
func (m *GetStickyContentReq) String() string { return proto.CompactTextString(m) }
func (*GetStickyContentReq) ProtoMessage()    {}
func (*GetStickyContentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{98}
}
func (m *GetStickyContentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStickyContentReq.Unmarshal(m, b)
}
func (m *GetStickyContentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStickyContentReq.Marshal(b, m, deterministic)
}
func (dst *GetStickyContentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStickyContentReq.Merge(dst, src)
}
func (m *GetStickyContentReq) XXX_Size() int {
	return xxx_messageInfo_GetStickyContentReq.Size(m)
}
func (m *GetStickyContentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStickyContentReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStickyContentReq proto.InternalMessageInfo

func (m *GetStickyContentReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *GetStickyContentReq) GetParentId() string {
	if m != nil {
		return m.ParentId
	}
	return ""
}

type GetStickyContentResp struct {
	ParentId             string   `protobuf:"bytes,1,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	TargetIds            []string `protobuf:"bytes,2,rep,name=target_ids,json=targetIds,proto3" json:"target_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStickyContentResp) Reset()         { *m = GetStickyContentResp{} }
func (m *GetStickyContentResp) String() string { return proto.CompactTextString(m) }
func (*GetStickyContentResp) ProtoMessage()    {}
func (*GetStickyContentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{99}
}
func (m *GetStickyContentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStickyContentResp.Unmarshal(m, b)
}
func (m *GetStickyContentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStickyContentResp.Marshal(b, m, deterministic)
}
func (dst *GetStickyContentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStickyContentResp.Merge(dst, src)
}
func (m *GetStickyContentResp) XXX_Size() int {
	return xxx_messageInfo_GetStickyContentResp.Size(m)
}
func (m *GetStickyContentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStickyContentResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStickyContentResp proto.InternalMessageInfo

func (m *GetStickyContentResp) GetParentId() string {
	if m != nil {
		return m.ParentId
	}
	return ""
}

func (m *GetStickyContentResp) GetTargetIds() []string {
	if m != nil {
		return m.TargetIds
	}
	return nil
}

type StickyContent struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ParentId             string   `protobuf:"bytes,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	TargetIds            []string `protobuf:"bytes,3,rep,name=target_ids,json=targetIds,proto3" json:"target_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StickyContent) Reset()         { *m = StickyContent{} }
func (m *StickyContent) String() string { return proto.CompactTextString(m) }
func (*StickyContent) ProtoMessage()    {}
func (*StickyContent) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{100}
}
func (m *StickyContent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StickyContent.Unmarshal(m, b)
}
func (m *StickyContent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StickyContent.Marshal(b, m, deterministic)
}
func (dst *StickyContent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StickyContent.Merge(dst, src)
}
func (m *StickyContent) XXX_Size() int {
	return xxx_messageInfo_StickyContent.Size(m)
}
func (m *StickyContent) XXX_DiscardUnknown() {
	xxx_messageInfo_StickyContent.DiscardUnknown(m)
}

var xxx_messageInfo_StickyContent proto.InternalMessageInfo

func (m *StickyContent) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *StickyContent) GetParentId() string {
	if m != nil {
		return m.ParentId
	}
	return ""
}

func (m *StickyContent) GetTargetIds() []string {
	if m != nil {
		return m.TargetIds
	}
	return nil
}

type BatchGetStickyPostReq struct {
	UserIdList           []uint32 `protobuf:"varint,1,rep,packed,name=user_id_list,json=userIdList,proto3" json:"user_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetStickyPostReq) Reset()         { *m = BatchGetStickyPostReq{} }
func (m *BatchGetStickyPostReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetStickyPostReq) ProtoMessage()    {}
func (*BatchGetStickyPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{101}
}
func (m *BatchGetStickyPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetStickyPostReq.Unmarshal(m, b)
}
func (m *BatchGetStickyPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetStickyPostReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetStickyPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetStickyPostReq.Merge(dst, src)
}
func (m *BatchGetStickyPostReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetStickyPostReq.Size(m)
}
func (m *BatchGetStickyPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetStickyPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetStickyPostReq proto.InternalMessageInfo

func (m *BatchGetStickyPostReq) GetUserIdList() []uint32 {
	if m != nil {
		return m.UserIdList
	}
	return nil
}

type BatchGetStickyPostResp struct {
	StickyMap            map[uint32]*StickyContent `protobuf:"bytes,1,rep,name=sticky_map,json=stickyMap,proto3" json:"sticky_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *BatchGetStickyPostResp) Reset()         { *m = BatchGetStickyPostResp{} }
func (m *BatchGetStickyPostResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetStickyPostResp) ProtoMessage()    {}
func (*BatchGetStickyPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{102}
}
func (m *BatchGetStickyPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetStickyPostResp.Unmarshal(m, b)
}
func (m *BatchGetStickyPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetStickyPostResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetStickyPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetStickyPostResp.Merge(dst, src)
}
func (m *BatchGetStickyPostResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetStickyPostResp.Size(m)
}
func (m *BatchGetStickyPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetStickyPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetStickyPostResp proto.InternalMessageInfo

func (m *BatchGetStickyPostResp) GetStickyMap() map[uint32]*StickyContent {
	if m != nil {
		return m.StickyMap
	}
	return nil
}

// 根据浏览数来查
type GetPostsByTimeSortViewCntReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	QueryDay             int64    `protobuf:"varint,3,opt,name=query_day,json=queryDay,proto3" json:"query_day,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPostsByTimeSortViewCntReq) Reset()         { *m = GetPostsByTimeSortViewCntReq{} }
func (m *GetPostsByTimeSortViewCntReq) String() string { return proto.CompactTextString(m) }
func (*GetPostsByTimeSortViewCntReq) ProtoMessage()    {}
func (*GetPostsByTimeSortViewCntReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{103}
}
func (m *GetPostsByTimeSortViewCntReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostsByTimeSortViewCntReq.Unmarshal(m, b)
}
func (m *GetPostsByTimeSortViewCntReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostsByTimeSortViewCntReq.Marshal(b, m, deterministic)
}
func (dst *GetPostsByTimeSortViewCntReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostsByTimeSortViewCntReq.Merge(dst, src)
}
func (m *GetPostsByTimeSortViewCntReq) XXX_Size() int {
	return xxx_messageInfo_GetPostsByTimeSortViewCntReq.Size(m)
}
func (m *GetPostsByTimeSortViewCntReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostsByTimeSortViewCntReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostsByTimeSortViewCntReq proto.InternalMessageInfo

func (m *GetPostsByTimeSortViewCntReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetPostsByTimeSortViewCntReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetPostsByTimeSortViewCntReq) GetQueryDay() int64 {
	if m != nil {
		return m.QueryDay
	}
	return 0
}

type GetPostsByTimeSortViewCntResp struct {
	TotalCnt             uint32               `protobuf:"varint,1,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	PostList             []*PostWeightAllInfo `protobuf:"bytes,2,rep,name=post_list,json=postList,proto3" json:"post_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetPostsByTimeSortViewCntResp) Reset()         { *m = GetPostsByTimeSortViewCntResp{} }
func (m *GetPostsByTimeSortViewCntResp) String() string { return proto.CompactTextString(m) }
func (*GetPostsByTimeSortViewCntResp) ProtoMessage()    {}
func (*GetPostsByTimeSortViewCntResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{104}
}
func (m *GetPostsByTimeSortViewCntResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostsByTimeSortViewCntResp.Unmarshal(m, b)
}
func (m *GetPostsByTimeSortViewCntResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostsByTimeSortViewCntResp.Marshal(b, m, deterministic)
}
func (dst *GetPostsByTimeSortViewCntResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostsByTimeSortViewCntResp.Merge(dst, src)
}
func (m *GetPostsByTimeSortViewCntResp) XXX_Size() int {
	return xxx_messageInfo_GetPostsByTimeSortViewCntResp.Size(m)
}
func (m *GetPostsByTimeSortViewCntResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostsByTimeSortViewCntResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostsByTimeSortViewCntResp proto.InternalMessageInfo

func (m *GetPostsByTimeSortViewCntResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

func (m *GetPostsByTimeSortViewCntResp) GetPostList() []*PostWeightAllInfo {
	if m != nil {
		return m.PostList
	}
	return nil
}

type PostWeightInfo struct {
	HasWeight            bool         `protobuf:"varint,1,opt,name=has_weight,json=hasWeight,proto3" json:"has_weight,omitempty"`
	Weight               string       `protobuf:"bytes,2,opt,name=weight,proto3" json:"weight,omitempty"`
	WeightStartTs        uint32       `protobuf:"varint,3,opt,name=weight_start_ts,json=weightStartTs,proto3" json:"weight_start_ts,omitempty"`
	WeightEndTs          uint32       `protobuf:"varint,4,opt,name=weight_end_ts,json=weightEndTs,proto3" json:"weight_end_ts,omitempty"`
	WeightStatus         WeightStatus `protobuf:"varint,5,opt,name=weight_status,json=weightStatus,proto3,enum=ugc.content.WeightStatus" json:"weight_status,omitempty"`
	PostId               string       `protobuf:"bytes,6,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CreateTime           int64        `protobuf:"varint,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PostWeightInfo) Reset()         { *m = PostWeightInfo{} }
func (m *PostWeightInfo) String() string { return proto.CompactTextString(m) }
func (*PostWeightInfo) ProtoMessage()    {}
func (*PostWeightInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{105}
}
func (m *PostWeightInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostWeightInfo.Unmarshal(m, b)
}
func (m *PostWeightInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostWeightInfo.Marshal(b, m, deterministic)
}
func (dst *PostWeightInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostWeightInfo.Merge(dst, src)
}
func (m *PostWeightInfo) XXX_Size() int {
	return xxx_messageInfo_PostWeightInfo.Size(m)
}
func (m *PostWeightInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PostWeightInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PostWeightInfo proto.InternalMessageInfo

func (m *PostWeightInfo) GetHasWeight() bool {
	if m != nil {
		return m.HasWeight
	}
	return false
}

func (m *PostWeightInfo) GetWeight() string {
	if m != nil {
		return m.Weight
	}
	return ""
}

func (m *PostWeightInfo) GetWeightStartTs() uint32 {
	if m != nil {
		return m.WeightStartTs
	}
	return 0
}

func (m *PostWeightInfo) GetWeightEndTs() uint32 {
	if m != nil {
		return m.WeightEndTs
	}
	return 0
}

func (m *PostWeightInfo) GetWeightStatus() WeightStatus {
	if m != nil {
		return m.WeightStatus
	}
	return WeightStatus_WeiStDefault
}

func (m *PostWeightInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *PostWeightInfo) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

type UpdatePostWeightInfo struct {
	HasWeight            bool     `protobuf:"varint,1,opt,name=has_weight,json=hasWeight,proto3" json:"has_weight,omitempty"`
	Weight               string   `protobuf:"bytes,2,opt,name=weight,proto3" json:"weight,omitempty"`
	WeightStartTs        uint32   `protobuf:"varint,3,opt,name=weight_start_ts,json=weightStartTs,proto3" json:"weight_start_ts,omitempty"`
	WeightEndTs          uint32   `protobuf:"varint,4,opt,name=weight_end_ts,json=weightEndTs,proto3" json:"weight_end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePostWeightInfo) Reset()         { *m = UpdatePostWeightInfo{} }
func (m *UpdatePostWeightInfo) String() string { return proto.CompactTextString(m) }
func (*UpdatePostWeightInfo) ProtoMessage()    {}
func (*UpdatePostWeightInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{106}
}
func (m *UpdatePostWeightInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePostWeightInfo.Unmarshal(m, b)
}
func (m *UpdatePostWeightInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePostWeightInfo.Marshal(b, m, deterministic)
}
func (dst *UpdatePostWeightInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePostWeightInfo.Merge(dst, src)
}
func (m *UpdatePostWeightInfo) XXX_Size() int {
	return xxx_messageInfo_UpdatePostWeightInfo.Size(m)
}
func (m *UpdatePostWeightInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePostWeightInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePostWeightInfo proto.InternalMessageInfo

func (m *UpdatePostWeightInfo) GetHasWeight() bool {
	if m != nil {
		return m.HasWeight
	}
	return false
}

func (m *UpdatePostWeightInfo) GetWeight() string {
	if m != nil {
		return m.Weight
	}
	return ""
}

func (m *UpdatePostWeightInfo) GetWeightStartTs() uint32 {
	if m != nil {
		return m.WeightStartTs
	}
	return 0
}

func (m *UpdatePostWeightInfo) GetWeightEndTs() uint32 {
	if m != nil {
		return m.WeightEndTs
	}
	return 0
}

type StatusResult struct {
	PostId               string       `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	WeightStatus         WeightStatus `protobuf:"varint,2,opt,name=weight_status,json=weightStatus,proto3,enum=ugc.content.WeightStatus" json:"weight_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *StatusResult) Reset()         { *m = StatusResult{} }
func (m *StatusResult) String() string { return proto.CompactTextString(m) }
func (*StatusResult) ProtoMessage()    {}
func (*StatusResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{107}
}
func (m *StatusResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StatusResult.Unmarshal(m, b)
}
func (m *StatusResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StatusResult.Marshal(b, m, deterministic)
}
func (dst *StatusResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StatusResult.Merge(dst, src)
}
func (m *StatusResult) XXX_Size() int {
	return xxx_messageInfo_StatusResult.Size(m)
}
func (m *StatusResult) XXX_DiscardUnknown() {
	xxx_messageInfo_StatusResult.DiscardUnknown(m)
}

var xxx_messageInfo_StatusResult proto.InternalMessageInfo

func (m *StatusResult) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *StatusResult) GetWeightStatus() WeightStatus {
	if m != nil {
		return m.WeightStatus
	}
	return WeightStatus_WeiStDefault
}

type BatUpdateWeightReq struct {
	IdList               []string              `protobuf:"bytes,1,rep,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	WeightInfo           *UpdatePostWeightInfo `protobuf:"bytes,2,opt,name=weight_info,json=weightInfo,proto3" json:"weight_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *BatUpdateWeightReq) Reset()         { *m = BatUpdateWeightReq{} }
func (m *BatUpdateWeightReq) String() string { return proto.CompactTextString(m) }
func (*BatUpdateWeightReq) ProtoMessage()    {}
func (*BatUpdateWeightReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{108}
}
func (m *BatUpdateWeightReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatUpdateWeightReq.Unmarshal(m, b)
}
func (m *BatUpdateWeightReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatUpdateWeightReq.Marshal(b, m, deterministic)
}
func (dst *BatUpdateWeightReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatUpdateWeightReq.Merge(dst, src)
}
func (m *BatUpdateWeightReq) XXX_Size() int {
	return xxx_messageInfo_BatUpdateWeightReq.Size(m)
}
func (m *BatUpdateWeightReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatUpdateWeightReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatUpdateWeightReq proto.InternalMessageInfo

func (m *BatUpdateWeightReq) GetIdList() []string {
	if m != nil {
		return m.IdList
	}
	return nil
}

func (m *BatUpdateWeightReq) GetWeightInfo() *UpdatePostWeightInfo {
	if m != nil {
		return m.WeightInfo
	}
	return nil
}

type BatUpdateWeightResp struct {
	UpdateCnt            uint32          `protobuf:"varint,1,opt,name=update_cnt,json=updateCnt,proto3" json:"update_cnt,omitempty"`
	Results              []*StatusResult `protobuf:"bytes,2,rep,name=results,proto3" json:"results,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatUpdateWeightResp) Reset()         { *m = BatUpdateWeightResp{} }
func (m *BatUpdateWeightResp) String() string { return proto.CompactTextString(m) }
func (*BatUpdateWeightResp) ProtoMessage()    {}
func (*BatUpdateWeightResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{109}
}
func (m *BatUpdateWeightResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatUpdateWeightResp.Unmarshal(m, b)
}
func (m *BatUpdateWeightResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatUpdateWeightResp.Marshal(b, m, deterministic)
}
func (dst *BatUpdateWeightResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatUpdateWeightResp.Merge(dst, src)
}
func (m *BatUpdateWeightResp) XXX_Size() int {
	return xxx_messageInfo_BatUpdateWeightResp.Size(m)
}
func (m *BatUpdateWeightResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatUpdateWeightResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatUpdateWeightResp proto.InternalMessageInfo

func (m *BatUpdateWeightResp) GetUpdateCnt() uint32 {
	if m != nil {
		return m.UpdateCnt
	}
	return 0
}

func (m *BatUpdateWeightResp) GetResults() []*StatusResult {
	if m != nil {
		return m.Results
	}
	return nil
}

// 批量获取所有提权帖子
type BatGetPostWeightReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetPostWeightReq) Reset()         { *m = BatGetPostWeightReq{} }
func (m *BatGetPostWeightReq) String() string { return proto.CompactTextString(m) }
func (*BatGetPostWeightReq) ProtoMessage()    {}
func (*BatGetPostWeightReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{110}
}
func (m *BatGetPostWeightReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetPostWeightReq.Unmarshal(m, b)
}
func (m *BatGetPostWeightReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetPostWeightReq.Marshal(b, m, deterministic)
}
func (dst *BatGetPostWeightReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetPostWeightReq.Merge(dst, src)
}
func (m *BatGetPostWeightReq) XXX_Size() int {
	return xxx_messageInfo_BatGetPostWeightReq.Size(m)
}
func (m *BatGetPostWeightReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetPostWeightReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetPostWeightReq proto.InternalMessageInfo

func (m *BatGetPostWeightReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *BatGetPostWeightReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type PostWeightAllInfo struct {
	PostInfo             *PostInfo       `protobuf:"bytes,1,opt,name=post_info,json=postInfo,proto3" json:"post_info,omitempty"`
	WeightInfo           *PostWeightInfo `protobuf:"bytes,2,opt,name=weight_info,json=weightInfo,proto3" json:"weight_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *PostWeightAllInfo) Reset()         { *m = PostWeightAllInfo{} }
func (m *PostWeightAllInfo) String() string { return proto.CompactTextString(m) }
func (*PostWeightAllInfo) ProtoMessage()    {}
func (*PostWeightAllInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{111}
}
func (m *PostWeightAllInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostWeightAllInfo.Unmarshal(m, b)
}
func (m *PostWeightAllInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostWeightAllInfo.Marshal(b, m, deterministic)
}
func (dst *PostWeightAllInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostWeightAllInfo.Merge(dst, src)
}
func (m *PostWeightAllInfo) XXX_Size() int {
	return xxx_messageInfo_PostWeightAllInfo.Size(m)
}
func (m *PostWeightAllInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PostWeightAllInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PostWeightAllInfo proto.InternalMessageInfo

func (m *PostWeightAllInfo) GetPostInfo() *PostInfo {
	if m != nil {
		return m.PostInfo
	}
	return nil
}

func (m *PostWeightAllInfo) GetWeightInfo() *PostWeightInfo {
	if m != nil {
		return m.WeightInfo
	}
	return nil
}

type BatGetPostWeightResp struct {
	WeightInfo           []*PostWeightInfo `protobuf:"bytes,1,rep,name=weight_info,json=weightInfo,proto3" json:"weight_info,omitempty"`
	TotalCnt             uint32            `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatGetPostWeightResp) Reset()         { *m = BatGetPostWeightResp{} }
func (m *BatGetPostWeightResp) String() string { return proto.CompactTextString(m) }
func (*BatGetPostWeightResp) ProtoMessage()    {}
func (*BatGetPostWeightResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{112}
}
func (m *BatGetPostWeightResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetPostWeightResp.Unmarshal(m, b)
}
func (m *BatGetPostWeightResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetPostWeightResp.Marshal(b, m, deterministic)
}
func (dst *BatGetPostWeightResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetPostWeightResp.Merge(dst, src)
}
func (m *BatGetPostWeightResp) XXX_Size() int {
	return xxx_messageInfo_BatGetPostWeightResp.Size(m)
}
func (m *BatGetPostWeightResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetPostWeightResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetPostWeightResp proto.InternalMessageInfo

func (m *BatGetPostWeightResp) GetWeightInfo() []*PostWeightInfo {
	if m != nil {
		return m.WeightInfo
	}
	return nil
}

func (m *BatGetPostWeightResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

type BatGetPostAndWeightReq struct {
	Offset               uint32       `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32       `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	FilterStatus         WeightStatus `protobuf:"varint,3,opt,name=filter_status,json=filterStatus,proto3,enum=ugc.content.WeightStatus" json:"filter_status,omitempty"`
	WeightFilter         string       `protobuf:"bytes,4,opt,name=weight_filter,json=weightFilter,proto3" json:"weight_filter,omitempty"`
	BeginTime            uint32       `protobuf:"varint,5,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32       `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	PostIdList           []string     `protobuf:"bytes,7,rep,name=post_id_list,json=postIdList,proto3" json:"post_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BatGetPostAndWeightReq) Reset()         { *m = BatGetPostAndWeightReq{} }
func (m *BatGetPostAndWeightReq) String() string { return proto.CompactTextString(m) }
func (*BatGetPostAndWeightReq) ProtoMessage()    {}
func (*BatGetPostAndWeightReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{113}
}
func (m *BatGetPostAndWeightReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetPostAndWeightReq.Unmarshal(m, b)
}
func (m *BatGetPostAndWeightReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetPostAndWeightReq.Marshal(b, m, deterministic)
}
func (dst *BatGetPostAndWeightReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetPostAndWeightReq.Merge(dst, src)
}
func (m *BatGetPostAndWeightReq) XXX_Size() int {
	return xxx_messageInfo_BatGetPostAndWeightReq.Size(m)
}
func (m *BatGetPostAndWeightReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetPostAndWeightReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetPostAndWeightReq proto.InternalMessageInfo

func (m *BatGetPostAndWeightReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *BatGetPostAndWeightReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *BatGetPostAndWeightReq) GetFilterStatus() WeightStatus {
	if m != nil {
		return m.FilterStatus
	}
	return WeightStatus_WeiStDefault
}

func (m *BatGetPostAndWeightReq) GetWeightFilter() string {
	if m != nil {
		return m.WeightFilter
	}
	return ""
}

func (m *BatGetPostAndWeightReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *BatGetPostAndWeightReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *BatGetPostAndWeightReq) GetPostIdList() []string {
	if m != nil {
		return m.PostIdList
	}
	return nil
}

type BatGetPostAndWeightResp struct {
	PostList             []*PostWeightAllInfo `protobuf:"bytes,1,rep,name=post_list,json=postList,proto3" json:"post_list,omitempty"`
	TotalCnt             uint32               `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatGetPostAndWeightResp) Reset()         { *m = BatGetPostAndWeightResp{} }
func (m *BatGetPostAndWeightResp) String() string { return proto.CompactTextString(m) }
func (*BatGetPostAndWeightResp) ProtoMessage()    {}
func (*BatGetPostAndWeightResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{114}
}
func (m *BatGetPostAndWeightResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetPostAndWeightResp.Unmarshal(m, b)
}
func (m *BatGetPostAndWeightResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetPostAndWeightResp.Marshal(b, m, deterministic)
}
func (dst *BatGetPostAndWeightResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetPostAndWeightResp.Merge(dst, src)
}
func (m *BatGetPostAndWeightResp) XXX_Size() int {
	return xxx_messageInfo_BatGetPostAndWeightResp.Size(m)
}
func (m *BatGetPostAndWeightResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetPostAndWeightResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetPostAndWeightResp proto.InternalMessageInfo

func (m *BatGetPostAndWeightResp) GetPostList() []*PostWeightAllInfo {
	if m != nil {
		return m.PostList
	}
	return nil
}

func (m *BatGetPostAndWeightResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

type UpdatePostMachineAuditByIdReq struct {
	PostId               string            `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Status               PostMachineStatus `protobuf:"varint,2,opt,name=status,proto3,enum=ugc.content.PostMachineStatus" json:"status,omitempty"`
	Reason               string            `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UpdatePostMachineAuditByIdReq) Reset()         { *m = UpdatePostMachineAuditByIdReq{} }
func (m *UpdatePostMachineAuditByIdReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePostMachineAuditByIdReq) ProtoMessage()    {}
func (*UpdatePostMachineAuditByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{115}
}
func (m *UpdatePostMachineAuditByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePostMachineAuditByIdReq.Unmarshal(m, b)
}
func (m *UpdatePostMachineAuditByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePostMachineAuditByIdReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePostMachineAuditByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePostMachineAuditByIdReq.Merge(dst, src)
}
func (m *UpdatePostMachineAuditByIdReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePostMachineAuditByIdReq.Size(m)
}
func (m *UpdatePostMachineAuditByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePostMachineAuditByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePostMachineAuditByIdReq proto.InternalMessageInfo

func (m *UpdatePostMachineAuditByIdReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UpdatePostMachineAuditByIdReq) GetStatus() PostMachineStatus {
	if m != nil {
		return m.Status
	}
	return PostMachineStatus_MachineDefault
}

func (m *UpdatePostMachineAuditByIdReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type UpdatePostMachineAuditByIdRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePostMachineAuditByIdRsp) Reset()         { *m = UpdatePostMachineAuditByIdRsp{} }
func (m *UpdatePostMachineAuditByIdRsp) String() string { return proto.CompactTextString(m) }
func (*UpdatePostMachineAuditByIdRsp) ProtoMessage()    {}
func (*UpdatePostMachineAuditByIdRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{116}
}
func (m *UpdatePostMachineAuditByIdRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePostMachineAuditByIdRsp.Unmarshal(m, b)
}
func (m *UpdatePostMachineAuditByIdRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePostMachineAuditByIdRsp.Marshal(b, m, deterministic)
}
func (dst *UpdatePostMachineAuditByIdRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePostMachineAuditByIdRsp.Merge(dst, src)
}
func (m *UpdatePostMachineAuditByIdRsp) XXX_Size() int {
	return xxx_messageInfo_UpdatePostMachineAuditByIdRsp.Size(m)
}
func (m *UpdatePostMachineAuditByIdRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePostMachineAuditByIdRsp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePostMachineAuditByIdRsp proto.InternalMessageInfo

// App Tab筛选器
type AppTabFilter struct {
	MarketId             uint32   `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	IsShowInMainTab      bool     `protobuf:"varint,2,opt,name=is_show_in_main_tab,json=isShowInMainTab,proto3" json:"is_show_in_main_tab,omitempty"`
	ShowSubTabNames      []string `protobuf:"bytes,3,rep,name=show_sub_tab_names,json=showSubTabNames,proto3" json:"show_sub_tab_names,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AppTabFilter) Reset()         { *m = AppTabFilter{} }
func (m *AppTabFilter) String() string { return proto.CompactTextString(m) }
func (*AppTabFilter) ProtoMessage()    {}
func (*AppTabFilter) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{117}
}
func (m *AppTabFilter) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AppTabFilter.Unmarshal(m, b)
}
func (m *AppTabFilter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AppTabFilter.Marshal(b, m, deterministic)
}
func (dst *AppTabFilter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppTabFilter.Merge(dst, src)
}
func (m *AppTabFilter) XXX_Size() int {
	return xxx_messageInfo_AppTabFilter.Size(m)
}
func (m *AppTabFilter) XXX_DiscardUnknown() {
	xxx_messageInfo_AppTabFilter.DiscardUnknown(m)
}

var xxx_messageInfo_AppTabFilter proto.InternalMessageInfo

func (m *AppTabFilter) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *AppTabFilter) GetIsShowInMainTab() bool {
	if m != nil {
		return m.IsShowInMainTab
	}
	return false
}

func (m *AppTabFilter) GetShowSubTabNames() []string {
	if m != nil {
		return m.ShowSubTabNames
	}
	return nil
}

// 推荐流强插帖子
type ForcePostInUserRcmdFeedBaseInfo struct {
	ConfigId             string          `protobuf:"bytes,1,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`
	PostId               string          `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Index                uint32          `protobuf:"varint,3,opt,name=index,proto3" json:"index,omitempty"`
	BeginTs              uint32          `protobuf:"varint,4,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32          `protobuf:"varint,5,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	OperatorName         string          `protobuf:"bytes,6,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	AppTabFilters        []*AppTabFilter `protobuf:"bytes,7,rep,name=app_tab_filters,json=appTabFilters,proto3" json:"app_tab_filters,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ForcePostInUserRcmdFeedBaseInfo) Reset()         { *m = ForcePostInUserRcmdFeedBaseInfo{} }
func (m *ForcePostInUserRcmdFeedBaseInfo) String() string { return proto.CompactTextString(m) }
func (*ForcePostInUserRcmdFeedBaseInfo) ProtoMessage()    {}
func (*ForcePostInUserRcmdFeedBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{118}
}
func (m *ForcePostInUserRcmdFeedBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ForcePostInUserRcmdFeedBaseInfo.Unmarshal(m, b)
}
func (m *ForcePostInUserRcmdFeedBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ForcePostInUserRcmdFeedBaseInfo.Marshal(b, m, deterministic)
}
func (dst *ForcePostInUserRcmdFeedBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ForcePostInUserRcmdFeedBaseInfo.Merge(dst, src)
}
func (m *ForcePostInUserRcmdFeedBaseInfo) XXX_Size() int {
	return xxx_messageInfo_ForcePostInUserRcmdFeedBaseInfo.Size(m)
}
func (m *ForcePostInUserRcmdFeedBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ForcePostInUserRcmdFeedBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ForcePostInUserRcmdFeedBaseInfo proto.InternalMessageInfo

func (m *ForcePostInUserRcmdFeedBaseInfo) GetConfigId() string {
	if m != nil {
		return m.ConfigId
	}
	return ""
}

func (m *ForcePostInUserRcmdFeedBaseInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *ForcePostInUserRcmdFeedBaseInfo) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *ForcePostInUserRcmdFeedBaseInfo) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *ForcePostInUserRcmdFeedBaseInfo) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *ForcePostInUserRcmdFeedBaseInfo) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *ForcePostInUserRcmdFeedBaseInfo) GetAppTabFilters() []*AppTabFilter {
	if m != nil {
		return m.AppTabFilters
	}
	return nil
}

type ForcePostInUserRcmdFeedTotalInfo struct {
	BaseInfo             *ForcePostInUserRcmdFeedBaseInfo `protobuf:"bytes,1,opt,name=base_info,json=baseInfo,proto3" json:"base_info,omitempty"`
	Uid                  uint32                           `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Content              string                           `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	Attachments          []*AttachmentInfo                `protobuf:"bytes,4,rep,name=attachments,proto3" json:"attachments,omitempty"`
	Topicids             []string                         `protobuf:"bytes,5,rep,name=topicids,proto3" json:"topicids,omitempty"`
	CreateTs             uint32                           `protobuf:"varint,6,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`
	ViewCnt              uint32                           `protobuf:"varint,7,opt,name=view_cnt,json=viewCnt,proto3" json:"view_cnt,omitempty"`
	AttitudeCnt          uint32                           `protobuf:"varint,8,opt,name=attitude_cnt,json=attitudeCnt,proto3" json:"attitude_cnt,omitempty"`
	CommentCnt           uint32                           `protobuf:"varint,9,opt,name=comment_cnt,json=commentCnt,proto3" json:"comment_cnt,omitempty"`
	ShareCnt             uint32                           `protobuf:"varint,10,opt,name=share_cnt,json=shareCnt,proto3" json:"share_cnt,omitempty"`
	UpdateTs             uint32                           `protobuf:"varint,11,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *ForcePostInUserRcmdFeedTotalInfo) Reset()         { *m = ForcePostInUserRcmdFeedTotalInfo{} }
func (m *ForcePostInUserRcmdFeedTotalInfo) String() string { return proto.CompactTextString(m) }
func (*ForcePostInUserRcmdFeedTotalInfo) ProtoMessage()    {}
func (*ForcePostInUserRcmdFeedTotalInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{119}
}
func (m *ForcePostInUserRcmdFeedTotalInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ForcePostInUserRcmdFeedTotalInfo.Unmarshal(m, b)
}
func (m *ForcePostInUserRcmdFeedTotalInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ForcePostInUserRcmdFeedTotalInfo.Marshal(b, m, deterministic)
}
func (dst *ForcePostInUserRcmdFeedTotalInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ForcePostInUserRcmdFeedTotalInfo.Merge(dst, src)
}
func (m *ForcePostInUserRcmdFeedTotalInfo) XXX_Size() int {
	return xxx_messageInfo_ForcePostInUserRcmdFeedTotalInfo.Size(m)
}
func (m *ForcePostInUserRcmdFeedTotalInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ForcePostInUserRcmdFeedTotalInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ForcePostInUserRcmdFeedTotalInfo proto.InternalMessageInfo

func (m *ForcePostInUserRcmdFeedTotalInfo) GetBaseInfo() *ForcePostInUserRcmdFeedBaseInfo {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *ForcePostInUserRcmdFeedTotalInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ForcePostInUserRcmdFeedTotalInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ForcePostInUserRcmdFeedTotalInfo) GetAttachments() []*AttachmentInfo {
	if m != nil {
		return m.Attachments
	}
	return nil
}

func (m *ForcePostInUserRcmdFeedTotalInfo) GetTopicids() []string {
	if m != nil {
		return m.Topicids
	}
	return nil
}

func (m *ForcePostInUserRcmdFeedTotalInfo) GetCreateTs() uint32 {
	if m != nil {
		return m.CreateTs
	}
	return 0
}

func (m *ForcePostInUserRcmdFeedTotalInfo) GetViewCnt() uint32 {
	if m != nil {
		return m.ViewCnt
	}
	return 0
}

func (m *ForcePostInUserRcmdFeedTotalInfo) GetAttitudeCnt() uint32 {
	if m != nil {
		return m.AttitudeCnt
	}
	return 0
}

func (m *ForcePostInUserRcmdFeedTotalInfo) GetCommentCnt() uint32 {
	if m != nil {
		return m.CommentCnt
	}
	return 0
}

func (m *ForcePostInUserRcmdFeedTotalInfo) GetShareCnt() uint32 {
	if m != nil {
		return m.ShareCnt
	}
	return 0
}

func (m *ForcePostInUserRcmdFeedTotalInfo) GetUpdateTs() uint32 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

type SetForcePostInUserRcmdFeedReq struct {
	Info                 *ForcePostInUserRcmdFeedBaseInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *SetForcePostInUserRcmdFeedReq) Reset()         { *m = SetForcePostInUserRcmdFeedReq{} }
func (m *SetForcePostInUserRcmdFeedReq) String() string { return proto.CompactTextString(m) }
func (*SetForcePostInUserRcmdFeedReq) ProtoMessage()    {}
func (*SetForcePostInUserRcmdFeedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{120}
}
func (m *SetForcePostInUserRcmdFeedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetForcePostInUserRcmdFeedReq.Unmarshal(m, b)
}
func (m *SetForcePostInUserRcmdFeedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetForcePostInUserRcmdFeedReq.Marshal(b, m, deterministic)
}
func (dst *SetForcePostInUserRcmdFeedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetForcePostInUserRcmdFeedReq.Merge(dst, src)
}
func (m *SetForcePostInUserRcmdFeedReq) XXX_Size() int {
	return xxx_messageInfo_SetForcePostInUserRcmdFeedReq.Size(m)
}
func (m *SetForcePostInUserRcmdFeedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetForcePostInUserRcmdFeedReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetForcePostInUserRcmdFeedReq proto.InternalMessageInfo

func (m *SetForcePostInUserRcmdFeedReq) GetInfo() *ForcePostInUserRcmdFeedBaseInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type SetForcePostInUserRcmdFeedResp struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetForcePostInUserRcmdFeedResp) Reset()         { *m = SetForcePostInUserRcmdFeedResp{} }
func (m *SetForcePostInUserRcmdFeedResp) String() string { return proto.CompactTextString(m) }
func (*SetForcePostInUserRcmdFeedResp) ProtoMessage()    {}
func (*SetForcePostInUserRcmdFeedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{121}
}
func (m *SetForcePostInUserRcmdFeedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetForcePostInUserRcmdFeedResp.Unmarshal(m, b)
}
func (m *SetForcePostInUserRcmdFeedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetForcePostInUserRcmdFeedResp.Marshal(b, m, deterministic)
}
func (dst *SetForcePostInUserRcmdFeedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetForcePostInUserRcmdFeedResp.Merge(dst, src)
}
func (m *SetForcePostInUserRcmdFeedResp) XXX_Size() int {
	return xxx_messageInfo_SetForcePostInUserRcmdFeedResp.Size(m)
}
func (m *SetForcePostInUserRcmdFeedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetForcePostInUserRcmdFeedResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetForcePostInUserRcmdFeedResp proto.InternalMessageInfo

func (m *SetForcePostInUserRcmdFeedResp) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type GetForcePostInUserRcmdFeedReq struct {
	ConfigId             string   `protobuf:"bytes,1,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Index                uint32   `protobuf:"varint,3,opt,name=index,proto3" json:"index,omitempty"`
	Status               uint32   `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	Offset               uint32   `protobuf:"varint,5,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,6,opt,name=limit,proto3" json:"limit,omitempty"`
	MarketIds            []uint32 `protobuf:"varint,7,rep,packed,name=market_ids,json=marketIds,proto3" json:"market_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetForcePostInUserRcmdFeedReq) Reset()         { *m = GetForcePostInUserRcmdFeedReq{} }
func (m *GetForcePostInUserRcmdFeedReq) String() string { return proto.CompactTextString(m) }
func (*GetForcePostInUserRcmdFeedReq) ProtoMessage()    {}
func (*GetForcePostInUserRcmdFeedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{122}
}
func (m *GetForcePostInUserRcmdFeedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetForcePostInUserRcmdFeedReq.Unmarshal(m, b)
}
func (m *GetForcePostInUserRcmdFeedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetForcePostInUserRcmdFeedReq.Marshal(b, m, deterministic)
}
func (dst *GetForcePostInUserRcmdFeedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetForcePostInUserRcmdFeedReq.Merge(dst, src)
}
func (m *GetForcePostInUserRcmdFeedReq) XXX_Size() int {
	return xxx_messageInfo_GetForcePostInUserRcmdFeedReq.Size(m)
}
func (m *GetForcePostInUserRcmdFeedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetForcePostInUserRcmdFeedReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetForcePostInUserRcmdFeedReq proto.InternalMessageInfo

func (m *GetForcePostInUserRcmdFeedReq) GetConfigId() string {
	if m != nil {
		return m.ConfigId
	}
	return ""
}

func (m *GetForcePostInUserRcmdFeedReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetForcePostInUserRcmdFeedReq) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *GetForcePostInUserRcmdFeedReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetForcePostInUserRcmdFeedReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetForcePostInUserRcmdFeedReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetForcePostInUserRcmdFeedReq) GetMarketIds() []uint32 {
	if m != nil {
		return m.MarketIds
	}
	return nil
}

type GetForcePostInUserRcmdFeedResp struct {
	Infos                []*ForcePostInUserRcmdFeedTotalInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	TotalCnt             uint32                              `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *GetForcePostInUserRcmdFeedResp) Reset()         { *m = GetForcePostInUserRcmdFeedResp{} }
func (m *GetForcePostInUserRcmdFeedResp) String() string { return proto.CompactTextString(m) }
func (*GetForcePostInUserRcmdFeedResp) ProtoMessage()    {}
func (*GetForcePostInUserRcmdFeedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{123}
}
func (m *GetForcePostInUserRcmdFeedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetForcePostInUserRcmdFeedResp.Unmarshal(m, b)
}
func (m *GetForcePostInUserRcmdFeedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetForcePostInUserRcmdFeedResp.Marshal(b, m, deterministic)
}
func (dst *GetForcePostInUserRcmdFeedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetForcePostInUserRcmdFeedResp.Merge(dst, src)
}
func (m *GetForcePostInUserRcmdFeedResp) XXX_Size() int {
	return xxx_messageInfo_GetForcePostInUserRcmdFeedResp.Size(m)
}
func (m *GetForcePostInUserRcmdFeedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetForcePostInUserRcmdFeedResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetForcePostInUserRcmdFeedResp proto.InternalMessageInfo

func (m *GetForcePostInUserRcmdFeedResp) GetInfos() []*ForcePostInUserRcmdFeedTotalInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

func (m *GetForcePostInUserRcmdFeedResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// app
type GetForcePostsInUserRcmdFeedReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	TabName              string   `protobuf:"bytes,4,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetForcePostsInUserRcmdFeedReq) Reset()         { *m = GetForcePostsInUserRcmdFeedReq{} }
func (m *GetForcePostsInUserRcmdFeedReq) String() string { return proto.CompactTextString(m) }
func (*GetForcePostsInUserRcmdFeedReq) ProtoMessage()    {}
func (*GetForcePostsInUserRcmdFeedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{124}
}
func (m *GetForcePostsInUserRcmdFeedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetForcePostsInUserRcmdFeedReq.Unmarshal(m, b)
}
func (m *GetForcePostsInUserRcmdFeedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetForcePostsInUserRcmdFeedReq.Marshal(b, m, deterministic)
}
func (dst *GetForcePostsInUserRcmdFeedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetForcePostsInUserRcmdFeedReq.Merge(dst, src)
}
func (m *GetForcePostsInUserRcmdFeedReq) XXX_Size() int {
	return xxx_messageInfo_GetForcePostsInUserRcmdFeedReq.Size(m)
}
func (m *GetForcePostsInUserRcmdFeedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetForcePostsInUserRcmdFeedReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetForcePostsInUserRcmdFeedReq proto.InternalMessageInfo

func (m *GetForcePostsInUserRcmdFeedReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetForcePostsInUserRcmdFeedReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetForcePostsInUserRcmdFeedReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetForcePostsInUserRcmdFeedReq) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

type GetForcePostsInUserRcmdFeedResp struct {
	Infos                []*ForcePostInUserRcmdFeedTotalInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *GetForcePostsInUserRcmdFeedResp) Reset()         { *m = GetForcePostsInUserRcmdFeedResp{} }
func (m *GetForcePostsInUserRcmdFeedResp) String() string { return proto.CompactTextString(m) }
func (*GetForcePostsInUserRcmdFeedResp) ProtoMessage()    {}
func (*GetForcePostsInUserRcmdFeedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{125}
}
func (m *GetForcePostsInUserRcmdFeedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetForcePostsInUserRcmdFeedResp.Unmarshal(m, b)
}
func (m *GetForcePostsInUserRcmdFeedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetForcePostsInUserRcmdFeedResp.Marshal(b, m, deterministic)
}
func (dst *GetForcePostsInUserRcmdFeedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetForcePostsInUserRcmdFeedResp.Merge(dst, src)
}
func (m *GetForcePostsInUserRcmdFeedResp) XXX_Size() int {
	return xxx_messageInfo_GetForcePostsInUserRcmdFeedResp.Size(m)
}
func (m *GetForcePostsInUserRcmdFeedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetForcePostsInUserRcmdFeedResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetForcePostsInUserRcmdFeedResp proto.InternalMessageInfo

func (m *GetForcePostsInUserRcmdFeedResp) GetInfos() []*ForcePostInUserRcmdFeedTotalInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

type DelForcePostInUserRcmdFeedReq struct {
	ConfigIds            []string `protobuf:"bytes,1,rep,name=config_ids,json=configIds,proto3" json:"config_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelForcePostInUserRcmdFeedReq) Reset()         { *m = DelForcePostInUserRcmdFeedReq{} }
func (m *DelForcePostInUserRcmdFeedReq) String() string { return proto.CompactTextString(m) }
func (*DelForcePostInUserRcmdFeedReq) ProtoMessage()    {}
func (*DelForcePostInUserRcmdFeedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{126}
}
func (m *DelForcePostInUserRcmdFeedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelForcePostInUserRcmdFeedReq.Unmarshal(m, b)
}
func (m *DelForcePostInUserRcmdFeedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelForcePostInUserRcmdFeedReq.Marshal(b, m, deterministic)
}
func (dst *DelForcePostInUserRcmdFeedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelForcePostInUserRcmdFeedReq.Merge(dst, src)
}
func (m *DelForcePostInUserRcmdFeedReq) XXX_Size() int {
	return xxx_messageInfo_DelForcePostInUserRcmdFeedReq.Size(m)
}
func (m *DelForcePostInUserRcmdFeedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelForcePostInUserRcmdFeedReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelForcePostInUserRcmdFeedReq proto.InternalMessageInfo

func (m *DelForcePostInUserRcmdFeedReq) GetConfigIds() []string {
	if m != nil {
		return m.ConfigIds
	}
	return nil
}

type DelForcePostInUserRcmdFeedResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelForcePostInUserRcmdFeedResp) Reset()         { *m = DelForcePostInUserRcmdFeedResp{} }
func (m *DelForcePostInUserRcmdFeedResp) String() string { return proto.CompactTextString(m) }
func (*DelForcePostInUserRcmdFeedResp) ProtoMessage()    {}
func (*DelForcePostInUserRcmdFeedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{127}
}
func (m *DelForcePostInUserRcmdFeedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelForcePostInUserRcmdFeedResp.Unmarshal(m, b)
}
func (m *DelForcePostInUserRcmdFeedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelForcePostInUserRcmdFeedResp.Marshal(b, m, deterministic)
}
func (dst *DelForcePostInUserRcmdFeedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelForcePostInUserRcmdFeedResp.Merge(dst, src)
}
func (m *DelForcePostInUserRcmdFeedResp) XXX_Size() int {
	return xxx_messageInfo_DelForcePostInUserRcmdFeedResp.Size(m)
}
func (m *DelForcePostInUserRcmdFeedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelForcePostInUserRcmdFeedResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelForcePostInUserRcmdFeedResp proto.InternalMessageInfo

type SearchPostByTopicReq struct {
	Limit    uint32 `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"`
	Desc     bool   `protobuf:"varint,2,opt,name=desc,proto3" json:"desc,omitempty"`
	LoadMore string `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	// 以下是筛选条件
	TopicId              string   `protobuf:"bytes,4,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	CreateBegin          int64    `protobuf:"varint,5,opt,name=create_begin,json=createBegin,proto3" json:"create_begin,omitempty"`
	CreateEnd            int64    `protobuf:"varint,6,opt,name=create_end,json=createEnd,proto3" json:"create_end,omitempty"`
	Uid                  uint32   `protobuf:"varint,7,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchPostByTopicReq) Reset()         { *m = SearchPostByTopicReq{} }
func (m *SearchPostByTopicReq) String() string { return proto.CompactTextString(m) }
func (*SearchPostByTopicReq) ProtoMessage()    {}
func (*SearchPostByTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{128}
}
func (m *SearchPostByTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchPostByTopicReq.Unmarshal(m, b)
}
func (m *SearchPostByTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchPostByTopicReq.Marshal(b, m, deterministic)
}
func (dst *SearchPostByTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchPostByTopicReq.Merge(dst, src)
}
func (m *SearchPostByTopicReq) XXX_Size() int {
	return xxx_messageInfo_SearchPostByTopicReq.Size(m)
}
func (m *SearchPostByTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchPostByTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchPostByTopicReq proto.InternalMessageInfo

func (m *SearchPostByTopicReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *SearchPostByTopicReq) GetDesc() bool {
	if m != nil {
		return m.Desc
	}
	return false
}

func (m *SearchPostByTopicReq) GetLoadMore() string {
	if m != nil {
		return m.LoadMore
	}
	return ""
}

func (m *SearchPostByTopicReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *SearchPostByTopicReq) GetCreateBegin() int64 {
	if m != nil {
		return m.CreateBegin
	}
	return 0
}

func (m *SearchPostByTopicReq) GetCreateEnd() int64 {
	if m != nil {
		return m.CreateEnd
	}
	return 0
}

func (m *SearchPostByTopicReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type SearchPostByTopicResp struct {
	LoadMore             string   `protobuf:"bytes,1,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	PostList             []string `protobuf:"bytes,2,rep,name=post_list,json=postList,proto3" json:"post_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchPostByTopicResp) Reset()         { *m = SearchPostByTopicResp{} }
func (m *SearchPostByTopicResp) String() string { return proto.CompactTextString(m) }
func (*SearchPostByTopicResp) ProtoMessage()    {}
func (*SearchPostByTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{129}
}
func (m *SearchPostByTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchPostByTopicResp.Unmarshal(m, b)
}
func (m *SearchPostByTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchPostByTopicResp.Marshal(b, m, deterministic)
}
func (dst *SearchPostByTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchPostByTopicResp.Merge(dst, src)
}
func (m *SearchPostByTopicResp) XXX_Size() int {
	return xxx_messageInfo_SearchPostByTopicResp.Size(m)
}
func (m *SearchPostByTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchPostByTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchPostByTopicResp proto.InternalMessageInfo

func (m *SearchPostByTopicResp) GetLoadMore() string {
	if m != nil {
		return m.LoadMore
	}
	return ""
}

func (m *SearchPostByTopicResp) GetPostList() []string {
	if m != nil {
		return m.PostList
	}
	return nil
}

type AssociatePostWithTopicReq struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	PostList             []string `protobuf:"bytes,2,rep,name=post_list,json=postList,proto3" json:"post_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AssociatePostWithTopicReq) Reset()         { *m = AssociatePostWithTopicReq{} }
func (m *AssociatePostWithTopicReq) String() string { return proto.CompactTextString(m) }
func (*AssociatePostWithTopicReq) ProtoMessage()    {}
func (*AssociatePostWithTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{130}
}
func (m *AssociatePostWithTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AssociatePostWithTopicReq.Unmarshal(m, b)
}
func (m *AssociatePostWithTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AssociatePostWithTopicReq.Marshal(b, m, deterministic)
}
func (dst *AssociatePostWithTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AssociatePostWithTopicReq.Merge(dst, src)
}
func (m *AssociatePostWithTopicReq) XXX_Size() int {
	return xxx_messageInfo_AssociatePostWithTopicReq.Size(m)
}
func (m *AssociatePostWithTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AssociatePostWithTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_AssociatePostWithTopicReq proto.InternalMessageInfo

func (m *AssociatePostWithTopicReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *AssociatePostWithTopicReq) GetPostList() []string {
	if m != nil {
		return m.PostList
	}
	return nil
}

type AssociatePostWithTopicResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AssociatePostWithTopicResp) Reset()         { *m = AssociatePostWithTopicResp{} }
func (m *AssociatePostWithTopicResp) String() string { return proto.CompactTextString(m) }
func (*AssociatePostWithTopicResp) ProtoMessage()    {}
func (*AssociatePostWithTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{131}
}
func (m *AssociatePostWithTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AssociatePostWithTopicResp.Unmarshal(m, b)
}
func (m *AssociatePostWithTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AssociatePostWithTopicResp.Marshal(b, m, deterministic)
}
func (dst *AssociatePostWithTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AssociatePostWithTopicResp.Merge(dst, src)
}
func (m *AssociatePostWithTopicResp) XXX_Size() int {
	return xxx_messageInfo_AssociatePostWithTopicResp.Size(m)
}
func (m *AssociatePostWithTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AssociatePostWithTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_AssociatePostWithTopicResp proto.InternalMessageInfo

type BatGetLocationByGeoTopicIdReq struct {
	GeoTopicIdList       []string `protobuf:"bytes,1,rep,name=geo_topic_id_list,json=geoTopicIdList,proto3" json:"geo_topic_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetLocationByGeoTopicIdReq) Reset()         { *m = BatGetLocationByGeoTopicIdReq{} }
func (m *BatGetLocationByGeoTopicIdReq) String() string { return proto.CompactTextString(m) }
func (*BatGetLocationByGeoTopicIdReq) ProtoMessage()    {}
func (*BatGetLocationByGeoTopicIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{132}
}
func (m *BatGetLocationByGeoTopicIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetLocationByGeoTopicIdReq.Unmarshal(m, b)
}
func (m *BatGetLocationByGeoTopicIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetLocationByGeoTopicIdReq.Marshal(b, m, deterministic)
}
func (dst *BatGetLocationByGeoTopicIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetLocationByGeoTopicIdReq.Merge(dst, src)
}
func (m *BatGetLocationByGeoTopicIdReq) XXX_Size() int {
	return xxx_messageInfo_BatGetLocationByGeoTopicIdReq.Size(m)
}
func (m *BatGetLocationByGeoTopicIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetLocationByGeoTopicIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetLocationByGeoTopicIdReq proto.InternalMessageInfo

func (m *BatGetLocationByGeoTopicIdReq) GetGeoTopicIdList() []string {
	if m != nil {
		return m.GeoTopicIdList
	}
	return nil
}

type BatGetLocationByGeoTopicIdResp struct {
	CityMap              map[string]string `protobuf:"bytes,1,rep,name=city_map,json=cityMap,proto3" json:"city_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ProvinceMap          map[string]string `protobuf:"bytes,2,rep,name=province_map,json=provinceMap,proto3" json:"province_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatGetLocationByGeoTopicIdResp) Reset()         { *m = BatGetLocationByGeoTopicIdResp{} }
func (m *BatGetLocationByGeoTopicIdResp) String() string { return proto.CompactTextString(m) }
func (*BatGetLocationByGeoTopicIdResp) ProtoMessage()    {}
func (*BatGetLocationByGeoTopicIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{133}
}
func (m *BatGetLocationByGeoTopicIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetLocationByGeoTopicIdResp.Unmarshal(m, b)
}
func (m *BatGetLocationByGeoTopicIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetLocationByGeoTopicIdResp.Marshal(b, m, deterministic)
}
func (dst *BatGetLocationByGeoTopicIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetLocationByGeoTopicIdResp.Merge(dst, src)
}
func (m *BatGetLocationByGeoTopicIdResp) XXX_Size() int {
	return xxx_messageInfo_BatGetLocationByGeoTopicIdResp.Size(m)
}
func (m *BatGetLocationByGeoTopicIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetLocationByGeoTopicIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetLocationByGeoTopicIdResp proto.InternalMessageInfo

func (m *BatGetLocationByGeoTopicIdResp) GetCityMap() map[string]string {
	if m != nil {
		return m.CityMap
	}
	return nil
}

func (m *BatGetLocationByGeoTopicIdResp) GetProvinceMap() map[string]string {
	if m != nil {
		return m.ProvinceMap
	}
	return nil
}

type TransPostToSquareReq struct {
	NonPublicPostId      string    `protobuf:"bytes,1,opt,name=non_public_post_id,json=nonPublicPostId,proto3" json:"non_public_post_id,omitempty"`
	PostInfo             *PostInfo `protobuf:"bytes,2,opt,name=post_info,json=postInfo,proto3" json:"post_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *TransPostToSquareReq) Reset()         { *m = TransPostToSquareReq{} }
func (m *TransPostToSquareReq) String() string { return proto.CompactTextString(m) }
func (*TransPostToSquareReq) ProtoMessage()    {}
func (*TransPostToSquareReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{134}
}
func (m *TransPostToSquareReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TransPostToSquareReq.Unmarshal(m, b)
}
func (m *TransPostToSquareReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TransPostToSquareReq.Marshal(b, m, deterministic)
}
func (dst *TransPostToSquareReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransPostToSquareReq.Merge(dst, src)
}
func (m *TransPostToSquareReq) XXX_Size() int {
	return xxx_messageInfo_TransPostToSquareReq.Size(m)
}
func (m *TransPostToSquareReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TransPostToSquareReq.DiscardUnknown(m)
}

var xxx_messageInfo_TransPostToSquareReq proto.InternalMessageInfo

func (m *TransPostToSquareReq) GetNonPublicPostId() string {
	if m != nil {
		return m.NonPublicPostId
	}
	return ""
}

func (m *TransPostToSquareReq) GetPostInfo() *PostInfo {
	if m != nil {
		return m.PostInfo
	}
	return nil
}

type NonPublicPostConversionSquarePostRequest struct {
	PostList             []*TransPostToSquareReq `protobuf:"bytes,1,rep,name=post_list,json=postList,proto3" json:"post_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *NonPublicPostConversionSquarePostRequest) Reset() {
	*m = NonPublicPostConversionSquarePostRequest{}
}
func (m *NonPublicPostConversionSquarePostRequest) String() string { return proto.CompactTextString(m) }
func (*NonPublicPostConversionSquarePostRequest) ProtoMessage()    {}
func (*NonPublicPostConversionSquarePostRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{135}
}
func (m *NonPublicPostConversionSquarePostRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicPostConversionSquarePostRequest.Unmarshal(m, b)
}
func (m *NonPublicPostConversionSquarePostRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicPostConversionSquarePostRequest.Marshal(b, m, deterministic)
}
func (dst *NonPublicPostConversionSquarePostRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicPostConversionSquarePostRequest.Merge(dst, src)
}
func (m *NonPublicPostConversionSquarePostRequest) XXX_Size() int {
	return xxx_messageInfo_NonPublicPostConversionSquarePostRequest.Size(m)
}
func (m *NonPublicPostConversionSquarePostRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicPostConversionSquarePostRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicPostConversionSquarePostRequest proto.InternalMessageInfo

func (m *NonPublicPostConversionSquarePostRequest) GetPostList() []*TransPostToSquareReq {
	if m != nil {
		return m.PostList
	}
	return nil
}

type NonPublicPostConversionSquarePostResponse struct {
	PostIdMap            map[string]string `protobuf:"bytes,1,rep,name=post_id_map,json=postIdMap,proto3" json:"post_id_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *NonPublicPostConversionSquarePostResponse) Reset() {
	*m = NonPublicPostConversionSquarePostResponse{}
}
func (m *NonPublicPostConversionSquarePostResponse) String() string {
	return proto.CompactTextString(m)
}
func (*NonPublicPostConversionSquarePostResponse) ProtoMessage() {}
func (*NonPublicPostConversionSquarePostResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{136}
}
func (m *NonPublicPostConversionSquarePostResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicPostConversionSquarePostResponse.Unmarshal(m, b)
}
func (m *NonPublicPostConversionSquarePostResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicPostConversionSquarePostResponse.Marshal(b, m, deterministic)
}
func (dst *NonPublicPostConversionSquarePostResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicPostConversionSquarePostResponse.Merge(dst, src)
}
func (m *NonPublicPostConversionSquarePostResponse) XXX_Size() int {
	return xxx_messageInfo_NonPublicPostConversionSquarePostResponse.Size(m)
}
func (m *NonPublicPostConversionSquarePostResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicPostConversionSquarePostResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicPostConversionSquarePostResponse proto.InternalMessageInfo

func (m *NonPublicPostConversionSquarePostResponse) GetPostIdMap() map[string]string {
	if m != nil {
		return m.PostIdMap
	}
	return nil
}

type SearchConversionNonPublicPostRequest struct {
	CategoryId           string   `protobuf:"bytes,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	SocialCommunityId    string   `protobuf:"bytes,3,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	NonPublicPostId      string   `protobuf:"bytes,4,opt,name=non_public_post_id,json=nonPublicPostId,proto3" json:"non_public_post_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	Offset               uint32   `protobuf:"varint,6,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,7,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchConversionNonPublicPostRequest) Reset()         { *m = SearchConversionNonPublicPostRequest{} }
func (m *SearchConversionNonPublicPostRequest) String() string { return proto.CompactTextString(m) }
func (*SearchConversionNonPublicPostRequest) ProtoMessage()    {}
func (*SearchConversionNonPublicPostRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{137}
}
func (m *SearchConversionNonPublicPostRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchConversionNonPublicPostRequest.Unmarshal(m, b)
}
func (m *SearchConversionNonPublicPostRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchConversionNonPublicPostRequest.Marshal(b, m, deterministic)
}
func (dst *SearchConversionNonPublicPostRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchConversionNonPublicPostRequest.Merge(dst, src)
}
func (m *SearchConversionNonPublicPostRequest) XXX_Size() int {
	return xxx_messageInfo_SearchConversionNonPublicPostRequest.Size(m)
}
func (m *SearchConversionNonPublicPostRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchConversionNonPublicPostRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SearchConversionNonPublicPostRequest proto.InternalMessageInfo

func (m *SearchConversionNonPublicPostRequest) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *SearchConversionNonPublicPostRequest) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *SearchConversionNonPublicPostRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *SearchConversionNonPublicPostRequest) GetNonPublicPostId() string {
	if m != nil {
		return m.NonPublicPostId
	}
	return ""
}

func (m *SearchConversionNonPublicPostRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SearchConversionNonPublicPostRequest) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *SearchConversionNonPublicPostRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type ConversionNonPublicPostInfo struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	NonPublicPostId      string   `protobuf:"bytes,2,opt,name=non_public_post_id,json=nonPublicPostId,proto3" json:"non_public_post_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 string   `protobuf:"bytes,4,opt,name=ttid,proto3" json:"ttid,omitempty"`
	NickName             string   `protobuf:"bytes,5,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	CategoryId           string   `protobuf:"bytes,6,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	CategoryName         string   `protobuf:"bytes,7,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty"`
	SocialCommunityId    string   `protobuf:"bytes,8,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	SocialCommunityName  string   `protobuf:"bytes,9,opt,name=social_community_name,json=socialCommunityName,proto3" json:"social_community_name,omitempty"`
	CreateTime           uint32   `protobuf:"varint,10,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	OriginUid            uint32   `protobuf:"varint,11,opt,name=origin_uid,json=originUid,proto3" json:"origin_uid,omitempty"`
	Operator             string   `protobuf:"bytes,12,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConversionNonPublicPostInfo) Reset()         { *m = ConversionNonPublicPostInfo{} }
func (m *ConversionNonPublicPostInfo) String() string { return proto.CompactTextString(m) }
func (*ConversionNonPublicPostInfo) ProtoMessage()    {}
func (*ConversionNonPublicPostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{138}
}
func (m *ConversionNonPublicPostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConversionNonPublicPostInfo.Unmarshal(m, b)
}
func (m *ConversionNonPublicPostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConversionNonPublicPostInfo.Marshal(b, m, deterministic)
}
func (dst *ConversionNonPublicPostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConversionNonPublicPostInfo.Merge(dst, src)
}
func (m *ConversionNonPublicPostInfo) XXX_Size() int {
	return xxx_messageInfo_ConversionNonPublicPostInfo.Size(m)
}
func (m *ConversionNonPublicPostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ConversionNonPublicPostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ConversionNonPublicPostInfo proto.InternalMessageInfo

func (m *ConversionNonPublicPostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *ConversionNonPublicPostInfo) GetNonPublicPostId() string {
	if m != nil {
		return m.NonPublicPostId
	}
	return ""
}

func (m *ConversionNonPublicPostInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ConversionNonPublicPostInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *ConversionNonPublicPostInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *ConversionNonPublicPostInfo) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *ConversionNonPublicPostInfo) GetCategoryName() string {
	if m != nil {
		return m.CategoryName
	}
	return ""
}

func (m *ConversionNonPublicPostInfo) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *ConversionNonPublicPostInfo) GetSocialCommunityName() string {
	if m != nil {
		return m.SocialCommunityName
	}
	return ""
}

func (m *ConversionNonPublicPostInfo) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *ConversionNonPublicPostInfo) GetOriginUid() uint32 {
	if m != nil {
		return m.OriginUid
	}
	return 0
}

func (m *ConversionNonPublicPostInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type SearchConversionNonPublicPostResponse struct {
	ConversionNonPublicPost []*ConversionNonPublicPostInfo `protobuf:"bytes,1,rep,name=ConversionNonPublicPost,proto3" json:"ConversionNonPublicPost,omitempty"`
	Count                   int32                          `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}                       `json:"-"`
	XXX_unrecognized        []byte                         `json:"-"`
	XXX_sizecache           int32                          `json:"-"`
}

func (m *SearchConversionNonPublicPostResponse) Reset()         { *m = SearchConversionNonPublicPostResponse{} }
func (m *SearchConversionNonPublicPostResponse) String() string { return proto.CompactTextString(m) }
func (*SearchConversionNonPublicPostResponse) ProtoMessage()    {}
func (*SearchConversionNonPublicPostResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{139}
}
func (m *SearchConversionNonPublicPostResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchConversionNonPublicPostResponse.Unmarshal(m, b)
}
func (m *SearchConversionNonPublicPostResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchConversionNonPublicPostResponse.Marshal(b, m, deterministic)
}
func (dst *SearchConversionNonPublicPostResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchConversionNonPublicPostResponse.Merge(dst, src)
}
func (m *SearchConversionNonPublicPostResponse) XXX_Size() int {
	return xxx_messageInfo_SearchConversionNonPublicPostResponse.Size(m)
}
func (m *SearchConversionNonPublicPostResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchConversionNonPublicPostResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SearchConversionNonPublicPostResponse proto.InternalMessageInfo

func (m *SearchConversionNonPublicPostResponse) GetConversionNonPublicPost() []*ConversionNonPublicPostInfo {
	if m != nil {
		return m.ConversionNonPublicPost
	}
	return nil
}

func (m *SearchConversionNonPublicPostResponse) GetCount() int32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type BatchConversionNonPublicPostRequest struct {
	Info                 []*ConversionNonPublicPostInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *BatchConversionNonPublicPostRequest) Reset()         { *m = BatchConversionNonPublicPostRequest{} }
func (m *BatchConversionNonPublicPostRequest) String() string { return proto.CompactTextString(m) }
func (*BatchConversionNonPublicPostRequest) ProtoMessage()    {}
func (*BatchConversionNonPublicPostRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{140}
}
func (m *BatchConversionNonPublicPostRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchConversionNonPublicPostRequest.Unmarshal(m, b)
}
func (m *BatchConversionNonPublicPostRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchConversionNonPublicPostRequest.Marshal(b, m, deterministic)
}
func (dst *BatchConversionNonPublicPostRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchConversionNonPublicPostRequest.Merge(dst, src)
}
func (m *BatchConversionNonPublicPostRequest) XXX_Size() int {
	return xxx_messageInfo_BatchConversionNonPublicPostRequest.Size(m)
}
func (m *BatchConversionNonPublicPostRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchConversionNonPublicPostRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchConversionNonPublicPostRequest proto.InternalMessageInfo

func (m *BatchConversionNonPublicPostRequest) GetInfo() []*ConversionNonPublicPostInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type BatchConversionNonPublicPostResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchConversionNonPublicPostResponse) Reset()         { *m = BatchConversionNonPublicPostResponse{} }
func (m *BatchConversionNonPublicPostResponse) String() string { return proto.CompactTextString(m) }
func (*BatchConversionNonPublicPostResponse) ProtoMessage()    {}
func (*BatchConversionNonPublicPostResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{141}
}
func (m *BatchConversionNonPublicPostResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchConversionNonPublicPostResponse.Unmarshal(m, b)
}
func (m *BatchConversionNonPublicPostResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchConversionNonPublicPostResponse.Marshal(b, m, deterministic)
}
func (dst *BatchConversionNonPublicPostResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchConversionNonPublicPostResponse.Merge(dst, src)
}
func (m *BatchConversionNonPublicPostResponse) XXX_Size() int {
	return xxx_messageInfo_BatchConversionNonPublicPostResponse.Size(m)
}
func (m *BatchConversionNonPublicPostResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchConversionNonPublicPostResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchConversionNonPublicPostResponse proto.InternalMessageInfo

type BatchConversionNonpublicPostRecordsByPostIdsRequest struct {
	PostIds              []string `protobuf:"bytes,1,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchConversionNonpublicPostRecordsByPostIdsRequest) Reset() {
	*m = BatchConversionNonpublicPostRecordsByPostIdsRequest{}
}
func (m *BatchConversionNonpublicPostRecordsByPostIdsRequest) String() string {
	return proto.CompactTextString(m)
}
func (*BatchConversionNonpublicPostRecordsByPostIdsRequest) ProtoMessage() {}
func (*BatchConversionNonpublicPostRecordsByPostIdsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{142}
}
func (m *BatchConversionNonpublicPostRecordsByPostIdsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchConversionNonpublicPostRecordsByPostIdsRequest.Unmarshal(m, b)
}
func (m *BatchConversionNonpublicPostRecordsByPostIdsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchConversionNonpublicPostRecordsByPostIdsRequest.Marshal(b, m, deterministic)
}
func (dst *BatchConversionNonpublicPostRecordsByPostIdsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchConversionNonpublicPostRecordsByPostIdsRequest.Merge(dst, src)
}
func (m *BatchConversionNonpublicPostRecordsByPostIdsRequest) XXX_Size() int {
	return xxx_messageInfo_BatchConversionNonpublicPostRecordsByPostIdsRequest.Size(m)
}
func (m *BatchConversionNonpublicPostRecordsByPostIdsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchConversionNonpublicPostRecordsByPostIdsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchConversionNonpublicPostRecordsByPostIdsRequest proto.InternalMessageInfo

func (m *BatchConversionNonpublicPostRecordsByPostIdsRequest) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

type BatchConversionNonpublicPostRecordsByPostIdsResponse struct {
	ConversionNonPublicPostMap map[string]*ConversionNonPublicPostInfo `protobuf:"bytes,1,rep,name=ConversionNonPublicPostMap,proto3" json:"ConversionNonPublicPostMap,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral       struct{}                                `json:"-"`
	XXX_unrecognized           []byte                                  `json:"-"`
	XXX_sizecache              int32                                   `json:"-"`
}

func (m *BatchConversionNonpublicPostRecordsByPostIdsResponse) Reset() {
	*m = BatchConversionNonpublicPostRecordsByPostIdsResponse{}
}
func (m *BatchConversionNonpublicPostRecordsByPostIdsResponse) String() string {
	return proto.CompactTextString(m)
}
func (*BatchConversionNonpublicPostRecordsByPostIdsResponse) ProtoMessage() {}
func (*BatchConversionNonpublicPostRecordsByPostIdsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_content_787bf6d395c36388, []int{143}
}
func (m *BatchConversionNonpublicPostRecordsByPostIdsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchConversionNonpublicPostRecordsByPostIdsResponse.Unmarshal(m, b)
}
func (m *BatchConversionNonpublicPostRecordsByPostIdsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchConversionNonpublicPostRecordsByPostIdsResponse.Marshal(b, m, deterministic)
}
func (dst *BatchConversionNonpublicPostRecordsByPostIdsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchConversionNonpublicPostRecordsByPostIdsResponse.Merge(dst, src)
}
func (m *BatchConversionNonpublicPostRecordsByPostIdsResponse) XXX_Size() int {
	return xxx_messageInfo_BatchConversionNonpublicPostRecordsByPostIdsResponse.Size(m)
}
func (m *BatchConversionNonpublicPostRecordsByPostIdsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchConversionNonpublicPostRecordsByPostIdsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchConversionNonpublicPostRecordsByPostIdsResponse proto.InternalMessageInfo

func (m *BatchConversionNonpublicPostRecordsByPostIdsResponse) GetConversionNonPublicPostMap() map[string]*ConversionNonPublicPostInfo {
	if m != nil {
		return m.ConversionNonPublicPostMap
	}
	return nil
}

func init() {
	proto.RegisterType((*AttachmentInfo)(nil), "ugc.content.AttachmentInfo")
	proto.RegisterType((*PostInfo)(nil), "ugc.content.PostInfo")
	proto.RegisterType((*CommentInfo)(nil), "ugc.content.CommentInfo")
	proto.RegisterType((*Location)(nil), "ugc.content.Location")
	proto.RegisterType((*BatchGetPostListByIdReq)(nil), "ugc.content.BatchGetPostListByIdReq")
	proto.RegisterType((*BatchGetPostListByIdResp)(nil), "ugc.content.BatchGetPostListByIdResp")
	proto.RegisterType((*AddPostDirectlyReq)(nil), "ugc.content.AddPostDirectlyReq")
	proto.RegisterType((*AddPostDirectlyResp)(nil), "ugc.content.AddPostDirectlyResp")
	proto.RegisterType((*GeneralContent)(nil), "ugc.content.GeneralContent")
	proto.RegisterType((*AddPostDirectlyByBussReq)(nil), "ugc.content.AddPostDirectlyByBussReq")
	proto.RegisterType((*AddPostDirectlyByBussResp)(nil), "ugc.content.AddPostDirectlyByBussResp")
	proto.RegisterType((*UpdatePostGeneralContentsReq)(nil), "ugc.content.UpdatePostGeneralContentsReq")
	proto.RegisterType((*UpdatePostGeneralContentsResp)(nil), "ugc.content.UpdatePostGeneralContentsResp")
	proto.RegisterType((*CommonTopicInfo)(nil), "ugc.content.CommonTopicInfo")
	proto.RegisterType((*AddPostReq)(nil), "ugc.content.AddPostReq")
	proto.RegisterType((*MoodInfo)(nil), "ugc.content.MoodInfo")
	proto.RegisterType((*Extra)(nil), "ugc.content.Extra")
	proto.RegisterType((*AddPostResp)(nil), "ugc.content.AddPostResp")
	proto.RegisterType((*MarkAttachmentUploadedReq)(nil), "ugc.content.MarkAttachmentUploadedReq")
	proto.RegisterType((*MarkAttachmentUploadedResp)(nil), "ugc.content.MarkAttachmentUploadedResp")
	proto.RegisterType((*BanPostByIdReq)(nil), "ugc.content.BanPostByIdReq")
	proto.RegisterType((*BanPostByIdResp)(nil), "ugc.content.BanPostByIdResp")
	proto.RegisterType((*DelPostReq)(nil), "ugc.content.DelPostReq")
	proto.RegisterType((*DelPostResp)(nil), "ugc.content.DelPostResp")
	proto.RegisterType((*GetPostByIdReq)(nil), "ugc.content.GetPostByIdReq")
	proto.RegisterType((*GetPostByIdResp)(nil), "ugc.content.GetPostByIdResp")
	proto.RegisterType((*GetVMVideoExistReq)(nil), "ugc.content.GetVMVideoExistReq")
	proto.RegisterType((*GetVMVideoExistResp)(nil), "ugc.content.GetVMVideoExistResp")
	proto.RegisterType((*UpdateAttachmentStatusReq)(nil), "ugc.content.UpdateAttachmentStatusReq")
	proto.RegisterType((*UpdateAttachmentStatusResp)(nil), "ugc.content.UpdateAttachmentStatusResp")
	proto.RegisterType((*UpdateVideoUrlReq)(nil), "ugc.content.UpdateVideoUrlReq")
	proto.RegisterType((*UpdateVideoUrlResp)(nil), "ugc.content.UpdateVideoUrlResp")
	proto.RegisterType((*UpdatePostSpecialLabelReq)(nil), "ugc.content.UpdatePostSpecialLabelReq")
	proto.RegisterType((*UpdatePostSpecialLabelResp)(nil), "ugc.content.UpdatePostSpecialLabelResp")
	proto.RegisterType((*ReportPostViewReq)(nil), "ugc.content.ReportPostViewReq")
	proto.RegisterMapType((map[string]ReportPostViewReq_ViewType)(nil), "ugc.content.ReportPostViewReq.PostIdsEntry")
	proto.RegisterType((*ReportPostViewResp)(nil), "ugc.content.ReportPostViewResp")
	proto.RegisterType((*ReportPostViewV2Req)(nil), "ugc.content.ReportPostViewV2Req")
	proto.RegisterType((*ReportPostViewV2Resp)(nil), "ugc.content.ReportPostViewV2Resp")
	proto.RegisterType((*ReportTopicViewCountReq)(nil), "ugc.content.ReportTopicViewCountReq")
	proto.RegisterType((*ReportTopicViewCountRsp)(nil), "ugc.content.ReportTopicViewCountRsp")
	proto.RegisterType((*GetTopicViewCountReq)(nil), "ugc.content.GetTopicViewCountReq")
	proto.RegisterType((*GetTopicViewCountRsp)(nil), "ugc.content.GetTopicViewCountRsp")
	proto.RegisterType((*ReportPostShareReq)(nil), "ugc.content.ReportPostShareReq")
	proto.RegisterType((*ReportPostShareResp)(nil), "ugc.content.ReportPostShareResp")
	proto.RegisterType((*AddCommentReq)(nil), "ugc.content.AddCommentReq")
	proto.RegisterType((*AddCommentResp)(nil), "ugc.content.AddCommentResp")
	proto.RegisterType((*GetCommentListReq)(nil), "ugc.content.GetCommentListReq")
	proto.RegisterType((*GetCommentListResp)(nil), "ugc.content.GetCommentListResp")
	proto.RegisterType((*DelCommentReq)(nil), "ugc.content.DelCommentReq")
	proto.RegisterType((*DelCommentResp)(nil), "ugc.content.DelCommentResp")
	proto.RegisterType((*GetCommentByIdReq)(nil), "ugc.content.GetCommentByIdReq")
	proto.RegisterType((*GetCommentByIdResp)(nil), "ugc.content.GetCommentByIdResp")
	proto.RegisterType((*BatchGetCommentByIdsReq)(nil), "ugc.content.BatchGetCommentByIdsReq")
	proto.RegisterType((*BatchGetCommentByIdsResp)(nil), "ugc.content.BatchGetCommentByIdsResp")
	proto.RegisterMapType((map[string]*CommentInfo)(nil), "ugc.content.BatchGetCommentByIdsResp.CommentInfosEntry")
	proto.RegisterType((*BanCommentByIdReq)(nil), "ugc.content.BanCommentByIdReq")
	proto.RegisterType((*BanCommentByIdResp)(nil), "ugc.content.BanCommentByIdResp")
	proto.RegisterType((*AddAttitudeReq)(nil), "ugc.content.AddAttitudeReq")
	proto.RegisterType((*AddAttitudeResp)(nil), "ugc.content.AddAttitudeResp")
	proto.RegisterType((*DelAttitudeReq)(nil), "ugc.content.DelAttitudeReq")
	proto.RegisterType((*DelAttitudeResp)(nil), "ugc.content.DelAttitudeResp")
	proto.RegisterType((*AttitudeUserInfo)(nil), "ugc.content.AttitudeUserInfo")
	proto.RegisterType((*GetAttitudeUserListReq)(nil), "ugc.content.GetAttitudeUserListReq")
	proto.RegisterType((*GetAttitudeUserListResp)(nil), "ugc.content.GetAttitudeUserListResp")
	proto.RegisterType((*GetUserWonAttitudeCountReq)(nil), "ugc.content.GetUserWonAttitudeCountReq")
	proto.RegisterType((*GetUserWonAttitudeCountResp)(nil), "ugc.content.GetUserWonAttitudeCountResp")
	proto.RegisterType((*CheckUserAttitudeAvailableReq)(nil), "ugc.content.CheckUserAttitudeAvailableReq")
	proto.RegisterType((*CheckUserAttitudeAvailableResp)(nil), "ugc.content.CheckUserAttitudeAvailableResp")
	proto.RegisterType((*AppReportReq)(nil), "ugc.content.AppReportReq")
	proto.RegisterType((*AppReportResp)(nil), "ugc.content.AppReportResp")
	proto.RegisterType((*UpdatePostTagsReq)(nil), "ugc.content.UpdatePostTagsReq")
	proto.RegisterType((*UpdatePostTagsResp)(nil), "ugc.content.UpdatePostTagsResp")
	proto.RegisterType((*GetPostsByFilterReq)(nil), "ugc.content.GetPostsByFilterReq")
	proto.RegisterType((*GetPostsByFilterResp)(nil), "ugc.content.GetPostsByFilterResp")
	proto.RegisterType((*GetPostTagsReq)(nil), "ugc.content.GetPostTagsReq")
	proto.RegisterType((*GetPostTagsResp)(nil), "ugc.content.GetPostTagsResp")
	proto.RegisterType((*PostTag)(nil), "ugc.content.PostTag")
	proto.RegisterType((*BatchGetPostTagsReq)(nil), "ugc.content.BatchGetPostTagsReq")
	proto.RegisterType((*BatchGetPostTagsResp)(nil), "ugc.content.BatchGetPostTagsResp")
	proto.RegisterMapType((map[string]*PostTag)(nil), "ugc.content.BatchGetPostTagsResp.TagsEntry")
	proto.RegisterType((*MarkFirstPostReq)(nil), "ugc.content.MarkFirstPostReq")
	proto.RegisterType((*MarkFirstPostResp)(nil), "ugc.content.MarkFirstPostResp")
	proto.RegisterType((*UpdatePostPrivacyPolicyReq)(nil), "ugc.content.UpdatePostPrivacyPolicyReq")
	proto.RegisterType((*UpdatePostPrivacyPolicyResp)(nil), "ugc.content.UpdatePostPrivacyPolicyResp")
	proto.RegisterType((*GetStrictUserPostedCountReq)(nil), "ugc.content.GetStrictUserPostedCountReq")
	proto.RegisterType((*GetStrictUserPostedCountResp)(nil), "ugc.content.GetStrictUserPostedCountResp")
	proto.RegisterType((*SetUserNewestPostReq)(nil), "ugc.content.SetUserNewestPostReq")
	proto.RegisterType((*SetUserNewestPostResp)(nil), "ugc.content.SetUserNewestPostResp")
	proto.RegisterType((*BatchGetUserNewestPostReq)(nil), "ugc.content.BatchGetUserNewestPostReq")
	proto.RegisterType((*BatchGetUserNewestPostResp)(nil), "ugc.content.BatchGetUserNewestPostResp")
	proto.RegisterMapType((map[uint32]string)(nil), "ugc.content.BatchGetUserNewestPostResp.PostMapEntry")
	proto.RegisterType((*GetCommentsByFilterReq)(nil), "ugc.content.GetCommentsByFilterReq")
	proto.RegisterType((*GetCommentsByFilterResp)(nil), "ugc.content.GetCommentsByFilterResp")
	proto.RegisterType((*UpdateContentStickyStatusReq)(nil), "ugc.content.UpdateContentStickyStatusReq")
	proto.RegisterType((*UpdateContentStickyStatusResp)(nil), "ugc.content.UpdateContentStickyStatusResp")
	proto.RegisterType((*UpdateAttachmentPrivacyReq)(nil), "ugc.content.UpdateAttachmentPrivacyReq")
	proto.RegisterType((*UpdateAttachmentPrivacyResp)(nil), "ugc.content.UpdateAttachmentPrivacyResp")
	proto.RegisterType((*AddStickyContentReq)(nil), "ugc.content.AddStickyContentReq")
	proto.RegisterType((*AddStickyContentResp)(nil), "ugc.content.AddStickyContentResp")
	proto.RegisterType((*RemoveStickyContentReq)(nil), "ugc.content.RemoveStickyContentReq")
	proto.RegisterType((*RemoveStickyContentResp)(nil), "ugc.content.RemoveStickyContentResp")
	proto.RegisterType((*GetStickyContentReq)(nil), "ugc.content.GetStickyContentReq")
	proto.RegisterType((*GetStickyContentResp)(nil), "ugc.content.GetStickyContentResp")
	proto.RegisterType((*StickyContent)(nil), "ugc.content.StickyContent")
	proto.RegisterType((*BatchGetStickyPostReq)(nil), "ugc.content.BatchGetStickyPostReq")
	proto.RegisterType((*BatchGetStickyPostResp)(nil), "ugc.content.BatchGetStickyPostResp")
	proto.RegisterMapType((map[uint32]*StickyContent)(nil), "ugc.content.BatchGetStickyPostResp.StickyMapEntry")
	proto.RegisterType((*GetPostsByTimeSortViewCntReq)(nil), "ugc.content.GetPostsByTimeSortViewCntReq")
	proto.RegisterType((*GetPostsByTimeSortViewCntResp)(nil), "ugc.content.GetPostsByTimeSortViewCntResp")
	proto.RegisterType((*PostWeightInfo)(nil), "ugc.content.PostWeightInfo")
	proto.RegisterType((*UpdatePostWeightInfo)(nil), "ugc.content.UpdatePostWeightInfo")
	proto.RegisterType((*StatusResult)(nil), "ugc.content.StatusResult")
	proto.RegisterType((*BatUpdateWeightReq)(nil), "ugc.content.BatUpdateWeightReq")
	proto.RegisterType((*BatUpdateWeightResp)(nil), "ugc.content.BatUpdateWeightResp")
	proto.RegisterType((*BatGetPostWeightReq)(nil), "ugc.content.BatGetPostWeightReq")
	proto.RegisterType((*PostWeightAllInfo)(nil), "ugc.content.PostWeightAllInfo")
	proto.RegisterType((*BatGetPostWeightResp)(nil), "ugc.content.BatGetPostWeightResp")
	proto.RegisterType((*BatGetPostAndWeightReq)(nil), "ugc.content.BatGetPostAndWeightReq")
	proto.RegisterType((*BatGetPostAndWeightResp)(nil), "ugc.content.BatGetPostAndWeightResp")
	proto.RegisterType((*UpdatePostMachineAuditByIdReq)(nil), "ugc.content.UpdatePostMachineAuditByIdReq")
	proto.RegisterType((*UpdatePostMachineAuditByIdRsp)(nil), "ugc.content.UpdatePostMachineAuditByIdRsp")
	proto.RegisterType((*AppTabFilter)(nil), "ugc.content.AppTabFilter")
	proto.RegisterType((*ForcePostInUserRcmdFeedBaseInfo)(nil), "ugc.content.ForcePostInUserRcmdFeedBaseInfo")
	proto.RegisterType((*ForcePostInUserRcmdFeedTotalInfo)(nil), "ugc.content.ForcePostInUserRcmdFeedTotalInfo")
	proto.RegisterType((*SetForcePostInUserRcmdFeedReq)(nil), "ugc.content.SetForcePostInUserRcmdFeedReq")
	proto.RegisterType((*SetForcePostInUserRcmdFeedResp)(nil), "ugc.content.SetForcePostInUserRcmdFeedResp")
	proto.RegisterType((*GetForcePostInUserRcmdFeedReq)(nil), "ugc.content.GetForcePostInUserRcmdFeedReq")
	proto.RegisterType((*GetForcePostInUserRcmdFeedResp)(nil), "ugc.content.GetForcePostInUserRcmdFeedResp")
	proto.RegisterType((*GetForcePostsInUserRcmdFeedReq)(nil), "ugc.content.GetForcePostsInUserRcmdFeedReq")
	proto.RegisterType((*GetForcePostsInUserRcmdFeedResp)(nil), "ugc.content.GetForcePostsInUserRcmdFeedResp")
	proto.RegisterType((*DelForcePostInUserRcmdFeedReq)(nil), "ugc.content.DelForcePostInUserRcmdFeedReq")
	proto.RegisterType((*DelForcePostInUserRcmdFeedResp)(nil), "ugc.content.DelForcePostInUserRcmdFeedResp")
	proto.RegisterType((*SearchPostByTopicReq)(nil), "ugc.content.SearchPostByTopicReq")
	proto.RegisterType((*SearchPostByTopicResp)(nil), "ugc.content.SearchPostByTopicResp")
	proto.RegisterType((*AssociatePostWithTopicReq)(nil), "ugc.content.AssociatePostWithTopicReq")
	proto.RegisterType((*AssociatePostWithTopicResp)(nil), "ugc.content.AssociatePostWithTopicResp")
	proto.RegisterType((*BatGetLocationByGeoTopicIdReq)(nil), "ugc.content.BatGetLocationByGeoTopicIdReq")
	proto.RegisterType((*BatGetLocationByGeoTopicIdResp)(nil), "ugc.content.BatGetLocationByGeoTopicIdResp")
	proto.RegisterMapType((map[string]string)(nil), "ugc.content.BatGetLocationByGeoTopicIdResp.CityMapEntry")
	proto.RegisterMapType((map[string]string)(nil), "ugc.content.BatGetLocationByGeoTopicIdResp.ProvinceMapEntry")
	proto.RegisterType((*TransPostToSquareReq)(nil), "ugc.content.TransPostToSquareReq")
	proto.RegisterType((*NonPublicPostConversionSquarePostRequest)(nil), "ugc.content.NonPublicPostConversionSquarePostRequest")
	proto.RegisterType((*NonPublicPostConversionSquarePostResponse)(nil), "ugc.content.NonPublicPostConversionSquarePostResponse")
	proto.RegisterMapType((map[string]string)(nil), "ugc.content.NonPublicPostConversionSquarePostResponse.PostIdMapEntry")
	proto.RegisterType((*SearchConversionNonPublicPostRequest)(nil), "ugc.content.SearchConversionNonPublicPostRequest")
	proto.RegisterType((*ConversionNonPublicPostInfo)(nil), "ugc.content.ConversionNonPublicPostInfo")
	proto.RegisterType((*SearchConversionNonPublicPostResponse)(nil), "ugc.content.SearchConversionNonPublicPostResponse")
	proto.RegisterType((*BatchConversionNonPublicPostRequest)(nil), "ugc.content.BatchConversionNonPublicPostRequest")
	proto.RegisterType((*BatchConversionNonPublicPostResponse)(nil), "ugc.content.BatchConversionNonPublicPostResponse")
	proto.RegisterType((*BatchConversionNonpublicPostRecordsByPostIdsRequest)(nil), "ugc.content.BatchConversionNonpublicPostRecordsByPostIdsRequest")
	proto.RegisterType((*BatchConversionNonpublicPostRecordsByPostIdsResponse)(nil), "ugc.content.BatchConversionNonpublicPostRecordsByPostIdsResponse")
	proto.RegisterMapType((map[string]*ConversionNonPublicPostInfo)(nil), "ugc.content.BatchConversionNonpublicPostRecordsByPostIdsResponse.ConversionNonPublicPostMapEntry")
	proto.RegisterEnum("ugc.content.ContentType", ContentType_name, ContentType_value)
	proto.RegisterEnum("ugc.content.PostOrigin", PostOrigin_name, PostOrigin_value)
	proto.RegisterEnum("ugc.content.IsSystem", IsSystem_name, IsSystem_value)
	proto.RegisterEnum("ugc.content.UnmarshalType", UnmarshalType_name, UnmarshalType_value)
	proto.RegisterEnum("ugc.content.ContentOrigin", ContentOrigin_name, ContentOrigin_value)
	proto.RegisterEnum("ugc.content.BusinessType", BusinessType_name, BusinessType_value)
	proto.RegisterEnum("ugc.content.AttachmentDownloadPrivacy", AttachmentDownloadPrivacy_name, AttachmentDownloadPrivacy_value)
	proto.RegisterEnum("ugc.content.PostPrivacyPolicy", PostPrivacyPolicy_name, PostPrivacyPolicy_value)
	proto.RegisterEnum("ugc.content.ZonePolicy", ZonePolicy_name, ZonePolicy_value)
	proto.RegisterEnum("ugc.content.ContentStatus", ContentStatus_name, ContentStatus_value)
	proto.RegisterEnum("ugc.content.StickyStatus", StickyStatus_name, StickyStatus_value)
	proto.RegisterEnum("ugc.content.WeightStatus", WeightStatus_name, WeightStatus_value)
	proto.RegisterEnum("ugc.content.PostMachineStatus", PostMachineStatus_name, PostMachineStatus_value)
	proto.RegisterEnum("ugc.content.ForcePostInUserRcmdFeedStatus", ForcePostInUserRcmdFeedStatus_name, ForcePostInUserRcmdFeedStatus_value)
	proto.RegisterEnum("ugc.content.AttachmentInfo_AttachmentType", AttachmentInfo_AttachmentType_name, AttachmentInfo_AttachmentType_value)
	proto.RegisterEnum("ugc.content.PostInfo_PostType", PostInfo_PostType_name, PostInfo_PostType_value)
	proto.RegisterEnum("ugc.content.AddPostDirectlyReq_Availability", AddPostDirectlyReq_Availability_name, AddPostDirectlyReq_Availability_value)
	proto.RegisterEnum("ugc.content.ReportPostViewReq_ViewType", ReportPostViewReq_ViewType_name, ReportPostViewReq_ViewType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UgcContentClient is the client API for UgcContent service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UgcContentClient interface {
	// 通用
	// 帖子置顶
	AddStickyContent(ctx context.Context, in *AddStickyContentReq, opts ...grpc.CallOption) (*AddStickyContentResp, error)
	RemoveStickyContent(ctx context.Context, in *RemoveStickyContentReq, opts ...grpc.CallOption) (*RemoveStickyContentResp, error)
	GetStickyContent(ctx context.Context, in *GetStickyContentReq, opts ...grpc.CallOption) (*GetStickyContentResp, error)
	BatchGetStickyPost(ctx context.Context, in *BatchGetStickyPostReq, opts ...grpc.CallOption) (*BatchGetStickyPostResp, error)
	// 修改帖子/评论 的置顶字段
	UpdateContentStickyStatus(ctx context.Context, in *UpdateContentStickyStatusReq, opts ...grpc.CallOption) (*UpdateContentStickyStatusResp, error)
	UpdateAttachmentPrivacy(ctx context.Context, in *UpdateAttachmentPrivacyReq, opts ...grpc.CallOption) (*UpdateAttachmentPrivacyResp, error)
	// 帖子
	BatchGetPostListById(ctx context.Context, in *BatchGetPostListByIdReq, opts ...grpc.CallOption) (*BatchGetPostListByIdResp, error)
	AddPost(ctx context.Context, in *AddPostReq, opts ...grpc.CallOption) (*AddPostResp, error)
	MarkAttachmentUploaded(ctx context.Context, in *MarkAttachmentUploadedReq, opts ...grpc.CallOption) (*MarkAttachmentUploadedResp, error)
	GetPostById(ctx context.Context, in *GetPostByIdReq, opts ...grpc.CallOption) (*GetPostByIdResp, error)
	DelPost(ctx context.Context, in *DelPostReq, opts ...grpc.CallOption) (*DelPostResp, error)
	ReportPostView(ctx context.Context, in *ReportPostViewReq, opts ...grpc.CallOption) (*ReportPostViewResp, error)
	ReportPostViewV2(ctx context.Context, in *ReportPostViewV2Req, opts ...grpc.CallOption) (*ReportPostViewV2Resp, error)
	ReportPostShare(ctx context.Context, in *ReportPostShareReq, opts ...grpc.CallOption) (*ReportPostShareResp, error)
	MarkFirstPost(ctx context.Context, in *MarkFirstPostReq, opts ...grpc.CallOption) (*MarkFirstPostResp, error)
	UpdatePostPrivacyPolicy(ctx context.Context, in *UpdatePostPrivacyPolicyReq, opts ...grpc.CallOption) (*UpdatePostPrivacyPolicyResp, error)
	GetStrictUserPostedCount(ctx context.Context, in *GetStrictUserPostedCountReq, opts ...grpc.CallOption) (*GetStrictUserPostedCountResp, error)
	SetUserNewestPost(ctx context.Context, in *SetUserNewestPostReq, opts ...grpc.CallOption) (*SetUserNewestPostResp, error)
	BatchGetUserNewestPost(ctx context.Context, in *BatchGetUserNewestPostReq, opts ...grpc.CallOption) (*BatchGetUserNewestPostResp, error)
	// topic展示
	ReportTopicViewCount(ctx context.Context, in *ReportTopicViewCountReq, opts ...grpc.CallOption) (*ReportTopicViewCountRsp, error)
	GetTopicViewCount(ctx context.Context, in *GetTopicViewCountReq, opts ...grpc.CallOption) (*GetTopicViewCountRsp, error)
	// 评论
	AddComment(ctx context.Context, in *AddCommentReq, opts ...grpc.CallOption) (*AddCommentResp, error)
	GetCommentList(ctx context.Context, in *GetCommentListReq, opts ...grpc.CallOption) (*GetCommentListResp, error)
	GetCommentById(ctx context.Context, in *GetCommentByIdReq, opts ...grpc.CallOption) (*GetCommentByIdResp, error)
	BatchGetCommentByIds(ctx context.Context, in *BatchGetCommentByIdsReq, opts ...grpc.CallOption) (*BatchGetCommentByIdsResp, error)
	DelComment(ctx context.Context, in *DelCommentReq, opts ...grpc.CallOption) (*DelCommentResp, error)
	// 点赞
	AddAttitude(ctx context.Context, in *AddAttitudeReq, opts ...grpc.CallOption) (*AddAttitudeResp, error)
	DelAttitude(ctx context.Context, in *DelAttitudeReq, opts ...grpc.CallOption) (*DelAttitudeResp, error)
	GetAttitudeUserList(ctx context.Context, in *GetAttitudeUserListReq, opts ...grpc.CallOption) (*GetAttitudeUserListResp, error)
	GetUserWonAttitudeCount(ctx context.Context, in *GetUserWonAttitudeCountReq, opts ...grpc.CallOption) (*GetUserWonAttitudeCountResp, error)
	CheckUserAttitudeAvailable(ctx context.Context, in *CheckUserAttitudeAvailableReq, opts ...grpc.CallOption) (*CheckUserAttitudeAvailableResp, error)
	// -------- 运营后台 --------
	// 审核
	UpdateAttachmentStatus(ctx context.Context, in *UpdateAttachmentStatusReq, opts ...grpc.CallOption) (*UpdateAttachmentStatusResp, error)
	// 比如说官方发帖, 跳过等待审核这一步
	AddPostDirectly(ctx context.Context, in *AddPostDirectlyReq, opts ...grpc.CallOption) (*AddPostDirectlyResp, error)
	// 封贴
	BanPostById(ctx context.Context, in *BanPostByIdReq, opts ...grpc.CallOption) (*BanPostByIdResp, error)
	// 封评论
	BanCommentById(ctx context.Context, in *BanCommentByIdReq, opts ...grpc.CallOption) (*BanCommentByIdResp, error)
	// 更新视频的url地址
	UpdateVideoUrl(ctx context.Context, in *UpdateVideoUrlReq, opts ...grpc.CallOption) (*UpdateVideoUrlResp, error)
	// 配置特殊标签
	UpdatePostSpecialLabel(ctx context.Context, in *UpdatePostSpecialLabelReq, opts ...grpc.CallOption) (*UpdatePostSpecialLabelResp, error)
	// 举报
	AppReport(ctx context.Context, in *AppReportReq, opts ...grpc.CallOption) (*AppReportResp, error)
	// 应该是给推荐系统用的打标签
	UpdatePostTags(ctx context.Context, in *UpdatePostTagsReq, opts ...grpc.CallOption) (*UpdatePostTagsResp, error)
	// 根据条件查询帖子列表
	GetPostsByFilter(ctx context.Context, in *GetPostsByFilterReq, opts ...grpc.CallOption) (*GetPostsByFilterResp, error)
	// 获取帖子标签
	GetPostTags(ctx context.Context, in *GetPostTagsReq, opts ...grpc.CallOption) (*GetPostTagsResp, error)
	BatchGetPostTags(ctx context.Context, in *BatchGetPostTagsReq, opts ...grpc.CallOption) (*BatchGetPostTagsResp, error)
	GetCommentsByFilter(ctx context.Context, in *GetCommentsByFilterReq, opts ...grpc.CallOption) (*GetCommentsByFilterResp, error)
	// 根据浏览数来排序
	GetPostsByTimeSortViewCnt(ctx context.Context, in *GetPostsByTimeSortViewCntReq, opts ...grpc.CallOption) (*GetPostsByTimeSortViewCntResp, error)
	// 批量修改提权
	BatUpdateWeight(ctx context.Context, in *BatUpdateWeightReq, opts ...grpc.CallOption) (*BatUpdateWeightResp, error)
	// 批量获取所有提权帖子
	BatGetPostWeight(ctx context.Context, in *BatGetPostWeightReq, opts ...grpc.CallOption) (*BatGetPostWeightResp, error)
	BatGetPostAndWeight(ctx context.Context, in *BatGetPostAndWeightReq, opts ...grpc.CallOption) (*BatGetPostAndWeightResp, error)
	UpdatePostMachineAuditById(ctx context.Context, in *UpdatePostMachineAuditByIdReq, opts ...grpc.CallOption) (*UpdatePostMachineAuditByIdRsp, error)
	// 推荐流强插帖子
	SetForcePostInUserRcmdFeed(ctx context.Context, in *SetForcePostInUserRcmdFeedReq, opts ...grpc.CallOption) (*SetForcePostInUserRcmdFeedResp, error)
	GetForcePostInUserRcmdFeed(ctx context.Context, in *GetForcePostInUserRcmdFeedReq, opts ...grpc.CallOption) (*GetForcePostInUserRcmdFeedResp, error)
	DelForcePostInUserRcmdFeed(ctx context.Context, in *DelForcePostInUserRcmdFeedReq, opts ...grpc.CallOption) (*DelForcePostInUserRcmdFeedResp, error)
	// app 请求
	GetForcePostsInUserRcmdFeed(ctx context.Context, in *GetForcePostsInUserRcmdFeedReq, opts ...grpc.CallOption) (*GetForcePostsInUserRcmdFeedResp, error)
	// 根据话题搜索帖子，运营后台用
	SearchPostByTopic(ctx context.Context, in *SearchPostByTopicReq, opts ...grpc.CallOption) (*SearchPostByTopicResp, error)
	// 帖子关联指定话题，运营后台用
	AssociatePostWithTopic(ctx context.Context, in *AssociatePostWithTopicReq, opts ...grpc.CallOption) (*AssociatePostWithTopicResp, error)
	AddPostDirectlyByBuss(ctx context.Context, in *AddPostDirectlyByBussReq, opts ...grpc.CallOption) (*AddPostDirectlyByBussResp, error)
	UpdatePostGeneralContents(ctx context.Context, in *UpdatePostGeneralContentsReq, opts ...grpc.CallOption) (*UpdatePostGeneralContentsResp, error)
	// 批量根据定位的topicId获取其地理位置信息，提供该接口给推荐查询数据进行回溯
	BatGetLocationByGeoTopicId(ctx context.Context, in *BatGetLocationByGeoTopicIdReq, opts ...grpc.CallOption) (*BatGetLocationByGeoTopicIdResp, error)
	// 私域帖子转换广场帖子
	NonPublicPostConversionSquarePost(ctx context.Context, in *NonPublicPostConversionSquarePostRequest, opts ...grpc.CallOption) (*NonPublicPostConversionSquarePostResponse, error)
	SearchConversionNonPublicPost(ctx context.Context, in *SearchConversionNonPublicPostRequest, opts ...grpc.CallOption) (*SearchConversionNonPublicPostResponse, error)
	BatchConversionNonPublicPost(ctx context.Context, in *BatchConversionNonPublicPostRequest, opts ...grpc.CallOption) (*BatchConversionNonPublicPostResponse, error)
	BatchConversionNonpublicPostRecordsByPostIds(ctx context.Context, in *BatchConversionNonpublicPostRecordsByPostIdsRequest, opts ...grpc.CallOption) (*BatchConversionNonpublicPostRecordsByPostIdsResponse, error)
}

type ugcContentClient struct {
	cc *grpc.ClientConn
}

func NewUgcContentClient(cc *grpc.ClientConn) UgcContentClient {
	return &ugcContentClient{cc}
}

func (c *ugcContentClient) AddStickyContent(ctx context.Context, in *AddStickyContentReq, opts ...grpc.CallOption) (*AddStickyContentResp, error) {
	out := new(AddStickyContentResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/AddStickyContent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) RemoveStickyContent(ctx context.Context, in *RemoveStickyContentReq, opts ...grpc.CallOption) (*RemoveStickyContentResp, error) {
	out := new(RemoveStickyContentResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/RemoveStickyContent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) GetStickyContent(ctx context.Context, in *GetStickyContentReq, opts ...grpc.CallOption) (*GetStickyContentResp, error) {
	out := new(GetStickyContentResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/GetStickyContent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) BatchGetStickyPost(ctx context.Context, in *BatchGetStickyPostReq, opts ...grpc.CallOption) (*BatchGetStickyPostResp, error) {
	out := new(BatchGetStickyPostResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/BatchGetStickyPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) UpdateContentStickyStatus(ctx context.Context, in *UpdateContentStickyStatusReq, opts ...grpc.CallOption) (*UpdateContentStickyStatusResp, error) {
	out := new(UpdateContentStickyStatusResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/UpdateContentStickyStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) UpdateAttachmentPrivacy(ctx context.Context, in *UpdateAttachmentPrivacyReq, opts ...grpc.CallOption) (*UpdateAttachmentPrivacyResp, error) {
	out := new(UpdateAttachmentPrivacyResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/UpdateAttachmentPrivacy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) BatchGetPostListById(ctx context.Context, in *BatchGetPostListByIdReq, opts ...grpc.CallOption) (*BatchGetPostListByIdResp, error) {
	out := new(BatchGetPostListByIdResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/BatchGetPostListById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) AddPost(ctx context.Context, in *AddPostReq, opts ...grpc.CallOption) (*AddPostResp, error) {
	out := new(AddPostResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/AddPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) MarkAttachmentUploaded(ctx context.Context, in *MarkAttachmentUploadedReq, opts ...grpc.CallOption) (*MarkAttachmentUploadedResp, error) {
	out := new(MarkAttachmentUploadedResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/MarkAttachmentUploaded", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) GetPostById(ctx context.Context, in *GetPostByIdReq, opts ...grpc.CallOption) (*GetPostByIdResp, error) {
	out := new(GetPostByIdResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/GetPostById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) DelPost(ctx context.Context, in *DelPostReq, opts ...grpc.CallOption) (*DelPostResp, error) {
	out := new(DelPostResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/DelPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) ReportPostView(ctx context.Context, in *ReportPostViewReq, opts ...grpc.CallOption) (*ReportPostViewResp, error) {
	out := new(ReportPostViewResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/ReportPostView", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) ReportPostViewV2(ctx context.Context, in *ReportPostViewV2Req, opts ...grpc.CallOption) (*ReportPostViewV2Resp, error) {
	out := new(ReportPostViewV2Resp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/ReportPostViewV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) ReportPostShare(ctx context.Context, in *ReportPostShareReq, opts ...grpc.CallOption) (*ReportPostShareResp, error) {
	out := new(ReportPostShareResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/ReportPostShare", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) MarkFirstPost(ctx context.Context, in *MarkFirstPostReq, opts ...grpc.CallOption) (*MarkFirstPostResp, error) {
	out := new(MarkFirstPostResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/MarkFirstPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) UpdatePostPrivacyPolicy(ctx context.Context, in *UpdatePostPrivacyPolicyReq, opts ...grpc.CallOption) (*UpdatePostPrivacyPolicyResp, error) {
	out := new(UpdatePostPrivacyPolicyResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/UpdatePostPrivacyPolicy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) GetStrictUserPostedCount(ctx context.Context, in *GetStrictUserPostedCountReq, opts ...grpc.CallOption) (*GetStrictUserPostedCountResp, error) {
	out := new(GetStrictUserPostedCountResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/GetStrictUserPostedCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) SetUserNewestPost(ctx context.Context, in *SetUserNewestPostReq, opts ...grpc.CallOption) (*SetUserNewestPostResp, error) {
	out := new(SetUserNewestPostResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/SetUserNewestPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) BatchGetUserNewestPost(ctx context.Context, in *BatchGetUserNewestPostReq, opts ...grpc.CallOption) (*BatchGetUserNewestPostResp, error) {
	out := new(BatchGetUserNewestPostResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/BatchGetUserNewestPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) ReportTopicViewCount(ctx context.Context, in *ReportTopicViewCountReq, opts ...grpc.CallOption) (*ReportTopicViewCountRsp, error) {
	out := new(ReportTopicViewCountRsp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/ReportTopicViewCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) GetTopicViewCount(ctx context.Context, in *GetTopicViewCountReq, opts ...grpc.CallOption) (*GetTopicViewCountRsp, error) {
	out := new(GetTopicViewCountRsp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/GetTopicViewCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) AddComment(ctx context.Context, in *AddCommentReq, opts ...grpc.CallOption) (*AddCommentResp, error) {
	out := new(AddCommentResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/AddComment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) GetCommentList(ctx context.Context, in *GetCommentListReq, opts ...grpc.CallOption) (*GetCommentListResp, error) {
	out := new(GetCommentListResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/GetCommentList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) GetCommentById(ctx context.Context, in *GetCommentByIdReq, opts ...grpc.CallOption) (*GetCommentByIdResp, error) {
	out := new(GetCommentByIdResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/GetCommentById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) BatchGetCommentByIds(ctx context.Context, in *BatchGetCommentByIdsReq, opts ...grpc.CallOption) (*BatchGetCommentByIdsResp, error) {
	out := new(BatchGetCommentByIdsResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/BatchGetCommentByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) DelComment(ctx context.Context, in *DelCommentReq, opts ...grpc.CallOption) (*DelCommentResp, error) {
	out := new(DelCommentResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/DelComment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) AddAttitude(ctx context.Context, in *AddAttitudeReq, opts ...grpc.CallOption) (*AddAttitudeResp, error) {
	out := new(AddAttitudeResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/AddAttitude", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) DelAttitude(ctx context.Context, in *DelAttitudeReq, opts ...grpc.CallOption) (*DelAttitudeResp, error) {
	out := new(DelAttitudeResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/DelAttitude", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) GetAttitudeUserList(ctx context.Context, in *GetAttitudeUserListReq, opts ...grpc.CallOption) (*GetAttitudeUserListResp, error) {
	out := new(GetAttitudeUserListResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/GetAttitudeUserList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) GetUserWonAttitudeCount(ctx context.Context, in *GetUserWonAttitudeCountReq, opts ...grpc.CallOption) (*GetUserWonAttitudeCountResp, error) {
	out := new(GetUserWonAttitudeCountResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/GetUserWonAttitudeCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) CheckUserAttitudeAvailable(ctx context.Context, in *CheckUserAttitudeAvailableReq, opts ...grpc.CallOption) (*CheckUserAttitudeAvailableResp, error) {
	out := new(CheckUserAttitudeAvailableResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/CheckUserAttitudeAvailable", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) UpdateAttachmentStatus(ctx context.Context, in *UpdateAttachmentStatusReq, opts ...grpc.CallOption) (*UpdateAttachmentStatusResp, error) {
	out := new(UpdateAttachmentStatusResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/UpdateAttachmentStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) AddPostDirectly(ctx context.Context, in *AddPostDirectlyReq, opts ...grpc.CallOption) (*AddPostDirectlyResp, error) {
	out := new(AddPostDirectlyResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/AddPostDirectly", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) BanPostById(ctx context.Context, in *BanPostByIdReq, opts ...grpc.CallOption) (*BanPostByIdResp, error) {
	out := new(BanPostByIdResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/BanPostById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) BanCommentById(ctx context.Context, in *BanCommentByIdReq, opts ...grpc.CallOption) (*BanCommentByIdResp, error) {
	out := new(BanCommentByIdResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/BanCommentById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) UpdateVideoUrl(ctx context.Context, in *UpdateVideoUrlReq, opts ...grpc.CallOption) (*UpdateVideoUrlResp, error) {
	out := new(UpdateVideoUrlResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/UpdateVideoUrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) UpdatePostSpecialLabel(ctx context.Context, in *UpdatePostSpecialLabelReq, opts ...grpc.CallOption) (*UpdatePostSpecialLabelResp, error) {
	out := new(UpdatePostSpecialLabelResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/UpdatePostSpecialLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) AppReport(ctx context.Context, in *AppReportReq, opts ...grpc.CallOption) (*AppReportResp, error) {
	out := new(AppReportResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/AppReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) UpdatePostTags(ctx context.Context, in *UpdatePostTagsReq, opts ...grpc.CallOption) (*UpdatePostTagsResp, error) {
	out := new(UpdatePostTagsResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/UpdatePostTags", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) GetPostsByFilter(ctx context.Context, in *GetPostsByFilterReq, opts ...grpc.CallOption) (*GetPostsByFilterResp, error) {
	out := new(GetPostsByFilterResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/GetPostsByFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) GetPostTags(ctx context.Context, in *GetPostTagsReq, opts ...grpc.CallOption) (*GetPostTagsResp, error) {
	out := new(GetPostTagsResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/GetPostTags", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) BatchGetPostTags(ctx context.Context, in *BatchGetPostTagsReq, opts ...grpc.CallOption) (*BatchGetPostTagsResp, error) {
	out := new(BatchGetPostTagsResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/BatchGetPostTags", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) GetCommentsByFilter(ctx context.Context, in *GetCommentsByFilterReq, opts ...grpc.CallOption) (*GetCommentsByFilterResp, error) {
	out := new(GetCommentsByFilterResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/GetCommentsByFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) GetPostsByTimeSortViewCnt(ctx context.Context, in *GetPostsByTimeSortViewCntReq, opts ...grpc.CallOption) (*GetPostsByTimeSortViewCntResp, error) {
	out := new(GetPostsByTimeSortViewCntResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/GetPostsByTimeSortViewCnt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) BatUpdateWeight(ctx context.Context, in *BatUpdateWeightReq, opts ...grpc.CallOption) (*BatUpdateWeightResp, error) {
	out := new(BatUpdateWeightResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/BatUpdateWeight", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) BatGetPostWeight(ctx context.Context, in *BatGetPostWeightReq, opts ...grpc.CallOption) (*BatGetPostWeightResp, error) {
	out := new(BatGetPostWeightResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/BatGetPostWeight", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) BatGetPostAndWeight(ctx context.Context, in *BatGetPostAndWeightReq, opts ...grpc.CallOption) (*BatGetPostAndWeightResp, error) {
	out := new(BatGetPostAndWeightResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/BatGetPostAndWeight", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) UpdatePostMachineAuditById(ctx context.Context, in *UpdatePostMachineAuditByIdReq, opts ...grpc.CallOption) (*UpdatePostMachineAuditByIdRsp, error) {
	out := new(UpdatePostMachineAuditByIdRsp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/UpdatePostMachineAuditById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) SetForcePostInUserRcmdFeed(ctx context.Context, in *SetForcePostInUserRcmdFeedReq, opts ...grpc.CallOption) (*SetForcePostInUserRcmdFeedResp, error) {
	out := new(SetForcePostInUserRcmdFeedResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/SetForcePostInUserRcmdFeed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) GetForcePostInUserRcmdFeed(ctx context.Context, in *GetForcePostInUserRcmdFeedReq, opts ...grpc.CallOption) (*GetForcePostInUserRcmdFeedResp, error) {
	out := new(GetForcePostInUserRcmdFeedResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/GetForcePostInUserRcmdFeed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) DelForcePostInUserRcmdFeed(ctx context.Context, in *DelForcePostInUserRcmdFeedReq, opts ...grpc.CallOption) (*DelForcePostInUserRcmdFeedResp, error) {
	out := new(DelForcePostInUserRcmdFeedResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/DelForcePostInUserRcmdFeed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) GetForcePostsInUserRcmdFeed(ctx context.Context, in *GetForcePostsInUserRcmdFeedReq, opts ...grpc.CallOption) (*GetForcePostsInUserRcmdFeedResp, error) {
	out := new(GetForcePostsInUserRcmdFeedResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/GetForcePostsInUserRcmdFeed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) SearchPostByTopic(ctx context.Context, in *SearchPostByTopicReq, opts ...grpc.CallOption) (*SearchPostByTopicResp, error) {
	out := new(SearchPostByTopicResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/SearchPostByTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) AssociatePostWithTopic(ctx context.Context, in *AssociatePostWithTopicReq, opts ...grpc.CallOption) (*AssociatePostWithTopicResp, error) {
	out := new(AssociatePostWithTopicResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/AssociatePostWithTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) AddPostDirectlyByBuss(ctx context.Context, in *AddPostDirectlyByBussReq, opts ...grpc.CallOption) (*AddPostDirectlyByBussResp, error) {
	out := new(AddPostDirectlyByBussResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/AddPostDirectlyByBuss", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) UpdatePostGeneralContents(ctx context.Context, in *UpdatePostGeneralContentsReq, opts ...grpc.CallOption) (*UpdatePostGeneralContentsResp, error) {
	out := new(UpdatePostGeneralContentsResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/UpdatePostGeneralContents", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) BatGetLocationByGeoTopicId(ctx context.Context, in *BatGetLocationByGeoTopicIdReq, opts ...grpc.CallOption) (*BatGetLocationByGeoTopicIdResp, error) {
	out := new(BatGetLocationByGeoTopicIdResp)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/BatGetLocationByGeoTopicId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) NonPublicPostConversionSquarePost(ctx context.Context, in *NonPublicPostConversionSquarePostRequest, opts ...grpc.CallOption) (*NonPublicPostConversionSquarePostResponse, error) {
	out := new(NonPublicPostConversionSquarePostResponse)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/NonPublicPostConversionSquarePost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) SearchConversionNonPublicPost(ctx context.Context, in *SearchConversionNonPublicPostRequest, opts ...grpc.CallOption) (*SearchConversionNonPublicPostResponse, error) {
	out := new(SearchConversionNonPublicPostResponse)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/SearchConversionNonPublicPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) BatchConversionNonPublicPost(ctx context.Context, in *BatchConversionNonPublicPostRequest, opts ...grpc.CallOption) (*BatchConversionNonPublicPostResponse, error) {
	out := new(BatchConversionNonPublicPostResponse)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/BatchConversionNonPublicPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcContentClient) BatchConversionNonpublicPostRecordsByPostIds(ctx context.Context, in *BatchConversionNonpublicPostRecordsByPostIdsRequest, opts ...grpc.CallOption) (*BatchConversionNonpublicPostRecordsByPostIdsResponse, error) {
	out := new(BatchConversionNonpublicPostRecordsByPostIdsResponse)
	err := c.cc.Invoke(ctx, "/ugc.content.UgcContent/BatchConversionNonpublicPostRecordsByPostIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UgcContentServer is the server API for UgcContent service.
type UgcContentServer interface {
	// 通用
	// 帖子置顶
	AddStickyContent(context.Context, *AddStickyContentReq) (*AddStickyContentResp, error)
	RemoveStickyContent(context.Context, *RemoveStickyContentReq) (*RemoveStickyContentResp, error)
	GetStickyContent(context.Context, *GetStickyContentReq) (*GetStickyContentResp, error)
	BatchGetStickyPost(context.Context, *BatchGetStickyPostReq) (*BatchGetStickyPostResp, error)
	// 修改帖子/评论 的置顶字段
	UpdateContentStickyStatus(context.Context, *UpdateContentStickyStatusReq) (*UpdateContentStickyStatusResp, error)
	UpdateAttachmentPrivacy(context.Context, *UpdateAttachmentPrivacyReq) (*UpdateAttachmentPrivacyResp, error)
	// 帖子
	BatchGetPostListById(context.Context, *BatchGetPostListByIdReq) (*BatchGetPostListByIdResp, error)
	AddPost(context.Context, *AddPostReq) (*AddPostResp, error)
	MarkAttachmentUploaded(context.Context, *MarkAttachmentUploadedReq) (*MarkAttachmentUploadedResp, error)
	GetPostById(context.Context, *GetPostByIdReq) (*GetPostByIdResp, error)
	DelPost(context.Context, *DelPostReq) (*DelPostResp, error)
	ReportPostView(context.Context, *ReportPostViewReq) (*ReportPostViewResp, error)
	ReportPostViewV2(context.Context, *ReportPostViewV2Req) (*ReportPostViewV2Resp, error)
	ReportPostShare(context.Context, *ReportPostShareReq) (*ReportPostShareResp, error)
	MarkFirstPost(context.Context, *MarkFirstPostReq) (*MarkFirstPostResp, error)
	UpdatePostPrivacyPolicy(context.Context, *UpdatePostPrivacyPolicyReq) (*UpdatePostPrivacyPolicyResp, error)
	GetStrictUserPostedCount(context.Context, *GetStrictUserPostedCountReq) (*GetStrictUserPostedCountResp, error)
	SetUserNewestPost(context.Context, *SetUserNewestPostReq) (*SetUserNewestPostResp, error)
	BatchGetUserNewestPost(context.Context, *BatchGetUserNewestPostReq) (*BatchGetUserNewestPostResp, error)
	// topic展示
	ReportTopicViewCount(context.Context, *ReportTopicViewCountReq) (*ReportTopicViewCountRsp, error)
	GetTopicViewCount(context.Context, *GetTopicViewCountReq) (*GetTopicViewCountRsp, error)
	// 评论
	AddComment(context.Context, *AddCommentReq) (*AddCommentResp, error)
	GetCommentList(context.Context, *GetCommentListReq) (*GetCommentListResp, error)
	GetCommentById(context.Context, *GetCommentByIdReq) (*GetCommentByIdResp, error)
	BatchGetCommentByIds(context.Context, *BatchGetCommentByIdsReq) (*BatchGetCommentByIdsResp, error)
	DelComment(context.Context, *DelCommentReq) (*DelCommentResp, error)
	// 点赞
	AddAttitude(context.Context, *AddAttitudeReq) (*AddAttitudeResp, error)
	DelAttitude(context.Context, *DelAttitudeReq) (*DelAttitudeResp, error)
	GetAttitudeUserList(context.Context, *GetAttitudeUserListReq) (*GetAttitudeUserListResp, error)
	GetUserWonAttitudeCount(context.Context, *GetUserWonAttitudeCountReq) (*GetUserWonAttitudeCountResp, error)
	CheckUserAttitudeAvailable(context.Context, *CheckUserAttitudeAvailableReq) (*CheckUserAttitudeAvailableResp, error)
	// -------- 运营后台 --------
	// 审核
	UpdateAttachmentStatus(context.Context, *UpdateAttachmentStatusReq) (*UpdateAttachmentStatusResp, error)
	// 比如说官方发帖, 跳过等待审核这一步
	AddPostDirectly(context.Context, *AddPostDirectlyReq) (*AddPostDirectlyResp, error)
	// 封贴
	BanPostById(context.Context, *BanPostByIdReq) (*BanPostByIdResp, error)
	// 封评论
	BanCommentById(context.Context, *BanCommentByIdReq) (*BanCommentByIdResp, error)
	// 更新视频的url地址
	UpdateVideoUrl(context.Context, *UpdateVideoUrlReq) (*UpdateVideoUrlResp, error)
	// 配置特殊标签
	UpdatePostSpecialLabel(context.Context, *UpdatePostSpecialLabelReq) (*UpdatePostSpecialLabelResp, error)
	// 举报
	AppReport(context.Context, *AppReportReq) (*AppReportResp, error)
	// 应该是给推荐系统用的打标签
	UpdatePostTags(context.Context, *UpdatePostTagsReq) (*UpdatePostTagsResp, error)
	// 根据条件查询帖子列表
	GetPostsByFilter(context.Context, *GetPostsByFilterReq) (*GetPostsByFilterResp, error)
	// 获取帖子标签
	GetPostTags(context.Context, *GetPostTagsReq) (*GetPostTagsResp, error)
	BatchGetPostTags(context.Context, *BatchGetPostTagsReq) (*BatchGetPostTagsResp, error)
	GetCommentsByFilter(context.Context, *GetCommentsByFilterReq) (*GetCommentsByFilterResp, error)
	// 根据浏览数来排序
	GetPostsByTimeSortViewCnt(context.Context, *GetPostsByTimeSortViewCntReq) (*GetPostsByTimeSortViewCntResp, error)
	// 批量修改提权
	BatUpdateWeight(context.Context, *BatUpdateWeightReq) (*BatUpdateWeightResp, error)
	// 批量获取所有提权帖子
	BatGetPostWeight(context.Context, *BatGetPostWeightReq) (*BatGetPostWeightResp, error)
	BatGetPostAndWeight(context.Context, *BatGetPostAndWeightReq) (*BatGetPostAndWeightResp, error)
	UpdatePostMachineAuditById(context.Context, *UpdatePostMachineAuditByIdReq) (*UpdatePostMachineAuditByIdRsp, error)
	// 推荐流强插帖子
	SetForcePostInUserRcmdFeed(context.Context, *SetForcePostInUserRcmdFeedReq) (*SetForcePostInUserRcmdFeedResp, error)
	GetForcePostInUserRcmdFeed(context.Context, *GetForcePostInUserRcmdFeedReq) (*GetForcePostInUserRcmdFeedResp, error)
	DelForcePostInUserRcmdFeed(context.Context, *DelForcePostInUserRcmdFeedReq) (*DelForcePostInUserRcmdFeedResp, error)
	// app 请求
	GetForcePostsInUserRcmdFeed(context.Context, *GetForcePostsInUserRcmdFeedReq) (*GetForcePostsInUserRcmdFeedResp, error)
	// 根据话题搜索帖子，运营后台用
	SearchPostByTopic(context.Context, *SearchPostByTopicReq) (*SearchPostByTopicResp, error)
	// 帖子关联指定话题，运营后台用
	AssociatePostWithTopic(context.Context, *AssociatePostWithTopicReq) (*AssociatePostWithTopicResp, error)
	AddPostDirectlyByBuss(context.Context, *AddPostDirectlyByBussReq) (*AddPostDirectlyByBussResp, error)
	UpdatePostGeneralContents(context.Context, *UpdatePostGeneralContentsReq) (*UpdatePostGeneralContentsResp, error)
	// 批量根据定位的topicId获取其地理位置信息，提供该接口给推荐查询数据进行回溯
	BatGetLocationByGeoTopicId(context.Context, *BatGetLocationByGeoTopicIdReq) (*BatGetLocationByGeoTopicIdResp, error)
	// 私域帖子转换广场帖子
	NonPublicPostConversionSquarePost(context.Context, *NonPublicPostConversionSquarePostRequest) (*NonPublicPostConversionSquarePostResponse, error)
	SearchConversionNonPublicPost(context.Context, *SearchConversionNonPublicPostRequest) (*SearchConversionNonPublicPostResponse, error)
	BatchConversionNonPublicPost(context.Context, *BatchConversionNonPublicPostRequest) (*BatchConversionNonPublicPostResponse, error)
	BatchConversionNonpublicPostRecordsByPostIds(context.Context, *BatchConversionNonpublicPostRecordsByPostIdsRequest) (*BatchConversionNonpublicPostRecordsByPostIdsResponse, error)
}

func RegisterUgcContentServer(s *grpc.Server, srv UgcContentServer) {
	s.RegisterService(&_UgcContent_serviceDesc, srv)
}

func _UgcContent_AddStickyContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddStickyContentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).AddStickyContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/AddStickyContent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).AddStickyContent(ctx, req.(*AddStickyContentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_RemoveStickyContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveStickyContentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).RemoveStickyContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/RemoveStickyContent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).RemoveStickyContent(ctx, req.(*RemoveStickyContentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_GetStickyContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStickyContentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).GetStickyContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/GetStickyContent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).GetStickyContent(ctx, req.(*GetStickyContentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_BatchGetStickyPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetStickyPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).BatchGetStickyPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/BatchGetStickyPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).BatchGetStickyPost(ctx, req.(*BatchGetStickyPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_UpdateContentStickyStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateContentStickyStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).UpdateContentStickyStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/UpdateContentStickyStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).UpdateContentStickyStatus(ctx, req.(*UpdateContentStickyStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_UpdateAttachmentPrivacy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAttachmentPrivacyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).UpdateAttachmentPrivacy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/UpdateAttachmentPrivacy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).UpdateAttachmentPrivacy(ctx, req.(*UpdateAttachmentPrivacyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_BatchGetPostListById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetPostListByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).BatchGetPostListById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/BatchGetPostListById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).BatchGetPostListById(ctx, req.(*BatchGetPostListByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_AddPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).AddPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/AddPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).AddPost(ctx, req.(*AddPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_MarkAttachmentUploaded_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkAttachmentUploadedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).MarkAttachmentUploaded(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/MarkAttachmentUploaded",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).MarkAttachmentUploaded(ctx, req.(*MarkAttachmentUploadedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_GetPostById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).GetPostById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/GetPostById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).GetPostById(ctx, req.(*GetPostByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_DelPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).DelPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/DelPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).DelPost(ctx, req.(*DelPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_ReportPostView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportPostViewReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).ReportPostView(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/ReportPostView",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).ReportPostView(ctx, req.(*ReportPostViewReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_ReportPostViewV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportPostViewV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).ReportPostViewV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/ReportPostViewV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).ReportPostViewV2(ctx, req.(*ReportPostViewV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_ReportPostShare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportPostShareReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).ReportPostShare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/ReportPostShare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).ReportPostShare(ctx, req.(*ReportPostShareReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_MarkFirstPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkFirstPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).MarkFirstPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/MarkFirstPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).MarkFirstPost(ctx, req.(*MarkFirstPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_UpdatePostPrivacyPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePostPrivacyPolicyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).UpdatePostPrivacyPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/UpdatePostPrivacyPolicy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).UpdatePostPrivacyPolicy(ctx, req.(*UpdatePostPrivacyPolicyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_GetStrictUserPostedCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStrictUserPostedCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).GetStrictUserPostedCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/GetStrictUserPostedCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).GetStrictUserPostedCount(ctx, req.(*GetStrictUserPostedCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_SetUserNewestPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserNewestPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).SetUserNewestPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/SetUserNewestPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).SetUserNewestPost(ctx, req.(*SetUserNewestPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_BatchGetUserNewestPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserNewestPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).BatchGetUserNewestPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/BatchGetUserNewestPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).BatchGetUserNewestPost(ctx, req.(*BatchGetUserNewestPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_ReportTopicViewCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportTopicViewCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).ReportTopicViewCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/ReportTopicViewCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).ReportTopicViewCount(ctx, req.(*ReportTopicViewCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_GetTopicViewCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicViewCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).GetTopicViewCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/GetTopicViewCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).GetTopicViewCount(ctx, req.(*GetTopicViewCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_AddComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCommentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).AddComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/AddComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).AddComment(ctx, req.(*AddCommentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_GetCommentList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommentListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).GetCommentList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/GetCommentList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).GetCommentList(ctx, req.(*GetCommentListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_GetCommentById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommentByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).GetCommentById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/GetCommentById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).GetCommentById(ctx, req.(*GetCommentByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_BatchGetCommentByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetCommentByIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).BatchGetCommentByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/BatchGetCommentByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).BatchGetCommentByIds(ctx, req.(*BatchGetCommentByIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_DelComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelCommentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).DelComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/DelComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).DelComment(ctx, req.(*DelCommentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_AddAttitude_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAttitudeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).AddAttitude(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/AddAttitude",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).AddAttitude(ctx, req.(*AddAttitudeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_DelAttitude_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelAttitudeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).DelAttitude(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/DelAttitude",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).DelAttitude(ctx, req.(*DelAttitudeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_GetAttitudeUserList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAttitudeUserListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).GetAttitudeUserList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/GetAttitudeUserList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).GetAttitudeUserList(ctx, req.(*GetAttitudeUserListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_GetUserWonAttitudeCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserWonAttitudeCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).GetUserWonAttitudeCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/GetUserWonAttitudeCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).GetUserWonAttitudeCount(ctx, req.(*GetUserWonAttitudeCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_CheckUserAttitudeAvailable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserAttitudeAvailableReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).CheckUserAttitudeAvailable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/CheckUserAttitudeAvailable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).CheckUserAttitudeAvailable(ctx, req.(*CheckUserAttitudeAvailableReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_UpdateAttachmentStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAttachmentStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).UpdateAttachmentStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/UpdateAttachmentStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).UpdateAttachmentStatus(ctx, req.(*UpdateAttachmentStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_AddPostDirectly_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPostDirectlyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).AddPostDirectly(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/AddPostDirectly",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).AddPostDirectly(ctx, req.(*AddPostDirectlyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_BanPostById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BanPostByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).BanPostById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/BanPostById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).BanPostById(ctx, req.(*BanPostByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_BanCommentById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BanCommentByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).BanCommentById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/BanCommentById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).BanCommentById(ctx, req.(*BanCommentByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_UpdateVideoUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateVideoUrlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).UpdateVideoUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/UpdateVideoUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).UpdateVideoUrl(ctx, req.(*UpdateVideoUrlReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_UpdatePostSpecialLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePostSpecialLabelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).UpdatePostSpecialLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/UpdatePostSpecialLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).UpdatePostSpecialLabel(ctx, req.(*UpdatePostSpecialLabelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_AppReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AppReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).AppReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/AppReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).AppReport(ctx, req.(*AppReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_UpdatePostTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePostTagsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).UpdatePostTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/UpdatePostTags",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).UpdatePostTags(ctx, req.(*UpdatePostTagsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_GetPostsByFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostsByFilterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).GetPostsByFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/GetPostsByFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).GetPostsByFilter(ctx, req.(*GetPostsByFilterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_GetPostTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostTagsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).GetPostTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/GetPostTags",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).GetPostTags(ctx, req.(*GetPostTagsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_BatchGetPostTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetPostTagsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).BatchGetPostTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/BatchGetPostTags",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).BatchGetPostTags(ctx, req.(*BatchGetPostTagsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_GetCommentsByFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommentsByFilterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).GetCommentsByFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/GetCommentsByFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).GetCommentsByFilter(ctx, req.(*GetCommentsByFilterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_GetPostsByTimeSortViewCnt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostsByTimeSortViewCntReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).GetPostsByTimeSortViewCnt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/GetPostsByTimeSortViewCnt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).GetPostsByTimeSortViewCnt(ctx, req.(*GetPostsByTimeSortViewCntReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_BatUpdateWeight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatUpdateWeightReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).BatUpdateWeight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/BatUpdateWeight",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).BatUpdateWeight(ctx, req.(*BatUpdateWeightReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_BatGetPostWeight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetPostWeightReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).BatGetPostWeight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/BatGetPostWeight",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).BatGetPostWeight(ctx, req.(*BatGetPostWeightReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_BatGetPostAndWeight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetPostAndWeightReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).BatGetPostAndWeight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/BatGetPostAndWeight",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).BatGetPostAndWeight(ctx, req.(*BatGetPostAndWeightReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_UpdatePostMachineAuditById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePostMachineAuditByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).UpdatePostMachineAuditById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/UpdatePostMachineAuditById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).UpdatePostMachineAuditById(ctx, req.(*UpdatePostMachineAuditByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_SetForcePostInUserRcmdFeed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetForcePostInUserRcmdFeedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).SetForcePostInUserRcmdFeed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/SetForcePostInUserRcmdFeed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).SetForcePostInUserRcmdFeed(ctx, req.(*SetForcePostInUserRcmdFeedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_GetForcePostInUserRcmdFeed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetForcePostInUserRcmdFeedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).GetForcePostInUserRcmdFeed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/GetForcePostInUserRcmdFeed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).GetForcePostInUserRcmdFeed(ctx, req.(*GetForcePostInUserRcmdFeedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_DelForcePostInUserRcmdFeed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelForcePostInUserRcmdFeedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).DelForcePostInUserRcmdFeed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/DelForcePostInUserRcmdFeed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).DelForcePostInUserRcmdFeed(ctx, req.(*DelForcePostInUserRcmdFeedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_GetForcePostsInUserRcmdFeed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetForcePostsInUserRcmdFeedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).GetForcePostsInUserRcmdFeed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/GetForcePostsInUserRcmdFeed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).GetForcePostsInUserRcmdFeed(ctx, req.(*GetForcePostsInUserRcmdFeedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_SearchPostByTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchPostByTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).SearchPostByTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/SearchPostByTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).SearchPostByTopic(ctx, req.(*SearchPostByTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_AssociatePostWithTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssociatePostWithTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).AssociatePostWithTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/AssociatePostWithTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).AssociatePostWithTopic(ctx, req.(*AssociatePostWithTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_AddPostDirectlyByBuss_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPostDirectlyByBussReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).AddPostDirectlyByBuss(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/AddPostDirectlyByBuss",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).AddPostDirectlyByBuss(ctx, req.(*AddPostDirectlyByBussReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_UpdatePostGeneralContents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePostGeneralContentsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).UpdatePostGeneralContents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/UpdatePostGeneralContents",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).UpdatePostGeneralContents(ctx, req.(*UpdatePostGeneralContentsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_BatGetLocationByGeoTopicId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetLocationByGeoTopicIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).BatGetLocationByGeoTopicId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/BatGetLocationByGeoTopicId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).BatGetLocationByGeoTopicId(ctx, req.(*BatGetLocationByGeoTopicIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_NonPublicPostConversionSquarePost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NonPublicPostConversionSquarePostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).NonPublicPostConversionSquarePost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/NonPublicPostConversionSquarePost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).NonPublicPostConversionSquarePost(ctx, req.(*NonPublicPostConversionSquarePostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_SearchConversionNonPublicPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchConversionNonPublicPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).SearchConversionNonPublicPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/SearchConversionNonPublicPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).SearchConversionNonPublicPost(ctx, req.(*SearchConversionNonPublicPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_BatchConversionNonPublicPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchConversionNonPublicPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).BatchConversionNonPublicPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/BatchConversionNonPublicPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).BatchConversionNonPublicPost(ctx, req.(*BatchConversionNonPublicPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcContent_BatchConversionNonpublicPostRecordsByPostIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchConversionNonpublicPostRecordsByPostIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcContentServer).BatchConversionNonpublicPostRecordsByPostIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.content.UgcContent/BatchConversionNonpublicPostRecordsByPostIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcContentServer).BatchConversionNonpublicPostRecordsByPostIds(ctx, req.(*BatchConversionNonpublicPostRecordsByPostIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _UgcContent_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ugc.content.UgcContent",
	HandlerType: (*UgcContentServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddStickyContent",
			Handler:    _UgcContent_AddStickyContent_Handler,
		},
		{
			MethodName: "RemoveStickyContent",
			Handler:    _UgcContent_RemoveStickyContent_Handler,
		},
		{
			MethodName: "GetStickyContent",
			Handler:    _UgcContent_GetStickyContent_Handler,
		},
		{
			MethodName: "BatchGetStickyPost",
			Handler:    _UgcContent_BatchGetStickyPost_Handler,
		},
		{
			MethodName: "UpdateContentStickyStatus",
			Handler:    _UgcContent_UpdateContentStickyStatus_Handler,
		},
		{
			MethodName: "UpdateAttachmentPrivacy",
			Handler:    _UgcContent_UpdateAttachmentPrivacy_Handler,
		},
		{
			MethodName: "BatchGetPostListById",
			Handler:    _UgcContent_BatchGetPostListById_Handler,
		},
		{
			MethodName: "AddPost",
			Handler:    _UgcContent_AddPost_Handler,
		},
		{
			MethodName: "MarkAttachmentUploaded",
			Handler:    _UgcContent_MarkAttachmentUploaded_Handler,
		},
		{
			MethodName: "GetPostById",
			Handler:    _UgcContent_GetPostById_Handler,
		},
		{
			MethodName: "DelPost",
			Handler:    _UgcContent_DelPost_Handler,
		},
		{
			MethodName: "ReportPostView",
			Handler:    _UgcContent_ReportPostView_Handler,
		},
		{
			MethodName: "ReportPostViewV2",
			Handler:    _UgcContent_ReportPostViewV2_Handler,
		},
		{
			MethodName: "ReportPostShare",
			Handler:    _UgcContent_ReportPostShare_Handler,
		},
		{
			MethodName: "MarkFirstPost",
			Handler:    _UgcContent_MarkFirstPost_Handler,
		},
		{
			MethodName: "UpdatePostPrivacyPolicy",
			Handler:    _UgcContent_UpdatePostPrivacyPolicy_Handler,
		},
		{
			MethodName: "GetStrictUserPostedCount",
			Handler:    _UgcContent_GetStrictUserPostedCount_Handler,
		},
		{
			MethodName: "SetUserNewestPost",
			Handler:    _UgcContent_SetUserNewestPost_Handler,
		},
		{
			MethodName: "BatchGetUserNewestPost",
			Handler:    _UgcContent_BatchGetUserNewestPost_Handler,
		},
		{
			MethodName: "ReportTopicViewCount",
			Handler:    _UgcContent_ReportTopicViewCount_Handler,
		},
		{
			MethodName: "GetTopicViewCount",
			Handler:    _UgcContent_GetTopicViewCount_Handler,
		},
		{
			MethodName: "AddComment",
			Handler:    _UgcContent_AddComment_Handler,
		},
		{
			MethodName: "GetCommentList",
			Handler:    _UgcContent_GetCommentList_Handler,
		},
		{
			MethodName: "GetCommentById",
			Handler:    _UgcContent_GetCommentById_Handler,
		},
		{
			MethodName: "BatchGetCommentByIds",
			Handler:    _UgcContent_BatchGetCommentByIds_Handler,
		},
		{
			MethodName: "DelComment",
			Handler:    _UgcContent_DelComment_Handler,
		},
		{
			MethodName: "AddAttitude",
			Handler:    _UgcContent_AddAttitude_Handler,
		},
		{
			MethodName: "DelAttitude",
			Handler:    _UgcContent_DelAttitude_Handler,
		},
		{
			MethodName: "GetAttitudeUserList",
			Handler:    _UgcContent_GetAttitudeUserList_Handler,
		},
		{
			MethodName: "GetUserWonAttitudeCount",
			Handler:    _UgcContent_GetUserWonAttitudeCount_Handler,
		},
		{
			MethodName: "CheckUserAttitudeAvailable",
			Handler:    _UgcContent_CheckUserAttitudeAvailable_Handler,
		},
		{
			MethodName: "UpdateAttachmentStatus",
			Handler:    _UgcContent_UpdateAttachmentStatus_Handler,
		},
		{
			MethodName: "AddPostDirectly",
			Handler:    _UgcContent_AddPostDirectly_Handler,
		},
		{
			MethodName: "BanPostById",
			Handler:    _UgcContent_BanPostById_Handler,
		},
		{
			MethodName: "BanCommentById",
			Handler:    _UgcContent_BanCommentById_Handler,
		},
		{
			MethodName: "UpdateVideoUrl",
			Handler:    _UgcContent_UpdateVideoUrl_Handler,
		},
		{
			MethodName: "UpdatePostSpecialLabel",
			Handler:    _UgcContent_UpdatePostSpecialLabel_Handler,
		},
		{
			MethodName: "AppReport",
			Handler:    _UgcContent_AppReport_Handler,
		},
		{
			MethodName: "UpdatePostTags",
			Handler:    _UgcContent_UpdatePostTags_Handler,
		},
		{
			MethodName: "GetPostsByFilter",
			Handler:    _UgcContent_GetPostsByFilter_Handler,
		},
		{
			MethodName: "GetPostTags",
			Handler:    _UgcContent_GetPostTags_Handler,
		},
		{
			MethodName: "BatchGetPostTags",
			Handler:    _UgcContent_BatchGetPostTags_Handler,
		},
		{
			MethodName: "GetCommentsByFilter",
			Handler:    _UgcContent_GetCommentsByFilter_Handler,
		},
		{
			MethodName: "GetPostsByTimeSortViewCnt",
			Handler:    _UgcContent_GetPostsByTimeSortViewCnt_Handler,
		},
		{
			MethodName: "BatUpdateWeight",
			Handler:    _UgcContent_BatUpdateWeight_Handler,
		},
		{
			MethodName: "BatGetPostWeight",
			Handler:    _UgcContent_BatGetPostWeight_Handler,
		},
		{
			MethodName: "BatGetPostAndWeight",
			Handler:    _UgcContent_BatGetPostAndWeight_Handler,
		},
		{
			MethodName: "UpdatePostMachineAuditById",
			Handler:    _UgcContent_UpdatePostMachineAuditById_Handler,
		},
		{
			MethodName: "SetForcePostInUserRcmdFeed",
			Handler:    _UgcContent_SetForcePostInUserRcmdFeed_Handler,
		},
		{
			MethodName: "GetForcePostInUserRcmdFeed",
			Handler:    _UgcContent_GetForcePostInUserRcmdFeed_Handler,
		},
		{
			MethodName: "DelForcePostInUserRcmdFeed",
			Handler:    _UgcContent_DelForcePostInUserRcmdFeed_Handler,
		},
		{
			MethodName: "GetForcePostsInUserRcmdFeed",
			Handler:    _UgcContent_GetForcePostsInUserRcmdFeed_Handler,
		},
		{
			MethodName: "SearchPostByTopic",
			Handler:    _UgcContent_SearchPostByTopic_Handler,
		},
		{
			MethodName: "AssociatePostWithTopic",
			Handler:    _UgcContent_AssociatePostWithTopic_Handler,
		},
		{
			MethodName: "AddPostDirectlyByBuss",
			Handler:    _UgcContent_AddPostDirectlyByBuss_Handler,
		},
		{
			MethodName: "UpdatePostGeneralContents",
			Handler:    _UgcContent_UpdatePostGeneralContents_Handler,
		},
		{
			MethodName: "BatGetLocationByGeoTopicId",
			Handler:    _UgcContent_BatGetLocationByGeoTopicId_Handler,
		},
		{
			MethodName: "NonPublicPostConversionSquarePost",
			Handler:    _UgcContent_NonPublicPostConversionSquarePost_Handler,
		},
		{
			MethodName: "SearchConversionNonPublicPost",
			Handler:    _UgcContent_SearchConversionNonPublicPost_Handler,
		},
		{
			MethodName: "BatchConversionNonPublicPost",
			Handler:    _UgcContent_BatchConversionNonPublicPost_Handler,
		},
		{
			MethodName: "BatchConversionNonpublicPostRecordsByPostIds",
			Handler:    _UgcContent_BatchConversionNonpublicPostRecordsByPostIds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ugc/content.proto",
}

func init() { proto.RegisterFile("ugc/content.proto", fileDescriptor_content_787bf6d395c36388) }

var fileDescriptor_content_787bf6d395c36388 = []byte{
	// 7989 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x7d, 0x4b, 0x6c, 0x23, 0x49,
	0x96, 0x98, 0x92, 0x94, 0x44, 0xf2, 0x51, 0x94, 0x52, 0x21, 0x95, 0xc4, 0xa2, 0x4a, 0x25, 0x75,
	0x56, 0x75, 0x97, 0x4a, 0x55, 0x5d, 0x3d, 0x5d, 0x3d, 0xd3, 0x33, 0x3b, 0xdb, 0xd3, 0x3b, 0x94,
	0xc4, 0x52, 0x73, 0x5a, 0xa2, 0xb4, 0x49, 0xaa, 0x7a, 0x6b, 0xbc, 0x40, 0x22, 0xc5, 0x4c, 0x49,
	0xb9, 0x45, 0x66, 0x66, 0x33, 0x92, 0xaa, 0xd2, 0xd8, 0x27, 0xc3, 0xb3, 0xde, 0x8b, 0x0f, 0x6b,
	0xd8, 0x06, 0xf6, 0xb8, 0x6b, 0x60, 0x01, 0x1b, 0xfe, 0x1c, 0x0c, 0x1f, 0x16, 0xf0, 0x1e, 0xf7,
	0x66, 0x03, 0x0b, 0x2f, 0x7c, 0xf0, 0xc5, 0x80, 0x6f, 0xf6, 0xc9, 0x27, 0x03, 0x36, 0x6c, 0x03,
	0x46, 0x7c, 0x32, 0x33, 0xf2, 0x4b, 0xaa, 0xaa, 0x67, 0x30, 0x27, 0x31, 0x5f, 0xbc, 0xf8, 0xbd,
	0x78, 0xf1, 0x7e, 0xf1, 0x22, 0x04, 0xcb, 0xe3, 0xcb, 0xfe, 0x27, 0x7d, 0xc7, 0xf6, 0x4c, 0xdb,
	0x7b, 0xe6, 0x8e, 0x1c, 0xcf, 0x41, 0xd5, 0xf1, 0x65, 0xff, 0x19, 0x07, 0x29, 0x7f, 0x59, 0x84,
	0xc5, 0xa6, 0xe7, 0xe9, 0xfd, 0xab, 0xa1, 0x69, 0x7b, 0x6d, 0xfb, 0xc2, 0x41, 0x32, 0x14, 0x5f,
	0x9b, 0x37, 0x75, 0x69, 0x5b, 0xda, 0xa9, 0xa8, 0xe4, 0x27, 0xfa, 0x12, 0x66, 0xbd, 0x1b, 0xd7,
	0xac, 0x17, 0xb6, 0xa5, 0x9d, 0xc5, 0xe7, 0xbb, 0xcf, 0x84, 0x06, 0x9e, 0x45, 0x2b, 0x0b, 0x9f,
	0xbd, 0x1b, 0xd7, 0x54, 0x69, 0x3d, 0x54, 0x87, 0x12, 0x47, 0xaf, 0x17, 0x69, 0xab, 0xfe, 0x27,
	0x5a, 0x85, 0x39, 0xf3, 0xad, 0x37, 0xd2, 0xeb, 0xb3, 0x14, 0xce, 0x3e, 0xd0, 0x73, 0x98, 0xc7,
	0x9e, 0xee, 0x8d, 0x71, 0x7d, 0x8e, 0xf6, 0xd8, 0x88, 0xf4, 0xb8, 0xcf, 0xfe, 0x76, 0x29, 0x86,
	0xca, 0x31, 0xd1, 0x26, 0xc0, 0xf5, 0x50, 0xf3, 0xbb, 0x99, 0xa7, 0xcd, 0x55, 0xae, 0x87, 0xfb,
	0x61, 0x47, 0xae, 0x3e, 0xd2, 0x87, 0x75, 0x60, 0x1d, 0xd1, 0x0f, 0xf4, 0x14, 0x90, 0x33, 0xb2,
	0x2e, 0x2d, 0x5b, 0xbb, 0xb6, 0x0c, 0xd3, 0xd1, 0xfa, 0xce, 0xb5, 0x39, 0xaa, 0x57, 0x29, 0x8a,
	0xcc, 0x4a, 0x5e, 0x92, 0x82, 0x7d, 0x02, 0x57, 0xfe, 0x58, 0x12, 0x69, 0x45, 0xe6, 0x87, 0xca,
	0x30, 0xdb, 0x39, 0xe9, 0xb4, 0xe4, 0x19, 0x54, 0x81, 0xb9, 0xf6, 0x71, 0xf3, 0xb0, 0x25, 0x4b,
	0xa8, 0x04, 0xc5, 0xc3, 0xf6, 0x0b, 0xb9, 0x40, 0x60, 0x2f, 0xdb, 0x07, 0xad, 0x13, 0xb9, 0x48,
	0x60, 0xfb, 0xc7, 0x5d, 0x79, 0x96, 0xc0, 0x9a, 0x67, 0x07, 0xed, 0x13, 0x79, 0x8e, 0x54, 0xee,
	0xb5, 0x7e, 0xaf, 0x27, 0xcf, 0xa3, 0x2a, 0x94, 0xce, 0xd4, 0xa3, 0x7d, 0x7d, 0x64, 0xc8, 0x25,
	0x02, 0x7e, 0xe9, 0x78, 0xa6, 0x5c, 0x46, 0x00, 0xf3, 0x4d, 0xef, 0x0c, 0x9b, 0x23, 0xb9, 0x82,
	0xee, 0xc0, 0x72, 0xd3, 0xeb, 0x3a, 0x7d, 0x4b, 0x1f, 0xec, 0x3b, 0xc3, 0xe1, 0xd8, 0xb6, 0xbc,
	0x1b, 0x19, 0x94, 0xbf, 0x58, 0x82, 0xf2, 0xa9, 0x83, 0xd9, 0xca, 0xad, 0x43, 0xc9, 0x75, 0xb0,
	0xa7, 0x59, 0x06, 0x5f, 0xbd, 0x79, 0xf2, 0xd9, 0x36, 0xd0, 0x5d, 0x28, 0x7b, 0x8e, 0x6b, 0xf5,
	0x49, 0x49, 0x81, 0xad, 0x00, 0xfd, 0x6e, 0x1b, 0xe8, 0xb7, 0xa1, 0x42, 0xeb, 0xd0, 0x05, 0x2e,
	0x52, 0x72, 0xdf, 0x8f, 0x90, 0xdb, 0x6f, 0x9d, 0xfe, 0xa0, 0x8b, 0x5a, 0x76, 0xf9, 0x2f, 0x71,
	0x61, 0x67, 0xa3, 0x0b, 0xfb, 0x13, 0xa8, 0xea, 0x01, 0xa9, 0xc8, 0x3a, 0x16, 0x77, 0xaa, 0xcf,
	0x37, 0x72, 0x38, 0x47, 0x15, 0xf1, 0xd1, 0x06, 0x54, 0xfa, 0x23, 0x53, 0xf7, 0x4c, 0x4d, 0x67,
	0x8b, 0x39, 0xab, 0x96, 0x19, 0xa0, 0xe9, 0xa1, 0x07, 0x50, 0xeb, 0x3b, 0x43, 0x82, 0xa8, 0xf5,
	0x9d, 0xb1, 0xed, 0xd5, 0x4b, 0xdb, 0xd2, 0x4e, 0x4d, 0x5d, 0xe0, 0xc0, 0x7d, 0x02, 0x43, 0x1f,
	0xc2, 0xa2, 0xee, 0x79, 0x96, 0x37, 0x36, 0x4c, 0x8e, 0x55, 0xa6, 0x58, 0x35, 0x1f, 0xca, 0xd0,
	0x08, 0xdb, 0x58, 0xe6, 0x1b, 0x8e, 0x52, 0xa1, 0x28, 0x15, 0x02, 0x61, 0xc5, 0xeb, 0x50, 0x1a,
	0x63, 0x73, 0x44, 0xe8, 0x06, 0xb4, 0x6c, 0x9e, 0x7c, 0xb6, 0x0d, 0x81, 0x45, 0xab, 0x53, 0xb3,
	0xe8, 0x0f, 0x60, 0xdd, 0x73, 0x5c, 0x6d, 0x60, 0x5e, 0x9b, 0x03, 0x2d, 0x3a, 0x83, 0x05, 0xda,
	0xf8, 0xaa, 0xe7, 0xb8, 0x47, 0xa4, 0x74, 0x5f, 0x9c, 0xc9, 0x16, 0x54, 0xf1, 0x95, 0x3e, 0xf2,
	0xa7, 0x51, 0xa3, 0xa8, 0x40, 0x41, 0x0c, 0x61, 0x15, 0xe6, 0x06, 0xfa, 0xb9, 0x39, 0xa8, 0x2f,
	0x32, 0xde, 0xa6, 0x1f, 0xe8, 0x7b, 0xb0, 0x3a, 0xd4, 0x2f, 0x6d, 0xeb, 0xc2, 0x32, 0x0d, 0x4d,
	0x98, 0xe3, 0x12, 0xad, 0x8f, 0x82, 0xb2, 0x97, 0xc1, 0x64, 0xb7, 0x61, 0x01, 0x8f, 0xcf, 0xb5,
	0x80, 0x53, 0x64, 0xda, 0x1c, 0xe0, 0xf1, 0x79, 0x8f, 0x33, 0x0b, 0x82, 0x59, 0x4f, 0xbf, 0xc4,
	0xf5, 0xe5, 0xed, 0xe2, 0x4e, 0x4d, 0xa5, 0xbf, 0xd1, 0x07, 0xb0, 0xc0, 0xa7, 0xcd, 0x78, 0x08,
	0x6d, 0x4b, 0x3b, 0x73, 0x6a, 0x95, 0xc3, 0x28, 0x9b, 0x7c, 0x09, 0x35, 0xec, 0x59, 0xfd, 0xd7,
	0x37, 0x1a, 0xa7, 0xd9, 0x0a, 0xa5, 0xd9, 0xdd, 0x08, 0xcd, 0xba, 0x14, 0x83, 0x93, 0x6c, 0x01,
	0x0b, 0x5f, 0x64, 0x60, 0x97, 0xa6, 0x13, 0x0e, 0x6c, 0x95, 0x0d, 0xec, 0xd2, 0x74, 0xfc, 0x81,
	0xad, 0x43, 0x69, 0x60, 0x5f, 0x6a, 0x03, 0xdd, 0xab, 0xdf, 0x61, 0x9c, 0x3f, 0xb0, 0x2f, 0x8f,
	0x74, 0x0f, 0x29, 0x50, 0xbb, 0x18, 0x0f, 0x06, 0x1a, 0xa9, 0x6f, 0xeb, 0x43, 0xb3, 0xbe, 0x46,
	0x8b, 0xab, 0x04, 0x78, 0x68, 0x3a, 0x1d, 0x7d, 0x68, 0xa2, 0x9f, 0x42, 0xc9, 0x1d, 0x59, 0xd7,
	0x7a, 0xff, 0xa6, 0xbe, 0x4e, 0x07, 0xf6, 0x51, 0x06, 0x9f, 0x1e, 0x38, 0x6f, 0xec, 0x81, 0xa3,
	0x1b, 0xa7, 0x0c, 0x5b, 0xf5, 0xab, 0xa1, 0x1f, 0x41, 0x95, 0x6e, 0x22, 0x26, 0x32, 0xea, 0x75,
	0xda, 0xca, 0x7a, 0x62, 0x1b, 0x9d, 0xd0, 0x62, 0x15, 0xdc, 0xe0, 0x37, 0x6a, 0xc1, 0x22, 0x6f,
	0x44, 0x73, 0x9d, 0x81, 0xd5, 0xbf, 0xa9, 0xdf, 0xcd, 0xd8, 0x83, 0xbc, 0xd3, 0x53, 0x8a, 0xa5,
	0xd6, 0x5c, 0xf1, 0x93, 0x4c, 0xd3, 0xb0, 0x6e, 0x02, 0x0a, 0xe1, 0x7a, 0x63, 0xbb, 0x48, 0xa6,
	0x69, 0x58, 0x37, 0x9c, 0x44, 0x18, 0x7d, 0x06, 0x6b, 0x16, 0xd6, 0x2c, 0x5b, 0xbb, 0xb2, 0x2e,
	0xaf, 0x34, 0xcb, 0xf6, 0xcc, 0x91, 0xde, 0xf7, 0x2c, 0xc7, 0xae, 0x6f, 0x6c, 0x4b, 0x3b, 0x65,
	0x75, 0xc5, 0xc2, 0x6d, 0xfb, 0x2b, 0xeb, 0xf2, 0xaa, 0x1d, 0x16, 0x91, 0x8d, 0x88, 0x3d, 0x43,
	0xc3, 0x7d, 0x67, 0x64, 0xd6, 0xef, 0x6d, 0x4b, 0x3b, 0x05, 0xb5, 0x8c, 0x3d, 0xa3, 0x4b, 0xbe,
	0xd1, 0x4b, 0xd8, 0xc1, 0x37, 0xd8, 0x33, 0x87, 0xda, 0xc8, 0x64, 0xfc, 0x6c, 0x68, 0xd8, 0x1b,
	0x99, 0xfa, 0x50, 0x1b, 0xe9, 0xf6, 0x6b, 0xcb, 0xbe, 0xd4, 0x5c, 0x07, 0x5b, 0xb4, 0x8f, 0x4d,
	0x5a, 0xf7, 0x21, 0xc3, 0x57, 0x7d, 0xf4, 0x2e, 0xc5, 0x56, 0x19, 0xf2, 0x29, 0xc7, 0x25, 0x44,
	0x19, 0xea, 0xfd, 0x2b, 0xcb, 0x36, 0x7d, 0x86, 0xb9, 0x9f, 0x41, 0x94, 0x63, 0x86, 0xc6, 0xb9,
	0xa6, 0x36, 0x14, 0x3f, 0xd1, 0x73, 0xa8, 0x0c, 0x1d, 0xc7, 0xd0, 0x2c, 0xfb, 0xc2, 0xa9, 0x6f,
	0x6d, 0x4b, 0x3b, 0xd5, 0xe7, 0x77, 0x22, 0x2d, 0x1c, 0x3b, 0x8e, 0x41, 0x65, 0x4f, 0x79, 0xc8,
	0x7f, 0xa1, 0x8f, 0x60, 0x09, 0x9b, 0x7d, 0xc7, 0x36, 0x34, 0xba, 0x8b, 0x08, 0xb7, 0x6d, 0x53,
	0x86, 0xae, 0x31, 0xf0, 0x11, 0x81, 0xb6, 0x0d, 0xb2, 0x29, 0x19, 0x02, 0xdd, 0xcd, 0xf5, 0x0f,
	0x18, 0x47, 0x52, 0x10, 0xdd, 0xc1, 0xe8, 0x0e, 0xcc, 0x5b, 0x58, 0x73, 0xce, 0x71, 0x5d, 0xa1,
	0xd4, 0x9d, 0xb3, 0xf0, 0xc9, 0x39, 0x46, 0x0f, 0x61, 0xd1, 0xc2, 0x9a, 0x6e, 0xf7, 0xaf, 0x9c,
	0x91, 0x76, 0x61, 0x9a, 0x46, 0xfd, 0x01, 0x2d, 0x5e, 0xb0, 0x70, 0x93, 0x02, 0x5f, 0x98, 0xa6,
	0x81, 0x76, 0x40, 0x1e, 0xea, 0xf6, 0x58, 0x1f, 0x08, 0x2b, 0xfa, 0x90, 0xae, 0xe8, 0x22, 0x83,
	0x07, 0x8b, 0xba, 0x0e, 0x25, 0x0b, 0x6b, 0xd7, 0x8e, 0x67, 0xd6, 0x3f, 0xa4, 0x0d, 0xcd, 0x5b,
	0x98, 0x68, 0x0f, 0xf4, 0x14, 0xe6, 0x2d, 0x57, 0x1b, 0x38, 0xfd, 0xfa, 0x47, 0x29, 0x33, 0x3f,
	0x72, 0xfa, 0x3a, 0x21, 0xb5, 0x3a, 0x67, 0xb9, 0x47, 0x4e, 0x1f, 0xbd, 0x00, 0xf9, 0xd2, 0xb4,
	0xcd, 0x91, 0x3e, 0xf0, 0x55, 0x28, 0xae, 0x3f, 0x4a, 0x91, 0xd9, 0x87, 0x0c, 0x89, 0xcb, 0x37,
	0x75, 0xe9, 0x32, 0xf2, 0x8d, 0x89, 0x28, 0xf2, 0x2c, 0x6f, 0x60, 0xd6, 0x77, 0x98, 0x28, 0xa2,
	0x1f, 0x44, 0x60, 0x9f, 0x8f, 0xb1, 0x65, 0x9b, 0x18, 0x33, 0x19, 0xf1, 0x98, 0x09, 0x6c, 0x1f,
	0x48, 0x85, 0xc4, 0xcf, 0x00, 0x11, 0xa6, 0x70, 0x6c, 0x7f, 0xce, 0xf6, 0x85, 0x83, 0xeb, 0xbb,
	0x74, 0x10, 0xf7, 0x62, 0xd2, 0x95, 0xa0, 0x31, 0x12, 0x90, 0xd5, 0x93, 0xfb, 0x51, 0x00, 0x11,
	0x18, 0xd5, 0x36, 0xde, 0x77, 0xec, 0x6b, 0x73, 0xe4, 0x99, 0x46, 0xfd, 0x09, 0xa5, 0x8c, 0x08,
	0x22, 0x3b, 0xf6, 0x17, 0x8e, 0x6d, 0xfa, 0x9b, 0xee, 0x69, 0xca, 0x8e, 0xfd, 0xb9, 0x63, 0x9b,
	0x7c, 0xb7, 0xc1, 0x2f, 0x82, 0xdf, 0xca, 0x57, 0x4c, 0xe1, 0xc6, 0xd4, 0xbf, 0xaf, 0xcb, 0xa5,
	0xd0, 0x10, 0x98, 0xa4, 0xff, 0x95, 0xff, 0x3c, 0x07, 0x55, 0x2e, 0xe9, 0x29, 0xef, 0x6d, 0x02,
	0xf8, 0x5a, 0x21, 0xd0, 0xe0, 0x15, 0x0e, 0x61, 0x32, 0xce, 0xd7, 0xee, 0x85, 0x88, 0x76, 0x7f,
	0x04, 0x4b, 0x7d, 0x3a, 0x31, 0x4c, 0xd7, 0x94, 0x20, 0x30, 0x33, 0x6b, 0x51, 0x04, 0xb7, 0x8d,
	0x1c, 0x75, 0x2d, 0xe8, 0xb9, 0xb9, 0x88, 0x9e, 0x7b, 0x04, 0xf2, 0xc8, 0x74, 0x07, 0x44, 0xb4,
	0x68, 0x3e, 0xc6, 0x3c, 0x53, 0xa4, 0x14, 0xde, 0x73, 0xce, 0x18, 0x62, 0x4c, 0xe1, 0x97, 0x6e,
	0xa9, 0xf0, 0x13, 0x3a, 0xbd, 0x3c, 0x95, 0x4e, 0xaf, 0xa4, 0xe9, 0xf4, 0x50, 0x37, 0xc3, 0xd4,
	0xba, 0xf9, 0xb7, 0x99, 0xee, 0xe3, 0xdd, 0x11, 0xad, 0x4e, 0xc6, 0x5f, 0x4f, 0xf0, 0x5d, 0x30,
	0x78, 0x3c, 0x3e, 0xe7, 0xdf, 0x31, 0x6b, 0x65, 0x21, 0x66, 0xad, 0x7c, 0x0c, 0x2b, 0x01, 0x05,
	0x85, 0xe5, 0xad, 0x31, 0x23, 0x93, 0x13, 0x71, 0x3f, 0x58, 0xe5, 0xb8, 0x3a, 0x5d, 0x9c, 0x42,
	0x9d, 0x2e, 0xdd, 0x4e, 0x9d, 0x86, 0xa2, 0x49, 0x16, 0x45, 0xd3, 0x26, 0x00, 0xf6, 0x4c, 0x97,
	0x53, 0x76, 0x99, 0x99, 0x42, 0x04, 0xc2, 0xa8, 0x1a, 0x0a, 0x14, 0x34, 0x59, 0xa0, 0x28, 0x7f,
	0x21, 0x41, 0xd9, 0x87, 0xb1, 0x39, 0x8d, 0x6d, 0x6f, 0x74, 0xa3, 0xf5, 0x1d, 0xc3, 0xe4, 0xac,
	0x5d, 0xe5, 0xb0, 0x7d, 0xc7, 0xe0, 0x96, 0x24, 0xfd, 0xf4, 0x0d, 0x54, 0xfe, 0x49, 0x38, 0xc3,
	0x1d, 0x39, 0xd7, 0x96, 0xdd, 0x37, 0x59, 0x6d, 0xc6, 0xdb, 0x0b, 0x3e, 0x90, 0x56, 0x6f, 0x40,
	0xd9, 0xff, 0xe6, 0xac, 0x1d, 0x7c, 0xd3, 0xd5, 0xb1, 0x3c, 0xde, 0xf5, 0x1c, 0x2b, 0x24, 0x00,
	0x5a, 0x11, 0xc1, 0x2c, 0xf9, 0xcd, 0x1d, 0x06, 0xfa, 0x5b, 0xf9, 0x2f, 0x12, 0xac, 0xef, 0xe9,
	0x5e, 0xff, 0xea, 0xd0, 0xf4, 0xc8, 0x56, 0x3f, 0xb2, 0xb0, 0xb7, 0x77, 0xd3, 0x36, 0x54, 0xf3,
	0x5b, 0x62, 0x8a, 0xf0, 0x4d, 0xa8, 0x0d, 0x2c, 0xec, 0xd5, 0x25, 0x2a, 0x95, 0x81, 0xed, 0x44,
	0x82, 0x48, 0x38, 0x29, 0xb2, 0x80, 0xcc, 0x69, 0xaa, 0xa7, 0xf1, 0x20, 0xb5, 0xa6, 0x23, 0x4b,
	0xfb, 0x11, 0x2c, 0xd9, 0xa6, 0x69, 0x68, 0xe6, 0x5b, 0xbd, 0xef, 0x31, 0xc5, 0x55, 0xa4, 0x6b,
	0x54, 0x23, 0xe0, 0x16, 0x81, 0x52, 0x51, 0xb1, 0x01, 0x95, 0xa1, 0x3e, 0x7a, 0x6d, 0x52, 0x56,
	0x9a, 0xa5, 0x4b, 0x55, 0x66, 0x00, 0xa6, 0x9b, 0xfa, 0x03, 0x2b, 0x18, 0x00, 0xdb, 0xd0, 0xc0,
	0x40, 0xa4, 0x17, 0xa5, 0x03, 0xf5, 0xf4, 0xf9, 0x61, 0x97, 0x28, 0x4d, 0x3a, 0xc1, 0x60, 0x76,
	0xf1, 0x95, 0xf6, 0xfd, 0x01, 0xe6, 0x06, 0x90, 0xba, 0xca, 0xbf, 0x99, 0x03, 0xd4, 0x34, 0x0c,
	0x52, 0x72, 0x60, 0x8d, 0xcc, 0xbe, 0x37, 0xb8, 0x21, 0xb4, 0x12, 0x84, 0x8a, 0x14, 0x11, 0x2a,
	0x39, 0xee, 0xc8, 0x73, 0xee, 0x6a, 0x4e, 0xe7, 0x89, 0x24, 0xdc, 0xcb, 0x5f, 0xa3, 0x17, 0xf2,
	0x04, 0x96, 0xf5, 0x6b, 0xdd, 0x1a, 0xe8, 0xe7, 0xd6, 0x80, 0xb0, 0xd7, 0x50, 0xc7, 0xaf, 0xb9,
	0x27, 0x22, 0x8b, 0x05, 0xc7, 0x3a, 0x7e, 0x9d, 0x30, 0xad, 0xcb, 0x09, 0xd3, 0x3a, 0xbe, 0xef,
	0x2b, 0xc9, 0x7d, 0x1f, 0xb3, 0x32, 0x61, 0x7a, 0x2b, 0x93, 0xd8, 0xed, 0x9e, 0x65, 0x70, 0xcf,
	0x96, 0xfe, 0x26, 0xab, 0x73, 0x3d, 0xd4, 0x3c, 0xf3, 0x2d, 0x13, 0x59, 0x15, 0x75, 0xfe, 0x7a,
	0xd8, 0x33, 0xdf, 0x7a, 0x49, 0x6d, 0x5d, 0x4b, 0xd1, 0xd6, 0x69, 0x06, 0xc3, 0xe2, 0x3b, 0x18,
	0x0c, 0xe9, 0x5a, 0x7f, 0xe9, 0x5d, 0xb4, 0xbe, 0xf2, 0x31, 0x2c, 0x34, 0x05, 0xc2, 0x13, 0x9d,
	0xdb, 0x3c, 0x3a, 0x92, 0x67, 0x88, 0x7b, 0xdd, 0xec, 0x1c, 0xa8, 0x27, 0xed, 0x03, 0xe6, 0x9d,
	0xb7, 0x4f, 0xba, 0x72, 0x41, 0x79, 0x06, 0x2b, 0x09, 0xa6, 0xc5, 0x6e, 0xa6, 0x13, 0xad, 0xfc,
	0x89, 0x04, 0x8b, 0xd1, 0xe9, 0x10, 0x85, 0x34, 0xb6, 0x87, 0xfa, 0x08, 0x5f, 0x11, 0x53, 0x8d,
	0xd0, 0x8a, 0x31, 0x7a, 0x2d, 0x80, 0x52, 0x62, 0x7d, 0x08, 0x8b, 0xfe, 0xda, 0xf2, 0xb5, 0x2b,
	0x30, 0x34, 0x0e, 0xe5, 0xab, 0x14, 0x0b, 0x93, 0x2c, 0x84, 0x7c, 0xbc, 0x05, 0x55, 0x1e, 0xa7,
	0xa0, 0x3e, 0x0c, 0xe3, 0x72, 0x60, 0x20, 0xe2, 0xc2, 0x28, 0xff, 0xb8, 0x08, 0xf5, 0xd8, 0x64,
	0xf6, 0x6e, 0xf6, 0xc6, 0x18, 0xe7, 0xee, 0xc3, 0xe7, 0x91, 0xb8, 0xce, 0x74, 0x9b, 0x2d, 0xb2,
	0x27, 0x8a, 0xb1, 0x3d, 0x91, 0x70, 0x43, 0x66, 0x93, 0x6e, 0x48, 0xc4, 0x2a, 0x9f, 0x9b, 0xce,
	0x2a, 0x4f, 0xe3, 0xb6, 0xf9, 0x77, 0xe0, 0xb6, 0x04, 0x6b, 0x97, 0xa6, 0x36, 0x44, 0xcb, 0xef,
	0xc4, 0x92, 0xdf, 0x87, 0xbb, 0x19, 0xcb, 0x92, 0xc7, 0x69, 0x7f, 0x2c, 0xc1, 0xbd, 0x33, 0xd7,
	0xd0, 0x3d, 0x93, 0xd4, 0x8c, 0x4e, 0xca, 0x5f, 0xd1, 0xf4, 0x40, 0x8f, 0x0c, 0xc5, 0x31, 0x17,
	0xaa, 0x35, 0x95, 0xfc, 0x4c, 0x25, 0x5d, 0xf1, 0xf6, 0xa4, 0x53, 0xb6, 0x60, 0x33, 0x67, 0x48,
	0xd8, 0x55, 0x8e, 0x61, 0x29, 0x46, 0x0f, 0xc2, 0x2b, 0x21, 0x2b, 0x30, 0x4d, 0x59, 0xf6, 0x7c,
	0x3e, 0xd8, 0x04, 0x60, 0x85, 0x01, 0x0b, 0xd6, 0x54, 0x86, 0x4e, 0x75, 0xd4, 0x3f, 0x5c, 0x00,
	0xe0, 0xa4, 0xfb, 0xcd, 0xd0, 0x25, 0xdf, 0x87, 0xb5, 0x50, 0x37, 0x68, 0xd6, 0x50, 0xbf, 0xf4,
	0x8d, 0x50, 0xa6, 0x60, 0x57, 0xc3, 0xd2, 0x36, 0x29, 0x64, 0x56, 0x53, 0xb4, 0x96, 0x1f, 0x65,
	0x1c, 0xf3, 0x10, 0x65, 0xa4, 0x16, 0x8f, 0x34, 0x46, 0x2d, 0xd8, 0xd2, 0xd4, 0x16, 0xec, 0x33,
	0x58, 0xd1, 0x6d, 0xcf, 0xc2, 0xae, 0x3e, 0xf4, 0x7d, 0x57, 0xb2, 0xc3, 0x98, 0xa6, 0x59, 0xf6,
	0x8b, 0x98, 0xff, 0x4a, 0x16, 0x27, 0xae, 0x92, 0x2a, 0x13, 0x55, 0x12, 0x4c, 0x54, 0x49, 0xd5,
	0xe9, 0x55, 0x52, 0x94, 0x30, 0xfa, 0xd8, 0xb0, 0x9c, 0x68, 0x2c, 0x2c, 0x2c, 0x6d, 0x92, 0x42,
	0x46, 0x18, 0x15, 0xd6, 0xdc, 0x91, 0x69, 0x98, 0x17, 0x96, 0x6d, 0x1a, 0x9a, 0xa8, 0xdb, 0x6b,
	0x93, 0x75, 0xfb, 0x9d, 0xb0, 0x6a, 0x53, 0xd0, 0xf2, 0xbe, 0x72, 0x5c, 0x14, 0x94, 0x63, 0x3c,
	0xe2, 0xb4, 0x94, 0x17, 0x71, 0x92, 0xf3, 0x23, 0x4e, 0xcb, 0xb9, 0x11, 0x27, 0xf4, 0x6e, 0x11,
	0xa7, 0x0d, 0xa8, 0x18, 0xe6, 0xb5, 0xd5, 0x37, 0xc3, 0x78, 0x58, 0x99, 0x01, 0xda, 0x06, 0xb5,
	0x86, 0x07, 0xba, 0x77, 0xe1, 0x8c, 0x86, 0x34, 0x1c, 0x56, 0x53, 0x83, 0xef, 0x20, 0x84, 0xb7,
	0x26, 0x84, 0xf0, 0x76, 0xfc, 0x28, 0xfc, 0x7d, 0x2a, 0x8e, 0x51, 0x64, 0x30, 0x2d, 0x52, 0xe2,
	0x47, 0xe6, 0x05, 0xa3, 0x61, 0x2b, 0x62, 0x34, 0x24, 0xe3, 0x58, 0xdb, 0xdf, 0x49, 0x1c, 0xeb,
	0x83, 0xa4, 0x02, 0x21, 0x1a, 0x88, 0x99, 0xb7, 0x96, 0x4b, 0x83, 0x2b, 0x35, 0xb5, 0xcc, 0x00,
	0x6d, 0x37, 0xaa, 0x5d, 0x1e, 0x4c, 0xa7, 0x5d, 0x42, 0x7f, 0xe8, 0x61, 0x7e, 0xa8, 0xe6, 0xc3,
	0x94, 0x50, 0x8d, 0x10, 0x80, 0xf9, 0x28, 0x23, 0x00, 0xf3, 0x68, 0x8a, 0x00, 0x4c, 0xc4, 0xa0,
	0xdf, 0x89, 0x19, 0xf4, 0x41, 0x54, 0xe5, 0x71, 0x6e, 0x54, 0x65, 0x77, 0x4a, 0x3b, 0xed, 0xc9,
	0x77, 0x66, 0xa7, 0x3d, 0x7d, 0xa7, 0xe8, 0x4c, 0x2c, 0xf6, 0xf2, 0xf1, 0xf4, 0xb1, 0x97, 0x21,
	0x94, 0xfd, 0xf5, 0x23, 0x84, 0x67, 0x2b, 0x1d, 0xe8, 0x40, 0xba, 0xa0, 0x06, 0x7a, 0x02, 0x88,
	0x16, 0x9c, 0x5b, 0xb6, 0xa1, 0xc5, 0x74, 0xc3, 0x12, 0x29, 0xd9, 0xb3, 0x6c, 0xc3, 0xdf, 0xc6,
	0x1b, 0x9c, 0x5f, 0xe8, 0x4e, 0x65, 0x9e, 0x25, 0x65, 0x0c, 0x6a, 0x55, 0xfd, 0x00, 0xe6, 0x28,
	0xf7, 0x93, 0x05, 0x78, 0x63, 0x19, 0xde, 0x15, 0xd7, 0x3d, 0xec, 0x03, 0xad, 0xc1, 0xfc, 0x95,
	0x69, 0x5d, 0x7a, 0x57, 0x5c, 0x7b, 0xf1, 0x2f, 0xe5, 0xcf, 0x8b, 0x50, 0x0d, 0x54, 0x57, 0x8e,
	0x9e, 0x17, 0xc4, 0x7c, 0x61, 0x6a, 0x31, 0xff, 0x10, 0x16, 0x69, 0x63, 0x71, 0x23, 0x8c, 0xba,
	0xa5, 0xfb, 0xbe, 0x21, 0xf6, 0x1c, 0xee, 0x24, 0x94, 0xd5, 0x6b, 0xf3, 0x06, 0xd7, 0x81, 0xee,
	0xa7, 0x95, 0x98, 0xae, 0xfa, 0xda, 0xbc, 0xc1, 0xb1, 0x3a, 0x4c, 0x55, 0xd1, 0x3a, 0xd5, 0x78,
	0x1d, 0xaa, 0xa9, 0x68, 0x9d, 0xa7, 0x80, 0x58, 0xe3, 0x63, 0x97, 0x88, 0x29, 0xcd, 0x73, 0x5e,
	0x9b, 0x36, 0xf7, 0x27, 0x64, 0x5a, 0x72, 0x46, 0x0b, 0x7a, 0x04, 0x4e, 0xb0, 0x59, 0xb3, 0x11,
	0x6c, 0x1e, 0x09, 0xa1, 0x25, 0x22, 0x76, 0x74, 0x3c, 0x4c, 0x43, 0xd0, 0xf1, 0x2c, 0xc6, 0xc7,
	0x43, 0x15, 0x84, 0x3f, 0x1e, 0x86, 0x18, 0xe9, 0x81, 0x49, 0x6f, 0x99, 0x96, 0x08, 0x3d, 0x28,
	0x7f, 0x26, 0xc1, 0xdd, 0x63, 0x7d, 0xf4, 0x3a, 0x94, 0xb7, 0xac, 0xd4, 0x34, 0x72, 0x8d, 0xac,
	0x68, 0x9c, 0xae, 0x10, 0x8f, 0xd3, 0x1d, 0xc3, 0xaa, 0x48, 0x7b, 0xfb, 0xc2, 0x61, 0xce, 0x74,
	0x71, 0xb2, 0x86, 0x42, 0x7a, 0xe4, 0x9b, 0x3a, 0xd7, 0x7b, 0xd0, 0xc8, 0x1a, 0x23, 0x76, 0x53,
	0xd8, 0x41, 0x4a, 0xb2, 0x83, 0xf2, 0x4f, 0x24, 0x58, 0xdc, 0xd3, 0x6d, 0xc2, 0x91, 0x7e, 0x20,
	0x23, 0x73, 0x76, 0x4c, 0x1a, 0x9e, 0xeb, 0xcc, 0x49, 0xa1, 0xd2, 0x70, 0x4f, 0xb7, 0xd3, 0x02,
	0xe3, 0xc5, 0xb4, 0xc0, 0xf8, 0x53, 0x40, 0x11, 0x3c, 0x16, 0x1f, 0x67, 0xb6, 0x94, 0x2c, 0xa0,
	0xd2, 0x28, 0xb9, 0xb2, 0x0c, 0x4b, 0x91, 0x71, 0x61, 0x57, 0xf9, 0x10, 0xe0, 0xc0, 0x1c, 0x08,
	0x76, 0x5f, 0xba, 0x8d, 0x5c, 0x83, 0x6a, 0x80, 0x86, 0x5d, 0xe5, 0x3f, 0x50, 0xe7, 0xcc, 0x9b,
	0x6a, 0x86, 0xef, 0x15, 0xa1, 0x79, 0x04, 0xf2, 0xb7, 0x63, 0x73, 0x74, 0xa3, 0x9d, 0xdf, 0x04,
	0x01, 0x51, 0x16, 0x80, 0xa9, 0x51, 0xf8, 0xde, 0x0d, 0x0f, 0x88, 0x46, 0x24, 0xfa, 0x5c, 0x7e,
	0x88, 0x66, 0x3e, 0x11, 0xa2, 0xf9, 0x02, 0x96, 0x22, 0xd3, 0xc1, 0x2e, 0x7a, 0x0c, 0xb3, 0x64,
	0x02, 0x74, 0x32, 0x99, 0x41, 0x19, 0x8a, 0xa2, 0xfc, 0x0e, 0xa0, 0x43, 0xd3, 0x7b, 0x79, 0x4c,
	0x37, 0x6a, 0xeb, 0xad, 0x95, 0x4f, 0x4b, 0xff, 0xc4, 0xbf, 0x10, 0x9c, 0xf8, 0x2b, 0x07, 0xb0,
	0x92, 0x68, 0x00, 0xbb, 0x84, 0x37, 0xae, 0x87, 0xda, 0x78, 0x34, 0xe0, 0x0d, 0xcc, 0x5d, 0x0f,
	0xcf, 0x46, 0x03, 0x22, 0x08, 0xcd, 0xb7, 0xd8, 0xf2, 0x30, 0x67, 0x19, 0xfe, 0xa5, 0xfc, 0x2b,
	0x09, 0xee, 0x32, 0xa7, 0x21, 0xe4, 0x5e, 0x2e, 0xce, 0xde, 0x63, 0x7f, 0xb1, 0x28, 0xb0, 0xbf,
	0xbf, 0xc8, 0xc0, 0x99, 0xdc, 0xae, 0x85, 0xd0, 0xaf, 0xcd, 0x1b, 0x41, 0xb8, 0xce, 0x4e, 0x2b,
	0x5c, 0x95, 0x7b, 0xd0, 0xc8, 0x1a, 0x2f, 0x76, 0x95, 0xff, 0x24, 0xc1, 0x32, 0x2b, 0xa6, 0x64,
	0x39, 0x1b, 0x0d, 0x7e, 0x0d, 0xd3, 0x58, 0x87, 0x92, 0x6d, 0xbe, 0xa1, 0x34, 0x67, 0x9b, 0x68,
	0xde, 0x36, 0xdf, 0x10, 0xa2, 0x87, 0x6b, 0x31, 0x27, 0xae, 0x45, 0x03, 0x2a, 0xb6, 0xa3, 0xf1,
	0x12, 0x16, 0xd5, 0x2c, 0xd9, 0xce, 0xcb, 0x21, 0xaf, 0xc2, 0x0d, 0x9d, 0x92, 0x60, 0xe8, 0x28,
	0xab, 0x80, 0xe2, 0xd3, 0xc2, 0xae, 0xf2, 0x33, 0x7f, 0xed, 0x08, 0x6f, 0x75, 0x5d, 0xb3, 0x6f,
	0xe9, 0x03, 0xba, 0x73, 0x73, 0x27, 0x1d, 0x9c, 0x45, 0x17, 0x84, 0xb3, 0xe8, 0x90, 0xae, 0xc9,
	0xb6, 0xb0, 0xab, 0xfc, 0x2f, 0x09, 0x96, 0x55, 0xd3, 0x75, 0x46, 0x94, 0xdf, 0x5f, 0x5a, 0xe6,
	0x9b, 0x5c, 0x8f, 0xef, 0x05, 0x94, 0x79, 0xdf, 0x84, 0xdf, 0x88, 0x4c, 0x7d, 0x12, 0x59, 0xda,
	0x44, 0x53, 0x6c, 0x77, 0x18, 0xb8, 0x65, 0x7b, 0x23, 0x62, 0x42, 0xb3, 0xaf, 0x46, 0x1f, 0x16,
	0xc4, 0x82, 0x94, 0xbc, 0x97, 0x9f, 0xc0, 0xdc, 0xb5, 0x3e, 0x18, 0xfb, 0x12, 0xe2, 0xd1, 0x84,
	0x6e, 0xc8, 0x5f, 0x2a, 0x30, 0x58, 0xad, 0x1f, 0x17, 0x7e, 0x24, 0x29, 0x9b, 0x50, 0xf6, 0xc1,
	0xc2, 0x69, 0x51, 0x09, 0x8a, 0x9d, 0xd6, 0x37, 0xb2, 0x44, 0x48, 0x1f, 0x6f, 0x07, 0xbb, 0x0a,
	0x86, 0x95, 0x28, 0xf4, 0xe5, 0xf3, 0x49, 0x3e, 0x70, 0x84, 0x22, 0x95, 0x60, 0x92, 0x68, 0x07,
	0xe4, 0x0b, 0x6b, 0xe0, 0x99, 0x23, 0x6d, 0x68, 0xd9, 0xdc, 0xc1, 0x2a, 0xd2, 0xca, 0x8b, 0x0c,
	0x7e, 0x6c, 0xd9, 0xd4, 0xb5, 0x52, 0xbe, 0x82, 0xd5, 0x64, 0xa7, 0xd8, 0x45, 0xdf, 0x83, 0x55,
	0x6c, 0xd1, 0x5c, 0x81, 0xbe, 0x6e, 0x7b, 0x5a, 0xd0, 0x11, 0xf3, 0xe7, 0x91, 0x50, 0xc6, 0xa9,
	0xa9, 0x7c, 0x0e, 0xeb, 0xac, 0x25, 0x6a, 0x64, 0x05, 0xf9, 0x05, 0x64, 0x0a, 0x79, 0x11, 0x01,
	0xe5, 0x6e, 0x46, 0x3d, 0xec, 0x2a, 0x9f, 0xc2, 0xea, 0xa1, 0x99, 0xd2, 0x9e, 0xe8, 0xfd, 0x4b,
	0x11, 0xef, 0x5f, 0x79, 0x9a, 0x56, 0x05, 0xbb, 0x84, 0x43, 0x19, 0x19, 0xb8, 0x2d, 0x47, 0x3f,
	0x94, 0x17, 0xe2, 0x42, 0x74, 0xaf, 0xf4, 0x91, 0x99, 0x4b, 0xf1, 0xac, 0xb3, 0x38, 0xe5, 0x8e,
	0xb8, 0x74, 0xbc, 0x1d, 0xec, 0x2a, 0xff, 0xbb, 0x08, 0xb5, 0xa6, 0x61, 0xf0, 0x63, 0x9e, 0x77,
	0x6a, 0x3a, 0xeb, 0x20, 0xa9, 0x98, 0x71, 0x90, 0x94, 0x72, 0x2a, 0x38, 0x3b, 0xe9, 0x54, 0x70,
	0x2e, 0x1a, 0xf2, 0x08, 0x45, 0xe8, 0xfc, 0xd4, 0xf6, 0xe9, 0x7b, 0x9e, 0x03, 0x66, 0x47, 0x59,
	0xca, 0x39, 0x51, 0x96, 0x29, 0x82, 0xe7, 0x6b, 0x30, 0x8f, 0x9d, 0xf1, 0xa8, 0x6f, 0xfa, 0x89,
	0x3c, 0xec, 0x4b, 0x90, 0x89, 0x55, 0xd1, 0xf9, 0x0b, 0xbd, 0xb7, 0x85, 0xe9, 0xbc, 0xb7, 0xd0,
	0x25, 0xad, 0x45, 0x5d, 0x52, 0xe5, 0xaf, 0x24, 0x58, 0x14, 0xd7, 0x1e, 0xbb, 0x93, 0x4e, 0x7a,
	0x9f, 0x93, 0x15, 0xa1, 0x1f, 0x94, 0x05, 0xf2, 0xce, 0x21, 0x7d, 0xc4, 0x77, 0xb2, 0xf8, 0xd3,
	0xad, 0xf7, 0x6a, 0xba, 0xf5, 0xae, 0xfc, 0x8f, 0x02, 0x2c, 0x1f, 0x9a, 0x1e, 0xef, 0xfd, 0x68,
	0x92, 0x51, 0x91, 0xc2, 0x7f, 0x85, 0x54, 0xfe, 0x6b, 0x40, 0x99, 0x74, 0x72, 0xec, 0x8c, 0x02,
	0x0f, 0xcc, 0xff, 0x0e, 0x37, 0xeb, 0xac, 0xb0, 0x59, 0xb3, 0x4f, 0xab, 0xef, 0x41, 0x45, 0xc7,
	0x7d, 0xd3, 0x36, 0x2c, 0xfb, 0x92, 0xf2, 0x6c, 0x59, 0x0d, 0x01, 0x84, 0xb7, 0xc4, 0x33, 0x5e,
	0x2d, 0x44, 0x65, 0xea, 0x70, 0x55, 0x38, 0xd3, 0x6d, 0x06, 0xb5, 0xe2, 0xd6, 0x62, 0xf9, 0x36,
	0xd6, 0x62, 0xc4, 0x08, 0xac, 0xe4, 0x1b, 0x81, 0x90, 0x30, 0x02, 0x6d, 0x6a, 0xc6, 0x45, 0x08,
	0x8e, 0x5d, 0x36, 0x20, 0xc6, 0x3c, 0xc2, 0x21, 0x5d, 0xce, 0x51, 0x75, 0x3f, 0x6c, 0x80, 0x0c,
	0x88, 0x2e, 0xf5, 0x90, 0x50, 0xbb, 0x10, 0xa5, 0xb6, 0xe2, 0x42, 0xed, 0x20, 0x48, 0x3e, 0x7b,
	0x1f, 0xdb, 0x66, 0xda, 0x8c, 0x04, 0x45, 0x86, 0x45, 0xb1, 0x47, 0xec, 0x2a, 0xff, 0x52, 0x12,
	0xb9, 0xcc, 0xb7, 0xe5, 0x27, 0x6c, 0x98, 0xf7, 0xb2, 0xe8, 0x23, 0x6b, 0x54, 0xcc, 0x5f, 0xa3,
	0xd9, 0xc4, 0x1a, 0x7d, 0x25, 0xae, 0x91, 0x70, 0x8a, 0x1a, 0xec, 0x60, 0x69, 0xca, 0x1d, 0xac,
	0xfc, 0x3b, 0xe1, 0xd8, 0x59, 0x68, 0x8f, 0xda, 0xca, 0x1f, 0x11, 0x82, 0xfa, 0xf3, 0x17, 0x4f,
	0x9e, 0x6b, 0x01, 0x11, 0xde, 0xff, 0xf0, 0xf9, 0xfd, 0x08, 0xf1, 0x37, 0x52, 0x78, 0xaa, 0x1c,
	0x1d, 0x3e, 0x76, 0xd1, 0xef, 0x87, 0xe9, 0x1d, 0x2c, 0x72, 0xc4, 0x98, 0xf6, 0x87, 0x91, 0x81,
	0x65, 0xd5, 0x16, 0xc9, 0xc5, 0x8d, 0x38, 0x7f, 0x07, 0x50, 0x50, 0xe3, 0x15, 0x2c, 0x27, 0x50,
	0x52, 0xcc, 0xb9, 0x67, 0xa2, 0x39, 0x97, 0xb7, 0x24, 0x82, 0xfd, 0x76, 0x01, 0xcb, 0x7b, 0xba,
	0x7d, 0x3b, 0x6e, 0xcc, 0xf0, 0xa0, 0x37, 0xa0, 0x62, 0x61, 0xcd, 0x30, 0x07, 0xa6, 0x67, 0xf2,
	0x53, 0xfd, 0xb2, 0x85, 0x0f, 0xe8, 0x37, 0x31, 0x04, 0xe3, 0xfd, 0x60, 0x57, 0xf9, 0x65, 0x81,
	0xaa, 0x8e, 0x26, 0x4f, 0x6f, 0x79, 0x9f, 0x2d, 0x29, 0xc8, 0xcc, 0x62, 0x44, 0x66, 0x3e, 0x80,
	0x20, 0x7d, 0x46, 0x5c, 0xda, 0x05, 0x1f, 0x48, 0x59, 0x43, 0x81, 0x9a, 0x85, 0xb5, 0x0b, 0x6b,
	0x84, 0x3d, 0xcd, 0xb3, 0x86, 0x2c, 0xa9, 0xa0, 0xac, 0x56, 0x2d, 0xfc, 0x82, 0xc0, 0x7a, 0xd6,
	0xd0, 0x44, 0x0f, 0x61, 0xd1, 0xd3, 0x47, 0x97, 0xa6, 0x17, 0x4b, 0x14, 0x5a, 0x60, 0x50, 0xee,
	0x16, 0x87, 0x7a, 0xb8, 0x14, 0xd1, 0xc3, 0x34, 0xd1, 0xd0, 0x74, 0x43, 0x19, 0x5b, 0x53, 0xcb,
	0x04, 0x40, 0x79, 0x6b, 0x19, 0x96, 0x22, 0x64, 0xc0, 0xae, 0xf2, 0x5f, 0x25, 0x2a, 0x3a, 0x7e,
	0xa5, 0xa4, 0x49, 0xce, 0x68, 0x36, 0x77, 0x46, 0x73, 0x91, 0x19, 0x25, 0x08, 0x3b, 0x9f, 0x42,
	0xd8, 0xc8, 0xb4, 0x4b, 0xc9, 0x69, 0x47, 0xa6, 0x88, 0x5d, 0xc5, 0x00, 0xd9, 0xff, 0xa6, 0xdd,
	0xf3, 0x48, 0x68, 0xba, 0x29, 0x99, 0x18, 0x41, 0x21, 0x65, 0x04, 0x08, 0x66, 0xe9, 0x8a, 0xb2,
	0x30, 0x22, 0xfd, 0xad, 0x9c, 0xc2, 0xda, 0xa1, 0xe9, 0x89, 0x1d, 0x4d, 0x54, 0xf7, 0xf9, 0x34,
	0x56, 0x2e, 0x60, 0x3d, 0xb5, 0x45, 0xec, 0xa2, 0xaf, 0x01, 0x05, 0xa3, 0xa4, 0xf3, 0x10, 0xb4,
	0xda, 0x66, 0xdc, 0x70, 0x8c, 0xcc, 0x5c, 0x95, 0xf5, 0x58, 0x83, 0xca, 0x33, 0x68, 0x1c, 0xb2,
	0x95, 0xf9, 0xc6, 0xb1, 0x9b, 0x62, 0x5a, 0x18, 0x19, 0x3d, 0x3f, 0x1e, 0x95, 0x82, 0xe3, 0x51,
	0xe5, 0x33, 0xd8, 0xc8, 0xc4, 0x67, 0xce, 0x82, 0xe7, 0x78, 0xfa, 0xc0, 0x77, 0x16, 0xe8, 0x87,
	0xf2, 0x29, 0x6c, 0xee, 0x5f, 0x99, 0xfd, 0xd7, 0xa4, 0x9a, 0x5f, 0x87, 0x67, 0x1e, 0x0c, 0xcc,
	0xf4, 0x7e, 0xb6, 0xe1, 0x7e, 0x5e, 0x15, 0xec, 0x2a, 0xff, 0x54, 0x82, 0x85, 0xa6, 0xeb, 0x32,
	0xef, 0xe1, 0x7d, 0xd8, 0x79, 0x1b, 0x16, 0x2e, 0x46, 0xce, 0x50, 0x8b, 0xf2, 0x34, 0x10, 0x18,
	0xe7, 0xd8, 0x2d, 0xa8, 0x8e, 0x68, 0x37, 0x11, 0x59, 0xce, 0x40, 0xf1, 0xbc, 0xfe, 0xa8, 0x4b,
	0xa0, 0x2c, 0x41, 0x4d, 0x18, 0x24, 0x76, 0x95, 0x57, 0x7e, 0x4c, 0x84, 0x1e, 0xa4, 0xea, 0x97,
	0xf9, 0xa1, 0x1d, 0xff, 0xf4, 0xa9, 0x20, 0x9c, 0x3e, 0x65, 0x6d, 0xbf, 0x30, 0x2e, 0x11, 0x36,
	0x8d, 0x5d, 0xe5, 0x7f, 0x16, 0x69, 0x6c, 0x8a, 0xc0, 0xf0, 0xde, 0xcd, 0x0b, 0xea, 0xc4, 0x92,
	0x3e, 0x57, 0x61, 0x6e, 0x60, 0x0d, 0xad, 0xc0, 0xaf, 0xa3, 0x1f, 0xb9, 0xf6, 0x0e, 0xfa, 0x09,
	0x40, 0x70, 0xf7, 0x81, 0xb9, 0x2a, 0x93, 0x8f, 0x89, 0x2b, 0xfe, 0xe5, 0x07, 0xfc, 0x7e, 0x96,
	0xa1, 0x30, 0xeb, 0x6a, 0xe6, 0x81, 0xf6, 0x42, 0xf4, 0x40, 0x9b, 0xb8, 0x39, 0x2c, 0xce, 0x7b,
	0x6e, 0x5e, 0x5a, 0x2c, 0x72, 0x5e, 0x54, 0xab, 0x0c, 0xb6, 0x47, 0x40, 0x94, 0x37, 0x18, 0x8a,
	0x69, 0xb3, 0x23, 0xcd, 0xa2, 0xca, 0x13, 0x36, 0x5a, 0xb6, 0x41, 0xec, 0x88, 0x31, 0x36, 0x35,
	0x42, 0x77, 0x8d, 0xf9, 0xff, 0x34, 0x38, 0x5e, 0x56, 0x6b, 0x63, 0x6c, 0x12, 0x12, 0x33, 0x7a,
	0x06, 0xeb, 0x24, 0x0b, 0xeb, 0x14, 0x3f, 0x30, 0x5e, 0x4e, 0x1c, 0x18, 0x07, 0xc1, 0x1f, 0x24,
	0x5e, 0x44, 0x78, 0x08, 0x8b, 0xce, 0x1b, 0xdb, 0x1c, 0x69, 0x63, 0xdf, 0x74, 0x59, 0xa1, 0xad,
	0x2e, 0x50, 0xe8, 0x99, 0xc5, 0x2c, 0x97, 0x2d, 0x7e, 0x92, 0xcc, 0x1d, 0x4e, 0x9e, 0xe2, 0x4f,
	0x40, 0xcc, 0xc1, 0x54, 0x2e, 0xa9, 0x3f, 0x1f, 0x5b, 0x76, 0xec, 0x46, 0x57, 0x58, 0x8a, 0xad,
	0x70, 0x24, 0x9b, 0xad, 0x30, 0x5d, 0x36, 0xdb, 0xe3, 0x20, 0x92, 0x3c, 0x89, 0x9d, 0x95, 0x0f,
	0x83, 0x28, 0xad, 0xcf, 0x9e, 0x01, 0xe5, 0xa4, 0x90, 0x72, 0xca, 0x26, 0x94, 0x38, 0x4e, 0x6a,
	0xf1, 0x0f, 0x61, 0x45, 0x4c, 0xc7, 0xf3, 0x7b, 0x9d, 0x98, 0x6a, 0xa8, 0xfc, 0xb9, 0x04, 0xab,
	0xc9, 0x9a, 0xd8, 0x45, 0xbf, 0x23, 0xf4, 0x12, 0x0f, 0x8f, 0xa5, 0x55, 0x78, 0x46, 0x7e, 0x30,
	0xcb, 0x8a, 0x56, 0x6c, 0x1c, 0x43, 0x25, 0x00, 0xa5, 0x58, 0x52, 0xbb, 0x51, 0x4b, 0x6a, 0x35,
	0x41, 0xd2, 0x9e, 0x7e, 0x29, 0x5a, 0x51, 0x07, 0x20, 0x1f, 0xeb, 0xa3, 0xd7, 0xd4, 0x56, 0x98,
	0x98, 0xd1, 0x91, 0x19, 0x5b, 0xf9, 0x21, 0x2c, 0xc7, 0x5a, 0xc1, 0x2e, 0x3d, 0x6e, 0xa7, 0x66,
	0x49, 0x74, 0x85, 0xaa, 0x17, 0x3e, 0x56, 0xdb, 0x50, 0xfe, 0x50, 0x12, 0xe3, 0x8f, 0xd1, 0x03,
	0xe8, 0x77, 0x0a, 0xc5, 0x7c, 0x0e, 0xf3, 0xfc, 0xf0, 0xb2, 0x38, 0xd5, 0x29, 0x37, 0xc7, 0x56,
	0x36, 0x61, 0x23, 0x73, 0x1c, 0xd8, 0x55, 0x3e, 0xa7, 0xca, 0xa8, 0xeb, 0x8d, 0xac, 0x3e, 0x55,
	0x49, 0x04, 0xcb, 0x34, 0x02, 0xed, 0x95, 0x35, 0x4e, 0xe5, 0xef, 0xc0, 0xbd, 0xec, 0x7a, 0x59,
	0x21, 0x2f, 0x96, 0x58, 0x6b, 0x5d, 0x13, 0x59, 0xc1, 0x4a, 0xb9, 0x75, 0xc0, 0x81, 0x41, 0x64,
	0xc5, 0x76, 0x46, 0x43, 0x7a, 0x7c, 0x1c, 0xc6, 0x0e, 0xab, 0x0c, 0xc6, 0x02, 0x87, 0x3a, 0xac,
	0x76, 0x99, 0x0a, 0xed, 0x98, 0x6f, 0xcc, 0x70, 0x81, 0x13, 0x4a, 0x30, 0x9b, 0x9e, 0xf7, 0x01,
	0xcc, 0xb7, 0xae, 0x35, 0xa2, 0x6e, 0x21, 0xed, 0xa3, 0xa8, 0x0a, 0x10, 0x65, 0x1d, 0xee, 0xa4,
	0x74, 0x81, 0x5d, 0xe5, 0x13, 0xb8, 0xeb, 0xf3, 0x73, 0x72, 0x00, 0x08, 0x66, 0xc7, 0x7e, 0x9c,
	0xb1, 0xa6, 0xd2, 0xdf, 0xca, 0x3f, 0x93, 0xa0, 0x91, 0x55, 0x03, 0xbb, 0xe8, 0x84, 0x47, 0x52,
	0x87, 0xba, 0xcb, 0x37, 0xcf, 0xf7, 0x53, 0x37, 0x4f, 0xb2, 0x2a, 0xbf, 0x8e, 0xe2, 0x0a, 0x41,
	0xe6, 0x63, 0xdd, 0x6d, 0xfc, 0x98, 0x05, 0x99, 0xfd, 0x02, 0x71, 0x2f, 0xd5, 0xd8, 0x5e, 0x5a,
	0x15, 0xf7, 0x52, 0x45, 0xdc, 0x35, 0xff, 0x57, 0xa2, 0x66, 0x98, 0x1f, 0x92, 0x78, 0x4f, 0x65,
	0xf7, 0xab, 0xd1, 0x56, 0xc2, 0x92, 0x2e, 0x44, 0x96, 0xf4, 0xbd, 0x75, 0x95, 0x82, 0xa9, 0xc9,
	0x98, 0x9c, 0xfd, 0x24, 0x99, 0x1f, 0x8f, 0x8f, 0x14, 0x6e, 0x11, 0x1f, 0x51, 0xfe, 0x2c, 0x48,
	0xbd, 0x0b, 0xc2, 0x9b, 0x42, 0x1e, 0xfd, 0x3b, 0x09, 0x8b, 0xa8, 0xb9, 0x56, 0x8c, 0x9b, 0x6b,
	0x9f, 0xc6, 0xce, 0xa9, 0x72, 0xd2, 0xf8, 0xfd, 0x63, 0xaa, 0x20, 0x17, 0x2f, 0x75, 0x8c, 0xd8,
	0x55, 0xfe, 0xb5, 0x94, 0x3c, 0xc8, 0xf2, 0x53, 0x88, 0x7e, 0x15, 0x73, 0x10, 0xf2, 0x99, 0x66,
	0xdf, 0x29, 0x9f, 0x29, 0x94, 0x8c, 0x29, 0x03, 0xc6, 0xae, 0x72, 0x45, 0x73, 0x75, 0xd9, 0x3c,
	0xfd, 0x1c, 0x95, 0xbc, 0x89, 0x6c, 0x40, 0xc5, 0xd5, 0x47, 0x11, 0x0b, 0xb9, 0xcc, 0x00, 0xac,
	0x90, 0xbb, 0x75, 0xc1, 0x5c, 0xca, 0x0c, 0xd0, 0x36, 0x94, 0x35, 0x58, 0x4d, 0xf6, 0x84, 0x5d,
	0xe5, 0x35, 0xac, 0xa9, 0xe6, 0xd0, 0xb9, 0x36, 0x7f, 0x1d, 0x83, 0xa0, 0x27, 0x21, 0x29, 0x9d,
	0x61, 0x57, 0xf9, 0x9a, 0x5a, 0xbf, 0xdf, 0xcd, 0x20, 0x14, 0x95, 0xda, 0x54, 0x89, 0x4e, 0xa2,
	0x95, 0xa4, 0xd8, 0xc8, 0x37, 0x01, 0x82, 0x91, 0xfb, 0xe7, 0x4d, 0x15, 0x7f, 0xe8, 0x58, 0x31,
	0xa0, 0x16, 0x69, 0xf0, 0x1d, 0xe9, 0x13, 0xed, 0xa5, 0x18, 0xef, 0xe5, 0xb7, 0xe0, 0x8e, 0x2f,
	0x8b, 0x59, 0x6f, 0xbe, 0xd0, 0xdf, 0x86, 0x05, 0xde, 0x5b, 0x68, 0x35, 0xd5, 0x54, 0x60, 0x5d,
	0xd2, 0x2d, 0xfe, 0x57, 0x12, 0xac, 0xa5, 0xd5, 0xc5, 0x2e, 0xfa, 0x5d, 0x00, 0x7e, 0xb3, 0x26,
	0x54, 0x00, 0xcf, 0x53, 0x15, 0x40, 0xb4, 0x22, 0xdf, 0xa6, 0x81, 0xf8, 0xaf, 0x60, 0xff, 0xbb,
	0xf1, 0x7b, 0xb0, 0x18, 0x2d, 0x4c, 0x51, 0x01, 0xdf, 0x8b, 0x9a, 0x53, 0x8d, 0x14, 0x09, 0xe0,
	0xaf, 0x8e, 0xa0, 0x1e, 0x2c, 0xaa, 0xf5, 0xb9, 0x41, 0xdc, 0xb3, 0x86, 0x66, 0xd7, 0x19, 0xd1,
	0x83, 0xbb, 0x7d, 0xc6, 0x12, 0x6b, 0x30, 0xef, 0x5c, 0x5c, 0x60, 0xd3, 0x57, 0x12, 0xfc, 0x2b,
	0xd4, 0x1d, 0x85, 0x98, 0xee, 0x60, 0x79, 0x0d, 0x86, 0x7e, 0xc3, 0x35, 0x70, 0x99, 0x02, 0x0e,
	0xf4, 0x1b, 0xe5, 0x06, 0x36, 0x73, 0xba, 0x62, 0x0c, 0x43, 0x5d, 0x63, 0xad, 0x1f, 0x58, 0x19,
	0x65, 0x0a, 0xd8, 0xb7, 0xbd, 0xe0, 0x8a, 0xb9, 0x20, 0x8d, 0x93, 0x06, 0xd3, 0x37, 0xa6, 0x75,
	0x79, 0xe5, 0x35, 0x07, 0x83, 0x98, 0x35, 0xfe, 0x0f, 0x0a, 0xb0, 0x18, 0x96, 0xfb, 0xf7, 0xe4,
	0xae, 0x74, 0xac, 0xbd, 0xa1, 0x10, 0xda, 0x5b, 0x59, 0xad, 0x5c, 0xe9, 0x98, 0xa1, 0x90, 0x79,
	0xf3, 0x22, 0x2e, 0xc3, 0xd8, 0x17, 0xf1, 0x7d, 0xd8, 0x2f, 0xe2, 0x63, 0x10, 0xdf, 0x17, 0x73,
	0x6b, 0xa6, 0xc6, 0xc0, 0x5d, 0x02, 0xed, 0x61, 0x62, 0x51, 0x72, 0x3c, 0xd3, 0x36, 0x08, 0x16,
	0xf3, 0x8f, 0xab, 0x0c, 0xd8, 0xb2, 0x8d, 0x1e, 0x46, 0x5f, 0x06, 0x38, 0x91, 0x87, 0x0a, 0xa2,
	0xb2, 0xfb, 0x1b, 0xbf, 0x59, 0x7a, 0x05, 0xeb, 0x8d, 0xf0, 0x25, 0x0a, 0xda, 0xf9, 0x88, 0xa0,
	0xdd, 0x02, 0xae, 0x22, 0x59, 0x98, 0xad, 0xc4, 0x4c, 0x21, 0x06, 0x22, 0x84, 0x57, 0xfe, 0x44,
	0x82, 0xd5, 0xd0, 0x86, 0xfc, 0x8d, 0xa2, 0x8a, 0x72, 0x09, 0x0b, 0x81, 0x12, 0x1a, 0x0f, 0xbc,
	0xec, 0x30, 0x40, 0x82, 0x7c, 0x85, 0x5b, 0x91, 0x4f, 0xf9, 0x16, 0xd0, 0x9e, 0xee, 0x31, 0x32,
	0x30, 0x34, 0x2e, 0x03, 0xa3, 0xbe, 0xd2, 0x3c, 0xf7, 0x2d, 0xf7, 0x80, 0x0f, 0x93, 0xa5, 0x85,
	0xb2, 0x5d, 0xf6, 0x41, 0xa4, 0xb3, 0x34, 0x92, 0xaa, 0xf0, 0x26, 0xf8, 0xad, 0x58, 0xd4, 0x49,
	0x8b, 0x76, 0xc9, 0x4e, 0xf2, 0xc6, 0x14, 0x26, 0x70, 0x7e, 0x85, 0x41, 0x08, 0xeb, 0x7f, 0x06,
	0xa5, 0x11, 0xa5, 0x85, 0x9f, 0xaa, 0x10, 0xd7, 0xee, 0x21, 0xb5, 0x54, 0x1f, 0x53, 0xd9, 0xa7,
	0x5d, 0xf1, 0x0d, 0x17, 0x4e, 0xef, 0x56, 0xfb, 0x59, 0xf9, 0xa5, 0x04, 0xcb, 0x89, 0x7d, 0x15,
	0xf8, 0xc3, 0x94, 0x0e, 0xb9, 0x89, 0x44, 0x74, 0x07, 0xd2, 0x3a, 0x5f, 0xa4, 0x51, 0x6f, 0x23,
	0x63, 0x03, 0x27, 0xe8, 0xf6, 0x2d, 0x75, 0x51, 0x63, 0x93, 0xc1, 0x6e, 0xbc, 0x55, 0x29, 0xe5,
	0x9c, 0x38, 0xbb, 0xd5, 0xa8, 0xbc, 0x29, 0x44, 0xe5, 0x8d, 0xf2, 0xf7, 0x0b, 0x54, 0xc0, 0xf3,
	0x3e, 0x9b, 0xb6, 0xf1, 0x8e, 0x34, 0x24, 0x6c, 0xca, 0x93, 0x27, 0x38, 0x9b, 0x16, 0x27, 0xb2,
	0x29, 0xc3, 0xe7, 0xbb, 0xfc, 0x41, 0xc0, 0xe6, 0x3c, 0xd6, 0xc2, 0x0e, 0xe0, 0x39, 0x2f, 0xf3,
	0x50, 0xcb, 0x26, 0x00, 0xb5, 0x90, 0xc3, 0xb8, 0x7a, 0x4d, 0xad, 0x50, 0x08, 0x8d, 0xaa, 0xdf,
	0x85, 0x32, 0xdd, 0x70, 0xa4, 0x90, 0x05, 0x90, 0x4b, 0xa6, 0x6d, 0xd0, 0xa2, 0x78, 0x80, 0xa0,
	0x94, 0x08, 0x10, 0x60, 0x7a, 0xa2, 0x94, 0x24, 0x04, 0x3d, 0x45, 0x4c, 0xdc, 0xf3, 0x9b, 0x5a,
	0x28, 0xe7, 0x93, 0xff, 0x8f, 0x24, 0xf1, 0xaa, 0x08, 0xbf, 0xa1, 0xdf, 0x1c, 0x1b, 0xd6, 0xe4,
	0xcc, 0xbc, 0xcf, 0x63, 0x09, 0xb1, 0x93, 0x2e, 0xfc, 0xfb, 0x49, 0x07, 0x6b, 0x30, 0x3f, 0x32,
	0x75, 0xcc, 0x7d, 0xc7, 0x8a, 0xca, 0xbf, 0xa2, 0x97, 0x56, 0x12, 0x23, 0xc1, 0xae, 0xf2, 0x87,
	0x2c, 0xe8, 0xda, 0xd3, 0xcf, 0xf9, 0x6a, 0x44, 0xce, 0xc0, 0xa4, 0xd8, 0x19, 0xd8, 0x53, 0x58,
	0xb1, 0xb0, 0x86, 0xaf, 0x9c, 0x37, 0x9a, 0x65, 0x6b, 0x43, 0x9d, 0x2c, 0x9a, 0x7e, 0xce, 0x4f,
	0x79, 0x96, 0x2c, 0xdc, 0xbd, 0x72, 0xde, 0xb4, 0xed, 0x63, 0xdd, 0xb2, 0x7b, 0xfa, 0x39, 0x7a,
	0x02, 0x88, 0xa2, 0xd2, 0xa0, 0x99, 0x7e, 0x4e, 0x53, 0x8c, 0x7d, 0x4b, 0x66, 0x89, 0x94, 0x74,
	0xc7, 0xe7, 0x3d, 0xfd, 0xbc, 0x43, 0xc0, 0x84, 0x67, 0xb7, 0x5e, 0x38, 0xa3, 0xbe, 0xc9, 0x36,
	0x20, 0xf1, 0x2f, 0xd5, 0xfe, 0xd0, 0x78, 0x61, 0x9a, 0xc6, 0x9e, 0x8e, 0x4d, 0x9f, 0xe9, 0xfb,
	0x8e, 0x7d, 0x61, 0x5d, 0x0a, 0x56, 0x19, 0x03, 0xe4, 0x99, 0xee, 0xab, 0x30, 0x67, 0xd9, 0x86,
	0xf9, 0x96, 0x8b, 0x75, 0xf6, 0x41, 0xd8, 0x8a, 0x73, 0x9d, 0x2f, 0xc9, 0x4b, 0x8c, 0xe7, 0xe8,
	0xf5, 0x60, 0x2e, 0xe2, 0x19, 0x33, 0xce, 0x99, 0x54, 0xe5, 0x3d, 0x80, 0x9a, 0xe3, 0x9a, 0x23,
	0xdd, 0x73, 0x46, 0x2c, 0x5b, 0x9a, 0x29, 0xae, 0x05, 0x1f, 0x48, 0x2f, 0x36, 0x34, 0x61, 0x49,
	0x77, 0x5d, 0x3a, 0x5d, 0xc6, 0xf2, 0x7e, 0x06, 0x48, 0x74, 0xcf, 0x88, 0x24, 0x57, 0x6b, 0xba,
	0xf0, 0x85, 0x95, 0x7f, 0x5e, 0x84, 0xed, 0x0c, 0x4a, 0xf4, 0x08, 0x8b, 0x51, 0x52, 0xb4, 0xa1,
	0x72, 0xae, 0x63, 0x53, 0x94, 0x63, 0x4f, 0x23, 0x3d, 0x4c, 0xa0, 0xa5, 0x5a, 0x3e, 0xf7, 0xa9,
	0x9a, 0xbc, 0x32, 0x95, 0xfd, 0x5c, 0x51, 0x2c, 0xb9, 0x65, 0xf6, 0x96, 0xc9, 0x2d, 0x0d, 0x1e,
	0xda, 0x25, 0x16, 0xed, 0x9c, 0x90, 0xfc, 0x64, 0x19, 0xe2, 0x5d, 0x53, 0x0f, 0xf3, 0x8d, 0xce,
	0xef, 0xd5, 0xf5, 0x30, 0x59, 0x2d, 0xf6, 0x82, 0x4b, 0xf0, 0xd8, 0x4d, 0xe9, 0x9a, 0x59, 0x5f,
	0xc4, 0xcf, 0x0e, 0xef, 0xc4, 0x07, 0x69, 0x32, 0xd5, 0xe0, 0x46, 0x3c, 0xbb, 0x3d, 0x18, 0xdc,
	0xad, 0x0f, 0xee, 0xcc, 0xfb, 0xfe, 0x1c, 0x41, 0xd8, 0x80, 0x0a, 0x7f, 0x61, 0xc6, 0xf6, 0x78,
	0x1a, 0x42, 0x99, 0xbd, 0x2f, 0xc3, 0x0a, 0xb9, 0x86, 0xf3, 0x30, 0x77, 0xfe, 0xcb, 0x0c, 0xd0,
	0xc3, 0x8a, 0x0e, 0x9b, 0x5d, 0xd3, 0xcb, 0x20, 0x36, 0xd9, 0xea, 0x3f, 0x85, 0xd9, 0x77, 0x5e,
	0x23, 0x5a, 0x53, 0xf9, 0x2d, 0xb8, 0x9f, 0xd7, 0x45, 0xde, 0x3d, 0xba, 0xbf, 0x96, 0xa8, 0xdd,
	0x9a, 0x33, 0xbc, 0xef, 0x72, 0x4b, 0xad, 0x45, 0x1c, 0xf9, 0x9a, 0x28, 0x9c, 0xb8, 0xce, 0x99,
	0x4b, 0xd7, 0x39, 0xf3, 0xa2, 0xce, 0xd9, 0x04, 0x08, 0x04, 0x10, 0xdb, 0x3c, 0x35, 0xb5, 0xe2,
	0x4b, 0x20, 0xac, 0xfc, 0x5d, 0x09, 0xee, 0x1f, 0xe6, 0x93, 0x63, 0x9f, 0x8c, 0x2e, 0x3c, 0x63,
	0xff, 0x78, 0x1a, 0x9a, 0x07, 0x3b, 0x4b, 0x65, 0x75, 0xf3, 0x25, 0xfc, 0xdf, 0x8e, 0x8e, 0x01,
	0x27, 0xe9, 0x9a, 0x8c, 0xfd, 0x85, 0x54, 0x28, 0xa4, 0x53, 0xa1, 0x28, 0x52, 0xe1, 0x2e, 0x94,
	0x7d, 0x91, 0xe9, 0xdf, 0xc3, 0xf3, 0x98, 0xa8, 0x54, 0x2e, 0x60, 0x2b, 0xb7, 0xf3, 0xef, 0x88,
	0x02, 0xca, 0x97, 0xb0, 0x79, 0x60, 0x0e, 0x72, 0x78, 0x87, 0xc6, 0x44, 0x38, 0xef, 0xf8, 0xc9,
	0x8c, 0x15, 0x9f, 0x79, 0xb0, 0xb2, 0x0d, 0xf7, 0xf3, 0xea, 0x63, 0x57, 0xf9, 0xf7, 0x12, 0xac,
	0x76, 0x4d, 0x7d, 0xd4, 0xbf, 0x62, 0x79, 0xde, 0xf4, 0x18, 0x25, 0x3b, 0xba, 0x87, 0x60, 0xd6,
	0x30, 0x71, 0x9f, 0xab, 0x1b, 0xfa, 0x3b, 0x1a, 0x08, 0x8b, 0x27, 0x4f, 0x89, 0x27, 0x49, 0xb3,
	0xf9, 0x27, 0x49, 0x73, 0x93, 0xa2, 0x73, 0xf3, 0xf1, 0x93, 0x24, 0xbe, 0xc2, 0xa5, 0xf0, 0x88,
	0xf3, 0x77, 0xe1, 0x4e, 0xca, 0x6c, 0x26, 0x45, 0xeb, 0x36, 0xe2, 0xce, 0x61, 0x45, 0x70, 0xfe,
	0xba, 0x70, 0xb7, 0x89, 0xb1, 0xd3, 0xb7, 0x7c, 0xdb, 0xdc, 0xf2, 0xae, 0x02, 0x2a, 0x65, 0xe7,
	0x7e, 0xe6, 0x37, 0x7a, 0x0f, 0x1a, 0x59, 0x8d, 0xd2, 0xb4, 0xe7, 0x4d, 0x66, 0x32, 0xf9, 0x39,
	0x7e, 0x7b, 0x37, 0x87, 0xc1, 0xad, 0x3f, 0xd2, 0xed, 0x63, 0x58, 0x16, 0xaf, 0x06, 0x8a, 0xfe,
	0xc6, 0x62, 0x78, 0x3f, 0x90, 0xf6, 0xf4, 0x1f, 0x0b, 0x70, 0x3f, 0xaf, 0x31, 0xec, 0xa2, 0x2e,
	0xd0, 0xb7, 0x28, 0x84, 0x78, 0xc3, 0x8f, 0xe2, 0xf1, 0x86, 0x9c, 0xea, 0xcf, 0xf6, 0xe9, 0x03,
	0x03, 0x7e, 0xd0, 0xb9, 0xcf, 0xbe, 0x90, 0x06, 0xc1, 0xeb, 0x18, 0xb4, 0x61, 0xe6, 0x7a, 0x7c,
	0x71, 0x9b, 0x86, 0x4f, 0x79, 0xfd, 0xa0, 0xf1, 0xaa, 0x1b, 0x42, 0x1a, 0x3f, 0x86, 0x05, 0xb1,
	0xe7, 0x94, 0x13, 0xa2, 0xcc, 0xa8, 0x76, 0xe3, 0x4b, 0x90, 0xe3, 0x8d, 0xdf, 0xa6, 0xbe, 0xf2,
	0x06, 0x56, 0x7b, 0x23, 0xdd, 0xc6, 0xf4, 0x98, 0xc9, 0xe9, 0x7e, 0x3b, 0xe6, 0xb9, 0xba, 0x4f,
	0x00, 0xd9, 0x8e, 0xad, 0xb9, 0xe3, 0xf3, 0x81, 0xd5, 0x8f, 0x9d, 0x06, 0x2d, 0xd9, 0x8e, 0x7d,
	0x4a, 0x0b, 0x4e, 0xfd, 0x9b, 0x57, 0x82, 0x1f, 0x54, 0x98, 0xca, 0x0f, 0x52, 0xfe, 0x00, 0x76,
	0x3a, 0x62, 0x33, 0xec, 0x31, 0x21, 0x6c, 0x39, 0x36, 0x1b, 0x04, 0x8f, 0x40, 0x8d, 0x4d, 0x4c,
	0x3c, 0x87, 0x84, 0x75, 0x1d, 0xf5, 0x37, 0xd3, 0xa6, 0x20, 0xf0, 0xe8, 0x5f, 0x4b, 0xf0, 0x78,
	0x8a, 0xce, 0xb0, 0xeb, 0xd8, 0xd8, 0x44, 0x26, 0x3f, 0x3b, 0xb5, 0x0c, 0x81, 0x8f, 0x5a, 0x91,
	0xfe, 0xa6, 0x6e, 0x8c, 0x27, 0xcb, 0x87, 0xa1, 0x2c, 0xd7, 0xff, 0x6e, 0x7c, 0xc1, 0x22, 0x31,
	0x61, 0xe1, 0xad, 0xd6, 0xed, 0xff, 0x49, 0xf0, 0x90, 0xc9, 0x87, 0xb0, 0xfb, 0xc8, 0xa8, 0x7c,
	0xda, 0x11, 0x73, 0x45, 0xf7, 0xcc, 0x4b, 0x67, 0x74, 0x13, 0xae, 0x20, 0xf8, 0xa0, 0x3c, 0xbd,
	0xfc, 0x0c, 0x56, 0xe8, 0xbe, 0x66, 0xaf, 0xeb, 0xd1, 0x27, 0x12, 0xc3, 0xe8, 0xea, 0x32, 0x8e,
	0x3e, 0x9e, 0xc8, 0x6e, 0x0a, 0xa6, 0xb0, 0xcc, 0x6c, 0x3a, 0xcb, 0x70, 0x81, 0x37, 0x97, 0xa6,
	0xd2, 0xe6, 0xd3, 0x55, 0x5a, 0x49, 0x74, 0xc8, 0xff, 0x45, 0x11, 0x36, 0x32, 0x66, 0x9e, 0xff,
	0x78, 0x63, 0xfa, 0x28, 0x0b, 0xb9, 0xa3, 0x2c, 0x86, 0xa3, 0xf4, 0xaf, 0x37, 0xcf, 0x0a, 0xd7,
	0x9b, 0x37, 0xa0, 0x62, 0x5b, 0xfd, 0xd7, 0x4c, 0xbf, 0xf2, 0x27, 0x71, 0x08, 0x80, 0xda, 0xf0,
	0x31, 0xfa, 0xcf, 0x27, 0xe8, 0xff, 0x00, 0x6a, 0x01, 0x02, 0x6d, 0xa1, 0xc4, 0x3c, 0x01, 0x1f,
	0x48, 0x5b, 0xc9, 0x58, 0x8b, 0x72, 0xd6, 0x5a, 0x3c, 0x87, 0x3b, 0x09, 0x7c, 0xda, 0x38, 0xbb,
	0x97, 0xbe, 0x12, 0xab, 0x11, 0x8c, 0x54, 0x08, 0x96, 0xf9, 0x09, 0xb4, 0x41, 0xb0, 0x8c, 0xe8,
	0x30, 0xfe, 0x6e, 0xc6, 0x38, 0x38, 0xb9, 0xaa, 0x30, 0xc8, 0x99, 0x45, 0x33, 0x8f, 0x7d, 0xef,
	0x85, 0x9f, 0x5e, 0x05, 0xdf, 0xca, 0x9f, 0x4a, 0xf0, 0xe1, 0x04, 0x76, 0xe5, 0xbb, 0xef, 0x1c,
	0xd6, 0x33, 0x50, 0xf8, 0x4e, 0xdc, 0x89, 0x9f, 0xb1, 0x65, 0xf1, 0x80, 0x9a, 0xd5, 0x50, 0x78,
	0x82, 0x5b, 0xa0, 0x99, 0xed, 0xfc, 0xd2, 0x42, 0x1f, 0x1e, 0xd0, 0x78, 0xf4, 0x84, 0x0d, 0xf5,
	0x45, 0x60, 0x83, 0xdf, 0x6e, 0x34, 0xcc, 0xfe, 0xfe, 0x08, 0x1e, 0xe6, 0x77, 0xc2, 0xc8, 0xa0,
	0x9c, 0xc2, 0x67, 0x49, 0x3c, 0x57, 0xc0, 0xeb, 0x3b, 0x23, 0x03, 0xef, 0xdd, 0xf0, 0x5b, 0x22,
	0xfe, 0xe0, 0xc4, 0xbb, 0x2b, 0x52, 0xe4, 0xee, 0x8a, 0xf2, 0x37, 0x05, 0xf8, 0xfe, 0xed, 0x9a,
	0xe4, 0x2b, 0xf2, 0xa7, 0x12, 0x34, 0x32, 0x86, 0x7b, 0x1c, 0xc8, 0x47, 0x3d, 0x19, 0xd7, 0xbf,
	0x65, 0x3f, 0xcf, 0xb2, 0xfb, 0x60, 0xb2, 0x33, 0x67, 0x10, 0x8d, 0x37, 0xb0, 0x35, 0xa1, 0x7a,
	0xea, 0x43, 0xbc, 0x91, 0x83, 0x82, 0xe9, 0x97, 0x32, 0x94, 0xc3, 0xbb, 0x1f, 0x43, 0x55, 0x38,
	0xe6, 0x45, 0x35, 0xa8, 0xbc, 0x38, 0x51, 0x8f, 0x9b, 0xbd, 0x5e, 0xeb, 0x40, 0x9e, 0x41, 0x4b,
	0x50, 0x3d, 0x51, 0xdb, 0x87, 0xed, 0x8e, 0xc6, 0x9e, 0xb3, 0xdb, 0xfd, 0xcb, 0x02, 0x40, 0xf8,
	0x84, 0x03, 0x5a, 0x05, 0xf9, 0xf4, 0xa4, 0xdb, 0xd3, 0x08, 0x92, 0xd6, 0x21, 0x15, 0x8f, 0xe4,
	0x19, 0xb4, 0x0e, 0x2b, 0x3e, 0xb4, 0xa3, 0xb5, 0xf7, 0x5b, 0xda, 0x9e, 0xda, 0x6a, 0x7e, 0x2d,
	0x4b, 0x68, 0x19, 0x6a, 0x61, 0x41, 0x73, 0xbf, 0x27, 0x17, 0xd0, 0x1a, 0xa0, 0xb0, 0x05, 0xb5,
	0x75, 0xd8, 0xee, 0xf6, 0x5a, 0xaa, 0x5c, 0x14, 0x51, 0x0f, 0xb5, 0xaf, 0x4f, 0x8e, 0xe4, 0xd9,
	0x28, 0xa8, 0x77, 0x78, 0x24, 0xcf, 0x89, 0x3d, 0x51, 0x90, 0xb6, 0x7f, 0xf2, 0xb2, 0xa5, 0xca,
	0xf3, 0xd1, 0x66, 0x9b, 0x6d, 0x4d, 0x6d, 0x9e, 0xb6, 0x54, 0xb9, 0x84, 0x3e, 0x80, 0xcd, 0x08,
	0xfc, 0xc5, 0x89, 0xda, 0x6a, 0x1f, 0x76, 0xb4, 0xa3, 0x66, 0xe7, 0xf0, 0xac, 0x79, 0xd8, 0x92,
	0xcb, 0x68, 0x13, 0xee, 0x86, 0x28, 0x87, 0xad, 0x4e, 0x4b, 0x6d, 0x92, 0x76, 0x3b, 0xbd, 0x56,
	0xa7, 0x27, 0x57, 0xd0, 0x1a, 0x2c, 0x0b, 0x2d, 0x74, 0xf6, 0xdb, 0x04, 0xfc, 0xdf, 0x4a, 0xe8,
	0x21, 0x6c, 0x89, 0xa4, 0xe8, 0x68, 0xa7, 0x67, 0x7b, 0x47, 0xed, 0x7d, 0x52, 0xf3, 0x65, 0x4b,
	0xed, 0xb6, 0x4f, 0x3a, 0xf2, 0x7f, 0x2f, 0xed, 0xfe, 0x10, 0xca, 0x6d, 0xdc, 0xa5, 0xaf, 0x5e,
	0x22, 0x04, 0x8b, 0xb4, 0xc6, 0xde, 0x2b, 0xed, 0xb4, 0x75, 0x72, 0x7a, 0xd4, 0x12, 0x48, 0xb7,
	0xf7, 0x4a, 0xeb, 0xb6, 0x7a, 0xbd, 0x76, 0xe7, 0x50, 0xeb, 0x35, 0x0f, 0x65, 0x69, 0x57, 0x87,
	0xda, 0x59, 0xe4, 0x15, 0xa1, 0x06, 0xac, 0x9d, 0x75, 0x8e, 0x9b, 0x6a, 0xf7, 0xab, 0xe6, 0x91,
	0xd6, 0x7b, 0x75, 0xda, 0xd2, 0x0e, 0x5a, 0x2f, 0x9a, 0x67, 0x47, 0x3d, 0x79, 0x06, 0x6d, 0xc0,
	0x7a, 0xac, 0xec, 0x54, 0x3d, 0xe9, 0x9d, 0xec, 0x9d, 0xbd, 0x90, 0x25, 0xd2, 0x45, 0xac, 0xf0,
	0x67, 0xdd, 0x93, 0x8e, 0x5c, 0xd8, 0xed, 0x43, 0x6d, 0x3f, 0xf2, 0x02, 0x11, 0x82, 0x45, 0xbe,
	0xfa, 0x61, 0xd3, 0x75, 0x58, 0xe5, 0xb0, 0xc3, 0xe6, 0x71, 0x4b, 0x3b, 0x68, 0x77, 0x7b, 0x6a,
	0x7b, 0xbf, 0x27, 0x4b, 0xe8, 0x21, 0x6c, 0xf3, 0x92, 0xee, 0xc9, 0x7e, 0x9b, 0xd2, 0xec, 0xf8,
	0xf8, 0xac, 0xd3, 0xee, 0xbd, 0x0a, 0xb1, 0x0a, 0xbb, 0x1d, 0x58, 0xd8, 0x13, 0x5f, 0x24, 0xb8,
	0x0b, 0x77, 0xf6, 0xce, 0xba, 0xed, 0x4e, 0xab, 0xdb, 0x8d, 0xcf, 0xe2, 0x01, 0x6c, 0x45, 0x8b,
	0xf6, 0xbf, 0x6a, 0x76, 0x3a, 0xad, 0x23, 0xed, 0xf4, 0xa8, 0xf9, 0x8a, 0x76, 0x2f, 0x4b, 0xbb,
	0xaf, 0xe0, 0x6e, 0xe6, 0x79, 0x32, 0x5a, 0x81, 0xa5, 0x53, 0xb5, 0xfd, 0xb2, 0xb9, 0xff, 0x4a,
	0x68, 0x56, 0x00, 0xd2, 0xbf, 0xbd, 0x96, 0x2c, 0xd1, 0xb5, 0xf0, 0x81, 0x74, 0xdd, 0xe4, 0xc2,
	0xee, 0x19, 0x0b, 0x99, 0x47, 0x12, 0x73, 0xd0, 0x16, 0x6c, 0xd0, 0x05, 0x0a, 0xb0, 0x4f, 0x8e,
	0xda, 0x91, 0xe6, 0x33, 0x10, 0x82, 0xae, 0x76, 0x77, 0x00, 0xc2, 0xf7, 0x0c, 0x50, 0x15, 0x4a,
	0x61, 0xdd, 0x1a, 0x54, 0x4e, 0x3a, 0x47, 0xaf, 0xb4, 0x9f, 0x9f, 0x74, 0x08, 0xe6, 0xdf, 0x2b,
	0x04, 0x2b, 0x12, 0x1c, 0x14, 0xad, 0x70, 0x4e, 0xd4, 0xba, 0xbd, 0x66, 0xef, 0xac, 0xab, 0xf1,
	0x2b, 0x84, 0x5b, 0xb0, 0x11, 0x2b, 0x38, 0xeb, 0x1c, 0xb4, 0x54, 0x4d, 0x6d, 0xbd, 0x6c, 0xb7,
	0xbe, 0x91, 0x25, 0xf4, 0x14, 0x76, 0x72, 0x10, 0xb4, 0x66, 0xe7, 0x40, 0x3b, 0x55, 0x5b, 0xa7,
	0x4d, 0xb5, 0x75, 0x20, 0x17, 0xc8, 0x1e, 0x88, 0x61, 0x77, 0xcf, 0xba, 0xa7, 0xed, 0xfd, 0xf6,
	0xc9, 0x59, 0x57, 0x2e, 0x12, 0xde, 0x8b, 0x15, 0xb7, 0x8f, 0x8e, 0x5a, 0x87, 0x4d, 0xb2, 0x4b,
	0xef, 0xc2, 0x9d, 0xc4, 0x10, 0xa9, 0x5c, 0x98, 0x4b, 0xa9, 0x76, 0xd0, 0x3a, 0x6a, 0x11, 0x49,
	0x33, 0x9f, 0x52, 0x6d, 0x8f, 0x2c, 0xf6, 0x81, 0x5c, 0xda, 0x7d, 0x02, 0x0b, 0x62, 0x4a, 0x03,
	0x11, 0x4a, 0xdd, 0x5e, 0x7b, 0xff, 0xeb, 0x57, 0xfe, 0xe4, 0x01, 0xe6, 0x19, 0x40, 0x96, 0x76,
	0xbb, 0xb0, 0x20, 0x86, 0xe0, 0x91, 0x4c, 0xbf, 0xbb, 0xde, 0x81, 0x79, 0xa1, 0x8f, 0x07, 0x9e,
	0x3c, 0x83, 0x16, 0x01, 0x28, 0xe4, 0xa5, 0x3e, 0xb0, 0x0c, 0x59, 0x0a, 0x30, 0x5a, 0x6f, 0x5d,
	0x6b, 0x64, 0x1a, 0x72, 0x81, 0x74, 0x40, 0x21, 0x2f, 0xc6, 0xde, 0x78, 0x64, 0xca, 0xc5, 0x5d,
	0x83, 0x71, 0x42, 0x24, 0xda, 0x4c, 0x58, 0x86, 0x03, 0xc2, 0xb6, 0xef, 0xc0, 0xb2, 0x8f, 0x34,
	0xc6, 0xae, 0xd5, 0xb7, 0x9c, 0x31, 0x66, 0x72, 0x8f, 0x83, 0x3b, 0x34, 0x53, 0x4a, 0x2e, 0x08,
	0x20, 0xd5, 0xfc, 0x03, 0xb3, 0xef, 0xc9, 0xc5, 0xdd, 0x2e, 0x6c, 0x66, 0xc4, 0x07, 0x78, 0x8f,
	0x15, 0x98, 0xa3, 0x71, 0x09, 0x79, 0x06, 0x2d, 0x40, 0xb9, 0xe3, 0x78, 0xd4, 0x35, 0x97, 0x25,
	0xf2, 0xd5, 0xb6, 0xf7, 0x69, 0xac, 0x41, 0x2e, 0x10, 0x96, 0xf2, 0xe7, 0x52, 0x7c, 0xfe, 0x7f,
	0x3e, 0x01, 0x38, 0xbb, 0xec, 0xfb, 0xa7, 0xef, 0xaf, 0x40, 0x8e, 0xe7, 0x33, 0xa0, 0xed, 0x68,
	0xc4, 0x32, 0x99, 0x58, 0xd1, 0xf8, 0x60, 0x02, 0x06, 0x76, 0x95, 0x19, 0x74, 0x0e, 0x2b, 0x29,
	0x59, 0x0a, 0xe8, 0x41, 0xec, 0x9a, 0x6c, 0x5a, 0xd2, 0x44, 0xe3, 0xe1, 0x64, 0x24, 0xda, 0xc7,
	0x2b, 0x90, 0xe3, 0x19, 0x0a, 0xb1, 0xe1, 0xa7, 0x64, 0x43, 0xc4, 0x86, 0x9f, 0x96, 0xe2, 0xa0,
	0xcc, 0x20, 0x8d, 0x1e, 0x22, 0xc6, 0x4e, 0xf3, 0x91, 0x32, 0xf1, 0xb8, 0xff, 0xdb, 0xc6, 0x83,
	0x29, 0x52, 0x02, 0x94, 0x19, 0xe4, 0xf9, 0x37, 0xa8, 0x53, 0xd2, 0x74, 0xd0, 0xe3, 0x94, 0xe3,
	0xc7, 0xf4, 0x94, 0xa3, 0xc6, 0xee, 0xb4, 0xa8, 0xb4, 0x57, 0x1b, 0xd6, 0x33, 0x32, 0x69, 0xd0,
	0xa3, 0x94, 0x86, 0xd2, 0x12, 0x84, 0x1a, 0x3b, 0xd3, 0x21, 0xd2, 0xfe, 0xcc, 0x68, 0x0e, 0xaa,
	0xff, 0x98, 0x24, 0x7a, 0x98, 0x99, 0x75, 0x2a, 0xbc, 0xa7, 0xd9, 0xf8, 0x70, 0x0a, 0x2c, 0xda,
	0xcd, 0x97, 0x50, 0xe2, 0x6f, 0xaa, 0xa0, 0xf5, 0x38, 0x73, 0xfa, 0xeb, 0x52, 0x4f, 0x2f, 0xa0,
	0xf5, 0x5f, 0xc3, 0x5a, 0xfa, 0x33, 0x1a, 0x28, 0x9a, 0xab, 0x94, 0xf9, 0x1e, 0x48, 0xe3, 0xd1,
	0x54, 0x78, 0xb4, 0xb3, 0x9f, 0x41, 0x55, 0x78, 0xbd, 0x01, 0xc5, 0x9f, 0xda, 0x11, 0x9f, 0xa9,
	0x68, 0xdc, 0xcb, 0x2e, 0xf4, 0x27, 0xce, 0x1f, 0xba, 0x88, 0x4d, 0x3c, 0x7c, 0x25, 0x23, 0x36,
	0x71, 0xf1, 0x5d, 0x8c, 0x19, 0xd4, 0x85, 0xc5, 0xe8, 0xbd, 0x6e, 0x74, 0x3f, 0xff, 0x1e, 0x7b,
	0x63, 0x2b, 0xb7, 0xdc, 0xdf, 0x96, 0xf1, 0xcb, 0xe2, 0xb1, 0x6d, 0x99, 0x72, 0x81, 0x3d, 0xb6,
	0x2d, 0xd3, 0x6e, 0x9b, 0x2b, 0x33, 0xe8, 0x25, 0x2c, 0xc5, 0x6e, 0x50, 0xa3, 0xac, 0x01, 0xf9,
	0xf7, 0xb4, 0x1b, 0xdb, 0xf9, 0x08, 0xb4, 0xdd, 0x53, 0x22, 0x7f, 0x85, 0xec, 0x61, 0xb4, 0x99,
	0x58, 0x4f, 0x31, 0x3f, 0xb9, 0x71, 0x3f, 0xaf, 0x38, 0xba, 0xd3, 0x92, 0x46, 0xc3, 0xa3, 0x8c,
	0xe4, 0x82, 0x78, 0xee, 0x71, 0xea, 0x4e, 0x4b, 0x4f, 0x0e, 0x9e, 0x41, 0xdf, 0x42, 0x3d, 0x2b,
	0xcd, 0x17, 0xed, 0x24, 0x25, 0x5e, 0x7a, 0x16, 0x71, 0xe3, 0xf1, 0x94, 0x98, 0xb4, 0xcb, 0xdf,
	0x87, 0xe5, 0x44, 0xe2, 0x2d, 0x8a, 0x2e, 0x63, 0x5a, 0xee, 0x6f, 0x43, 0x99, 0x84, 0xe2, 0xef,
	0xc9, 0xf4, 0x84, 0xda, 0xd8, 0x9e, 0xcc, 0x4c, 0xf1, 0x8d, 0xed, 0xc9, 0xec, 0xec, 0x5c, 0x65,
	0x06, 0x19, 0xfe, 0xfb, 0x06, 0xd1, 0x27, 0x01, 0xd0, 0xc3, 0x14, 0xde, 0x49, 0x3c, 0x34, 0xd0,
	0x98, 0x02, 0x8b, 0xf6, 0xf2, 0xb7, 0xe8, 0xed, 0xd5, 0x58, 0x17, 0x09, 0x75, 0x94, 0x6c, 0x7f,
	0x12, 0x0a, 0x6d, 0xfc, 0x90, 0x3e, 0x89, 0xc8, 0x73, 0x57, 0x51, 0x23, 0x2e, 0xed, 0xc2, 0x8b,
	0xbb, 0x8d, 0x8d, 0xcc, 0x32, 0x5f, 0x26, 0x44, 0x2f, 0x16, 0xc7, 0x64, 0x42, 0xe2, 0x9a, 0x77,
	0x4c, 0x26, 0x24, 0x6f, 0x25, 0xc7, 0x1b, 0xa5, 0x72, 0x2f, 0xab, 0x51, 0x5f, 0xf4, 0x6d, 0xe5,
	0x96, 0xc7, 0xb5, 0x8b, 0x78, 0x2d, 0x34, 0x43, 0xbb, 0xc4, 0xae, 0xcd, 0x66, 0x68, 0x97, 0xf8,
	0xfd, 0x52, 0x46, 0xd9, 0xf0, 0x1e, 0x72, 0x8c, 0xb2, 0x91, 0x2b, 0xd1, 0x31, 0xca, 0xc6, 0x2e,
	0x2f, 0x53, 0xc9, 0x2f, 0xdc, 0x54, 0x44, 0x89, 0x75, 0x10, 0xee, 0x2b, 0xc6, 0x24, 0x7f, 0xfc,
	0x82, 0x23, 0x6d, 0x4b, 0xb8, 0xfe, 0x87, 0x12, 0x3d, 0x67, 0xb7, 0x15, 0xbf, 0x35, 0x48, 0x6d,
	0xb5, 0x94, 0xfb, 0x77, 0x31, 0x5b, 0x2d, 0xfd, 0xce, 0x5f, 0x8c, 0xf7, 0x33, 0xae, 0xf1, 0x31,
	0x79, 0x98, 0x71, 0x97, 0x2e, 0x26, 0x0f, 0xb3, 0x6f, 0xe8, 0x35, 0x76, 0xa6, 0x43, 0xa4, 0xfd,
	0xbd, 0x81, 0x46, 0xf6, 0x9d, 0x3a, 0x14, 0xb5, 0x9a, 0x72, 0xef, 0xeb, 0x35, 0x9e, 0x4c, 0x8d,
	0xeb, 0xcb, 0xad, 0xf4, 0x67, 0x82, 0x62, 0x72, 0x2b, 0xf3, 0xed, 0xa3, 0xc6, 0xa3, 0xa9, 0xf0,
	0x7c, 0x7d, 0x18, 0x7b, 0x42, 0x36, 0xa6, 0x0f, 0x93, 0x2f, 0x6f, 0x37, 0xb6, 0xf3, 0x11, 0x7c,
	0xee, 0x12, 0x9e, 0xde, 0x8a, 0x71, 0x57, 0xf4, 0xb1, 0xb0, 0x18, 0x77, 0xc5, 0x5f, 0xec, 0xa2,
	0x5b, 0x3f, 0x7a, 0x7b, 0x39, 0xb6, 0xf5, 0x13, 0x57, 0xa8, 0x63, 0x5b, 0x3f, 0xe5, 0xea, 0x33,
	0x6d, 0x34, 0xfa, 0x2c, 0x51, 0xac, 0xd1, 0xc4, 0x53, 0x4c, 0xb1, 0x46, 0x53, 0xde, 0x34, 0x12,
	0x96, 0x2e, 0xfe, 0x12, 0x51, 0xea, 0xd2, 0xa5, 0x3c, 0x7d, 0xd4, 0x78, 0x34, 0x15, 0x1e, 0xed,
	0xec, 0x00, 0x2a, 0xc1, 0x65, 0x49, 0x94, 0xc8, 0x80, 0x09, 0x6e, 0x7a, 0x36, 0x1a, 0x59, 0x45,
	0x51, 0x3a, 0xf8, 0x37, 0xb6, 0x52, 0xe9, 0x20, 0xdc, 0x1c, 0x4b, 0xa5, 0x43, 0xe4, 0x0e, 0xa5,
	0xef, 0x57, 0x45, 0x6e, 0xd3, 0x25, 0xfd, 0xaa, 0xf8, 0x1d, 0xcb, 0xa4, 0x96, 0x4a, 0x5c, 0xc7,
	0x8b, 0x18, 0xbf, 0x74, 0xb0, 0xa9, 0xc6, 0xaf, 0x3f, 0xd2, 0x7b, 0xd9, 0x85, 0xfe, 0x30, 0xe3,
	0xf7, 0xd5, 0x62, 0xc3, 0x4c, 0xb9, 0x39, 0x17, 0x1b, 0x66, 0xda, 0x85, 0xb7, 0x40, 0x22, 0xc6,
	0xaf, 0x97, 0x24, 0x25, 0x62, 0xca, 0xf5, 0x9b, 0xa4, 0x44, 0x4c, 0xbb, 0xa5, 0xc2, 0x3c, 0xc0,
	0xcc, 0xbc, 0x69, 0xf4, 0x38, 0x83, 0x98, 0xc9, 0x54, 0xee, 0x98, 0x07, 0x98, 0x9b, 0x8a, 0xcd,
	0x24, 0x46, 0x2c, 0x55, 0x15, 0xc5, 0xb7, 0x5b, 0x3c, 0x77, 0xb6, 0xb1, 0x9d, 0x8f, 0x20, 0x2c,
	0x46, 0x24, 0x95, 0x33, 0xb9, 0x18, 0xf1, 0xb4, 0xd5, 0xe4, 0x62, 0x24, 0x72, 0x41, 0xd9, 0x62,
	0xa4, 0x24, 0x2a, 0xa2, 0x07, 0x19, 0x75, 0xc5, 0x9c, 0xce, 0xc6, 0xc3, 0xc9, 0x48, 0xb4, 0x8f,
	0x6b, 0xf1, 0x12, 0x60, 0x3c, 0x19, 0x10, 0xed, 0x66, 0xec, 0x99, 0x94, 0xfc, 0xc5, 0xc6, 0xd4,
	0xb8, 0xbe, 0x9a, 0xca, 0x4e, 0x60, 0x8a, 0xf5, 0x9b, 0x9b, 0x4c, 0x15, 0x53, 0x53, 0xf9, 0x59,
	0x51, 0xac, 0xe3, 0xc3, 0x69, 0x3b, 0x3e, 0xbc, 0x45, 0xc7, 0x87, 0x53, 0x74, 0x9c, 0x9d, 0xfa,
	0x12, 0xeb, 0x38, 0x37, 0xc7, 0x26, 0xd6, 0xf1, 0x84, 0x7c, 0x9a, 0x19, 0xf4, 0x0b, 0x7a, 0x81,
	0x32, 0x2b, 0x37, 0x08, 0x65, 0x4f, 0x23, 0x99, 0xc2, 0xd4, 0x78, 0x3a, 0x3d, 0x72, 0xe8, 0x2a,
	0xc5, 0xd2, 0x5f, 0x12, 0xae, 0x52, 0x32, 0xd9, 0x27, 0xe1, 0x2a, 0xa5, 0x64, 0xd0, 0x30, 0xbd,
	0x95, 0x9e, 0xb4, 0x12, 0xd3, 0x5b, 0x99, 0xe9, 0x32, 0x31, 0xbd, 0x95, 0x93, 0x01, 0x33, 0x83,
	0xae, 0xe0, 0x4e, 0xea, 0xab, 0xf5, 0xe8, 0xc3, 0x3c, 0xbb, 0x22, 0xf8, 0x87, 0x03, 0x8d, 0x8f,
	0xa6, 0x41, 0x8b, 0x86, 0xc8, 0x52, 0x5e, 0x95, 0x4f, 0x0d, 0x91, 0xa5, 0x3f, 0x88, 0x9f, 0xb9,
	0x23, 0xd3, 0x1e, 0xaa, 0xa7, 0xfc, 0x99, 0x9d, 0xfe, 0x12, 0xe3, 0xcf, 0xdc, 0x64, 0xa0, 0xc6,
	0x93, 0x5b, 0xe4, 0xd4, 0x28, 0x33, 0xe8, 0x1f, 0x49, 0xf0, 0xc1, 0xc4, 0x4c, 0x0c, 0xf4, 0x83,
	0xdb, 0x66, 0x6e, 0xd0, 0x93, 0xd4, 0xc6, 0xe7, 0xef, 0x96, 0xf0, 0xa1, 0xcc, 0xa0, 0x3f, 0x92,
	0x60, 0x33, 0xf7, 0xac, 0x1b, 0x7d, 0x9a, 0xc2, 0xa4, 0xf9, 0xa7, 0xce, 0x8d, 0xe7, 0xb7, 0xa9,
	0x12, 0x0c, 0xe5, 0x97, 0x12, 0xdc, 0xcb, 0x3b, 0x6e, 0x46, 0xdf, 0x9b, 0x70, 0x6c, 0x9b, 0x1c,
	0xc8, 0xa7, 0xb7, 0xa8, 0x11, 0x8c, 0xe3, 0xdf, 0x4a, 0xf0, 0xf4, 0x36, 0x67, 0xc2, 0xe8, 0xa7,
	0xef, 0x71, 0x9c, 0xcc, 0xc6, 0xd9, 0x7c, 0xef, 0x03, 0x69, 0x65, 0x66, 0xef, 0x93, 0x9f, 0x7f,
	0x7c, 0xe9, 0x0c, 0x74, 0xfb, 0xf2, 0xd9, 0x0f, 0x9e, 0x7b, 0xde, 0xb3, 0xbe, 0x33, 0xfc, 0x84,
	0xfe, 0xd7, 0xd7, 0xbe, 0x33, 0xf8, 0x04, 0x9b, 0xa3, 0x6b, 0xab, 0x6f, 0xe2, 0x4f, 0x84, 0xff,
	0x09, 0x7b, 0x3e, 0x4f, 0x8b, 0x3f, 0xfb, 0xff, 0x01, 0x00, 0x00, 0xff, 0xff, 0xdd, 0xaa, 0xee,
	0x38, 0x29, 0x76, 0x00, 0x00,
}
