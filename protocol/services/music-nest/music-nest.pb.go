// Code generated by protoc-gen-go. DO NOT EDIT.
// source: music-nest/music-nest.proto

package music_nest // import "golang.52tt.com/protocol/services/music-nest"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type LevelType int32

const (
	LevelType_UNKNOW_LEVEL_TYPE LevelType = 0
	LevelType_SUPER_LEVEL       LevelType = 1
	LevelType_A_LEVEL           LevelType = 2
	LevelType_B_LEVEL           LevelType = 3
	LevelType_C_LEVEL           LevelType = 4
)

var LevelType_name = map[int32]string{
	0: "UNKNOW_LEVEL_TYPE",
	1: "SUPER_LEVEL",
	2: "A_LEVEL",
	3: "B_LEVEL",
	4: "C_LEVEL",
}
var LevelType_value = map[string]int32{
	"UNKNOW_LEVEL_TYPE": 0,
	"SUPER_LEVEL":       1,
	"A_LEVEL":           2,
	"B_LEVEL":           3,
	"C_LEVEL":           4,
}

func (x LevelType) String() string {
	return proto.EnumName(LevelType_name, int32(x))
}
func (LevelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{0}
}

type LiveStatus int32

const (
	LiveStatus_UNKNOWN_STATUS   LiveStatus = 0
	LiveStatus_NOT_START_STATUS LiveStatus = 1
	LiveStatus_WARN_UP_STATUS   LiveStatus = 2
	LiveStatus_LIVING_STATUS    LiveStatus = 3
	LiveStatus_CLOSED_STATUS    LiveStatus = 4
)

var LiveStatus_name = map[int32]string{
	0: "UNKNOWN_STATUS",
	1: "NOT_START_STATUS",
	2: "WARN_UP_STATUS",
	3: "LIVING_STATUS",
	4: "CLOSED_STATUS",
}
var LiveStatus_value = map[string]int32{
	"UNKNOWN_STATUS":   0,
	"NOT_START_STATUS": 1,
	"WARN_UP_STATUS":   2,
	"LIVING_STATUS":    3,
	"CLOSED_STATUS":    4,
}

func (x LiveStatus) String() string {
	return proto.EnumName(LiveStatus_name, int32(x))
}
func (LiveStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{1}
}

type MaterialType int32

const (
	MaterialType_UNKNOW_MATERIAL_TYPE MaterialType = 0
	MaterialType_WORD_TYPE            MaterialType = 1
	MaterialType_IMAGE_TYPE           MaterialType = 2
	MaterialType_VIDEO_TYPE           MaterialType = 3
)

var MaterialType_name = map[int32]string{
	0: "UNKNOW_MATERIAL_TYPE",
	1: "WORD_TYPE",
	2: "IMAGE_TYPE",
	3: "VIDEO_TYPE",
}
var MaterialType_value = map[string]int32{
	"UNKNOW_MATERIAL_TYPE": 0,
	"WORD_TYPE":            1,
	"IMAGE_TYPE":           2,
	"VIDEO_TYPE":           3,
}

func (x MaterialType) String() string {
	return proto.EnumName(MaterialType_name, int32(x))
}
func (MaterialType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{2}
}

type PatternType int32

const (
	PatternType_UNKNOW_PATTERN_TYPE PatternType = 0
	PatternType_CP_TYPE             PatternType = 1
	PatternType_NORMAL_TYPE_1       PatternType = 2
	PatternType_NORMAL_TYPE_2       PatternType = 3
	PatternType_BATTLE_TYPE_1V1     PatternType = 4
	PatternType_BATTLE_TYPE_3V3     PatternType = 5
	PatternType_BATTLE_HOME_TYPE    PatternType = 6
)

var PatternType_name = map[int32]string{
	0: "UNKNOW_PATTERN_TYPE",
	1: "CP_TYPE",
	2: "NORMAL_TYPE_1",
	3: "NORMAL_TYPE_2",
	4: "BATTLE_TYPE_1V1",
	5: "BATTLE_TYPE_3V3",
	6: "BATTLE_HOME_TYPE",
}
var PatternType_value = map[string]int32{
	"UNKNOW_PATTERN_TYPE": 0,
	"CP_TYPE":             1,
	"NORMAL_TYPE_1":       2,
	"NORMAL_TYPE_2":       3,
	"BATTLE_TYPE_1V1":     4,
	"BATTLE_TYPE_3V3":     5,
	"BATTLE_HOME_TYPE":    6,
}

func (x PatternType) String() string {
	return proto.EnumName(PatternType_name, int32(x))
}
func (PatternType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{3}
}

type BrandIntegralStatus int32

const (
	BrandIntegralStatus_UnKnowStatus       BrandIntegralStatus = 0
	BrandIntegralStatus_AlreadyAddIntegral BrandIntegralStatus = 1
	BrandIntegralStatus_NoAddIntegral      BrandIntegralStatus = 2
)

var BrandIntegralStatus_name = map[int32]string{
	0: "UnKnowStatus",
	1: "AlreadyAddIntegral",
	2: "NoAddIntegral",
}
var BrandIntegralStatus_value = map[string]int32{
	"UnKnowStatus":       0,
	"AlreadyAddIntegral": 1,
	"NoAddIntegral":      2,
}

func (x BrandIntegralStatus) String() string {
	return proto.EnumName(BrandIntegralStatus_name, int32(x))
}
func (BrandIntegralStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{4}
}

// 节目清单协议
type StageType int32

const (
	StageType_UNKNOWN_STAGE_TYPE StageType = 0
	StageType_SOLO               StageType = 1
	StageType_BATTLE             StageType = 2
	StageType_INTRODUCTION       StageType = 3
)

var StageType_name = map[int32]string{
	0: "UNKNOWN_STAGE_TYPE",
	1: "SOLO",
	2: "BATTLE",
	3: "INTRODUCTION",
}
var StageType_value = map[string]int32{
	"UNKNOWN_STAGE_TYPE": 0,
	"SOLO":               1,
	"BATTLE":             2,
	"INTRODUCTION":       3,
}

func (x StageType) String() string {
	return proto.EnumName(StageType_name, int32(x))
}
func (StageType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{5}
}

type MusicNestPerformanceType int32

const (
	MusicNestPerformanceType_INVALID_MUSIC_NEST_PERFORMANCE_TYPE MusicNestPerformanceType = 0
	MusicNestPerformanceType_NORMAL_PERFORMANCE                  MusicNestPerformanceType = 1
	MusicNestPerformanceType_NEXT_ACTIVITY                       MusicNestPerformanceType = 2
)

var MusicNestPerformanceType_name = map[int32]string{
	0: "INVALID_MUSIC_NEST_PERFORMANCE_TYPE",
	1: "NORMAL_PERFORMANCE",
	2: "NEXT_ACTIVITY",
}
var MusicNestPerformanceType_value = map[string]int32{
	"INVALID_MUSIC_NEST_PERFORMANCE_TYPE": 0,
	"NORMAL_PERFORMANCE":                  1,
	"NEXT_ACTIVITY":                       2,
}

func (x MusicNestPerformanceType) String() string {
	return proto.EnumName(MusicNestPerformanceType_name, int32(x))
}
func (MusicNestPerformanceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{6}
}

// 是否乐窝活动
// - 前置条件
// - 针对部分房间
// - 在运营管理后台-乐窝活动，配置状态处于已上架的活动对应的房间channelID
// - 在指定时间
// - 根据该活动所配置的生效时间（预热~结束时间）范围内
type IsMusicNestActChannelReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsMusicNestActChannelReq) Reset()         { *m = IsMusicNestActChannelReq{} }
func (m *IsMusicNestActChannelReq) String() string { return proto.CompactTextString(m) }
func (*IsMusicNestActChannelReq) ProtoMessage()    {}
func (*IsMusicNestActChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{0}
}
func (m *IsMusicNestActChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsMusicNestActChannelReq.Unmarshal(m, b)
}
func (m *IsMusicNestActChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsMusicNestActChannelReq.Marshal(b, m, deterministic)
}
func (dst *IsMusicNestActChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsMusicNestActChannelReq.Merge(dst, src)
}
func (m *IsMusicNestActChannelReq) XXX_Size() int {
	return xxx_messageInfo_IsMusicNestActChannelReq.Size(m)
}
func (m *IsMusicNestActChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsMusicNestActChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsMusicNestActChannelReq proto.InternalMessageInfo

func (m *IsMusicNestActChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type IsMusicNestActChannelResp struct {
	IsInAct              bool     `protobuf:"varint,2,opt,name=is_in_act,json=isInAct,proto3" json:"is_in_act,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsMusicNestActChannelResp) Reset()         { *m = IsMusicNestActChannelResp{} }
func (m *IsMusicNestActChannelResp) String() string { return proto.CompactTextString(m) }
func (*IsMusicNestActChannelResp) ProtoMessage()    {}
func (*IsMusicNestActChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{1}
}
func (m *IsMusicNestActChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsMusicNestActChannelResp.Unmarshal(m, b)
}
func (m *IsMusicNestActChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsMusicNestActChannelResp.Marshal(b, m, deterministic)
}
func (dst *IsMusicNestActChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsMusicNestActChannelResp.Merge(dst, src)
}
func (m *IsMusicNestActChannelResp) XXX_Size() int {
	return xxx_messageInfo_IsMusicNestActChannelResp.Size(m)
}
func (m *IsMusicNestActChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsMusicNestActChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsMusicNestActChannelResp proto.InternalMessageInfo

func (m *IsMusicNestActChannelResp) GetIsInAct() bool {
	if m != nil {
		return m.IsInAct
	}
	return false
}

type CategoryInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MainTitle            string   `protobuf:"bytes,2,opt,name=main_title,json=mainTitle,proto3" json:"main_title,omitempty"`
	SubTitle             string   `protobuf:"bytes,3,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	Status               bool     `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	ImageUrl             string   `protobuf:"bytes,5,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	Index                uint32   `protobuf:"varint,6,opt,name=index,proto3" json:"index,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CategoryInfo) Reset()         { *m = CategoryInfo{} }
func (m *CategoryInfo) String() string { return proto.CompactTextString(m) }
func (*CategoryInfo) ProtoMessage()    {}
func (*CategoryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{2}
}
func (m *CategoryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CategoryInfo.Unmarshal(m, b)
}
func (m *CategoryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CategoryInfo.Marshal(b, m, deterministic)
}
func (dst *CategoryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CategoryInfo.Merge(dst, src)
}
func (m *CategoryInfo) XXX_Size() int {
	return xxx_messageInfo_CategoryInfo.Size(m)
}
func (m *CategoryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CategoryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CategoryInfo proto.InternalMessageInfo

func (m *CategoryInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CategoryInfo) GetMainTitle() string {
	if m != nil {
		return m.MainTitle
	}
	return ""
}

func (m *CategoryInfo) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *CategoryInfo) GetStatus() bool {
	if m != nil {
		return m.Status
	}
	return false
}

func (m *CategoryInfo) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *CategoryInfo) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

type GuestInfo struct {
	IsMain               bool     `protobuf:"varint,1,opt,name=is_main,json=isMain,proto3" json:"is_main,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	MicId                uint32   `protobuf:"varint,3,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	Nickname             string   `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string   `protobuf:"bytes,5,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuestInfo) Reset()         { *m = GuestInfo{} }
func (m *GuestInfo) String() string { return proto.CompactTextString(m) }
func (*GuestInfo) ProtoMessage()    {}
func (*GuestInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{3}
}
func (m *GuestInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuestInfo.Unmarshal(m, b)
}
func (m *GuestInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuestInfo.Marshal(b, m, deterministic)
}
func (dst *GuestInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuestInfo.Merge(dst, src)
}
func (m *GuestInfo) XXX_Size() int {
	return xxx_messageInfo_GuestInfo.Size(m)
}
func (m *GuestInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GuestInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GuestInfo proto.InternalMessageInfo

func (m *GuestInfo) GetIsMain() bool {
	if m != nil {
		return m.IsMain
	}
	return false
}

func (m *GuestInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GuestInfo) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *GuestInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GuestInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type GuestInfos struct {
	GuestInfos           []*GuestInfo `protobuf:"bytes,1,rep,name=guest_infos,json=guestInfos,proto3" json:"guest_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GuestInfos) Reset()         { *m = GuestInfos{} }
func (m *GuestInfos) String() string { return proto.CompactTextString(m) }
func (*GuestInfos) ProtoMessage()    {}
func (*GuestInfos) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{4}
}
func (m *GuestInfos) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuestInfos.Unmarshal(m, b)
}
func (m *GuestInfos) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuestInfos.Marshal(b, m, deterministic)
}
func (dst *GuestInfos) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuestInfos.Merge(dst, src)
}
func (m *GuestInfos) XXX_Size() int {
	return xxx_messageInfo_GuestInfos.Size(m)
}
func (m *GuestInfos) XXX_DiscardUnknown() {
	xxx_messageInfo_GuestInfos.DiscardUnknown(m)
}

var xxx_messageInfo_GuestInfos proto.InternalMessageInfo

func (m *GuestInfos) GetGuestInfos() []*GuestInfo {
	if m != nil {
		return m.GuestInfos
	}
	return nil
}

type GuestInfoWithTTId struct {
	IsMain               bool     `protobuf:"varint,1,opt,name=is_main,json=isMain,proto3" json:"is_main,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	MicId                uint32   `protobuf:"varint,3,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	Nickname             string   `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Ttid                 string   `protobuf:"bytes,5,opt,name=ttid,proto3" json:"ttid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuestInfoWithTTId) Reset()         { *m = GuestInfoWithTTId{} }
func (m *GuestInfoWithTTId) String() string { return proto.CompactTextString(m) }
func (*GuestInfoWithTTId) ProtoMessage()    {}
func (*GuestInfoWithTTId) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{5}
}
func (m *GuestInfoWithTTId) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuestInfoWithTTId.Unmarshal(m, b)
}
func (m *GuestInfoWithTTId) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuestInfoWithTTId.Marshal(b, m, deterministic)
}
func (dst *GuestInfoWithTTId) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuestInfoWithTTId.Merge(dst, src)
}
func (m *GuestInfoWithTTId) XXX_Size() int {
	return xxx_messageInfo_GuestInfoWithTTId.Size(m)
}
func (m *GuestInfoWithTTId) XXX_DiscardUnknown() {
	xxx_messageInfo_GuestInfoWithTTId.DiscardUnknown(m)
}

var xxx_messageInfo_GuestInfoWithTTId proto.InternalMessageInfo

func (m *GuestInfoWithTTId) GetIsMain() bool {
	if m != nil {
		return m.IsMain
	}
	return false
}

func (m *GuestInfoWithTTId) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GuestInfoWithTTId) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *GuestInfoWithTTId) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GuestInfoWithTTId) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

type GuestInfosWithTTId struct {
	GuestInfos           []*GuestInfoWithTTId `protobuf:"bytes,1,rep,name=guest_infos,json=guestInfos,proto3" json:"guest_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GuestInfosWithTTId) Reset()         { *m = GuestInfosWithTTId{} }
func (m *GuestInfosWithTTId) String() string { return proto.CompactTextString(m) }
func (*GuestInfosWithTTId) ProtoMessage()    {}
func (*GuestInfosWithTTId) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{6}
}
func (m *GuestInfosWithTTId) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuestInfosWithTTId.Unmarshal(m, b)
}
func (m *GuestInfosWithTTId) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuestInfosWithTTId.Marshal(b, m, deterministic)
}
func (dst *GuestInfosWithTTId) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuestInfosWithTTId.Merge(dst, src)
}
func (m *GuestInfosWithTTId) XXX_Size() int {
	return xxx_messageInfo_GuestInfosWithTTId.Size(m)
}
func (m *GuestInfosWithTTId) XXX_DiscardUnknown() {
	xxx_messageInfo_GuestInfosWithTTId.DiscardUnknown(m)
}

var xxx_messageInfo_GuestInfosWithTTId proto.InternalMessageInfo

func (m *GuestInfosWithTTId) GetGuestInfos() []*GuestInfoWithTTId {
	if m != nil {
		return m.GuestInfos
	}
	return nil
}

type MusicInfo struct {
	MusicName            string   `protobuf:"bytes,1,opt,name=music_name,json=musicName,proto3" json:"music_name,omitempty"`
	Singer               string   `protobuf:"bytes,2,opt,name=singer,proto3" json:"singer,omitempty"`
	MusicUrl             string   `protobuf:"bytes,3,opt,name=music_url,json=musicUrl,proto3" json:"music_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicInfo) Reset()         { *m = MusicInfo{} }
func (m *MusicInfo) String() string { return proto.CompactTextString(m) }
func (*MusicInfo) ProtoMessage()    {}
func (*MusicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{7}
}
func (m *MusicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicInfo.Unmarshal(m, b)
}
func (m *MusicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicInfo.Marshal(b, m, deterministic)
}
func (dst *MusicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicInfo.Merge(dst, src)
}
func (m *MusicInfo) XXX_Size() int {
	return xxx_messageInfo_MusicInfo.Size(m)
}
func (m *MusicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MusicInfo proto.InternalMessageInfo

func (m *MusicInfo) GetMusicName() string {
	if m != nil {
		return m.MusicName
	}
	return ""
}

func (m *MusicInfo) GetSinger() string {
	if m != nil {
		return m.Singer
	}
	return ""
}

func (m *MusicInfo) GetMusicUrl() string {
	if m != nil {
		return m.MusicUrl
	}
	return ""
}

type ReviewInfo struct {
	ReviewType           MaterialType `protobuf:"varint,1,opt,name=review_type,json=reviewType,proto3,enum=music_nest.MaterialType" json:"review_type,omitempty"`
	Name                 string       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	ReviewUrl            string       `protobuf:"bytes,3,opt,name=review_url,json=reviewUrl,proto3" json:"review_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ReviewInfo) Reset()         { *m = ReviewInfo{} }
func (m *ReviewInfo) String() string { return proto.CompactTextString(m) }
func (*ReviewInfo) ProtoMessage()    {}
func (*ReviewInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{8}
}
func (m *ReviewInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReviewInfo.Unmarshal(m, b)
}
func (m *ReviewInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReviewInfo.Marshal(b, m, deterministic)
}
func (dst *ReviewInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReviewInfo.Merge(dst, src)
}
func (m *ReviewInfo) XXX_Size() int {
	return xxx_messageInfo_ReviewInfo.Size(m)
}
func (m *ReviewInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ReviewInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ReviewInfo proto.InternalMessageInfo

func (m *ReviewInfo) GetReviewType() MaterialType {
	if m != nil {
		return m.ReviewType
	}
	return MaterialType_UNKNOW_MATERIAL_TYPE
}

func (m *ReviewInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ReviewInfo) GetReviewUrl() string {
	if m != nil {
		return m.ReviewUrl
	}
	return ""
}

type ActivityBaseInfo struct {
	Topic                string             `protobuf:"bytes,1,opt,name=topic,proto3" json:"topic,omitempty"`
	CategoryId           uint32             `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Level                LevelType          `protobuf:"varint,3,opt,name=level,proto3,enum=music_nest.LevelType" json:"level,omitempty"`
	Status               uint32             `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	IsRecommend          bool               `protobuf:"varint,5,opt,name=is_recommend,json=isRecommend,proto3" json:"is_recommend,omitempty"`
	LiveOnTime           uint32             `protobuf:"varint,6,opt,name=live_on_time,json=liveOnTime,proto3" json:"live_on_time,omitempty"`
	LiveOffTime          uint32             `protobuf:"varint,7,opt,name=live_off_time,json=liveOffTime,proto3" json:"live_off_time,omitempty"`
	WarnUpTime           uint32             `protobuf:"varint,8,opt,name=warn_up_time,json=warnUpTime,proto3" json:"warn_up_time,omitempty"`
	Uid                  uint32             `protobuf:"varint,9,opt,name=uid,proto3" json:"uid,omitempty"`
	Introduction         string             `protobuf:"bytes,10,opt,name=introduction,proto3" json:"introduction,omitempty"`
	ChannelId            uint32             `protobuf:"varint,11,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	IsNotify             bool               `protobuf:"varint,12,opt,name=is_notify,json=isNotify,proto3" json:"is_notify,omitempty"`
	LiveStatus           LiveStatus         `protobuf:"varint,13,opt,name=live_status,json=liveStatus,proto3,enum=music_nest.LiveStatus" json:"live_status,omitempty"`
	Ttid                 string             `protobuf:"bytes,14,opt,name=ttid,proto3" json:"ttid,omitempty"`
	BrandIntegralInfo    *BrandIntegralInfo `protobuf:"bytes,15,opt,name=brand_integral_info,json=brandIntegralInfo,proto3" json:"brand_integral_info,omitempty"`
	Operator             string             `protobuf:"bytes,16,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ActivityBaseInfo) Reset()         { *m = ActivityBaseInfo{} }
func (m *ActivityBaseInfo) String() string { return proto.CompactTextString(m) }
func (*ActivityBaseInfo) ProtoMessage()    {}
func (*ActivityBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{9}
}
func (m *ActivityBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActivityBaseInfo.Unmarshal(m, b)
}
func (m *ActivityBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActivityBaseInfo.Marshal(b, m, deterministic)
}
func (dst *ActivityBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActivityBaseInfo.Merge(dst, src)
}
func (m *ActivityBaseInfo) XXX_Size() int {
	return xxx_messageInfo_ActivityBaseInfo.Size(m)
}
func (m *ActivityBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ActivityBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ActivityBaseInfo proto.InternalMessageInfo

func (m *ActivityBaseInfo) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *ActivityBaseInfo) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *ActivityBaseInfo) GetLevel() LevelType {
	if m != nil {
		return m.Level
	}
	return LevelType_UNKNOW_LEVEL_TYPE
}

func (m *ActivityBaseInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ActivityBaseInfo) GetIsRecommend() bool {
	if m != nil {
		return m.IsRecommend
	}
	return false
}

func (m *ActivityBaseInfo) GetLiveOnTime() uint32 {
	if m != nil {
		return m.LiveOnTime
	}
	return 0
}

func (m *ActivityBaseInfo) GetLiveOffTime() uint32 {
	if m != nil {
		return m.LiveOffTime
	}
	return 0
}

func (m *ActivityBaseInfo) GetWarnUpTime() uint32 {
	if m != nil {
		return m.WarnUpTime
	}
	return 0
}

func (m *ActivityBaseInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ActivityBaseInfo) GetIntroduction() string {
	if m != nil {
		return m.Introduction
	}
	return ""
}

func (m *ActivityBaseInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ActivityBaseInfo) GetIsNotify() bool {
	if m != nil {
		return m.IsNotify
	}
	return false
}

func (m *ActivityBaseInfo) GetLiveStatus() LiveStatus {
	if m != nil {
		return m.LiveStatus
	}
	return LiveStatus_UNKNOWN_STATUS
}

func (m *ActivityBaseInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *ActivityBaseInfo) GetBrandIntegralInfo() *BrandIntegralInfo {
	if m != nil {
		return m.BrandIntegralInfo
	}
	return nil
}

func (m *ActivityBaseInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type ActivityTopicInfo struct {
	FrameColor           string       `protobuf:"bytes,1,opt,name=frame_color,json=frameColor,proto3" json:"frame_color,omitempty"`
	ShortCoverImage      string       `protobuf:"bytes,2,opt,name=short_cover_image,json=shortCoverImage,proto3" json:"short_cover_image,omitempty"`
	LongCoverImage       string       `protobuf:"bytes,3,opt,name=long_cover_image,json=longCoverImage,proto3" json:"long_cover_image,omitempty"`
	CategoryImage        string       `protobuf:"bytes,4,opt,name=category_image,json=categoryImage,proto3" json:"category_image,omitempty"`
	MaterialType         MaterialType `protobuf:"varint,5,opt,name=material_type,json=materialType,proto3,enum=music_nest.MaterialType" json:"material_type,omitempty"`
	ActivityIntroduction string       `protobuf:"bytes,6,opt,name=activity_introduction,json=activityIntroduction,proto3" json:"activity_introduction,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ActivityTopicInfo) Reset()         { *m = ActivityTopicInfo{} }
func (m *ActivityTopicInfo) String() string { return proto.CompactTextString(m) }
func (*ActivityTopicInfo) ProtoMessage()    {}
func (*ActivityTopicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{10}
}
func (m *ActivityTopicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActivityTopicInfo.Unmarshal(m, b)
}
func (m *ActivityTopicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActivityTopicInfo.Marshal(b, m, deterministic)
}
func (dst *ActivityTopicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActivityTopicInfo.Merge(dst, src)
}
func (m *ActivityTopicInfo) XXX_Size() int {
	return xxx_messageInfo_ActivityTopicInfo.Size(m)
}
func (m *ActivityTopicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ActivityTopicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ActivityTopicInfo proto.InternalMessageInfo

func (m *ActivityTopicInfo) GetFrameColor() string {
	if m != nil {
		return m.FrameColor
	}
	return ""
}

func (m *ActivityTopicInfo) GetShortCoverImage() string {
	if m != nil {
		return m.ShortCoverImage
	}
	return ""
}

func (m *ActivityTopicInfo) GetLongCoverImage() string {
	if m != nil {
		return m.LongCoverImage
	}
	return ""
}

func (m *ActivityTopicInfo) GetCategoryImage() string {
	if m != nil {
		return m.CategoryImage
	}
	return ""
}

func (m *ActivityTopicInfo) GetMaterialType() MaterialType {
	if m != nil {
		return m.MaterialType
	}
	return MaterialType_UNKNOW_MATERIAL_TYPE
}

func (m *ActivityTopicInfo) GetActivityIntroduction() string {
	if m != nil {
		return m.ActivityIntroduction
	}
	return ""
}

type CreateCategoryReq struct {
	MainTitle            string   `protobuf:"bytes,1,opt,name=main_title,json=mainTitle,proto3" json:"main_title,omitempty"`
	SubTitle             string   `protobuf:"bytes,2,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	ImageUrl             string   `protobuf:"bytes,3,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateCategoryReq) Reset()         { *m = CreateCategoryReq{} }
func (m *CreateCategoryReq) String() string { return proto.CompactTextString(m) }
func (*CreateCategoryReq) ProtoMessage()    {}
func (*CreateCategoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{11}
}
func (m *CreateCategoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateCategoryReq.Unmarshal(m, b)
}
func (m *CreateCategoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateCategoryReq.Marshal(b, m, deterministic)
}
func (dst *CreateCategoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateCategoryReq.Merge(dst, src)
}
func (m *CreateCategoryReq) XXX_Size() int {
	return xxx_messageInfo_CreateCategoryReq.Size(m)
}
func (m *CreateCategoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateCategoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateCategoryReq proto.InternalMessageInfo

func (m *CreateCategoryReq) GetMainTitle() string {
	if m != nil {
		return m.MainTitle
	}
	return ""
}

func (m *CreateCategoryReq) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *CreateCategoryReq) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

type CreateCategoryResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateCategoryResp) Reset()         { *m = CreateCategoryResp{} }
func (m *CreateCategoryResp) String() string { return proto.CompactTextString(m) }
func (*CreateCategoryResp) ProtoMessage()    {}
func (*CreateCategoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{12}
}
func (m *CreateCategoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateCategoryResp.Unmarshal(m, b)
}
func (m *CreateCategoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateCategoryResp.Marshal(b, m, deterministic)
}
func (dst *CreateCategoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateCategoryResp.Merge(dst, src)
}
func (m *CreateCategoryResp) XXX_Size() int {
	return xxx_messageInfo_CreateCategoryResp.Size(m)
}
func (m *CreateCategoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateCategoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateCategoryResp proto.InternalMessageInfo

type UpdateCategoryReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MainTitle            string   `protobuf:"bytes,2,opt,name=main_title,json=mainTitle,proto3" json:"main_title,omitempty"`
	SubTitle             string   `protobuf:"bytes,3,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	Status               bool     `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	ImageUrl             string   `protobuf:"bytes,5,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateCategoryReq) Reset()         { *m = UpdateCategoryReq{} }
func (m *UpdateCategoryReq) String() string { return proto.CompactTextString(m) }
func (*UpdateCategoryReq) ProtoMessage()    {}
func (*UpdateCategoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{13}
}
func (m *UpdateCategoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCategoryReq.Unmarshal(m, b)
}
func (m *UpdateCategoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCategoryReq.Marshal(b, m, deterministic)
}
func (dst *UpdateCategoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCategoryReq.Merge(dst, src)
}
func (m *UpdateCategoryReq) XXX_Size() int {
	return xxx_messageInfo_UpdateCategoryReq.Size(m)
}
func (m *UpdateCategoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCategoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCategoryReq proto.InternalMessageInfo

func (m *UpdateCategoryReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateCategoryReq) GetMainTitle() string {
	if m != nil {
		return m.MainTitle
	}
	return ""
}

func (m *UpdateCategoryReq) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *UpdateCategoryReq) GetStatus() bool {
	if m != nil {
		return m.Status
	}
	return false
}

func (m *UpdateCategoryReq) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

type UpdateCategoryResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateCategoryResp) Reset()         { *m = UpdateCategoryResp{} }
func (m *UpdateCategoryResp) String() string { return proto.CompactTextString(m) }
func (*UpdateCategoryResp) ProtoMessage()    {}
func (*UpdateCategoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{14}
}
func (m *UpdateCategoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCategoryResp.Unmarshal(m, b)
}
func (m *UpdateCategoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCategoryResp.Marshal(b, m, deterministic)
}
func (dst *UpdateCategoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCategoryResp.Merge(dst, src)
}
func (m *UpdateCategoryResp) XXX_Size() int {
	return xxx_messageInfo_UpdateCategoryResp.Size(m)
}
func (m *UpdateCategoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCategoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCategoryResp proto.InternalMessageInfo

type GetAllCategoryReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllCategoryReq) Reset()         { *m = GetAllCategoryReq{} }
func (m *GetAllCategoryReq) String() string { return proto.CompactTextString(m) }
func (*GetAllCategoryReq) ProtoMessage()    {}
func (*GetAllCategoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{15}
}
func (m *GetAllCategoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllCategoryReq.Unmarshal(m, b)
}
func (m *GetAllCategoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllCategoryReq.Marshal(b, m, deterministic)
}
func (dst *GetAllCategoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllCategoryReq.Merge(dst, src)
}
func (m *GetAllCategoryReq) XXX_Size() int {
	return xxx_messageInfo_GetAllCategoryReq.Size(m)
}
func (m *GetAllCategoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllCategoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllCategoryReq proto.InternalMessageInfo

type GetAllCategoryResp struct {
	CategoryInfos        []*CategoryInfo `protobuf:"bytes,1,rep,name=category_infos,json=categoryInfos,proto3" json:"category_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetAllCategoryResp) Reset()         { *m = GetAllCategoryResp{} }
func (m *GetAllCategoryResp) String() string { return proto.CompactTextString(m) }
func (*GetAllCategoryResp) ProtoMessage()    {}
func (*GetAllCategoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{16}
}
func (m *GetAllCategoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllCategoryResp.Unmarshal(m, b)
}
func (m *GetAllCategoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllCategoryResp.Marshal(b, m, deterministic)
}
func (dst *GetAllCategoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllCategoryResp.Merge(dst, src)
}
func (m *GetAllCategoryResp) XXX_Size() int {
	return xxx_messageInfo_GetAllCategoryResp.Size(m)
}
func (m *GetAllCategoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllCategoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllCategoryResp proto.InternalMessageInfo

func (m *GetAllCategoryResp) GetCategoryInfos() []*CategoryInfo {
	if m != nil {
		return m.CategoryInfos
	}
	return nil
}

type MoveCategoryReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CurrentIndex         uint32   `protobuf:"varint,2,opt,name=current_index,json=currentIndex,proto3" json:"current_index,omitempty"`
	TargetIndex          uint32   `protobuf:"varint,3,opt,name=target_index,json=targetIndex,proto3" json:"target_index,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MoveCategoryReq) Reset()         { *m = MoveCategoryReq{} }
func (m *MoveCategoryReq) String() string { return proto.CompactTextString(m) }
func (*MoveCategoryReq) ProtoMessage()    {}
func (*MoveCategoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{17}
}
func (m *MoveCategoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoveCategoryReq.Unmarshal(m, b)
}
func (m *MoveCategoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoveCategoryReq.Marshal(b, m, deterministic)
}
func (dst *MoveCategoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoveCategoryReq.Merge(dst, src)
}
func (m *MoveCategoryReq) XXX_Size() int {
	return xxx_messageInfo_MoveCategoryReq.Size(m)
}
func (m *MoveCategoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MoveCategoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_MoveCategoryReq proto.InternalMessageInfo

func (m *MoveCategoryReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MoveCategoryReq) GetCurrentIndex() uint32 {
	if m != nil {
		return m.CurrentIndex
	}
	return 0
}

func (m *MoveCategoryReq) GetTargetIndex() uint32 {
	if m != nil {
		return m.TargetIndex
	}
	return 0
}

type MoveCategoryResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MoveCategoryResp) Reset()         { *m = MoveCategoryResp{} }
func (m *MoveCategoryResp) String() string { return proto.CompactTextString(m) }
func (*MoveCategoryResp) ProtoMessage()    {}
func (*MoveCategoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{18}
}
func (m *MoveCategoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoveCategoryResp.Unmarshal(m, b)
}
func (m *MoveCategoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoveCategoryResp.Marshal(b, m, deterministic)
}
func (dst *MoveCategoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoveCategoryResp.Merge(dst, src)
}
func (m *MoveCategoryResp) XXX_Size() int {
	return xxx_messageInfo_MoveCategoryResp.Size(m)
}
func (m *MoveCategoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MoveCategoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_MoveCategoryResp proto.InternalMessageInfo

type CreatActivityReq struct {
	ActivityBaseInfo     *ActivityBaseInfo `protobuf:"bytes,1,opt,name=activity_base_info,json=activityBaseInfo,proto3" json:"activity_base_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CreatActivityReq) Reset()         { *m = CreatActivityReq{} }
func (m *CreatActivityReq) String() string { return proto.CompactTextString(m) }
func (*CreatActivityReq) ProtoMessage()    {}
func (*CreatActivityReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{19}
}
func (m *CreatActivityReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreatActivityReq.Unmarshal(m, b)
}
func (m *CreatActivityReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreatActivityReq.Marshal(b, m, deterministic)
}
func (dst *CreatActivityReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreatActivityReq.Merge(dst, src)
}
func (m *CreatActivityReq) XXX_Size() int {
	return xxx_messageInfo_CreatActivityReq.Size(m)
}
func (m *CreatActivityReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreatActivityReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreatActivityReq proto.InternalMessageInfo

func (m *CreatActivityReq) GetActivityBaseInfo() *ActivityBaseInfo {
	if m != nil {
		return m.ActivityBaseInfo
	}
	return nil
}

type CreatActivityResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreatActivityResp) Reset()         { *m = CreatActivityResp{} }
func (m *CreatActivityResp) String() string { return proto.CompactTextString(m) }
func (*CreatActivityResp) ProtoMessage()    {}
func (*CreatActivityResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{20}
}
func (m *CreatActivityResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreatActivityResp.Unmarshal(m, b)
}
func (m *CreatActivityResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreatActivityResp.Marshal(b, m, deterministic)
}
func (dst *CreatActivityResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreatActivityResp.Merge(dst, src)
}
func (m *CreatActivityResp) XXX_Size() int {
	return xxx_messageInfo_CreatActivityResp.Size(m)
}
func (m *CreatActivityResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreatActivityResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreatActivityResp proto.InternalMessageInfo

type UpdateActivityReq struct {
	ActivityId           uint32            `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	ActivityBaseInfo     *ActivityBaseInfo `protobuf:"bytes,2,opt,name=activity_base_info,json=activityBaseInfo,proto3" json:"activity_base_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UpdateActivityReq) Reset()         { *m = UpdateActivityReq{} }
func (m *UpdateActivityReq) String() string { return proto.CompactTextString(m) }
func (*UpdateActivityReq) ProtoMessage()    {}
func (*UpdateActivityReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{21}
}
func (m *UpdateActivityReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateActivityReq.Unmarshal(m, b)
}
func (m *UpdateActivityReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateActivityReq.Marshal(b, m, deterministic)
}
func (dst *UpdateActivityReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateActivityReq.Merge(dst, src)
}
func (m *UpdateActivityReq) XXX_Size() int {
	return xxx_messageInfo_UpdateActivityReq.Size(m)
}
func (m *UpdateActivityReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateActivityReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateActivityReq proto.InternalMessageInfo

func (m *UpdateActivityReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *UpdateActivityReq) GetActivityBaseInfo() *ActivityBaseInfo {
	if m != nil {
		return m.ActivityBaseInfo
	}
	return nil
}

type UpdateActivityResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateActivityResp) Reset()         { *m = UpdateActivityResp{} }
func (m *UpdateActivityResp) String() string { return proto.CompactTextString(m) }
func (*UpdateActivityResp) ProtoMessage()    {}
func (*UpdateActivityResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{22}
}
func (m *UpdateActivityResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateActivityResp.Unmarshal(m, b)
}
func (m *UpdateActivityResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateActivityResp.Marshal(b, m, deterministic)
}
func (dst *UpdateActivityResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateActivityResp.Merge(dst, src)
}
func (m *UpdateActivityResp) XXX_Size() int {
	return xxx_messageInfo_UpdateActivityResp.Size(m)
}
func (m *UpdateActivityResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateActivityResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateActivityResp proto.InternalMessageInfo

// 实现设置，更新，获取欢迎弹层
type WelcomePop struct {
	StartFlag            bool     `protobuf:"varint,1,opt,name=startFlag,proto3" json:"startFlag,omitempty"`
	Topic                string   `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
	OptionOne            []string `protobuf:"bytes,3,rep,name=optionOne,proto3" json:"optionOne,omitempty"`
	PictureUrl           string   `protobuf:"bytes,4,opt,name=pictureUrl,proto3" json:"pictureUrl,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WelcomePop) Reset()         { *m = WelcomePop{} }
func (m *WelcomePop) String() string { return proto.CompactTextString(m) }
func (*WelcomePop) ProtoMessage()    {}
func (*WelcomePop) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{23}
}
func (m *WelcomePop) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WelcomePop.Unmarshal(m, b)
}
func (m *WelcomePop) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WelcomePop.Marshal(b, m, deterministic)
}
func (dst *WelcomePop) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WelcomePop.Merge(dst, src)
}
func (m *WelcomePop) XXX_Size() int {
	return xxx_messageInfo_WelcomePop.Size(m)
}
func (m *WelcomePop) XXX_DiscardUnknown() {
	xxx_messageInfo_WelcomePop.DiscardUnknown(m)
}

var xxx_messageInfo_WelcomePop proto.InternalMessageInfo

func (m *WelcomePop) GetStartFlag() bool {
	if m != nil {
		return m.StartFlag
	}
	return false
}

func (m *WelcomePop) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *WelcomePop) GetOptionOne() []string {
	if m != nil {
		return m.OptionOne
	}
	return nil
}

func (m *WelcomePop) GetPictureUrl() string {
	if m != nil {
		return m.PictureUrl
	}
	return ""
}

type UpdateActivityTopicReq struct {
	ActivityId             uint32             `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	MusicActivityTopicInfo *ActivityTopicInfo `protobuf:"bytes,2,opt,name=music_activity_topic_info,json=musicActivityTopicInfo,proto3" json:"music_activity_topic_info,omitempty"`
	PatternType            PatternType        `protobuf:"varint,3,opt,name=pattern_type,json=patternType,proto3,enum=music_nest.PatternType" json:"pattern_type,omitempty"`
	GuestInfosArray        []*GuestInfos      `protobuf:"bytes,4,rep,name=guest_infos_array,json=guestInfosArray,proto3" json:"guest_infos_array,omitempty"`
	WelcomePop             *WelcomePop        `protobuf:"bytes,5,opt,name=welcome_pop,json=welcomePop,proto3" json:"welcome_pop,omitempty"`
	CokeStateInfo          *CokeStateInfo     `protobuf:"bytes,6,opt,name=coke_state_info,json=cokeStateInfo,proto3" json:"coke_state_info,omitempty"`
	IsStayAddTicket        bool               `protobuf:"varint,7,opt,name=is_stay_add_ticket,json=isStayAddTicket,proto3" json:"is_stay_add_ticket,omitempty"`
	StayInterval           uint32             `protobuf:"varint,8,opt,name=stay_interval,json=stayInterval,proto3" json:"stay_interval,omitempty"`
	TicketCnt              uint32             `protobuf:"varint,9,opt,name=ticket_cnt,json=ticketCnt,proto3" json:"ticket_cnt,omitempty"`
	TotalAddTicketCnt      uint32             `protobuf:"varint,10,opt,name=total_add_ticket_cnt,json=totalAddTicketCnt,proto3" json:"total_add_ticket_cnt,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}           `json:"-"`
	XXX_unrecognized       []byte             `json:"-"`
	XXX_sizecache          int32              `json:"-"`
}

func (m *UpdateActivityTopicReq) Reset()         { *m = UpdateActivityTopicReq{} }
func (m *UpdateActivityTopicReq) String() string { return proto.CompactTextString(m) }
func (*UpdateActivityTopicReq) ProtoMessage()    {}
func (*UpdateActivityTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{24}
}
func (m *UpdateActivityTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateActivityTopicReq.Unmarshal(m, b)
}
func (m *UpdateActivityTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateActivityTopicReq.Marshal(b, m, deterministic)
}
func (dst *UpdateActivityTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateActivityTopicReq.Merge(dst, src)
}
func (m *UpdateActivityTopicReq) XXX_Size() int {
	return xxx_messageInfo_UpdateActivityTopicReq.Size(m)
}
func (m *UpdateActivityTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateActivityTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateActivityTopicReq proto.InternalMessageInfo

func (m *UpdateActivityTopicReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *UpdateActivityTopicReq) GetMusicActivityTopicInfo() *ActivityTopicInfo {
	if m != nil {
		return m.MusicActivityTopicInfo
	}
	return nil
}

func (m *UpdateActivityTopicReq) GetPatternType() PatternType {
	if m != nil {
		return m.PatternType
	}
	return PatternType_UNKNOW_PATTERN_TYPE
}

func (m *UpdateActivityTopicReq) GetGuestInfosArray() []*GuestInfos {
	if m != nil {
		return m.GuestInfosArray
	}
	return nil
}

func (m *UpdateActivityTopicReq) GetWelcomePop() *WelcomePop {
	if m != nil {
		return m.WelcomePop
	}
	return nil
}

func (m *UpdateActivityTopicReq) GetCokeStateInfo() *CokeStateInfo {
	if m != nil {
		return m.CokeStateInfo
	}
	return nil
}

func (m *UpdateActivityTopicReq) GetIsStayAddTicket() bool {
	if m != nil {
		return m.IsStayAddTicket
	}
	return false
}

func (m *UpdateActivityTopicReq) GetStayInterval() uint32 {
	if m != nil {
		return m.StayInterval
	}
	return 0
}

func (m *UpdateActivityTopicReq) GetTicketCnt() uint32 {
	if m != nil {
		return m.TicketCnt
	}
	return 0
}

func (m *UpdateActivityTopicReq) GetTotalAddTicketCnt() uint32 {
	if m != nil {
		return m.TotalAddTicketCnt
	}
	return 0
}

// 获取近期乐窝活动的加票停留信息
type GetAllStayAddTicketActsReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllStayAddTicketActsReq) Reset()         { *m = GetAllStayAddTicketActsReq{} }
func (m *GetAllStayAddTicketActsReq) String() string { return proto.CompactTextString(m) }
func (*GetAllStayAddTicketActsReq) ProtoMessage()    {}
func (*GetAllStayAddTicketActsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{25}
}
func (m *GetAllStayAddTicketActsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllStayAddTicketActsReq.Unmarshal(m, b)
}
func (m *GetAllStayAddTicketActsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllStayAddTicketActsReq.Marshal(b, m, deterministic)
}
func (dst *GetAllStayAddTicketActsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllStayAddTicketActsReq.Merge(dst, src)
}
func (m *GetAllStayAddTicketActsReq) XXX_Size() int {
	return xxx_messageInfo_GetAllStayAddTicketActsReq.Size(m)
}
func (m *GetAllStayAddTicketActsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllStayAddTicketActsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllStayAddTicketActsReq proto.InternalMessageInfo

type StayAddTicketActsInfo struct {
	ActivityId           uint32   `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	Status               uint32   `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LiveOffTime          uint32   `protobuf:"varint,4,opt,name=live_off_time,json=liveOffTime,proto3" json:"live_off_time,omitempty"`
	WarnUpTime           uint32   `protobuf:"varint,5,opt,name=warn_up_time,json=warnUpTime,proto3" json:"warn_up_time,omitempty"`
	IsStayAddTicket      bool     `protobuf:"varint,6,opt,name=is_stay_add_ticket,json=isStayAddTicket,proto3" json:"is_stay_add_ticket,omitempty"`
	StayInterval         uint32   `protobuf:"varint,7,opt,name=stay_interval,json=stayInterval,proto3" json:"stay_interval,omitempty"`
	TicketCnt            uint32   `protobuf:"varint,8,opt,name=ticket_cnt,json=ticketCnt,proto3" json:"ticket_cnt,omitempty"`
	TotalAddTicketCnt    uint32   `protobuf:"varint,9,opt,name=total_add_ticket_cnt,json=totalAddTicketCnt,proto3" json:"total_add_ticket_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StayAddTicketActsInfo) Reset()         { *m = StayAddTicketActsInfo{} }
func (m *StayAddTicketActsInfo) String() string { return proto.CompactTextString(m) }
func (*StayAddTicketActsInfo) ProtoMessage()    {}
func (*StayAddTicketActsInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{26}
}
func (m *StayAddTicketActsInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StayAddTicketActsInfo.Unmarshal(m, b)
}
func (m *StayAddTicketActsInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StayAddTicketActsInfo.Marshal(b, m, deterministic)
}
func (dst *StayAddTicketActsInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StayAddTicketActsInfo.Merge(dst, src)
}
func (m *StayAddTicketActsInfo) XXX_Size() int {
	return xxx_messageInfo_StayAddTicketActsInfo.Size(m)
}
func (m *StayAddTicketActsInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StayAddTicketActsInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StayAddTicketActsInfo proto.InternalMessageInfo

func (m *StayAddTicketActsInfo) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *StayAddTicketActsInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *StayAddTicketActsInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StayAddTicketActsInfo) GetLiveOffTime() uint32 {
	if m != nil {
		return m.LiveOffTime
	}
	return 0
}

func (m *StayAddTicketActsInfo) GetWarnUpTime() uint32 {
	if m != nil {
		return m.WarnUpTime
	}
	return 0
}

func (m *StayAddTicketActsInfo) GetIsStayAddTicket() bool {
	if m != nil {
		return m.IsStayAddTicket
	}
	return false
}

func (m *StayAddTicketActsInfo) GetStayInterval() uint32 {
	if m != nil {
		return m.StayInterval
	}
	return 0
}

func (m *StayAddTicketActsInfo) GetTicketCnt() uint32 {
	if m != nil {
		return m.TicketCnt
	}
	return 0
}

func (m *StayAddTicketActsInfo) GetTotalAddTicketCnt() uint32 {
	if m != nil {
		return m.TotalAddTicketCnt
	}
	return 0
}

type GetAllStayAddTicketActsResp struct {
	InfoList             []*StayAddTicketActsInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetAllStayAddTicketActsResp) Reset()         { *m = GetAllStayAddTicketActsResp{} }
func (m *GetAllStayAddTicketActsResp) String() string { return proto.CompactTextString(m) }
func (*GetAllStayAddTicketActsResp) ProtoMessage()    {}
func (*GetAllStayAddTicketActsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{27}
}
func (m *GetAllStayAddTicketActsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllStayAddTicketActsResp.Unmarshal(m, b)
}
func (m *GetAllStayAddTicketActsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllStayAddTicketActsResp.Marshal(b, m, deterministic)
}
func (dst *GetAllStayAddTicketActsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllStayAddTicketActsResp.Merge(dst, src)
}
func (m *GetAllStayAddTicketActsResp) XXX_Size() int {
	return xxx_messageInfo_GetAllStayAddTicketActsResp.Size(m)
}
func (m *GetAllStayAddTicketActsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllStayAddTicketActsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllStayAddTicketActsResp proto.InternalMessageInfo

func (m *GetAllStayAddTicketActsResp) GetInfoList() []*StayAddTicketActsInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type BrandIntegralInfo struct {
	BrandInfo            []*BrandInfo `protobuf:"bytes,1,rep,name=brand_info,json=brandInfo,proto3" json:"brand_info,omitempty"`
	Count                int32        `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	BrandIntegralStatus  uint32       `protobuf:"varint,3,opt,name=brand_integral_status,json=brandIntegralStatus,proto3" json:"brand_integral_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BrandIntegralInfo) Reset()         { *m = BrandIntegralInfo{} }
func (m *BrandIntegralInfo) String() string { return proto.CompactTextString(m) }
func (*BrandIntegralInfo) ProtoMessage()    {}
func (*BrandIntegralInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{28}
}
func (m *BrandIntegralInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BrandIntegralInfo.Unmarshal(m, b)
}
func (m *BrandIntegralInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BrandIntegralInfo.Marshal(b, m, deterministic)
}
func (dst *BrandIntegralInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BrandIntegralInfo.Merge(dst, src)
}
func (m *BrandIntegralInfo) XXX_Size() int {
	return xxx_messageInfo_BrandIntegralInfo.Size(m)
}
func (m *BrandIntegralInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BrandIntegralInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BrandIntegralInfo proto.InternalMessageInfo

func (m *BrandIntegralInfo) GetBrandInfo() []*BrandInfo {
	if m != nil {
		return m.BrandInfo
	}
	return nil
}

func (m *BrandIntegralInfo) GetCount() int32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *BrandIntegralInfo) GetBrandIntegralStatus() uint32 {
	if m != nil {
		return m.BrandIntegralStatus
	}
	return 0
}

type BrandInfo struct {
	BrandId              string   `protobuf:"bytes,1,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	BrandName            string   `protobuf:"bytes,2,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BrandInfo) Reset()         { *m = BrandInfo{} }
func (m *BrandInfo) String() string { return proto.CompactTextString(m) }
func (*BrandInfo) ProtoMessage()    {}
func (*BrandInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{29}
}
func (m *BrandInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BrandInfo.Unmarshal(m, b)
}
func (m *BrandInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BrandInfo.Marshal(b, m, deterministic)
}
func (dst *BrandInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BrandInfo.Merge(dst, src)
}
func (m *BrandInfo) XXX_Size() int {
	return xxx_messageInfo_BrandInfo.Size(m)
}
func (m *BrandInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BrandInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BrandInfo proto.InternalMessageInfo

func (m *BrandInfo) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

func (m *BrandInfo) GetBrandName() string {
	if m != nil {
		return m.BrandName
	}
	return ""
}

type UpdateActivityTopicResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateActivityTopicResp) Reset()         { *m = UpdateActivityTopicResp{} }
func (m *UpdateActivityTopicResp) String() string { return proto.CompactTextString(m) }
func (*UpdateActivityTopicResp) ProtoMessage()    {}
func (*UpdateActivityTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{30}
}
func (m *UpdateActivityTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateActivityTopicResp.Unmarshal(m, b)
}
func (m *UpdateActivityTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateActivityTopicResp.Marshal(b, m, deterministic)
}
func (dst *UpdateActivityTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateActivityTopicResp.Merge(dst, src)
}
func (m *UpdateActivityTopicResp) XXX_Size() int {
	return xxx_messageInfo_UpdateActivityTopicResp.Size(m)
}
func (m *UpdateActivityTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateActivityTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateActivityTopicResp proto.InternalMessageInfo

type UpdateMusicListInfoReq struct {
	ActivityId           uint32       `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	MusicInfos           []*MusicInfo `protobuf:"bytes,2,rep,name=music_infos,json=musicInfos,proto3" json:"music_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpdateMusicListInfoReq) Reset()         { *m = UpdateMusicListInfoReq{} }
func (m *UpdateMusicListInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpdateMusicListInfoReq) ProtoMessage()    {}
func (*UpdateMusicListInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{31}
}
func (m *UpdateMusicListInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateMusicListInfoReq.Unmarshal(m, b)
}
func (m *UpdateMusicListInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateMusicListInfoReq.Marshal(b, m, deterministic)
}
func (dst *UpdateMusicListInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateMusicListInfoReq.Merge(dst, src)
}
func (m *UpdateMusicListInfoReq) XXX_Size() int {
	return xxx_messageInfo_UpdateMusicListInfoReq.Size(m)
}
func (m *UpdateMusicListInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateMusicListInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateMusicListInfoReq proto.InternalMessageInfo

func (m *UpdateMusicListInfoReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *UpdateMusicListInfoReq) GetMusicInfos() []*MusicInfo {
	if m != nil {
		return m.MusicInfos
	}
	return nil
}

type UpdateMusicListInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateMusicListInfoResp) Reset()         { *m = UpdateMusicListInfoResp{} }
func (m *UpdateMusicListInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpdateMusicListInfoResp) ProtoMessage()    {}
func (*UpdateMusicListInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{32}
}
func (m *UpdateMusicListInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateMusicListInfoResp.Unmarshal(m, b)
}
func (m *UpdateMusicListInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateMusicListInfoResp.Marshal(b, m, deterministic)
}
func (dst *UpdateMusicListInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateMusicListInfoResp.Merge(dst, src)
}
func (m *UpdateMusicListInfoResp) XXX_Size() int {
	return xxx_messageInfo_UpdateMusicListInfoResp.Size(m)
}
func (m *UpdateMusicListInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateMusicListInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateMusicListInfoResp proto.InternalMessageInfo

type UpdateActivityReviewReq struct {
	ActivityId           uint32        `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	ReviewInfos          []*ReviewInfo `protobuf:"bytes,2,rep,name=review_infos,json=reviewInfos,proto3" json:"review_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpdateActivityReviewReq) Reset()         { *m = UpdateActivityReviewReq{} }
func (m *UpdateActivityReviewReq) String() string { return proto.CompactTextString(m) }
func (*UpdateActivityReviewReq) ProtoMessage()    {}
func (*UpdateActivityReviewReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{33}
}
func (m *UpdateActivityReviewReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateActivityReviewReq.Unmarshal(m, b)
}
func (m *UpdateActivityReviewReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateActivityReviewReq.Marshal(b, m, deterministic)
}
func (dst *UpdateActivityReviewReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateActivityReviewReq.Merge(dst, src)
}
func (m *UpdateActivityReviewReq) XXX_Size() int {
	return xxx_messageInfo_UpdateActivityReviewReq.Size(m)
}
func (m *UpdateActivityReviewReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateActivityReviewReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateActivityReviewReq proto.InternalMessageInfo

func (m *UpdateActivityReviewReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *UpdateActivityReviewReq) GetReviewInfos() []*ReviewInfo {
	if m != nil {
		return m.ReviewInfos
	}
	return nil
}

type UpdateActivityReviewResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateActivityReviewResp) Reset()         { *m = UpdateActivityReviewResp{} }
func (m *UpdateActivityReviewResp) String() string { return proto.CompactTextString(m) }
func (*UpdateActivityReviewResp) ProtoMessage()    {}
func (*UpdateActivityReviewResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{34}
}
func (m *UpdateActivityReviewResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateActivityReviewResp.Unmarshal(m, b)
}
func (m *UpdateActivityReviewResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateActivityReviewResp.Marshal(b, m, deterministic)
}
func (dst *UpdateActivityReviewResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateActivityReviewResp.Merge(dst, src)
}
func (m *UpdateActivityReviewResp) XXX_Size() int {
	return xxx_messageInfo_UpdateActivityReviewResp.Size(m)
}
func (m *UpdateActivityReviewResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateActivityReviewResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateActivityReviewResp proto.InternalMessageInfo

type GetActivityListReq struct {
	// Types that are valid to be assigned to RequestType:
	//	*GetActivityListReq_ActivityId
	//	*GetActivityListReq_TopicName
	//	*GetActivityListReq_Uid
	//	*GetActivityListReq_ChannelId
	RequestType          isGetActivityListReq_RequestType `protobuf_oneof:"request_type"`
	CategoryId           uint32                           `protobuf:"varint,5,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Status               uint32                           `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`
	LiveOnStartTime      uint32                           `protobuf:"varint,7,opt,name=live_on_start_time,json=liveOnStartTime,proto3" json:"live_on_start_time,omitempty"`
	LiveOnEndTime        uint32                           `protobuf:"varint,8,opt,name=live_on_end_time,json=liveOnEndTime,proto3" json:"live_on_end_time,omitempty"`
	Page                 uint32                           `protobuf:"varint,9,opt,name=page,proto3" json:"page,omitempty"`
	Limit                uint32                           `protobuf:"varint,10,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *GetActivityListReq) Reset()         { *m = GetActivityListReq{} }
func (m *GetActivityListReq) String() string { return proto.CompactTextString(m) }
func (*GetActivityListReq) ProtoMessage()    {}
func (*GetActivityListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{35}
}
func (m *GetActivityListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivityListReq.Unmarshal(m, b)
}
func (m *GetActivityListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivityListReq.Marshal(b, m, deterministic)
}
func (dst *GetActivityListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivityListReq.Merge(dst, src)
}
func (m *GetActivityListReq) XXX_Size() int {
	return xxx_messageInfo_GetActivityListReq.Size(m)
}
func (m *GetActivityListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivityListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivityListReq proto.InternalMessageInfo

type isGetActivityListReq_RequestType interface {
	isGetActivityListReq_RequestType()
}

type GetActivityListReq_ActivityId struct {
	ActivityId uint32 `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3,oneof"`
}

type GetActivityListReq_TopicName struct {
	TopicName string `protobuf:"bytes,2,opt,name=topic_name,json=topicName,proto3,oneof"`
}

type GetActivityListReq_Uid struct {
	Uid uint32 `protobuf:"varint,3,opt,name=uid,proto3,oneof"`
}

type GetActivityListReq_ChannelId struct {
	ChannelId uint32 `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3,oneof"`
}

func (*GetActivityListReq_ActivityId) isGetActivityListReq_RequestType() {}

func (*GetActivityListReq_TopicName) isGetActivityListReq_RequestType() {}

func (*GetActivityListReq_Uid) isGetActivityListReq_RequestType() {}

func (*GetActivityListReq_ChannelId) isGetActivityListReq_RequestType() {}

func (m *GetActivityListReq) GetRequestType() isGetActivityListReq_RequestType {
	if m != nil {
		return m.RequestType
	}
	return nil
}

func (m *GetActivityListReq) GetActivityId() uint32 {
	if x, ok := m.GetRequestType().(*GetActivityListReq_ActivityId); ok {
		return x.ActivityId
	}
	return 0
}

func (m *GetActivityListReq) GetTopicName() string {
	if x, ok := m.GetRequestType().(*GetActivityListReq_TopicName); ok {
		return x.TopicName
	}
	return ""
}

func (m *GetActivityListReq) GetUid() uint32 {
	if x, ok := m.GetRequestType().(*GetActivityListReq_Uid); ok {
		return x.Uid
	}
	return 0
}

func (m *GetActivityListReq) GetChannelId() uint32 {
	if x, ok := m.GetRequestType().(*GetActivityListReq_ChannelId); ok {
		return x.ChannelId
	}
	return 0
}

func (m *GetActivityListReq) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *GetActivityListReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetActivityListReq) GetLiveOnStartTime() uint32 {
	if m != nil {
		return m.LiveOnStartTime
	}
	return 0
}

func (m *GetActivityListReq) GetLiveOnEndTime() uint32 {
	if m != nil {
		return m.LiveOnEndTime
	}
	return 0
}

func (m *GetActivityListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetActivityListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*GetActivityListReq) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _GetActivityListReq_OneofMarshaler, _GetActivityListReq_OneofUnmarshaler, _GetActivityListReq_OneofSizer, []interface{}{
		(*GetActivityListReq_ActivityId)(nil),
		(*GetActivityListReq_TopicName)(nil),
		(*GetActivityListReq_Uid)(nil),
		(*GetActivityListReq_ChannelId)(nil),
	}
}

func _GetActivityListReq_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*GetActivityListReq)
	// request_type
	switch x := m.RequestType.(type) {
	case *GetActivityListReq_ActivityId:
		b.EncodeVarint(1<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.ActivityId))
	case *GetActivityListReq_TopicName:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.TopicName)
	case *GetActivityListReq_Uid:
		b.EncodeVarint(3<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.Uid))
	case *GetActivityListReq_ChannelId:
		b.EncodeVarint(4<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.ChannelId))
	case nil:
	default:
		return fmt.Errorf("GetActivityListReq.RequestType has unexpected type %T", x)
	}
	return nil
}

func _GetActivityListReq_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*GetActivityListReq)
	switch tag {
	case 1: // request_type.activity_id
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.RequestType = &GetActivityListReq_ActivityId{uint32(x)}
		return true, err
	case 2: // request_type.topic_name
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.RequestType = &GetActivityListReq_TopicName{x}
		return true, err
	case 3: // request_type.uid
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.RequestType = &GetActivityListReq_Uid{uint32(x)}
		return true, err
	case 4: // request_type.channel_id
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.RequestType = &GetActivityListReq_ChannelId{uint32(x)}
		return true, err
	default:
		return false, nil
	}
}

func _GetActivityListReq_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*GetActivityListReq)
	// request_type
	switch x := m.RequestType.(type) {
	case *GetActivityListReq_ActivityId:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(x.ActivityId))
	case *GetActivityListReq_TopicName:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(len(x.TopicName)))
		n += len(x.TopicName)
	case *GetActivityListReq_Uid:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(x.Uid))
	case *GetActivityListReq_ChannelId:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(x.ChannelId))
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type GetActivityListResp struct {
	AllActivityInfos     []*GetActivityListResp_AllActivityInfo `protobuf:"bytes,1,rep,name=all_activity_infos,json=allActivityInfos,proto3" json:"all_activity_infos,omitempty"`
	Page                 uint32                                 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	NumActivities        uint32                                 `protobuf:"varint,3,opt,name=num_activities,json=numActivities,proto3" json:"num_activities,omitempty"`
	TotalCnt             uint32                                 `protobuf:"varint,4,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *GetActivityListResp) Reset()         { *m = GetActivityListResp{} }
func (m *GetActivityListResp) String() string { return proto.CompactTextString(m) }
func (*GetActivityListResp) ProtoMessage()    {}
func (*GetActivityListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{36}
}
func (m *GetActivityListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivityListResp.Unmarshal(m, b)
}
func (m *GetActivityListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivityListResp.Marshal(b, m, deterministic)
}
func (dst *GetActivityListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivityListResp.Merge(dst, src)
}
func (m *GetActivityListResp) XXX_Size() int {
	return xxx_messageInfo_GetActivityListResp.Size(m)
}
func (m *GetActivityListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivityListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivityListResp proto.InternalMessageInfo

func (m *GetActivityListResp) GetAllActivityInfos() []*GetActivityListResp_AllActivityInfo {
	if m != nil {
		return m.AllActivityInfos
	}
	return nil
}

func (m *GetActivityListResp) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetActivityListResp) GetNumActivities() uint32 {
	if m != nil {
		return m.NumActivities
	}
	return 0
}

func (m *GetActivityListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

type GetActivityListResp_AllActivityInfo struct {
	ActivityId             uint32                `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	Info                   *ActivityBaseInfo     `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	MusicActivityTopicInfo *ActivityTopicInfo    `protobuf:"bytes,3,opt,name=music_activity_topic_info,json=musicActivityTopicInfo,proto3" json:"music_activity_topic_info,omitempty"`
	PatternType            PatternType           `protobuf:"varint,4,opt,name=pattern_type,json=patternType,proto3,enum=music_nest.PatternType" json:"pattern_type,omitempty"`
	GuestInfos             []*GuestInfosWithTTId `protobuf:"bytes,5,rep,name=guest_infos,json=guestInfos,proto3" json:"guest_infos,omitempty"`
	MusicInfos             []*MusicInfo          `protobuf:"bytes,6,rep,name=music_infos,json=musicInfos,proto3" json:"music_infos,omitempty"`
	ReviewInfos            []*ReviewInfo         `protobuf:"bytes,7,rep,name=review_infos,json=reviewInfos,proto3" json:"review_infos,omitempty"`
	WelcomePop             *WelcomePop           `protobuf:"bytes,8,opt,name=welcome_pop,json=welcomePop,proto3" json:"welcome_pop,omitempty"`
	CokeStateInfo          *CokeStateInfo        `protobuf:"bytes,9,opt,name=coke_state_info,json=cokeStateInfo,proto3" json:"coke_state_info,omitempty"`
	IsStayAddTicket        bool                  `protobuf:"varint,10,opt,name=is_stay_add_ticket,json=isStayAddTicket,proto3" json:"is_stay_add_ticket,omitempty"`
	StayInterval           uint32                `protobuf:"varint,11,opt,name=stay_interval,json=stayInterval,proto3" json:"stay_interval,omitempty"`
	TicketCnt              uint32                `protobuf:"varint,12,opt,name=ticket_cnt,json=ticketCnt,proto3" json:"ticket_cnt,omitempty"`
	TotalAddTicketCnt      uint32                `protobuf:"varint,13,opt,name=total_add_ticket_cnt,json=totalAddTicketCnt,proto3" json:"total_add_ticket_cnt,omitempty"`
	BrandInfo              *BrandIntegralInfo    `protobuf:"bytes,14,opt,name=brand_info,json=brandInfo,proto3" json:"brand_info,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}              `json:"-"`
	XXX_unrecognized       []byte                `json:"-"`
	XXX_sizecache          int32                 `json:"-"`
}

func (m *GetActivityListResp_AllActivityInfo) Reset()         { *m = GetActivityListResp_AllActivityInfo{} }
func (m *GetActivityListResp_AllActivityInfo) String() string { return proto.CompactTextString(m) }
func (*GetActivityListResp_AllActivityInfo) ProtoMessage()    {}
func (*GetActivityListResp_AllActivityInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{36, 0}
}
func (m *GetActivityListResp_AllActivityInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivityListResp_AllActivityInfo.Unmarshal(m, b)
}
func (m *GetActivityListResp_AllActivityInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivityListResp_AllActivityInfo.Marshal(b, m, deterministic)
}
func (dst *GetActivityListResp_AllActivityInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivityListResp_AllActivityInfo.Merge(dst, src)
}
func (m *GetActivityListResp_AllActivityInfo) XXX_Size() int {
	return xxx_messageInfo_GetActivityListResp_AllActivityInfo.Size(m)
}
func (m *GetActivityListResp_AllActivityInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivityListResp_AllActivityInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivityListResp_AllActivityInfo proto.InternalMessageInfo

func (m *GetActivityListResp_AllActivityInfo) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *GetActivityListResp_AllActivityInfo) GetInfo() *ActivityBaseInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetActivityListResp_AllActivityInfo) GetMusicActivityTopicInfo() *ActivityTopicInfo {
	if m != nil {
		return m.MusicActivityTopicInfo
	}
	return nil
}

func (m *GetActivityListResp_AllActivityInfo) GetPatternType() PatternType {
	if m != nil {
		return m.PatternType
	}
	return PatternType_UNKNOW_PATTERN_TYPE
}

func (m *GetActivityListResp_AllActivityInfo) GetGuestInfos() []*GuestInfosWithTTId {
	if m != nil {
		return m.GuestInfos
	}
	return nil
}

func (m *GetActivityListResp_AllActivityInfo) GetMusicInfos() []*MusicInfo {
	if m != nil {
		return m.MusicInfos
	}
	return nil
}

func (m *GetActivityListResp_AllActivityInfo) GetReviewInfos() []*ReviewInfo {
	if m != nil {
		return m.ReviewInfos
	}
	return nil
}

func (m *GetActivityListResp_AllActivityInfo) GetWelcomePop() *WelcomePop {
	if m != nil {
		return m.WelcomePop
	}
	return nil
}

func (m *GetActivityListResp_AllActivityInfo) GetCokeStateInfo() *CokeStateInfo {
	if m != nil {
		return m.CokeStateInfo
	}
	return nil
}

func (m *GetActivityListResp_AllActivityInfo) GetIsStayAddTicket() bool {
	if m != nil {
		return m.IsStayAddTicket
	}
	return false
}

func (m *GetActivityListResp_AllActivityInfo) GetStayInterval() uint32 {
	if m != nil {
		return m.StayInterval
	}
	return 0
}

func (m *GetActivityListResp_AllActivityInfo) GetTicketCnt() uint32 {
	if m != nil {
		return m.TicketCnt
	}
	return 0
}

func (m *GetActivityListResp_AllActivityInfo) GetTotalAddTicketCnt() uint32 {
	if m != nil {
		return m.TotalAddTicketCnt
	}
	return 0
}

func (m *GetActivityListResp_AllActivityInfo) GetBrandInfo() *BrandIntegralInfo {
	if m != nil {
		return m.BrandInfo
	}
	return nil
}

type GetMyMusicNestListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	YearMonth            string   `protobuf:"bytes,4,opt,name=year_month,json=yearMonth,proto3" json:"year_month,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMyMusicNestListReq) Reset()         { *m = GetMyMusicNestListReq{} }
func (m *GetMyMusicNestListReq) String() string { return proto.CompactTextString(m) }
func (*GetMyMusicNestListReq) ProtoMessage()    {}
func (*GetMyMusicNestListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{37}
}
func (m *GetMyMusicNestListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyMusicNestListReq.Unmarshal(m, b)
}
func (m *GetMyMusicNestListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyMusicNestListReq.Marshal(b, m, deterministic)
}
func (dst *GetMyMusicNestListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyMusicNestListReq.Merge(dst, src)
}
func (m *GetMyMusicNestListReq) XXX_Size() int {
	return xxx_messageInfo_GetMyMusicNestListReq.Size(m)
}
func (m *GetMyMusicNestListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyMusicNestListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyMusicNestListReq proto.InternalMessageInfo

func (m *GetMyMusicNestListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMyMusicNestListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetMyMusicNestListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetMyMusicNestListReq) GetYearMonth() string {
	if m != nil {
		return m.YearMonth
	}
	return ""
}

type GetMyMusicNestListResp struct {
	Next                 uint32                 `protobuf:"varint,1,opt,name=next,proto3" json:"next,omitempty"`
	Activities           []*MyMusicNestActivity `protobuf:"bytes,2,rep,name=activities,proto3" json:"activities,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetMyMusicNestListResp) Reset()         { *m = GetMyMusicNestListResp{} }
func (m *GetMyMusicNestListResp) String() string { return proto.CompactTextString(m) }
func (*GetMyMusicNestListResp) ProtoMessage()    {}
func (*GetMyMusicNestListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{38}
}
func (m *GetMyMusicNestListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyMusicNestListResp.Unmarshal(m, b)
}
func (m *GetMyMusicNestListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyMusicNestListResp.Marshal(b, m, deterministic)
}
func (dst *GetMyMusicNestListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyMusicNestListResp.Merge(dst, src)
}
func (m *GetMyMusicNestListResp) XXX_Size() int {
	return xxx_messageInfo_GetMyMusicNestListResp.Size(m)
}
func (m *GetMyMusicNestListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyMusicNestListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyMusicNestListResp proto.InternalMessageInfo

func (m *GetMyMusicNestListResp) GetNext() uint32 {
	if m != nil {
		return m.Next
	}
	return 0
}

func (m *GetMyMusicNestListResp) GetActivities() []*MyMusicNestActivity {
	if m != nil {
		return m.Activities
	}
	return nil
}

type MyMusicNestActivity struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Image                string   `protobuf:"bytes,3,opt,name=image,proto3" json:"image,omitempty"`
	StartTime            uint32   `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Warmup               uint32   `protobuf:"varint,6,opt,name=warmup,proto3" json:"warmup,omitempty"`
	Participants         uint32   `protobuf:"varint,7,opt,name=participants,proto3" json:"participants,omitempty"`
	Tkid                 string   `protobuf:"bytes,8,opt,name=tkid,proto3" json:"tkid,omitempty"`
	Type                 uint32   `protobuf:"varint,9,opt,name=type,proto3" json:"type,omitempty"`
	Status               uint32   `protobuf:"varint,10,opt,name=status,proto3" json:"status,omitempty"`
	ChannelId            uint32   `protobuf:"varint,11,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ActivityTimeChange   bool     `protobuf:"varint,12,opt,name=activity_time_change,json=activityTimeChange,proto3" json:"activity_time_change,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MyMusicNestActivity) Reset()         { *m = MyMusicNestActivity{} }
func (m *MyMusicNestActivity) String() string { return proto.CompactTextString(m) }
func (*MyMusicNestActivity) ProtoMessage()    {}
func (*MyMusicNestActivity) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{39}
}
func (m *MyMusicNestActivity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MyMusicNestActivity.Unmarshal(m, b)
}
func (m *MyMusicNestActivity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MyMusicNestActivity.Marshal(b, m, deterministic)
}
func (dst *MyMusicNestActivity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MyMusicNestActivity.Merge(dst, src)
}
func (m *MyMusicNestActivity) XXX_Size() int {
	return xxx_messageInfo_MyMusicNestActivity.Size(m)
}
func (m *MyMusicNestActivity) XXX_DiscardUnknown() {
	xxx_messageInfo_MyMusicNestActivity.DiscardUnknown(m)
}

var xxx_messageInfo_MyMusicNestActivity proto.InternalMessageInfo

func (m *MyMusicNestActivity) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MyMusicNestActivity) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *MyMusicNestActivity) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *MyMusicNestActivity) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *MyMusicNestActivity) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *MyMusicNestActivity) GetWarmup() uint32 {
	if m != nil {
		return m.Warmup
	}
	return 0
}

func (m *MyMusicNestActivity) GetParticipants() uint32 {
	if m != nil {
		return m.Participants
	}
	return 0
}

func (m *MyMusicNestActivity) GetTkid() string {
	if m != nil {
		return m.Tkid
	}
	return ""
}

func (m *MyMusicNestActivity) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *MyMusicNestActivity) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *MyMusicNestActivity) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MyMusicNestActivity) GetActivityTimeChange() bool {
	if m != nil {
		return m.ActivityTimeChange
	}
	return false
}

type GetMusicNestActivityInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ActivityId           uint32   `protobuf:"varint,2,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMusicNestActivityInfoReq) Reset()         { *m = GetMusicNestActivityInfoReq{} }
func (m *GetMusicNestActivityInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetMusicNestActivityInfoReq) ProtoMessage()    {}
func (*GetMusicNestActivityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{40}
}
func (m *GetMusicNestActivityInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicNestActivityInfoReq.Unmarshal(m, b)
}
func (m *GetMusicNestActivityInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicNestActivityInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetMusicNestActivityInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicNestActivityInfoReq.Merge(dst, src)
}
func (m *GetMusicNestActivityInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetMusicNestActivityInfoReq.Size(m)
}
func (m *GetMusicNestActivityInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicNestActivityInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicNestActivityInfoReq proto.InternalMessageInfo

func (m *GetMusicNestActivityInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMusicNestActivityInfoReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

type GetMusicNestActivityInfoResp struct {
	Id                   uint32                     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string                     `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Poster               string                     `protobuf:"bytes,3,opt,name=poster,proto3" json:"poster,omitempty"`
	StartTime            uint32                     `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint32                     `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Warmup               uint32                     `protobuf:"varint,6,opt,name=warmup,proto3" json:"warmup,omitempty"`
	TopicDetails         string                     `protobuf:"bytes,7,opt,name=topic_details,json=topicDetails,proto3" json:"topic_details,omitempty"`
	Songs                []*MusicInfo               `protobuf:"bytes,8,rep,name=songs,proto3" json:"songs,omitempty"`
	Replays              []*ReviewInfo              `protobuf:"bytes,9,rep,name=replays,proto3" json:"replays,omitempty"`
	Director             *Director                  `protobuf:"bytes,10,opt,name=director,proto3" json:"director,omitempty"`
	Guests               []*GuestInfo               `protobuf:"bytes,11,rep,name=guests,proto3" json:"guests,omitempty"`
	PlayTogether         *PlayTogether              `protobuf:"bytes,12,opt,name=play_together,json=playTogether,proto3" json:"play_together,omitempty"`
	ChannelId            uint32                     `protobuf:"varint,13,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Top3Activity         []*SimpleMusicNestActivity `protobuf:"bytes,14,rep,name=top3_activity,json=top3Activity,proto3" json:"top3_activity,omitempty"`
	Status               uint32                     `protobuf:"varint,15,opt,name=status,proto3" json:"status,omitempty"`
	RecentActivity       *SimpleMusicNestActivity   `protobuf:"bytes,16,opt,name=recent_activity,json=recentActivity,proto3" json:"recent_activity,omitempty"`
	ChannelType          uint32                     `protobuf:"varint,17,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	IsSub                bool                       `protobuf:"varint,18,opt,name=is_sub,json=isSub,proto3" json:"is_sub,omitempty"`
	TopicType            uint32                     `protobuf:"varint,19,opt,name=topic_type,json=topicType,proto3" json:"topic_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetMusicNestActivityInfoResp) Reset()         { *m = GetMusicNestActivityInfoResp{} }
func (m *GetMusicNestActivityInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetMusicNestActivityInfoResp) ProtoMessage()    {}
func (*GetMusicNestActivityInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{41}
}
func (m *GetMusicNestActivityInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicNestActivityInfoResp.Unmarshal(m, b)
}
func (m *GetMusicNestActivityInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicNestActivityInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetMusicNestActivityInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicNestActivityInfoResp.Merge(dst, src)
}
func (m *GetMusicNestActivityInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetMusicNestActivityInfoResp.Size(m)
}
func (m *GetMusicNestActivityInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicNestActivityInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicNestActivityInfoResp proto.InternalMessageInfo

func (m *GetMusicNestActivityInfoResp) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetMusicNestActivityInfoResp) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetMusicNestActivityInfoResp) GetPoster() string {
	if m != nil {
		return m.Poster
	}
	return ""
}

func (m *GetMusicNestActivityInfoResp) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetMusicNestActivityInfoResp) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetMusicNestActivityInfoResp) GetWarmup() uint32 {
	if m != nil {
		return m.Warmup
	}
	return 0
}

func (m *GetMusicNestActivityInfoResp) GetTopicDetails() string {
	if m != nil {
		return m.TopicDetails
	}
	return ""
}

func (m *GetMusicNestActivityInfoResp) GetSongs() []*MusicInfo {
	if m != nil {
		return m.Songs
	}
	return nil
}

func (m *GetMusicNestActivityInfoResp) GetReplays() []*ReviewInfo {
	if m != nil {
		return m.Replays
	}
	return nil
}

func (m *GetMusicNestActivityInfoResp) GetDirector() *Director {
	if m != nil {
		return m.Director
	}
	return nil
}

func (m *GetMusicNestActivityInfoResp) GetGuests() []*GuestInfo {
	if m != nil {
		return m.Guests
	}
	return nil
}

func (m *GetMusicNestActivityInfoResp) GetPlayTogether() *PlayTogether {
	if m != nil {
		return m.PlayTogether
	}
	return nil
}

func (m *GetMusicNestActivityInfoResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMusicNestActivityInfoResp) GetTop3Activity() []*SimpleMusicNestActivity {
	if m != nil {
		return m.Top3Activity
	}
	return nil
}

func (m *GetMusicNestActivityInfoResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetMusicNestActivityInfoResp) GetRecentActivity() *SimpleMusicNestActivity {
	if m != nil {
		return m.RecentActivity
	}
	return nil
}

func (m *GetMusicNestActivityInfoResp) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *GetMusicNestActivityInfoResp) GetIsSub() bool {
	if m != nil {
		return m.IsSub
	}
	return false
}

func (m *GetMusicNestActivityInfoResp) GetTopicType() uint32 {
	if m != nil {
		return m.TopicType
	}
	return 0
}

type Director struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Introduce            string   `protobuf:"bytes,3,opt,name=introduce,proto3" json:"introduce,omitempty"`
	IsFollow             bool     `protobuf:"varint,4,opt,name=is_follow,json=isFollow,proto3" json:"is_follow,omitempty"`
	Account              string   `protobuf:"bytes,5,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Director) Reset()         { *m = Director{} }
func (m *Director) String() string { return proto.CompactTextString(m) }
func (*Director) ProtoMessage()    {}
func (*Director) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{42}
}
func (m *Director) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Director.Unmarshal(m, b)
}
func (m *Director) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Director.Marshal(b, m, deterministic)
}
func (dst *Director) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Director.Merge(dst, src)
}
func (m *Director) XXX_Size() int {
	return xxx_messageInfo_Director.Size(m)
}
func (m *Director) XXX_DiscardUnknown() {
	xxx_messageInfo_Director.DiscardUnknown(m)
}

var xxx_messageInfo_Director proto.InternalMessageInfo

func (m *Director) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *Director) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *Director) GetIntroduce() string {
	if m != nil {
		return m.Introduce
	}
	return ""
}

func (m *Director) GetIsFollow() bool {
	if m != nil {
		return m.IsFollow
	}
	return false
}

func (m *Director) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type SimpleMusicNestActivity struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Cover                string   `protobuf:"bytes,2,opt,name=cover,proto3" json:"cover,omitempty"`
	Title                string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	HoldTime             uint32   `protobuf:"varint,4,opt,name=hold_time,json=holdTime,proto3" json:"hold_time,omitempty"`
	PlayNum              uint32   `protobuf:"varint,5,opt,name=play_num,json=playNum,proto3" json:"play_num,omitempty"`
	Status               uint32   `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`
	ChannelId            uint32   `protobuf:"varint,7,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType          uint32   `protobuf:"varint,8,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SimpleMusicNestActivity) Reset()         { *m = SimpleMusicNestActivity{} }
func (m *SimpleMusicNestActivity) String() string { return proto.CompactTextString(m) }
func (*SimpleMusicNestActivity) ProtoMessage()    {}
func (*SimpleMusicNestActivity) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{43}
}
func (m *SimpleMusicNestActivity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimpleMusicNestActivity.Unmarshal(m, b)
}
func (m *SimpleMusicNestActivity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimpleMusicNestActivity.Marshal(b, m, deterministic)
}
func (dst *SimpleMusicNestActivity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimpleMusicNestActivity.Merge(dst, src)
}
func (m *SimpleMusicNestActivity) XXX_Size() int {
	return xxx_messageInfo_SimpleMusicNestActivity.Size(m)
}
func (m *SimpleMusicNestActivity) XXX_DiscardUnknown() {
	xxx_messageInfo_SimpleMusicNestActivity.DiscardUnknown(m)
}

var xxx_messageInfo_SimpleMusicNestActivity proto.InternalMessageInfo

func (m *SimpleMusicNestActivity) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SimpleMusicNestActivity) GetCover() string {
	if m != nil {
		return m.Cover
	}
	return ""
}

func (m *SimpleMusicNestActivity) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SimpleMusicNestActivity) GetHoldTime() uint32 {
	if m != nil {
		return m.HoldTime
	}
	return 0
}

func (m *SimpleMusicNestActivity) GetPlayNum() uint32 {
	if m != nil {
		return m.PlayNum
	}
	return 0
}

func (m *SimpleMusicNestActivity) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *SimpleMusicNestActivity) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SimpleMusicNestActivity) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type PlayTogether struct {
	Accounts             []string `protobuf:"bytes,1,rep,name=accounts,proto3" json:"accounts,omitempty"`
	PlayNum              uint32   `protobuf:"varint,2,opt,name=play_num,json=playNum,proto3" json:"play_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlayTogether) Reset()         { *m = PlayTogether{} }
func (m *PlayTogether) String() string { return proto.CompactTextString(m) }
func (*PlayTogether) ProtoMessage()    {}
func (*PlayTogether) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{44}
}
func (m *PlayTogether) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayTogether.Unmarshal(m, b)
}
func (m *PlayTogether) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayTogether.Marshal(b, m, deterministic)
}
func (dst *PlayTogether) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayTogether.Merge(dst, src)
}
func (m *PlayTogether) XXX_Size() int {
	return xxx_messageInfo_PlayTogether.Size(m)
}
func (m *PlayTogether) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayTogether.DiscardUnknown(m)
}

var xxx_messageInfo_PlayTogether proto.InternalMessageInfo

func (m *PlayTogether) GetAccounts() []string {
	if m != nil {
		return m.Accounts
	}
	return nil
}

func (m *PlayTogether) GetPlayNum() uint32 {
	if m != nil {
		return m.PlayNum
	}
	return 0
}

type GetMusicNestCoverAndLiveListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMusicNestCoverAndLiveListReq) Reset()         { *m = GetMusicNestCoverAndLiveListReq{} }
func (m *GetMusicNestCoverAndLiveListReq) String() string { return proto.CompactTextString(m) }
func (*GetMusicNestCoverAndLiveListReq) ProtoMessage()    {}
func (*GetMusicNestCoverAndLiveListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{45}
}
func (m *GetMusicNestCoverAndLiveListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicNestCoverAndLiveListReq.Unmarshal(m, b)
}
func (m *GetMusicNestCoverAndLiveListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicNestCoverAndLiveListReq.Marshal(b, m, deterministic)
}
func (dst *GetMusicNestCoverAndLiveListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicNestCoverAndLiveListReq.Merge(dst, src)
}
func (m *GetMusicNestCoverAndLiveListReq) XXX_Size() int {
	return xxx_messageInfo_GetMusicNestCoverAndLiveListReq.Size(m)
}
func (m *GetMusicNestCoverAndLiveListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicNestCoverAndLiveListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicNestCoverAndLiveListReq proto.InternalMessageInfo

func (m *GetMusicNestCoverAndLiveListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetMusicNestCoverAndLiveListResp struct {
	CoverInfos           []*GetMusicNestCoverAndLiveListResp_CoverInfo `protobuf:"bytes,1,rep,name=cover_infos,json=coverInfos,proto3" json:"cover_infos,omitempty"`
	LiveInfos            []*GetMusicNestCoverAndLiveListResp_LiveInfo  `protobuf:"bytes,2,rep,name=live_infos,json=liveInfos,proto3" json:"live_infos,omitempty"`
	Title                string                                        `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	JumpUrl              string                                        `protobuf:"bytes,5,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-"`
	XXX_unrecognized     []byte                                        `json:"-"`
	XXX_sizecache        int32                                         `json:"-"`
}

func (m *GetMusicNestCoverAndLiveListResp) Reset()         { *m = GetMusicNestCoverAndLiveListResp{} }
func (m *GetMusicNestCoverAndLiveListResp) String() string { return proto.CompactTextString(m) }
func (*GetMusicNestCoverAndLiveListResp) ProtoMessage()    {}
func (*GetMusicNestCoverAndLiveListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{46}
}
func (m *GetMusicNestCoverAndLiveListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicNestCoverAndLiveListResp.Unmarshal(m, b)
}
func (m *GetMusicNestCoverAndLiveListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicNestCoverAndLiveListResp.Marshal(b, m, deterministic)
}
func (dst *GetMusicNestCoverAndLiveListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicNestCoverAndLiveListResp.Merge(dst, src)
}
func (m *GetMusicNestCoverAndLiveListResp) XXX_Size() int {
	return xxx_messageInfo_GetMusicNestCoverAndLiveListResp.Size(m)
}
func (m *GetMusicNestCoverAndLiveListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicNestCoverAndLiveListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicNestCoverAndLiveListResp proto.InternalMessageInfo

func (m *GetMusicNestCoverAndLiveListResp) GetCoverInfos() []*GetMusicNestCoverAndLiveListResp_CoverInfo {
	if m != nil {
		return m.CoverInfos
	}
	return nil
}

func (m *GetMusicNestCoverAndLiveListResp) GetLiveInfos() []*GetMusicNestCoverAndLiveListResp_LiveInfo {
	if m != nil {
		return m.LiveInfos
	}
	return nil
}

func (m *GetMusicNestCoverAndLiveListResp) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetMusicNestCoverAndLiveListResp) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

type GetMusicNestCoverAndLiveListResp_CoverInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ShortCoverImage      string   `protobuf:"bytes,2,opt,name=short_cover_image,json=shortCoverImage,proto3" json:"short_cover_image,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMusicNestCoverAndLiveListResp_CoverInfo) Reset() {
	*m = GetMusicNestCoverAndLiveListResp_CoverInfo{}
}
func (m *GetMusicNestCoverAndLiveListResp_CoverInfo) String() string {
	return proto.CompactTextString(m)
}
func (*GetMusicNestCoverAndLiveListResp_CoverInfo) ProtoMessage() {}
func (*GetMusicNestCoverAndLiveListResp_CoverInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{46, 0}
}
func (m *GetMusicNestCoverAndLiveListResp_CoverInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicNestCoverAndLiveListResp_CoverInfo.Unmarshal(m, b)
}
func (m *GetMusicNestCoverAndLiveListResp_CoverInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicNestCoverAndLiveListResp_CoverInfo.Marshal(b, m, deterministic)
}
func (dst *GetMusicNestCoverAndLiveListResp_CoverInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicNestCoverAndLiveListResp_CoverInfo.Merge(dst, src)
}
func (m *GetMusicNestCoverAndLiveListResp_CoverInfo) XXX_Size() int {
	return xxx_messageInfo_GetMusicNestCoverAndLiveListResp_CoverInfo.Size(m)
}
func (m *GetMusicNestCoverAndLiveListResp_CoverInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicNestCoverAndLiveListResp_CoverInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicNestCoverAndLiveListResp_CoverInfo proto.InternalMessageInfo

func (m *GetMusicNestCoverAndLiveListResp_CoverInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetMusicNestCoverAndLiveListResp_CoverInfo) GetShortCoverImage() string {
	if m != nil {
		return m.ShortCoverImage
	}
	return ""
}

type GetMusicNestCoverAndLiveListResp_LiveInfo struct {
	Id                   uint32                                                `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ShortCoverImage      string                                                `protobuf:"bytes,2,opt,name=short_cover_image,json=shortCoverImage,proto3" json:"short_cover_image,omitempty"`
	CategoryTitle        string                                                `protobuf:"bytes,3,opt,name=category_title,json=categoryTitle,proto3" json:"category_title,omitempty"`
	BackgroundUrl        string                                                `protobuf:"bytes,4,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`
	ChannelId            uint32                                                `protobuf:"varint,6,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	IsRecommend          bool                                                  `protobuf:"varint,8,opt,name=is_recommend,json=isRecommend,proto3" json:"is_recommend,omitempty"`
	Topic                string                                                `protobuf:"bytes,9,opt,name=topic,proto3" json:"topic,omitempty"`
	LiveOnTime           uint32                                                `protobuf:"varint,10,opt,name=live_on_time,json=liveOnTime,proto3" json:"live_on_time,omitempty"`
	WarnUpTime           uint32                                                `protobuf:"varint,11,opt,name=warn_up_time,json=warnUpTime,proto3" json:"warn_up_time,omitempty"`
	SongInfos            []*GetMusicNestCoverAndLiveListResp_LiveInfo_SongInfo `protobuf:"bytes,12,rep,name=song_infos,json=songInfos,proto3" json:"song_infos,omitempty"`
	GuestInfosArray      []*GuestInfos                                         `protobuf:"bytes,13,rep,name=guest_infos_array,json=guestInfosArray,proto3" json:"guest_infos_array,omitempty"`
	PatternType          PatternType                                           `protobuf:"varint,14,opt,name=pattern_type,json=patternType,proto3,enum=music_nest.PatternType" json:"pattern_type,omitempty"`
	LevelType            LevelType                                             `protobuf:"varint,15,opt,name=level_type,json=levelType,proto3,enum=music_nest.LevelType" json:"level_type,omitempty"`
	IsSubActivity        bool                                                  `protobuf:"varint,16,opt,name=is_sub_activity,json=isSubActivity,proto3" json:"is_sub_activity,omitempty"`
	LongCoverImage       string                                                `protobuf:"bytes,17,opt,name=long_cover_image,json=longCoverImage,proto3" json:"long_cover_image,omitempty"`
	NumAudienceJoined    uint32                                                `protobuf:"varint,18,opt,name=num_audience_joined,json=numAudienceJoined,proto3" json:"num_audience_joined,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                              `json:"-"`
	XXX_unrecognized     []byte                                                `json:"-"`
	XXX_sizecache        int32                                                 `json:"-"`
}

func (m *GetMusicNestCoverAndLiveListResp_LiveInfo) Reset() {
	*m = GetMusicNestCoverAndLiveListResp_LiveInfo{}
}
func (m *GetMusicNestCoverAndLiveListResp_LiveInfo) String() string { return proto.CompactTextString(m) }
func (*GetMusicNestCoverAndLiveListResp_LiveInfo) ProtoMessage()    {}
func (*GetMusicNestCoverAndLiveListResp_LiveInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{46, 1}
}
func (m *GetMusicNestCoverAndLiveListResp_LiveInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicNestCoverAndLiveListResp_LiveInfo.Unmarshal(m, b)
}
func (m *GetMusicNestCoverAndLiveListResp_LiveInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicNestCoverAndLiveListResp_LiveInfo.Marshal(b, m, deterministic)
}
func (dst *GetMusicNestCoverAndLiveListResp_LiveInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicNestCoverAndLiveListResp_LiveInfo.Merge(dst, src)
}
func (m *GetMusicNestCoverAndLiveListResp_LiveInfo) XXX_Size() int {
	return xxx_messageInfo_GetMusicNestCoverAndLiveListResp_LiveInfo.Size(m)
}
func (m *GetMusicNestCoverAndLiveListResp_LiveInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicNestCoverAndLiveListResp_LiveInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicNestCoverAndLiveListResp_LiveInfo proto.InternalMessageInfo

func (m *GetMusicNestCoverAndLiveListResp_LiveInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetMusicNestCoverAndLiveListResp_LiveInfo) GetShortCoverImage() string {
	if m != nil {
		return m.ShortCoverImage
	}
	return ""
}

func (m *GetMusicNestCoverAndLiveListResp_LiveInfo) GetCategoryTitle() string {
	if m != nil {
		return m.CategoryTitle
	}
	return ""
}

func (m *GetMusicNestCoverAndLiveListResp_LiveInfo) GetBackgroundUrl() string {
	if m != nil {
		return m.BackgroundUrl
	}
	return ""
}

func (m *GetMusicNestCoverAndLiveListResp_LiveInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMusicNestCoverAndLiveListResp_LiveInfo) GetIsRecommend() bool {
	if m != nil {
		return m.IsRecommend
	}
	return false
}

func (m *GetMusicNestCoverAndLiveListResp_LiveInfo) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *GetMusicNestCoverAndLiveListResp_LiveInfo) GetLiveOnTime() uint32 {
	if m != nil {
		return m.LiveOnTime
	}
	return 0
}

func (m *GetMusicNestCoverAndLiveListResp_LiveInfo) GetWarnUpTime() uint32 {
	if m != nil {
		return m.WarnUpTime
	}
	return 0
}

func (m *GetMusicNestCoverAndLiveListResp_LiveInfo) GetSongInfos() []*GetMusicNestCoverAndLiveListResp_LiveInfo_SongInfo {
	if m != nil {
		return m.SongInfos
	}
	return nil
}

func (m *GetMusicNestCoverAndLiveListResp_LiveInfo) GetGuestInfosArray() []*GuestInfos {
	if m != nil {
		return m.GuestInfosArray
	}
	return nil
}

func (m *GetMusicNestCoverAndLiveListResp_LiveInfo) GetPatternType() PatternType {
	if m != nil {
		return m.PatternType
	}
	return PatternType_UNKNOW_PATTERN_TYPE
}

func (m *GetMusicNestCoverAndLiveListResp_LiveInfo) GetLevelType() LevelType {
	if m != nil {
		return m.LevelType
	}
	return LevelType_UNKNOW_LEVEL_TYPE
}

func (m *GetMusicNestCoverAndLiveListResp_LiveInfo) GetIsSubActivity() bool {
	if m != nil {
		return m.IsSubActivity
	}
	return false
}

func (m *GetMusicNestCoverAndLiveListResp_LiveInfo) GetLongCoverImage() string {
	if m != nil {
		return m.LongCoverImage
	}
	return ""
}

func (m *GetMusicNestCoverAndLiveListResp_LiveInfo) GetNumAudienceJoined() uint32 {
	if m != nil {
		return m.NumAudienceJoined
	}
	return 0
}

type GetMusicNestCoverAndLiveListResp_LiveInfo_SongInfo struct {
	SongName             string   `protobuf:"bytes,1,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMusicNestCoverAndLiveListResp_LiveInfo_SongInfo) Reset() {
	*m = GetMusicNestCoverAndLiveListResp_LiveInfo_SongInfo{}
}
func (m *GetMusicNestCoverAndLiveListResp_LiveInfo_SongInfo) String() string {
	return proto.CompactTextString(m)
}
func (*GetMusicNestCoverAndLiveListResp_LiveInfo_SongInfo) ProtoMessage() {}
func (*GetMusicNestCoverAndLiveListResp_LiveInfo_SongInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{46, 1, 0}
}
func (m *GetMusicNestCoverAndLiveListResp_LiveInfo_SongInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicNestCoverAndLiveListResp_LiveInfo_SongInfo.Unmarshal(m, b)
}
func (m *GetMusicNestCoverAndLiveListResp_LiveInfo_SongInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicNestCoverAndLiveListResp_LiveInfo_SongInfo.Marshal(b, m, deterministic)
}
func (dst *GetMusicNestCoverAndLiveListResp_LiveInfo_SongInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicNestCoverAndLiveListResp_LiveInfo_SongInfo.Merge(dst, src)
}
func (m *GetMusicNestCoverAndLiveListResp_LiveInfo_SongInfo) XXX_Size() int {
	return xxx_messageInfo_GetMusicNestCoverAndLiveListResp_LiveInfo_SongInfo.Size(m)
}
func (m *GetMusicNestCoverAndLiveListResp_LiveInfo_SongInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicNestCoverAndLiveListResp_LiveInfo_SongInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicNestCoverAndLiveListResp_LiveInfo_SongInfo proto.InternalMessageInfo

func (m *GetMusicNestCoverAndLiveListResp_LiveInfo_SongInfo) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

type GetMusicNestHomePageReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMusicNestHomePageReq) Reset()         { *m = GetMusicNestHomePageReq{} }
func (m *GetMusicNestHomePageReq) String() string { return proto.CompactTextString(m) }
func (*GetMusicNestHomePageReq) ProtoMessage()    {}
func (*GetMusicNestHomePageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{47}
}
func (m *GetMusicNestHomePageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicNestHomePageReq.Unmarshal(m, b)
}
func (m *GetMusicNestHomePageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicNestHomePageReq.Marshal(b, m, deterministic)
}
func (dst *GetMusicNestHomePageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicNestHomePageReq.Merge(dst, src)
}
func (m *GetMusicNestHomePageReq) XXX_Size() int {
	return xxx_messageInfo_GetMusicNestHomePageReq.Size(m)
}
func (m *GetMusicNestHomePageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicNestHomePageReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicNestHomePageReq proto.InternalMessageInfo

func (m *GetMusicNestHomePageReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetMusicNestHomePageResp struct {
	IsSubMusicNest        bool                                             `protobuf:"varint,1,opt,name=is_sub_music_nest,json=isSubMusicNest,proto3" json:"is_sub_music_nest,omitempty"`
	ActivityInfos         []*GetMusicNestHomePageResp_ActivityInfo         `protobuf:"bytes,2,rep,name=activity_infos,json=activityInfos,proto3" json:"activity_infos,omitempty"`
	CategoryActivityInfos []*GetMusicNestHomePageResp_CategoryActivityInfo `protobuf:"bytes,3,rep,name=category_activity_infos,json=categoryActivityInfos,proto3" json:"category_activity_infos,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                                         `json:"-"`
	XXX_unrecognized      []byte                                           `json:"-"`
	XXX_sizecache         int32                                            `json:"-"`
}

func (m *GetMusicNestHomePageResp) Reset()         { *m = GetMusicNestHomePageResp{} }
func (m *GetMusicNestHomePageResp) String() string { return proto.CompactTextString(m) }
func (*GetMusicNestHomePageResp) ProtoMessage()    {}
func (*GetMusicNestHomePageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{48}
}
func (m *GetMusicNestHomePageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicNestHomePageResp.Unmarshal(m, b)
}
func (m *GetMusicNestHomePageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicNestHomePageResp.Marshal(b, m, deterministic)
}
func (dst *GetMusicNestHomePageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicNestHomePageResp.Merge(dst, src)
}
func (m *GetMusicNestHomePageResp) XXX_Size() int {
	return xxx_messageInfo_GetMusicNestHomePageResp.Size(m)
}
func (m *GetMusicNestHomePageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicNestHomePageResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicNestHomePageResp proto.InternalMessageInfo

func (m *GetMusicNestHomePageResp) GetIsSubMusicNest() bool {
	if m != nil {
		return m.IsSubMusicNest
	}
	return false
}

func (m *GetMusicNestHomePageResp) GetActivityInfos() []*GetMusicNestHomePageResp_ActivityInfo {
	if m != nil {
		return m.ActivityInfos
	}
	return nil
}

func (m *GetMusicNestHomePageResp) GetCategoryActivityInfos() []*GetMusicNestHomePageResp_CategoryActivityInfo {
	if m != nil {
		return m.CategoryActivityInfos
	}
	return nil
}

type GetMusicNestHomePageResp_ActivityInfo struct {
	Id                   uint32     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Topic                string     `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
	ChannelId            uint32     `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	NumAudience          uint32     `protobuf:"varint,4,opt,name=num_audience,json=numAudience,proto3" json:"num_audience,omitempty"`
	LiveStatus           LiveStatus `protobuf:"varint,5,opt,name=live_status,json=liveStatus,proto3,enum=music_nest.LiveStatus" json:"live_status,omitempty"`
	LiveOnTime           uint32     `protobuf:"varint,6,opt,name=live_on_time,json=liveOnTime,proto3" json:"live_on_time,omitempty"`
	ShortCoverImage      string     `protobuf:"bytes,7,opt,name=short_cover_image,json=shortCoverImage,proto3" json:"short_cover_image,omitempty"`
	LongCoverImage       string     `protobuf:"bytes,8,opt,name=long_cover_image,json=longCoverImage,proto3" json:"long_cover_image,omitempty"`
	FrameColor           string     `protobuf:"bytes,9,opt,name=frame_color,json=frameColor,proto3" json:"frame_color,omitempty"`
	IsRecommend          bool       `protobuf:"varint,10,opt,name=is_recommend,json=isRecommend,proto3" json:"is_recommend,omitempty"`
	WarnUpTime           uint32     `protobuf:"varint,11,opt,name=warn_up_time,json=warnUpTime,proto3" json:"warn_up_time,omitempty"`
	IsSubActivity        bool       `protobuf:"varint,12,opt,name=is_sub_activity,json=isSubActivity,proto3" json:"is_sub_activity,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetMusicNestHomePageResp_ActivityInfo) Reset()         { *m = GetMusicNestHomePageResp_ActivityInfo{} }
func (m *GetMusicNestHomePageResp_ActivityInfo) String() string { return proto.CompactTextString(m) }
func (*GetMusicNestHomePageResp_ActivityInfo) ProtoMessage()    {}
func (*GetMusicNestHomePageResp_ActivityInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{48, 0}
}
func (m *GetMusicNestHomePageResp_ActivityInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicNestHomePageResp_ActivityInfo.Unmarshal(m, b)
}
func (m *GetMusicNestHomePageResp_ActivityInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicNestHomePageResp_ActivityInfo.Marshal(b, m, deterministic)
}
func (dst *GetMusicNestHomePageResp_ActivityInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicNestHomePageResp_ActivityInfo.Merge(dst, src)
}
func (m *GetMusicNestHomePageResp_ActivityInfo) XXX_Size() int {
	return xxx_messageInfo_GetMusicNestHomePageResp_ActivityInfo.Size(m)
}
func (m *GetMusicNestHomePageResp_ActivityInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicNestHomePageResp_ActivityInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicNestHomePageResp_ActivityInfo proto.InternalMessageInfo

func (m *GetMusicNestHomePageResp_ActivityInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetMusicNestHomePageResp_ActivityInfo) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *GetMusicNestHomePageResp_ActivityInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMusicNestHomePageResp_ActivityInfo) GetNumAudience() uint32 {
	if m != nil {
		return m.NumAudience
	}
	return 0
}

func (m *GetMusicNestHomePageResp_ActivityInfo) GetLiveStatus() LiveStatus {
	if m != nil {
		return m.LiveStatus
	}
	return LiveStatus_UNKNOWN_STATUS
}

func (m *GetMusicNestHomePageResp_ActivityInfo) GetLiveOnTime() uint32 {
	if m != nil {
		return m.LiveOnTime
	}
	return 0
}

func (m *GetMusicNestHomePageResp_ActivityInfo) GetShortCoverImage() string {
	if m != nil {
		return m.ShortCoverImage
	}
	return ""
}

func (m *GetMusicNestHomePageResp_ActivityInfo) GetLongCoverImage() string {
	if m != nil {
		return m.LongCoverImage
	}
	return ""
}

func (m *GetMusicNestHomePageResp_ActivityInfo) GetFrameColor() string {
	if m != nil {
		return m.FrameColor
	}
	return ""
}

func (m *GetMusicNestHomePageResp_ActivityInfo) GetIsRecommend() bool {
	if m != nil {
		return m.IsRecommend
	}
	return false
}

func (m *GetMusicNestHomePageResp_ActivityInfo) GetWarnUpTime() uint32 {
	if m != nil {
		return m.WarnUpTime
	}
	return 0
}

func (m *GetMusicNestHomePageResp_ActivityInfo) GetIsSubActivity() bool {
	if m != nil {
		return m.IsSubActivity
	}
	return false
}

type GetMusicNestHomePageResp_CategoryActivityInfo struct {
	MainTitle            string                                   `protobuf:"bytes,1,opt,name=main_title,json=mainTitle,proto3" json:"main_title,omitempty"`
	SubTitle             string                                   `protobuf:"bytes,2,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	ActivityInfos        []*GetMusicNestHomePageResp_ActivityInfo `protobuf:"bytes,3,rep,name=activity_infos,json=activityInfos,proto3" json:"activity_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                 `json:"-"`
	XXX_unrecognized     []byte                                   `json:"-"`
	XXX_sizecache        int32                                    `json:"-"`
}

func (m *GetMusicNestHomePageResp_CategoryActivityInfo) Reset() {
	*m = GetMusicNestHomePageResp_CategoryActivityInfo{}
}
func (m *GetMusicNestHomePageResp_CategoryActivityInfo) String() string {
	return proto.CompactTextString(m)
}
func (*GetMusicNestHomePageResp_CategoryActivityInfo) ProtoMessage() {}
func (*GetMusicNestHomePageResp_CategoryActivityInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{48, 1}
}
func (m *GetMusicNestHomePageResp_CategoryActivityInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicNestHomePageResp_CategoryActivityInfo.Unmarshal(m, b)
}
func (m *GetMusicNestHomePageResp_CategoryActivityInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicNestHomePageResp_CategoryActivityInfo.Marshal(b, m, deterministic)
}
func (dst *GetMusicNestHomePageResp_CategoryActivityInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicNestHomePageResp_CategoryActivityInfo.Merge(dst, src)
}
func (m *GetMusicNestHomePageResp_CategoryActivityInfo) XXX_Size() int {
	return xxx_messageInfo_GetMusicNestHomePageResp_CategoryActivityInfo.Size(m)
}
func (m *GetMusicNestHomePageResp_CategoryActivityInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicNestHomePageResp_CategoryActivityInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicNestHomePageResp_CategoryActivityInfo proto.InternalMessageInfo

func (m *GetMusicNestHomePageResp_CategoryActivityInfo) GetMainTitle() string {
	if m != nil {
		return m.MainTitle
	}
	return ""
}

func (m *GetMusicNestHomePageResp_CategoryActivityInfo) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *GetMusicNestHomePageResp_CategoryActivityInfo) GetActivityInfos() []*GetMusicNestHomePageResp_ActivityInfo {
	if m != nil {
		return m.ActivityInfos
	}
	return nil
}

type SubMusicNestReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IsSub                bool     `protobuf:"varint,2,opt,name=is_sub,json=isSub,proto3" json:"is_sub,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubMusicNestReq) Reset()         { *m = SubMusicNestReq{} }
func (m *SubMusicNestReq) String() string { return proto.CompactTextString(m) }
func (*SubMusicNestReq) ProtoMessage()    {}
func (*SubMusicNestReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{49}
}
func (m *SubMusicNestReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubMusicNestReq.Unmarshal(m, b)
}
func (m *SubMusicNestReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubMusicNestReq.Marshal(b, m, deterministic)
}
func (dst *SubMusicNestReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubMusicNestReq.Merge(dst, src)
}
func (m *SubMusicNestReq) XXX_Size() int {
	return xxx_messageInfo_SubMusicNestReq.Size(m)
}
func (m *SubMusicNestReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SubMusicNestReq.DiscardUnknown(m)
}

var xxx_messageInfo_SubMusicNestReq proto.InternalMessageInfo

func (m *SubMusicNestReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SubMusicNestReq) GetIsSub() bool {
	if m != nil {
		return m.IsSub
	}
	return false
}

type SubMusicNestResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubMusicNestResp) Reset()         { *m = SubMusicNestResp{} }
func (m *SubMusicNestResp) String() string { return proto.CompactTextString(m) }
func (*SubMusicNestResp) ProtoMessage()    {}
func (*SubMusicNestResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{50}
}
func (m *SubMusicNestResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubMusicNestResp.Unmarshal(m, b)
}
func (m *SubMusicNestResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubMusicNestResp.Marshal(b, m, deterministic)
}
func (dst *SubMusicNestResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubMusicNestResp.Merge(dst, src)
}
func (m *SubMusicNestResp) XXX_Size() int {
	return xxx_messageInfo_SubMusicNestResp.Size(m)
}
func (m *SubMusicNestResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SubMusicNestResp.DiscardUnknown(m)
}

var xxx_messageInfo_SubMusicNestResp proto.InternalMessageInfo

type SubMusicNestActivityReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ActivityId           uint32   `protobuf:"varint,2,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubMusicNestActivityReq) Reset()         { *m = SubMusicNestActivityReq{} }
func (m *SubMusicNestActivityReq) String() string { return proto.CompactTextString(m) }
func (*SubMusicNestActivityReq) ProtoMessage()    {}
func (*SubMusicNestActivityReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{51}
}
func (m *SubMusicNestActivityReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubMusicNestActivityReq.Unmarshal(m, b)
}
func (m *SubMusicNestActivityReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubMusicNestActivityReq.Marshal(b, m, deterministic)
}
func (dst *SubMusicNestActivityReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubMusicNestActivityReq.Merge(dst, src)
}
func (m *SubMusicNestActivityReq) XXX_Size() int {
	return xxx_messageInfo_SubMusicNestActivityReq.Size(m)
}
func (m *SubMusicNestActivityReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SubMusicNestActivityReq.DiscardUnknown(m)
}

var xxx_messageInfo_SubMusicNestActivityReq proto.InternalMessageInfo

func (m *SubMusicNestActivityReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SubMusicNestActivityReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

type SubMusicNestActivityResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubMusicNestActivityResp) Reset()         { *m = SubMusicNestActivityResp{} }
func (m *SubMusicNestActivityResp) String() string { return proto.CompactTextString(m) }
func (*SubMusicNestActivityResp) ProtoMessage()    {}
func (*SubMusicNestActivityResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{52}
}
func (m *SubMusicNestActivityResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubMusicNestActivityResp.Unmarshal(m, b)
}
func (m *SubMusicNestActivityResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubMusicNestActivityResp.Marshal(b, m, deterministic)
}
func (dst *SubMusicNestActivityResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubMusicNestActivityResp.Merge(dst, src)
}
func (m *SubMusicNestActivityResp) XXX_Size() int {
	return xxx_messageInfo_SubMusicNestActivityResp.Size(m)
}
func (m *SubMusicNestActivityResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SubMusicNestActivityResp.DiscardUnknown(m)
}

var xxx_messageInfo_SubMusicNestActivityResp proto.InternalMessageInfo

type Guest struct {
	UidArray             []*Guest_UserId `protobuf:"bytes,1,rep,name=uidArray,proto3" json:"uidArray,omitempty"`
	Introduction         string          `protobuf:"bytes,2,opt,name=introduction,proto3" json:"introduction,omitempty"`
	PkIcon               string          `protobuf:"bytes,3,opt,name=pk_icon,json=pkIcon,proto3" json:"pk_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *Guest) Reset()         { *m = Guest{} }
func (m *Guest) String() string { return proto.CompactTextString(m) }
func (*Guest) ProtoMessage()    {}
func (*Guest) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{53}
}
func (m *Guest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Guest.Unmarshal(m, b)
}
func (m *Guest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Guest.Marshal(b, m, deterministic)
}
func (dst *Guest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Guest.Merge(dst, src)
}
func (m *Guest) XXX_Size() int {
	return xxx_messageInfo_Guest.Size(m)
}
func (m *Guest) XXX_DiscardUnknown() {
	xxx_messageInfo_Guest.DiscardUnknown(m)
}

var xxx_messageInfo_Guest proto.InternalMessageInfo

func (m *Guest) GetUidArray() []*Guest_UserId {
	if m != nil {
		return m.UidArray
	}
	return nil
}

func (m *Guest) GetIntroduction() string {
	if m != nil {
		return m.Introduction
	}
	return ""
}

func (m *Guest) GetPkIcon() string {
	if m != nil {
		return m.PkIcon
	}
	return ""
}

type Guest_UserId struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 string   `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Guest_UserId) Reset()         { *m = Guest_UserId{} }
func (m *Guest_UserId) String() string { return proto.CompactTextString(m) }
func (*Guest_UserId) ProtoMessage()    {}
func (*Guest_UserId) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{53, 0}
}
func (m *Guest_UserId) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Guest_UserId.Unmarshal(m, b)
}
func (m *Guest_UserId) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Guest_UserId.Marshal(b, m, deterministic)
}
func (dst *Guest_UserId) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Guest_UserId.Merge(dst, src)
}
func (m *Guest_UserId) XXX_Size() int {
	return xxx_messageInfo_Guest_UserId.Size(m)
}
func (m *Guest_UserId) XXX_DiscardUnknown() {
	xxx_messageInfo_Guest_UserId.DiscardUnknown(m)
}

var xxx_messageInfo_Guest_UserId proto.InternalMessageInfo

func (m *Guest_UserId) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *Guest_UserId) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

type Stage struct {
	StageType            StageType `protobuf:"varint,1,opt,name=stage_type,json=stageType,proto3,enum=music_nest.StageType" json:"stage_type,omitempty"`
	Id                   uint32    `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Title                string    `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Icon                 string    `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"`
	Introduction         string    `protobuf:"bytes,5,opt,name=introduction,proto3" json:"introduction,omitempty"`
	GuestList            []*Guest  `protobuf:"bytes,6,rep,name=guest_list,json=guestList,proto3" json:"guest_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *Stage) Reset()         { *m = Stage{} }
func (m *Stage) String() string { return proto.CompactTextString(m) }
func (*Stage) ProtoMessage()    {}
func (*Stage) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{54}
}
func (m *Stage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Stage.Unmarshal(m, b)
}
func (m *Stage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Stage.Marshal(b, m, deterministic)
}
func (dst *Stage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Stage.Merge(dst, src)
}
func (m *Stage) XXX_Size() int {
	return xxx_messageInfo_Stage.Size(m)
}
func (m *Stage) XXX_DiscardUnknown() {
	xxx_messageInfo_Stage.DiscardUnknown(m)
}

var xxx_messageInfo_Stage proto.InternalMessageInfo

func (m *Stage) GetStageType() StageType {
	if m != nil {
		return m.StageType
	}
	return StageType_UNKNOWN_STAGE_TYPE
}

func (m *Stage) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Stage) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *Stage) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *Stage) GetIntroduction() string {
	if m != nil {
		return m.Introduction
	}
	return ""
}

func (m *Stage) GetGuestList() []*Guest {
	if m != nil {
		return m.GuestList
	}
	return nil
}

type Performance struct {
	Id                    string   `protobuf:"bytes,13,opt,name=id,proto3" json:"id,omitempty"`
	ChannelId             uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	VisibleBeginTime      int64    `protobuf:"varint,2,opt,name=visible_begin_time,json=visibleBeginTime,proto3" json:"visible_begin_time,omitempty"`
	VisibleEndTime        int64    `protobuf:"varint,3,opt,name=visible_end_time,json=visibleEndTime,proto3" json:"visible_end_time,omitempty"`
	Title                 string   `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	EntryIcon             string   `protobuf:"bytes,5,opt,name=entry_icon,json=entryIcon,proto3" json:"entry_icon,omitempty"`
	CurrentStageId        uint32   `protobuf:"varint,6,opt,name=current_stage_id,json=currentStageId,proto3" json:"current_stage_id,omitempty"`
	StageList             []*Stage `protobuf:"bytes,7,rep,name=stage_list,json=stageList,proto3" json:"stage_list,omitempty"`
	StageUpdateTime       int64    `protobuf:"varint,8,opt,name=stage_update_time,json=stageUpdateTime,proto3" json:"stage_update_time,omitempty"`
	PerformanceUpdateTime int64    `protobuf:"varint,9,opt,name=performance_update_time,json=performanceUpdateTime,proto3" json:"performance_update_time,omitempty"`
	Maintainer            string   `protobuf:"bytes,10,opt,name=maintainer,proto3" json:"maintainer,omitempty"`
	OwnerNickName         string   `protobuf:"bytes,11,opt,name=owner_nick_name,json=ownerNickName,proto3" json:"owner_nick_name,omitempty"`
	DisplayId             uint32   `protobuf:"varint,12,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	ChannelViewId         string   `protobuf:"bytes,14,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *Performance) Reset()         { *m = Performance{} }
func (m *Performance) String() string { return proto.CompactTextString(m) }
func (*Performance) ProtoMessage()    {}
func (*Performance) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{55}
}
func (m *Performance) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Performance.Unmarshal(m, b)
}
func (m *Performance) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Performance.Marshal(b, m, deterministic)
}
func (dst *Performance) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Performance.Merge(dst, src)
}
func (m *Performance) XXX_Size() int {
	return xxx_messageInfo_Performance.Size(m)
}
func (m *Performance) XXX_DiscardUnknown() {
	xxx_messageInfo_Performance.DiscardUnknown(m)
}

var xxx_messageInfo_Performance proto.InternalMessageInfo

func (m *Performance) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *Performance) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *Performance) GetVisibleBeginTime() int64 {
	if m != nil {
		return m.VisibleBeginTime
	}
	return 0
}

func (m *Performance) GetVisibleEndTime() int64 {
	if m != nil {
		return m.VisibleEndTime
	}
	return 0
}

func (m *Performance) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *Performance) GetEntryIcon() string {
	if m != nil {
		return m.EntryIcon
	}
	return ""
}

func (m *Performance) GetCurrentStageId() uint32 {
	if m != nil {
		return m.CurrentStageId
	}
	return 0
}

func (m *Performance) GetStageList() []*Stage {
	if m != nil {
		return m.StageList
	}
	return nil
}

func (m *Performance) GetStageUpdateTime() int64 {
	if m != nil {
		return m.StageUpdateTime
	}
	return 0
}

func (m *Performance) GetPerformanceUpdateTime() int64 {
	if m != nil {
		return m.PerformanceUpdateTime
	}
	return 0
}

func (m *Performance) GetMaintainer() string {
	if m != nil {
		return m.Maintainer
	}
	return ""
}

func (m *Performance) GetOwnerNickName() string {
	if m != nil {
		return m.OwnerNickName
	}
	return ""
}

func (m *Performance) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

func (m *Performance) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type AddPerformanceReq struct {
	Performance          *Performance `protobuf:"bytes,1,opt,name=performance,proto3" json:"performance,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AddPerformanceReq) Reset()         { *m = AddPerformanceReq{} }
func (m *AddPerformanceReq) String() string { return proto.CompactTextString(m) }
func (*AddPerformanceReq) ProtoMessage()    {}
func (*AddPerformanceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{56}
}
func (m *AddPerformanceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPerformanceReq.Unmarshal(m, b)
}
func (m *AddPerformanceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPerformanceReq.Marshal(b, m, deterministic)
}
func (dst *AddPerformanceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPerformanceReq.Merge(dst, src)
}
func (m *AddPerformanceReq) XXX_Size() int {
	return xxx_messageInfo_AddPerformanceReq.Size(m)
}
func (m *AddPerformanceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPerformanceReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPerformanceReq proto.InternalMessageInfo

func (m *AddPerformanceReq) GetPerformance() *Performance {
	if m != nil {
		return m.Performance
	}
	return nil
}

type AddPerformanceResp struct {
	Performance          *Performance `protobuf:"bytes,1,opt,name=performance,proto3" json:"performance,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AddPerformanceResp) Reset()         { *m = AddPerformanceResp{} }
func (m *AddPerformanceResp) String() string { return proto.CompactTextString(m) }
func (*AddPerformanceResp) ProtoMessage()    {}
func (*AddPerformanceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{57}
}
func (m *AddPerformanceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPerformanceResp.Unmarshal(m, b)
}
func (m *AddPerformanceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPerformanceResp.Marshal(b, m, deterministic)
}
func (dst *AddPerformanceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPerformanceResp.Merge(dst, src)
}
func (m *AddPerformanceResp) XXX_Size() int {
	return xxx_messageInfo_AddPerformanceResp.Size(m)
}
func (m *AddPerformanceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPerformanceResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPerformanceResp proto.InternalMessageInfo

func (m *AddPerformanceResp) GetPerformance() *Performance {
	if m != nil {
		return m.Performance
	}
	return nil
}

type StopPerformanceReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Id                   string   `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopPerformanceReq) Reset()         { *m = StopPerformanceReq{} }
func (m *StopPerformanceReq) String() string { return proto.CompactTextString(m) }
func (*StopPerformanceReq) ProtoMessage()    {}
func (*StopPerformanceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{58}
}
func (m *StopPerformanceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopPerformanceReq.Unmarshal(m, b)
}
func (m *StopPerformanceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopPerformanceReq.Marshal(b, m, deterministic)
}
func (dst *StopPerformanceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopPerformanceReq.Merge(dst, src)
}
func (m *StopPerformanceReq) XXX_Size() int {
	return xxx_messageInfo_StopPerformanceReq.Size(m)
}
func (m *StopPerformanceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StopPerformanceReq.DiscardUnknown(m)
}

var xxx_messageInfo_StopPerformanceReq proto.InternalMessageInfo

func (m *StopPerformanceReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StopPerformanceReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type StopPerformanceResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopPerformanceResp) Reset()         { *m = StopPerformanceResp{} }
func (m *StopPerformanceResp) String() string { return proto.CompactTextString(m) }
func (*StopPerformanceResp) ProtoMessage()    {}
func (*StopPerformanceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{59}
}
func (m *StopPerformanceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopPerformanceResp.Unmarshal(m, b)
}
func (m *StopPerformanceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopPerformanceResp.Marshal(b, m, deterministic)
}
func (dst *StopPerformanceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopPerformanceResp.Merge(dst, src)
}
func (m *StopPerformanceResp) XXX_Size() int {
	return xxx_messageInfo_StopPerformanceResp.Size(m)
}
func (m *StopPerformanceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StopPerformanceResp.DiscardUnknown(m)
}

var xxx_messageInfo_StopPerformanceResp proto.InternalMessageInfo

type GetPerformanceReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	IsCurrent            bool     `protobuf:"varint,2,opt,name=is_current,json=isCurrent,proto3" json:"is_current,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPerformanceReq) Reset()         { *m = GetPerformanceReq{} }
func (m *GetPerformanceReq) String() string { return proto.CompactTextString(m) }
func (*GetPerformanceReq) ProtoMessage()    {}
func (*GetPerformanceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{60}
}
func (m *GetPerformanceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPerformanceReq.Unmarshal(m, b)
}
func (m *GetPerformanceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPerformanceReq.Marshal(b, m, deterministic)
}
func (dst *GetPerformanceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPerformanceReq.Merge(dst, src)
}
func (m *GetPerformanceReq) XXX_Size() int {
	return xxx_messageInfo_GetPerformanceReq.Size(m)
}
func (m *GetPerformanceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPerformanceReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPerformanceReq proto.InternalMessageInfo

func (m *GetPerformanceReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPerformanceReq) GetIsCurrent() bool {
	if m != nil {
		return m.IsCurrent
	}
	return false
}

type NextDirectionAct struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Title                string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	CategoryTitle        string   `protobuf:"bytes,4,opt,name=category_title,json=categoryTitle,proto3" json:"category_title,omitempty"`
	ExpireTime           uint32   `protobuf:"varint,5,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	ShortCoverImage      string   `protobuf:"bytes,6,opt,name=short_cover_image,json=shortCoverImage,proto3" json:"short_cover_image,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NextDirectionAct) Reset()         { *m = NextDirectionAct{} }
func (m *NextDirectionAct) String() string { return proto.CompactTextString(m) }
func (*NextDirectionAct) ProtoMessage()    {}
func (*NextDirectionAct) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{61}
}
func (m *NextDirectionAct) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NextDirectionAct.Unmarshal(m, b)
}
func (m *NextDirectionAct) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NextDirectionAct.Marshal(b, m, deterministic)
}
func (dst *NextDirectionAct) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NextDirectionAct.Merge(dst, src)
}
func (m *NextDirectionAct) XXX_Size() int {
	return xxx_messageInfo_NextDirectionAct.Size(m)
}
func (m *NextDirectionAct) XXX_DiscardUnknown() {
	xxx_messageInfo_NextDirectionAct.DiscardUnknown(m)
}

var xxx_messageInfo_NextDirectionAct proto.InternalMessageInfo

func (m *NextDirectionAct) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *NextDirectionAct) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *NextDirectionAct) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *NextDirectionAct) GetCategoryTitle() string {
	if m != nil {
		return m.CategoryTitle
	}
	return ""
}

func (m *NextDirectionAct) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *NextDirectionAct) GetShortCoverImage() string {
	if m != nil {
		return m.ShortCoverImage
	}
	return ""
}

type GetPerformanceResp struct {
	Performances         []*Performance    `protobuf:"bytes,1,rep,name=performances,proto3" json:"performances,omitempty"`
	PerformanceType      uint32            `protobuf:"varint,3,opt,name=performance_type,json=performanceType,proto3" json:"performance_type,omitempty"`
	NextAct              *NextDirectionAct `protobuf:"bytes,4,opt,name=next_act,json=nextAct,proto3" json:"next_act,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetPerformanceResp) Reset()         { *m = GetPerformanceResp{} }
func (m *GetPerformanceResp) String() string { return proto.CompactTextString(m) }
func (*GetPerformanceResp) ProtoMessage()    {}
func (*GetPerformanceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{62}
}
func (m *GetPerformanceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPerformanceResp.Unmarshal(m, b)
}
func (m *GetPerformanceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPerformanceResp.Marshal(b, m, deterministic)
}
func (dst *GetPerformanceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPerformanceResp.Merge(dst, src)
}
func (m *GetPerformanceResp) XXX_Size() int {
	return xxx_messageInfo_GetPerformanceResp.Size(m)
}
func (m *GetPerformanceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPerformanceResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPerformanceResp proto.InternalMessageInfo

func (m *GetPerformanceResp) GetPerformances() []*Performance {
	if m != nil {
		return m.Performances
	}
	return nil
}

func (m *GetPerformanceResp) GetPerformanceType() uint32 {
	if m != nil {
		return m.PerformanceType
	}
	return 0
}

func (m *GetPerformanceResp) GetNextAct() *NextDirectionAct {
	if m != nil {
		return m.NextAct
	}
	return nil
}

type GetPerformanceByIdReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPerformanceByIdReq) Reset()         { *m = GetPerformanceByIdReq{} }
func (m *GetPerformanceByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetPerformanceByIdReq) ProtoMessage()    {}
func (*GetPerformanceByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{63}
}
func (m *GetPerformanceByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPerformanceByIdReq.Unmarshal(m, b)
}
func (m *GetPerformanceByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPerformanceByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetPerformanceByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPerformanceByIdReq.Merge(dst, src)
}
func (m *GetPerformanceByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetPerformanceByIdReq.Size(m)
}
func (m *GetPerformanceByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPerformanceByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPerformanceByIdReq proto.InternalMessageInfo

func (m *GetPerformanceByIdReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type GetPerformanceByIdResp struct {
	Performances         *Performance `protobuf:"bytes,1,opt,name=performances,proto3" json:"performances,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPerformanceByIdResp) Reset()         { *m = GetPerformanceByIdResp{} }
func (m *GetPerformanceByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetPerformanceByIdResp) ProtoMessage()    {}
func (*GetPerformanceByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{64}
}
func (m *GetPerformanceByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPerformanceByIdResp.Unmarshal(m, b)
}
func (m *GetPerformanceByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPerformanceByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetPerformanceByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPerformanceByIdResp.Merge(dst, src)
}
func (m *GetPerformanceByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetPerformanceByIdResp.Size(m)
}
func (m *GetPerformanceByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPerformanceByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPerformanceByIdResp proto.InternalMessageInfo

func (m *GetPerformanceByIdResp) GetPerformances() *Performance {
	if m != nil {
		return m.Performances
	}
	return nil
}

type SetCurrentPerformanceStageReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	CurrentStageId       uint32   `protobuf:"varint,2,opt,name=current_stage_id,json=currentStageId,proto3" json:"current_stage_id,omitempty"`
	Id                   string   `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetCurrentPerformanceStageReq) Reset()         { *m = SetCurrentPerformanceStageReq{} }
func (m *SetCurrentPerformanceStageReq) String() string { return proto.CompactTextString(m) }
func (*SetCurrentPerformanceStageReq) ProtoMessage()    {}
func (*SetCurrentPerformanceStageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{65}
}
func (m *SetCurrentPerformanceStageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCurrentPerformanceStageReq.Unmarshal(m, b)
}
func (m *SetCurrentPerformanceStageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCurrentPerformanceStageReq.Marshal(b, m, deterministic)
}
func (dst *SetCurrentPerformanceStageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCurrentPerformanceStageReq.Merge(dst, src)
}
func (m *SetCurrentPerformanceStageReq) XXX_Size() int {
	return xxx_messageInfo_SetCurrentPerformanceStageReq.Size(m)
}
func (m *SetCurrentPerformanceStageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCurrentPerformanceStageReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetCurrentPerformanceStageReq proto.InternalMessageInfo

func (m *SetCurrentPerformanceStageReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetCurrentPerformanceStageReq) GetCurrentStageId() uint32 {
	if m != nil {
		return m.CurrentStageId
	}
	return 0
}

func (m *SetCurrentPerformanceStageReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type SetCurrentPerformanceStageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetCurrentPerformanceStageResp) Reset()         { *m = SetCurrentPerformanceStageResp{} }
func (m *SetCurrentPerformanceStageResp) String() string { return proto.CompactTextString(m) }
func (*SetCurrentPerformanceStageResp) ProtoMessage()    {}
func (*SetCurrentPerformanceStageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{66}
}
func (m *SetCurrentPerformanceStageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCurrentPerformanceStageResp.Unmarshal(m, b)
}
func (m *SetCurrentPerformanceStageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCurrentPerformanceStageResp.Marshal(b, m, deterministic)
}
func (dst *SetCurrentPerformanceStageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCurrentPerformanceStageResp.Merge(dst, src)
}
func (m *SetCurrentPerformanceStageResp) XXX_Size() int {
	return xxx_messageInfo_SetCurrentPerformanceStageResp.Size(m)
}
func (m *SetCurrentPerformanceStageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCurrentPerformanceStageResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetCurrentPerformanceStageResp proto.InternalMessageInfo

type DisappearNextDirectionActReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisappearNextDirectionActReq) Reset()         { *m = DisappearNextDirectionActReq{} }
func (m *DisappearNextDirectionActReq) String() string { return proto.CompactTextString(m) }
func (*DisappearNextDirectionActReq) ProtoMessage()    {}
func (*DisappearNextDirectionActReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{67}
}
func (m *DisappearNextDirectionActReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisappearNextDirectionActReq.Unmarshal(m, b)
}
func (m *DisappearNextDirectionActReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisappearNextDirectionActReq.Marshal(b, m, deterministic)
}
func (dst *DisappearNextDirectionActReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisappearNextDirectionActReq.Merge(dst, src)
}
func (m *DisappearNextDirectionActReq) XXX_Size() int {
	return xxx_messageInfo_DisappearNextDirectionActReq.Size(m)
}
func (m *DisappearNextDirectionActReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DisappearNextDirectionActReq.DiscardUnknown(m)
}

var xxx_messageInfo_DisappearNextDirectionActReq proto.InternalMessageInfo

func (m *DisappearNextDirectionActReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type DisappearNextDirectionActResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisappearNextDirectionActResp) Reset()         { *m = DisappearNextDirectionActResp{} }
func (m *DisappearNextDirectionActResp) String() string { return proto.CompactTextString(m) }
func (*DisappearNextDirectionActResp) ProtoMessage()    {}
func (*DisappearNextDirectionActResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{68}
}
func (m *DisappearNextDirectionActResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisappearNextDirectionActResp.Unmarshal(m, b)
}
func (m *DisappearNextDirectionActResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisappearNextDirectionActResp.Marshal(b, m, deterministic)
}
func (dst *DisappearNextDirectionActResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisappearNextDirectionActResp.Merge(dst, src)
}
func (m *DisappearNextDirectionActResp) XXX_Size() int {
	return xxx_messageInfo_DisappearNextDirectionActResp.Size(m)
}
func (m *DisappearNextDirectionActResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DisappearNextDirectionActResp.DiscardUnknown(m)
}

var xxx_messageInfo_DisappearNextDirectionActResp proto.InternalMessageInfo

// 已看过
type VisitedInfo struct {
	VisitedSize          uint32   `protobuf:"varint,1,opt,name=visited_size,json=visitedSize,proto3" json:"visited_size,omitempty"`
	NeedUse              bool     `protobuf:"varint,2,opt,name=need_use,json=needUse,proto3" json:"need_use,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VisitedInfo) Reset()         { *m = VisitedInfo{} }
func (m *VisitedInfo) String() string { return proto.CompactTextString(m) }
func (*VisitedInfo) ProtoMessage()    {}
func (*VisitedInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{69}
}
func (m *VisitedInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VisitedInfo.Unmarshal(m, b)
}
func (m *VisitedInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VisitedInfo.Marshal(b, m, deterministic)
}
func (dst *VisitedInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VisitedInfo.Merge(dst, src)
}
func (m *VisitedInfo) XXX_Size() int {
	return xxx_messageInfo_VisitedInfo.Size(m)
}
func (m *VisitedInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_VisitedInfo.DiscardUnknown(m)
}

var xxx_messageInfo_VisitedInfo proto.InternalMessageInfo

func (m *VisitedInfo) GetVisitedSize() uint32 {
	if m != nil {
		return m.VisitedSize
	}
	return 0
}

func (m *VisitedInfo) GetNeedUse() bool {
	if m != nil {
		return m.NeedUse
	}
	return false
}

type GetSpecifiedChannelVisitedSizeReq struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSpecifiedChannelVisitedSizeReq) Reset()         { *m = GetSpecifiedChannelVisitedSizeReq{} }
func (m *GetSpecifiedChannelVisitedSizeReq) String() string { return proto.CompactTextString(m) }
func (*GetSpecifiedChannelVisitedSizeReq) ProtoMessage()    {}
func (*GetSpecifiedChannelVisitedSizeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{70}
}
func (m *GetSpecifiedChannelVisitedSizeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSpecifiedChannelVisitedSizeReq.Unmarshal(m, b)
}
func (m *GetSpecifiedChannelVisitedSizeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSpecifiedChannelVisitedSizeReq.Marshal(b, m, deterministic)
}
func (dst *GetSpecifiedChannelVisitedSizeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSpecifiedChannelVisitedSizeReq.Merge(dst, src)
}
func (m *GetSpecifiedChannelVisitedSizeReq) XXX_Size() int {
	return xxx_messageInfo_GetSpecifiedChannelVisitedSizeReq.Size(m)
}
func (m *GetSpecifiedChannelVisitedSizeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSpecifiedChannelVisitedSizeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSpecifiedChannelVisitedSizeReq proto.InternalMessageInfo

func (m *GetSpecifiedChannelVisitedSizeReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type GetSpecifiedChannelVisitedSizeResp struct {
	VisitedInfoList      map[uint32]*VisitedInfo `protobuf:"bytes,1,rep,name=visited_info_list,json=visitedInfoList,proto3" json:"visited_info_list,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetSpecifiedChannelVisitedSizeResp) Reset()         { *m = GetSpecifiedChannelVisitedSizeResp{} }
func (m *GetSpecifiedChannelVisitedSizeResp) String() string { return proto.CompactTextString(m) }
func (*GetSpecifiedChannelVisitedSizeResp) ProtoMessage()    {}
func (*GetSpecifiedChannelVisitedSizeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{71}
}
func (m *GetSpecifiedChannelVisitedSizeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSpecifiedChannelVisitedSizeResp.Unmarshal(m, b)
}
func (m *GetSpecifiedChannelVisitedSizeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSpecifiedChannelVisitedSizeResp.Marshal(b, m, deterministic)
}
func (dst *GetSpecifiedChannelVisitedSizeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSpecifiedChannelVisitedSizeResp.Merge(dst, src)
}
func (m *GetSpecifiedChannelVisitedSizeResp) XXX_Size() int {
	return xxx_messageInfo_GetSpecifiedChannelVisitedSizeResp.Size(m)
}
func (m *GetSpecifiedChannelVisitedSizeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSpecifiedChannelVisitedSizeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSpecifiedChannelVisitedSizeResp proto.InternalMessageInfo

func (m *GetSpecifiedChannelVisitedSizeResp) GetVisitedInfoList() map[uint32]*VisitedInfo {
	if m != nil {
		return m.VisitedInfoList
	}
	return nil
}

type OptionStruct struct {
	OptionName           string   `protobuf:"bytes,1,opt,name=optionName,proto3" json:"optionName,omitempty"`
	OptionId             uint32   `protobuf:"varint,2,opt,name=optionId,proto3" json:"optionId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OptionStruct) Reset()         { *m = OptionStruct{} }
func (m *OptionStruct) String() string { return proto.CompactTextString(m) }
func (*OptionStruct) ProtoMessage()    {}
func (*OptionStruct) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{72}
}
func (m *OptionStruct) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OptionStruct.Unmarshal(m, b)
}
func (m *OptionStruct) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OptionStruct.Marshal(b, m, deterministic)
}
func (dst *OptionStruct) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OptionStruct.Merge(dst, src)
}
func (m *OptionStruct) XXX_Size() int {
	return xxx_messageInfo_OptionStruct.Size(m)
}
func (m *OptionStruct) XXX_DiscardUnknown() {
	xxx_messageInfo_OptionStruct.DiscardUnknown(m)
}

var xxx_messageInfo_OptionStruct proto.InternalMessageInfo

func (m *OptionStruct) GetOptionName() string {
	if m != nil {
		return m.OptionName
	}
	return ""
}

func (m *OptionStruct) GetOptionId() uint32 {
	if m != nil {
		return m.OptionId
	}
	return 0
}

type SendWelcomePop struct {
	StartFlag            bool            `protobuf:"varint,1,opt,name=startFlag,proto3" json:"startFlag,omitempty"`
	Topic                string          `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
	OptionOne            []*OptionStruct `protobuf:"bytes,3,rep,name=optionOne,proto3" json:"optionOne,omitempty"`
	PictureUrl           string          `protobuf:"bytes,4,opt,name=pictureUrl,proto3" json:"pictureUrl,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SendWelcomePop) Reset()         { *m = SendWelcomePop{} }
func (m *SendWelcomePop) String() string { return proto.CompactTextString(m) }
func (*SendWelcomePop) ProtoMessage()    {}
func (*SendWelcomePop) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{73}
}
func (m *SendWelcomePop) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendWelcomePop.Unmarshal(m, b)
}
func (m *SendWelcomePop) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendWelcomePop.Marshal(b, m, deterministic)
}
func (dst *SendWelcomePop) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendWelcomePop.Merge(dst, src)
}
func (m *SendWelcomePop) XXX_Size() int {
	return xxx_messageInfo_SendWelcomePop.Size(m)
}
func (m *SendWelcomePop) XXX_DiscardUnknown() {
	xxx_messageInfo_SendWelcomePop.DiscardUnknown(m)
}

var xxx_messageInfo_SendWelcomePop proto.InternalMessageInfo

func (m *SendWelcomePop) GetStartFlag() bool {
	if m != nil {
		return m.StartFlag
	}
	return false
}

func (m *SendWelcomePop) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *SendWelcomePop) GetOptionOne() []*OptionStruct {
	if m != nil {
		return m.OptionOne
	}
	return nil
}

func (m *SendWelcomePop) GetPictureUrl() string {
	if m != nil {
		return m.PictureUrl
	}
	return ""
}

// 用户进房消息
type GetWelcomePopReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWelcomePopReq) Reset()         { *m = GetWelcomePopReq{} }
func (m *GetWelcomePopReq) String() string { return proto.CompactTextString(m) }
func (*GetWelcomePopReq) ProtoMessage()    {}
func (*GetWelcomePopReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{74}
}
func (m *GetWelcomePopReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWelcomePopReq.Unmarshal(m, b)
}
func (m *GetWelcomePopReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWelcomePopReq.Marshal(b, m, deterministic)
}
func (dst *GetWelcomePopReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWelcomePopReq.Merge(dst, src)
}
func (m *GetWelcomePopReq) XXX_Size() int {
	return xxx_messageInfo_GetWelcomePopReq.Size(m)
}
func (m *GetWelcomePopReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWelcomePopReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWelcomePopReq proto.InternalMessageInfo

func (m *GetWelcomePopReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetWelcomePopReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetWelcomePopResp struct {
	WelcomeMsg           *SendWelcomePop `protobuf:"bytes,1,opt,name=welcome_msg,json=welcomeMsg,proto3" json:"welcome_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetWelcomePopResp) Reset()         { *m = GetWelcomePopResp{} }
func (m *GetWelcomePopResp) String() string { return proto.CompactTextString(m) }
func (*GetWelcomePopResp) ProtoMessage()    {}
func (*GetWelcomePopResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{75}
}
func (m *GetWelcomePopResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWelcomePopResp.Unmarshal(m, b)
}
func (m *GetWelcomePopResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWelcomePopResp.Marshal(b, m, deterministic)
}
func (dst *GetWelcomePopResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWelcomePopResp.Merge(dst, src)
}
func (m *GetWelcomePopResp) XXX_Size() int {
	return xxx_messageInfo_GetWelcomePopResp.Size(m)
}
func (m *GetWelcomePopResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWelcomePopResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWelcomePopResp proto.InternalMessageInfo

func (m *GetWelcomePopResp) GetWelcomeMsg() *SendWelcomePop {
	if m != nil {
		return m.WelcomeMsg
	}
	return nil
}

// 用户点击消息触发公屏推送
type UserClickPopReq struct {
	ChannelId            uint32        `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Index                uint32        `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`
	OptionOne            *OptionStruct `protobuf:"bytes,3,opt,name=optionOne,proto3" json:"optionOne,omitempty"`
	Uid                  uint32        `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UserClickPopReq) Reset()         { *m = UserClickPopReq{} }
func (m *UserClickPopReq) String() string { return proto.CompactTextString(m) }
func (*UserClickPopReq) ProtoMessage()    {}
func (*UserClickPopReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{76}
}
func (m *UserClickPopReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserClickPopReq.Unmarshal(m, b)
}
func (m *UserClickPopReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserClickPopReq.Marshal(b, m, deterministic)
}
func (dst *UserClickPopReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserClickPopReq.Merge(dst, src)
}
func (m *UserClickPopReq) XXX_Size() int {
	return xxx_messageInfo_UserClickPopReq.Size(m)
}
func (m *UserClickPopReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserClickPopReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserClickPopReq proto.InternalMessageInfo

func (m *UserClickPopReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserClickPopReq) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *UserClickPopReq) GetOptionOne() *OptionStruct {
	if m != nil {
		return m.OptionOne
	}
	return nil
}

func (m *UserClickPopReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type UserClickPopResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserClickPopResp) Reset()         { *m = UserClickPopResp{} }
func (m *UserClickPopResp) String() string { return proto.CompactTextString(m) }
func (*UserClickPopResp) ProtoMessage()    {}
func (*UserClickPopResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{77}
}
func (m *UserClickPopResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserClickPopResp.Unmarshal(m, b)
}
func (m *UserClickPopResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserClickPopResp.Marshal(b, m, deterministic)
}
func (dst *UserClickPopResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserClickPopResp.Merge(dst, src)
}
func (m *UserClickPopResp) XXX_Size() int {
	return xxx_messageInfo_UserClickPopResp.Size(m)
}
func (m *UserClickPopResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserClickPopResp.DiscardUnknown(m)
}

var xxx_messageInfo_UserClickPopResp proto.InternalMessageInfo

// 快乐水推送类型
type PushCokeStateInfo struct {
	Info                 *CokeAct `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushCokeStateInfo) Reset()         { *m = PushCokeStateInfo{} }
func (m *PushCokeStateInfo) String() string { return proto.CompactTextString(m) }
func (*PushCokeStateInfo) ProtoMessage()    {}
func (*PushCokeStateInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{78}
}
func (m *PushCokeStateInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushCokeStateInfo.Unmarshal(m, b)
}
func (m *PushCokeStateInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushCokeStateInfo.Marshal(b, m, deterministic)
}
func (dst *PushCokeStateInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushCokeStateInfo.Merge(dst, src)
}
func (m *PushCokeStateInfo) XXX_Size() int {
	return xxx_messageInfo_PushCokeStateInfo.Size(m)
}
func (m *PushCokeStateInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PushCokeStateInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PushCokeStateInfo proto.InternalMessageInfo

func (m *PushCokeStateInfo) GetInfo() *CokeAct {
	if m != nil {
		return m.Info
	}
	return nil
}

// 进房后乐窝活动进行期间的信息获取 (和乐窝活动相关的汇总）
type GetMusicNestLiveInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMusicNestLiveInfoReq) Reset()         { *m = GetMusicNestLiveInfoReq{} }
func (m *GetMusicNestLiveInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetMusicNestLiveInfoReq) ProtoMessage()    {}
func (*GetMusicNestLiveInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{79}
}
func (m *GetMusicNestLiveInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicNestLiveInfoReq.Unmarshal(m, b)
}
func (m *GetMusicNestLiveInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicNestLiveInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetMusicNestLiveInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicNestLiveInfoReq.Merge(dst, src)
}
func (m *GetMusicNestLiveInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetMusicNestLiveInfoReq.Size(m)
}
func (m *GetMusicNestLiveInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicNestLiveInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicNestLiveInfoReq proto.InternalMessageInfo

func (m *GetMusicNestLiveInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMusicNestLiveInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetMusicNestLiveInfoResp struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	CokeInfo             *CokeAct `protobuf:"bytes,2,opt,name=coke_info,json=cokeInfo,proto3" json:"coke_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMusicNestLiveInfoResp) Reset()         { *m = GetMusicNestLiveInfoResp{} }
func (m *GetMusicNestLiveInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetMusicNestLiveInfoResp) ProtoMessage()    {}
func (*GetMusicNestLiveInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{80}
}
func (m *GetMusicNestLiveInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicNestLiveInfoResp.Unmarshal(m, b)
}
func (m *GetMusicNestLiveInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicNestLiveInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetMusicNestLiveInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicNestLiveInfoResp.Merge(dst, src)
}
func (m *GetMusicNestLiveInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetMusicNestLiveInfoResp.Size(m)
}
func (m *GetMusicNestLiveInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicNestLiveInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicNestLiveInfoResp proto.InternalMessageInfo

func (m *GetMusicNestLiveInfoResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMusicNestLiveInfoResp) GetCokeInfo() *CokeAct {
	if m != nil {
		return m.CokeInfo
	}
	return nil
}

// 乐窝活动节目进行情况信息推送
type CokeRuleInfo struct {
	ActId                uint32   `protobuf:"varint,1,opt,name=act_id,json=actId,proto3" json:"act_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	StayTs               uint32   `protobuf:"varint,3,opt,name=stay_ts,json=stayTs,proto3" json:"stay_ts,omitempty"`
	CokeNum              uint32   `protobuf:"varint,4,opt,name=coke_num,json=cokeNum,proto3" json:"coke_num,omitempty"`
	VoteNum              uint32   `protobuf:"varint,5,opt,name=vote_num,json=voteNum,proto3" json:"vote_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CokeRuleInfo) Reset()         { *m = CokeRuleInfo{} }
func (m *CokeRuleInfo) String() string { return proto.CompactTextString(m) }
func (*CokeRuleInfo) ProtoMessage()    {}
func (*CokeRuleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{81}
}
func (m *CokeRuleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CokeRuleInfo.Unmarshal(m, b)
}
func (m *CokeRuleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CokeRuleInfo.Marshal(b, m, deterministic)
}
func (dst *CokeRuleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CokeRuleInfo.Merge(dst, src)
}
func (m *CokeRuleInfo) XXX_Size() int {
	return xxx_messageInfo_CokeRuleInfo.Size(m)
}
func (m *CokeRuleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CokeRuleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CokeRuleInfo proto.InternalMessageInfo

func (m *CokeRuleInfo) GetActId() uint32 {
	if m != nil {
		return m.ActId
	}
	return 0
}

func (m *CokeRuleInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CokeRuleInfo) GetStayTs() uint32 {
	if m != nil {
		return m.StayTs
	}
	return 0
}

func (m *CokeRuleInfo) GetCokeNum() uint32 {
	if m != nil {
		return m.CokeNum
	}
	return 0
}

func (m *CokeRuleInfo) GetVoteNum() uint32 {
	if m != nil {
		return m.VoteNum
	}
	return 0
}

type CokeAct struct {
	CokeRuleInfo         *CokeRuleInfo `protobuf:"bytes,1,opt,name=coke_rule_info,json=cokeRuleInfo,proto3" json:"coke_rule_info,omitempty"`
	IsOpenCoke           bool          `protobuf:"varint,2,opt,name=is_open_coke,json=isOpenCoke,proto3" json:"is_open_coke,omitempty"`
	LeftCoke             uint32        `protobuf:"varint,3,opt,name=left_coke,json=leftCoke,proto3" json:"left_coke,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CokeAct) Reset()         { *m = CokeAct{} }
func (m *CokeAct) String() string { return proto.CompactTextString(m) }
func (*CokeAct) ProtoMessage()    {}
func (*CokeAct) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{82}
}
func (m *CokeAct) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CokeAct.Unmarshal(m, b)
}
func (m *CokeAct) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CokeAct.Marshal(b, m, deterministic)
}
func (dst *CokeAct) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CokeAct.Merge(dst, src)
}
func (m *CokeAct) XXX_Size() int {
	return xxx_messageInfo_CokeAct.Size(m)
}
func (m *CokeAct) XXX_DiscardUnknown() {
	xxx_messageInfo_CokeAct.DiscardUnknown(m)
}

var xxx_messageInfo_CokeAct proto.InternalMessageInfo

func (m *CokeAct) GetCokeRuleInfo() *CokeRuleInfo {
	if m != nil {
		return m.CokeRuleInfo
	}
	return nil
}

func (m *CokeAct) GetIsOpenCoke() bool {
	if m != nil {
		return m.IsOpenCoke
	}
	return false
}

func (m *CokeAct) GetLeftCoke() uint32 {
	if m != nil {
		return m.LeftCoke
	}
	return 0
}

type CokeStateInfo struct {
	StartFlag            bool     `protobuf:"varint,1,opt,name=startFlag,proto3" json:"startFlag,omitempty"`
	StayTs               uint32   `protobuf:"varint,2,opt,name=stay_ts,json=stayTs,proto3" json:"stay_ts,omitempty"`
	CokeNum              uint32   `protobuf:"varint,3,opt,name=coke_num,json=cokeNum,proto3" json:"coke_num,omitempty"`
	VoteNum              uint32   `protobuf:"varint,4,opt,name=vote_num,json=voteNum,proto3" json:"vote_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CokeStateInfo) Reset()         { *m = CokeStateInfo{} }
func (m *CokeStateInfo) String() string { return proto.CompactTextString(m) }
func (*CokeStateInfo) ProtoMessage()    {}
func (*CokeStateInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{83}
}
func (m *CokeStateInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CokeStateInfo.Unmarshal(m, b)
}
func (m *CokeStateInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CokeStateInfo.Marshal(b, m, deterministic)
}
func (dst *CokeStateInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CokeStateInfo.Merge(dst, src)
}
func (m *CokeStateInfo) XXX_Size() int {
	return xxx_messageInfo_CokeStateInfo.Size(m)
}
func (m *CokeStateInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CokeStateInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CokeStateInfo proto.InternalMessageInfo

func (m *CokeStateInfo) GetStartFlag() bool {
	if m != nil {
		return m.StartFlag
	}
	return false
}

func (m *CokeStateInfo) GetStayTs() uint32 {
	if m != nil {
		return m.StayTs
	}
	return 0
}

func (m *CokeStateInfo) GetCokeNum() uint32 {
	if m != nil {
		return m.CokeNum
	}
	return 0
}

func (m *CokeStateInfo) GetVoteNum() uint32 {
	if m != nil {
		return m.VoteNum
	}
	return 0
}

type RandomGetRobotImageReq struct {
	Limit                int64    `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RandomGetRobotImageReq) Reset()         { *m = RandomGetRobotImageReq{} }
func (m *RandomGetRobotImageReq) String() string { return proto.CompactTextString(m) }
func (*RandomGetRobotImageReq) ProtoMessage()    {}
func (*RandomGetRobotImageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{84}
}
func (m *RandomGetRobotImageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RandomGetRobotImageReq.Unmarshal(m, b)
}
func (m *RandomGetRobotImageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RandomGetRobotImageReq.Marshal(b, m, deterministic)
}
func (dst *RandomGetRobotImageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RandomGetRobotImageReq.Merge(dst, src)
}
func (m *RandomGetRobotImageReq) XXX_Size() int {
	return xxx_messageInfo_RandomGetRobotImageReq.Size(m)
}
func (m *RandomGetRobotImageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RandomGetRobotImageReq.DiscardUnknown(m)
}

var xxx_messageInfo_RandomGetRobotImageReq proto.InternalMessageInfo

func (m *RandomGetRobotImageReq) GetLimit() int64 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type RandomGetRobotImageResp struct {
	ImageUrl             []string `protobuf:"bytes,1,rep,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RandomGetRobotImageResp) Reset()         { *m = RandomGetRobotImageResp{} }
func (m *RandomGetRobotImageResp) String() string { return proto.CompactTextString(m) }
func (*RandomGetRobotImageResp) ProtoMessage()    {}
func (*RandomGetRobotImageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_nest_da1d8e283f0abeb5, []int{85}
}
func (m *RandomGetRobotImageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RandomGetRobotImageResp.Unmarshal(m, b)
}
func (m *RandomGetRobotImageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RandomGetRobotImageResp.Marshal(b, m, deterministic)
}
func (dst *RandomGetRobotImageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RandomGetRobotImageResp.Merge(dst, src)
}
func (m *RandomGetRobotImageResp) XXX_Size() int {
	return xxx_messageInfo_RandomGetRobotImageResp.Size(m)
}
func (m *RandomGetRobotImageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RandomGetRobotImageResp.DiscardUnknown(m)
}

var xxx_messageInfo_RandomGetRobotImageResp proto.InternalMessageInfo

func (m *RandomGetRobotImageResp) GetImageUrl() []string {
	if m != nil {
		return m.ImageUrl
	}
	return nil
}

func init() {
	proto.RegisterType((*IsMusicNestActChannelReq)(nil), "music_nest.IsMusicNestActChannelReq")
	proto.RegisterType((*IsMusicNestActChannelResp)(nil), "music_nest.IsMusicNestActChannelResp")
	proto.RegisterType((*CategoryInfo)(nil), "music_nest.CategoryInfo")
	proto.RegisterType((*GuestInfo)(nil), "music_nest.GuestInfo")
	proto.RegisterType((*GuestInfos)(nil), "music_nest.GuestInfos")
	proto.RegisterType((*GuestInfoWithTTId)(nil), "music_nest.GuestInfoWithTTId")
	proto.RegisterType((*GuestInfosWithTTId)(nil), "music_nest.GuestInfosWithTTId")
	proto.RegisterType((*MusicInfo)(nil), "music_nest.MusicInfo")
	proto.RegisterType((*ReviewInfo)(nil), "music_nest.ReviewInfo")
	proto.RegisterType((*ActivityBaseInfo)(nil), "music_nest.ActivityBaseInfo")
	proto.RegisterType((*ActivityTopicInfo)(nil), "music_nest.ActivityTopicInfo")
	proto.RegisterType((*CreateCategoryReq)(nil), "music_nest.CreateCategoryReq")
	proto.RegisterType((*CreateCategoryResp)(nil), "music_nest.CreateCategoryResp")
	proto.RegisterType((*UpdateCategoryReq)(nil), "music_nest.UpdateCategoryReq")
	proto.RegisterType((*UpdateCategoryResp)(nil), "music_nest.UpdateCategoryResp")
	proto.RegisterType((*GetAllCategoryReq)(nil), "music_nest.GetAllCategoryReq")
	proto.RegisterType((*GetAllCategoryResp)(nil), "music_nest.GetAllCategoryResp")
	proto.RegisterType((*MoveCategoryReq)(nil), "music_nest.MoveCategoryReq")
	proto.RegisterType((*MoveCategoryResp)(nil), "music_nest.MoveCategoryResp")
	proto.RegisterType((*CreatActivityReq)(nil), "music_nest.CreatActivityReq")
	proto.RegisterType((*CreatActivityResp)(nil), "music_nest.CreatActivityResp")
	proto.RegisterType((*UpdateActivityReq)(nil), "music_nest.UpdateActivityReq")
	proto.RegisterType((*UpdateActivityResp)(nil), "music_nest.UpdateActivityResp")
	proto.RegisterType((*WelcomePop)(nil), "music_nest.WelcomePop")
	proto.RegisterType((*UpdateActivityTopicReq)(nil), "music_nest.UpdateActivityTopicReq")
	proto.RegisterType((*GetAllStayAddTicketActsReq)(nil), "music_nest.GetAllStayAddTicketActsReq")
	proto.RegisterType((*StayAddTicketActsInfo)(nil), "music_nest.StayAddTicketActsInfo")
	proto.RegisterType((*GetAllStayAddTicketActsResp)(nil), "music_nest.GetAllStayAddTicketActsResp")
	proto.RegisterType((*BrandIntegralInfo)(nil), "music_nest.BrandIntegralInfo")
	proto.RegisterType((*BrandInfo)(nil), "music_nest.BrandInfo")
	proto.RegisterType((*UpdateActivityTopicResp)(nil), "music_nest.UpdateActivityTopicResp")
	proto.RegisterType((*UpdateMusicListInfoReq)(nil), "music_nest.UpdateMusicListInfoReq")
	proto.RegisterType((*UpdateMusicListInfoResp)(nil), "music_nest.UpdateMusicListInfoResp")
	proto.RegisterType((*UpdateActivityReviewReq)(nil), "music_nest.UpdateActivityReviewReq")
	proto.RegisterType((*UpdateActivityReviewResp)(nil), "music_nest.UpdateActivityReviewResp")
	proto.RegisterType((*GetActivityListReq)(nil), "music_nest.GetActivityListReq")
	proto.RegisterType((*GetActivityListResp)(nil), "music_nest.GetActivityListResp")
	proto.RegisterType((*GetActivityListResp_AllActivityInfo)(nil), "music_nest.GetActivityListResp.AllActivityInfo")
	proto.RegisterType((*GetMyMusicNestListReq)(nil), "music_nest.GetMyMusicNestListReq")
	proto.RegisterType((*GetMyMusicNestListResp)(nil), "music_nest.GetMyMusicNestListResp")
	proto.RegisterType((*MyMusicNestActivity)(nil), "music_nest.MyMusicNestActivity")
	proto.RegisterType((*GetMusicNestActivityInfoReq)(nil), "music_nest.GetMusicNestActivityInfoReq")
	proto.RegisterType((*GetMusicNestActivityInfoResp)(nil), "music_nest.GetMusicNestActivityInfoResp")
	proto.RegisterType((*Director)(nil), "music_nest.Director")
	proto.RegisterType((*SimpleMusicNestActivity)(nil), "music_nest.SimpleMusicNestActivity")
	proto.RegisterType((*PlayTogether)(nil), "music_nest.PlayTogether")
	proto.RegisterType((*GetMusicNestCoverAndLiveListReq)(nil), "music_nest.GetMusicNestCoverAndLiveListReq")
	proto.RegisterType((*GetMusicNestCoverAndLiveListResp)(nil), "music_nest.GetMusicNestCoverAndLiveListResp")
	proto.RegisterType((*GetMusicNestCoverAndLiveListResp_CoverInfo)(nil), "music_nest.GetMusicNestCoverAndLiveListResp.CoverInfo")
	proto.RegisterType((*GetMusicNestCoverAndLiveListResp_LiveInfo)(nil), "music_nest.GetMusicNestCoverAndLiveListResp.LiveInfo")
	proto.RegisterType((*GetMusicNestCoverAndLiveListResp_LiveInfo_SongInfo)(nil), "music_nest.GetMusicNestCoverAndLiveListResp.LiveInfo.SongInfo")
	proto.RegisterType((*GetMusicNestHomePageReq)(nil), "music_nest.GetMusicNestHomePageReq")
	proto.RegisterType((*GetMusicNestHomePageResp)(nil), "music_nest.GetMusicNestHomePageResp")
	proto.RegisterType((*GetMusicNestHomePageResp_ActivityInfo)(nil), "music_nest.GetMusicNestHomePageResp.ActivityInfo")
	proto.RegisterType((*GetMusicNestHomePageResp_CategoryActivityInfo)(nil), "music_nest.GetMusicNestHomePageResp.CategoryActivityInfo")
	proto.RegisterType((*SubMusicNestReq)(nil), "music_nest.SubMusicNestReq")
	proto.RegisterType((*SubMusicNestResp)(nil), "music_nest.SubMusicNestResp")
	proto.RegisterType((*SubMusicNestActivityReq)(nil), "music_nest.SubMusicNestActivityReq")
	proto.RegisterType((*SubMusicNestActivityResp)(nil), "music_nest.SubMusicNestActivityResp")
	proto.RegisterType((*Guest)(nil), "music_nest.Guest")
	proto.RegisterType((*Guest_UserId)(nil), "music_nest.Guest.UserId")
	proto.RegisterType((*Stage)(nil), "music_nest.Stage")
	proto.RegisterType((*Performance)(nil), "music_nest.Performance")
	proto.RegisterType((*AddPerformanceReq)(nil), "music_nest.AddPerformanceReq")
	proto.RegisterType((*AddPerformanceResp)(nil), "music_nest.AddPerformanceResp")
	proto.RegisterType((*StopPerformanceReq)(nil), "music_nest.StopPerformanceReq")
	proto.RegisterType((*StopPerformanceResp)(nil), "music_nest.StopPerformanceResp")
	proto.RegisterType((*GetPerformanceReq)(nil), "music_nest.GetPerformanceReq")
	proto.RegisterType((*NextDirectionAct)(nil), "music_nest.NextDirectionAct")
	proto.RegisterType((*GetPerformanceResp)(nil), "music_nest.GetPerformanceResp")
	proto.RegisterType((*GetPerformanceByIdReq)(nil), "music_nest.GetPerformanceByIdReq")
	proto.RegisterType((*GetPerformanceByIdResp)(nil), "music_nest.GetPerformanceByIdResp")
	proto.RegisterType((*SetCurrentPerformanceStageReq)(nil), "music_nest.SetCurrentPerformanceStageReq")
	proto.RegisterType((*SetCurrentPerformanceStageResp)(nil), "music_nest.SetCurrentPerformanceStageResp")
	proto.RegisterType((*DisappearNextDirectionActReq)(nil), "music_nest.DisappearNextDirectionActReq")
	proto.RegisterType((*DisappearNextDirectionActResp)(nil), "music_nest.DisappearNextDirectionActResp")
	proto.RegisterType((*VisitedInfo)(nil), "music_nest.VisitedInfo")
	proto.RegisterType((*GetSpecifiedChannelVisitedSizeReq)(nil), "music_nest.GetSpecifiedChannelVisitedSizeReq")
	proto.RegisterType((*GetSpecifiedChannelVisitedSizeResp)(nil), "music_nest.GetSpecifiedChannelVisitedSizeResp")
	proto.RegisterMapType((map[uint32]*VisitedInfo)(nil), "music_nest.GetSpecifiedChannelVisitedSizeResp.VisitedInfoListEntry")
	proto.RegisterType((*OptionStruct)(nil), "music_nest.OptionStruct")
	proto.RegisterType((*SendWelcomePop)(nil), "music_nest.SendWelcomePop")
	proto.RegisterType((*GetWelcomePopReq)(nil), "music_nest.GetWelcomePopReq")
	proto.RegisterType((*GetWelcomePopResp)(nil), "music_nest.GetWelcomePopResp")
	proto.RegisterType((*UserClickPopReq)(nil), "music_nest.UserClickPopReq")
	proto.RegisterType((*UserClickPopResp)(nil), "music_nest.UserClickPopResp")
	proto.RegisterType((*PushCokeStateInfo)(nil), "music_nest.PushCokeStateInfo")
	proto.RegisterType((*GetMusicNestLiveInfoReq)(nil), "music_nest.GetMusicNestLiveInfoReq")
	proto.RegisterType((*GetMusicNestLiveInfoResp)(nil), "music_nest.GetMusicNestLiveInfoResp")
	proto.RegisterType((*CokeRuleInfo)(nil), "music_nest.CokeRuleInfo")
	proto.RegisterType((*CokeAct)(nil), "music_nest.CokeAct")
	proto.RegisterType((*CokeStateInfo)(nil), "music_nest.CokeStateInfo")
	proto.RegisterType((*RandomGetRobotImageReq)(nil), "music_nest.RandomGetRobotImageReq")
	proto.RegisterType((*RandomGetRobotImageResp)(nil), "music_nest.RandomGetRobotImageResp")
	proto.RegisterEnum("music_nest.LevelType", LevelType_name, LevelType_value)
	proto.RegisterEnum("music_nest.LiveStatus", LiveStatus_name, LiveStatus_value)
	proto.RegisterEnum("music_nest.MaterialType", MaterialType_name, MaterialType_value)
	proto.RegisterEnum("music_nest.PatternType", PatternType_name, PatternType_value)
	proto.RegisterEnum("music_nest.BrandIntegralStatus", BrandIntegralStatus_name, BrandIntegralStatus_value)
	proto.RegisterEnum("music_nest.StageType", StageType_name, StageType_value)
	proto.RegisterEnum("music_nest.MusicNestPerformanceType", MusicNestPerformanceType_name, MusicNestPerformanceType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MusicNestClient is the client API for MusicNest service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MusicNestClient interface {
	// 运营平台 分类管理
	CreateCategory(ctx context.Context, in *CreateCategoryReq, opts ...grpc.CallOption) (*CreateCategoryResp, error)
	UpdateCategory(ctx context.Context, in *UpdateCategoryReq, opts ...grpc.CallOption) (*UpdateCategoryResp, error)
	GetAllCategory(ctx context.Context, in *GetAllCategoryReq, opts ...grpc.CallOption) (*GetAllCategoryResp, error)
	MoveCategory(ctx context.Context, in *MoveCategoryReq, opts ...grpc.CallOption) (*MoveCategoryResp, error)
	// 运营平台活动类操作相关
	CreatActivity(ctx context.Context, in *CreatActivityReq, opts ...grpc.CallOption) (*CreatActivityResp, error)
	UpdateActivity(ctx context.Context, in *UpdateActivityReq, opts ...grpc.CallOption) (*UpdateActivityResp, error)
	UpdateActivityTopic(ctx context.Context, in *UpdateActivityTopicReq, opts ...grpc.CallOption) (*UpdateActivityTopicResp, error)
	UpdateMusicListInfo(ctx context.Context, in *UpdateMusicListInfoReq, opts ...grpc.CallOption) (*UpdateMusicListInfoResp, error)
	UpdateActivityReview(ctx context.Context, in *UpdateActivityReviewReq, opts ...grpc.CallOption) (*UpdateActivityReviewResp, error)
	GetActivityList(ctx context.Context, in *GetActivityListReq, opts ...grpc.CallOption) (*GetActivityListResp, error)
	// web我的乐窝
	GetMyMusicNestList(ctx context.Context, in *GetMyMusicNestListReq, opts ...grpc.CallOption) (*GetMyMusicNestListResp, error)
	// web活动详情
	GetMusicNestActivityInfo(ctx context.Context, in *GetMusicNestActivityInfoReq, opts ...grpc.CallOption) (*GetMusicNestActivityInfoResp, error)
	// 音乐板块的乐窝首页
	GetMusicNestCoverAndLiveList(ctx context.Context, in *GetMusicNestCoverAndLiveListReq, opts ...grpc.CallOption) (*GetMusicNestCoverAndLiveListResp, error)
	// 乐窝主页
	GetMusicNestHomePage(ctx context.Context, in *GetMusicNestHomePageReq, opts ...grpc.CallOption) (*GetMusicNestHomePageResp, error)
	// 订阅乐窝
	SubMusicNest(ctx context.Context, in *SubMusicNestReq, opts ...grpc.CallOption) (*SubMusicNestResp, error)
	// 想作
	SubMusicNestActivity(ctx context.Context, in *SubMusicNestActivityReq, opts ...grpc.CallOption) (*SubMusicNestActivityResp, error)
	// 节目清单接口
	AddPerformance(ctx context.Context, in *AddPerformanceReq, opts ...grpc.CallOption) (*AddPerformanceResp, error)
	// 删除活动
	StopPerformance(ctx context.Context, in *StopPerformanceReq, opts ...grpc.CallOption) (*StopPerformanceResp, error)
	// 获取此房间所有节目单
	GetPerformance(ctx context.Context, in *GetPerformanceReq, opts ...grpc.CallOption) (*GetPerformanceResp, error)
	// 设置当前阶段
	SetCurrentPerformanceStage(ctx context.Context, in *SetCurrentPerformanceStageReq, opts ...grpc.CallOption) (*SetCurrentPerformanceStageResp, error)
	// 获取节目单
	GetPerformanceById(ctx context.Context, in *GetPerformanceByIdReq, opts ...grpc.CallOption) (*GetPerformanceByIdResp, error)
	// 下一场引导消失
	DisappearNextDirectionAct(ctx context.Context, in *DisappearNextDirectionActReq, opts ...grpc.CallOption) (*DisappearNextDirectionActResp, error)
	// 已看过获取
	GetSpecifiedChannelVisitedSize(ctx context.Context, in *GetSpecifiedChannelVisitedSizeReq, opts ...grpc.CallOption) (*GetSpecifiedChannelVisitedSizeResp, error)
	// 获取欢迎层弹层信息
	GetWelcomePop(ctx context.Context, in *GetWelcomePopReq, opts ...grpc.CallOption) (*GetWelcomePopResp, error)
	// 用户点击选择弹层某一选项消息
	UserClickPop(ctx context.Context, in *UserClickPopReq, opts ...grpc.CallOption) (*UserClickPopResp, error)
	IsMusicNestActChannel(ctx context.Context, in *IsMusicNestActChannelReq, opts ...grpc.CallOption) (*IsMusicNestActChannelResp, error)
	// // 进房后乐窝活动进行期间的信息获取 (和乐窝活动相关的汇总）
	GetMusicNestLiveInfo(ctx context.Context, in *GetMusicNestLiveInfoReq, opts ...grpc.CallOption) (*GetMusicNestLiveInfoResp, error)
	// 随机获取指定个数的机器人头像
	RandomGetRobotImage(ctx context.Context, in *RandomGetRobotImageReq, opts ...grpc.CallOption) (*RandomGetRobotImageResp, error)
	// 获取近期乐窝活动的加票停留信息
	GetAllStayAddTicketActs(ctx context.Context, in *GetAllStayAddTicketActsReq, opts ...grpc.CallOption) (*GetAllStayAddTicketActsResp, error)
}

type musicNestClient struct {
	cc *grpc.ClientConn
}

func NewMusicNestClient(cc *grpc.ClientConn) MusicNestClient {
	return &musicNestClient{cc}
}

func (c *musicNestClient) CreateCategory(ctx context.Context, in *CreateCategoryReq, opts ...grpc.CallOption) (*CreateCategoryResp, error) {
	out := new(CreateCategoryResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/CreateCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) UpdateCategory(ctx context.Context, in *UpdateCategoryReq, opts ...grpc.CallOption) (*UpdateCategoryResp, error) {
	out := new(UpdateCategoryResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/UpdateCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) GetAllCategory(ctx context.Context, in *GetAllCategoryReq, opts ...grpc.CallOption) (*GetAllCategoryResp, error) {
	out := new(GetAllCategoryResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/GetAllCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) MoveCategory(ctx context.Context, in *MoveCategoryReq, opts ...grpc.CallOption) (*MoveCategoryResp, error) {
	out := new(MoveCategoryResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/MoveCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) CreatActivity(ctx context.Context, in *CreatActivityReq, opts ...grpc.CallOption) (*CreatActivityResp, error) {
	out := new(CreatActivityResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/CreatActivity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) UpdateActivity(ctx context.Context, in *UpdateActivityReq, opts ...grpc.CallOption) (*UpdateActivityResp, error) {
	out := new(UpdateActivityResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/UpdateActivity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) UpdateActivityTopic(ctx context.Context, in *UpdateActivityTopicReq, opts ...grpc.CallOption) (*UpdateActivityTopicResp, error) {
	out := new(UpdateActivityTopicResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/UpdateActivityTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) UpdateMusicListInfo(ctx context.Context, in *UpdateMusicListInfoReq, opts ...grpc.CallOption) (*UpdateMusicListInfoResp, error) {
	out := new(UpdateMusicListInfoResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/UpdateMusicListInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) UpdateActivityReview(ctx context.Context, in *UpdateActivityReviewReq, opts ...grpc.CallOption) (*UpdateActivityReviewResp, error) {
	out := new(UpdateActivityReviewResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/UpdateActivityReview", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) GetActivityList(ctx context.Context, in *GetActivityListReq, opts ...grpc.CallOption) (*GetActivityListResp, error) {
	out := new(GetActivityListResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/GetActivityList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) GetMyMusicNestList(ctx context.Context, in *GetMyMusicNestListReq, opts ...grpc.CallOption) (*GetMyMusicNestListResp, error) {
	out := new(GetMyMusicNestListResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/GetMyMusicNestList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) GetMusicNestActivityInfo(ctx context.Context, in *GetMusicNestActivityInfoReq, opts ...grpc.CallOption) (*GetMusicNestActivityInfoResp, error) {
	out := new(GetMusicNestActivityInfoResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/GetMusicNestActivityInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) GetMusicNestCoverAndLiveList(ctx context.Context, in *GetMusicNestCoverAndLiveListReq, opts ...grpc.CallOption) (*GetMusicNestCoverAndLiveListResp, error) {
	out := new(GetMusicNestCoverAndLiveListResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/GetMusicNestCoverAndLiveList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) GetMusicNestHomePage(ctx context.Context, in *GetMusicNestHomePageReq, opts ...grpc.CallOption) (*GetMusicNestHomePageResp, error) {
	out := new(GetMusicNestHomePageResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/GetMusicNestHomePage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) SubMusicNest(ctx context.Context, in *SubMusicNestReq, opts ...grpc.CallOption) (*SubMusicNestResp, error) {
	out := new(SubMusicNestResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/SubMusicNest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) SubMusicNestActivity(ctx context.Context, in *SubMusicNestActivityReq, opts ...grpc.CallOption) (*SubMusicNestActivityResp, error) {
	out := new(SubMusicNestActivityResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/SubMusicNestActivity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) AddPerformance(ctx context.Context, in *AddPerformanceReq, opts ...grpc.CallOption) (*AddPerformanceResp, error) {
	out := new(AddPerformanceResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/AddPerformance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) StopPerformance(ctx context.Context, in *StopPerformanceReq, opts ...grpc.CallOption) (*StopPerformanceResp, error) {
	out := new(StopPerformanceResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/StopPerformance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) GetPerformance(ctx context.Context, in *GetPerformanceReq, opts ...grpc.CallOption) (*GetPerformanceResp, error) {
	out := new(GetPerformanceResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/GetPerformance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) SetCurrentPerformanceStage(ctx context.Context, in *SetCurrentPerformanceStageReq, opts ...grpc.CallOption) (*SetCurrentPerformanceStageResp, error) {
	out := new(SetCurrentPerformanceStageResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/SetCurrentPerformanceStage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) GetPerformanceById(ctx context.Context, in *GetPerformanceByIdReq, opts ...grpc.CallOption) (*GetPerformanceByIdResp, error) {
	out := new(GetPerformanceByIdResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/GetPerformanceById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) DisappearNextDirectionAct(ctx context.Context, in *DisappearNextDirectionActReq, opts ...grpc.CallOption) (*DisappearNextDirectionActResp, error) {
	out := new(DisappearNextDirectionActResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/DisappearNextDirectionAct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) GetSpecifiedChannelVisitedSize(ctx context.Context, in *GetSpecifiedChannelVisitedSizeReq, opts ...grpc.CallOption) (*GetSpecifiedChannelVisitedSizeResp, error) {
	out := new(GetSpecifiedChannelVisitedSizeResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/GetSpecifiedChannelVisitedSize", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) GetWelcomePop(ctx context.Context, in *GetWelcomePopReq, opts ...grpc.CallOption) (*GetWelcomePopResp, error) {
	out := new(GetWelcomePopResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/GetWelcomePop", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) UserClickPop(ctx context.Context, in *UserClickPopReq, opts ...grpc.CallOption) (*UserClickPopResp, error) {
	out := new(UserClickPopResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/UserClickPop", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) IsMusicNestActChannel(ctx context.Context, in *IsMusicNestActChannelReq, opts ...grpc.CallOption) (*IsMusicNestActChannelResp, error) {
	out := new(IsMusicNestActChannelResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/IsMusicNestActChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) GetMusicNestLiveInfo(ctx context.Context, in *GetMusicNestLiveInfoReq, opts ...grpc.CallOption) (*GetMusicNestLiveInfoResp, error) {
	out := new(GetMusicNestLiveInfoResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/GetMusicNestLiveInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) RandomGetRobotImage(ctx context.Context, in *RandomGetRobotImageReq, opts ...grpc.CallOption) (*RandomGetRobotImageResp, error) {
	out := new(RandomGetRobotImageResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/RandomGetRobotImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicNestClient) GetAllStayAddTicketActs(ctx context.Context, in *GetAllStayAddTicketActsReq, opts ...grpc.CallOption) (*GetAllStayAddTicketActsResp, error) {
	out := new(GetAllStayAddTicketActsResp)
	err := c.cc.Invoke(ctx, "/music_nest.MusicNest/GetAllStayAddTicketActs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MusicNestServer is the server API for MusicNest service.
type MusicNestServer interface {
	// 运营平台 分类管理
	CreateCategory(context.Context, *CreateCategoryReq) (*CreateCategoryResp, error)
	UpdateCategory(context.Context, *UpdateCategoryReq) (*UpdateCategoryResp, error)
	GetAllCategory(context.Context, *GetAllCategoryReq) (*GetAllCategoryResp, error)
	MoveCategory(context.Context, *MoveCategoryReq) (*MoveCategoryResp, error)
	// 运营平台活动类操作相关
	CreatActivity(context.Context, *CreatActivityReq) (*CreatActivityResp, error)
	UpdateActivity(context.Context, *UpdateActivityReq) (*UpdateActivityResp, error)
	UpdateActivityTopic(context.Context, *UpdateActivityTopicReq) (*UpdateActivityTopicResp, error)
	UpdateMusicListInfo(context.Context, *UpdateMusicListInfoReq) (*UpdateMusicListInfoResp, error)
	UpdateActivityReview(context.Context, *UpdateActivityReviewReq) (*UpdateActivityReviewResp, error)
	GetActivityList(context.Context, *GetActivityListReq) (*GetActivityListResp, error)
	// web我的乐窝
	GetMyMusicNestList(context.Context, *GetMyMusicNestListReq) (*GetMyMusicNestListResp, error)
	// web活动详情
	GetMusicNestActivityInfo(context.Context, *GetMusicNestActivityInfoReq) (*GetMusicNestActivityInfoResp, error)
	// 音乐板块的乐窝首页
	GetMusicNestCoverAndLiveList(context.Context, *GetMusicNestCoverAndLiveListReq) (*GetMusicNestCoverAndLiveListResp, error)
	// 乐窝主页
	GetMusicNestHomePage(context.Context, *GetMusicNestHomePageReq) (*GetMusicNestHomePageResp, error)
	// 订阅乐窝
	SubMusicNest(context.Context, *SubMusicNestReq) (*SubMusicNestResp, error)
	// 想作
	SubMusicNestActivity(context.Context, *SubMusicNestActivityReq) (*SubMusicNestActivityResp, error)
	// 节目清单接口
	AddPerformance(context.Context, *AddPerformanceReq) (*AddPerformanceResp, error)
	// 删除活动
	StopPerformance(context.Context, *StopPerformanceReq) (*StopPerformanceResp, error)
	// 获取此房间所有节目单
	GetPerformance(context.Context, *GetPerformanceReq) (*GetPerformanceResp, error)
	// 设置当前阶段
	SetCurrentPerformanceStage(context.Context, *SetCurrentPerformanceStageReq) (*SetCurrentPerformanceStageResp, error)
	// 获取节目单
	GetPerformanceById(context.Context, *GetPerformanceByIdReq) (*GetPerformanceByIdResp, error)
	// 下一场引导消失
	DisappearNextDirectionAct(context.Context, *DisappearNextDirectionActReq) (*DisappearNextDirectionActResp, error)
	// 已看过获取
	GetSpecifiedChannelVisitedSize(context.Context, *GetSpecifiedChannelVisitedSizeReq) (*GetSpecifiedChannelVisitedSizeResp, error)
	// 获取欢迎层弹层信息
	GetWelcomePop(context.Context, *GetWelcomePopReq) (*GetWelcomePopResp, error)
	// 用户点击选择弹层某一选项消息
	UserClickPop(context.Context, *UserClickPopReq) (*UserClickPopResp, error)
	IsMusicNestActChannel(context.Context, *IsMusicNestActChannelReq) (*IsMusicNestActChannelResp, error)
	// // 进房后乐窝活动进行期间的信息获取 (和乐窝活动相关的汇总）
	GetMusicNestLiveInfo(context.Context, *GetMusicNestLiveInfoReq) (*GetMusicNestLiveInfoResp, error)
	// 随机获取指定个数的机器人头像
	RandomGetRobotImage(context.Context, *RandomGetRobotImageReq) (*RandomGetRobotImageResp, error)
	// 获取近期乐窝活动的加票停留信息
	GetAllStayAddTicketActs(context.Context, *GetAllStayAddTicketActsReq) (*GetAllStayAddTicketActsResp, error)
}

func RegisterMusicNestServer(s *grpc.Server, srv MusicNestServer) {
	s.RegisterService(&_MusicNest_serviceDesc, srv)
}

func _MusicNest_CreateCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).CreateCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/CreateCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).CreateCategory(ctx, req.(*CreateCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_UpdateCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).UpdateCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/UpdateCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).UpdateCategory(ctx, req.(*UpdateCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_GetAllCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).GetAllCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/GetAllCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).GetAllCategory(ctx, req.(*GetAllCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_MoveCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MoveCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).MoveCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/MoveCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).MoveCategory(ctx, req.(*MoveCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_CreatActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatActivityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).CreatActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/CreatActivity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).CreatActivity(ctx, req.(*CreatActivityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_UpdateActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateActivityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).UpdateActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/UpdateActivity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).UpdateActivity(ctx, req.(*UpdateActivityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_UpdateActivityTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateActivityTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).UpdateActivityTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/UpdateActivityTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).UpdateActivityTopic(ctx, req.(*UpdateActivityTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_UpdateMusicListInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMusicListInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).UpdateMusicListInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/UpdateMusicListInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).UpdateMusicListInfo(ctx, req.(*UpdateMusicListInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_UpdateActivityReview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateActivityReviewReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).UpdateActivityReview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/UpdateActivityReview",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).UpdateActivityReview(ctx, req.(*UpdateActivityReviewReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_GetActivityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActivityListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).GetActivityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/GetActivityList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).GetActivityList(ctx, req.(*GetActivityListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_GetMyMusicNestList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMyMusicNestListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).GetMyMusicNestList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/GetMyMusicNestList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).GetMyMusicNestList(ctx, req.(*GetMyMusicNestListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_GetMusicNestActivityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMusicNestActivityInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).GetMusicNestActivityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/GetMusicNestActivityInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).GetMusicNestActivityInfo(ctx, req.(*GetMusicNestActivityInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_GetMusicNestCoverAndLiveList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMusicNestCoverAndLiveListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).GetMusicNestCoverAndLiveList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/GetMusicNestCoverAndLiveList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).GetMusicNestCoverAndLiveList(ctx, req.(*GetMusicNestCoverAndLiveListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_GetMusicNestHomePage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMusicNestHomePageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).GetMusicNestHomePage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/GetMusicNestHomePage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).GetMusicNestHomePage(ctx, req.(*GetMusicNestHomePageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_SubMusicNest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubMusicNestReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).SubMusicNest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/SubMusicNest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).SubMusicNest(ctx, req.(*SubMusicNestReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_SubMusicNestActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubMusicNestActivityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).SubMusicNestActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/SubMusicNestActivity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).SubMusicNestActivity(ctx, req.(*SubMusicNestActivityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_AddPerformance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPerformanceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).AddPerformance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/AddPerformance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).AddPerformance(ctx, req.(*AddPerformanceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_StopPerformance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopPerformanceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).StopPerformance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/StopPerformance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).StopPerformance(ctx, req.(*StopPerformanceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_GetPerformance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPerformanceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).GetPerformance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/GetPerformance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).GetPerformance(ctx, req.(*GetPerformanceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_SetCurrentPerformanceStage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCurrentPerformanceStageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).SetCurrentPerformanceStage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/SetCurrentPerformanceStage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).SetCurrentPerformanceStage(ctx, req.(*SetCurrentPerformanceStageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_GetPerformanceById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPerformanceByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).GetPerformanceById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/GetPerformanceById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).GetPerformanceById(ctx, req.(*GetPerformanceByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_DisappearNextDirectionAct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisappearNextDirectionActReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).DisappearNextDirectionAct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/DisappearNextDirectionAct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).DisappearNextDirectionAct(ctx, req.(*DisappearNextDirectionActReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_GetSpecifiedChannelVisitedSize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSpecifiedChannelVisitedSizeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).GetSpecifiedChannelVisitedSize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/GetSpecifiedChannelVisitedSize",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).GetSpecifiedChannelVisitedSize(ctx, req.(*GetSpecifiedChannelVisitedSizeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_GetWelcomePop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWelcomePopReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).GetWelcomePop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/GetWelcomePop",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).GetWelcomePop(ctx, req.(*GetWelcomePopReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_UserClickPop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserClickPopReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).UserClickPop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/UserClickPop",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).UserClickPop(ctx, req.(*UserClickPopReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_IsMusicNestActChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsMusicNestActChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).IsMusicNestActChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/IsMusicNestActChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).IsMusicNestActChannel(ctx, req.(*IsMusicNestActChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_GetMusicNestLiveInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMusicNestLiveInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).GetMusicNestLiveInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/GetMusicNestLiveInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).GetMusicNestLiveInfo(ctx, req.(*GetMusicNestLiveInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_RandomGetRobotImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RandomGetRobotImageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).RandomGetRobotImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/RandomGetRobotImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).RandomGetRobotImage(ctx, req.(*RandomGetRobotImageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicNest_GetAllStayAddTicketActs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllStayAddTicketActsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicNestServer).GetAllStayAddTicketActs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_nest.MusicNest/GetAllStayAddTicketActs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicNestServer).GetAllStayAddTicketActs(ctx, req.(*GetAllStayAddTicketActsReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MusicNest_serviceDesc = grpc.ServiceDesc{
	ServiceName: "music_nest.MusicNest",
	HandlerType: (*MusicNestServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateCategory",
			Handler:    _MusicNest_CreateCategory_Handler,
		},
		{
			MethodName: "UpdateCategory",
			Handler:    _MusicNest_UpdateCategory_Handler,
		},
		{
			MethodName: "GetAllCategory",
			Handler:    _MusicNest_GetAllCategory_Handler,
		},
		{
			MethodName: "MoveCategory",
			Handler:    _MusicNest_MoveCategory_Handler,
		},
		{
			MethodName: "CreatActivity",
			Handler:    _MusicNest_CreatActivity_Handler,
		},
		{
			MethodName: "UpdateActivity",
			Handler:    _MusicNest_UpdateActivity_Handler,
		},
		{
			MethodName: "UpdateActivityTopic",
			Handler:    _MusicNest_UpdateActivityTopic_Handler,
		},
		{
			MethodName: "UpdateMusicListInfo",
			Handler:    _MusicNest_UpdateMusicListInfo_Handler,
		},
		{
			MethodName: "UpdateActivityReview",
			Handler:    _MusicNest_UpdateActivityReview_Handler,
		},
		{
			MethodName: "GetActivityList",
			Handler:    _MusicNest_GetActivityList_Handler,
		},
		{
			MethodName: "GetMyMusicNestList",
			Handler:    _MusicNest_GetMyMusicNestList_Handler,
		},
		{
			MethodName: "GetMusicNestActivityInfo",
			Handler:    _MusicNest_GetMusicNestActivityInfo_Handler,
		},
		{
			MethodName: "GetMusicNestCoverAndLiveList",
			Handler:    _MusicNest_GetMusicNestCoverAndLiveList_Handler,
		},
		{
			MethodName: "GetMusicNestHomePage",
			Handler:    _MusicNest_GetMusicNestHomePage_Handler,
		},
		{
			MethodName: "SubMusicNest",
			Handler:    _MusicNest_SubMusicNest_Handler,
		},
		{
			MethodName: "SubMusicNestActivity",
			Handler:    _MusicNest_SubMusicNestActivity_Handler,
		},
		{
			MethodName: "AddPerformance",
			Handler:    _MusicNest_AddPerformance_Handler,
		},
		{
			MethodName: "StopPerformance",
			Handler:    _MusicNest_StopPerformance_Handler,
		},
		{
			MethodName: "GetPerformance",
			Handler:    _MusicNest_GetPerformance_Handler,
		},
		{
			MethodName: "SetCurrentPerformanceStage",
			Handler:    _MusicNest_SetCurrentPerformanceStage_Handler,
		},
		{
			MethodName: "GetPerformanceById",
			Handler:    _MusicNest_GetPerformanceById_Handler,
		},
		{
			MethodName: "DisappearNextDirectionAct",
			Handler:    _MusicNest_DisappearNextDirectionAct_Handler,
		},
		{
			MethodName: "GetSpecifiedChannelVisitedSize",
			Handler:    _MusicNest_GetSpecifiedChannelVisitedSize_Handler,
		},
		{
			MethodName: "GetWelcomePop",
			Handler:    _MusicNest_GetWelcomePop_Handler,
		},
		{
			MethodName: "UserClickPop",
			Handler:    _MusicNest_UserClickPop_Handler,
		},
		{
			MethodName: "IsMusicNestActChannel",
			Handler:    _MusicNest_IsMusicNestActChannel_Handler,
		},
		{
			MethodName: "GetMusicNestLiveInfo",
			Handler:    _MusicNest_GetMusicNestLiveInfo_Handler,
		},
		{
			MethodName: "RandomGetRobotImage",
			Handler:    _MusicNest_RandomGetRobotImage_Handler,
		},
		{
			MethodName: "GetAllStayAddTicketActs",
			Handler:    _MusicNest_GetAllStayAddTicketActs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "music-nest/music-nest.proto",
}

func init() {
	proto.RegisterFile("music-nest/music-nest.proto", fileDescriptor_music_nest_da1d8e283f0abeb5)
}

var fileDescriptor_music_nest_da1d8e283f0abeb5 = []byte{
	// 5027 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x3c, 0x4d, 0x6f, 0x23, 0x47,
	0x76, 0x22, 0x29, 0x51, 0xe4, 0x23, 0x29, 0x51, 0xa5, 0x2f, 0x0e, 0x3d, 0x9f, 0x3d, 0xf1, 0xce,
	0x78, 0x66, 0x2d, 0x8f, 0x67, 0xbc, 0xe3, 0xd8, 0x59, 0xdb, 0xe0, 0x68, 0xe4, 0x31, 0x3d, 0x12,
	0xa5, 0x6d, 0x51, 0x33, 0xde, 0x0d, 0x9c, 0x4e, 0xab, 0x59, 0xe2, 0xf4, 0x8a, 0xec, 0xee, 0xe9,
	0x6e, 0x6a, 0x46, 0x4e, 0x10, 0x24, 0x08, 0x02, 0x04, 0x01, 0x02, 0x04, 0xd8, 0x1c, 0x12, 0x20,
	0xd7, 0xbd, 0x04, 0xb9, 0xe4, 0xb0, 0xd7, 0x5c, 0x72, 0x0c, 0x72, 0xcd, 0x07, 0xf2, 0x03, 0x72,
	0x0d, 0x72, 0x4b, 0x4e, 0x41, 0xbd, 0xaa, 0x6e, 0x56, 0x75, 0x37, 0x29, 0x8e, 0xed, 0x45, 0x6e,
	0x5d, 0xaf, 0x5e, 0x55, 0xbd, 0xaa, 0x7a, 0xdf, 0xaf, 0x48, 0x78, 0x6b, 0x38, 0x0a, 0x6c, 0xeb,
	0x5d, 0x87, 0x06, 0xe1, 0x7b, 0xe3, 0xcf, 0x2d, 0xcf, 0x77, 0x43, 0x97, 0x00, 0x42, 0x0c, 0x06,
	0xd1, 0x3e, 0x82, 0x46, 0x3b, 0xd8, 0x63, 0xed, 0x0e, 0x0d, 0xc2, 0x96, 0x15, 0x6e, 0xbf, 0x30,
	0x1d, 0x87, 0x0e, 0x74, 0xfa, 0x92, 0x5c, 0x01, 0xb0, 0x78, 0xcb, 0xb0, 0x7b, 0x8d, 0xdc, 0xf5,
	0xdc, 0xed, 0x9a, 0x5e, 0x16, 0x90, 0x76, 0x4f, 0xfb, 0x10, 0x2e, 0x4d, 0x18, 0x1a, 0x78, 0xa4,
	0x09, 0x65, 0x3b, 0x30, 0x6c, 0xc7, 0x30, 0xad, 0xb0, 0x91, 0xbf, 0x9e, 0xbb, 0x5d, 0xd2, 0x17,
	0xed, 0xa0, 0xed, 0xb4, 0xac, 0x50, 0xfb, 0x65, 0x0e, 0xaa, 0xdb, 0x66, 0x48, 0xfb, 0xae, 0x7f,
	0xde, 0x76, 0x4e, 0x5c, 0xb2, 0x04, 0xf9, 0x78, 0x81, 0xbc, 0xdd, 0x63, 0x0b, 0x0f, 0x4d, 0xdb,
	0x31, 0x42, 0x3b, 0x1c, 0x50, 0x1c, 0x5d, 0xd6, 0xcb, 0x0c, 0xd2, 0x65, 0x00, 0xf2, 0x16, 0x94,
	0x83, 0xd1, 0xb1, 0xe8, 0x2d, 0x60, 0x6f, 0x29, 0x18, 0x1d, 0xf3, 0xce, 0x0d, 0x28, 0x06, 0xa1,
	0x19, 0x8e, 0x82, 0xc6, 0x3c, 0xae, 0x2a, 0x5a, 0x6c, 0x90, 0x3d, 0x34, 0xfb, 0xd4, 0x18, 0xf9,
	0x83, 0xc6, 0x02, 0x1f, 0x84, 0x80, 0x23, 0x7f, 0x40, 0xd6, 0x60, 0xc1, 0x76, 0x7a, 0xf4, 0x75,
	0xa3, 0x88, 0x34, 0xf0, 0x86, 0xf6, 0xc7, 0x39, 0x28, 0x3f, 0x19, 0xd1, 0x20, 0x44, 0x22, 0x37,
	0x61, 0xd1, 0x0e, 0x0c, 0x46, 0x05, 0x52, 0x5a, 0xd2, 0x8b, 0x76, 0xb0, 0x67, 0xda, 0x0e, 0xa9,
	0x43, 0x61, 0x64, 0xf7, 0x90, 0xcc, 0x9a, 0xce, 0x3e, 0xc9, 0x3a, 0x14, 0x87, 0xb6, 0xc5, 0x0e,
	0xad, 0xc0, 0xe7, 0x1b, 0xda, 0x56, 0xbb, 0x47, 0x9a, 0x50, 0x72, 0x6c, 0xeb, 0xd4, 0x31, 0x87,
	0x14, 0x89, 0x2b, 0xeb, 0x71, 0x9b, 0x34, 0x60, 0xd1, 0xb4, 0x2c, 0x77, 0xe4, 0x84, 0x82, 0xb8,
	0xa8, 0xa9, 0x3d, 0x06, 0x88, 0x89, 0x08, 0xc8, 0x43, 0xa8, 0xf4, 0x59, 0xcb, 0xb0, 0x59, 0xb3,
	0x91, 0xbb, 0x5e, 0xb8, 0x5d, 0xb9, 0xbf, 0xbe, 0x35, 0xbe, 0xd1, 0xad, 0x18, 0x59, 0x87, 0x7e,
	0x3c, 0x4e, 0xfb, 0x93, 0x1c, 0xac, 0xc4, 0x3d, 0xcf, 0xed, 0xf0, 0x45, 0xb7, 0xdb, 0xee, 0xfd,
	0x9a, 0xf7, 0x44, 0x60, 0x3e, 0x0c, 0xed, 0x9e, 0xd8, 0x10, 0x7e, 0x6b, 0x5d, 0x20, 0xe3, 0xdd,
	0xc4, 0x74, 0x7c, 0x9a, 0xb5, 0xab, 0x2b, 0x99, 0xbb, 0x8a, 0xc6, 0x28, 0xbb, 0x33, 0xa0, 0x8c,
	0x8c, 0x88, 0x17, 0xc5, 0xb8, 0x87, 0x0f, 0x64, 0x44, 0xe5, 0x04, 0xf7, 0x20, 0x9f, 0x32, 0xaa,
	0x18, 0x83, 0xd8, 0x4e, 0x9f, 0xfa, 0x82, 0xb1, 0x44, 0x8b, 0x31, 0x08, 0x1f, 0xc6, 0x18, 0x44,
	0x70, 0x15, 0x02, 0x8e, 0xfc, 0x81, 0xf6, 0x0d, 0x80, 0x4e, 0xcf, 0x6c, 0xfa, 0x0a, 0x57, 0xf8,
	0x08, 0x2a, 0x3e, 0xb6, 0x8c, 0xf0, 0xdc, 0xe3, 0x4b, 0x2c, 0xdd, 0x6f, 0xc8, 0xe4, 0xee, 0x99,
	0x21, 0xf5, 0x6d, 0x73, 0xd0, 0x3d, 0xf7, 0xa8, 0x0e, 0x1c, 0x99, 0x7d, 0xb3, 0x33, 0x41, 0xb2,
	0xf8, 0xda, 0xf8, 0xcd, 0x08, 0x16, 0xd3, 0x8d, 0x97, 0x2e, 0x73, 0x08, 0x5b, 0xfb, 0x1f, 0xe7,
	0xa1, 0xde, 0xb2, 0x42, 0xfb, 0xcc, 0x0e, 0xcf, 0x1f, 0x99, 0x01, 0x45, 0x12, 0xd6, 0x60, 0x21,
	0x74, 0x3d, 0xdb, 0x12, 0xfb, 0xe3, 0x0d, 0x72, 0x0d, 0x2a, 0x96, 0x10, 0x2c, 0x23, 0xbe, 0x3e,
	0x88, 0x40, 0xed, 0x1e, 0xb9, 0x0b, 0x0b, 0x03, 0x7a, 0x46, 0xf9, 0x2a, 0x4b, 0x2a, 0xe3, 0xec,
	0xb2, 0x0e, 0x24, 0x98, 0xe3, 0x24, 0x44, 0xa9, 0x16, 0x8b, 0xd2, 0x0d, 0xa8, 0xda, 0x81, 0xe1,
	0x53, 0xcb, 0x1d, 0x0e, 0xa9, 0xc3, 0xef, 0xb7, 0xa4, 0x57, 0xec, 0x40, 0x8f, 0x40, 0xe4, 0x3a,
	0x54, 0x07, 0xf6, 0x19, 0x35, 0x5c, 0x26, 0xc4, 0x43, 0x2a, 0xe4, 0x0a, 0x18, 0x6c, 0xdf, 0xe9,
	0xda, 0x43, 0x4a, 0x34, 0xa8, 0x71, 0x8c, 0x93, 0x13, 0x8e, 0xb2, 0x88, 0x28, 0x15, 0x44, 0x39,
	0x39, 0x41, 0x9c, 0xeb, 0x50, 0x7d, 0x65, 0xfa, 0x8e, 0x31, 0xf2, 0x38, 0x4a, 0x89, 0xcf, 0xc2,
	0x60, 0x47, 0x1e, 0x62, 0x08, 0x3e, 0x2d, 0x8f, 0xf9, 0x54, 0x83, 0xaa, 0xed, 0x84, 0xbe, 0xdb,
	0x1b, 0x59, 0xa1, 0xed, 0x3a, 0x0d, 0xc0, 0xf3, 0x51, 0x60, 0x09, 0xc5, 0x56, 0x49, 0x28, 0x36,
	0x54, 0x15, 0x81, 0xe1, 0xb8, 0xa1, 0x7d, 0x72, 0xde, 0xa8, 0xe2, 0xe6, 0x4a, 0x76, 0xd0, 0xc1,
	0x36, 0xf9, 0x10, 0x90, 0x44, 0x43, 0x9c, 0x4c, 0x0d, 0xcf, 0x71, 0x43, 0x39, 0x47, 0xfb, 0x8c,
	0x1e, 0x62, 0x2f, 0xdf, 0x30, 0xff, 0x8e, 0xa5, 0x61, 0x69, 0x2c, 0x0d, 0x64, 0x0f, 0x56, 0x8f,
	0x7d, 0xd3, 0xe9, 0x19, 0xb6, 0x13, 0xd2, 0xbe, 0x6f, 0x0e, 0x50, 0x00, 0x1a, 0xcb, 0xd7, 0x73,
	0x49, 0xfe, 0x7f, 0xc4, 0xd0, 0xda, 0x02, 0x0b, 0xa5, 0x7b, 0xe5, 0x38, 0x09, 0x62, 0xc2, 0xe8,
	0x7a, 0xd4, 0x37, 0x43, 0xd7, 0x6f, 0xd4, 0x39, 0x07, 0x47, 0x6d, 0xed, 0x97, 0x79, 0x58, 0x89,
	0xb8, 0xa8, 0xcb, 0x98, 0x05, 0x47, 0x5c, 0x83, 0xca, 0x89, 0x6f, 0x0e, 0xa9, 0x61, 0xb9, 0x03,
	0xd7, 0x17, 0xcc, 0x04, 0x08, 0xda, 0x66, 0x10, 0x72, 0x07, 0x56, 0x82, 0x17, 0xae, 0x1f, 0x1a,
	0x96, 0x7b, 0x46, 0x7d, 0x03, 0x35, 0xa6, 0x60, 0xde, 0x65, 0xec, 0xd8, 0x66, 0xf0, 0x36, 0x03,
	0x93, 0xdb, 0x50, 0x1f, 0xb8, 0x4e, 0x5f, 0x41, 0xe5, 0xdc, 0xbc, 0xc4, 0xe0, 0x12, 0xe6, 0xdb,
	0xb0, 0x34, 0xe6, 0x53, 0xc4, 0xe3, 0xba, 0xa3, 0x16, 0xb3, 0x2a, 0xa2, 0x7d, 0x02, 0xb5, 0xa1,
	0x10, 0x24, 0x2e, 0x69, 0x0b, 0x17, 0x48, 0x5a, 0x75, 0x28, 0xb5, 0xc8, 0x03, 0x58, 0x37, 0xc5,
	0x8e, 0x0d, 0x85, 0x27, 0x8a, 0xb8, 0xd8, 0x5a, 0xd4, 0xd9, 0x96, 0xfa, 0xb4, 0x01, 0xac, 0x6c,
	0xfb, 0xd4, 0x0c, 0x69, 0x64, 0xa1, 0x84, 0x25, 0x94, 0x0c, 0x52, 0x6e, 0xaa, 0x41, 0xca, 0x27,
	0x0c, 0x92, 0x62, 0x78, 0x0a, 0xaa, 0xe1, 0xd1, 0xd6, 0x80, 0x24, 0x57, 0x0b, 0x3c, 0xed, 0x2f,
	0x73, 0xb0, 0x72, 0xe4, 0xf5, 0x12, 0x44, 0xfc, 0x7f, 0x5b, 0x49, 0x46, 0x6c, 0x92, 0xaa, 0xc0,
	0xd3, 0x56, 0x61, 0xe5, 0x09, 0x0d, 0x5b, 0x83, 0x81, 0x44, 0xab, 0x76, 0x04, 0x24, 0x09, 0x0c,
	0x3c, 0xf2, 0x99, 0x7c, 0xed, 0x92, 0xa6, 0x57, 0x2e, 0x54, 0xf6, 0x0c, 0x24, 0x86, 0x40, 0x3d,
	0x6f, 0xc3, 0xf2, 0x9e, 0x7b, 0x36, 0xf5, 0x54, 0x6e, 0x42, 0xcd, 0x1a, 0xf9, 0x3e, 0x75, 0x98,
	0x31, 0x61, 0x26, 0x9d, 0x2b, 0xc1, 0xaa, 0x00, 0xb6, 0x19, 0x8c, 0x69, 0xb0, 0xd0, 0xf4, 0xfb,
	0x34, 0xc2, 0xe1, 0x26, 0xad, 0xc2, 0x61, 0x88, 0xa2, 0x11, 0xa8, 0xab, 0x4b, 0x05, 0x9e, 0xf6,
	0x3b, 0x50, 0xc7, 0xdb, 0x8a, 0xe4, 0x88, 0xad, 0xff, 0x25, 0x90, 0x98, 0xc9, 0x8e, 0xcd, 0x80,
	0x72, 0x09, 0xce, 0xa1, 0x04, 0x5f, 0x96, 0xf7, 0x95, 0x54, 0xe1, 0x7a, 0xdd, 0x4c, 0x40, 0xd8,
	0x51, 0x26, 0xe6, 0x0f, 0x3c, 0xed, 0x0f, 0x63, 0x66, 0x90, 0x97, 0xbd, 0x06, 0x95, 0x31, 0x6f,
	0x47, 0xfb, 0x87, 0x98, 0xa3, 0x7b, 0x13, 0xe8, 0xca, 0x7f, 0x2b, 0xba, 0xe2, 0x8b, 0x57, 0x08,
	0xfb, 0x03, 0x80, 0xe7, 0x74, 0x60, 0xb9, 0x43, 0x7a, 0xe0, 0x7a, 0xe4, 0x32, 0x94, 0x83, 0xd0,
	0xf4, 0xc3, 0xcf, 0x07, 0x66, 0x5f, 0x38, 0x13, 0x63, 0xc0, 0xd8, 0x5c, 0xe5, 0x65, 0x73, 0x75,
	0x19, 0xca, 0xae, 0xc7, 0xa4, 0x6e, 0xdf, 0x61, 0x2c, 0x5a, 0x60, 0x0c, 0x1c, 0x03, 0xc8, 0x55,
	0x00, 0xcf, 0xb6, 0xc2, 0x91, 0xcf, 0x98, 0x4f, 0x28, 0x08, 0x09, 0xa2, 0xfd, 0xfd, 0x3c, 0x6c,
	0xa8, 0x64, 0xa1, 0x5e, 0x9b, 0xe9, 0x74, 0xbe, 0x82, 0x4b, 0xfc, 0x08, 0x62, 0x34, 0xa4, 0x48,
	0x3e, 0xa4, 0x2b, 0x59, 0x87, 0x14, 0x6b, 0x4e, 0x7d, 0x03, 0x7b, 0xd3, 0x1a, 0xf5, 0x63, 0xa8,
	0x7a, 0x66, 0x18, 0x52, 0xdf, 0xe1, 0x2a, 0x8b, 0x1b, 0xda, 0x4d, 0x79, 0xb2, 0x03, 0xde, 0x8f,
	0x1a, 0xab, 0xe2, 0x8d, 0x1b, 0xe4, 0x11, 0xac, 0x48, 0x6e, 0x90, 0x61, 0xfa, 0xbe, 0x79, 0xde,
	0x98, 0x47, 0x11, 0xd9, 0xc8, 0x74, 0x86, 0x02, 0x7d, 0x79, 0xec, 0x05, 0xb5, 0x18, 0x3a, 0xb3,
	0x4f, 0xaf, 0xf8, 0xad, 0x18, 0x9e, 0xeb, 0xa1, 0x0c, 0x27, 0x46, 0x8f, 0x2f, 0x4d, 0x87, 0x57,
	0xe3, 0x0b, 0x6c, 0xc1, 0xb2, 0xe5, 0x9e, 0x72, 0xc3, 0x26, 0xb8, 0xa5, 0x88, 0x83, 0x2f, 0x29,
	0xd2, 0xe9, 0x9e, 0xa2, 0x41, 0xa3, 0x42, 0x3c, 0xe5, 0x26, 0xb9, 0x0b, 0xc4, 0x0e, 0xd8, 0x04,
	0xe7, 0x86, 0xd9, 0xeb, 0x19, 0xa1, 0x6d, 0x9d, 0xd2, 0x10, 0x0d, 0x7b, 0x49, 0x5f, 0xb6, 0x83,
	0xc3, 0xd0, 0x3c, 0x6f, 0xf5, 0x7a, 0x5d, 0x04, 0x33, 0x41, 0x45, 0x4c, 0x66, 0xfa, 0xfc, 0x33,
	0x73, 0x20, 0xac, 0x7b, 0x95, 0x01, 0xdb, 0x02, 0xc6, 0x74, 0x1c, 0x9f, 0xc5, 0xb0, 0x9c, 0x50,
	0x98, 0xf9, 0x32, 0x87, 0x6c, 0x3b, 0x21, 0x79, 0x0f, 0xd6, 0x42, 0x37, 0x34, 0x07, 0xd2, 0x72,
	0x88, 0x08, 0x88, 0xb8, 0x82, 0x7d, 0xf1, 0x8a, 0xdb, 0x4e, 0xa8, 0x5d, 0x86, 0x26, 0xd7, 0x4b,
	0x0a, 0x2d, 0x2d, 0x2b, 0x0c, 0x98, 0xd6, 0xfa, 0xd7, 0x3c, 0xac, 0xa7, 0x3a, 0x22, 0x3b, 0x39,
	0x9d, 0xa1, 0xc6, 0x0a, 0x35, 0xaf, 0xf8, 0x4a, 0xaa, 0xab, 0x51, 0x48, 0xba, 0x1a, 0x29, 0x2f,
	0x68, 0xfe, 0x62, 0x2f, 0x68, 0x21, 0xe5, 0x05, 0x65, 0x9f, 0x7b, 0x71, 0xc6, 0x73, 0x5f, 0xbc,
	0xf0, 0xdc, 0x4b, 0xb3, 0x9e, 0x7b, 0x79, 0xd2, 0xb9, 0x7f, 0x0d, 0x6f, 0x4d, 0x3c, 0xf7, 0xc0,
	0x23, 0x9f, 0x42, 0x99, 0x31, 0x9c, 0x31, 0xb0, 0x83, 0x50, 0xd8, 0x84, 0x1b, 0x32, 0xd7, 0x65,
	0x5e, 0x8a, 0x5e, 0x62, 0x63, 0x76, 0xed, 0x20, 0xd4, 0x7e, 0x91, 0x83, 0x95, 0x94, 0x87, 0x44,
	0x3e, 0x00, 0x88, 0xbc, 0x2b, 0x54, 0xc9, 0xa9, 0x50, 0x49, 0x0c, 0x39, 0x71, 0xf5, 0xf2, 0x71,
	0xf4, 0xc9, 0x54, 0x15, 0x8f, 0xc3, 0xd8, 0x45, 0x2e, 0xe8, 0xbc, 0x41, 0xee, 0xc3, 0x7a, 0xc2,
	0x53, 0x13, 0xd7, 0xcd, 0xaf, 0x74, 0x55, 0x71, 0xc6, 0xb8, 0xc7, 0xa7, 0xed, 0x40, 0x39, 0x5e,
	0x81, 0x5c, 0x82, 0x92, 0x98, 0xa0, 0x27, 0x1c, 0x88, 0x45, 0x3e, 0x06, 0x0d, 0x39, 0xef, 0x92,
	0x22, 0x03, 0x4e, 0x10, 0x0b, 0x58, 0xb4, 0x4b, 0xb0, 0x99, 0xa9, 0xe6, 0x02, 0x4f, 0x7b, 0x19,
	0x69, 0x40, 0x8c, 0x7e, 0xd8, 0x51, 0xe0, 0x6e, 0x66, 0xd1, 0x80, 0x0f, 0xa1, 0xc2, 0x4f, 0x82,
	0x1b, 0xe2, 0x7c, 0xfa, 0x74, 0xe2, 0x88, 0x4a, 0xe7, 0xf1, 0x14, 0x37, 0xc1, 0x31, 0x35, 0x89,
	0x25, 0x03, 0x4f, 0x1b, 0x25, 0x09, 0xe5, 0x21, 0xd3, 0x4c, 0xe4, 0x7c, 0x04, 0x55, 0x11, 0x03,
	0xc9, 0xf4, 0x28, 0x7a, 0x6b, 0x1c, 0x80, 0xe9, 0x22, 0xfc, 0xe2, 0x14, 0x35, 0xa1, 0x91, 0xbd,
	0x6c, 0xe0, 0x69, 0xff, 0x92, 0xe7, 0x8e, 0x88, 0xe8, 0x61, 0xe4, 0x32, 0x72, 0x6e, 0x64, 0x90,
	0xf3, 0xc5, 0x9c, 0x42, 0xd0, 0x35, 0x00, 0x6e, 0x12, 0xc6, 0x97, 0xf2, 0xc5, 0x9c, 0x5e, 0x46,
	0x58, 0x87, 0x47, 0xb7, 0x18, 0x7a, 0x14, 0xc4, 0x58, 0x0c, 0x3e, 0xae, 0x29, 0xd2, 0x3e, 0x2f,
	0xba, 0x24, 0x79, 0x4f, 0x04, 0x68, 0x0b, 0xa9, 0x00, 0x6d, 0xac, 0x47, 0x8a, 0x8a, 0x1e, 0xb9,
	0x0b, 0x24, 0x0a, 0xa8, 0xd0, 0xaa, 0xca, 0x31, 0xd3, 0x32, 0x0f, 0xab, 0x0e, 0x19, 0x1c, 0xf5,
	0xc1, 0x2d, 0xa8, 0x47, 0xc8, 0xd4, 0xe9, 0xc9, 0xb1, 0x53, 0x8d, 0xa3, 0xee, 0x38, 0x3d, 0x44,
	0x24, 0x30, 0xef, 0x31, 0xef, 0x9b, 0xcb, 0x2d, 0x7e, 0x33, 0xfe, 0x1f, 0xd8, 0x43, 0x3b, 0x52,
	0xa2, 0xbc, 0xf1, 0x68, 0x89, 0xdd, 0xcf, 0x4b, 0x34, 0x4e, 0xcc, 0xac, 0x69, 0xff, 0xbe, 0x08,
	0xab, 0xa9, 0x83, 0x0d, 0x3c, 0xf2, 0x35, 0x10, 0x73, 0x30, 0x30, 0x24, 0xbf, 0x7b, 0xec, 0xe6,
	0xbd, 0xa7, 0xd8, 0xb0, 0xf4, 0xe0, 0xad, 0xd6, 0x60, 0xd0, 0x8a, 0x7d, 0x72, 0xf4, 0x44, 0x54,
	0x40, 0x10, 0x13, 0x9c, 0x97, 0x08, 0x7e, 0x1b, 0x96, 0x9c, 0xd1, 0x30, 0x5a, 0xd2, 0xa6, 0x91,
	0x4c, 0xd6, 0x9c, 0xd1, 0xb0, 0x15, 0x03, 0x99, 0x6b, 0xcb, 0x75, 0x16, 0x53, 0x54, 0x5c, 0xcd,
	0x96, 0x10, 0xb0, 0xed, 0x84, 0xcd, 0x3f, 0x2b, 0xc2, 0x72, 0x62, 0xf5, 0x8b, 0x79, 0xf6, 0x1e,
	0xcc, 0xcf, 0xec, 0x54, 0x21, 0xe6, 0x74, 0xb7, 0xa3, 0xf0, 0x7d, 0xba, 0x1d, 0xf3, 0x6f, 0xe0,
	0x76, 0x7c, 0xa6, 0x66, 0x5f, 0x16, 0xf0, 0xb2, 0xae, 0x66, 0x3b, 0x1c, 0x59, 0xe9, 0x97, 0xa4,
	0x2e, 0x29, 0xce, 0xa8, 0x4b, 0x52, 0x42, 0xbf, 0x38, 0xb3, 0xd0, 0x27, 0xdd, 0x9c, 0xd2, 0x77,
	0x71, 0x73, 0xca, 0xdf, 0x8b, 0x9b, 0x03, 0x33, 0x9a, 0xdb, 0xca, 0x85, 0xe6, 0xb6, 0x3a, 0xab,
	0xb9, 0xad, 0x4d, 0x30, 0xb7, 0xe4, 0xc7, 0x8a, 0xe5, 0x5b, 0x9a, 0x25, 0x9d, 0x30, 0xb6, 0x80,
	0xda, 0x19, 0xac, 0x3f, 0xa1, 0xe1, 0xde, 0x79, 0x9c, 0xdb, 0x8d, 0xd4, 0xa6, 0xc8, 0xb6, 0xe4,
	0xc6, 0xd9, 0x96, 0x0d, 0x28, 0xba, 0x27, 0x27, 0x01, 0x0d, 0x23, 0xb7, 0x87, 0xb7, 0xc6, 0x4a,
	0xa4, 0x20, 0x29, 0x11, 0xb6, 0xcd, 0x73, 0x6a, 0xfa, 0xc6, 0xd0, 0x75, 0xc2, 0x17, 0xc2, 0xa3,
	0x2f, 0x33, 0xc8, 0x1e, 0x03, 0x68, 0x43, 0xd8, 0xc8, 0x5a, 0x37, 0xf0, 0x30, 0x6b, 0x46, 0x5f,
	0x87, 0x62, 0x65, 0xfc, 0x26, 0x9f, 0x01, 0x48, 0x22, 0xcf, 0xed, 0xc5, 0x35, 0x85, 0xe7, 0xce,
	0xe5, 0xe4, 0x34, 0xb7, 0x0c, 0xd2, 0x10, 0xed, 0xdf, 0xf2, 0xb0, 0x9a, 0x81, 0x93, 0x8a, 0x28,
	0x59, 0xec, 0x22, 0x85, 0xd8, 0xbc, 0x81, 0x29, 0x63, 0x29, 0xc3, 0xc1, 0x1b, 0x6c, 0x87, 0x92,
	0x7a, 0xe6, 0x5a, 0x86, 0x87, 0x41, 0xa8, 0x6f, 0x2f, 0x41, 0x29, 0x56, 0xc8, 0x5c, 0xf7, 0x2f,
	0x52, 0xa1, 0x8a, 0x37, 0xa0, 0xf8, 0xca, 0xf4, 0x87, 0x23, 0x2f, 0x52, 0xfc, 0xbc, 0x45, 0x34,
	0x26, 0xd8, 0x7e, 0x68, 0x5b, 0xb6, 0x67, 0x3a, 0x61, 0x10, 0x79, 0x6b, 0x32, 0x0c, 0x53, 0x4b,
	0xa7, 0x76, 0x0f, 0xa5, 0xa0, 0xac, 0xe3, 0x37, 0xc2, 0x98, 0x22, 0x10, 0xaa, 0x9d, 0x7d, 0x4b,
	0xc6, 0x05, 0xa6, 0x38, 0xa9, 0xa9, 0x7c, 0xd8, 0x3d, 0x58, 0x1b, 0xeb, 0x2b, 0x7b, 0x48, 0x0d,
	0xd6, 0xd5, 0xa7, 0x22, 0x35, 0x16, 0x87, 0x99, 0x6c, 0x1b, 0xdb, 0xd8, 0xa3, 0x1d, 0xa0, 0xbb,
	0x97, 0x3a, 0xda, 0xc8, 0x39, 0x49, 0xf3, 0x51, 0x42, 0xd7, 0xe6, 0x93, 0xba, 0x56, 0xfb, 0xdf,
	0x05, 0xb8, 0x3c, 0x79, 0xca, 0xc0, 0x9b, 0xf1, 0xd6, 0x36, 0xa0, 0xe8, 0xb9, 0x41, 0x48, 0x7d,
	0x71, 0x6d, 0xa2, 0xf5, 0x6b, 0xb8, 0xb7, 0x9b, 0x50, 0xe3, 0xba, 0xbd, 0x47, 0x43, 0xd3, 0x1e,
	0xf0, 0x8b, 0x2b, 0xeb, 0x55, 0x04, 0x3e, 0xe6, 0x30, 0x72, 0x17, 0x16, 0x02, 0xd7, 0xe9, 0x07,
	0x8d, 0xd2, 0x34, 0x95, 0xc9, 0x71, 0xc8, 0x3d, 0x58, 0xf4, 0xa9, 0x37, 0x30, 0xcf, 0x83, 0x46,
	0x79, 0xaa, 0xa2, 0x8c, 0xd0, 0xc8, 0x3d, 0x28, 0xf5, 0x6c, 0x9f, 0x5a, 0xa1, 0xeb, 0xe3, 0x8d,
	0x57, 0xee, 0xaf, 0xc9, 0x43, 0x1e, 0x8b, 0x3e, 0x3d, 0xc6, 0x22, 0xef, 0x42, 0x11, 0xf5, 0x7a,
	0xd0, 0xa8, 0x4c, 0xab, 0x2c, 0x08, 0x24, 0xf2, 0x09, 0xd4, 0xd8, 0x4a, 0x46, 0xe8, 0xf6, 0x69,
	0xf8, 0x82, 0xfa, 0xc8, 0x12, 0x89, 0x7c, 0xce, 0xc1, 0xc0, 0x3c, 0xef, 0x8a, 0x7e, 0xbd, 0xea,
	0x49, 0xad, 0x04, 0xdf, 0xd5, 0x92, 0x7c, 0xf7, 0x05, 0x1e, 0xe1, 0x83, 0xd8, 0x58, 0x36, 0x96,
	0x90, 0xa6, 0x9b, 0x4a, 0x64, 0x60, 0x0f, 0xbd, 0x01, 0x4d, 0x0b, 0x3a, 0x3b, 0xe7, 0x07, 0xb1,
	0x48, 0x8f, 0x19, 0x7f, 0x59, 0x61, 0xfc, 0x5d, 0x58, 0xf6, 0xa9, 0x45, 0x9d, 0x70, 0xbc, 0x46,
	0x1d, 0x77, 0x30, 0xd3, 0x1a, 0x4b, 0x7c, 0x6c, 0xbc, 0xca, 0x0d, 0xa8, 0x46, 0xdb, 0x41, 0xd1,
	0x5b, 0xe1, 0xb1, 0x9c, 0x80, 0xa1, 0xa9, 0x5d, 0x87, 0x22, 0x33, 0x1d, 0xa3, 0xe3, 0x06, 0x41,
	0xe1, 0x59, 0xb0, 0x83, 0xc3, 0xd1, 0x31, 0xea, 0x7f, 0x64, 0x16, 0x1c, 0xb7, 0x2a, 0xf4, 0x3f,
	0x83, 0xb0, 0x51, 0xda, 0x9f, 0xe7, 0xa0, 0x14, 0x5d, 0x56, 0x86, 0xf0, 0xc8, 0x35, 0x98, 0x7c,
	0xa2, 0x06, 0x73, 0x99, 0x45, 0x56, 0x3c, 0xbd, 0x19, 0xa9, 0xaa, 0x31, 0x40, 0x64, 0xba, 0x4f,
	0xdc, 0xc1, 0xc0, 0x7d, 0x25, 0x32, 0x81, 0x25, 0x3b, 0xf8, 0x1c, 0xdb, 0x53, 0x4a, 0x52, 0xff,
	0x99, 0x83, 0xcd, 0x09, 0x87, 0x92, 0x25, 0x87, 0x98, 0x0f, 0x8e, 0xe4, 0x10, 0x1b, 0x63, 0xe9,
	0x2c, 0xc8, 0xd2, 0xf9, 0x16, 0x94, 0x5f, 0xb8, 0x83, 0x9e, 0x2c, 0x84, 0x25, 0x06, 0x88, 0x64,
	0x10, 0x79, 0xcd, 0x19, 0x0d, 0x23, 0x19, 0x64, 0xed, 0xce, 0x68, 0x38, 0xd1, 0x69, 0x56, 0xf9,
	0x6b, 0x31, 0xc9, 0x5f, 0xc9, 0xfb, 0x2a, 0xa5, 0xee, 0x4b, 0xdb, 0x81, 0xaa, 0xcc, 0xbf, 0xec,
	0xa8, 0xc5, 0x21, 0x70, 0xa7, 0xb6, 0xac, 0xc7, 0x6d, 0x85, 0xc0, 0xbc, 0x42, 0xa0, 0xf6, 0x00,
	0xae, 0xc9, 0xca, 0x0b, 0x33, 0xe1, 0x2d, 0xa7, 0xb7, 0x6b, 0x9f, 0xd1, 0x89, 0xb6, 0x55, 0xfb,
	0x87, 0x12, 0x5c, 0x9f, 0x3e, 0x2a, 0xf0, 0xc8, 0x73, 0xa8, 0x88, 0x74, 0xbb, 0xe4, 0x68, 0x3f,
	0x4c, 0x38, 0xda, 0x53, 0xa7, 0xd8, 0xe2, 0x79, 0x79, 0xf4, 0xcd, 0xac, 0xe8, 0x33, 0x20, 0x5d,
	0xc0, 0xe2, 0x85, 0x12, 0x8e, 0xfd, 0xe8, 0x8d, 0xe6, 0x65, 0x0d, 0xee, 0x5a, 0x0c, 0xc4, 0x57,
	0x30, 0xbe, 0xf7, 0x79, 0xf9, 0xde, 0x2f, 0x41, 0xe9, 0xe7, 0xa3, 0xa1, 0x27, 0x25, 0x9d, 0x17,
	0x59, 0xfb, 0xc8, 0x1f, 0x34, 0x9f, 0x40, 0x39, 0xa6, 0x2f, 0xc5, 0x5b, 0x6f, 0x50, 0x9c, 0x68,
	0xfe, 0xd7, 0x02, 0x94, 0x22, 0x8a, 0xbe, 0xcb, 0x44, 0x4a, 0xed, 0x42, 0xe6, 0xe1, 0x38, 0x55,
	0xcd, 0x33, 0xec, 0x6f, 0xc3, 0xd2, 0xb1, 0x69, 0x9d, 0xf6, 0x7d, 0x77, 0xe4, 0xf4, 0x70, 0x67,
	0xa2, 0xc4, 0x31, 0x86, 0x1e, 0xf9, 0x83, 0x04, 0x8b, 0x16, 0x33, 0x58, 0x54, 0x29, 0xb5, 0x95,
	0xd2, 0xa5, 0xb6, 0x38, 0xb5, 0x5a, 0x96, 0x53, 0xab, 0xc9, 0x02, 0x1c, 0xa4, 0x0a, 0x70, 0xc9,
	0xb4, 0x52, 0x25, 0x95, 0x56, 0xfa, 0x1a, 0x80, 0x59, 0x1e, 0xc1, 0x02, 0x55, 0x64, 0x81, 0x4f,
	0xbf, 0x15, 0x0b, 0x6c, 0x1d, 0xba, 0x4e, 0x9f, 0xf3, 0x42, 0x20, 0xbe, 0x82, 0xec, 0x6c, 0x67,
	0xed, 0xcd, 0xb2, 0x9d, 0xc9, 0xb0, 0x67, 0xe9, 0x0d, 0xc2, 0x9e, 0x0f, 0x00, 0xb0, 0xce, 0xc9,
	0x47, 0x2e, 0x4f, 0x2b, 0x88, 0x96, 0x07, 0xd1, 0x27, 0xf9, 0x01, 0x2c, 0x73, 0x0d, 0xae, 0x9a,
	0x8c, 0x92, 0x5e, 0x43, 0x55, 0x1e, 0xeb, 0xc1, 0xac, 0x62, 0xd8, 0x4a, 0x66, 0x31, 0x6c, 0x0b,
	0x56, 0x31, 0x7e, 0x1d, 0xf5, 0x6c, 0xea, 0x58, 0xd4, 0xf8, 0xb9, 0x6b, 0x3b, 0xb4, 0x87, 0x06,
	0xa2, 0xa6, 0xaf, 0xb0, 0x20, 0x56, 0xf4, 0x7c, 0x89, 0x1d, 0xcd, 0x5b, 0x50, 0x8a, 0x8e, 0x13,
	0x8b, 0x3c, 0x6c, 0x15, 0xa9, 0xd4, 0x5d, 0x62, 0x00, 0x4c, 0x1c, 0xdd, 0x85, 0x4d, 0xf9, 0x86,
	0xbe, 0x60, 0x11, 0x90, 0xd9, 0xa7, 0xd9, 0xda, 0xe6, 0x7f, 0x8a, 0xd0, 0xc8, 0xc6, 0x0e, 0x3c,
	0xf2, 0x0e, 0xac, 0x88, 0x4d, 0x8f, 0x8f, 0x47, 0x24, 0xf9, 0x97, 0x70, 0xdb, 0xf1, 0x30, 0xf2,
	0x15, 0x2c, 0x25, 0x82, 0x7f, 0xae, 0x3b, 0xde, 0x9f, 0xc4, 0x38, 0xf2, 0x42, 0x5b, 0x8a, 0x5b,
	0x57, 0x33, 0x95, 0xd8, 0xff, 0x25, 0x6c, 0xc6, 0x82, 0x97, 0x58, 0xa2, 0x80, 0x4b, 0x7c, 0x34,
	0xd3, 0x12, 0x51, 0x45, 0x47, 0x59, 0x6a, 0xdd, 0xca, 0x80, 0x06, 0xcd, 0x5f, 0x15, 0xa0, 0xaa,
	0xe4, 0x04, 0xb2, 0xbc, 0xcc, 0x74, 0x5d, 0xe3, 0x82, 0xa4, 0xef, 0x0d, 0xa8, 0xca, 0x17, 0x1e,
	0xe5, 0x7c, 0xa5, 0x9b, 0x4e, 0x56, 0x99, 0x17, 0x66, 0xae, 0x32, 0x5f, 0x5c, 0x78, 0xcf, 0xd4,
	0x75, 0x8b, 0xb3, 0x57, 0x74, 0x4b, 0x99, 0x4c, 0x9c, 0x28, 0x24, 0x97, 0x53, 0x85, 0xe4, 0xa4,
	0x26, 0x83, 0xcc, 0x47, 0x03, 0x17, 0x68, 0xa4, 0x0c, 0xe1, 0xab, 0x66, 0x08, 0x5f, 0xf3, 0xef,
	0x72, 0xb0, 0x96, 0x75, 0xcf, 0xdf, 0xa9, 0x90, 0x9b, 0xe6, 0xec, 0xc2, 0xf7, 0xc3, 0xd9, 0xda,
	0xc7, 0xb0, 0x2c, 0xcb, 0x50, 0x76, 0x88, 0x34, 0x76, 0x1d, 0xf3, 0x92, 0xeb, 0xa8, 0x11, 0xa8,
	0xab, 0x63, 0x03, 0x4f, 0xdb, 0x85, 0x4d, 0x19, 0x26, 0xd7, 0x0d, 0xbf, 0x45, 0xe8, 0xd5, 0x84,
	0x46, 0xf6, 0x6c, 0x81, 0xa7, 0xfd, 0x6d, 0x0e, 0x16, 0x50, 0x3f, 0x93, 0x0f, 0xa0, 0x34, 0xb2,
	0x7b, 0xa8, 0x95, 0xb3, 0xaa, 0xba, 0x88, 0xb4, 0x75, 0x14, 0x50, 0xbf, 0xdd, 0xd3, 0x63, 0xcc,
	0xd4, 0x6b, 0x8d, 0x7c, 0xc6, 0x6b, 0x8d, 0x4d, 0x58, 0xf4, 0x4e, 0x0d, 0xdb, 0x72, 0x9d, 0x38,
	0x68, 0x3b, 0x6d, 0x5b, 0xae, 0xd3, 0xdc, 0x82, 0x22, 0x9f, 0x30, 0x63, 0x57, 0xd1, 0x6b, 0x8b,
	0xbc, 0xf4, 0xf6, 0xe8, 0x9f, 0x73, 0xb0, 0x70, 0x18, 0x32, 0x6e, 0xfd, 0x00, 0xc3, 0xbd, 0x3e,
	0x95, 0xdf, 0xef, 0xac, 0x27, 0x0a, 0x0e, 0x7d, 0xca, 0x55, 0x7f, 0x10, 0x7d, 0x0a, 0xe1, 0xcf,
	0xa7, 0x43, 0x4c, 0xc5, 0x89, 0x25, 0x30, 0x8f, 0xb4, 0x72, 0x73, 0x8f, 0xdf, 0xa9, 0x6d, 0x2e,
	0x64, 0x6c, 0xf3, 0x1e, 0xf0, 0x94, 0x1a, 0x2f, 0x82, 0xf0, 0x1c, 0xda, 0x4a, 0xea, 0x08, 0xf5,
	0x32, 0x22, 0x61, 0xd5, 0xe3, 0xaf, 0xe6, 0xa1, 0x72, 0x40, 0xfd, 0x13, 0xd7, 0x1f, 0x9a, 0x4c,
	0x69, 0x70, 0xfa, 0x6a, 0x38, 0xb7, 0x78, 0x20, 0x30, 0xe5, 0xfd, 0x1e, 0xf9, 0x21, 0x90, 0x33,
	0x3b, 0xb0, 0x8f, 0x07, 0xd4, 0x38, 0xa6, 0x7d, 0x5b, 0x28, 0x0c, 0xb6, 0xbd, 0x82, 0x5e, 0x17,
	0x3d, 0x8f, 0x58, 0x07, 0x8a, 0xde, 0x6d, 0x88, 0x60, 0xe3, 0x9c, 0x72, 0x01, 0x71, 0x97, 0x04,
	0x3c, 0x4a, 0x2a, 0x67, 0xfb, 0x78, 0x57, 0x00, 0xa8, 0x13, 0xfa, 0xe7, 0xfc, 0x22, 0xf9, 0x01,
	0x94, 0x11, 0xc2, 0xee, 0x92, 0x4d, 0x1f, 0x95, 0xed, 0xf9, 0xcd, 0xc4, 0xde, 0xd0, 0x92, 0x80,
	0xe3, 0x95, 0x60, 0x36, 0x42, 0xdc, 0x1d, 0x9e, 0xd3, 0x62, 0xfa, 0x9c, 0x10, 0x51, 0xdc, 0x1b,
	0x3b, 0x27, 0xd4, 0x78, 0x38, 0x62, 0x84, 0x65, 0x82, 0x71, 0x3e, 0xbc, 0xa0, 0x2f, 0x63, 0x07,
	0x2f, 0x1f, 0x20, 0xf1, 0x0f, 0x61, 0xd3, 0x1b, 0x1f, 0xa9, 0x32, 0xa2, 0x8c, 0x23, 0xd6, 0xa5,
	0x6e, 0x69, 0xdc, 0x55, 0xae, 0x58, 0x42, 0xd3, 0x76, 0xa8, 0x2f, 0x1e, 0x1d, 0x49, 0x10, 0xa6,
	0xb9, 0xdc, 0x57, 0x0e, 0xf5, 0x0d, 0x16, 0x99, 0x71, 0x73, 0x5d, 0xe1, 0xfe, 0x20, 0x82, 0x3b,
	0xb6, 0x75, 0xda, 0x11, 0x6f, 0xc1, 0x7a, 0x76, 0x80, 0x71, 0x84, 0xdd, 0x8b, 0x32, 0x81, 0x02,
	0xd2, 0xee, 0xb1, 0x69, 0xa2, 0x2b, 0xe5, 0x79, 0xd3, 0xe8, 0x3d, 0x51, 0x4d, 0x80, 0x9f, 0xd9,
	0xf4, 0x55, 0xbb, 0xa7, 0x75, 0x60, 0xa5, 0xd5, 0xeb, 0x49, 0xcc, 0xc1, 0x64, 0xff, 0x23, 0xa8,
	0x48, 0xc4, 0x8b, 0x37, 0x0a, 0xaa, 0xaf, 0x24, 0x0d, 0x90, 0x71, 0xb5, 0x7d, 0x20, 0xc9, 0xf9,
	0x02, 0xef, 0xbb, 0x4c, 0xb8, 0x0d, 0xe4, 0x30, 0x74, 0xbd, 0x04, 0x85, 0x17, 0x70, 0xec, 0x58,
	0x00, 0x91, 0xc1, 0xb5, 0x75, 0x58, 0x4d, 0x4d, 0x12, 0x78, 0xda, 0x4f, 0xf0, 0x45, 0xca, 0x9b,
	0x4d, 0x7d, 0x05, 0xc0, 0x0e, 0x0c, 0xc1, 0x6a, 0x42, 0xc3, 0x96, 0xed, 0x60, 0x9b, 0x03, 0xb4,
	0x7f, 0xca, 0x41, 0xbd, 0x43, 0x5f, 0x87, 0x3c, 0x0a, 0xb7, 0x5d, 0xa7, 0x65, 0x85, 0xb3, 0x53,
	0x3b, 0x4d, 0x5d, 0xa4, 0xc3, 0x89, 0xf9, 0xac, 0x70, 0xe2, 0x1a, 0x54, 0xe8, 0x6b, 0xcf, 0xf6,
	0xa9, 0x52, 0x03, 0xe6, 0xa0, 0xc9, 0x66, 0xbd, 0x98, 0x69, 0xd6, 0xb5, 0x5f, 0xe5, 0xb0, 0x2a,
	0x96, 0xbc, 0xce, 0xdf, 0x82, 0xaa, 0x74, 0x45, 0x51, 0x30, 0x39, 0xf1, 0x3e, 0x15, 0x64, 0xf2,
	0x0e, 0xd4, 0x65, 0xc1, 0x89, 0xdf, 0x3e, 0xd4, 0xf4, 0x65, 0x09, 0x8e, 0x7a, 0xf4, 0x43, 0x28,
	0x39, 0xf4, 0x35, 0xe6, 0x5c, 0x70, 0xb3, 0x89, 0xda, 0x49, 0xf2, 0x9c, 0xf5, 0x45, 0x86, 0xdd,
	0xb2, 0x42, 0xed, 0x16, 0x26, 0xa6, 0x25, 0x1a, 0x1e, 0x9d, 0xb7, 0x7b, 0xea, 0x23, 0x20, 0xce,
	0x18, 0x47, 0x98, 0x49, 0x4e, 0x21, 0x66, 0xee, 0x31, 0x37, 0xf3, 0x1e, 0xb5, 0xd7, 0x70, 0xe5,
	0x90, 0x86, 0x82, 0x27, 0x24, 0x34, 0xae, 0x71, 0x2e, 0x66, 0xb2, 0x2c, 0x25, 0x97, 0xcf, 0x54,
	0x72, 0x7c, 0x43, 0x85, 0x78, 0x43, 0xd7, 0xe1, 0xea, 0xb4, 0x95, 0x03, 0x4f, 0xfb, 0x04, 0x2e,
	0x3f, 0xb6, 0x03, 0xd3, 0xf3, 0xa8, 0xe9, 0xa7, 0x4e, 0xf0, 0xe2, 0xc7, 0xdc, 0xd7, 0xe0, 0xca,
	0x94, 0xe1, 0x81, 0xa7, 0x3d, 0x85, 0xca, 0x33, 0x3b, 0xb0, 0x43, 0xca, 0xcb, 0xd9, 0x37, 0xa0,
	0x7a, 0xc6, 0x9b, 0x46, 0x60, 0x7f, 0x43, 0xc5, 0x84, 0x15, 0x01, 0x3b, 0xb4, 0xbf, 0xc1, 0xa8,
	0xde, 0xa1, 0xb4, 0x67, 0x8c, 0x02, 0x1a, 0xbd, 0x00, 0x67, 0xed, 0xa3, 0x80, 0x6a, 0x4f, 0xe1,
	0xc6, 0x13, 0x1a, 0x1e, 0x7a, 0xd4, 0xb2, 0x4f, 0x6c, 0xda, 0xdb, 0x8e, 0x74, 0x57, 0x3c, 0x98,
	0x51, 0x2c, 0xe9, 0x3a, 0xbb, 0x37, 0x7e, 0x1a, 0x50, 0x8b, 0x75, 0x5d, 0xbb, 0x87, 0x66, 0xf0,
	0xbf, 0x73, 0xa0, 0x5d, 0x34, 0x5b, 0xe0, 0x11, 0x17, 0x56, 0x22, 0x8a, 0x93, 0x6f, 0x0d, 0xb6,
	0x13, 0x1e, 0xdc, 0x05, 0x53, 0x6d, 0x49, 0x07, 0xc1, 0x28, 0xd8, 0x61, 0x56, 0x4c, 0x5f, 0x3e,
	0x53, 0xa1, 0xcd, 0xdf, 0x86, 0xb5, 0x2c, 0x44, 0xe6, 0xac, 0x9c, 0xd2, 0xf3, 0xc8, 0x59, 0x39,
	0xa5, 0xe7, 0xe4, 0x5d, 0x58, 0x38, 0x33, 0x07, 0x23, 0x2a, 0x2a, 0x89, 0x0a, 0x37, 0x4a, 0x53,
	0xe8, 0x1c, 0xeb, 0xe3, 0xfc, 0x6f, 0xe6, 0xb4, 0x2f, 0xa1, 0xba, 0x8f, 0x2f, 0xa5, 0x0e, 0x43,
	0x7f, 0x64, 0x85, 0xcc, 0xfe, 0xf0, 0x97, 0x53, 0x9d, 0x71, 0x24, 0x28, 0x41, 0xf8, 0xd3, 0x50,
	0xd6, 0x6a, 0x47, 0x2c, 0x17, 0xb7, 0xb5, 0xbf, 0xc9, 0xc1, 0xd2, 0x21, 0x75, 0x7a, 0xdf, 0xf1,
	0x35, 0xd7, 0xc3, 0xe4, 0x6b, 0xae, 0x84, 0x0b, 0x28, 0xd3, 0xfb, 0x26, 0xef, 0xbc, 0xb6, 0xa1,
	0xfe, 0x84, 0x86, 0x52, 0x39, 0xef, 0x62, 0x41, 0x4b, 0x3d, 0x5f, 0xd7, 0x0e, 0xd0, 0x26, 0xc8,
	0x93, 0xa0, 0x32, 0x88, 0x8b, 0x88, 0xc3, 0xa0, 0x2f, 0x74, 0x41, 0x53, 0xf1, 0x25, 0x94, 0x63,
	0x89, 0x0b, 0x89, 0x7b, 0x41, 0x5f, 0xfb, 0x8b, 0x1c, 0x2c, 0x33, 0xf7, 0x73, 0x7b, 0x60, 0x5b,
	0xa7, 0xb3, 0x91, 0x15, 0xff, 0xcc, 0x20, 0x2f, 0xfd, 0xcc, 0x20, 0x79, 0x6e, 0xb9, 0x59, 0xcf,
	0x4d, 0x6c, 0x72, 0x7e, 0xbc, 0x49, 0x02, 0x75, 0x95, 0xa2, 0xc0, 0xd3, 0x7e, 0x0c, 0x2b, 0x07,
	0xa3, 0xe0, 0x85, 0x52, 0xd0, 0x24, 0xb7, 0x44, 0xe5, 0x9a, 0xef, 0x78, 0x35, 0x59, 0xf9, 0x64,
	0x32, 0x8f, 0x08, 0xda, 0x97, 0x6a, 0x0a, 0x21, 0x4e, 0xe9, 0x7d, 0x9b, 0x2b, 0x38, 0x55, 0x13,
	0x0c, 0xe3, 0xb9, 0x02, 0xef, 0xa2, 0xc9, 0xee, 0x41, 0x19, 0x8b, 0xb6, 0x52, 0xb9, 0x3d, 0x93,
	0xe8, 0x12, 0xc3, 0xc2, 0x1a, 0xe6, 0x2f, 0x72, 0x50, 0x65, 0x50, 0x7d, 0x34, 0xe0, 0x5b, 0x5e,
	0x87, 0xa2, 0x69, 0x85, 0xe3, 0xd9, 0x17, 0x4c, 0x2b, 0x6c, 0x27, 0x7d, 0xe4, 0x7c, 0x72, 0xe1,
	0x4d, 0x58, 0xc4, 0xea, 0x6d, 0x18, 0x3d, 0x2a, 0x28, 0xb2, 0x66, 0x17, 0x93, 0xbd, 0x48, 0x91,
	0x33, 0x1a, 0x8a, 0x1b, 0x58, 0x64, 0xed, 0xce, 0x68, 0xc8, 0xba, 0xce, 0xdc, 0x90, 0xca, 0x89,
	0x6a, 0xd6, 0xee, 0x8c, 0x86, 0xda, 0x9f, 0xe6, 0x60, 0x51, 0xd0, 0x4a, 0x3e, 0x85, 0x25, 0x9c,
	0xc1, 0x1f, 0x0d, 0x94, 0x47, 0xa3, 0x8d, 0xe4, 0xc6, 0xa2, 0x2d, 0xe8, 0x55, 0x4b, 0xde, 0xd0,
	0x75, 0x0c, 0xa8, 0x5d, 0x8f, 0x3a, 0x06, 0x83, 0x0b, 0x15, 0x0b, 0x76, 0xb0, 0xef, 0x51, 0x87,
	0x8d, 0x63, 0xd1, 0xec, 0x80, 0x9e, 0x84, 0xbc, 0x9b, 0x93, 0x5f, 0x62, 0x00, 0xd6, 0xa9, 0xfd,
	0x3e, 0xd4, 0x54, 0x9e, 0x98, 0x2e, 0xf2, 0xd2, 0x41, 0xe4, 0x27, 0x1e, 0x44, 0x61, 0xf2, 0x41,
	0xcc, 0xab, 0x07, 0xb1, 0x05, 0x1b, 0xba, 0xe9, 0xf4, 0xdc, 0xe1, 0x13, 0x1a, 0xea, 0xee, 0xb1,
	0x1b, 0xa2, 0x63, 0xc2, 0xd8, 0x2a, 0xae, 0x1c, 0xe7, 0xd0, 0xdd, 0xe6, 0x0d, 0xed, 0x21, 0x6c,
	0x66, 0xe2, 0x07, 0x9e, 0xfa, 0x64, 0x59, 0xe4, 0xe4, 0xa3, 0x27, 0xcb, 0x77, 0x7e, 0x06, 0xe5,
	0x38, 0x8b, 0x47, 0xd6, 0x61, 0xe5, 0xa8, 0xf3, 0xb4, 0xb3, 0xff, 0xdc, 0xd8, 0xdd, 0x79, 0xb6,
	0xb3, 0x6b, 0x74, 0x7f, 0x7a, 0xb0, 0x53, 0x9f, 0x23, 0xcb, 0x50, 0x39, 0x3c, 0x3a, 0xd8, 0xd1,
	0x39, 0xb4, 0x9e, 0x23, 0x15, 0x58, 0x6c, 0x89, 0x46, 0x9e, 0x35, 0x1e, 0x89, 0x46, 0x81, 0x35,
	0xb6, 0x45, 0x63, 0xfe, 0x8e, 0x07, 0xb0, 0x2b, 0x3f, 0xef, 0x5f, 0xe2, 0x93, 0x77, 0x8c, 0xc3,
	0x6e, 0xab, 0x7b, 0x74, 0x58, 0x9f, 0x23, 0x6b, 0x50, 0xef, 0xec, 0x77, 0x59, 0x5b, 0xef, 0x46,
	0xd0, 0x1c, 0xc3, 0x7c, 0xde, 0xd2, 0x3b, 0xc6, 0xd1, 0x41, 0x04, 0xcb, 0x93, 0x15, 0xa8, 0xed,
	0xb6, 0x9f, 0xb5, 0x3b, 0x4f, 0x22, 0x50, 0x81, 0x81, 0xb6, 0x77, 0xf7, 0x0f, 0x77, 0x1e, 0x47,
	0xa0, 0xf9, 0x3b, 0xcf, 0xa1, 0x2a, 0x3f, 0x77, 0x27, 0x0d, 0x58, 0x13, 0x1b, 0xda, 0x6b, 0x75,
	0x77, 0xf4, 0x76, 0x2b, 0xde, 0x53, 0x0d, 0xca, 0xcf, 0xf7, 0xf5, 0xc7, 0xbc, 0x99, 0x23, 0x4b,
	0x00, 0xed, 0xbd, 0xd6, 0x93, 0x1d, 0xde, 0xce, 0xb3, 0xf6, 0xb3, 0xf6, 0xe3, 0x9d, 0x7d, 0xde,
	0x2e, 0xdc, 0xf9, 0xeb, 0x1c, 0x54, 0xa4, 0x3c, 0x29, 0xd9, 0x84, 0x55, 0x31, 0xf1, 0x41, 0xab,
	0xdb, 0xdd, 0xd1, 0x3b, 0xd1, 0xbc, 0xec, 0x00, 0x0e, 0xa2, 0x59, 0x57, 0xa0, 0xd6, 0xd9, 0xd7,
	0xf7, 0xc4, 0xaa, 0xc6, 0xfb, 0x7c, 0x1f, 0x32, 0xe8, 0x7e, 0xbd, 0x40, 0x56, 0x61, 0xf9, 0x51,
	0xab, 0xdb, 0xdd, 0xdd, 0x11, 0x58, 0xcf, 0xde, 0xaf, 0xcf, 0x27, 0x81, 0x0f, 0x9e, 0x3d, 0xa8,
	0x2f, 0xb0, 0xe3, 0x12, 0xc0, 0x2f, 0xf6, 0xf7, 0x04, 0xad, 0xc5, 0x3b, 0x3a, 0xac, 0x3e, 0x4a,
	0x3f, 0xae, 0x23, 0x75, 0xa8, 0x1e, 0x39, 0x4f, 0x1d, 0xf7, 0x15, 0x6f, 0xd7, 0xe7, 0xc8, 0x06,
	0x90, 0xd6, 0xc0, 0xa7, 0x66, 0xef, 0xbc, 0xd5, 0x8b, 0xb1, 0x05, 0x99, 0xae, 0x0c, 0xca, 0xdf,
	0x79, 0x0a, 0xe5, 0x38, 0xc2, 0x67, 0xe3, 0xa4, 0x9b, 0x8b, 0x0e, 0x69, 0x8e, 0x94, 0x60, 0xfe,
	0x70, 0x7f, 0x77, 0xbf, 0x9e, 0x23, 0x00, 0x45, 0x4e, 0x58, 0x3d, 0xcf, 0xd6, 0x6d, 0x77, 0xba,
	0xfa, 0xfe, 0xe3, 0xa3, 0xed, 0x6e, 0x7b, 0xbf, 0x53, 0x2f, 0xdc, 0x71, 0xa0, 0x11, 0x2b, 0xb5,
	0x83, 0x84, 0xab, 0x7b, 0x0b, 0x6e, 0xb6, 0x3b, 0xcf, 0x5a, 0xbb, 0xed, 0xc7, 0xc6, 0xde, 0xd1,
	0x61, 0x7b, 0xdb, 0xe8, 0xec, 0x1c, 0x76, 0x8d, 0x83, 0x1d, 0xfd, 0x73, 0x76, 0x4a, 0x9d, 0xed,
	0x78, 0xb1, 0x0d, 0x20, 0xe2, 0xe0, 0xa4, 0x4e, 0x41, 0xfc, 0xce, 0x57, 0x5d, 0xa3, 0xb5, 0xdd,
	0x6d, 0x3f, 0x6b, 0x77, 0x7f, 0x5a, 0xcf, 0xdf, 0xff, 0x8f, 0x75, 0xf1, 0x6b, 0x27, 0xcc, 0xb7,
	0xfe, 0x04, 0x96, 0xd4, 0x5f, 0x10, 0x10, 0xe5, 0xa1, 0x47, 0xea, 0xb7, 0x0c, 0xcd, 0xab, 0xd3,
	0xba, 0x03, 0x4f, 0x9b, 0x63, 0x53, 0xaa, 0xef, 0xfc, 0xd5, 0x29, 0x53, 0xbf, 0x4c, 0x50, 0xa7,
	0xcc, 0xf8, 0x89, 0x00, 0x4e, 0xa9, 0xfe, 0x1e, 0x40, 0x9d, 0x32, 0xf5, 0x03, 0x02, 0x75, 0xca,
	0xf4, 0x4f, 0x09, 0xb4, 0x39, 0xf2, 0x14, 0xaa, 0xf2, 0x03, 0x7d, 0xf2, 0x96, 0x52, 0x3c, 0x57,
	0x7f, 0x25, 0xd0, 0xbc, 0x3c, 0xb9, 0x13, 0x27, 0xeb, 0x40, 0x4d, 0x79, 0x79, 0x4f, 0x2e, 0xa7,
	0x4e, 0x49, 0xca, 0xa2, 0x35, 0xaf, 0x4c, 0xe9, 0x55, 0x8f, 0x30, 0x9e, 0x30, 0xe3, 0x08, 0xe5,
	0x19, 0xaf, 0x4e, 0xeb, 0xc6, 0x29, 0x7f, 0x17, 0x56, 0x33, 0x9e, 0x81, 0x12, 0x6d, 0xf2, 0xc0,
	0xe8, 0x39, 0x7c, 0xf3, 0xe6, 0x85, 0x38, 0xea, 0x0a, 0xca, 0xd3, 0xce, 0xac, 0x15, 0x92, 0xcf,
	0x4d, 0xb3, 0x56, 0x48, 0xbf, 0x0f, 0x9d, 0x23, 0x16, 0xac, 0x65, 0x3d, 0xd5, 0x24, 0x37, 0xa7,
	0xed, 0x5e, 0xbc, 0x21, 0x6d, 0xfe, 0xc6, 0xc5, 0x48, 0xb8, 0x48, 0x17, 0x96, 0x13, 0x8f, 0x0b,
	0xc9, 0xd5, 0xa9, 0x2f, 0x0f, 0x5f, 0x36, 0xaf, 0x5d, 0xf0, 0x32, 0x51, 0x9b, 0x23, 0x5f, 0x63,
	0xc8, 0x9c, 0x78, 0x9c, 0x44, 0x6e, 0x24, 0x73, 0xbf, 0xa9, 0x47, 0x53, 0x4d, 0xed, 0x22, 0x14,
	0x9c, 0xde, 0x55, 0x9d, 0x23, 0x25, 0x69, 0x7d, 0x6b, 0x52, 0x82, 0x39, 0xf1, 0xae, 0xa6, 0x79,
	0x7b, 0x36, 0x44, 0x5c, 0xf0, 0xf7, 0xd4, 0xf7, 0x34, 0xc9, 0xf2, 0x1d, 0xb9, 0x3b, 0x7b, 0xa1,
	0xef, 0x65, 0xf3, 0x87, 0x6f, 0x52, 0x15, 0xe4, 0x7c, 0x90, 0x95, 0x28, 0x57, 0xf9, 0x60, 0x42,
	0xed, 0x4a, 0xe5, 0x83, 0x49, 0xf9, 0x76, 0xae, 0x20, 0x94, 0xca, 0x94, 0xa2, 0x20, 0x12, 0xf9,
	0x76, 0x55, 0x41, 0xa4, 0x12, 0xea, 0x48, 0x71, 0x56, 0x12, 0x5c, 0xa5, 0x78, 0x42, 0xd2, 0x5d,
	0xa5, 0x78, 0x62, 0x2e, 0x1d, 0xb5, 0x86, 0x9a, 0x65, 0x53, 0xb5, 0x46, 0x2a, 0xa3, 0xa7, 0x6a,
	0x8d, 0x74, 0x82, 0x8e, 0x0b, 0x43, 0x22, 0x45, 0xa6, 0x0a, 0x43, 0x3a, 0x09, 0xa7, 0x0a, 0x43,
	0x56, 0x7e, 0x2d, 0x52, 0xe7, 0x13, 0x09, 0x4d, 0x65, 0xdf, 0x52, 0xea, 0x3c, 0x3d, 0xe5, 0x08,
	0x9a, 0x93, 0x33, 0x1c, 0xe4, 0x1d, 0x35, 0x28, 0x9b, 0x92, 0x83, 0x69, 0xde, 0x99, 0x15, 0x55,
	0x12, 0xeb, 0x44, 0xa6, 0x28, 0x25, 0xd6, 0xe9, 0x94, 0x53, 0x4a, 0xac, 0x33, 0x92, 0x4d, 0xda,
	0x1c, 0xf1, 0xe1, 0xd2, 0xc4, 0xb4, 0x0a, 0xb9, 0xad, 0x3e, 0xc6, 0x9a, 0x9c, 0xbc, 0x69, 0xbe,
	0x33, 0x23, 0x26, 0xae, 0xf9, 0x47, 0x39, 0xb8, 0x3a, 0x3d, 0x89, 0x41, 0xde, 0x7d, 0x93, 0x84,
	0xc7, 0xcb, 0xe6, 0xd6, 0x9b, 0xe5, 0x47, 0xb8, 0x3d, 0x55, 0xc2, 0x6d, 0xd5, 0x9e, 0x26, 0xc3,
	0xf9, 0xe6, 0x95, 0x29, 0xbd, 0x91, 0x2c, 0xcb, 0x91, 0xad, 0x2a, 0xcb, 0x89, 0x28, 0x5c, 0x95,
	0xe5, 0x54, 0x40, 0x3c, 0x47, 0x4e, 0x60, 0x3d, 0xf3, 0x8f, 0x0b, 0x88, 0x22, 0xa7, 0x93, 0xfe,
	0x16, 0xa1, 0xf9, 0xf6, 0x0c, 0x58, 0x59, 0x5a, 0x2e, 0x7e, 0x7d, 0x32, 0x51, 0xcb, 0x49, 0xe1,
	0xf5, 0x64, 0x2d, 0x27, 0xc7, 0xcd, 0xdc, 0x68, 0x67, 0x44, 0x46, 0xaa, 0xd1, 0xce, 0x0e, 0xb5,
	0x54, 0xa3, 0x3d, 0x21, 0xbc, 0xd2, 0xe6, 0xc8, 0x00, 0x73, 0x00, 0x59, 0xbf, 0xdd, 0x21, 0x3f,
	0x48, 0x7b, 0x69, 0x59, 0x3f, 0xac, 0x6a, 0xde, 0x9a, 0x09, 0x8f, 0xad, 0xf6, 0x68, 0xeb, 0x67,
	0x3f, 0xec, 0xbb, 0x03, 0xd3, 0xe9, 0x6f, 0xfd, 0xe8, 0x7e, 0x18, 0x6e, 0x59, 0xee, 0xf0, 0x3d,
	0xfc, 0xd7, 0x0a, 0xcb, 0x1d, 0xbc, 0x17, 0x50, 0xff, 0xcc, 0xb6, 0x68, 0x20, 0xfd, 0xa5, 0xc5,
	0x71, 0x11, 0x7b, 0x1f, 0xfc, 0x5f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x58, 0xb3, 0xaf, 0x0f, 0xf2,
	0x42, 0x00, 0x00,
}
