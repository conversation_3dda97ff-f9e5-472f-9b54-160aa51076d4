// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/music-topic-channel/music-topic-channel.proto

package music_topic_channel

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockMusicChannelClient is a mock of MusicChannelClient interface.
type MockMusicChannelClient struct {
	ctrl     *gomock.Controller
	recorder *MockMusicChannelClientMockRecorder
}

// MockMusicChannelClientMockRecorder is the mock recorder for MockMusicChannelClient.
type MockMusicChannelClientMockRecorder struct {
	mock *MockMusicChannelClient
}

// NewMockMusicChannelClient creates a new mock instance.
func NewMockMusicChannelClient(ctrl *gomock.Controller) *MockMusicChannelClient {
	mock := &MockMusicChannelClient{ctrl: ctrl}
	mock.recorder = &MockMusicChannelClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMusicChannelClient) EXPECT() *MockMusicChannelClientMockRecorder {
	return m.recorder
}

// AddTemporaryChannel mocks base method.
func (m *MockMusicChannelClient) AddTemporaryChannel(ctx context.Context, in *AddTemporaryChannelReq, opts ...grpc.CallOption) (*AddTemporaryChannelResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddTemporaryChannel", varargs...)
	ret0, _ := ret[0].(*AddTemporaryChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddTemporaryChannel indicates an expected call of AddTemporaryChannel.
func (mr *MockMusicChannelClientMockRecorder) AddTemporaryChannel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddTemporaryChannel", reflect.TypeOf((*MockMusicChannelClient)(nil).AddTemporaryChannel), varargs...)
}

// BatchFilterIdsByGameCard mocks base method.
func (m *MockMusicChannelClient) BatchFilterIdsByGameCard(ctx context.Context, in *BatchFilterIdsByGameCardReq, opts ...grpc.CallOption) (*BatchFilterIdsByGameCardResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchFilterIdsByGameCard", varargs...)
	ret0, _ := ret[0].(*BatchFilterIdsByGameCardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchFilterIdsByGameCard indicates an expected call of BatchFilterIdsByGameCard.
func (mr *MockMusicChannelClientMockRecorder) BatchFilterIdsByGameCard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchFilterIdsByGameCard", reflect.TypeOf((*MockMusicChannelClient)(nil).BatchFilterIdsByGameCard), varargs...)
}

// BatchHighQualityChannels mocks base method.
func (m *MockMusicChannelClient) BatchHighQualityChannels(ctx context.Context, in *BatchHighQualityChannelsReq, opts ...grpc.CallOption) (*BatchHighQualityChannelsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchHighQualityChannels", varargs...)
	ret0, _ := ret[0].(*BatchHighQualityChannelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchHighQualityChannels indicates an expected call of BatchHighQualityChannels.
func (mr *MockMusicChannelClientMockRecorder) BatchHighQualityChannels(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchHighQualityChannels", reflect.TypeOf((*MockMusicChannelClient)(nil).BatchHighQualityChannels), varargs...)
}

// BatchIsHighQualityChannels mocks base method.
func (m *MockMusicChannelClient) BatchIsHighQualityChannels(ctx context.Context, in *BatchIsHighQualityChannelsReq, opts ...grpc.CallOption) (*BatchIsHighQualityChannelsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchIsHighQualityChannels", varargs...)
	ret0, _ := ret[0].(*BatchIsHighQualityChannelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchIsHighQualityChannels indicates an expected call of BatchIsHighQualityChannels.
func (mr *MockMusicChannelClientMockRecorder) BatchIsHighQualityChannels(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchIsHighQualityChannels", reflect.TypeOf((*MockMusicChannelClient)(nil).BatchIsHighQualityChannels), varargs...)
}

// BatchIsPublishing mocks base method.
func (m *MockMusicChannelClient) BatchIsPublishing(ctx context.Context, in *BatchIsPublishingReq, opts ...grpc.CallOption) (*BatchIsPublishingResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchIsPublishing", varargs...)
	ret0, _ := ret[0].(*BatchIsPublishingResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchIsPublishing indicates an expected call of BatchIsPublishing.
func (mr *MockMusicChannelClientMockRecorder) BatchIsPublishing(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchIsPublishing", reflect.TypeOf((*MockMusicChannelClient)(nil).BatchIsPublishing), varargs...)
}

// DelPublishHotRcmd mocks base method.
func (m *MockMusicChannelClient) DelPublishHotRcmd(ctx context.Context, in *DelPublishHotRcmdReq, opts ...grpc.CallOption) (*DelPublishHotRcmdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelPublishHotRcmd", varargs...)
	ret0, _ := ret[0].(*DelPublishHotRcmdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPublishHotRcmd indicates an expected call of DelPublishHotRcmd.
func (mr *MockMusicChannelClientMockRecorder) DelPublishHotRcmd(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPublishHotRcmd", reflect.TypeOf((*MockMusicChannelClient)(nil).DelPublishHotRcmd), varargs...)
}

// DisappearChannel mocks base method.
func (m *MockMusicChannelClient) DisappearChannel(ctx context.Context, in *DisappearChannelReq, opts ...grpc.CallOption) (*DisappearChannelResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DisappearChannel", varargs...)
	ret0, _ := ret[0].(*DisappearChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DisappearChannel indicates an expected call of DisappearChannel.
func (mr *MockMusicChannelClientMockRecorder) DisappearChannel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisappearChannel", reflect.TypeOf((*MockMusicChannelClient)(nil).DisappearChannel), varargs...)
}

// DismissMusicChannel mocks base method.
func (m *MockMusicChannelClient) DismissMusicChannel(ctx context.Context, in *DismissMusicChannelReq, opts ...grpc.CallOption) (*DismissMusicChannelResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DismissMusicChannel", varargs...)
	ret0, _ := ret[0].(*DismissMusicChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DismissMusicChannel indicates an expected call of DismissMusicChannel.
func (mr *MockMusicChannelClientMockRecorder) DismissMusicChannel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DismissMusicChannel", reflect.TypeOf((*MockMusicChannelClient)(nil).DismissMusicChannel), varargs...)
}

// FreezeChannel mocks base method.
func (m *MockMusicChannelClient) FreezeChannel(ctx context.Context, in *FreezeChannelReq, opts ...grpc.CallOption) (*FreezeChannelResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FreezeChannel", varargs...)
	ret0, _ := ret[0].(*FreezeChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FreezeChannel indicates an expected call of FreezeChannel.
func (mr *MockMusicChannelClientMockRecorder) FreezeChannel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreezeChannel", reflect.TypeOf((*MockMusicChannelClient)(nil).FreezeChannel), varargs...)
}

// GetChannelFreezeInfo mocks base method.
func (m *MockMusicChannelClient) GetChannelFreezeInfo(ctx context.Context, in *GetChannelFreezeInfoReq, opts ...grpc.CallOption) (*GetChannelFreezeInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelFreezeInfo", varargs...)
	ret0, _ := ret[0].(*GetChannelFreezeInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelFreezeInfo indicates an expected call of GetChannelFreezeInfo.
func (mr *MockMusicChannelClientMockRecorder) GetChannelFreezeInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelFreezeInfo", reflect.TypeOf((*MockMusicChannelClient)(nil).GetChannelFreezeInfo), varargs...)
}

// GetChannelRoomUserNumber mocks base method.
func (m *MockMusicChannelClient) GetChannelRoomUserNumber(ctx context.Context, in *GetChannelRoomUserNumberReq, opts ...grpc.CallOption) (*GetChannelRoomUserNumberResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelRoomUserNumber", varargs...)
	ret0, _ := ret[0].(*GetChannelRoomUserNumberResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelRoomUserNumber indicates an expected call of GetChannelRoomUserNumber.
func (mr *MockMusicChannelClientMockRecorder) GetChannelRoomUserNumber(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelRoomUserNumber", reflect.TypeOf((*MockMusicChannelClient)(nil).GetChannelRoomUserNumber), varargs...)
}

// GetExtraHistory mocks base method.
func (m *MockMusicChannelClient) GetExtraHistory(ctx context.Context, in *GetExtraHistoryReq, opts ...grpc.CallOption) (*GetExtraHistoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExtraHistory", varargs...)
	ret0, _ := ret[0].(*GetExtraHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExtraHistory indicates an expected call of GetExtraHistory.
func (mr *MockMusicChannelClientMockRecorder) GetExtraHistory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExtraHistory", reflect.TypeOf((*MockMusicChannelClient)(nil).GetExtraHistory), varargs...)
}

// GetFilterIdsByTabId mocks base method.
func (m *MockMusicChannelClient) GetFilterIdsByTabId(ctx context.Context, in *GetFilterItemsByTabIdReq, opts ...grpc.CallOption) (*GetFilterItemsByTabIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFilterIdsByTabId", varargs...)
	ret0, _ := ret[0].(*GetFilterItemsByTabIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFilterIdsByTabId indicates an expected call of GetFilterIdsByTabId.
func (mr *MockMusicChannelClientMockRecorder) GetFilterIdsByTabId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFilterIdsByTabId", reflect.TypeOf((*MockMusicChannelClient)(nil).GetFilterIdsByTabId), varargs...)
}

// GetFilterInfoByFilterIds mocks base method.
func (m *MockMusicChannelClient) GetFilterInfoByFilterIds(ctx context.Context, in *GetFilterInfoByFilterIdsReq, opts ...grpc.CallOption) (*GetFilterInfoByFilterIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFilterInfoByFilterIds", varargs...)
	ret0, _ := ret[0].(*GetFilterInfoByFilterIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFilterInfoByFilterIds indicates an expected call of GetFilterInfoByFilterIds.
func (mr *MockMusicChannelClientMockRecorder) GetFilterInfoByFilterIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFilterInfoByFilterIds", reflect.TypeOf((*MockMusicChannelClient)(nil).GetFilterInfoByFilterIds), varargs...)
}

// GetMusicChannelByIds mocks base method.
func (m *MockMusicChannelClient) GetMusicChannelByIds(ctx context.Context, in *GetMusicChannelByIdsReq, opts ...grpc.CallOption) (*GetMusicChannelByIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMusicChannelByIds", varargs...)
	ret0, _ := ret[0].(*GetMusicChannelByIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMusicChannelByIds indicates an expected call of GetMusicChannelByIds.
func (mr *MockMusicChannelClientMockRecorder) GetMusicChannelByIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMusicChannelByIds", reflect.TypeOf((*MockMusicChannelClient)(nil).GetMusicChannelByIds), varargs...)
}

// GetMusicChannelFilterV2 mocks base method.
func (m *MockMusicChannelClient) GetMusicChannelFilterV2(ctx context.Context, in *GetMusicChannelFilterV2Req, opts ...grpc.CallOption) (*GetMusicChannelFilterV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMusicChannelFilterV2", varargs...)
	ret0, _ := ret[0].(*GetMusicChannelFilterV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMusicChannelFilterV2 indicates an expected call of GetMusicChannelFilterV2.
func (mr *MockMusicChannelClientMockRecorder) GetMusicChannelFilterV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMusicChannelFilterV2", reflect.TypeOf((*MockMusicChannelClient)(nil).GetMusicChannelFilterV2), varargs...)
}

// GetMusicChannelList mocks base method.
func (m *MockMusicChannelClient) GetMusicChannelList(ctx context.Context, in *GetMusicChannelListReq, opts ...grpc.CallOption) (*GetMusicChannelListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMusicChannelList", varargs...)
	ret0, _ := ret[0].(*GetMusicChannelListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMusicChannelList indicates an expected call of GetMusicChannelList.
func (mr *MockMusicChannelClientMockRecorder) GetMusicChannelList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMusicChannelList", reflect.TypeOf((*MockMusicChannelClient)(nil).GetMusicChannelList), varargs...)
}

// GetMusicFilterItemByIds mocks base method.
func (m *MockMusicChannelClient) GetMusicFilterItemByIds(ctx context.Context, in *GetMusicFilterItemByIdsReq, opts ...grpc.CallOption) (*GetMusicFilterItemByIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMusicFilterItemByIds", varargs...)
	ret0, _ := ret[0].(*GetMusicFilterItemByIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMusicFilterItemByIds indicates an expected call of GetMusicFilterItemByIds.
func (mr *MockMusicChannelClientMockRecorder) GetMusicFilterItemByIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMusicFilterItemByIds", reflect.TypeOf((*MockMusicChannelClient)(nil).GetMusicFilterItemByIds), varargs...)
}

// GetOnlineInfo mocks base method.
func (m *MockMusicChannelClient) GetOnlineInfo(ctx context.Context, in *GetOnlineInfoReq, opts ...grpc.CallOption) (*GetOnlineInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOnlineInfo", varargs...)
	ret0, _ := ret[0].(*GetOnlineInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOnlineInfo indicates an expected call of GetOnlineInfo.
func (mr *MockMusicChannelClientMockRecorder) GetOnlineInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOnlineInfo", reflect.TypeOf((*MockMusicChannelClient)(nil).GetOnlineInfo), varargs...)
}

// GetRcmdPgcChannel mocks base method.
func (m *MockMusicChannelClient) GetRcmdPgcChannel(ctx context.Context, in *GetRcmdPgcChannelReq, opts ...grpc.CallOption) (*GetRcmdPgcChannelResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRcmdPgcChannel", varargs...)
	ret0, _ := ret[0].(*GetRcmdPgcChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRcmdPgcChannel indicates an expected call of GetRcmdPgcChannel.
func (mr *MockMusicChannelClientMockRecorder) GetRcmdPgcChannel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRcmdPgcChannel", reflect.TypeOf((*MockMusicChannelClient)(nil).GetRcmdPgcChannel), varargs...)
}

// GetTabPublishHotRcmd mocks base method.
func (m *MockMusicChannelClient) GetTabPublishHotRcmd(ctx context.Context, in *GetTabPublishHotRcmdReq, opts ...grpc.CallOption) (*GetTabPublishHotRcmdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTabPublishHotRcmd", varargs...)
	ret0, _ := ret[0].(*GetTabPublishHotRcmdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabPublishHotRcmd indicates an expected call of GetTabPublishHotRcmd.
func (mr *MockMusicChannelClientMockRecorder) GetTabPublishHotRcmd(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabPublishHotRcmd", reflect.TypeOf((*MockMusicChannelClient)(nil).GetTabPublishHotRcmd), varargs...)
}

// GetUserSchoolLast mocks base method.
func (m *MockMusicChannelClient) GetUserSchoolLast(ctx context.Context, in *GetUserSchoolLastReq, opts ...grpc.CallOption) (*GetUserSchoolLastResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserSchoolLast", varargs...)
	ret0, _ := ret[0].(*GetUserSchoolLastResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSchoolLast indicates an expected call of GetUserSchoolLast.
func (mr *MockMusicChannelClientMockRecorder) GetUserSchoolLast(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSchoolLast", reflect.TypeOf((*MockMusicChannelClient)(nil).GetUserSchoolLast), varargs...)
}

// InitMusicChannelReleaseInfo mocks base method.
func (m *MockMusicChannelClient) InitMusicChannelReleaseInfo(ctx context.Context, in *InitMusicChannelReleaseInfoReq, opts ...grpc.CallOption) (*InitMusicChannelReleaseInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitMusicChannelReleaseInfo", varargs...)
	ret0, _ := ret[0].(*InitMusicChannelReleaseInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitMusicChannelReleaseInfo indicates an expected call of InitMusicChannelReleaseInfo.
func (mr *MockMusicChannelClientMockRecorder) InitMusicChannelReleaseInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitMusicChannelReleaseInfo", reflect.TypeOf((*MockMusicChannelClient)(nil).InitMusicChannelReleaseInfo), varargs...)
}

// IsOlderForMusicHomePage mocks base method.
func (m *MockMusicChannelClient) IsOlderForMusicHomePage(ctx context.Context, in *IsOlderForMusicHomePageReq, opts ...grpc.CallOption) (*IsOlderForMusicHomePageResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsOlderForMusicHomePage", varargs...)
	ret0, _ := ret[0].(*IsOlderForMusicHomePageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsOlderForMusicHomePage indicates an expected call of IsOlderForMusicHomePage.
func (mr *MockMusicChannelClientMockRecorder) IsOlderForMusicHomePage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsOlderForMusicHomePage", reflect.TypeOf((*MockMusicChannelClient)(nil).IsOlderForMusicHomePage), varargs...)
}

// IsPublishing mocks base method.
func (m *MockMusicChannelClient) IsPublishing(ctx context.Context, in *IsPublishingReq, opts ...grpc.CallOption) (*IsPublishingResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsPublishing", varargs...)
	ret0, _ := ret[0].(*IsPublishingResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsPublishing indicates an expected call of IsPublishing.
func (mr *MockMusicChannelClientMockRecorder) IsPublishing(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsPublishing", reflect.TypeOf((*MockMusicChannelClient)(nil).IsPublishing), varargs...)
}

// ListHomePageFilterItems mocks base method.
func (m *MockMusicChannelClient) ListHomePageFilterItems(ctx context.Context, in *ListHomePageFilterItemsReq, opts ...grpc.CallOption) (*ListHomePageFilterItemsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListHomePageFilterItems", varargs...)
	ret0, _ := ret[0].(*ListHomePageFilterItemsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListHomePageFilterItems indicates an expected call of ListHomePageFilterItems.
func (mr *MockMusicChannelClientMockRecorder) ListHomePageFilterItems(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListHomePageFilterItems", reflect.TypeOf((*MockMusicChannelClient)(nil).ListHomePageFilterItems), varargs...)
}

// ListMuseSocialCommunityChannels mocks base method.
func (m *MockMusicChannelClient) ListMuseSocialCommunityChannels(ctx context.Context, in *ListMuseSocialCommunityChannelsReq, opts ...grpc.CallOption) (*ListMuseSocialCommunityChannelsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListMuseSocialCommunityChannels", varargs...)
	ret0, _ := ret[0].(*ListMuseSocialCommunityChannelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListMuseSocialCommunityChannels indicates an expected call of ListMuseSocialCommunityChannels.
func (mr *MockMusicChannelClientMockRecorder) ListMuseSocialCommunityChannels(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMuseSocialCommunityChannels", reflect.TypeOf((*MockMusicChannelClient)(nil).ListMuseSocialCommunityChannels), varargs...)
}

// ListMusicChannelViewPbs mocks base method.
func (m *MockMusicChannelClient) ListMusicChannelViewPbs(ctx context.Context, in *ListMusicChannelViewPbsReq, opts ...grpc.CallOption) (*ListMusicChannelViewPbsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListMusicChannelViewPbs", varargs...)
	ret0, _ := ret[0].(*ListMusicChannelViewPbsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListMusicChannelViewPbs indicates an expected call of ListMusicChannelViewPbs.
func (mr *MockMusicChannelClientMockRecorder) ListMusicChannelViewPbs(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMusicChannelViewPbs", reflect.TypeOf((*MockMusicChannelClient)(nil).ListMusicChannelViewPbs), varargs...)
}

// ListMusicChannelViews mocks base method.
func (m *MockMusicChannelClient) ListMusicChannelViews(ctx context.Context, in *ListMusicChannelViewsReq, opts ...grpc.CallOption) (*ListMusicChannelViewsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListMusicChannelViews", varargs...)
	ret0, _ := ret[0].(*ListMusicChannelViewsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListMusicChannelViews indicates an expected call of ListMusicChannelViews.
func (mr *MockMusicChannelClientMockRecorder) ListMusicChannelViews(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMusicChannelViews", reflect.TypeOf((*MockMusicChannelClient)(nil).ListMusicChannelViews), varargs...)
}

// ListMusicChannelViewsForMusic mocks base method.
func (m *MockMusicChannelClient) ListMusicChannelViewsForMusic(ctx context.Context, in *ListMusicChannelViewsForMusicReq, opts ...grpc.CallOption) (*ListMusicChannelViewsForMusicResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListMusicChannelViewsForMusic", varargs...)
	ret0, _ := ret[0].(*ListMusicChannelViewsForMusicResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListMusicChannelViewsForMusic indicates an expected call of ListMusicChannelViewsForMusic.
func (mr *MockMusicChannelClientMockRecorder) ListMusicChannelViewsForMusic(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMusicChannelViewsForMusic", reflect.TypeOf((*MockMusicChannelClient)(nil).ListMusicChannelViewsForMusic), varargs...)
}

// ListPublishingChannelIds mocks base method.
func (m *MockMusicChannelClient) ListPublishingChannelIds(ctx context.Context, in *ListPublishingChannelIdsReq, opts ...grpc.CallOption) (*ListPublishingChannelIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListPublishingChannelIds", varargs...)
	ret0, _ := ret[0].(*ListPublishingChannelIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPublishingChannelIds indicates an expected call of ListPublishingChannelIds.
func (mr *MockMusicChannelClientMockRecorder) ListPublishingChannelIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPublishingChannelIds", reflect.TypeOf((*MockMusicChannelClient)(nil).ListPublishingChannelIds), varargs...)
}

// SearchHighQualityChannels mocks base method.
func (m *MockMusicChannelClient) SearchHighQualityChannels(ctx context.Context, in *SearchHighQualityChannelsReq, opts ...grpc.CallOption) (*SearchHighQualityChannelsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SearchHighQualityChannels", varargs...)
	ret0, _ := ret[0].(*SearchHighQualityChannelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchHighQualityChannels indicates an expected call of SearchHighQualityChannels.
func (mr *MockMusicChannelClientMockRecorder) SearchHighQualityChannels(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchHighQualityChannels", reflect.TypeOf((*MockMusicChannelClient)(nil).SearchHighQualityChannels), varargs...)
}

// SearchPublishHotRcmd mocks base method.
func (m *MockMusicChannelClient) SearchPublishHotRcmd(ctx context.Context, in *SearchPublishHotRcmdReq, opts ...grpc.CallOption) (*SearchPublishHotRcmdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SearchPublishHotRcmd", varargs...)
	ret0, _ := ret[0].(*SearchPublishHotRcmdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchPublishHotRcmd indicates an expected call of SearchPublishHotRcmd.
func (mr *MockMusicChannelClientMockRecorder) SearchPublishHotRcmd(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchPublishHotRcmd", reflect.TypeOf((*MockMusicChannelClient)(nil).SearchPublishHotRcmd), varargs...)
}

// SetExtraHistory mocks base method.
func (m *MockMusicChannelClient) SetExtraHistory(ctx context.Context, in *SetExtraHistoryReq, opts ...grpc.CallOption) (*SetExtraHistoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetExtraHistory", varargs...)
	ret0, _ := ret[0].(*SetExtraHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetExtraHistory indicates an expected call of SetExtraHistory.
func (mr *MockMusicChannelClientMockRecorder) SetExtraHistory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetExtraHistory", reflect.TypeOf((*MockMusicChannelClient)(nil).SetExtraHistory), varargs...)
}

// SetMusicChannelReleaseInfo mocks base method.
func (m *MockMusicChannelClient) SetMusicChannelReleaseInfo(ctx context.Context, in *SetMusicChannelReleaseInfoReq, opts ...grpc.CallOption) (*SetMusicChannelReleaseInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetMusicChannelReleaseInfo", varargs...)
	ret0, _ := ret[0].(*SetMusicChannelReleaseInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetMusicChannelReleaseInfo indicates an expected call of SetMusicChannelReleaseInfo.
func (mr *MockMusicChannelClientMockRecorder) SetMusicChannelReleaseInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMusicChannelReleaseInfo", reflect.TypeOf((*MockMusicChannelClient)(nil).SetMusicChannelReleaseInfo), varargs...)
}

// SetUserSchoolLast mocks base method.
func (m *MockMusicChannelClient) SetUserSchoolLast(ctx context.Context, in *SetUserSchoolLastReq, opts ...grpc.CallOption) (*SetUserSchoolLastResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetUserSchoolLast", varargs...)
	ret0, _ := ret[0].(*SetUserSchoolLastResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserSchoolLast indicates an expected call of SetUserSchoolLast.
func (mr *MockMusicChannelClientMockRecorder) SetUserSchoolLast(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSchoolLast", reflect.TypeOf((*MockMusicChannelClient)(nil).SetUserSchoolLast), varargs...)
}

// SortPublishHotRcmd mocks base method.
func (m *MockMusicChannelClient) SortPublishHotRcmd(ctx context.Context, in *SortPublishHotRcmdReq, opts ...grpc.CallOption) (*SortPublishHotRcmdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SortPublishHotRcmd", varargs...)
	ret0, _ := ret[0].(*SortPublishHotRcmdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SortPublishHotRcmd indicates an expected call of SortPublishHotRcmd.
func (mr *MockMusicChannelClientMockRecorder) SortPublishHotRcmd(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SortPublishHotRcmd", reflect.TypeOf((*MockMusicChannelClient)(nil).SortPublishHotRcmd), varargs...)
}

// SwitchChannelTab mocks base method.
func (m *MockMusicChannelClient) SwitchChannelTab(ctx context.Context, in *SwitchChannelTabReq, opts ...grpc.CallOption) (*SwitchChannelTabResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SwitchChannelTab", varargs...)
	ret0, _ := ret[0].(*SwitchChannelTabResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SwitchChannelTab indicates an expected call of SwitchChannelTab.
func (mr *MockMusicChannelClientMockRecorder) SwitchChannelTab(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SwitchChannelTab", reflect.TypeOf((*MockMusicChannelClient)(nil).SwitchChannelTab), varargs...)
}

// UnfreezeChannel mocks base method.
func (m *MockMusicChannelClient) UnfreezeChannel(ctx context.Context, in *UnfreezeChannelReq, opts ...grpc.CallOption) (*UnfreezeChannelResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UnfreezeChannel", varargs...)
	ret0, _ := ret[0].(*UnfreezeChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnfreezeChannel indicates an expected call of UnfreezeChannel.
func (mr *MockMusicChannelClientMockRecorder) UnfreezeChannel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnfreezeChannel", reflect.TypeOf((*MockMusicChannelClient)(nil).UnfreezeChannel), varargs...)
}

// UpdateHighQualityChannels mocks base method.
func (m *MockMusicChannelClient) UpdateHighQualityChannels(ctx context.Context, in *UpdateHighQualityChannelsReq, opts ...grpc.CallOption) (*UpdateHighQualityChannelsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateHighQualityChannels", varargs...)
	ret0, _ := ret[0].(*UpdateHighQualityChannelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateHighQualityChannels indicates an expected call of UpdateHighQualityChannels.
func (mr *MockMusicChannelClientMockRecorder) UpdateHighQualityChannels(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateHighQualityChannels", reflect.TypeOf((*MockMusicChannelClient)(nil).UpdateHighQualityChannels), varargs...)
}

// UpsertPublishHotRcmd mocks base method.
func (m *MockMusicChannelClient) UpsertPublishHotRcmd(ctx context.Context, in *UpsertPublishHotRcmdReq, opts ...grpc.CallOption) (*UpsertPublishHotRcmdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpsertPublishHotRcmd", varargs...)
	ret0, _ := ret[0].(*UpsertPublishHotRcmdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertPublishHotRcmd indicates an expected call of UpsertPublishHotRcmd.
func (mr *MockMusicChannelClientMockRecorder) UpsertPublishHotRcmd(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertPublishHotRcmd", reflect.TypeOf((*MockMusicChannelClient)(nil).UpsertPublishHotRcmd), varargs...)
}

// MockMusicChannelServer is a mock of MusicChannelServer interface.
type MockMusicChannelServer struct {
	ctrl     *gomock.Controller
	recorder *MockMusicChannelServerMockRecorder
}

// MockMusicChannelServerMockRecorder is the mock recorder for MockMusicChannelServer.
type MockMusicChannelServerMockRecorder struct {
	mock *MockMusicChannelServer
}

// NewMockMusicChannelServer creates a new mock instance.
func NewMockMusicChannelServer(ctrl *gomock.Controller) *MockMusicChannelServer {
	mock := &MockMusicChannelServer{ctrl: ctrl}
	mock.recorder = &MockMusicChannelServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMusicChannelServer) EXPECT() *MockMusicChannelServerMockRecorder {
	return m.recorder
}

// AddTemporaryChannel mocks base method.
func (m *MockMusicChannelServer) AddTemporaryChannel(ctx context.Context, in *AddTemporaryChannelReq) (*AddTemporaryChannelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddTemporaryChannel", ctx, in)
	ret0, _ := ret[0].(*AddTemporaryChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddTemporaryChannel indicates an expected call of AddTemporaryChannel.
func (mr *MockMusicChannelServerMockRecorder) AddTemporaryChannel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddTemporaryChannel", reflect.TypeOf((*MockMusicChannelServer)(nil).AddTemporaryChannel), ctx, in)
}

// BatchFilterIdsByGameCard mocks base method.
func (m *MockMusicChannelServer) BatchFilterIdsByGameCard(ctx context.Context, in *BatchFilterIdsByGameCardReq) (*BatchFilterIdsByGameCardResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchFilterIdsByGameCard", ctx, in)
	ret0, _ := ret[0].(*BatchFilterIdsByGameCardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchFilterIdsByGameCard indicates an expected call of BatchFilterIdsByGameCard.
func (mr *MockMusicChannelServerMockRecorder) BatchFilterIdsByGameCard(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchFilterIdsByGameCard", reflect.TypeOf((*MockMusicChannelServer)(nil).BatchFilterIdsByGameCard), ctx, in)
}

// BatchHighQualityChannels mocks base method.
func (m *MockMusicChannelServer) BatchHighQualityChannels(ctx context.Context, in *BatchHighQualityChannelsReq) (*BatchHighQualityChannelsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchHighQualityChannels", ctx, in)
	ret0, _ := ret[0].(*BatchHighQualityChannelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchHighQualityChannels indicates an expected call of BatchHighQualityChannels.
func (mr *MockMusicChannelServerMockRecorder) BatchHighQualityChannels(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchHighQualityChannels", reflect.TypeOf((*MockMusicChannelServer)(nil).BatchHighQualityChannels), ctx, in)
}

// BatchIsHighQualityChannels mocks base method.
func (m *MockMusicChannelServer) BatchIsHighQualityChannels(ctx context.Context, in *BatchIsHighQualityChannelsReq) (*BatchIsHighQualityChannelsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchIsHighQualityChannels", ctx, in)
	ret0, _ := ret[0].(*BatchIsHighQualityChannelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchIsHighQualityChannels indicates an expected call of BatchIsHighQualityChannels.
func (mr *MockMusicChannelServerMockRecorder) BatchIsHighQualityChannels(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchIsHighQualityChannels", reflect.TypeOf((*MockMusicChannelServer)(nil).BatchIsHighQualityChannels), ctx, in)
}

// BatchIsPublishing mocks base method.
func (m *MockMusicChannelServer) BatchIsPublishing(ctx context.Context, in *BatchIsPublishingReq) (*BatchIsPublishingResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchIsPublishing", ctx, in)
	ret0, _ := ret[0].(*BatchIsPublishingResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchIsPublishing indicates an expected call of BatchIsPublishing.
func (mr *MockMusicChannelServerMockRecorder) BatchIsPublishing(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchIsPublishing", reflect.TypeOf((*MockMusicChannelServer)(nil).BatchIsPublishing), ctx, in)
}

// DelPublishHotRcmd mocks base method.
func (m *MockMusicChannelServer) DelPublishHotRcmd(ctx context.Context, in *DelPublishHotRcmdReq) (*DelPublishHotRcmdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelPublishHotRcmd", ctx, in)
	ret0, _ := ret[0].(*DelPublishHotRcmdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPublishHotRcmd indicates an expected call of DelPublishHotRcmd.
func (mr *MockMusicChannelServerMockRecorder) DelPublishHotRcmd(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPublishHotRcmd", reflect.TypeOf((*MockMusicChannelServer)(nil).DelPublishHotRcmd), ctx, in)
}

// DisappearChannel mocks base method.
func (m *MockMusicChannelServer) DisappearChannel(ctx context.Context, in *DisappearChannelReq) (*DisappearChannelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DisappearChannel", ctx, in)
	ret0, _ := ret[0].(*DisappearChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DisappearChannel indicates an expected call of DisappearChannel.
func (mr *MockMusicChannelServerMockRecorder) DisappearChannel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisappearChannel", reflect.TypeOf((*MockMusicChannelServer)(nil).DisappearChannel), ctx, in)
}

// DismissMusicChannel mocks base method.
func (m *MockMusicChannelServer) DismissMusicChannel(ctx context.Context, in *DismissMusicChannelReq) (*DismissMusicChannelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DismissMusicChannel", ctx, in)
	ret0, _ := ret[0].(*DismissMusicChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DismissMusicChannel indicates an expected call of DismissMusicChannel.
func (mr *MockMusicChannelServerMockRecorder) DismissMusicChannel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DismissMusicChannel", reflect.TypeOf((*MockMusicChannelServer)(nil).DismissMusicChannel), ctx, in)
}

// FreezeChannel mocks base method.
func (m *MockMusicChannelServer) FreezeChannel(ctx context.Context, in *FreezeChannelReq) (*FreezeChannelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FreezeChannel", ctx, in)
	ret0, _ := ret[0].(*FreezeChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FreezeChannel indicates an expected call of FreezeChannel.
func (mr *MockMusicChannelServerMockRecorder) FreezeChannel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreezeChannel", reflect.TypeOf((*MockMusicChannelServer)(nil).FreezeChannel), ctx, in)
}

// GetChannelFreezeInfo mocks base method.
func (m *MockMusicChannelServer) GetChannelFreezeInfo(ctx context.Context, in *GetChannelFreezeInfoReq) (*GetChannelFreezeInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelFreezeInfo", ctx, in)
	ret0, _ := ret[0].(*GetChannelFreezeInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelFreezeInfo indicates an expected call of GetChannelFreezeInfo.
func (mr *MockMusicChannelServerMockRecorder) GetChannelFreezeInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelFreezeInfo", reflect.TypeOf((*MockMusicChannelServer)(nil).GetChannelFreezeInfo), ctx, in)
}

// GetChannelRoomUserNumber mocks base method.
func (m *MockMusicChannelServer) GetChannelRoomUserNumber(ctx context.Context, in *GetChannelRoomUserNumberReq) (*GetChannelRoomUserNumberResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelRoomUserNumber", ctx, in)
	ret0, _ := ret[0].(*GetChannelRoomUserNumberResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelRoomUserNumber indicates an expected call of GetChannelRoomUserNumber.
func (mr *MockMusicChannelServerMockRecorder) GetChannelRoomUserNumber(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelRoomUserNumber", reflect.TypeOf((*MockMusicChannelServer)(nil).GetChannelRoomUserNumber), ctx, in)
}

// GetExtraHistory mocks base method.
func (m *MockMusicChannelServer) GetExtraHistory(ctx context.Context, in *GetExtraHistoryReq) (*GetExtraHistoryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExtraHistory", ctx, in)
	ret0, _ := ret[0].(*GetExtraHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExtraHistory indicates an expected call of GetExtraHistory.
func (mr *MockMusicChannelServerMockRecorder) GetExtraHistory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExtraHistory", reflect.TypeOf((*MockMusicChannelServer)(nil).GetExtraHistory), ctx, in)
}

// GetFilterIdsByTabId mocks base method.
func (m *MockMusicChannelServer) GetFilterIdsByTabId(ctx context.Context, in *GetFilterItemsByTabIdReq) (*GetFilterItemsByTabIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFilterIdsByTabId", ctx, in)
	ret0, _ := ret[0].(*GetFilterItemsByTabIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFilterIdsByTabId indicates an expected call of GetFilterIdsByTabId.
func (mr *MockMusicChannelServerMockRecorder) GetFilterIdsByTabId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFilterIdsByTabId", reflect.TypeOf((*MockMusicChannelServer)(nil).GetFilterIdsByTabId), ctx, in)
}

// GetFilterInfoByFilterIds mocks base method.
func (m *MockMusicChannelServer) GetFilterInfoByFilterIds(ctx context.Context, in *GetFilterInfoByFilterIdsReq) (*GetFilterInfoByFilterIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFilterInfoByFilterIds", ctx, in)
	ret0, _ := ret[0].(*GetFilterInfoByFilterIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFilterInfoByFilterIds indicates an expected call of GetFilterInfoByFilterIds.
func (mr *MockMusicChannelServerMockRecorder) GetFilterInfoByFilterIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFilterInfoByFilterIds", reflect.TypeOf((*MockMusicChannelServer)(nil).GetFilterInfoByFilterIds), ctx, in)
}

// GetMusicChannelByIds mocks base method.
func (m *MockMusicChannelServer) GetMusicChannelByIds(ctx context.Context, in *GetMusicChannelByIdsReq) (*GetMusicChannelByIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMusicChannelByIds", ctx, in)
	ret0, _ := ret[0].(*GetMusicChannelByIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMusicChannelByIds indicates an expected call of GetMusicChannelByIds.
func (mr *MockMusicChannelServerMockRecorder) GetMusicChannelByIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMusicChannelByIds", reflect.TypeOf((*MockMusicChannelServer)(nil).GetMusicChannelByIds), ctx, in)
}

// GetMusicChannelFilterV2 mocks base method.
func (m *MockMusicChannelServer) GetMusicChannelFilterV2(ctx context.Context, in *GetMusicChannelFilterV2Req) (*GetMusicChannelFilterV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMusicChannelFilterV2", ctx, in)
	ret0, _ := ret[0].(*GetMusicChannelFilterV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMusicChannelFilterV2 indicates an expected call of GetMusicChannelFilterV2.
func (mr *MockMusicChannelServerMockRecorder) GetMusicChannelFilterV2(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMusicChannelFilterV2", reflect.TypeOf((*MockMusicChannelServer)(nil).GetMusicChannelFilterV2), ctx, in)
}

// GetMusicChannelList mocks base method.
func (m *MockMusicChannelServer) GetMusicChannelList(ctx context.Context, in *GetMusicChannelListReq) (*GetMusicChannelListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMusicChannelList", ctx, in)
	ret0, _ := ret[0].(*GetMusicChannelListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMusicChannelList indicates an expected call of GetMusicChannelList.
func (mr *MockMusicChannelServerMockRecorder) GetMusicChannelList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMusicChannelList", reflect.TypeOf((*MockMusicChannelServer)(nil).GetMusicChannelList), ctx, in)
}

// GetMusicFilterItemByIds mocks base method.
func (m *MockMusicChannelServer) GetMusicFilterItemByIds(ctx context.Context, in *GetMusicFilterItemByIdsReq) (*GetMusicFilterItemByIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMusicFilterItemByIds", ctx, in)
	ret0, _ := ret[0].(*GetMusicFilterItemByIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMusicFilterItemByIds indicates an expected call of GetMusicFilterItemByIds.
func (mr *MockMusicChannelServerMockRecorder) GetMusicFilterItemByIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMusicFilterItemByIds", reflect.TypeOf((*MockMusicChannelServer)(nil).GetMusicFilterItemByIds), ctx, in)
}

// GetOnlineInfo mocks base method.
func (m *MockMusicChannelServer) GetOnlineInfo(ctx context.Context, in *GetOnlineInfoReq) (*GetOnlineInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOnlineInfo", ctx, in)
	ret0, _ := ret[0].(*GetOnlineInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOnlineInfo indicates an expected call of GetOnlineInfo.
func (mr *MockMusicChannelServerMockRecorder) GetOnlineInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOnlineInfo", reflect.TypeOf((*MockMusicChannelServer)(nil).GetOnlineInfo), ctx, in)
}

// GetRcmdPgcChannel mocks base method.
func (m *MockMusicChannelServer) GetRcmdPgcChannel(ctx context.Context, in *GetRcmdPgcChannelReq) (*GetRcmdPgcChannelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRcmdPgcChannel", ctx, in)
	ret0, _ := ret[0].(*GetRcmdPgcChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRcmdPgcChannel indicates an expected call of GetRcmdPgcChannel.
func (mr *MockMusicChannelServerMockRecorder) GetRcmdPgcChannel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRcmdPgcChannel", reflect.TypeOf((*MockMusicChannelServer)(nil).GetRcmdPgcChannel), ctx, in)
}

// GetTabPublishHotRcmd mocks base method.
func (m *MockMusicChannelServer) GetTabPublishHotRcmd(ctx context.Context, in *GetTabPublishHotRcmdReq) (*GetTabPublishHotRcmdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTabPublishHotRcmd", ctx, in)
	ret0, _ := ret[0].(*GetTabPublishHotRcmdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabPublishHotRcmd indicates an expected call of GetTabPublishHotRcmd.
func (mr *MockMusicChannelServerMockRecorder) GetTabPublishHotRcmd(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabPublishHotRcmd", reflect.TypeOf((*MockMusicChannelServer)(nil).GetTabPublishHotRcmd), ctx, in)
}

// GetUserSchoolLast mocks base method.
func (m *MockMusicChannelServer) GetUserSchoolLast(ctx context.Context, in *GetUserSchoolLastReq) (*GetUserSchoolLastResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSchoolLast", ctx, in)
	ret0, _ := ret[0].(*GetUserSchoolLastResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSchoolLast indicates an expected call of GetUserSchoolLast.
func (mr *MockMusicChannelServerMockRecorder) GetUserSchoolLast(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSchoolLast", reflect.TypeOf((*MockMusicChannelServer)(nil).GetUserSchoolLast), ctx, in)
}

// InitMusicChannelReleaseInfo mocks base method.
func (m *MockMusicChannelServer) InitMusicChannelReleaseInfo(ctx context.Context, in *InitMusicChannelReleaseInfoReq) (*InitMusicChannelReleaseInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitMusicChannelReleaseInfo", ctx, in)
	ret0, _ := ret[0].(*InitMusicChannelReleaseInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitMusicChannelReleaseInfo indicates an expected call of InitMusicChannelReleaseInfo.
func (mr *MockMusicChannelServerMockRecorder) InitMusicChannelReleaseInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitMusicChannelReleaseInfo", reflect.TypeOf((*MockMusicChannelServer)(nil).InitMusicChannelReleaseInfo), ctx, in)
}

// IsOlderForMusicHomePage mocks base method.
func (m *MockMusicChannelServer) IsOlderForMusicHomePage(ctx context.Context, in *IsOlderForMusicHomePageReq) (*IsOlderForMusicHomePageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsOlderForMusicHomePage", ctx, in)
	ret0, _ := ret[0].(*IsOlderForMusicHomePageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsOlderForMusicHomePage indicates an expected call of IsOlderForMusicHomePage.
func (mr *MockMusicChannelServerMockRecorder) IsOlderForMusicHomePage(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsOlderForMusicHomePage", reflect.TypeOf((*MockMusicChannelServer)(nil).IsOlderForMusicHomePage), ctx, in)
}

// IsPublishing mocks base method.
func (m *MockMusicChannelServer) IsPublishing(ctx context.Context, in *IsPublishingReq) (*IsPublishingResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsPublishing", ctx, in)
	ret0, _ := ret[0].(*IsPublishingResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsPublishing indicates an expected call of IsPublishing.
func (mr *MockMusicChannelServerMockRecorder) IsPublishing(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsPublishing", reflect.TypeOf((*MockMusicChannelServer)(nil).IsPublishing), ctx, in)
}

// ListHomePageFilterItems mocks base method.
func (m *MockMusicChannelServer) ListHomePageFilterItems(ctx context.Context, in *ListHomePageFilterItemsReq) (*ListHomePageFilterItemsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListHomePageFilterItems", ctx, in)
	ret0, _ := ret[0].(*ListHomePageFilterItemsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListHomePageFilterItems indicates an expected call of ListHomePageFilterItems.
func (mr *MockMusicChannelServerMockRecorder) ListHomePageFilterItems(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListHomePageFilterItems", reflect.TypeOf((*MockMusicChannelServer)(nil).ListHomePageFilterItems), ctx, in)
}

// ListMuseSocialCommunityChannels mocks base method.
func (m *MockMusicChannelServer) ListMuseSocialCommunityChannels(ctx context.Context, in *ListMuseSocialCommunityChannelsReq) (*ListMuseSocialCommunityChannelsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListMuseSocialCommunityChannels", ctx, in)
	ret0, _ := ret[0].(*ListMuseSocialCommunityChannelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListMuseSocialCommunityChannels indicates an expected call of ListMuseSocialCommunityChannels.
func (mr *MockMusicChannelServerMockRecorder) ListMuseSocialCommunityChannels(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMuseSocialCommunityChannels", reflect.TypeOf((*MockMusicChannelServer)(nil).ListMuseSocialCommunityChannels), ctx, in)
}

// ListMusicChannelViewPbs mocks base method.
func (m *MockMusicChannelServer) ListMusicChannelViewPbs(ctx context.Context, in *ListMusicChannelViewPbsReq) (*ListMusicChannelViewPbsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListMusicChannelViewPbs", ctx, in)
	ret0, _ := ret[0].(*ListMusicChannelViewPbsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListMusicChannelViewPbs indicates an expected call of ListMusicChannelViewPbs.
func (mr *MockMusicChannelServerMockRecorder) ListMusicChannelViewPbs(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMusicChannelViewPbs", reflect.TypeOf((*MockMusicChannelServer)(nil).ListMusicChannelViewPbs), ctx, in)
}

// ListMusicChannelViews mocks base method.
func (m *MockMusicChannelServer) ListMusicChannelViews(ctx context.Context, in *ListMusicChannelViewsReq) (*ListMusicChannelViewsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListMusicChannelViews", ctx, in)
	ret0, _ := ret[0].(*ListMusicChannelViewsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListMusicChannelViews indicates an expected call of ListMusicChannelViews.
func (mr *MockMusicChannelServerMockRecorder) ListMusicChannelViews(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMusicChannelViews", reflect.TypeOf((*MockMusicChannelServer)(nil).ListMusicChannelViews), ctx, in)
}

// ListMusicChannelViewsForMusic mocks base method.
func (m *MockMusicChannelServer) ListMusicChannelViewsForMusic(ctx context.Context, in *ListMusicChannelViewsForMusicReq) (*ListMusicChannelViewsForMusicResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListMusicChannelViewsForMusic", ctx, in)
	ret0, _ := ret[0].(*ListMusicChannelViewsForMusicResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListMusicChannelViewsForMusic indicates an expected call of ListMusicChannelViewsForMusic.
func (mr *MockMusicChannelServerMockRecorder) ListMusicChannelViewsForMusic(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMusicChannelViewsForMusic", reflect.TypeOf((*MockMusicChannelServer)(nil).ListMusicChannelViewsForMusic), ctx, in)
}

// ListPublishingChannelIds mocks base method.
func (m *MockMusicChannelServer) ListPublishingChannelIds(ctx context.Context, in *ListPublishingChannelIdsReq) (*ListPublishingChannelIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPublishingChannelIds", ctx, in)
	ret0, _ := ret[0].(*ListPublishingChannelIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPublishingChannelIds indicates an expected call of ListPublishingChannelIds.
func (mr *MockMusicChannelServerMockRecorder) ListPublishingChannelIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPublishingChannelIds", reflect.TypeOf((*MockMusicChannelServer)(nil).ListPublishingChannelIds), ctx, in)
}

// SearchHighQualityChannels mocks base method.
func (m *MockMusicChannelServer) SearchHighQualityChannels(ctx context.Context, in *SearchHighQualityChannelsReq) (*SearchHighQualityChannelsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchHighQualityChannels", ctx, in)
	ret0, _ := ret[0].(*SearchHighQualityChannelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchHighQualityChannels indicates an expected call of SearchHighQualityChannels.
func (mr *MockMusicChannelServerMockRecorder) SearchHighQualityChannels(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchHighQualityChannels", reflect.TypeOf((*MockMusicChannelServer)(nil).SearchHighQualityChannels), ctx, in)
}

// SearchPublishHotRcmd mocks base method.
func (m *MockMusicChannelServer) SearchPublishHotRcmd(ctx context.Context, in *SearchPublishHotRcmdReq) (*SearchPublishHotRcmdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchPublishHotRcmd", ctx, in)
	ret0, _ := ret[0].(*SearchPublishHotRcmdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchPublishHotRcmd indicates an expected call of SearchPublishHotRcmd.
func (mr *MockMusicChannelServerMockRecorder) SearchPublishHotRcmd(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchPublishHotRcmd", reflect.TypeOf((*MockMusicChannelServer)(nil).SearchPublishHotRcmd), ctx, in)
}

// SetExtraHistory mocks base method.
func (m *MockMusicChannelServer) SetExtraHistory(ctx context.Context, in *SetExtraHistoryReq) (*SetExtraHistoryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetExtraHistory", ctx, in)
	ret0, _ := ret[0].(*SetExtraHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetExtraHistory indicates an expected call of SetExtraHistory.
func (mr *MockMusicChannelServerMockRecorder) SetExtraHistory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetExtraHistory", reflect.TypeOf((*MockMusicChannelServer)(nil).SetExtraHistory), ctx, in)
}

// SetMusicChannelReleaseInfo mocks base method.
func (m *MockMusicChannelServer) SetMusicChannelReleaseInfo(ctx context.Context, in *SetMusicChannelReleaseInfoReq) (*SetMusicChannelReleaseInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMusicChannelReleaseInfo", ctx, in)
	ret0, _ := ret[0].(*SetMusicChannelReleaseInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetMusicChannelReleaseInfo indicates an expected call of SetMusicChannelReleaseInfo.
func (mr *MockMusicChannelServerMockRecorder) SetMusicChannelReleaseInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMusicChannelReleaseInfo", reflect.TypeOf((*MockMusicChannelServer)(nil).SetMusicChannelReleaseInfo), ctx, in)
}

// SetUserSchoolLast mocks base method.
func (m *MockMusicChannelServer) SetUserSchoolLast(ctx context.Context, in *SetUserSchoolLastReq) (*SetUserSchoolLastResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserSchoolLast", ctx, in)
	ret0, _ := ret[0].(*SetUserSchoolLastResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserSchoolLast indicates an expected call of SetUserSchoolLast.
func (mr *MockMusicChannelServerMockRecorder) SetUserSchoolLast(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSchoolLast", reflect.TypeOf((*MockMusicChannelServer)(nil).SetUserSchoolLast), ctx, in)
}

// SortPublishHotRcmd mocks base method.
func (m *MockMusicChannelServer) SortPublishHotRcmd(ctx context.Context, in *SortPublishHotRcmdReq) (*SortPublishHotRcmdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SortPublishHotRcmd", ctx, in)
	ret0, _ := ret[0].(*SortPublishHotRcmdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SortPublishHotRcmd indicates an expected call of SortPublishHotRcmd.
func (mr *MockMusicChannelServerMockRecorder) SortPublishHotRcmd(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SortPublishHotRcmd", reflect.TypeOf((*MockMusicChannelServer)(nil).SortPublishHotRcmd), ctx, in)
}

// SwitchChannelTab mocks base method.
func (m *MockMusicChannelServer) SwitchChannelTab(ctx context.Context, in *SwitchChannelTabReq) (*SwitchChannelTabResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SwitchChannelTab", ctx, in)
	ret0, _ := ret[0].(*SwitchChannelTabResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SwitchChannelTab indicates an expected call of SwitchChannelTab.
func (mr *MockMusicChannelServerMockRecorder) SwitchChannelTab(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SwitchChannelTab", reflect.TypeOf((*MockMusicChannelServer)(nil).SwitchChannelTab), ctx, in)
}

// UnfreezeChannel mocks base method.
func (m *MockMusicChannelServer) UnfreezeChannel(ctx context.Context, in *UnfreezeChannelReq) (*UnfreezeChannelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnfreezeChannel", ctx, in)
	ret0, _ := ret[0].(*UnfreezeChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnfreezeChannel indicates an expected call of UnfreezeChannel.
func (mr *MockMusicChannelServerMockRecorder) UnfreezeChannel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnfreezeChannel", reflect.TypeOf((*MockMusicChannelServer)(nil).UnfreezeChannel), ctx, in)
}

// UpdateHighQualityChannels mocks base method.
func (m *MockMusicChannelServer) UpdateHighQualityChannels(ctx context.Context, in *UpdateHighQualityChannelsReq) (*UpdateHighQualityChannelsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateHighQualityChannels", ctx, in)
	ret0, _ := ret[0].(*UpdateHighQualityChannelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateHighQualityChannels indicates an expected call of UpdateHighQualityChannels.
func (mr *MockMusicChannelServerMockRecorder) UpdateHighQualityChannels(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateHighQualityChannels", reflect.TypeOf((*MockMusicChannelServer)(nil).UpdateHighQualityChannels), ctx, in)
}

// UpsertPublishHotRcmd mocks base method.
func (m *MockMusicChannelServer) UpsertPublishHotRcmd(ctx context.Context, in *UpsertPublishHotRcmdReq) (*UpsertPublishHotRcmdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertPublishHotRcmd", ctx, in)
	ret0, _ := ret[0].(*UpsertPublishHotRcmdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertPublishHotRcmd indicates an expected call of UpsertPublishHotRcmd.
func (mr *MockMusicChannelServerMockRecorder) UpsertPublishHotRcmd(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertPublishHotRcmd", reflect.TypeOf((*MockMusicChannelServer)(nil).UpsertPublishHotRcmd), ctx, in)
}
