// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-play-view_.proto

package channel_play // import "golang.52tt.com/protocol/app/channel-play"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ChannelViewType int32

const (
	ChannelViewType_ChannelView_Default       ChannelViewType = 0
	ChannelViewType_ChannelView_MOBA          ChannelViewType = 1
	ChannelViewType_ChannelView_Ktv           ChannelViewType = 2
	ChannelViewType_ChannelView_MysteryEscape ChannelViewType = 3
	ChannelViewType_ChanenlView_Marshal       ChannelViewType = 4
)

var ChannelViewType_name = map[int32]string{
	0: "ChannelView_Default",
	1: "ChannelView_MOBA",
	2: "ChannelView_Ktv",
	3: "ChannelView_MysteryEscape",
	4: "ChanenlView_Marshal",
}
var ChannelViewType_value = map[string]int32{
	"ChannelView_Default":       0,
	"ChannelView_MOBA":          1,
	"ChannelView_Ktv":           2,
	"ChannelView_MysteryEscape": 3,
	"ChanenlView_Marshal":       4,
}

func (x ChannelViewType) String() string {
	return proto.EnumName(ChannelViewType_name, int32(x))
}
func (ChannelViewType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__e60ae9ff3933a322, []int{0}
}

type MusicDescIcon int32

const (
	MusicDescIcon_DescIcon_None  MusicDescIcon = 0
	MusicDescIcon_DescIcon_MUSIC MusicDescIcon = 1
	MusicDescIcon_DescIcon_KTV   MusicDescIcon = 2
)

var MusicDescIcon_name = map[int32]string{
	0: "DescIcon_None",
	1: "DescIcon_MUSIC",
	2: "DescIcon_KTV",
}
var MusicDescIcon_value = map[string]int32{
	"DescIcon_None":  0,
	"DescIcon_MUSIC": 1,
	"DescIcon_KTV":   2,
}

func (x MusicDescIcon) String() string {
	return proto.EnumName(MusicDescIcon_name, int32(x))
}
func (MusicDescIcon) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__e60ae9ff3933a322, []int{1}
}

type MarshalType int32

const (
	MarshalType_UNKNOWN_TYPE MarshalType = 0
	MarshalType_JSON_TYPE    MarshalType = 1
	MarshalType_PROTO_TYPE   MarshalType = 2
)

var MarshalType_name = map[int32]string{
	0: "UNKNOWN_TYPE",
	1: "JSON_TYPE",
	2: "PROTO_TYPE",
}
var MarshalType_value = map[string]int32{
	"UNKNOWN_TYPE": 0,
	"JSON_TYPE":    1,
	"PROTO_TYPE":   2,
}

func (x MarshalType) String() string {
	return proto.EnumName(MarshalType_name, int32(x))
}
func (MarshalType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__e60ae9ff3933a322, []int{2}
}

// 提供view的来源标识
type GenViewSource int32

const (
	GenViewSource_FROM_GAME    GenViewSource = 0
	GenViewSource_FROM_MUSIC   GenViewSource = 1
	GenViewSource_FROM_MYSTERY GenViewSource = 2
	GenViewSource_FROM_REVENUE GenViewSource = 3
)

var GenViewSource_name = map[int32]string{
	0: "FROM_GAME",
	1: "FROM_MUSIC",
	2: "FROM_MYSTERY",
	3: "FROM_REVENUE",
}
var GenViewSource_value = map[string]int32{
	"FROM_GAME":    0,
	"FROM_MUSIC":   1,
	"FROM_MYSTERY": 2,
	"FROM_REVENUE": 3,
}

func (x GenViewSource) String() string {
	return proto.EnumName(GenViewSource_name, int32(x))
}
func (GenViewSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__e60ae9ff3933a322, []int{3}
}

// 默认
type ChannelViewDefault struct {
	DescList             []string          `protobuf:"bytes,1,rep,name=desc_list,json=descList,proto3" json:"desc_list,omitempty"`
	TabName              string            `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	CornerImg            string            `protobuf:"bytes,3,opt,name=corner_img,json=cornerImg,proto3" json:"corner_img,omitempty"`
	CornerBgImg          string            `protobuf:"bytes,4,opt,name=corner_bg_img,json=cornerBgImg,proto3" json:"corner_bg_img,omitempty"`
	RoomCondition        string            `protobuf:"bytes,5,opt,name=room_condition,json=roomCondition,proto3" json:"room_condition,omitempty"`
	AudienceInfo         []*UserInfoForImg `protobuf:"bytes,6,rep,name=audience_info,json=audienceInfo,proto3" json:"audience_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ChannelViewDefault) Reset()         { *m = ChannelViewDefault{} }
func (m *ChannelViewDefault) String() string { return proto.CompactTextString(m) }
func (*ChannelViewDefault) ProtoMessage()    {}
func (*ChannelViewDefault) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__e60ae9ff3933a322, []int{0}
}
func (m *ChannelViewDefault) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelViewDefault.Unmarshal(m, b)
}
func (m *ChannelViewDefault) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelViewDefault.Marshal(b, m, deterministic)
}
func (dst *ChannelViewDefault) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelViewDefault.Merge(dst, src)
}
func (m *ChannelViewDefault) XXX_Size() int {
	return xxx_messageInfo_ChannelViewDefault.Size(m)
}
func (m *ChannelViewDefault) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelViewDefault.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelViewDefault proto.InternalMessageInfo

func (m *ChannelViewDefault) GetDescList() []string {
	if m != nil {
		return m.DescList
	}
	return nil
}

func (m *ChannelViewDefault) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *ChannelViewDefault) GetCornerImg() string {
	if m != nil {
		return m.CornerImg
	}
	return ""
}

func (m *ChannelViewDefault) GetCornerBgImg() string {
	if m != nil {
		return m.CornerBgImg
	}
	return ""
}

func (m *ChannelViewDefault) GetRoomCondition() string {
	if m != nil {
		return m.RoomCondition
	}
	return ""
}

func (m *ChannelViewDefault) GetAudienceInfo() []*UserInfoForImg {
	if m != nil {
		return m.AudienceInfo
	}
	return nil
}

// 带区服模式人数的游戏
type ChannelViewMOBA struct {
	DescFirst            []*ChannelViewMOBA_DescAndColor `protobuf:"bytes,1,rep,name=desc_first,json=descFirst,proto3" json:"desc_first,omitempty"`
	DescList             []string                        `protobuf:"bytes,2,rep,name=desc_list,json=descList,proto3" json:"desc_list,omitempty"`
	TabName              string                          `protobuf:"bytes,3,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	CornerImg            string                          `protobuf:"bytes,4,opt,name=corner_img,json=cornerImg,proto3" json:"corner_img,omitempty"`
	CornerBgImg          string                          `protobuf:"bytes,5,opt,name=corner_bg_img,json=cornerBgImg,proto3" json:"corner_bg_img,omitempty"`
	RoomCondition        string                          `protobuf:"bytes,6,opt,name=room_condition,json=roomCondition,proto3" json:"room_condition,omitempty"`
	AudienceInfo         []*UserInfoForImg               `protobuf:"bytes,8,rep,name=audience_info,json=audienceInfo,proto3" json:"audience_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *ChannelViewMOBA) Reset()         { *m = ChannelViewMOBA{} }
func (m *ChannelViewMOBA) String() string { return proto.CompactTextString(m) }
func (*ChannelViewMOBA) ProtoMessage()    {}
func (*ChannelViewMOBA) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__e60ae9ff3933a322, []int{1}
}
func (m *ChannelViewMOBA) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelViewMOBA.Unmarshal(m, b)
}
func (m *ChannelViewMOBA) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelViewMOBA.Marshal(b, m, deterministic)
}
func (dst *ChannelViewMOBA) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelViewMOBA.Merge(dst, src)
}
func (m *ChannelViewMOBA) XXX_Size() int {
	return xxx_messageInfo_ChannelViewMOBA.Size(m)
}
func (m *ChannelViewMOBA) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelViewMOBA.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelViewMOBA proto.InternalMessageInfo

func (m *ChannelViewMOBA) GetDescFirst() []*ChannelViewMOBA_DescAndColor {
	if m != nil {
		return m.DescFirst
	}
	return nil
}

func (m *ChannelViewMOBA) GetDescList() []string {
	if m != nil {
		return m.DescList
	}
	return nil
}

func (m *ChannelViewMOBA) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *ChannelViewMOBA) GetCornerImg() string {
	if m != nil {
		return m.CornerImg
	}
	return ""
}

func (m *ChannelViewMOBA) GetCornerBgImg() string {
	if m != nil {
		return m.CornerBgImg
	}
	return ""
}

func (m *ChannelViewMOBA) GetRoomCondition() string {
	if m != nil {
		return m.RoomCondition
	}
	return ""
}

func (m *ChannelViewMOBA) GetAudienceInfo() []*UserInfoForImg {
	if m != nil {
		return m.AudienceInfo
	}
	return nil
}

type ChannelViewMOBA_DescAndColor struct {
	Desc                 string   `protobuf:"bytes,1,opt,name=desc,proto3" json:"desc,omitempty"`
	MarkBlue             bool     `protobuf:"varint,2,opt,name=mark_blue,json=markBlue,proto3" json:"mark_blue,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelViewMOBA_DescAndColor) Reset()         { *m = ChannelViewMOBA_DescAndColor{} }
func (m *ChannelViewMOBA_DescAndColor) String() string { return proto.CompactTextString(m) }
func (*ChannelViewMOBA_DescAndColor) ProtoMessage()    {}
func (*ChannelViewMOBA_DescAndColor) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__e60ae9ff3933a322, []int{1, 0}
}
func (m *ChannelViewMOBA_DescAndColor) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelViewMOBA_DescAndColor.Unmarshal(m, b)
}
func (m *ChannelViewMOBA_DescAndColor) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelViewMOBA_DescAndColor.Marshal(b, m, deterministic)
}
func (dst *ChannelViewMOBA_DescAndColor) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelViewMOBA_DescAndColor.Merge(dst, src)
}
func (m *ChannelViewMOBA_DescAndColor) XXX_Size() int {
	return xxx_messageInfo_ChannelViewMOBA_DescAndColor.Size(m)
}
func (m *ChannelViewMOBA_DescAndColor) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelViewMOBA_DescAndColor.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelViewMOBA_DescAndColor proto.InternalMessageInfo

func (m *ChannelViewMOBA_DescAndColor) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *ChannelViewMOBA_DescAndColor) GetMarkBlue() bool {
	if m != nil {
		return m.MarkBlue
	}
	return false
}

// 一起K歌,扩列聊天
type ChannelViewKtv struct {
	OrderSongCount       uint32            `protobuf:"varint,1,opt,name=order_song_count,json=orderSongCount,proto3" json:"order_song_count,omitempty"`
	SingSongName         string            `protobuf:"bytes,2,opt,name=sing_song_name,json=singSongName,proto3" json:"sing_song_name,omitempty"`
	TabName              string            `protobuf:"bytes,3,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	CornerImg            string            `protobuf:"bytes,4,opt,name=corner_img,json=cornerImg,proto3" json:"corner_img,omitempty"`
	CornerBgImg          string            `protobuf:"bytes,5,opt,name=corner_bg_img,json=cornerBgImg,proto3" json:"corner_bg_img,omitempty"`
	RoomCondition        string            `protobuf:"bytes,6,opt,name=room_condition,json=roomCondition,proto3" json:"room_condition,omitempty"`
	MusicIcon            MusicDescIcon     `protobuf:"varint,7,opt,name=music_icon,json=musicIcon,proto3,enum=ga.channel_play.MusicDescIcon" json:"music_icon,omitempty"`
	DescList             []string          `protobuf:"bytes,8,rep,name=desc_list,json=descList,proto3" json:"desc_list,omitempty"`
	AudienceInfo         []*UserInfoForImg `protobuf:"bytes,9,rep,name=audience_info,json=audienceInfo,proto3" json:"audience_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ChannelViewKtv) Reset()         { *m = ChannelViewKtv{} }
func (m *ChannelViewKtv) String() string { return proto.CompactTextString(m) }
func (*ChannelViewKtv) ProtoMessage()    {}
func (*ChannelViewKtv) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__e60ae9ff3933a322, []int{2}
}
func (m *ChannelViewKtv) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelViewKtv.Unmarshal(m, b)
}
func (m *ChannelViewKtv) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelViewKtv.Marshal(b, m, deterministic)
}
func (dst *ChannelViewKtv) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelViewKtv.Merge(dst, src)
}
func (m *ChannelViewKtv) XXX_Size() int {
	return xxx_messageInfo_ChannelViewKtv.Size(m)
}
func (m *ChannelViewKtv) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelViewKtv.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelViewKtv proto.InternalMessageInfo

func (m *ChannelViewKtv) GetOrderSongCount() uint32 {
	if m != nil {
		return m.OrderSongCount
	}
	return 0
}

func (m *ChannelViewKtv) GetSingSongName() string {
	if m != nil {
		return m.SingSongName
	}
	return ""
}

func (m *ChannelViewKtv) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *ChannelViewKtv) GetCornerImg() string {
	if m != nil {
		return m.CornerImg
	}
	return ""
}

func (m *ChannelViewKtv) GetCornerBgImg() string {
	if m != nil {
		return m.CornerBgImg
	}
	return ""
}

func (m *ChannelViewKtv) GetRoomCondition() string {
	if m != nil {
		return m.RoomCondition
	}
	return ""
}

func (m *ChannelViewKtv) GetMusicIcon() MusicDescIcon {
	if m != nil {
		return m.MusicIcon
	}
	return MusicDescIcon_DescIcon_None
}

func (m *ChannelViewKtv) GetDescList() []string {
	if m != nil {
		return m.DescList
	}
	return nil
}

func (m *ChannelViewKtv) GetAudienceInfo() []*UserInfoForImg {
	if m != nil {
		return m.AudienceInfo
	}
	return nil
}

type UserInfoForImg struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Username             string   `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Sex                  uint32   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfoForImg) Reset()         { *m = UserInfoForImg{} }
func (m *UserInfoForImg) String() string { return proto.CompactTextString(m) }
func (*UserInfoForImg) ProtoMessage()    {}
func (*UserInfoForImg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__e60ae9ff3933a322, []int{3}
}
func (m *UserInfoForImg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfoForImg.Unmarshal(m, b)
}
func (m *UserInfoForImg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfoForImg.Marshal(b, m, deterministic)
}
func (dst *UserInfoForImg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfoForImg.Merge(dst, src)
}
func (m *UserInfoForImg) XXX_Size() int {
	return xxx_messageInfo_UserInfoForImg.Size(m)
}
func (m *UserInfoForImg) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfoForImg.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfoForImg proto.InternalMessageInfo

func (m *UserInfoForImg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserInfoForImg) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *UserInfoForImg) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

// 密室逃脱
type ChannelViewMysteryEscape struct {
	PlayerInfo           *UserInfoForImg   `protobuf:"bytes,1,opt,name=player_info,json=playerInfo,proto3" json:"player_info,omitempty"`
	Process              string            `protobuf:"bytes,2,opt,name=process,proto3" json:"process,omitempty"`
	AudienceInfo         []*UserInfoForImg `protobuf:"bytes,3,rep,name=audience_info,json=audienceInfo,proto3" json:"audience_info,omitempty"`
	RoomCondition        string            `protobuf:"bytes,4,opt,name=room_condition,json=roomCondition,proto3" json:"room_condition,omitempty"`
	RoomConditionFlag    uint32            `protobuf:"varint,5,opt,name=room_condition_flag,json=roomConditionFlag,proto3" json:"room_condition_flag,omitempty"`
	RoomMode             uint32            `protobuf:"varint,6,opt,name=room_mode,json=roomMode,proto3" json:"room_mode,omitempty"`
	CornerImg            string            `protobuf:"bytes,7,opt,name=corner_img,json=cornerImg,proto3" json:"corner_img,omitempty"`
	AudienceCount        uint32            `protobuf:"varint,8,opt,name=audience_count,json=audienceCount,proto3" json:"audience_count,omitempty"`
	BackgroundImg        string            `protobuf:"bytes,9,opt,name=background_img,json=backgroundImg,proto3" json:"background_img,omitempty"`
	AvatarToast          string            `protobuf:"bytes,10,opt,name=avatar_toast,json=avatarToast,proto3" json:"avatar_toast,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ChannelViewMysteryEscape) Reset()         { *m = ChannelViewMysteryEscape{} }
func (m *ChannelViewMysteryEscape) String() string { return proto.CompactTextString(m) }
func (*ChannelViewMysteryEscape) ProtoMessage()    {}
func (*ChannelViewMysteryEscape) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__e60ae9ff3933a322, []int{4}
}
func (m *ChannelViewMysteryEscape) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelViewMysteryEscape.Unmarshal(m, b)
}
func (m *ChannelViewMysteryEscape) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelViewMysteryEscape.Marshal(b, m, deterministic)
}
func (dst *ChannelViewMysteryEscape) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelViewMysteryEscape.Merge(dst, src)
}
func (m *ChannelViewMysteryEscape) XXX_Size() int {
	return xxx_messageInfo_ChannelViewMysteryEscape.Size(m)
}
func (m *ChannelViewMysteryEscape) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelViewMysteryEscape.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelViewMysteryEscape proto.InternalMessageInfo

func (m *ChannelViewMysteryEscape) GetPlayerInfo() *UserInfoForImg {
	if m != nil {
		return m.PlayerInfo
	}
	return nil
}

func (m *ChannelViewMysteryEscape) GetProcess() string {
	if m != nil {
		return m.Process
	}
	return ""
}

func (m *ChannelViewMysteryEscape) GetAudienceInfo() []*UserInfoForImg {
	if m != nil {
		return m.AudienceInfo
	}
	return nil
}

func (m *ChannelViewMysteryEscape) GetRoomCondition() string {
	if m != nil {
		return m.RoomCondition
	}
	return ""
}

func (m *ChannelViewMysteryEscape) GetRoomConditionFlag() uint32 {
	if m != nil {
		return m.RoomConditionFlag
	}
	return 0
}

func (m *ChannelViewMysteryEscape) GetRoomMode() uint32 {
	if m != nil {
		return m.RoomMode
	}
	return 0
}

func (m *ChannelViewMysteryEscape) GetCornerImg() string {
	if m != nil {
		return m.CornerImg
	}
	return ""
}

func (m *ChannelViewMysteryEscape) GetAudienceCount() uint32 {
	if m != nil {
		return m.AudienceCount
	}
	return 0
}

func (m *ChannelViewMysteryEscape) GetBackgroundImg() string {
	if m != nil {
		return m.BackgroundImg
	}
	return ""
}

func (m *ChannelViewMysteryEscape) GetAvatarToast() string {
	if m != nil {
		return m.AvatarToast
	}
	return ""
}

// 2023.4.4 首页改版后使用，音乐view由音乐业务方提供
type ChannelViewMarshal struct {
	MarshalType          MarshalType `protobuf:"varint,1,opt,name=marshal_type,json=marshalType,proto3,enum=ga.channel_play.MarshalType" json:"marshal_type,omitempty"`
	Content              []byte      `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *ChannelViewMarshal) Reset()         { *m = ChannelViewMarshal{} }
func (m *ChannelViewMarshal) String() string { return proto.CompactTextString(m) }
func (*ChannelViewMarshal) ProtoMessage()    {}
func (*ChannelViewMarshal) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__e60ae9ff3933a322, []int{5}
}
func (m *ChannelViewMarshal) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelViewMarshal.Unmarshal(m, b)
}
func (m *ChannelViewMarshal) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelViewMarshal.Marshal(b, m, deterministic)
}
func (dst *ChannelViewMarshal) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelViewMarshal.Merge(dst, src)
}
func (m *ChannelViewMarshal) XXX_Size() int {
	return xxx_messageInfo_ChannelViewMarshal.Size(m)
}
func (m *ChannelViewMarshal) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelViewMarshal.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelViewMarshal proto.InternalMessageInfo

func (m *ChannelViewMarshal) GetMarshalType() MarshalType {
	if m != nil {
		return m.MarshalType
	}
	return MarshalType_UNKNOWN_TYPE
}

func (m *ChannelViewMarshal) GetContent() []byte {
	if m != nil {
		return m.Content
	}
	return nil
}

func init() {
	proto.RegisterType((*ChannelViewDefault)(nil), "ga.channel_play.ChannelViewDefault")
	proto.RegisterType((*ChannelViewMOBA)(nil), "ga.channel_play.ChannelViewMOBA")
	proto.RegisterType((*ChannelViewMOBA_DescAndColor)(nil), "ga.channel_play.ChannelViewMOBA.DescAndColor")
	proto.RegisterType((*ChannelViewKtv)(nil), "ga.channel_play.ChannelViewKtv")
	proto.RegisterType((*UserInfoForImg)(nil), "ga.channel_play.UserInfoForImg")
	proto.RegisterType((*ChannelViewMysteryEscape)(nil), "ga.channel_play.ChannelViewMysteryEscape")
	proto.RegisterType((*ChannelViewMarshal)(nil), "ga.channel_play.ChannelViewMarshal")
	proto.RegisterEnum("ga.channel_play.ChannelViewType", ChannelViewType_name, ChannelViewType_value)
	proto.RegisterEnum("ga.channel_play.MusicDescIcon", MusicDescIcon_name, MusicDescIcon_value)
	proto.RegisterEnum("ga.channel_play.MarshalType", MarshalType_name, MarshalType_value)
	proto.RegisterEnum("ga.channel_play.GenViewSource", GenViewSource_name, GenViewSource_value)
}

func init() {
	proto.RegisterFile("channel-play-view_.proto", fileDescriptor_channel_play_view__e60ae9ff3933a322)
}

var fileDescriptor_channel_play_view__e60ae9ff3933a322 = []byte{
	// 910 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x56, 0x51, 0x6f, 0xdb, 0xb6,
	0x13, 0x8f, 0x2d, 0x37, 0xb1, 0xcf, 0x96, 0xaa, 0x32, 0x7f, 0xe0, 0xaf, 0x76, 0xeb, 0x96, 0x19,
	0x0b, 0xe0, 0x05, 0x88, 0x03, 0x78, 0xd8, 0xe3, 0xd6, 0x25, 0x8e, 0xd3, 0x65, 0xa9, 0xed, 0x40,
	0x76, 0x32, 0x64, 0x2f, 0x04, 0x4d, 0xd3, 0xaa, 0x50, 0x89, 0x34, 0x24, 0x2a, 0x9d, 0x3f, 0xc1,
	0xf6, 0xb2, 0xef, 0xb4, 0x0f, 0xb6, 0x87, 0x81, 0xa4, 0xed, 0x4a, 0x76, 0xd1, 0x05, 0x79, 0xda,
	0x1b, 0xef, 0x77, 0x3f, 0x1e, 0xef, 0x7e, 0x77, 0xa4, 0x04, 0x1e, 0x7d, 0x4b, 0x38, 0x67, 0xd1,
	0xf1, 0x3c, 0x22, 0x8b, 0xe3, 0xfb, 0x90, 0xbd, 0xc7, 0xed, 0x79, 0x22, 0xa4, 0x40, 0x4f, 0x03,
	0xd2, 0x5e, 0x3a, 0xb1, 0x72, 0x36, 0xff, 0x2e, 0x01, 0xea, 0x1a, 0xe0, 0x36, 0x64, 0xef, 0xcf,
	0xd9, 0x8c, 0x64, 0x91, 0x44, 0x9f, 0x41, 0x6d, 0xca, 0x52, 0x8a, 0xa3, 0x30, 0x95, 0x5e, 0xe9,
	0xc0, 0x6a, 0xd5, 0xfc, 0xaa, 0x02, 0xde, 0x84, 0xa9, 0x44, 0xcf, 0xa1, 0x2a, 0xc9, 0x04, 0x73,
	0x12, 0x33, 0xaf, 0x7c, 0x50, 0x6a, 0xd5, 0xfc, 0x3d, 0x49, 0x26, 0x03, 0x12, 0x33, 0xf4, 0x12,
	0x80, 0x8a, 0x84, 0xb3, 0x04, 0x87, 0x71, 0xe0, 0x59, 0xda, 0x59, 0x33, 0xc8, 0x65, 0x1c, 0xa0,
	0x26, 0xd8, 0x4b, 0xf7, 0x24, 0xd0, 0x8c, 0x8a, 0x66, 0xd4, 0x0d, 0x78, 0x16, 0x28, 0xce, 0x21,
	0x38, 0x89, 0x10, 0x31, 0xa6, 0x82, 0x4f, 0x43, 0x19, 0x0a, 0xee, 0x3d, 0xd1, 0x24, 0x5b, 0xa1,
	0xdd, 0x15, 0x88, 0xce, 0xc1, 0x26, 0xd9, 0x34, 0x64, 0x9c, 0x32, 0x1c, 0xf2, 0x99, 0xf0, 0x76,
	0x0f, 0xac, 0x56, 0xbd, 0xf3, 0x65, 0x7b, 0xa3, 0xc2, 0xf6, 0x4d, 0xca, 0x92, 0x4b, 0x3e, 0x13,
	0x17, 0x42, 0xa5, 0xe0, 0x37, 0x56, 0xbb, 0x14, 0xd6, 0xfc, 0xc3, 0x82, 0xa7, 0xb9, 0xf2, 0xfb,
	0xc3, 0xb3, 0x53, 0xf4, 0x06, 0x40, 0xd7, 0x3e, 0x0b, 0x93, 0x65, 0xf1, 0xf5, 0xce, 0xf1, 0x56,
	0xd8, 0x8d, 0x5d, 0xed, 0x73, 0x96, 0xd2, 0x53, 0x3e, 0xed, 0x8a, 0x48, 0x24, 0xbe, 0x16, 0xef,
	0x42, 0xed, 0x2f, 0x2a, 0x59, 0xfe, 0x84, 0x92, 0xd6, 0xa7, 0x94, 0xac, 0xfc, 0xab, 0x92, 0x4f,
	0x1e, 0xa2, 0xe4, 0xee, 0x83, 0x94, 0xac, 0x3e, 0x42, 0xc9, 0x17, 0xaf, 0xa0, 0x91, 0x97, 0x00,
	0x21, 0xa8, 0xa8, 0x32, 0xbd, 0x92, 0x3e, 0x52, 0xaf, 0x95, 0x16, 0x31, 0x49, 0xde, 0xe1, 0x49,
	0x94, 0x99, 0xc9, 0xa9, 0xfa, 0x55, 0x05, 0x9c, 0x45, 0x19, 0x6b, 0xfe, 0x6e, 0x81, 0x93, 0x13,
	0xf5, 0x4a, 0xde, 0xa3, 0x16, 0xb8, 0x22, 0x99, 0xb2, 0x04, 0xa7, 0x82, 0x07, 0x98, 0x8a, 0x8c,
	0x4b, 0x1d, 0xcf, 0xf6, 0x1d, 0x8d, 0x8f, 0x04, 0x0f, 0xba, 0x0a, 0x45, 0x5f, 0x83, 0x93, 0x86,
	0x3c, 0x30, 0xc4, 0xdc, 0x60, 0x36, 0x14, 0xaa, 0x68, 0x5a, 0xd3, 0xff, 0x84, 0xdc, 0xdf, 0x03,
	0xc4, 0x59, 0x1a, 0x52, 0x1c, 0x52, 0xc1, 0xbd, 0xbd, 0x83, 0x52, 0xcb, 0xe9, 0x7c, 0xb1, 0xa5,
	0x75, 0x5f, 0x51, 0x94, 0xa0, 0x97, 0x54, 0x70, 0xbf, 0xa6, 0x77, 0xa8, 0x65, 0x71, 0x9e, 0xaa,
	0x1b, 0xf3, 0xb4, 0xd5, 0xca, 0xda, 0x63, 0x2e, 0xc5, 0x35, 0x38, 0x45, 0x3f, 0x72, 0xc1, 0xca,
	0xc2, 0xe9, 0x52, 0x7b, 0xb5, 0x44, 0x2f, 0xa0, 0x9a, 0xa5, 0x2c, 0xc9, 0x49, 0xbd, 0xb6, 0x15,
	0x3b, 0x65, 0xbf, 0x69, 0x85, 0x6d, 0x5f, 0x2d, 0x9b, 0x7f, 0x59, 0xe0, 0xe5, 0x2f, 0xcc, 0x22,
	0x95, 0x2c, 0x59, 0xf4, 0x52, 0x4a, 0xe6, 0x0c, 0xfd, 0x08, 0x75, 0x95, 0x93, 0x92, 0x5e, 0xa5,
	0xac, 0x0e, 0x79, 0x40, 0xca, 0x60, 0xf6, 0x28, 0x04, 0x79, 0xb0, 0x37, 0x4f, 0x04, 0x65, 0x69,
	0xba, 0x7a, 0x8f, 0x96, 0xe6, 0xb6, 0x20, 0xd6, 0x23, 0x04, 0xf9, 0x48, 0x67, 0x2b, 0x1f, 0xeb,
	0x6c, 0x1b, 0xf6, 0x8b, 0x34, 0x3c, 0x8b, 0x88, 0x19, 0x15, 0xdb, 0x7f, 0x56, 0xe0, 0x5e, 0x44,
	0x24, 0x50, 0xad, 0xd4, 0xfc, 0x58, 0x4c, 0x99, 0x9e, 0x15, 0xdb, 0xaf, 0x2a, 0xa0, 0x2f, 0xa6,
	0x9b, 0x03, 0xb9, 0xb7, 0x39, 0x90, 0x87, 0xe0, 0xac, 0x0b, 0x33, 0x17, 0xa3, 0xaa, 0x03, 0xac,
	0xcb, 0x35, 0xf7, 0xe2, 0x10, 0x9c, 0x09, 0xa1, 0xef, 0x82, 0x44, 0x64, 0x7c, 0xaa, 0x23, 0xd5,
	0x4c, 0xe6, 0x1f, 0x50, 0x15, 0xed, 0x2b, 0x68, 0x90, 0x7b, 0x22, 0x49, 0x82, 0xa5, 0x20, 0xa9,
	0xf4, 0xc0, 0x4c, 0xb7, 0xc1, 0xc6, 0x0a, 0x6a, 0x8a, 0xc2, 0x77, 0xa2, 0x4f, 0x92, 0xf4, 0x2d,
	0x89, 0xd0, 0x2b, 0x68, 0xc4, 0x66, 0x89, 0xe5, 0x62, 0xce, 0x74, 0xf3, 0x9c, 0xce, 0xe7, 0xdb,
	0xe3, 0x6c, 0x48, 0xe3, 0xc5, 0x9c, 0xf9, 0xf5, 0xf8, 0x83, 0xa1, 0x5a, 0x47, 0x05, 0x97, 0x8c,
	0x4b, 0xdd, 0xba, 0x86, 0xbf, 0x32, 0x8f, 0xfe, 0x2c, 0x15, 0x9e, 0x66, 0xcd, 0xfe, 0x3f, 0xec,
	0xe7, 0x20, 0xbc, 0xfc, 0x5a, 0xb9, 0x3b, 0xe8, 0x7f, 0xe0, 0xe6, 0x1d, 0xea, 0x45, 0x76, 0x4b,
	0x68, 0xbf, 0x10, 0x01, 0x5f, 0xc9, 0x7b, 0xb7, 0x8c, 0x5e, 0xc2, 0xf3, 0x02, 0x35, 0x3f, 0x8b,
	0xae, 0xb5, 0x3a, 0x82, 0xf1, 0xa5, 0xdb, 0xe4, 0xea, 0x56, 0x8e, 0x7e, 0x02, 0xbb, 0x70, 0x29,
	0xd1, 0x33, 0xb0, 0x57, 0x6b, 0x3c, 0x10, 0x9c, 0xb9, 0x3b, 0x08, 0x81, 0xb3, 0x86, 0xfa, 0x37,
	0xa3, 0xcb, 0xae, 0x5b, 0x42, 0xae, 0x79, 0x18, 0x35, 0x76, 0x35, 0xbe, 0x75, 0xcb, 0x47, 0x3f,
	0x40, 0x3d, 0xa7, 0x87, 0x22, 0xdc, 0x0c, 0xae, 0x06, 0xc3, 0x5f, 0x06, 0x78, 0x7c, 0x77, 0xdd,
	0x73, 0x77, 0x90, 0x0d, 0xb5, 0x9f, 0x47, 0xc3, 0xa5, 0x59, 0x42, 0x0e, 0xc0, 0xb5, 0x3f, 0x1c,
	0x0f, 0x8d, 0x5d, 0x3e, 0xf2, 0xc1, 0x7e, 0xcd, 0xb8, 0x4a, 0x6f, 0x24, 0xb2, 0x84, 0x32, 0xc5,
	0xbf, 0xf0, 0x87, 0x7d, 0xfc, 0xfa, 0xb4, 0xaf, 0xb6, 0x3b, 0x00, 0xda, 0xcc, 0x65, 0x60, 0xec,
	0xbb, 0xd1, 0xb8, 0xe7, 0xdf, 0xb9, 0xe5, 0x35, 0xe2, 0xf7, 0x6e, 0x7b, 0x83, 0x9b, 0x9e, 0x6b,
	0x9d, 0xf5, 0xc0, 0xa3, 0x22, 0x6e, 0x2f, 0xc2, 0x85, 0xc8, 0x54, 0xf7, 0xd4, 0x4c, 0x46, 0xe6,
	0xa7, 0xe1, 0xd7, 0x6f, 0x02, 0x11, 0x11, 0x1e, 0xb4, 0xbf, 0xeb, 0x48, 0xd9, 0xa6, 0x22, 0x3e,
	0xd1, 0x30, 0x15, 0xd1, 0x09, 0x99, 0xcf, 0x4f, 0xf2, 0xff, 0x1a, 0x93, 0x5d, 0xed, 0xfa, 0xf6,
	0x9f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xa7, 0x61, 0xcb, 0x24, 0x82, 0x08, 0x00, 0x00,
}
