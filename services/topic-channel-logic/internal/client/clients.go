package client

import (
	openGame "golang.52tt.com/clients/channel-open-game"
	channel_play_tab "golang.52tt.com/clients/channel-play-tab"
	"golang.52tt.com/clients/configserver"
	gameServerV2Client "golang.52tt.com/clients/game-server-v2"
	tcTab "golang.52tt.com/clients/topic-channel/tab"
)

var ConfigServerClient *configserver.Client
var ChannelPlayTabClient *channel_play_tab.Client
var TcTabClient tcTab.IClient
var GameServerCli *gameServerV2Client.Client
var ChannelOpenGameClient *openGame.Client

func Setup(configServerClient *configserver.Client, channelPlayTabClient *channel_play_tab.Client, tcTabClient tcTab.IClient,
	gameServerCli *gameServerV2Client.Client, channelOpenGameClient *openGame.Client) error {
	ConfigServerClient = configServerClient

	ChannelPlayTabClient = channelPlayTabClient

	TcTabClient = tcTabClient

	GameServerCli = gameServerCli

	ChannelOpenGameClient = channelOpenGameClient
	return nil

}
