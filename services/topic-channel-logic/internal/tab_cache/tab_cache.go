package tab_cache

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/monkey-send-chat/monkey_sender"
	"golang.52tt.com/pkg/protocol"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/protocol/common/status"
	openGamePB "golang.52tt.com/protocol/services/channel-open-game"
	channelPlayTabPb "golang.52tt.com/protocol/services/channel-play-tab"
	gameSeverPB "golang.52tt.com/protocol/services/game-server-v2"
	tabpb "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/topic-channel-logic/internal/client"
	"golang.52tt.com/services/topic-channel-logic/internal/conf"
	"strconv"
	"time"
)

var sc *conf.ServiceConfigT

type MinorityGameInfo struct {
	TabInfo   *tabpb.Tab
	GameScore uint32
}

var MinorityGameParentId uint32 //这个是一套环境一个不变的，所以这个就不用锁了

type minorityCache struct {
	minorityGameMap    map[uint32]*MinorityGameInfo
	minorityGameTabMap map[uint32]*tabpb.Tab
}

var minorityGameCache = &minorityCache{
	minorityGameMap: make(map[uint32]*MinorityGameInfo),
}

type tabCache struct {
	tabIdMap          map[uint32]*tabpb.Tab
	roomNameConfigMap map[uint32][]*tabpb.OfficialRoomNameConfig
	categoryMap       map[uint32][]*tabpb.Tab // k:category_id, v:tabs
	// baseBlocksMap 将tabId作为键，以map形式存储这个tab下的所有blockBase
	baseBlocksMap map[uint32][]*tabpb.Block

	//key：tabId value block关联关系
	blockRelationMap map[uint32][]*channel_play.DisplayBlockInfo

	//key:tabId value 白名单uidMap
	tabWhiteListMap map[uint32]map[uint32]bool
	// key:UGameId value:gameInfo
	gameInfoMap map[uint32]*gameSeverPB.ScanGameInfo
	//key UGameId value:tab
	uGameIdTabMap map[uint32]*tabpb.Tab

	//快速匹配配置缓存
	quickMatchConfigs []*channelPlayTabPb.SceneInfo

	miniGameConfMap map[uint32]*topic_channel.MiniGameConfig
}

var tabInfoCache = &tabCache{}

func GetTabIdMap() map[uint32]*tabpb.Tab {
	return tabInfoCache.tabIdMap
}

func GetRoomNameConfigMap() map[uint32][]*tabpb.OfficialRoomNameConfig {
	return tabInfoCache.roomNameConfigMap
}

func GetCategoryMap() map[uint32][]*tabpb.Tab {
	return tabInfoCache.categoryMap

}
func GetCategoryTabMap(categoryId uint32) []*tabpb.Tab {
	r := make([]*tabpb.Tab, 0, len(tabInfoCache.categoryMap[categoryId]))
	if len(tabInfoCache.categoryMap[categoryId]) > 0 {
		r = append(r, tabInfoCache.categoryMap[categoryId]...)
	}
	return r
}

func GetBlockRelationMap() map[uint32][]*channel_play.DisplayBlockInfo {
	return tabInfoCache.blockRelationMap
}

func setTabInfoCache(tmpCache *tabCache) {
	tabInfoCache = tmpCache
}

func GetMinorityGame() map[uint32]*MinorityGameInfo {
	return minorityGameCache.minorityGameMap
}

func GetMinorityGameTab() map[uint32]*tabpb.Tab {
	return minorityGameCache.minorityGameTabMap
}

func timerRefreshTabInfoCache(ctx context.Context) {
	defer func() {
		if err := recover(); err != nil {
			log.ErrorWithCtx(ctx, "panic, err:%v ", err)
		}
	}()
	err := refreshTabInfoCache(ctx, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "timerRefreshTabInfoCache err:%v", err)
	}
}

func SendCacheWarning(src, msg string) {
	sendErr := monkey_sender.GetNumMsgSenderByChatId(sc.GetPublicSwitchConfig().GetFallBackChatId(),
		sc.GetPublicSwitchConfig().GetFallBackWarnDuration()).SendMsg("topic-channel-logic缓存 "+src, msg)
	if sendErr != nil {
		log.Errorf("SendCacheWarning monkey_sender SendCacheWarning err:%v", sendErr)
	}
}

func refreshMinorityGameCache(ctx context.Context, confMap map[uint32]*gameSeverPB.ScanGameInfo) error {
	mgTabs, err := client.TcTabClient.GetMinorityGameTabs(ctx, &tabpb.GetMinorityGameTabsReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshMinorityGameCache GetMinorityGameTabs error: %v", err)
		SendCacheWarning("refreshTabInfoCache GetMinorityGameTabs", fmt.Sprintf("err:%v", err))
		return err
	}
	minorityGameMap := make(map[uint32]*MinorityGameInfo)
	minorityGameTabMap := make(map[uint32]*tabpb.Tab)
	for _, v := range mgTabs.GetChildTab() {
		minorityGameMap[v.GetUGameId()] = &MinorityGameInfo{TabInfo: v}
		minorityGameTabMap[v.GetId()] = v
	}
	MinorityGameParentId = mgTabs.GetParentTab().GetId()

	for k := range minorityGameMap {
		if val, ok := confMap[k]; ok {
			minorityGameMap[k].GameScore = val.GetScore()
		}
	}
	minorityNewCache := &minorityCache{
		minorityGameMap:    minorityGameMap,
		minorityGameTabMap: minorityGameTabMap,
	}
	minorityGameCache = minorityNewCache
	log.InfoWithCtx(ctx, "refreshMinorityGameCache  minorityGameMap:%d, minorityGameTabMap:%d",
		len(minorityGameMap), len(minorityGameTabMap))
	return nil
}

func refreshTabInfoCache(ctx context.Context, bInitial bool) error {
	confMap, serr := client.GameServerCli.GetAllScanGameListConfMap(ctx)
	if serr != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache GetMinorityGameTabs error: %v", serr)
		return serr
	}
	err := refreshMinorityGameCache(ctx, confMap)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache refreshMinorityGameCache error: %v", err)
		return err
	}
	categoryMap := make(map[uint32][]*tabpb.Tab)
	tabResp, err := client.TcTabClient.Tabs(ctx, 0, 1000)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache Tabs error: %v", err)
		SendCacheWarning("refreshTabInfoCache Tabs", fmt.Sprintf("err:%v", err))
		return err
	}
	if len(tabResp.GetTabs()) == 0 {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache Tabs err: len 0")
		return protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError)
	}
	tabIds := make([]uint32, 0)
	tabIdMap := make(map[uint32]*tabpb.Tab, len(tabResp.GetTabs()))
	uGameIdTabMap := make(map[uint32]*tabpb.Tab, len(tabResp.GetTabs()))
	for _, v := range tabResp.GetTabs() {
		//tabNameMap[v.Name] = v
		categoryMap[v.GetCategoryId()] = append(categoryMap[v.GetCategoryId()], v)
		tabIdMap[v.GetId()] = v
		tabIds = append(tabIds, v.GetId())
		uGameIdTabMap[v.GetUGameId()] = v
	}

	roomNameConfigMap := make(map[uint32][]*tabpb.OfficialRoomNameConfig, len(tabResp.GetTabs()))
	config, err := client.TcTabClient.BatchGetOfficialRoomNameConfig(ctx, &tabpb.BatchGetOfficialRoomNameConfigReq{TabIds: []uint32{}})
	if err != nil {
		log.ErrorWithCtx(ctx, "genOfficialRoomNameConfigMap BatchGetOfficialRoomNameConfig err%v", err)
		roomNameConfigMap = tabInfoCache.roomNameConfigMap
	} else {
		for _, v := range config.GetRoomNameConfigForTabs() {
			configList := make([]*tabpb.OfficialRoomNameConfig, 0)
			configList = append(configList, v.GetRoomNameConfigList()...)
			roomNameConfigMap[v.GetTabId()] = configList
		}
	}

	baseBlocksMap, err := genBaseBlocksMap(ctx, tabIds)
	if err != nil {
		SendCacheWarning("refreshTabInfoCache genBaseBlocksMap", fmt.Sprintf("err:%v", err))
		if bInitial {
			log.ErrorWithCtx(ctx, "refreshTabInfoCache update baseBlocksMap err %v", err)
			return protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError)
		} else {
			log.ErrorWithCtx(ctx, "refreshTabInfoCache update baseBlocksMap err %v", err)
			baseBlocksMap = tabInfoCache.baseBlocksMap
		}
	}
	blockRelationMap, err := genBlockRelationMap(ctx, tabIds, baseBlocksMap)
	if err != nil {
		SendCacheWarning("refreshTabInfoCache genBlockRelationMap", fmt.Sprintf("err:%v", err))
		if bInitial {
			//报错不更新，不阻碍其他缓存信息更新,需要告警
			log.ErrorWithCtx(ctx, "UserLine Time Err: refreshTabInfoCache update blockRelationMap len(tabIds:%d) len(baseBlockMap:%d) err %v",
				len(tabIds), len(baseBlocksMap), err)
			return err
		} else {
			//报错不更新，不阻碍其他缓存信息更新,需要告警
			log.ErrorWithCtx(ctx, "UserLine Time Err: refreshTabInfoCache update blockRelationMap len(tabIds:%d) len(baseBlockMap:%d) err %v",
				len(tabIds), len(baseBlocksMap), err)
			blockRelationMap = tabInfoCache.blockRelationMap
		}
	}

	whiteListMap, err := genTabWhiteListMap(ctx)
	if err != nil {
		//报错不更新，不阻碍其他缓存信息更新,需要告警
		log.ErrorWithCtx(ctx, "refreshTabInfoCache update tabWhiteListMap  err %v", err)
		whiteListMap = tabInfoCache.tabWhiteListMap
	}
	var quickMatchConfigs []*channelPlayTabPb.SceneInfo
	quickMatchConfigs, err = genQuickMatchConfigs(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache genQuickMatchConfigs error: %v", err)
		quickMatchConfigs = tabInfoCache.quickMatchConfigs
	}

	var miniGameConfMap map[uint32]*topic_channel.MiniGameConfig
	sgResp, err := client.ChannelOpenGameClient.GetSupportGameList(ctx, &openGamePB.GetSupportGameListReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache GetSupportGameList error: %v", err)
		miniGameConfMap = tabInfoCache.miniGameConfMap
	} else {
		miniGameConfMap = convertMiniGameConf(sgResp.Base)
	}

	log.InfoWithCtx(ctx, "refreshTabInfoCache  tabIdMap:%d, roomNameConfigMap:%d, categoryMap:%d, "+
		"baseBlocksMap：%d,  blockRelationMap:%d, whiteListMap:%d, quickMatchConfigs:%d",
		len(tabIdMap), len(roomNameConfigMap), len(categoryMap), len(baseBlocksMap), len(blockRelationMap), len(whiteListMap),
		len(quickMatchConfigs))

	tmpCache := &tabCache{
		tabIdMap:          tabIdMap,
		roomNameConfigMap: roomNameConfigMap,
		categoryMap:       categoryMap,
		baseBlocksMap:     baseBlocksMap,
		blockRelationMap:  blockRelationMap,
		tabWhiteListMap:   whiteListMap,
		gameInfoMap:       confMap,
		uGameIdTabMap:     uGameIdTabMap,
		quickMatchConfigs: quickMatchConfigs,
		miniGameConfMap:   miniGameConfMap,
	}
	setTabInfoCache(tmpCache)

	return nil
}

func GetMiniGameConfMap() map[uint32]*topic_channel.MiniGameConfig {
	return tabInfoCache.miniGameConfMap
}

func convertMiniGameConf(info []*openGamePB.GameBaseInfo) (miniGameConfMap map[uint32]*topic_channel.MiniGameConfig) {
	miniGameConfMap = make(map[uint32]*topic_channel.MiniGameConfig, 0)
	for _, v := range info {
		tmp := &topic_channel.MiniGameConfig{}
		tmp.GameId = v.GetGameId()
		tmp.GameVersion = v.GetGameVersion()
		tmp.CpId = v.GetCpId()
		tmp.GameName = v.GetGameName()
		tmp.GameUrl = v.GetGameUrl()
		tmp.GamePackage = v.GetGamePackage()
		tmp.EngineVer = v.GetEngineVer()
		tmp.GameMemberCntLimit = v.GetGameMemberCntLimit()
		tmp.EngineType = v.GetEngineType()
		tmp.GameAppLimit = v.GetGameAppLimit()
		tmp.GamePlatformLimit = v.GetGamePlatformLimit()
		tmp.GameDigest = v.GetGameDigest()
		tmp.MiniGameEngine, _ = miniGameConfEngine(v.GetGameExtraProperties())
		tmp.GameResUrl = v.GetGameResUrl()
		tmp.GameResDigest = v.GetGameResDigest()
		tmp.MainPackageUrl = v.GetMainPackageUrl()
		miniGameConfMap[tmp.GetGameId()] = tmp
	}
	return
}

type GameEngine struct {
	OtherEngine map[string]EngineInfo `json:"other_engine"`
}
type EngineInfo struct {
	GameUrl string `json:"gameUrl"`
	Name    string `json:"name"`
	ResUrl  string `json:"resUrl"`
}

func miniGameConfEngine(amazing string) ([]*topic_channel.MiniGameEngine, error) {
	res := make([]*topic_channel.MiniGameEngine, 0)
	if amazing == "" {
		return res, nil
	}
	gm := GameEngine{}
	err := json.Unmarshal([]byte(amazing), &gm)
	if err != nil {
		log.ErrorWithCtx(context.Background(), "MiniGameConfEngine err(%v)", err)
		return nil, err
	}
	for k, g := range gm.OtherEngine {
		gf := &topic_channel.MiniGameEngine{}
		id, _ := strconv.Atoi(k)
		gf.EngineId = uint32(id)
		gf.Name = g.Name
		gf.GameUrl = g.GameUrl
		gf.ResUrl = g.ResUrl
		res = append(res, gf)
	}
	return res, nil
}

func genQuickMatchConfigs(ctx context.Context) ([]*channelPlayTabPb.SceneInfo, error) {
	quickMatchConfigRsp, err := client.ChannelPlayTabClient.GetQuickMatchConfig(ctx, &channelPlayTabPb.GetQuickMatchConfigReq{
		ConfigType: channelPlayTabPb.QuickMatchConfigType_NormalQuickMatchList,
		Page:       1,
		Limit:      50,
	})
	return quickMatchConfigRsp.GetSceneInfo(), err
}
func GetGameInfoMap() map[uint32]*gameSeverPB.ScanGameInfo {
	return tabInfoCache.gameInfoMap
}

func GetUGameIdTabMap() map[uint32]*tabpb.Tab {
	return tabInfoCache.uGameIdTabMap

}

// 玩法白名单缓存,key tabId value uid数组
func genTabWhiteListMap(ctx context.Context) (map[uint32]map[uint32]bool, error) {
	resp, err := client.ChannelPlayTabClient.BatchGetWhiteUidListByTabIds(ctx, &channelPlayTabPb.BatchGetWhiteUidListByTabIdsReq{
		TabIds:  []uint32{},
		Source:  "topic-channel-logic_cache",
		NoCache: true,
	})
	if err != nil {
		return nil, err
	}
	listMap := make(map[uint32]map[uint32]bool, len(resp))
	for k, v := range resp {
		uidMap := make(map[uint32]bool, len(v))
		for _, uid := range v {
			uidMap[uid] = true
		}
		listMap[k] = uidMap
	}
	return listMap, nil
}

func genBlockRelationMap(ctx context.Context, tabIds []uint32, baseBlockMap map[uint32][]*tabpb.Block) (
	map[uint32][]*channel_play.DisplayBlockInfo, error) {
	resp, err := client.TcTabClient.BatchGetBlockRelations(ctx, &tabpb.BatchGetBlockRelationsReq{
		TabId: tabIds,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "genBlockRelationMap len(tabIds):%d err:%v", len(tabIds), err)
		return nil, err
	}
	relations := make(map[uint32][]*channel_play.DisplayBlockInfo, len(resp.GetRelations()))
	for k, blocks := range baseBlockMap {

		relationHandler := NewBlockRelationHandler(ctx, resp.GetRelations()[k].GetDisplayBlockInfo(), blocks)
		relations[k] = relationHandler.GetRelations()
	}
	return relations, nil
}

func genBaseBlocksMap(ctx context.Context, tabIds []uint32) (baseBlocksMap map[uint32][]*tabpb.Block, err error) {
	baseBlocksMap = make(map[uint32][]*tabpb.Block)
	resp, err := client.TcTabClient.BatchGetBlocks(ctx, &tabpb.BatchGetBlocksReq{TabId: tabIds})
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache genBaseBlocksMap err %v", err)
		return baseBlocksMap, err
	}
	if len(resp.GetData()) == 0 {
		log.ErrorWithCtx(ctx, "genBaseBlocksMap Blocks err: len 0")
		return baseBlocksMap, protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError)
	}
	for k, v := range resp.GetData() {
		if _, ok := baseBlocksMap[k]; !ok {
			baseBlocksMap[k] = make([]*tabpb.Block, 0)
			baseBlocksMap[k] = append(baseBlocksMap[k], v.GetBlocks()...)
		}
	}
	return baseBlocksMap, nil
}

func GetBaseBlocksMapCache() map[uint32][]*tabpb.Block {
	return tabInfoCache.baseBlocksMap
}

func GetBaseBlocksByTabId(tabId, clientType uint32) []*tabpb.Block {
	if protocol.IsFastPcClientType(clientType) && tabId == sc.GetPublicSwitchConfig().GetMuseChatTabId() {
		tabId = sc.GetPublicSwitchConfig().GetFastPcChatTabId()
	}
	return tabInfoCache.baseBlocksMap[tabId]
}

// 根据tabId获取对应的block关联关系
func GetBlockRelationByTabId(tabId uint32) []*channel_play.DisplayBlockInfo {
	c := tabInfoCache.blockRelationMap
	if c == nil {
		return nil
	}
	if relations, ok := c[tabId]; ok {
		return relations
	}
	return []*channel_play.DisplayBlockInfo{}
}

func GetWhiteListByTabId(tabId uint32) map[uint32]bool {
	res := make(map[uint32]bool)
	if tabInfoCache.tabWhiteListMap == nil {
		return res
	}
	if uidMap, ok := tabInfoCache.tabWhiteListMap[tabId]; ok {
		res = uidMap
	}
	return res
}

func GetWhiteList() map[uint32]map[uint32]bool {
	return tabInfoCache.tabWhiteListMap
}

func NewCache(ctx context.Context, config *conf.ServiceConfigT) error {
	sc = config
	err := refreshTabInfoCache(ctx, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache error :%v", err)
		return err
	}
	go func() {
		ticker := time.NewTicker(time.Second * 30)
		defer func() {
			ticker.Stop()
		}()

		for range ticker.C {
			refreshCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			timerRefreshTabInfoCache(refreshCtx)
			cancel()
		}
	}()

	return nil
}

func GetQuickMatchConfigs() []*channelPlayTabPb.SceneInfo {
	return tabInfoCache.quickMatchConfigs
}
