package tab_list

import (
	"context"
	"golang.52tt.com/kaihei-pkg/supervision"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol/grpc"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/topic-channel-logic/internal/conf"
	"golang.52tt.com/services/topic-channel-logic/internal/tab_cache"
)

type QuickTabListRes struct {
	ForceInsertTab   *tabPB.Tab   //强插第一位的玩法
	GameCardTabs     []*tabPB.Tab //根据用户拥有的游戏卡获取的玩法
	GameListTabInfos []*tabPB.Tab //用户gamelist对应的玩法
	ConfigTabs       []*tabPB.Tab //配置的玩法
	supervisorInst   *supervision.Supervisory
	sc               *conf.ServiceConfigT
}

const (
	maxTabFromUserTag  = 3
	maxTabFromGameList = 4
)

func (q *QuickTabListRes) GenFinalTabs(ctx context.Context, serviceInfo *grpc.ServiceInfo, tabFilter map[uint32]bool,
	categoryFilter map[uint32]bool) []*tabPB.Tab {
	var res []*tabPB.Tab
	if forceInsertTab := q.filterTabs(ctx, serviceInfo, tabFilter, categoryFilter, q.ForceInsertTab); len(forceInsertTab) != 0 {
		res = append(res, forceInsertTab...)
	} else {
		q.ForceInsertTab = nil
	}
	if gameCardTabs := q.filterTabs(ctx, serviceInfo, tabFilter, categoryFilter, q.GameCardTabs...); len(gameCardTabs) != 0 {
		if len(gameCardTabs) > maxTabFromUserTag {
			gameCardTabs = gameCardTabs[:maxTabFromUserTag]
		}
		res = append(res, gameCardTabs...)
	}
	if gameListTabInfos := q.filterTabs(ctx, serviceInfo, tabFilter, categoryFilter, q.GameListTabInfos...); len(gameListTabInfos) != 0 {
		if len(gameListTabInfos) > maxTabFromGameList {
			gameListTabInfos = gameListTabInfos[:maxTabFromGameList]
		}
		res = append(res, gameListTabInfos...)
	}
	if configTabs := q.filterTabs(ctx, serviceInfo, tabFilter, categoryFilter, q.ConfigTabs...); len(configTabs) != 0 {
		res = append(res, configTabs...)
	}
	//去重
	res = removeRepeatedTab(res)
	return res
}

func (q *QuickTabListRes) filterTabs(ctx context.Context, serviceInfo *grpc.ServiceInfo, tabFilter map[uint32]bool,
	categoryFilter map[uint32]bool, tabs ...*tabPB.Tab) []*tabPB.Tab {
	if len(tabs) == 0 {
		return nil
	}
	tabInfos := make([]*tabPB.Tab, 0, len(tabs))
	repeatedMap := make(map[uint32]bool)
	for _, tabInfo := range tabs {
		if repeatedMap[tabInfo.GetId()] || tabInfo == nil {
			continue
		}
		if tabFilter[tabInfo.GetId()] || categoryFilter[tabInfo.GetCategoryId()] {
			log.InfoWithCtx(ctx, "filterTabs tabFilter/categoryFilter filter tab_id:%d, category_id:%d",
				tabInfo.GetId(), tabInfo.GetCategoryId())
			continue
		}
		if q.supervisorInst.MiniGameStageStrategy(tabInfo, serviceInfo, tab_cache.GetWhiteList()) {
			log.InfoWithCtx(ctx, "filterTabs MiniGameStageStrategy filter tab_id:%d, category_id:%d",
				tabInfo.GetId(), tabInfo.GetCategoryId())
			continue
		}
		repeatedMap[tabInfo.GetId()] = true
		tabInfos = append(tabInfos, tabInfo)
	}
	return tabInfos
}

// 去重
func removeRepeatedTab(tabInfos []*tabPB.Tab) []*tabPB.Tab {
	if len(tabInfos) == 0 {
		return nil
	}
	tabMap := make(map[uint32]bool)
	var res []*tabPB.Tab
	for _, tabInfo := range tabInfos {
		if tabMap[tabInfo.GetId()] {
			continue
		}
		tabMap[tabInfo.GetId()] = true
		res = append(res, tabInfo)
	}
	return res
}
