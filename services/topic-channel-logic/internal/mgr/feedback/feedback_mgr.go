package feedback

import (
	"context"
	"fmt"
	channel_go "golang.52tt.com/protocol/services/channel-go"
	"golang.52tt.com/services/topic-channel-logic/internal/tab_cache"
	"strings"
	"time"

	account_go "golang.52tt.com/clients/account-go"
	device_info_service "golang.52tt.com/clients/datacenter/device-info-service"
	gangup_channel_cli "golang.52tt.com/clients/gangup-channel"
	topicchannel "golang.52tt.com/clients/topic-channel/channel"
	rcmdChannelLabel "golang.52tt.com/clients/topic-channel/rcmd-channel-label"
	tcTab "golang.52tt.com/clients/topic-channel/tab"
	"golang.52tt.com/kaihei-pkg/tab/question"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/app/topic-channel"
	gangup_channel "golang.52tt.com/protocol/services/gangup-channel"
	tcPB "golang.52tt.com/protocol/services/topic_channel/channel"
	"golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/topic-channel-logic/internal/conf"
)

type FeedbackMgr struct {
	sc                     *conf.ServiceConfigT
	topicChannelClient     *topicchannel.Client
	channelClient          channel_go.ChannelGoClient
	gangupChannelClient    *gangup_channel_cli.Client
	rcmdChannelLabelClient *rcmdChannelLabel.Client
	tabClient              *tcTab.Client

	tabQuestionValidators *question.Validators
}

func NewFeedbackMgr(
	sc *conf.ServiceConfigT,
	topicChannelClient *topicchannel.Client,
	channelClient channel_go.ChannelGoClient,
	gangupChannelClient *gangup_channel_cli.Client,
	rcmdChannelLabelClient *rcmdChannelLabel.Client,
	tabClient *tcTab.Client,
	accountClient account_go.IClient,
	deviceInfoClient device_info_service.IClient,
) *FeedbackMgr {
	tabQuestionValidators := question.NewValidators(accountClient, deviceInfoClient)
	return &FeedbackMgr{
		sc:                     sc,
		topicChannelClient:     topicChannelClient,
		channelClient:          channelClient,
		gangupChannelClient:    gangupChannelClient,
		rcmdChannelLabelClient: rcmdChannelLabelClient,
		tabClient:              tabClient,

		tabQuestionValidators: tabQuestionValidators,
	}
}

func (f *FeedbackMgr) GetNegativeFeedBackOption(ctx context.Context, uid, clientType uint32, channelId uint32) (
	[]*pb.NegativeFeedBackOption, error) {

	//options := make([]*pb.NegativeFeedBackOption, 0)
	defaultOption := f.dynamicNegativeFeedBackOption()
	tcResp, rerr := f.topicChannelClient.GetChannelByIds(ctx, &tcPB.GetChannelByIdsReq{
		Ids:       []uint32{channelId},
		Types:     nil,
		ReturnAll: false,
	})
	if rerr != nil {
		log.ErrorWithCtx(ctx, "GetNegativeFeedBackOption GetChannelByIds channelId(%v) error: %v", channelId, rerr)
		return defaultOption, rerr
	}
	if len(tcResp.GetInfo()) == 0 || tcResp.GetInfo()[0].GetTabId() == 0 {
		return defaultOption, nil
	}
	tabId := tcResp.GetInfo()[0].GetTabId()

	releaseCondition := tab_cache.GetBaseBlocksByTabId(tabId, clientType)
	if releaseCondition == nil {
		return defaultOption, nil
	}

	channelResp, err := f.channelClient.GetChannelSimpleInfo(ctx, &channel_go.GetChannelSimpleInfoReq{
		ChannelId: channelId,
		OpUid:     uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNegativeFeedBackOption GetChannelSimpleInfo channelId(%d) err: %v", channelId, err)
		return defaultOption, nil
	}

	handleFeedbackOpts(defaultOption, f.feedbackChannelNameHandler(ctx, uid, channelResp.GetChannelSimple().GetName()), f.feedbackChannelOwnerHandler(ctx))

	blockTitle := skipAllSelectedOption(tcResp.GetInfo()[0].GetBlockOptions(), releaseCondition)
	options := appendNegativeFeedBackOption(blockTitle, defaultOption)
	return options, nil
}

func skipAllSelectedOption(blockOptions []*tcPB.BlockOption, blocks []*tabPB.Block) []*tabPB.Block {
	//用户选择的
	var blockIdx = map[uint32]map[uint32]bool{}
	for _, block := range blockOptions {
		if blockIdx[block.BlockId] == nil {
			blockIdx[block.BlockId] = map[uint32]bool{block.ElemId: true}
		} else {
			blockIdx[block.BlockId][block.ElemId] = true
		}
	}
	//系统预设的
	var presetBlockMap = map[uint32]map[uint32]*tabPB.Elem{}
	for _, block := range blocks {
		for _, elem := range block.Elems {
			if presetBlockMap[block.Id] == nil {
				presetBlockMap[block.Id] = map[uint32]*tabPB.Elem{}
			}
			presetBlockMap[block.Id][elem.Id] = elem
		}
	}
	blockTitle := make([]*tabPB.Block, 0)
	for _, block := range blocks {
		if len(blockTitle) >= 5 { //最多展示五个
			break
		}
		/*		if len(blockIdx[block.Id]) < len(presetBlockMap[block.Id]) { //用户勾选个数比系统预设选项个数少
				blockTitle = append(blockTitle, block)
				continue
			}*/

		for _, elem := range block.Elems { //检查用户勾选的选项是否与预设选项完全相同
			//过滤用户没选的block
			if _, ok := blockIdx[block.GetId()]; !ok {
				continue
			}
			if !blockIdx[block.Id][elem.Id] { //用户未勾选该选项
				//allSelected = false
				blockTitle = append(blockTitle, block)
				break
			}

		}
	}

	return blockTitle
}

func defaultNegativeFeedBackOption() []*pb.NegativeFeedBackOption {
	options := []*pb.NegativeFeedBackOption{
		{
			BlockId:               0,
			Title:                 "房间类型不感兴趣",
			Negative_FeedbackType: pb.NegativeFeedbackType_FeedbackTypeChannelTab,
		},
		{
			BlockId:               0,
			Title:                 "房间名不感兴趣",
			Negative_FeedbackType: pb.NegativeFeedbackType_FeedbackTypeChannelName,
		},
		{
			BlockId:               0,
			Title:                 "不想看到房主",
			Negative_FeedbackType: pb.NegativeFeedbackType_FeedbackTypeChannelOwner,
			Reasons:               []string{"骂人没素质", "炸麦", "踢人", "低俗色情", "陪玩", "不感兴趣合不来"},
		},
	}
	return options
}

func appendNegativeFeedBackOption(blockTitle []*tabPB.Block, options []*pb.NegativeFeedBackOption) []*pb.NegativeFeedBackOption {
	for _, b := range blockTitle {
		title := b.Title
		if title == "人数" {
			title = "组队人数"
		}
		options = append(options, &pb.NegativeFeedBackOption{
			BlockId:               b.Id,
			Title:                 fmt.Sprintf("%s不准确", title),
			Negative_FeedbackType: pb.NegativeFeedbackType_FeedbackTypePublishCond,
		})
	}
	return options
}

func (f *FeedbackMgr) NegativeFeedBack(ctx context.Context, in *pb.NegativeFeedBackReq, reporter uint32) ([]uint32, error) {
	var hitTypes []uint32

	var channelNameKeywords, channelOwnerReasons []string

	chooseBlockMap := make(map[uint32]bool)
	chooseTypeMap := make(map[pb.NegativeFeedbackType]bool)
	for _, option := range in.FeedbackOptions {
		chooseBlockMap[option.GetBlockId()] = true
		chooseTypeMap[option.GetNegative_FeedbackType()] = true

		switch option.Negative_FeedbackType {
		case pb.NegativeFeedbackType_FeedbackTypeChannelName:
			channelNameKeywords = option.GetReasons()
		case pb.NegativeFeedbackType_FeedbackTypeChannelOwner:
			channelOwnerReasons = option.GetReasons()
		default:
			log.DebugWithCtx(ctx, "NegativeFeedBack skip feedback type %d", option.GetNegative_FeedbackType())
		}
	}

	if f.tabQuestionValidators.User(reporter).IsActive().Validate(ctx) {
		hitTypes = f.getReasonHitTypes(ctx, in.GetTabId(), channelOwnerReasons)
	}

	tcResp, rerr := f.topicChannelClient.GetChannelByIds(ctx, &tcPB.GetChannelByIdsReq{
		Ids:       []uint32{in.GetChannelId()},
		Types:     nil,
		ReturnAll: false,
	})
	if rerr != nil {
		log.ErrorWithCtx(ctx, "NegativeFeedBack GetChannelByIds reporter(%d) channelId(%d) error: %v", reporter, in.GetChannelId(), rerr)
		return hitTypes, rerr
	}
	if len(tcResp.GetInfo()) == 0 || tcResp.GetInfo()[0].GetTabId() == 0 || tcResp.GetInfo()[0].GetTabId() != in.GetTabId() {
		//已取消发布或玩法不匹配，则认为是客户端上报的是缓存房间，直接丢弃
		return hitTypes, nil
	}

	blockOptions := make([]*gangup_channel.BlockOption, 0)
	for _, inBlock := range tcResp.GetInfo()[0].GetBlockOptions() {
		if chooseBlockMap[inBlock.GetBlockId()] {
			blockOptions = append(blockOptions, &gangup_channel.BlockOption{
				BlockId: inBlock.GetBlockId(),
				ElemId:  inBlock.GetElemId(),
			})
		}
	}
	chooseType := make([]gangup_channel.NegativeFeedbackType, 0)
	for feedbackType := range chooseTypeMap {
		chooseType = append(chooseType, gangup_channel.NegativeFeedbackType(feedbackType))
	}
	_, err := f.gangupChannelClient.NegativeFeedBack(ctx, &gangup_channel.NegativeFeedBackReq{
		ChannelId:             in.GetChannelId(),
		TabId:                 in.GetTabId(),
		Creator:               in.GetUid(),
		BlockOptions:          blockOptions,
		Name:                  in.GetName(),
		Negative_FeedbackType: chooseType,
		ReporterUid:           reporter,
		ChannelNameKeywords:   channelNameKeywords,
		ChannelOwnerReasons:   channelOwnerReasons,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "NegativeFeedBack NegativeFeedBack reporter(%d) channelId(%d) error: %v", reporter, in.GetChannelId(), err)
		return hitTypes, err
	}
	return hitTypes, nil
}

func (f *FeedbackMgr) getReasonHitTypes(ctx context.Context, tabId uint32, channelOwnerReasons []string) []uint32 {
	var hitTypes []uint32

	resp, err := f.tabClient.GetTabQuestionConfig(ctx, &tabPB.GetTabQuestionConfigReq{
		TabId: tabId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getReasonHitTypes GetTabQuestionConfig tabId(%d) error: %v", tabId, err)
		return hitTypes
	}
	//不想看到房主类型的反馈选项文案100%匹配配置词汇
	if hitKeyWords(resp.GetConfig().GetAgeKeywords(), channelOwnerReasons) {
		hitTypes = append(hitTypes, uint32(pb.FeedbackKeywordType_FEEDBACK_KEYWORD_TYPE_AGE))
	}

	if hitKeyWords(resp.GetConfig().GetGenderKeywords(), channelOwnerReasons) {
		hitTypes = append(hitTypes, uint32(pb.FeedbackKeywordType_FEEDBACK_KEYWORD_TYPE_GENDER))
	}
	return hitTypes
}

func hitKeyWords(keyWords []string, inWords []string) bool {
	if len(keyWords) == 0 || len(inWords) == 0 {
		return false
	}
	for _, v := range inWords {
		for _, key := range keyWords {
			if strings.Contains(v, key) {
				return true
			}
		}
	}
	return false
}

func (f *FeedbackMgr) dynamicNegativeFeedBackOption() []*pb.NegativeFeedBackOption {
	opts := f.sc.GetOption().GetNegFeedbackOpts()
	if len(opts) == 0 {
		return defaultNegativeFeedBackOption()
	}

	options := make([]*pb.NegativeFeedBackOption, 0)
	for _, opt := range opts {
		options = append(options, &pb.NegativeFeedBackOption{
			BlockId:               0,
			Title:                 opt.Title,
			Negative_FeedbackType: pb.NegativeFeedbackType(opt.Type),
			Reasons:               opt.Reasons,
		})
	}

	return options
}

func handleFeedbackOpts(options []*pb.NegativeFeedBackOption, handlers ...func(option *pb.NegativeFeedBackOption)) {
	for _, option := range options {
		for _, handler := range handlers {
			handler(option)
		}
	}
}

func (f *FeedbackMgr) feedbackChannelNameHandler(ctx context.Context, uid uint32, channelName string) func(option *pb.NegativeFeedBackOption) {
	return func(option *pb.NegativeFeedBackOption) {
		if option.GetNegative_FeedbackType() != pb.NegativeFeedbackType_FeedbackTypeChannelName {
			return
		}

		ctx, cancel := context.WithTimeout(ctx, time.Second)
		defer cancel()

		resp, err := f.rcmdChannelLabelClient.CutWord(ctx, &rcmd_channel_label.CutWordReq{
			Uid:  uid,
			Text: channelName,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "feedbackChannelNameHandler uid(%d) channelName(%s) err: %v", uid, channelName, err)
			return
		}

		log.DebugWithCtx(ctx, "feedbackChannelNameHandler channelName(%s) words: %v", channelName, resp.GetLabels())
		option.Reasons = resp.GetLabels()
	}
}

func (f *FeedbackMgr) feedbackChannelOwnerHandler(ctx context.Context) func(option *pb.NegativeFeedBackOption) {
	return func(option *pb.NegativeFeedBackOption) {
		if option.GetNegative_FeedbackType() != pb.NegativeFeedbackType_FeedbackTypeChannelOwner {
			return
		}

		ctx, cancel := context.WithTimeout(ctx, 200*time.Millisecond)
		defer cancel()

		resp, err := f.gangupChannelClient.GetNegativeFeedbackOption(ctx, &gangup_channel.GetNegativeFeedbackOptionReq{})
		if err != nil {
			log.ErrorWithCtx(ctx, "feedbackChannelOwnerHandler GetNegativeFeedbackOption err: %v", err)
			return
		}

		reasons := resp.GetFeedback().GetListOwnerFeedback()
		if len(reasons) > 0 {
			option.Reasons = reasons
		} else {
			log.WarnWithCtx(ctx, "UserLine Time Err:ListOwnerFeedback empty")
		}
	}
}
