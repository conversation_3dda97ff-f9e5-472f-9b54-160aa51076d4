package conf

import (
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"math/rand"
)

type GameControlRule struct {
	Id    uint32 `json:"id"`
	Model string `json:"model"`

	MinWined uint32 `json:"min_wined"`
	MaxWined uint32 `json:"max_wined"`
}

type Option struct {
	ShiftRoomDuration    uint32             `json:"shift_room_duration"`
	ShiftRoomTabs        []uint32           `json:"shift_room_tabs"`
	GameForPeaceTeamIcon string             `json:"game_for_peace_team_icon"`
	GloryOfKingsTeamIcon string             `json:"glory_of_kings_team_icon"`
	GameControlList      []*GameControlRule `json:"game_control_list"`
	MaxRCMDTimeOutMsec   int64              `json:"max_rcmd_time_out_msec"`
	MinRCMDTimeOutMsec   int64              `json:"min_rcmd_time_out_msec"`
	DistributeMusicTabs  []uint32           `json:"distribute_music_tabs"`
	QuickMatchABTestConf AbtestConfig       `json:"quick_match_ab_test_conf"` // ab测试配置
	GloryTabId           uint32             `json:"glory_tab_id"`             //王者荣耀tab id
	PeaceTabId           uint32             `json:"peace_tab_id"`             //和平精英tab id
	SingARoundTabId      uint32             `json:"sing_a_round_tab_id"`
	SingARoundBg         string             `json:"sing_a_round_bg"`
	KTVBg                string             `json:"ktv_bg"`
	ListeningTabId       uint32             `json:"listening_tab_id"`
	OldListeningTabId    uint32             `json:"old_listening_tab_id"`
	ListeningBg          string             `json:"listening_bg"`
	RapTabId             uint32             `json:"rap_tab_id"`
	OldRapTabId          uint32             `json:"old_rap_tab_id"`
	RapBg                string             `json:"rap_bg"`
	FriendTag            string             `json:"friend_tag"`
	RelationTag          string             `json:"relation_tag"`
	NegFeedbackOpts      []*NegFeedbackOpt  `json:"neg_feedback_opts"`      // 负反馈选项
	MysteryPlaceFillTab  []uint32           `json:"mystery_place_fill_tab"` //迷境常玩房间配置tab
	QuickEntryRoomCard   map[string]string  `json:"quick_entry_room_card"`
	// 极速pc分类下玩法跳转页面配置
	FastPCCategoryJumpTypeInfo map[uint32]topic_channel.PcFastJumpType `json:"fast_pc_category_jump_type_info"` // key: categoryId, value: jumpType
}

// NegFeedbackOpt 负反馈选项信息
type NegFeedbackOpt struct {
	Type    uint32   `json:"feedback_type"` // 负反馈类型
	Title   string   `json:"title"`         // 负反馈标题
	Reasons []string `json:"reasons"`       // 反馈原因
}

type AbtestConfig struct {
	Url          string `json:"url"`
	AppId        uint32 `json:"app_id"`
	LayerTag     string `json:"layer_tag"`
	ExptvId      int64  `json:"exptv_id"`
	StartVersion string `json:"start_version"`
}

type GameConfig struct {
	GameID    uint32 `json:"tag_id"`
	GameTitle string `json:"game_title"`
}

/*
	func newOption(configer config.Configer, sectionName string) *Option {
		b := &Option{}
		//err := getObject(configer, sectionName, &b)
		//if err != nil {
		//	log.InfoWithCtx(nil, "getObject %s from config fail: %v", sectionName, err)
		//	return nil
		//}

		return b
	}

	func getObject(configer config.Configer, sectionName string, object interface{}) error {
		i, err := configer.DIY(sectionName)
		if err != nil || i == nil {
			return err
		}

		buf, err := json.Marshal(i)
		if err != nil {
			return err
		}

		err = json.Unmarshal(buf, object)
		if err != nil {
			return err
		}
		return nil
	}
*/

// GetNegFeedbackOpts 负反馈默认配置
func (opt *Option) GetNegFeedbackOpts() []*NegFeedbackOpt {

	var opts []*NegFeedbackOpt
	if opt != nil {
		opts = opt.NegFeedbackOpts
	}
	return opts
}

func (opt *Option) GetRandomMusicRoom() uint32 {
	if opt == nil || opt.DistributeMusicTabs == nil {
		return 0
	}
	tabs := opt.DistributeMusicTabs
	if len(tabs) == 0 {
		return 0
	}

	return tabs[rand.Intn(len(tabs))] //nolint:gosec
}

func (opt *Option) GetQuickEntryRoomCard(gameName string) string {
	if opt == nil || opt.QuickEntryRoomCard == nil {
		return ""
	}
	return opt.QuickEntryRoomCard[gameName]
}

func (opt *Option) GetFastPCCategoryJumpType(categoryId uint32) topic_channel.PcFastJumpType {
	if opt == nil || opt.FastPCCategoryJumpTypeInfo == nil {
		return topic_channel.PcFastJumpType_PC_FAST_JUMP_TYPE_TAB
	}
	return opt.FastPCCategoryJumpTypeInfo[categoryId]
}
