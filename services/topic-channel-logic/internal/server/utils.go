package server

import (
	"context"
	"encoding/json"
	"github.com/jinzhu/copier"
	"golang.52tt.com/services/topic-channel-logic/internal/tab_cache"
	"math/rand"
	"strconv"
	"strings"

	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/protocol/services/topic_channel/channel"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
)

const (
	BlockNoLimit = "不限"
	playServer   = "区服"
	playMode     = "模式"
	playerNum    = "人数"
)

func convertServerLoadMore(more *topic_channel.ListRecommendTopicChannelLoadMore) (*channel.GetListByTabLoadMore, *channel.GetRecommendChannelListLoadMore) {
	var tabLoadMore *channel.GetListByTabLoadMore
	var listLoadMore *channel.GetRecommendChannelListLoadMore
	if more != nil {
		if more.All != nil {
			listLoadMore = &channel.GetRecommendChannelListLoadMore{Num: more.All.Num}
		}
		if more.ByTab != nil {
			if more.ByTab.Newborn == nil && more.ByTab.Sink == nil && more.ByTab.Big == nil {
				// 全部为空就当他是空的
			} else {
				tabLoadMore = &channel.GetListByTabLoadMore{}
				if more.ByTab.Newborn != nil {
					tabLoadMore.Newborn = &channel.GetListByTabLoadMoreItem{
						Cursor:    more.ByTab.Newborn.Cursor,
						LastCount: more.ByTab.Newborn.LastCount,
						LastIndex: more.ByTab.Newborn.LastIndex,
						LastValue: more.ByTab.Newborn.LastValue,
						TheEnd:    more.ByTab.Newborn.TheEnd,
					}
				}

				if more.ByTab.Sink != nil {
					tabLoadMore.Sink = &channel.GetListByTabLoadMoreItem{
						Cursor:    more.ByTab.Sink.Cursor,
						LastCount: more.ByTab.Sink.LastCount,
						LastIndex: more.ByTab.Sink.LastIndex,
						LastValue: more.ByTab.Sink.LastValue,
						TheEnd:    more.ByTab.Sink.TheEnd,
					}
				}

				if more.ByTab.Big != nil {
					tabLoadMore.Big = &channel.GetListByTabLoadMoreItem{
						Cursor:    more.ByTab.Big.Cursor,
						LastCount: more.ByTab.Big.LastCount,
						LastIndex: more.ByTab.Big.LastIndex,
						LastValue: more.ByTab.Big.LastValue,
						TheEnd:    more.ByTab.Big.TheEnd,
					}
				}
			}
		}
	}
	return tabLoadMore, listLoadMore
}

func convertTagLoadMoreV2(more *channel.GetListByTabLoadMore) *topic_channel.ListRecommendTopicChannelLoadMore {
	loadMore := &topic_channel.ListRecommendTopicChannelLoadMore{}
	if more != nil {
		return loadMore
	}
	return nil
}

func convertListLoadMore(more *channel.GetRecommendChannelListLoadMore, page uint32, recommendIds []uint32) *topic_channel.ListRecommendTopicChannelLoadMore {
	var loadMore *topic_channel.ListRecommendTopicChannelLoadMore
	if more != nil {
		loadMore = &topic_channel.ListRecommendTopicChannelLoadMore{
			All: &topic_channel.ListRecommendTopicChannelLoadMore_All{
				Num: page, Recommendation: recommendIds,
			},
		}
	}
	return loadMore
}

func getChannelPara(blockOptions []*channel.BlockOption, blocks []*tabPB.Block) string {
	userSelected := map[string]string{}
	// 用户选择的
	blockIdx := map[uint32]map[uint32]bool{}
	if len(blockOptions) > 0 && len(blocks) > 0 {
		for _, block := range blockOptions {
			if blockIdx[block.BlockId] == nil {
				blockIdx[block.BlockId] = map[uint32]bool{block.ElemId: true}
			} else {
				blockIdx[block.BlockId][block.ElemId] = true
			}
		}

		for _, block := range blocks {
			for _, elem := range block.Elems {
				if blockIdx[block.Id] != nil && blockIdx[block.Id][elem.Id] {
					if userSelected[block.Title] == "" {
						userSelected[block.Title] = elem.Title
					} else {
						userSelected[block.Title] += "," + elem.Title
					}
				}
			}
		}
	}
	return utils.ToJson(userSelected)
}

func getChannelDesc(blockOptions []*channel.BlockOption, blocks []*tabPB.Block) (headDesc, secondDesc string) {
	if len(blockOptions) == 0 || len(blocks) == 0 {
		return
	}
	// 用户选择的
	blockIdx := map[uint32]map[uint32]bool{}
	for _, block := range blockOptions {
		if blockIdx[block.BlockId] == nil {
			blockIdx[block.BlockId] = map[uint32]bool{block.ElemId: true}
		} else {
			blockIdx[block.BlockId][block.ElemId] = true
		}
	}
	// 系统预设的
	presetBlockMap := map[uint32]map[uint32]*tabPB.Elem{}
	for _, block := range blocks {
		for _, elem := range block.Elems {
			if presetBlockMap[block.Id] == nil {
				presetBlockMap[block.Id] = map[uint32]*tabPB.Elem{}
			}
			presetBlockMap[block.Id][elem.Id] = elem
		}
	}

	var headTitle, secondTitle []string
	for _, block := range blocks {
		if len(blockIdx[block.Id]) > 0 {
			allSelected := true
			if len(blockIdx[block.Id]) == len(presetBlockMap[block.Id]) { // 系统预设选项个数与用户勾选个数相同
				for _, elem := range block.Elems { // 检查用户勾选的选项是否与预设选项完全相同
					if !blockIdx[block.Id][elem.Id] { // 用户未勾选该选项
						allSelected = false
						break
					}
				}
				if allSelected {
					if block.ShowRow == 2 {
						secondTitle = append(secondTitle, "不限"+block.Title)
					} else {
						headTitle = append(headTitle, "不限"+block.Title)
					}
				}
			} else {
				allSelected = false
			}
			if !allSelected {
				var idx []int
				for i, elem := range block.Elems {
					if blockIdx[block.Id][elem.Id] { // 获取当前block下所有选中的elem数组的index（不是elem_id）即这个block都勾选了哪几位elem
						idx = append(idx, i)
					}
				}
				var stringSet [][]string
				lastIdx := -1
				for _, i := range idx {
					if lastIdx == -1 { // 将第一个elem的tittle存入stringSet的第一位数组，二维数组的x轴上拼入
						stringSet = append(stringSet, []string{block.Elems[i].Title})
					} else if lastIdx+1 == i { // 如果该elem与上个存入stringSet的elem是连着的
						if len(stringSet[len(stringSet)-1]) < 2 { // stringSet最后一位的数组长度为0或者1
							stringSet[len(stringSet)-1] = append(stringSet[len(stringSet)-1], block.Elems[i].Title) // 二维数组y轴上拼入
						} else {
							stringSet[len(stringSet)-1][1] = block.Elems[i].Title // 二维数组y轴上替换第二位
						}
					} else {
						stringSet = append(stringSet, []string{block.Elems[i].Title})
					}
					lastIdx = i
				}
				var str []string
				for _, slice := range stringSet {
					str = append(str, strings.Join(slice, "-"))
				}
				title := strings.Join(str, "/")
				if block.ShowRow != 2 {
					if title != "" {
						headTitle = append(headTitle, title)
					}
				} else if block.ShowRow == 2 {
					if title != "" {
						secondTitle = append(secondTitle, title)
					}
				}
			}
		}
	}
	headDesc = strings.Join(headTitle, " | ")
	secondDesc = strings.Join(secondTitle, "-")
	return
}

// V5.5.0大改版，开黑列表内描述变动巨大，还不让影响旧版逻辑，只好新增个V2
func getChannelDescV2(ctx context.Context, blockOptions []*channel.BlockOption, blocks []*tabPB.Block) (headDesc, secondDesc, thirdDesc, specialDesc, teamSize string, locationArray []string) {
	if len(blockOptions) == 0 || len(blocks) == 0 {
		return
	}
	// 用户选择的
	blockIdx := map[uint32]map[uint32]bool{}
	for _, block := range blockOptions {
		if blockIdx[block.BlockId] == nil {
			blockIdx[block.BlockId] = map[uint32]bool{block.ElemId: true}
		} else {
			blockIdx[block.BlockId][block.ElemId] = true
		}
	}
	// 系统预设的
	presetBlockMap := map[uint32]map[uint32]*tabPB.Elem{}
	for _, block := range blocks {
		for _, elem := range block.Elems {
			if presetBlockMap[block.Id] == nil {
				presetBlockMap[block.Id] = map[uint32]*tabPB.Elem{}
			}
			presetBlockMap[block.Id][elem.Id] = elem
		}
	}

	var headTitle, secondTitle, thirdTitle []string
	for _, block := range blocks {
		if len(blockIdx[block.Id]) > 0 {
			allSelected := true
			if len(blockIdx[block.Id]) == len(presetBlockMap[block.Id]) { // 系统预设选项个数与用户勾选个数相同
				for _, elem := range block.Elems { // 检查用户勾选的选项是否与预设选项完全相同
					if !blockIdx[block.Id][elem.Id] { // 用户未勾选该选项
						allSelected = false
						break
					}
				}
				if allSelected {
					if block.ShowRow == 4 {
						// 吃鸡地图特殊处理
						headTitle = append(headTitle, "不限")
					} else if block.ShowRow == 2 {
						secondTitle = append(secondTitle, "不限"+block.Title)
					}
					if block.ShowRow == 5 {
						// 分路要全部列出来
						allSelected = false
					}
				}
			} else {
				allSelected = false
			}

			if !allSelected {
				var idx []int
				for i, elem := range block.Elems {
					if blockIdx[block.Id][elem.Id] { // 获取当前block下所有选中的elem数组的index（不是elem_id）即这个block都勾选了哪几位elem
						idx = append(idx, i)
					}
				}
				var stringSet [][]string
				lastIdx := -1
				// 地图和分路特殊处理
				if block.ShowRow != 4 && block.ShowRow != 5 {
					for _, i := range idx {
						if lastIdx == -1 { // 将第一个elem的tittle存入stringSet的第一位数组，二维数组的x轴上拼入
							stringSet = append(stringSet, []string{block.Elems[i].Title})
						} else if lastIdx+1 == i { // 如果该elem与上个存入stringSet的elem是连着的
							if len(stringSet[len(stringSet)-1]) < 2 { // stringSet最后一位的数组长度为0或者1
								stringSet[len(stringSet)-1] = append(stringSet[len(stringSet)-1], block.Elems[i].Title) // 二维数组y轴上拼入
							} else {
								stringSet[len(stringSet)-1][1] = block.Elems[i].Title // 二维数组y轴上替换第二位
							}
						} else {
							stringSet = append(stringSet, []string{block.Elems[i].Title})
						}
						lastIdx = i
					}
				} else {
					for _, i := range idx {
						stringSet = append(stringSet, []string{block.Elems[i].Title})
					}
				}

				if block.Title == "人数" {
					for _, i := range idx {
						teamSize = block.Elems[i].Title
					}
				}

				var str []string
				for _, slice := range stringSet {
					str = append(str, strings.Join(slice, "-"))
				}
				var title string
				if block.ShowRow == 4 {
					title = strings.Join(str, "、")
				} else {
					title = strings.Join(str, "/")
				}

				switch block.ShowRow {
				case 1:
					// 仅扩列聊天用
					if title != "" {
						specialDesc = title
					} else {
						log.ErrorWithCtx(ctx, "getChannelDescV2 ShowRow=1 title is null \n")
					}
				case 2:
					if title != "" {
						secondTitle = append(secondTitle, title)
					}
				case 3:
					thirdTitle = append(thirdTitle, title)
				case 4:
					// 地图信息
					if title != "" {
						headTitle = append(headTitle, title)
					}
				case 5:
					// 分路位置信息
					locationArray = str
				default:
					continue
				}

			}
		}
	}

	headDesc = strings.Join(headTitle, " | ")
	secondDesc = strings.Join(secondTitle, "-")

	var dis, mode string
	for _, v := range thirdTitle {
		if strings.Contains(v, "QQ") {
			dis = "QQ"
		} else if strings.Contains(v, "微信") {
			dis = "微信"
		} else if strings.Contains(v, "国际") {
			dis = "国际"
		} else if strings.Contains(v, "上分") {
			mode = "上分局"
		} else if strings.Contains(v, "娱乐") {
			mode = "娱乐局"
		}
	}
	thirdDesc = dis + mode
	if secondDesc != "" {
		secondDesc = "想找：" + secondDesc
	}
	return
}

func convertTab(tab *tabPB.Tab) (elem *topic_channel.Tab) {
	if tab != nil {
		elem = &topic_channel.Tab{}
		elem.Id = tab.GetId()
		elem.Name = tab.GetName()
		elem.ImageUri = tab.GetImageUri()
		elem.Version = tab.GetVersion()
		elem.TagId = tab.GetTagId()
		elem.FollowLabelImg = tab.FollowLabelImg
		elem.FollowLabelText = tab.FollowLabelText
		elem.CategoryId = tab.GetCategoryId()
		elem.MiniGameNum = tab.GetMiniGameNum()
		if uint32(tab.GetTabType()) == 0 {
			elem.TabType = topic_channel.Tab_NORMAL
		} else if uint32(tab.GetTabType()) == 1 {
			elem.TabType = topic_channel.Tab_GAME
		} else if uint32(tab.GetTabType()) == 2 {
			elem.TabType = topic_channel.Tab_MINI_GAME
		}

		if int32(tab.GetRoomNameType()) == 0 {
			elem.RoomNameType = topic_channel.Tab_DEFAULT
		} else if int32(tab.GetRoomNameType()) == 1 {
			elem.RoomNameType = topic_channel.Tab_SPLICE
		}

		elem.RoomNameVersion = tab.RoomNameVersion
		// 首页卡片相关
		elem.CardsImageUrl = tab.GetCardsImageUrl()
		elem.MaskLayer = tab.GetMaskLayer()
		elem.TabLabel = topic_channel.LabelType(tab.GetTabLabel())
		elem.RoomLabel = tab.GetRoomLabel()
		elem.CategorySort = tab.GetCategorySort()
		elem.DisplayElem = tab.GetDisplayElem()

		// v5.5.0新增小卡片和创建房间列表
		elem.NewTabCategoryUrl = tab.NewTabCategoryUrl
		elem.SmallCardUrl = tab.SmallCardUrl

		if tab.Name == "其他游戏" {
			elem.HasChildList = true
		}
		elem.GameCardId = tab.GetGameInfo().GetGameCardId()
	}
	return elem
}

func convertOtherGameConf(cfg *thirdPartyGameConfig) *topic_channel.ThirdPartyGame {
	gameBaseInfo := make([]*topic_channel.ThirdPartyGame_GameBaseInfo, 0)
	iosGameBase := &topic_channel.ThirdPartyGame_GameBaseInfo{
		Platform:    "ios",
		JumpUrl:     cfg.iosJumpUrl,
		DownloadUrl: cfg.iosDownloadUrl,
		PackageName: cfg.packageNameList,
	}
	androidGameBase := &topic_channel.ThirdPartyGame_GameBaseInfo{
		Platform:    "android",
		JumpUrl:     cfg.androidJumpUrl,
		DownloadUrl: cfg.androidDownloadUrl,
		PackageName: cfg.packageNameList,
	}
	gameBaseInfo = append(gameBaseInfo, iosGameBase, androidGameBase)

	return &topic_channel.ThirdPartyGame{
		LabelUrl:     cfg.labelUrl,
		PublicUrl:    cfg.publicUrl,
		GameBaseInfo: gameBaseInfo,
	}
}

func GetWelcomeText(welcomeTextList []string) (text string) {
	if len(welcomeTextList) != 0 {
		n := rand.Intn(len(welcomeTextList)) //nolint:gosec
		text = welcomeTextList[n]
	}
	if text == "" {
		text = "hi，快点击上麦和我一起玩吧~"
	}
	return text
}

func VerdictClientVersion(currentVer uint32, clientKind uint16, goalVer map[uint16]string) bool {
	// log.DebugWithCtx(ctx,"SwitchGamePlay VerdictClientVersion currentVer %d clientKind %d", currentVer, goalVer)
	res := false
	// 不是移动端直接不判断逻辑
	if _, ok := goalVer[clientKind]; !ok {
		return false
	}

	verDetail := strings.Split(goalVer[clientKind], ".")
	major, _ := strconv.Atoi(verDetail[0])
	minor, _ := strconv.Atoi(verDetail[1])
	patch, _ := strconv.Atoi(verDetail[2])

	if currentVer < protocol.FormatClientVersion(uint8(major), uint8(minor), uint16(patch)) {
		res = true
		return res
	}
	return res
}

type GameEngine struct {
	OtherEngine map[string]EngineInfo `json:"other_engine"`
}

type EngineInfo struct {
	GameUrl string `json:"gameUrl"`
	Name    string `json:"name"`
	ResUrl  string `json:"resUrl"`
}

func MiniGameConfEngine(ctx context.Context, amazing string) ([]*topic_channel.MiniGameEngine, error) {
	res := make([]*topic_channel.MiniGameEngine, 0)
	if amazing == "" {
		return res, nil
	}
	gm := GameEngine{}
	err := json.Unmarshal([]byte(amazing), &gm)
	if err != nil {
		log.ErrorWithCtx(ctx, "MiniGameConfEngine err(%v)", err)
		return nil, err
	}
	for k, g := range gm.OtherEngine {
		gf := &topic_channel.MiniGameEngine{}
		id, _ := strconv.Atoi(k)
		gf.EngineId = uint32(id)
		gf.Name = g.Name
		gf.GameUrl = g.GameUrl
		gf.ResUrl = g.ResUrl
		res = append(res, gf)
	}
	return res, nil
}

func AmazingVersion(clientType uint16, ClientVersion uint32, returnModel topic_channel.GetTabListReq_ReturnedMode, major, minor uint8, patch uint16) bool {
	if returnModel == topic_channel.GetTabListReq_MORECARDS {
		if clientType == protocol.ClientTypeANDROID {
			return true
		}
		if clientType == protocol.ClientTypeIOS {
			pointVer := protocol.FormatClientVersion(major, minor, patch)
			if ClientVersion > pointVer {
				return true
			}
		}
	}

	return false
}

// 获取block字符串map
func GetBlockTitleMap(blockOptions []*channel.BlockOption, blocks []*tabPB.Block) (mobaTitleMap, titleMap map[string]string,
	allSelectBlockMap map[string]bool, blockIdx map[uint32]map[uint32]bool,
) {
	// 不限的block
	allSelectBlockMap = make(map[string]bool, len(blocks))
	// 用户选择的
	blockIdx = initUserChoseBlockIdMap(blockOptions)
	// 系统预设的
	presetBlockMap := setPresetBlockMap(blocks)
	titleMap = make(map[string]string, 0)
	mobaTitleMap = make(map[string]string, 0)
	for _, block := range blocks {
		if len(blockIdx[block.GetId()]) < 1 {
			continue
		}
		// 系统预设选项个数与用户勾选个数相同,且为全选
		if len(blockIdx[block.GetId()]) == len(presetBlockMap[block.GetId()]) && isAllSelect(block, blockIdx) {
			// titleMap[block.Title] = "不限"
			allSelectBlockMap[block.GetTitle()] = true
			if IsMobaBlock(block.GetTitle()) {
				mobaTitleMap[block.GetTitle()] = BlockNoLimit
			} else {
				titleMap[block.GetTitle()] = BlockNoLimit
			}
			continue
		}
		var idx []int
		mostSelectNum := int(block.GetMostSelectNum())
		var userSelectedNum int
		for i, elem := range block.GetElems() {
			if (mostSelectNum > 0 && userSelectedNum >= mostSelectNum) || (mostSelectNum <= 0 &&
				block.GetMode() == tabPB.Block_SINGLE && userSelectedNum >= 1) {
				// 配置了发布项最多选N项或者单选时，房间列表也只返回对应数量的展示项
				break
			}
			if blockIdx[block.Id][elem.Id] { // 获取当前block下所有选中的elem数组的index（不是elem_id）即这个block都勾选了哪几位elem
				idx = append(idx, i)
				userSelectedNum++
			}
		}
		stringSet := genStringSet(block, idx)

		var str []string
		for _, slice := range stringSet {
			str = append(str, strings.Join(slice, "-"))
		}
		title := strings.Join(str, "、")
		if IsMobaBlock(block.GetTitle()) {
			mobaTitleMap[block.GetTitle()] = title
		} else {
			titleMap[block.GetTitle()] = title
		}
	}

	log.Debugf("getBlockTitleMap blockOptions(%+v) blocks(%+v) mobaTitleMap(%+v) titleMap(%+v) allSelectBlocks(%+v)",
		blockOptions, blocks, mobaTitleMap, titleMap, allSelectBlockMap)
	return
}

// 判断用户是否全选
func isAllSelect(block *tabPB.Block, blockIdx map[uint32]map[uint32]bool) bool {
	allSelected := true
	for _, elem := range block.GetElems() { // 检查用户勾选的选项是否与预设选项完全相同
		if !blockIdx[block.GetId()][elem.GetId()] { // 用户未勾选该选项
			allSelected = false
			break
		}
	}
	return allSelected
}

// 系统预设的
func setPresetBlockMap(blocks []*tabPB.Block) map[uint32]map[uint32]*tabPB.Elem {
	presetBlockMap := map[uint32]map[uint32]*tabPB.Elem{}
	for _, block := range blocks {
		for _, elem := range block.Elems {
			if presetBlockMap[block.Id] == nil {
				presetBlockMap[block.Id] = map[uint32]*tabPB.Elem{}
			}
			presetBlockMap[block.Id][elem.Id] = elem
		}
	}
	return presetBlockMap
}

// 用户选择的发布字段选项
func initUserChoseBlockIdMap(blockOptions []*channel.BlockOption) map[uint32]map[uint32]bool {
	blockIdx := map[uint32]map[uint32]bool{}
	for _, block := range blockOptions {
		if blockIdx[block.BlockId] == nil {
			blockIdx[block.BlockId] = map[uint32]bool{block.ElemId: true}
		} else {
			blockIdx[block.BlockId][block.ElemId] = true
		}
	}
	return blockIdx
}

// 处理elem
func genStringSet(block *tabPB.Block, idx []int) [][]string {
	stringSet := make([][]string, 0)
	if block.GetTitle() == "段位" || block.GetTitle() == "段位要求" {
		lastIdx := -1
		for _, i := range idx {
			if lastIdx == -1 { // 将第一个elem的tittle存入stringSet的第一位数组，二维数组的x轴上拼入
				stringSet = append(stringSet, []string{block.Elems[i].Title})
			} else if lastIdx+1 == i { // 如果该elem与上个存入stringSet的elem是连着的
				if len(stringSet[len(stringSet)-1]) < 2 { // stringSet最后一位的数组长度为0或者1
					stringSet[len(stringSet)-1] = append(stringSet[len(stringSet)-1], block.Elems[i].Title) // 二维数组y轴上拼入
				} else {
					stringSet[len(stringSet)-1][1] = block.Elems[i].Title // 二维数组y轴上替换第二位
				}
			} else {
				stringSet = append(stringSet, []string{block.Elems[i].Title})
			}
			lastIdx = i
		}
	} else {
		for _, i := range idx {
			stringSet = append(stringSet, []string{block.Elems[i].Title})
		}
	}
	return stringSet
}

func IsMobaBlock(title string) bool {
	if title == playServer || title == playerNum || title == playMode {
		return true
	}
	return false
}

// 房间发布信息展示文案
func (s *Server) getPublishInfoDesc(ctx context.Context, tabId uint32, blockOptions []*channel.BlockOption) (channelDesc string) {
	blocks := tab_cache.GetBaseBlocksMapCache()[tabId]
	displayInfo := tab_cache.GetBlockRelationByTabId(tabId)
	if len(blockOptions) == 0 || len(displayInfo) == 0 || len(blocks) == 0 {
		return
	}
	options := make([]*channel.BlockOption, 0, len(blockOptions))
	err := copier.Copy(&options, blockOptions)
	if err != nil {
		log.Errorf("getPublishInfoDesc blockOptions:%v err%v", blockOptions, err)
		return
	}
	selectedBlockSlice := make([]string, 0, len(blocks))
	mobaMap, normalMap, allSelectBlockMap, userSelectedMap := GetBlockTitleMap(options, blocks)
	blockShownMap := make(map[uint32]bool)
	// 不限的blockTitle数组
	allSelectBlockSlice := make([]string, 0, len(allSelectBlockMap))
	// 按照关联关系的顺序处理block
	for _, v := range displayInfo {
		if title := s.getBlockDesc(v.GetBlock(), mobaMap, normalMap, allSelectBlockMap); title != "" {
			if allSelectBlockMap[v.GetBlock().GetTitle()] {
				// 不限的block，最后要合并起来并放在最后， xxx/xxx（不限）
				allSelectBlockSlice = append(allSelectBlockSlice, v.GetBlock().GetTitle())
				blockShownMap[v.GetBlock().GetId()] = true
			} else {
				selectedBlockSlice = append(selectedBlockSlice, title)
				blockShownMap[v.GetBlock().GetId()] = true
			}
		}
		var elemBindTitles []string
		var elemBindAllSelectBidSlice []string
		// 处理关联的二级字段
		elemBindTitles, elemBindAllSelectBidSlice, blockShownMap = s.getElemBindBlockDesc(v.GetElemBindBlockInfos(),
			blockShownMap, mobaMap, normalMap, allSelectBlockMap, userSelectedMap[v.GetBlock().GetId()])
		if len(elemBindTitles) != 0 {
			selectedBlockSlice = append(selectedBlockSlice, elemBindTitles...)
		}
		if len(elemBindAllSelectBidSlice) != 0 {
			allSelectBlockSlice = append(allSelectBlockSlice, elemBindAllSelectBidSlice...)
		}

	}
	if len(allSelectBlockSlice) != 0 {
		// 选中了不限的block，合并后追加在最后
		selectedBlockSlice = append(selectedBlockSlice, strings.Join(allSelectBlockSlice, "/")+"("+BlockNoLimit+")")
	}
	channelDesc = strings.Join(selectedBlockSlice, " | ")
	log.DebugWithCtx(ctx, "getPublishInfoDesc tabId(%d), teamDesc(%v) blockOption(%v) displayInfo(%v)", tabId, channelDesc, blockOptions, displayInfo)
	return
}

func (s *Server) getElemBindBlockDesc(elemBindInfo []*channel_play.ElemBindBlockInfo, blockShownMap map[uint32]bool, mobaMap, normalMap map[string]string,
	allSelectBlockMap map[string]bool, elemSelectedMap map[uint32]bool,
) ([]string, []string, map[uint32]bool) {
	selectedBlockSlice := make([]string, 0)
	allSelectBlockSlice := make([]string, 0)
	if len(elemSelectedMap) == 0 {
		return selectedBlockSlice, allSelectBlockSlice, blockShownMap
	}
	for _, bindInfo := range elemBindInfo {
		if !elemSelectedMap[bindInfo.GetElemId()] {
			// 用户没有选这个elem，不需要处理关联的block
			continue
		}
		for _, bindBlock := range bindInfo.GetBindBlocks() {
			if blockShownMap[bindBlock.GetId()] {
				continue
			}
			if title := s.getBlockDesc(bindBlock, mobaMap, normalMap, allSelectBlockMap); title != "" {
				if allSelectBlockMap[bindBlock.GetTitle()] {
					// 不限的block，最后要合并起来并放在最后， xxx/xxx（不限）
					allSelectBlockSlice = append(allSelectBlockSlice, bindBlock.GetTitle())
					blockShownMap[bindBlock.GetId()] = true
				} else {
					selectedBlockSlice = append(selectedBlockSlice, title)
					blockShownMap[bindBlock.GetId()] = true
				}
			}
		}
	}
	return selectedBlockSlice, allSelectBlockSlice, blockShownMap
}

func (s *Server) getBlockDesc(block *topic_channel.Block, mobaMap, normalMap map[string]string, allSelectBlockMap map[string]bool) string {
	if allSelectBlockMap[block.GetTitle()] {
		return block.GetTitle() + "(" + BlockNoLimit + ")"
	} else if title, ok := mobaMap[block.GetTitle()]; ok {
		return title
	} else if title, ok := normalMap[block.GetTitle()]; ok {
		return title
	}
	return ""
}

func miniGameModelBlockElem(tabId uint32, blockOpts []*topic_channel.BlockOption) (modelBlock *tabPB.Block, modelElem *tabPB.Elem) {
	const (
		modelBlockTitle       = "模式"
		modelElemDefaultTitle = "default_model"
	)

	tabBlocks := tab_cache.GetBaseBlocksMapCache()
	if tabBlocks == nil {
		return
	}

	blocks := tabBlocks[tabId]
	if len(blocks) == 0 {
		return
	}

	for _, block := range blocks {
		if block.GetTitle() == modelBlockTitle {
			modelBlock = block
			break
		}
	}
	if modelBlock == nil {
		return
	}

	var modelElemId uint32
	for _, blockOpt := range blockOpts {
		if blockOpt.GetBlockId() == modelBlock.GetId() {
			modelElemId = blockOpt.GetElemId()
			break
		}
	}

	for _, elem := range modelBlock.GetElems() {
		if modelElemId == 0 && elem.GetTitle() == modelElemDefaultTitle || elem.GetId() == modelElemId {
			modelElem = elem
			break
		}
	}

	return modelBlock, modelElem
}

func convertTagLoadMore(more *channel.GetListByTabLoadMore) *topic_channel.ListRecommendTopicChannelLoadMore {
	var loadMore *topic_channel.ListRecommendTopicChannelLoadMore
	if more != nil {
		if more.Newborn == nil && more.Sink == nil && more.Big == nil {
			return nil
		}
		loadMore = &topic_channel.ListRecommendTopicChannelLoadMore{
			ByTab: &topic_channel.ListRecommendTopicChannelLoadMore_ByTabLoadMore{},
		}
		if more.Newborn != nil {
			loadMore.ByTab.Newborn = &topic_channel.ListRecommendTopicChannelLoadMore_ByTabLoadMoreItem{
				Cursor:    more.Newborn.Cursor,
				LastCount: more.Newborn.LastCount,
				LastIndex: more.Newborn.LastIndex,
				LastValue: more.Newborn.LastValue,
				TheEnd:    more.Newborn.TheEnd,
			}
		}
		if more.Sink != nil {
			loadMore.ByTab.Sink = &topic_channel.ListRecommendTopicChannelLoadMore_ByTabLoadMoreItem{
				Cursor:    more.Sink.Cursor,
				LastCount: more.Sink.LastCount,
				LastIndex: more.Sink.LastIndex,
				LastValue: more.Sink.LastValue,
				TheEnd:    more.Sink.TheEnd,
			}
		}
		if more.Big != nil {
			loadMore.ByTab.Big = &topic_channel.ListRecommendTopicChannelLoadMore_ByTabLoadMoreItem{
				Cursor:    more.Big.Cursor,
				LastCount: more.Big.LastCount,
				LastIndex: more.Big.LastIndex,
				LastValue: more.Big.LastValue,
				TheEnd:    more.Big.TheEnd,
			}
		}
	}
	return loadMore
}
