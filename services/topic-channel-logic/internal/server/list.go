package server

import (
	"context"
	"encoding/json"
	"golang.52tt.com/kaihei-pkg/supervision"
	channel_go "golang.52tt.com/protocol/services/channel-go"
	"golang.52tt.com/services/topic-channel-logic/internal/tab_cache"
	"strings"
	"time"

	abtestPB "golang.52tt.com/protocol/services/abtest"

	"golang.52tt.com/pkg/foundation/utils"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	gaChannelPB "golang.52tt.com/protocol/app/channel"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/protocol/common/status"
	channelPB "golang.52tt.com/protocol/services/topic_channel/channel"
	genPB "golang.52tt.com/protocol/services/topic_channel/recommendation_gen"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
)

// 外显的小众游戏数量
const DisplayMinorityLength = 3

func (s *Server) ListFreshmanRecommendedChannel(ctx context.Context,
	in *topic_channel.ListFreshmanRecommendedChannelReq) (out *topic_channel.ListFreshmanRecommendedChannelResp, err error) {
	out = new(topic_channel.ListFreshmanRecommendedChannelResp)
	var count uint32 = 20
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	userId := serviceInfo.UserID
	defer log.DebugWithCtx(ctx, "ListFreshmanRecommendedChannel uid:%d in:%v out:%v", userId, in, out)

	// Note(shadow) 2020-12-23:@黄菲菲 说去掉新用户限制
	// 注册时间>72小时，不返回
	//if user, err := s.accountClient.GetUser(ctx, userId); err != nil || uint32(time.Now().Unix())-user.RegisteredAt > 60*60*72 {
	//	return out, nil
	//}
	// Note(shadow) 2020-12-24:@黄菲菲 说改成玩伴数量限制
	friendCount, err := s.friendshipClient.GetFriendCount(ctx, userId, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "friendshipClient.GetFriendCount userId(%v) err(%v)", userId, err)
		return out, nil
	}

	if friendCount >= 5 {
		log.DebugWithCtx(ctx, "user(%d)'s friendCount(%d) greater than 5", userId, friendCount)
		return out, nil
	}

	user, err := s.accountClient.GetUser(ctx, userId)
	if err != nil {
		log.ErrorWithCtx(ctx, "accountClient.GetUser uid: %d err: %v", userId, err)
		return
	}

	var reserve []byte
	if uint32(time.Now().Unix())-user.RegisteredAt < 60*60*72 {
		var abTestGroup string
		abTestGroup, err = s.getAbTestGroup(ctx, userId)
		if err != nil {
			return
		}
		log.DebugWithCtx(ctx, "uid: %d abTestGroup: %s", userId, abTestGroup)

		switch abTestGroup {
		case "Test_group1":
			reserve, _ = json.Marshal(&genPB.RecommendationReqReserve{ImListExpGroup: genPB.IMChannelListABGroup_RegLess72Hour_Exp_A})
		case "Test_group2":
			reserve, _ = json.Marshal(&genPB.RecommendationReqReserve{ImListExpGroup: genPB.IMChannelListABGroup_RegLess72Hour_Exp_B})
		case "Default", "":
			return out, nil
		}
	} else {
		reserve, _ = json.Marshal(&genPB.RecommendationReqReserve{ImListExpGroup: genPB.IMChannelListABGroup_RegMore72Hour_Exp})
	}
	log.DebugWithCtx(ctx, "user(%d)'s ab test group %s", userId, string(reserve))

	supConfInst := &supervision.SupervisoryConf{
		RegisterLimitTime:      s.serviceConfigT.GetPublicSwitchConfig().GetRegisterLimitTime(),
		UserLevelLimit:         s.serviceConfigT.GetPublicSwitchConfig().GetUserLevelLimit(),
		RealNameStandardStatus: s.serviceConfigT.GetPublicSwitchConfig().GetRealNameStandardStatus(),
	}
	genResp, err := s.genRecommendationClient.GetRecommendationList(ctx, &genPB.GetRecommendationListReq{
		Uid: userId, Limit: count, GetMode: genPB.GetRecommendationListReq_NEXTPAGE,
		ChannelEnterSource: uint32(gaChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_IM_LIST_RECOMMENDED_CHANNEL_TO_FRESHMAN),
		ClientType:         uint32(serviceInfo.ClientType), ClientVersion: serviceInfo.ClientVersion, MarketId: serviceInfo.MarketID,
		Env:              genPB.GetRecommendationListReq_Environment(s.v2Config.GenServerEnv),
		ChannelPackageId: in.ChannelPackageId,
		Reserve:          string(reserve),
		RegulatoryLevel:  genPB.REGULATORY_LEVEL(s.supervisor.GetUserRegulatoryLevelByUid(ctx, userId, supConfInst)),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ListFreshmanRecommendedChannel gen GetRecommendationList userId(%v) err(%v)", userId, err)
		return
	}
	log.DebugWithCtx(ctx, "genResp.ChannelId: %v", genResp.ChannelId)
	if len(genResp.ChannelId) == 0 {
		return out, nil
	}

	channelResp, err := s.channelGoClient.BatchGetChannelSimpleInfo(ctx, &channel_go.BatchGetChannelSimpleInfoReq{
		OpUid:             userId,
		ChannelIdList:     genResp.GetChannelId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ListFreshmanRecommendedChannel list channel simple info by ids(%v) err(%v)", genResp.ChannelId, err)
		return
	}
	channelSimpleInfoResp := make(map[uint32]*channel_go.ChannelSimpleInfo, len(channelResp.GetChannelSimpleList()))
	for _, info := range channelResp.GetChannelSimpleList() {
		channelSimpleInfoResp[info.GetChannelId()] = info
	}
	userIds := make([]uint32, 0)
	for _, info := range channelSimpleInfoResp {
		userIds = append(userIds, info.GetCreaterUid())
	}
	userIds = utils.TrimRepeatUint32Value(userIds)

	userMap, err := s.accountClient.GetUsersMap(ctx, userIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "ListFreshmanRecommendedChannel GetUsersMap by ids(%v) err(%v)", userIds, err)
		return out, err
	}

	resp, err := s.tcChannelClient.GetChannelByIds(ctx, &channelPB.GetChannelByIdsReq{Ids: genResp.ChannelId})
	if err != nil {
		log.ErrorWithCtx(ctx, "ListFreshmanRecommendedChannel GetChannelByIds userId(%v) ids(%v) err(%v)", userId, genResp.ChannelId, err)
		return
	}
	log.DebugWithCtx(ctx, "ListFreshmanRecommendedChannel GetChannelByIds in(%d) out(%d)", len(genResp.ChannelId), len(resp.Info))

	tabs := make([]*tabPB.Tab, len(resp.Info))
	channelIdToTab := make(map[uint32]*tabPB.Tab)
	for i, info := range resp.Info {
		tab := &tabPB.Tab{Id: info.TabId}
		tabs[i] = tab
		channelIdToTab[info.Id] = tab
	}
	log.DebugWithCtx(ctx, "channelIdToTab: %v", channelIdToTab)

	tabResp, err := s.tcTabClient.FiniteTabs(ctx, &tabPB.FiniteTabsReq{Tabs: tabs})
	if err != nil {
		log.ErrorWithCtx(ctx, "ListFreshmanRecommendedChannel GetOnlineFriends FiniteTabs uid: %d tab %v err: %v", serviceInfo.UserID, tabs, err)
		return
	}
	for _, tab := range tabResp.GetTabs() {
		for cid, t := range channelIdToTab {
			if t.Id == tab.GetId() {
				channelIdToTab[cid] = tab
			}
		}
	}

	var items []*topic_channel.FreshmanRecommendedChannelItem
	for _, channelId := range genResp.ChannelId {
		var item = &topic_channel.FreshmanRecommendedChannelItem{
			ChannelInfo:   &topic_channel.TopicChannelInfoV2{ChannelId: channelId},
			OwnerUserInfo: &topic_channel.ChannelOwnerInfo{},
		}

		if channelSimpleInfoResp[channelId] != nil {
			if channelSimpleInfoResp[channelId].GetHasPwd() {
				//被锁房了就不返回
				continue
			}
			item.ChannelInfo.Name = channelSimpleInfoResp[channelId].GetName()
			item.ChannelInfo.DisplayId = channelSimpleInfoResp[channelId].GetDisplayId()
			item.ChannelInfo.AppId = channelSimpleInfoResp[channelId].GetAppId()
			item.ChannelInfo.HasPwd = channelSimpleInfoResp[channelId].GetHasPwd()
			item.ChannelInfo.ChannelType = channelSimpleInfoResp[channelId].GetChannelType()
			item.ChannelInfo.TopicTitle = channelSimpleInfoResp[channelId].GetTopicTitle()
			item.ChannelInfo.IconMd5 = channelSimpleInfoResp[channelId].GetIconMd5()
			item.ChannelInfo.BindId = channelSimpleInfoResp[channelId].GetBindId()

			if _, ok := userMap[channelSimpleInfoResp[channelId].GetCreaterUid()]; !ok {
				continue
			}
			item.OwnerUserInfo.Uid = channelSimpleInfoResp[channelId].GetCreaterUid()
			item.OwnerUserInfo.Account = userMap[channelSimpleInfoResp[channelId].GetCreaterUid()].Username
			item.OwnerUserInfo.Sex = uint32(userMap[channelSimpleInfoResp[channelId].GetCreaterUid()].Sex)
			item.OwnerUserInfo.Nickname = userMap[channelSimpleInfoResp[channelId].GetCreaterUid()].Nickname
		} else {
			//channel info都拿不到，不返回
			continue
		}

		if serviceInfo.ClientType == protocol.ClientTypeIOS && s.v2Config.IsIOSReviewing {
			//苹果监管需要临时写死，带安卓，android字眼不返回
			if strings.Contains(item.ChannelInfo.Name, "安卓") || strings.Contains(strings.ToLower(item.ChannelInfo.Name), "android") {
				continue
			}
			if strings.Contains(item.ChannelInfo.HeadDesc, "安卓") || strings.Contains(strings.ToLower(item.ChannelInfo.HeadDesc), "android") {
				continue
			}
			if strings.Contains(item.ChannelInfo.SecondDesc, "安卓") || strings.Contains(strings.ToLower(item.ChannelInfo.SecondDesc), "android") {
				continue
			}
		}
		if serviceInfo.ClientType == protocol.ClientTypeIOS && s.v2Config.IosReplaceAndroidString {
			item = s.freshmanRecommendedChannelItemReplaceByIOS(item)
		}

		if tab, ok := channelIdToTab[channelId]; ok {
			// 实际上运营后台只能配置FollowLabelImg
			item.FindPlayingImg = tab.FollowLabelImg
			item.FindPlayingText = tab.FindPlayingText
			items = append(items, item)
		}
	}
	out.Items = items

	return
}

func (s *Server) getAbTestGroup(ctx context.Context, uid uint32) (string, error) {
	resp, err := s.abtestClient.MatchTest(ctx, &abtestPB.MatchTestReq{ExpId: 1010, Tester: []uint32{uid}})
	if err != nil {
		log.ErrorWithCtx(ctx, "abtestClient.MatchTest uid: %d err: %v", uid, err)
		return "", err
	}
	for _, ver := range resp.Ver {
		switch ver.ExpVer {
		case "Test_group1":
			for _, tester := range ver.Tester {
				if tester == uid {
					return "Test_group1", nil
				}
			}
		case "Test_group2":
			for _, tester := range ver.Tester {
				if tester == uid {
					return "Test_group2", nil
				}
			}
		case "Default":
			for _, tester := range ver.Tester {
				if tester == uid {
					return "Default", nil
				}
			}
		}
	}
	return "", nil
}

func (s *Server) GetSubTabList(ctx context.Context, in *topic_channel.GetSubTabListReq) (out *topic_channel.GetSubTabListResp, err error) {
	out = &topic_channel.GetSubTabListResp{}
	log.DebugWithCtx(ctx, "GetSubTabList in : %v", in)

	var hiddenList []*tab_cache.MinorityGameInfo
	if in.ReturnMode == topic_channel.GetSubTabListReq_MORECARDS {
		hiddenList = s.sortMinorityGameTab(in.SelfGameIds)
	} else {
		serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
		if !ok {
			return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
		}

		hiddenList = s.getHiddenMinorityGameTab(serviceInfo)
	}

	log.DebugWithCtx(ctx, "GetSubTabList SelfGameIds(%v) hiddenList(%v)", in.SelfGameIds, hiddenList)

	out.ShowTopicChannelTabSecondaryItem = make([]*topic_channel.ShowTopicChannelTabSecondaryItem, len(hiddenList))
	for i, v := range hiddenList {
		out.ShowTopicChannelTabSecondaryItem[i] = convertTabSecondaryItem(v.TabInfo)
	}

	log.DebugWithCtx(ctx, "GetSubTabList out : %v", out)
	return out, nil
}

func convertTabSecondaryItem(tabInfo *tabPB.Tab) *topic_channel.ShowTopicChannelTabSecondaryItem {
	tabSecondaryItem := &topic_channel.ShowTopicChannelTabSecondaryItem{}
	tabSecondaryItem.ItemText = tabInfo.Name
	tabSecondaryItem.MaskLayer = tabInfo.MaskLayer
	tabSecondaryItem.ItemIcon = tabInfo.CardsImageUrl
	tabSecondaryItem.SmallCard = tabInfo.SmallCardUrl
	tabSecondaryItem.TabId = tabInfo.Id
	tabSecondaryItem.CategoryId = tabInfo.CategoryId
	tabSecondaryItem.TabType = topic_channel.ShowTopicChannelTabSecondaryItem_TabType(tabInfo.TabType)
	if tabInfo.Name == "其他游戏" {
		tabSecondaryItem.HasList = true
	}
	return tabSecondaryItem
}

func (s *Server) isAppleSuperVisionSkip(clientType uint16, channelInfo *topic_channel.TopicChannelInfoV2) bool {
	if clientType == protocol.ClientTypeIOS && s.v2Config.IsIOSReviewing {
		//苹果监管需要临时写死，带安卓，android字眼不返回
		if strings.Contains(channelInfo.Name, "安卓") || strings.Contains(strings.ToLower(channelInfo.Name), "android") {
			return true
		}
		if strings.Contains(channelInfo.HeadDesc, "安卓") || strings.Contains(strings.ToLower(channelInfo.HeadDesc), "android") {
			return true
		}
		if strings.Contains(channelInfo.SecondDesc, "安卓") || strings.Contains(strings.ToLower(channelInfo.SecondDesc), "android") {
			return true
		}
	}
	return false
}

func (s *Server) itemV2ReplaceByIOS(item *topic_channel.TopicChannelItemV2) *topic_channel.TopicChannelItemV2 {
	item.ChannelInfo.Name = strings.Replace(item.ChannelInfo.Name, "安卓手Q", "安Q", -1)
	item.ChannelInfo.Name = strings.Replace(item.ChannelInfo.Name, "安卓微信", "安微", -1)
	item.TabInfo.Name = strings.Replace(item.TabInfo.Name, "安卓手Q", "安Q", -1)
	item.TabInfo.Name = strings.Replace(item.TabInfo.Name, "安卓微信", "安微", -1)
	return item
}

func (s *Server) playmateRecommendedChannelItemReplaceByIOS(item *topic_channel.PlaymateRecommendedChannelItem) *topic_channel.PlaymateRecommendedChannelItem {
	item.ChannelInfo.Name = strings.Replace(item.ChannelInfo.Name, "安卓手Q", "安Q", -1)
	item.ChannelInfo.Name = strings.Replace(item.ChannelInfo.Name, "安卓微信", "安微", -1)
	return item
}

func (s *Server) freshmanRecommendedChannelItemReplaceByIOS(item *topic_channel.FreshmanRecommendedChannelItem) *topic_channel.FreshmanRecommendedChannelItem {
	item.ChannelInfo.Name = strings.Replace(item.ChannelInfo.Name, "安卓手Q", "安Q", -1)
	item.ChannelInfo.Name = strings.Replace(item.ChannelInfo.Name, "安卓微信", "安微", -1)
	return item
}
