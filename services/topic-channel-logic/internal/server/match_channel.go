package server

import (
	"context"
	"fmt"
	"golang.52tt.com/services/topic-channel-logic/internal/tab_cache"
	"time"

	configserverPB "golang.52tt.com/protocol/services/configserver"
	"golang.52tt.com/services/topic-channel-logic/internal/switch_cache"

	"golang.52tt.com/pkg/cost_time_reporter"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	appChannelPB "golang.52tt.com/protocol/app/channel"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/protocol/common/status"
	openGamePB "golang.52tt.com/protocol/services/channel-open-game"
	tmpOpenGamePB "golang.52tt.com/protocol/services/tmp-channel-open-game"
	channelPB "golang.52tt.com/protocol/services/topic_channel/channel"
	topicChannelPB "golang.52tt.com/protocol/services/topic_channel/channel"
	mini_game "golang.52tt.com/protocol/services/topic_channel/recommendation_mini_game_gen"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/topic-channel-logic/internal/conf"
	"golang.52tt.com/services/topic-channel-logic/internal/report"
	topicUtils "golang.52tt.com/services/topic-channel-logic/internal/utils"
)

// 临时房快速匹配
func (s *Server) QuickFormTeamV2(ctx context.Context, in *topic_channel.QuickFormTeamV2Req) (*topic_channel.QuickFormTeamV2Resp, error) {
	const defaultMiniGameModelTitle = "default_model"

	out := &topic_channel.QuickFormTeamV2Resp{}
	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	if s.serviceConfigT.GetPublicSwitchConfig().GetTopicChannelHomePageSwitch(serviceInfo.MarketID) {
		// 监管需求，屏蔽快速匹配功能
		log.DebugWithCtx(ctx, "QuickFormTeamV2 开启监管 marketid %v, uid %v", serviceInfo.MarketID, serviceInfo.UserID)
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelQuickMatchNoChannel)
	}
	uid := serviceInfo.UserID
	log.InfoWithCtx(ctx, "QuickFormTeamV2 uid(%v) in (%+v)", uid, in)

	defer func() {
		if out.GetChannelId() == 0 {
			log.WarnWithCtx(ctx, "QuickFormTeamV2 user %d match channel empty", uid)
		}
	}()
	//兜底开关开启
	if switch_cache.GetSwitchStatusByType(configserverPB.SwitchBusinessType_GAME_TEMP_QUICK_MATCH) {
		cid, err := s.quickFormTeamV2FallBack(ctx, in.GetTabId(), uid, in.GetBlockOption())
		if err != nil {
			log.ErrorWithCtx(ctx, "QuickFormTeamV2 fallback switch on uid:%d, in:%s, err:%v", uid, in.String(), err)
			return out, err
		}
		out.ChannelId = cid
		return out, nil
	}
	costReporter := cost_time_reporter.NewTimeCostCounterWithName("topic-channel-logic/QuickFormTeamV2", costReportNameSpace)
	defer func() {
		if len(costReporter.TimeCostList) > 0 {
			costReporter.ReportByHistogram(grpc.NewContextWithInfo(ctx))
		}
	}()

	tab := tab_cache.GetTabIdMap()[in.GetTabId()]
	if tab == nil {
		log.WarnWithCtx(ctx, "QuickFormTeamV2 tab %d not found", in.GetTabId())
		return out, nil
	}
	blocks := tab_cache.GetBaseBlocksMapCache()[in.GetTabId()]

	costReporter.Tick("get_tab_info_cost_time")

	// 获取发布字段中的"模式"
	modelBlock, modelElem := miniGameModelBlockElem(in.GetTabId(), in.GetBlockOption())
	gameModel := modelElem.GetMiniGameModel()
	log.InfoWithCtx(ctx, "QuickFormTeamV2 tab(%d) blockOption(%+v) modelBlock(%+v) modelElem(%+v)",
		in.GetTabId(), in.GetBlockOption(), modelBlock, modelElem)

	{
		// 增加控制游戏维护逻辑
		var isMaintain bool
		isMaintain, err := s.minGameMaintain(ctx, tab.TagId)
		if err != nil {
			log.ErrorWithCtx(ctx, "QuickFormTeamV2 minGameMaintain userId(%v) tagId(%v) tagId is empty", uid, tab.TagId)
			return out, err
		}
		if isMaintain {
			return out, protocol.NewExactServerError(nil, status.ErrMiniGameMaintain)
		}

		err = s.minGameLimitCheck(ctx, uid, tab.TagId, gameModel)
		if err != nil {
			return out, err
		}
	}
	costReporter.Tick("game_check_cost_time")

	if tab.TemMatchPolicy == tabPB.Tab_RECOMMENDPOLICY || tab.TemMatchPolicy == tabPB.Tab_NOPOLICY {
		// 处理BlockOption为空清空
		miniGameReq := make([]*mini_game.BlockOption, 0)
		if modelBlock != nil && modelElem != nil && modelElem.GetTitle() != defaultMiniGameModelTitle {
			miniGame := &mini_game.BlockOption{BlockId: modelBlock.GetId(), ElemId: modelElem.GetId()}
			miniGameReq = append(miniGameReq, miniGame)
		}
		warnLogUtil := topicUtils.NewWarnLog()
		// 向江正请求分配临时房，江正这边匹配，在channel-room那边创建房间已经存储到redis
		rcmdCtx, cancel := grpc.InheritContextWithInfoTimeout(ctx, 900*time.Millisecond)
		defer cancel()
		tmpChannelId, rcmdErr := s.recommendHourRoom.GetQuickMatchChannel(rcmdCtx, &mini_game.GetQuickMatchChannelReq{
			Uid: uid, TabId: in.TabId,
			BlockOptions: miniGameReq,
		})
		if rcmdErr != nil {
			log.ErrorWithCtx(ctx, "QuickFormTeamV2 GetQuickMatchChannel uid (%+v) tabId(%v) rcmdErr(%v)", uid, in.GetTabId(), rcmdErr)
			cid, err := s.quickFormTeamV2FallBack(ctx, in.GetTabId(), uid, in.GetBlockOption())
			if err != nil {
				log.ErrorWithCtx(ctx, "QuickFormTeamV2 quickFormTeamV2FallBack error uid:%d, in:%s, err:%v", uid, in.String(), err)
				return out, err
			}
			out.ChannelId = cid
			return out, nil
		}
		warnLogUtil.WarnLog(fmt.Sprintf("QuickFormTeamV2 tmpChannelId:%d, uid:%d, tabId:%d", tmpChannelId.GetChannelId(), uid, in.GetTabId()))
		out.ChannelId = tmpChannelId.GetChannelId()
	} else {
		// 向蒋鸿琨请求分配临时房
		channelName := tab.Name
		if len(in.BlockOption) > 0 {
			channelName = fmt.Sprintf("%s %s", tab.Name, gameModel)
		}
		log.InfoWithCtx(ctx, "QuickFormTeamV2 remedy uid (%+v) blocks (%+v) gameModel (%s) channelName %s", uid, blocks, gameModel, channelName)

		joyFun, err := s.tmpChannelOpenGameClient.RandomAllocJoystick(ctx, &tmpOpenGamePB.RandomAllocJoystickReq{
			GameId:   tab.TagId,
			GameMode: gameModel, ChannelName: channelName, Uid: uid,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "QuickFormTeamV2 RandomAllocJoystick uid (%v) err(%v)", uid, err)
			return out, err
		}
		// 确保存储的数据准确性
		if joyFun.Joystick.ChannelId == 0 {
			log.ErrorWithCtx(ctx, "QuickFormTeamV2 RandomAllocJoystick uid (%v) return channel id invalid (%v)", uid)
			return out, protocol.NewExactServerError(nil, status.ErrTempchannelInvalidChannelid)
		}
		out.ChannelId = joyFun.Joystick.ChannelId

		// 存储到redis
		var options []*topicChannelPB.BlockOption
		for _, o := range in.BlockOption {
			if o.ElemId != InfElemID && o.ElemId > 0 {
				options = append(options, &topicChannelPB.BlockOption{BlockId: o.BlockId, ElemId: o.ElemId})
			}
		}

		_, serr := s.tcChannelClient.AddTemporaryChannel(ctx, &topicChannelPB.AddTemporaryChannelReq{
			Channel: &topicChannelPB.ChannelInfo{
				Id:                 out.ChannelId,
				TabId:              in.GetTabId(),
				CreateTime:         time.Now().Unix(),
				IsRecommendChannel: false,
				BlockOptions:       options,
				DisplayType:        []topicChannelPB.ChannelDisplayType{topicChannelPB.ChannelDisplayType_TEMPORARY},
			},
		})
		if serr != nil {
			log.ErrorWithCtx(ctx, "QuickFormTeamV2 AddTemporaryChannel channelId(%v) userId(%v) tab(%v) err(%v)", out.ChannelId, uid, tab.Id, serr)
			return out, serr
		}
	}

	// 数据上报
	report.ReportCreateTemporaryChannel(ctx, out.ChannelId, uint32(appChannelPB.ChannelType_TEMP_KH_CHANNEL_TYPE),
		in.TabId, tab.Name, tab.MicMod)

	costReporter.Tick("gen_cid_cost_time")

	log.InfoWithCtx(ctx, "QuickFormTeamV2 uid(%v) tmpChannelId (%d) recommend policy (%d) success", uid, out.ChannelId, tab.TemMatchPolicy)
	return out, nil
}

func (s *Server) getMiniGameConfig(ctx context.Context) {
	// log.DebugWithCtx(ctx,"getMiniGameConfig cache the mini game start")
	cf, err := s.channelOpenGameClient.GetSupportGameList(ctx, &openGamePB.GetSupportGameListReq{})
	if err != nil {
		// 不用中断
		log.ErrorWithCtx(ctx, "timer sync cache getMiniGameConfig err(%v)", err)
		return
	}
	defer s.Unlock()
	s.Lock()
	for _, v := range cf.GetBase() {
		tmp := &topic_channel.MiniGameConfig{}
		tmp.GameId = v.GetGameId()
		tmp.GameVersion = v.GetGameVersion()
		tmp.GameName = v.GetGameName()
		tmp.CpId = v.GetCpId()
		tmp.GamePackage = v.GetGamePackage()
		tmp.GameUrl = v.GetGameUrl()
		tmp.EngineVer = v.GetEngineVer()
		tmp.GameMemberCntLimit = v.GetGameMemberCntLimit()
		tmp.EngineType = v.GetEngineType()
		tmp.GameAppLimit = v.GetGameAppLimit()
		tmp.GamePlatformLimit = v.GetGamePlatformLimit()
		tmp.GameDigest = v.GetGameDigest()
		tmp.MiniGameEngine, _ = MiniGameConfEngine(ctx, v.GetGameExtraProperties()) // 小游戏引擎，加载小游戏需要使用
		tmp.GameResUrl = v.GetGameResUrl()
		tmp.GameResDigest = v.GetGameResDigest()
		s.miniGameConfig[tmp.GameId] = tmp
	}

	// log.DebugWithCtx(ctx,"getMiniGameConfig cache the mini game end cache (%+v)", s.miniGameConfig)
}

func (s *Server) minGameMaintain(ctx context.Context, gameId uint32) (bool, protocol.ServerError) {
	res, err := s.channelOpenGameClient.GetGameMaintain(ctx, &openGamePB.GetGameMaintainReq{GameId: gameId})
	if err != nil {
		log.ErrorWithCtx(ctx, "minGameMaintain err(%v)", err)
		return false, err
	}
	n := time.Now().Unix()

	if res.GameMaintain == nil {
		return false, nil
	}
	if n > res.GameMaintain.GetMaintainBegin() && res.GameMaintain.GetMaintainEnd() > n {
		return true, nil
	}
	return false, nil
}

func (s *Server) minGameLimitCheck(ctx context.Context, uid, gameId uint32, gameModel string) protocol.ServerError {
	if nil == s.serviceConfigT.GetOption() {
		return nil
	}

	gameControlList := s.serviceConfigT.GetOption().GameControlList
	if 0 == len(gameControlList) {
		return nil
	}

	var rule *conf.GameControlRule
	for _, controlRule := range gameControlList {
		if controlRule.Id == gameId && controlRule.Model == gameModel {
			rule = controlRule
		}
	}

	// 配置不存在或者禁用
	if nil == rule || (rule.MinWined == 0 && rule.MaxWined == 0) {
		return nil
	}

	log.DebugWithCtx(ctx, "minGameLimitCheck uid:%d, gameid:%d, gamemodel:%s, win: [%d, %d]", uid, gameId, gameModel, rule.MinWined, rule.MaxWined)

	_, wined, _, err := s.channelOpenGameRecordClient.GetRecord(ctx, uid, gameId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecord %d  of %d err(%v)", uid, gameId, err)
	} else if rule.MinWined != 0 && wined < rule.MinWined {
		log.DebugWithCtx(ctx, "minGameLimitCheck uid:%d, gameid:%d, gamemodel:%s, current %d", uid, gameId, gameModel, wined)
		return protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError, fmt.Sprintf("再获胜%d次就可以发起进阶模式匹配啦~", rule.MinWined-wined))
	} else if rule.MaxWined != 0 && wined > rule.MaxWined {
		log.DebugWithCtx(ctx, "minGameLimitCheck uid:%d, gameid:%d, gamemodel:%s, current %d", uid, gameId, gameModel, wined)
		return protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError, "你已经是高手了，修改为进阶模式匹配和高手Pk吧")
	}
	return nil
}

// 小游戏临时房匹配兜底，返回个人房
func (s *Server) quickFormTeamV2FallBack(ctx context.Context, tabId, userId uint32, blockOption []*topic_channel.BlockOption) (uint32, error) {
	var channelList []*channelPB.ChannelInfo
	if tabId > 0 && tabId != InfElemID {
		var options []*channelPB.BlockOption
		for _, o := range blockOption {
			if o.GetElemId() > 0 && o.GetElemId() != InfElemID {
				options = append(options, &channelPB.BlockOption{BlockId: o.GetBlockId(), ElemId: o.GetElemId()})
			}
		}
		resp, err := s.tcChannelClient.GetRecommendChannelListByTab(ctx, &channelPB.GetRecommendChannelListByTabReq{
			Uid: userId, Limit: 1, TabId: tabId, BlockOptions: options, NotClearHistory: true, NotCheckHistory: true, NotSaveHistory: true,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "quickFormTeamFallBack GetRecommendChannelListByTab userId(%d) tabId(%d) blockOption:(%v) err(%v)",
				userId, tabId, blockOption, err)
			return 0, err
		}
		channelList = resp.GetChannelList()
	} else {
		resp, err := s.tcChannelClient.GetRecommendChannelList(ctx, &channelPB.GetRecommendChannelListReq{
			Uid: userId, Limit: 1, NotClearHistory: true, NotCheckHistory: true, NotSaveHistory: true, TabIdList: []uint32{},
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "quickFormTeamFallBack GetRecommendChannelList userId(%d) tabId(%d) blockOption:(%v) err(%v)",
				userId, tabId, blockOption, err)
			return 0, err
		}
		channelList = resp.GetChannelList()
	}

	if len(channelList) > 0 {
		cid := channelList[0].GetId()

		punished, err := s.isPunishChannel(ctx, userId, cid)
		if err != nil {
			log.ErrorWithCtx(ctx, "quickFormTeamFallBack tc isPunishChannel cid(%d) err: %v", cid, err)
			return 0, err
		}
		if punished {
			log.WarnWithCtx(ctx, "quickFormTeamFallBack tc channel %d punished", cid)
			return 0, nil
		}
		log.InfoWithCtx(ctx, "quickFormTeamFallBack userId(%d) tabId(%d) blockOption:(%v) cid(%d)", userId, tabId, blockOption, cid)
		return cid, nil
	}
	log.WarnWithCtx(ctx, "quickFormTeamFallBack userId(%d) tabId(%d) blockOption:(%v) no channel", userId, tabId, blockOption)
	return 0, nil
}
