package server

import (
	"context"
	"encoding/base64"
	"fmt"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/kaihei-pkg/supervision"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/metrics"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	gaChannelPB "golang.52tt.com/protocol/app/channel"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/protocol/common/status"
	channel_go "golang.52tt.com/protocol/services/channel-go"
	ktvpb "golang.52tt.com/protocol/services/channel-ktv"
	channel_level "golang.52tt.com/protocol/services/channel-level"
	channelmusic "golang.52tt.com/protocol/services/channelmusicsvr"
	"golang.52tt.com/protocol/services/rcmd/common"
	rcmdCommon "golang.52tt.com/protocol/services/rcmd/common"
	channelPB "golang.52tt.com/protocol/services/topic_channel/channel"
	"golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
	genPB "golang.52tt.com/protocol/services/topic_channel/recommendation_gen"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/topic-channel-logic/internal/conf"
	"golang.52tt.com/services/topic-channel-logic/internal/tab_cache"
	topicUtils "golang.52tt.com/services/topic-channel-logic/internal/utils"
	"sort"
	"strings"
	"time"
)

// v5.5.0版本的推荐列表，带开黑小队信息
func (s *Server) ListTopicChannelV3(ctx context.Context, in *topic_channel.ListTopicChannelV3Req) (*topic_channel.ListTopicChannelV3Resp, error) {
	out := &topic_channel.ListTopicChannelV3Resp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok || serviceInfo.UserID == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	if s.serviceConfigT.GetPublicSwitchConfig().GetTopicChannelHomePageSwitch(serviceInfo.MarketID) {
		//监管需求，屏蔽房间列表
		out.LoadFinish = true
		log.DebugWithCtx(ctx, "ListTopicChannelV3 开启监管 marketid %v, uid %v", serviceInfo.MarketID, serviceInfo.UserID)
		return out, nil
	}

	if in.Count == 0 {
		in.Count = 10
	}

	userId := serviceInfo.UserID
	log.DebugWithCtx(ctx, "ListTopicChannelV3 uid(%v) ip(%v) clientType(%v) version(%v) in(%v)\n",
		userId, serviceInfo.ClientIPAddr(), serviceInfo.ClientType, protocol.ClientVersion(serviceInfo.ClientVersion).String(), in)

	//var tabId uint32
	tabId := in.TabId

	var options []*channelPB.BlockOption
	var genOption []*genPB.BlockOption
	for _, o := range in.BlockOption {
		if o.ElemId > 0 && o.ElemId != InfElemID {
			options = append(options, &channelPB.BlockOption{BlockId: o.BlockId, ElemId: o.ElemId})
			genOption = append(genOption, &genPB.BlockOption{BlockId: o.BlockId, ElemId: o.ElemId})
		}
	}
	//先调推荐，超时或无返回再调topic channel拿一堆
	var getMode = genPB.GetRecommendationListReq_REFRESH
	var oldNum uint32
	if in.LoadMore != nil && in.LoadMore.RecommendMore != nil {
		getMode = genPB.GetRecommendationListReq_NEXTPAGE
		oldNum = in.LoadMore.RecommendMore.Num
	}

	//!!!推荐超时就会走保底!!!
	var cx, cancel = grpc.InheritContextWithInfoTimeout(ctx, time.Millisecond*time.Duration(s.serviceConfigT.GetOption().MaxRCMDTimeOutMsec))
	defer cancel()

	deadline, deadlineOK := ctx.Deadline()
	if deadlineOK && time.Until(deadline) < time.Millisecond*time.Duration(s.serviceConfigT.GetOption().MaxRCMDTimeOutMsec) {
		log.WarnWithCtx(ctx, "ListTopicChannelV3 userId(%d) context deadline is less than MaxRCMDTimeOutMsec \n", userId)
	}

	cxdeadline, cxdeadlineOK := cx.Deadline()
	rcmdContextRemain := time.Until(cxdeadline)
	if cxdeadlineOK && rcmdContextRemain < time.Millisecond*time.Duration(s.serviceConfigT.GetOption().MinRCMDTimeOutMsec) {
		log.WarnWithCtx(ctx, "ListTopicChannelV3 userId(%d) rcmdContextRemain(%d) is less than %dms \n", userId, rcmdContextRemain, s.serviceConfigT.GetOption().MinRCMDTimeOutMsec)
	}

	genResp := &genPB.GetRecommendationListResp{}
	var err error

	var sex uint32
	if in.Sex == topic_channel.Sex_Female {
		sex = 1
	} else if in.Sex == topic_channel.Sex_Male {
		sex = 2
	}

	tabIds := in.GetTabIds()
	if len(tabIds) == 0 && in.GetTabId() != 0 {
		tabIds = []uint32{in.GetTabId()}
	}

	supConfInst := &supervision.SupervisoryConf{
		RegisterLimitTime:      s.serviceConfigT.GetPublicSwitchConfig().GetRegisterLimitTime(),
		UserLevelLimit:         s.serviceConfigT.GetPublicSwitchConfig().GetUserLevelLimit(),
		RealNameStandardStatus: s.serviceConfigT.GetPublicSwitchConfig().GetRealNameStandardStatus(),
	}

	if rcmdContextRemain > time.Millisecond*time.Duration(s.serviceConfigT.GetOption().MinRCMDTimeOutMsec) {
		m := metrics.DISCOVERY_FUNC_TRACK(protocol.NewExactServerError(nil, 0, "topic-channel-logic.recommendationListTopicChannel")) //开启指标监控
		getRecommendationListReq := genPB.GetRecommendationListReq{
			Uid: userId, Limit: in.Count, TabId: tabId, BlockOptions: genOption, GetMode: getMode,
			ChannelEnterSource: uint32(gaChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_FINDPLAY_TAB),
			ClientType:         uint32(serviceInfo.ClientType), ClientVersion: serviceInfo.ClientVersion, MarketId: serviceInfo.MarketID,
			Env:              genPB.GetRecommendationListReq_Environment(s.v2Config.GenServerEnv),
			ChannelPackageId: in.ChannelPackageId,
			Sex:              sex,
			BrowseList: &rcmdCommon.RcmdBrowseInfo{
				NoBrowseList: in.GetBrowseList().GetNoBrowseList(),
			},
			RegulatoryLevel: genPB.REGULATORY_LEVEL(s.supervisor.GetUserRegulatoryLevelByUid(cx, userId, supConfInst)),
			TabIds:          tabIds,
			CategoryIds:     in.GetCategoryIds(),
			PrefGames:       prefGamesPb2Recommend(ctx, in.GetPrefGames()),
			Labels:          convert2RcmdGameLabel(in.Labels),
		}
		log.DebugWithCtx(ctx, "ListTopicChannelV3 GetRecommendationList getRecommendationListReq(%v)", utils.ToJson(getRecommendationListReq))
		warnLogUtil := topicUtils.NewWarnLog()
		genResp, err = s.genRecommendationClient.GetRecommendationList(cx, &getRecommendationListReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "ListTopicChannelV3 gen GetRecommendationList userId(%v) tabId(%v) err(%v)\n", userId, tabId, err)
		}
		warnLogUtil.WarnLog(fmt.Sprintf("GenRecommendationClient.GetRecommendationList %v", getRecommendationListReq))
		m.End() //关闭指标监控
	}

	log.InfoWithCtx(ctx, "ListTopicChannelV3 recommend req:%+v, userId:%d,genresp:%+v", in, userId, genResp)

	timeOutUidList := s.serviceConfigT.GetPublicSwitchConfig().GetTimeoutUidList()
	if s.serviceConfigT.Environment != conf.Production && len(timeOutUidList) > 0 {
		//log.DebugWithCtx(ctx,"ListTopicChannelV3 TimeoutUidList (%v)", s.serviceConfigT.GetOption().TimeoutUidList)
		shouldTimeout := false
		for _, v := range timeOutUidList {
			if v == userId {
				shouldTimeout = true
				break
			}
		}

		if shouldTimeout {
			log.DebugWithCtx(ctx, "ListTopicChannelV3 Manual Timeout userId %d", userId)
			genResp = &genPB.GetRecommendationListResp{}
		}
	}

	var listRecommendationInfo = &listRecommendationInfo{
		topicChannelInfo: map[uint32]*channelPB.ChannelInfo{},
		//entertainmentChannelInfo: map[uint32]*entertainmentChannelInfo{},
	}

	minorityGame := tab_cache.GetMinorityGameTab()

	var isRefresh bool
	if err != nil || len(genResp.ChannelId) == 0 && !genResp.BottomReached {
		m := metrics.DISCOVERY_FUNC_TRACK(protocol.NewExactServerError(nil, 0, "topic-channel-logic.fallbackListTopicChannel")) //开启指标监控
		//兜底策略
		listRecommendationInfo, out.LoadMore, err = s.fallbackListTopicChannel(ctx, in, options, userId, tabId)
		if err != nil {
			log.ErrorWithCtx(ctx, "ListTopicChannelV3 fallbackListTopicChannel userId(%v) err(%v)", userId, err)
			return out, err
		}
		//兜底 footprint  base64 encode 的 "tt_doudi"
		out.Footprint = base64.StdEncoding.EncodeToString([]byte("tt_doudi"))
		m.End() //关闭指标监控
	} else {
		//推荐有结果
		listRecommendationInfo.channelIds = genResp.ChannelId

		channelIds := genResp.ChannelId
		if len(channelIds) > 0 {
			resp, err := s.tcChannelClient.GetChannelByIds(ctx, &channelPB.GetChannelByIdsReq{Ids: channelIds})
			if err != nil {
				log.ErrorWithCtx(ctx, "ListTopicChannelV3 GetChannelByIds userId(%v) ids(%v) err(%v)", userId, channelIds, err)
				return out, err
			}
			log.DebugWithCtx(ctx, "ListTopicChannelV3 GetChannelByIds in(%d) out(%d)", len(channelIds), len(resp.Info))
			for _, info := range resp.Info {
				if !s.serviceConfigT.GetPublicSwitchConfig().GetChannelListNoFilterTabSwitch(tabId) && notMatchTab(tabId, info.TabId, minorityGame) {
					continue
				}
				listRecommendationInfo.topicChannelInfo[info.Id] = info
			}
		} else {
			log.InfoWithCtx(ctx, "ListTopicChannelV3 userId(%d) genResp.ChannelId is empty", userId)
			/*if len(s.serviceConfigT.GetPublicSwitchConfig().GetDoudiTabMap()) != 0 {
				if _, ok := s.serviceConfigT.GetPublicSwitchConfig().GetDoudiTabMap()[tabId]; ok {
					//兜底策略
					listRecommendationInfo, out.LoadMore, err = s.fallbackListTopicChannel(ctx, in, options, userId, tabId)
					if err != nil {
						log.ErrorWithCtx(ctx, "ListTopicChannelV3 fallbackListTopicChannel userId(%v) err(%v)", userId, err)
						return out, err
					}
					//兜底 footprint  base64 encode 的 "tt_doudi"
					out.Footprint = base64.StdEncoding.EncodeToString([]byte("tt_doudi"))
					log.DebugWithCtx(ctx, "ListTopicChannelV3 fall back, tabId:%d, out:%+v", tabId, out)
				}
			}*/
		}

		if !genResp.BottomReached {
			out.LoadMore = &topic_channel.ListRecommendTopicChannelLoadMore{RecommendMore: &topic_channel.ListRecommendTopicChannelLoadMore_RecommendMore{
				BottomReach: genResp.BottomReached, Num: oldNum + uint32(len(listRecommendationInfo.topicChannelInfo))}}
		}
		out.Footprint = genResp.GetFootprint()

		_, isRefresh = getGenRespNotify(ctx, genResp.NotifyList)
		if isRefresh {
			out.BaseResp = new(app.BaseResp)
			out.BaseResp.SuccessMsg = "刷新成功~正在努力寻找新房间"
		}
	}

	tabs := make([]*tabPB.Tab, 0)
	userIds := make([]uint32, 0)
	channelIds := make([]uint32, 0)
	//var findMusicInfoChannelIds, findKTVInfoChannelIds []uint32
	//var entertainmentChannelIds []uint32
	findMusicInfoChannelIds := make([]uint32, 0)
	findKTVInfoChannelIds := make([]uint32, 0)
	if len(listRecommendationInfo.topicChannelInfo) == 0 {
		out.LoadFinish = out.GetLoadMore() == nil
		return out, nil
	}

	for channelID, info := range listRecommendationInfo.topicChannelInfo {
		tabs = append(tabs, &tabPB.Tab{Id: info.TabId})
		//userIds = append(userIds, info.Creator)
		channelIds = append(channelIds, channelID)
		if s.v2Config.ShowMusicInfoTab[info.TabId] {
			findMusicInfoChannelIds = append(findMusicInfoChannelIds, info.Id)
		}
		if s.serviceConfigT.GetPublicSwitchConfig().IsKTVTab(info.TabId) {
			findKTVInfoChannelIds = append(findKTVInfoChannelIds, info.Id)
		}
	}

	if genResp != nil {
		for _, prefGame := range genResp.GetPrefGames() {
			tabs = append(tabs, &tabPB.Tab{Id: prefGame.GetTabId()})
		}
	}

	var tabData = map[uint32]*tabPB.Tab{}
	var tabBlockData = map[uint32]*tabPB.BlocksResp{}
	if len(tabs) > 0 {
		tabResp, err := s.tcTabClient.FiniteTabs(ctx, &tabPB.FiniteTabsReq{Tabs: tabs})
		if err != nil {
			log.ErrorWithCtx(ctx, "ListTopicChannelV3 FiniteTabs by tabIds(%v) err(%v)\n", tabs, err)
			return out, err
		}

		var tabIds []uint32
		for _, tab_ := range tabResp.GetTabs() {
			tabData[tab_.Id] = tab_
			tabIds = append(tabIds, tab_.GetId())
		}

		if len(tabIds) > 0 {
			blockResp, err := s.tcTabClient.BatchGetBlocks(ctx, &tabPB.BatchGetBlocksReq{TabId: tabIds})
			if err != nil {
				log.ErrorWithCtx(ctx, "ListTopicChannelV3 BatchGetBlocks by tabIds(%v) err(%v)\n", tabIds, err)
				return out, err
			}
			tabBlockData = blockResp.GetData()
		}
	}

	channelSimpleInfoResp, err := s.channelGoClient.BatchGetChannelSimpleInfo(ctx, &channel_go.BatchGetChannelSimpleInfoReq{
		OpUid:         userId,
		ChannelIdList: channelIds,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ListTopicChannelV3 list channel simple info by ids(%v) err(%v)\n", channelIds, err)
		return out, err
	}
	channelSimpleInfoMap := make(map[uint32]*channel_go.ChannelSimpleInfo, len(channelSimpleInfoResp.GetChannelSimpleList()))
	for _, info := range channelSimpleInfoResp.GetChannelSimpleList() {
		channelSimpleInfoMap[info.GetChannelId()] = info
	}

	for _, info := range channelSimpleInfoMap {
		userIds = append(userIds, info.GetBindId())
	}
	userIds = utils.TrimRepeatUint32Value(userIds)

	musicInfo, punishUsers, userMap, levelInfo, ktvInfos, memberMap, err := s.listTopicChannelV3SyncGetDependentInfo(ctx, findMusicInfoChannelIds, userIds, channelIds, findKTVInfoChannelIds, userId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ListTopicChannelV3 CheckPunishUser by ids(%v) err(%v) \n", userIds, err)
		return out, err
	}

	for _, id := range listRecommendationInfo.channelIds {
		if listRecommendationInfo.topicChannelInfo[id] == nil {
			//不是主题房，不返回
			log.WarnWithCtx(ctx, "ListTopicChannelV3 channelId(%v) is not topicchannel", id)
			continue
		}
		//这个房间是主题房
		var info = listRecommendationInfo.topicChannelInfo[id]

		var item = &topic_channel.TopicChannelItemV3{
			ChannelInfo:   &topic_channel.TopicChannelInfoV2{ChannelId: id, MusicDesc: musicInfo[id]},
			TabInfo:       &topic_channel.TopicChannelTab{},
			OwnerUserInfo: &topic_channel.ChannelOwnerInfo{},
			TraceInfo:     &topic_channel.RecommendationTraceInfo{},
		}

		if levelInfo != nil {
			item.LevelId = levelInfo[id].GetLevel().GetId()
		}

		item.TraceInfo.RecallFlag = getTraceInfoRecallFlag(genResp, id)
		var owner uint32
		if shouldSkipChannel(ctx, id, channelSimpleInfoMap[id]) {
			//channel info拿不到或锁房了，不返回
			continue
		} else {
			item.ChannelInfo = convertChannelSimpleInfoToTopicChannelInfoV2(channelSimpleInfoMap[id], item.ChannelInfo)
			owner = channelSimpleInfoMap[id].GetBindId()
		}
		//被惩罚用户不显示在开黑列表
		if punishUsers != nil && punishUsers[owner] {
			log.WarnWithCtx(ctx, "ListTopicChannelV3 owner(%d) is punishUsers,punishUsers (%v)", owner, punishUsers)
			continue
		}

		item.TabInfo = &topic_channel.TopicChannelTab{Id: info.GetTabId()}
		item.TabInfo = convertTabPBToTabInfo(tabData[info.GetTabId()], item.GetTabInfo())
		item.OwnerUserInfo = convertAccountToOwner(userMap[owner], item.OwnerUserInfo)

		//主题房的是房间人数, 5.1.0的版本改的
		item.ChannelInfo.OnMicCount = memberMap[id]
		//v5.5.0将房间人数挪动到数组中
		item.ChannelInfo.TopicChannelType = topic_channel.TopicChannelInfoV2Type_UGC
		//扩列专用
		/*var specialDesc, teamSize string
		var locationArray []string*/
		if tabBlockData[info.GetTabId()] != nil {
			//v5.5.0改版列表展示信息
			item.ChannelInfo.HeadDesc, item.ChannelInfo.SecondDesc, item.ChannelInfo.ThirdDesc, _, _, _ = getChannelDescV2(ctx, info.GetBlockOptions(), tabBlockData[info.GetTabId()].GetBlocks())
			item.ChannelInfo.ThirdDesc, item.ChannelInfo.SecondDesc, item.ChannelInfo.HeadDesc = s.printChannelDescInList(ctx, info.GetBlockOptions(), tabBlockData[info.GetTabId()].GetBlocks(), info.GetTabId(), minorityGame)
		}

		//5.9.0后使用-------------------
		item.HeadDesc, item.FirstDesc, item.SecondDesc = s.printChannelDescInList(ctx, info.GetBlockOptions(), tabBlockData[info.GetTabId()].GetBlocks(), info.GetTabId(), minorityGame)
		if val, ok := musicInfo[info.GetId()]; ok {
			item.SecondDescIcon = topic_channel.DescIcon_DescIcon_MUSIC
			item.SecondDesc = val
		} else if val, ok := ktvInfos[info.GetId()]; ok {
			item.SecondDescIcon = topic_channel.DescIcon_DescIcon_KTV
			item.SecondDesc = "演唱：" + val.GetCurPlayingSong().GetSongName()
			item.FirstDesc = fmt.Sprintf("已点歌曲：%d首", val.GetListLen())
		} else {
			item.SecondDescIcon = topic_channel.DescIcon_DescIcon_None
		}
		//------------------------------------------------------

		if genResp == nil || genResp.ChannelInfoMap[id] == nil {
			out.Items = append(out.Items, item)
			continue
		}
		//----------------------------------------------以下为地理位置信息
		for _, v := range genResp.ChannelInfoMap[id].GetRcmdLabels() {
			//开黑标签优先展示
			if v == genPB.RCMDLabel_GangUpWithHomeOwner {
				item.RcmdLabel = topic_channel.RCMDLabel_RCMDLabel_GangUpWithHomeOwner
				break
			} else if v == genPB.RCMDLabel_ChatWithHomeOwner {
				item.RcmdLabel = topic_channel.RCMDLabel_RCMDLabel_ChatWithHomeOwner
			}
		}

		if info.GetShowGeoInfo() {
			//该房间同意显示地理位置才拼接地理位置
			item.GeoInfo = trimLocationInfo(s.printLocationInfo(ctx, genResp.GetSelfLoc(), genResp.GetChannelInfoMap()[id].GetLoc()))
		}
		out.Items = append(out.Items, item)
	}

	/*if appointment {
		out.GangConf, err = s.getGangConf(ctx, userId, tabId)
		if err != nil {
			log.ErrorWithCtx(ctx, "ListTopicChannelV3 getGangConf err: %v\n", err)
			return nil, err
		}

		log.InfoWithCtx(ctx, "ListTopicChannelV3 getGangConf: %v\n", out.GetGangConf())
	}*/

	if genResp != nil {
		out.PrefGames = prefGamesRecommend2Pb(ctx, genResp.GetPrefGames(), tabData)
		out.PrefGamePos = genResp.GetInsertPos()
	}

	out.LoadFinish = out.LoadMore == nil
	log.InfoWithCtx(ctx, "ListTopicChannelV3 uid(%v) channelIds(%v) outLen(%d) prefGames(%v)\n", userId, listRecommendationInfo.channelIds, len(out.Items), out.GetPrefGames())
	return out, nil
}

// 将gamelabel转化为推荐协议类型
func convert2RcmdGameLabel(labels []*topic_channel.GameLabel) []*rcmd_channel_label.GameLabel {
	res := make([]*rcmd_channel_label.GameLabel, len(labels))

	for i, v := range labels {
		res[i] = &rcmd_channel_label.GameLabel{
			Val:         v.GetValValue(),
			DisplayName: v.GetDisplayName(),
			Type:        rcmd_channel_label.GameLabelType(v.GetType()),
			LabelType:   v.GetLabelType(),
		}
	}
	return res
}

func getGenRespNotify(ctx context.Context, notifyTypes []genPB.GetRecommendationListResp_NotifyType) (appointment, refresh bool) {
	for _, notifyType := range notifyTypes {
		if notifyType == genPB.GetRecommendationListResp_APPOINTMENT {
			appointment = true
		}
		if notifyType == genPB.GetRecommendationListResp_RefreshSucc {
			refresh = true
		}
	}
	return
}

func (s *Server) listTopicChannelV3SyncGetDependentInfo(ctx context.Context, findMusicInfoChannelIds, creators, channelIds,
	findKTVInfoChannelIds []uint32, uid uint32) (musicInfo map[uint32]string, punishMap map[uint32]bool, userMap map[uint32]*account.User,
	levelInfo map[uint32]*channel_level.GetChannelInfoResp, ktvInfos map[uint32]*ktvpb.PlayListStatus, memberMap map[uint32]uint32, err error) {
	musicInfo = make(map[uint32]string, 0)
	task := []func(*context.Context) error{
		func(ctx *context.Context) error {
			punishUsers, err := s.getPunishUsers(*ctx, creators)
			if err != nil {
				log.ErrorWithCtx(*ctx, "ListTopicChannelV3 CheckPunishUser by ids(%v) err(%v) \n", creators, err)
				return err
			}
			punishMap = punishUsers
			return nil
		},
		func(ctx *context.Context) error {
			userResp, err := s.accountClient.GetUsersMap(*ctx, creators)
			if err != nil {
				log.ErrorWithCtx(*ctx, "ListTopicChannelV3 GetUsersMap by ids(%v) err(%v) \n", creators, err)
				return err
			}
			userMap = userResp
			return nil
		},
		func(ctx *context.Context) error {
			if len(findMusicInfoChannelIds) == 0 {
				return nil
			}
			musicResp, err := s.channelMusicClient.BatGetChannelPlayingMusicInfo(*ctx, uid, &channelmusic.BatGetChannelPlayingMusicInfoReq{
				ChannelIdList: findMusicInfoChannelIds,
			})
			if err != nil {
				//报错了不返回就算了，接口不报错
				log.WarnWithCtx(*ctx, "ListTopicChannelV3 BatGetChannelPlayingMusicInfo by ids(%v) err(%v)\n", findMusicInfoChannelIds, err)
			} else {
				for _, item := range musicResp.MusicList {
					musicInfo[item.ChannelId] = item.MusicName + "-" + item.MusicAuthor
				}
			}
			return nil

		},
		func(ctx *context.Context) error {
			levelResp, err := s.channelLevelClient.BatchGetChannelInfo(*ctx, channelIds)
			if err != nil {
				//报错了不返回就算了，接口不报错
				log.ErrorWithCtx(*ctx, "ListTopicChannelV3 BatchGetChannelLevelInfo userId(%v) ids(%v) err(%v)\n", uid, channelIds, err)
			} else {
				levelInfo = levelResp
			}
			return nil
		},
		func(ctx *context.Context) error {
			if len(findKTVInfoChannelIds) == 0 {
				return nil
			}
			ktvResp, err := s.channelKtvCli.BatchGetPlayListStatus(*ctx, &ktvpb.GetPlayListStatusReq{ChannelIds: findKTVInfoChannelIds})
			if err != nil {
				//报错了不返回就算了，接口不报错
				log.ErrorWithCtx(*ctx, "ListTopicChannelV3 BatchGetPlayListStatus userId(%v) ids(%v) err(%v)\n", uid, channelIds, err)
			} else {
				ktvInfos = ktvResp.StatusesMapping
			}
			return nil
		},
		func(ctx *context.Context) error {
			memberMap = make(map[uint32]uint32, 0)
			memberMap, err = s.channelOLStatClient.BatchGetChannelMemberSize(*ctx, uid, channelIds)
			if err != nil {
				//报错了不返回就算了，接口不报错
				log.ErrorWithCtx(*ctx, "ListTopicChannelV3 BatchGetChannelMemberSize userId(%v) ids(%v) err(%v)\n", uid, channelIds, err)
			}
			return nil
		},
	}
	err = CoroutineDoWithSingleResult("", ctx, 5, task...)
	return
}

func (s *Server) fallbackListTopicChannel(ctx context.Context, in *topic_channel.ListTopicChannelV3Req, options []*channelPB.BlockOption,
	userId, tabId uint32) (listInfo *listRecommendationInfo, loadMore *topic_channel.ListRecommendTopicChannelLoadMore, err error) {
	listInfo = &listRecommendationInfo{
		topicChannelInfo: map[uint32]*channelPB.ChannelInfo{},
	}
	//兜底策略
	byTabLoadMore, listLoadMore := convertServerLoadMore(in.LoadMore)
	if tabId > 0 && tabId != InfElemID {
		resp, err := s.tcChannelClient.GetRecommendChannelListByTab(ctx, &channelPB.GetRecommendChannelListByTabReq{
			Uid: userId, Limit: in.Count, TabId: tabId, BlockOptions: options, LoadMore: byTabLoadMore,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "ListTopicChannelV3 fallbackListTopicChannel GetRecommendChannelListByTab userId(%v) err(%v)\n", userId, err)
			return listInfo, loadMore, err
		}

		log.DebugWithCtx(ctx, "ListTopicChannelV3 fallbackListTopicChannel GetRecommendChannelListByTab userId(%v) list by tab(%v) resp(%v)", userId, tabId, utils.ToJson(resp))
		for _, info := range resp.ChannelList {
			if isDisplayChannel(info.DisplayType) {
				//仅展示发布的
				listInfo.channelIds = append(listInfo.channelIds, info.Id)
				listInfo.topicChannelInfo[info.Id] = info
			}
		}
		//loadMore = convertTagLoadMore(resp.LoadMore)
		loadMore = convertTagLoadMoreV2(resp.LoadMore)
	} else {
		var tabIds []uint32
		resp, err := s.tcChannelClient.GetRecommendChannelList(ctx, &channelPB.GetRecommendChannelListReq{
			Uid: userId, Limit: in.Count, LoadMore: listLoadMore, TabIdList: tabIds, //ReturnPgc: 1,
		})

		log.DebugWithCtx(ctx, "ListTopicChannelV3 fallbackListTopicChannel TCGetRecommendChannelList userId(%v) list by tab(%v) resp(%v)", userId, tabId, utils.ToJson(resp))
		if err != nil {
			log.ErrorWithCtx(ctx, "ListTopicChannelV3 fallbackListTopicChannel TCGetRecommendChannelList userId(%v) err(%v)\n", userId, err)
			return listInfo, loadMore, err
		}
		for _, info := range resp.ChannelList {
			if info.IsRecommendChannel {
				//pgc房间 v5.5.0不展示
				continue
			}
			if isDisplayChannel(info.DisplayType) {
				//仅展示发布的
				listInfo.channelIds = append(listInfo.channelIds, info.Id)
				listInfo.topicChannelInfo[info.Id] = info
			}
		}
		loadMore = convertListLoadMore(resp.LoadMore, 0, nil)
	}
	return
}

func (s *Server) printChannelDescInList(ctx context.Context, blockOptions []*channelPB.BlockOption, blocks []*tabPB.Block, tabId uint32,
	minorityGame map[uint32]*tabPB.Tab) (headDesc, firstDesc, secondDesc string) {
	if len(blockOptions) == 0 || len(blocks) == 0 {
		//只有小众游戏在没有发布选项时还需要有描述字段
		if mtinfo, ok := minorityGame[tabId]; ok {
			//小众游戏名字即描述
			firstDesc = "游戏 : " + mtinfo.Name
		}
		return
	}
	//用户选择的
	var blockIdx = map[uint32]map[uint32]bool{}
	for _, block := range blockOptions {
		if blockIdx[block.BlockId] == nil {
			blockIdx[block.BlockId] = map[uint32]bool{block.ElemId: true}
		} else {
			blockIdx[block.BlockId][block.ElemId] = true
		}
	}
	//系统预设的
	var presetBlockMap = map[uint32]map[uint32]*tabPB.Elem{}
	for _, block := range blocks {
		for _, elem := range block.Elems {
			if presetBlockMap[block.Id] == nil {
				presetBlockMap[block.Id] = map[uint32]*tabPB.Elem{}
			}
			presetBlockMap[block.Id][elem.Id] = elem
		}
	}

	titleMap := make(map[string]string, 0)
	for _, block := range blocks {
		// todo PC端先过滤掉输入框类型的展示，后续有改动再说
		if block.GetMode() == tabPB.Block_USER_INPUT {
			continue
		}
		if len(blockIdx[block.Id]) > 0 {
			var allSelected = true
			if len(blockIdx[block.Id]) == len(presetBlockMap[block.Id]) { //系统预设选项个数与用户勾选个数相同
				for _, elem := range block.Elems { //检查用户勾选的选项是否与预设选项完全相同
					if !blockIdx[block.Id][elem.Id] { //用户未勾选该选项
						allSelected = false
						break
					}
				}
				if allSelected {
					titleMap[block.Title] = "不限"
				}
			} else {
				allSelected = false
			}
			if !allSelected {
				var idx []int
				for i, elem := range block.Elems {
					if blockIdx[block.Id][elem.Id] { //获取当前block下所有选中的elem数组的index（不是elem_id）即这个block都勾选了哪几位elem
						idx = append(idx, i)
					}
				}
				var stringSet [][]string
				if block.Title == "段位" {
					var lastIdx = -1
					for _, i := range idx {
						if lastIdx == -1 { //将第一个elem的tittle存入stringSet的第一位数组，二维数组的x轴上拼入
							stringSet = append(stringSet, []string{block.Elems[i].Title})
						} else if lastIdx+1 == i { //如果该elem与上个存入stringSet的elem是连着的
							if len(stringSet[len(stringSet)-1]) < 2 { //stringSet最后一位的数组长度为0或者1
								stringSet[len(stringSet)-1] = append(stringSet[len(stringSet)-1], block.Elems[i].Title) //二维数组y轴上拼入
							} else {
								stringSet[len(stringSet)-1][1] = block.Elems[i].Title //二维数组y轴上替换第二位
							}
						} else {
							stringSet = append(stringSet, []string{block.Elems[i].Title})
						}
						lastIdx = i
					}
				} else {
					for _, i := range idx {
						stringSet = append(stringSet, []string{block.Elems[i].Title})
					}
				}
				var str []string
				for _, slice := range stringSet {
					str = append(str, strings.Join(slice, "-"))
				}
				var title = strings.Join(str, "/")
				titleMap[block.Title] = title
			}
		}
	}

	headDescSlice := make([]string, 0)
	firstDescSlice := make([]string, 0)
	if titleMap["段位"] == "不限" {
		titleMap["段位"] = "不限段位"
	}

	headDescSlice = appendDesc(headDescSlice, titleMap["区服"], titleMap["模式"])
	secDescSlice := make([]string, 0)
	isMobaGame := false
	if titleMap["区服"] != "" && titleMap["人数"] != "" {
		isMobaGame = true
	}
	if s.roomHandler.isChatTab(tabId) { //扩列
		firstDescSlice = appendDesc(firstDescSlice, "主题 : "+titleMap["主题"])
	} else if isMobaGame { //moba
		firstDescSlice = appendDesc(firstDescSlice, titleMap["人数"], titleMap["段位"])
		for k, v := range titleMap {
			if v == "" {
				v = "不限"
			}
			if k != "区服" && k != "模式" && k != "人数" && k != "段位" {
				secDescSlice = append(secDescSlice, k+" : "+v)
			}
		}
	} else { //其他
		tmpSlice := make([]string, 0)
		for k, v := range titleMap {
			if k != "区服" && k != "模式" {
				tmpSlice = append(tmpSlice, k+" : "+v)
			}
		}
		//map的range是随机顺序的。。。不得不这么转一下
		sort.Strings(tmpSlice)
		for i, v := range tmpSlice {
			if i == 0 {
				firstDescSlice = append(firstDescSlice, v)
			} else {
				secDescSlice = append(secDescSlice, v)
			}
		}
	}

	headDesc = strings.Join(headDescSlice, " · ")
	if mtinfo, ok := minorityGame[tabId]; ok {
		//小众游戏名字即描述
		firstDesc = "游戏 : " + mtinfo.Name
	} else {
		firstDesc = strings.Join(firstDescSlice, " | ")
	}
	secondDesc = strings.Join(secDescSlice, " | ")

	return
}

func appendDesc(desc []string, str ...string) []string {
	for _, v := range str {
		if v != "" {
			desc = append(desc, v)
		}
	}
	return desc
}

func (s *Server) printLocationInfo(ctx context.Context, userLocation, channelLocation *common.LocationInfo) string {
	log.DebugWithCtx(ctx, "printLocationInfo userLocation(%v) channelLocation(%v)", userLocation, channelLocation)
	if s.serviceConfigT.GetPublicSwitchConfig().IsSensitiveProvince(channelLocation.GetProvince()) {
		return ""
	}

	if userLocation.GetCityCode() != 0 && userLocation.GetCityCode() == channelLocation.GetCityCode() {
		if s.serviceConfigT.GetPublicSwitchConfig().IsSensitiveCity(userLocation.GetCity()) {
			convertProvince, ok := conf.LocationConvertMap.Load(userLocation.GetProvince())
			if ok {
				return locationPrefix + convertProvince.(string)
			}
			return locationPrefix + userLocation.GetProvince()
		}
		return locationPrefix + userLocation.GetCity()
	}

	if userLocation.GetProvinceCode() != 0 && userLocation.GetProvinceCode() == channelLocation.GetProvinceCode() {
		convertProvince, ok := conf.LocationConvertMap.Load(userLocation.GetProvince())
		if ok {
			return locationPrefix + convertProvince.(string)
		}
		return locationPrefix + userLocation.Province
	}

	return ""
}

func trimLocationInfo(locationInfo string) string {
	return strings.TrimRight(strings.TrimRight(strings.TrimRight(locationInfo, "市"), "省"), "地区")
}

func convertChannelSimpleInfoToTopicChannelInfoV2(channelSimpleInfo *channel_go.ChannelSimpleInfo,
	channelInfo *topic_channel.TopicChannelInfoV2) *topic_channel.TopicChannelInfoV2 {
	channelInfo.Name = channelSimpleInfo.GetName()
	channelInfo.DisplayId = channelSimpleInfo.GetDisplayId()
	channelInfo.AppId = channelSimpleInfo.GetAppId()
	channelInfo.HasPwd = channelSimpleInfo.GetHasPwd()
	channelInfo.ChannelType = channelSimpleInfo.GetChannelType()
	channelInfo.TopicTitle = channelSimpleInfo.GetTopicTitle()
	channelInfo.IconMd5 = channelSimpleInfo.GetIconMd5()
	channelInfo.BindId = channelSimpleInfo.GetBindId()
	return channelInfo
}

func shouldSkipChannel(ctx context.Context, channelId uint32, channelSimpleInfo *channel_go.ChannelSimpleInfo) bool {
	if channelSimpleInfo == nil {
		log.WarnWithCtx(ctx, "shouldSkipChannel channel(%d) can't get  channel info \n", channelId)
		return true
	} else if channelSimpleInfo.GetHasPwd() {
		//被锁房了就不返回
		log.WarnWithCtx(ctx, "shouldSkipChannel %d HasPwd \n", channelId)
		return true
	} else {
		return false
	}
}

func convertTabPBToTabInfo(tab *tabPB.Tab, tabInfo *topic_channel.TopicChannelTab) *topic_channel.TopicChannelTab {
	if tab != nil {
		tabInfo.Name = tab.Name
		tabInfo.ListBackgroundUri = tab.ShowImageUri
		tabInfo.CardMainColor = tab.NewcomerColor
		tabInfo.CardBackgroundUri = tab.NewcomerImageUri
		tabInfo.CardFontBackgroundUri = tab.NewcomerFontBackgroundUri
		tabInfo.CardFontColor = tab.NewcomerFontColor
		tabInfo.RoomLabel = tab.RoomLabel

		//雷达和房间转移相关v5.5.0
		tabInfo.RadarDistributionImage = tab.RadarDistributionImage
		tabInfo.RoomDistributionImage = tab.RoomDistributionImage
		tabInfo.BottomTextColor = tab.BottomTextColor
		tabInfo.WelcomeTextColor = tab.WelcomeTextColor

		tabInfo.FollowLabelImg = tab.FollowLabelImg
		tabInfo.MaskLayer = tab.MaskLayer
	}
	return tabInfo
}

func convertAccountToOwner(accountInfo *account.User, ownerInfo *topic_channel.ChannelOwnerInfo) *topic_channel.ChannelOwnerInfo {
	if accountInfo != nil {
		ownerInfo.Uid = accountInfo.Uid
		ownerInfo.Account = accountInfo.Username
		ownerInfo.Sex = uint32(accountInfo.Sex)
		ownerInfo.Nickname = accountInfo.Nickname
	}
	return ownerInfo
}

func isDisplayChannel(displayType []channelPB.ChannelDisplayType) bool {
	for _, v := range displayType {
		if v == channelPB.ChannelDisplayType_DISPLAY_AT_MAIN_PAGE {
			//仅展示发布的
			return true
		}
	}
	return false
}

func prefGamesPb2Recommend(ctx context.Context, prefGames []*topic_channel.PrefGame) []*genPB.PrefGame {
	list := make([]*genPB.PrefGame, 0)
	for _, prefGame := range prefGames {
		pg := &genPB.PrefGame{TabId: prefGame.GetTabId()}
		for _, label := range prefGame.GetLabels() {
			l := &genPB.PrefGameLabel{Id: label.GetId(), Val: label.GetValValue()}
			if label.GetType() == topic_channel.PrefGame_Label_TypeSystem {
				l.Type = genPB.PrefGameLabelType_SYSTEM
			} else if label.GetType() == topic_channel.PrefGame_Label_TypeCustom {
				l.Type = genPB.PrefGameLabelType_CUSTOM
			} else {
				log.WarnWithCtx(ctx, "prefGamesPb2Recommend invalid label type")
				continue
			}

			pg.Labels = append(pg.Labels, l)
		}

		list = append(list, pg)
	}

	log.DebugWithCtx(ctx, "prefGamesPb2Recommend list: %v\n", list)
	return list
}

func prefGamesRecommend2Pb(ctx context.Context, prefGames []*genPB.PrefGame, tabMap map[uint32]*tabPB.Tab) []*topic_channel.PrefGame {
	const defaultTabImg = "https://ga-album-cdnqn.52tt.com/prod-yunying/e9d0-17f2f93e79e.png"

	list := make([]*topic_channel.PrefGame, 0)
	for _, prefGame := range prefGames {
		tab := tabMap[prefGame.GetTabId()]
		if tab == nil || tab.GetId() == 0 {
			log.WarnWithCtx(ctx, "prefGamesRecommend2Pb tab %d not found", prefGame.GetTabId())
			continue
		}

		tabImg := tab.GetSmallCardUrl()
		if tabImg == "" {
			tabImg = defaultTabImg
		}

		pg := &topic_channel.PrefGame{TabId: tab.GetId(), TabName: tab.GetName(), TabImg: tabImg}
		for _, label := range prefGame.GetLabels() {
			l := &topic_channel.PrefGame_Label{Id: label.GetId(), ValValue: label.GetVal()}
			if label.GetType() == genPB.PrefGameLabelType_SYSTEM {
				l.Type = topic_channel.PrefGame_Label_TypeSystem
			} else if label.GetType() == genPB.PrefGameLabelType_CUSTOM {
				l.Type = topic_channel.PrefGame_Label_TypeCustom
			} else {
				log.WarnWithCtx(ctx, "prefGamesRecommend2Pb invalid label type")
				continue
			}

			pg.Labels = append(pg.Labels, l)
		}

		list = append(list, pg)
	}

	log.DebugWithCtx(ctx, "prefGamesRecommend2Pb list: %v\n", list)
	return list
}
