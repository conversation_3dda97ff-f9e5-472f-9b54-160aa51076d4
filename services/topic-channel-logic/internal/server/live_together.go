package server

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	channelpb "golang.52tt.com/protocol/app/channel"
	pb "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/protocol/common/status"
	channel_go "golang.52tt.com/protocol/services/channel-go"
	topic_channel_pb "golang.52tt.com/protocol/services/topic_channel/channel"
	ugc_live_together "golang.52tt.com/protocol/services/ugc-live-together"
	"time"
)

func (s *Server) getConfigPreCheck(ctx context.Context, in *pb.GetLiveTogetherConfigReq, sv *grpc.ServiceInfo) (error, bool) {
	if in.GetChannelId() == 0 {
		log.InfoWithCtx(ctx, "GetLiveTogetherConfig zero cid in:%s, sv:%s", in.String(), sv.String())
		return nil, false
	}
	log.DebugWithCtx(ctx, "GetLiveTogetherConfig channelId(%d) sv:%v", in.GetChannelId(), sv)

	uid := sv.UserID
	simpleInfo, err := s.channelGoClient.GetChannelSimpleInfo(ctx,&channel_go.GetChannelSimpleInfoReq{
		ChannelId:     in.GetChannelId(),
		OpUid:         uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLiveTogetherConfig GetChannelSimpleInfo ChannelId(%d) error:%v", in.GetChannelId(), err)
		return err, false
	}
	if channelpb.ChannelType(simpleInfo.GetChannelSimple().GetChannelType()) != channelpb.ChannelType_USER_CHANNEL_TYPE {
		return nil, false
	}
	return nil, true
}

func (s *Server) getChannelRoleInfo(ctx context.Context, cid, uid uint32) (channelRole, ownerUid uint32, err error) {
	adminList, err := s.channelClient.GetChannelAdmin(ctx, uid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLiveTogetherConfig getChannelRoleInfo GetChannelAdmin reqUid:%d ChannelId(%d) error:%v", uid, cid, err)
		return channelRole, ownerUid, err
	}
	for _, admin := range adminList {
		if admin.GetAdminRole() == 1 {
			ownerUid = admin.GetUid()
			if admin.GetUid() == uid {
				channelRole = 1
				break
			}
		}
	}
	return channelRole, ownerUid, nil
}

func (s *Server) isOwnerInRoom(ctx context.Context, reqUid, ownerUid, inCid uint32) (error, bool) {
	// 判断房主是否在房
	cid, err := s.channelolClient.GetUserChannelId(ctx, reqUid, ownerUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLiveTogetherConfig GetUserChannelId ownerUid(%d) error:%v", ownerUid, err)
		return err, false
	}
	if cid == inCid {
		return nil, true
	}
	return nil, false
}

func (s *Server) GetLiveTogetherConfig(ctx context.Context, in *pb.GetLiveTogetherConfigReq) (out *pb.GetLiveTogetherConfigResp, err error) {
	out = &pb.GetLiveTogetherConfigResp{}
	sv, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}

	uid := sv.UserID
	err, pass := s.getConfigPreCheck(ctx, in, sv)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLiveTogetherConfig getConfigPreCheck in:%s sv:%s error:%v", in.String(), sv.String(), err)
		return out, err
	}

	if !pass {
		log.WarnWithCtx(ctx, "GetLiveTogetherConfig getConfigPreCheck in:%s sv:%s no pass check", in.String(), sv.String())
		return out, nil
	}
	// 获取房间房主uid，以及请求者在房间中的角色
	channelRole, ownerUid, err := s.getChannelRoleInfo(ctx, in.GetChannelId(), uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLiveTogetherConfig getChannelRoleInfo in:%s sv:%s error:%v", in.String(), sv.String(), err)
		return out, err
	}

	ct, err := s.tcChannelClient.GetChannelPlayModel(ctx, &topic_channel_pb.GetChannelPlayModelReq{
		ChannelId: in.GetChannelId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLiveTogetherConfig GetChannelPlayModel ChannelId(%d) error:%v", in.GetChannelId(), err)
		return out, err
	}
	os := protocol.NewTerminalType(sv.TerminalType).OS()
	marketId := sv.MarketID

	if skipLowerVersion(os, marketId, sv.ClientVersion) {
		return out, nil
	}

	info, err := s.ugcLiveTogetherClient.GetChannelLiveInfo(ctx, &ugc_live_together.GetChannelLiveInfoReq{
		ChannelId: in.GetChannelId(),
		TabId:     ct.GetTabId(),
		Role:      channelRole,
		App:       convertMarketIdToApp(marketId),
		Platform:  convertOsToPlatform(os),
		OwnerUid:  ownerUid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLiveTogetherConfig GetChannelLiveInfo ChannelId(%d) tabId(%d) error:%v", in.GetChannelId(), ct.GetTabId(), err)
		return out, err
	}
	log.DebugWithCtx(ctx, "GetLiveTogetherConfig GetChannelLiveInfo channelId:%d info:%v", in.GetChannelId(), info)

	if info.GetLiveConfig().GetPlayFormat() == ugc_live_together.LiveConfig_PlayFormatRtmp && skipRtmp(sv.ClientVersion) {
		log.DebugWithCtx(ctx, "GetLiveTogetherConfig channel(%d) tab(%d) skip rtmp", in.GetChannelId(), ct.GetTabId())
		return out, nil
	}

	// 判断房主是否在房
	err, isHasOwner := s.isOwnerInRoom(ctx, uid, ownerUid, in.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLiveTogetherConfig isOwnerInRoom in:%s sv:%s error:%v", in.String(), sv.String(), err)
		return out, err
	}
	out = convertLiveTogetherConfig(info)
	out.HasOwner = isHasOwner
	out.RefreshTime = uint32(time.Now().Unix())
	log.DebugWithCtx(ctx, "GetLiveTogetherConfig in:%s out:%s", in.String(), out.String())
	return out, nil
}

func skipLowerVersion(os protocol.OS, marketId, cVersion uint32) bool {
	if os == protocol.ANDROID {
		return false
	}
	if marketId == uint32(app.BaseReq_MARKET_MAIKE) && cVersion < protocol.FormatClientVersion(6, 9, 0) {
		return true
	}
	if marketId == uint32(app.BaseReq_MARKET_HUANYOU) && cVersion < protocol.FormatClientVersion(6, 9, 1) {
		return true
	}
	if marketId == uint32(app.BaseReq_MARKET_MIJING) && cVersion <= protocol.FormatClientVersion(6, 9, 0) {
		return true
	}
	return false
}

func skipRtmp(version uint32) bool {
	return version < protocol.FormatClientVersion(6, 11, 0)
}

func convertMarketIdToApp(marketId uint32) ugc_live_together.App {
	switch marketId {
	case 0:
		return ugc_live_together.App_AppTT
	case 2:
		return ugc_live_together.App_AppHuanYou
	case 5:
		return ugc_live_together.App_AppMaiKe
	case 6:
		return ugc_live_together.App_AppMiJing
	default:
		return ugc_live_together.App_AppNone
	}
}

func convertOsToPlatform(os protocol.OS) ugc_live_together.Platform {
	if os == protocol.ANDROID {
		return ugc_live_together.Platform_PlatformAndroid
	}
	if os == protocol.IOS {
		return ugc_live_together.Platform_PlatformIOS
	}
	return ugc_live_together.Platform_PlatformAll
}

func (s *Server) SetLiveTogetherStatus(ctx context.Context, in *pb.SetLiveTogetherStatusReq) (out *pb.SetLiveTogetherStatusResp, err error) {
	out = &pb.SetLiveTogetherStatusResp{}
	sv, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	uid := sv.UserID
	_, ownerId, err := s.getChannelRoleInfo(ctx, in.GetChannelId(), uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetLiveTogetherStatus getChannelRoleInfo in:%s sv:%s error:%v", in.String(), sv.String(), err)
		return out, err
	}
	var isChannelRoot bool
	if ownerId == uid {
		isChannelRoot = true
	}

	if !isChannelRoot {
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelPermissionDenied)
	}

	_, err = s.ugcLiveTogetherClient.SetChannelLiveStatus(ctx, &ugc_live_together.SetChannelLiveStatusReq{
		ChannelId:  in.GetChannelId(),
		LiveStatus: uint32(in.GetStatus()),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SetLiveTogetherStatus SetChannelLiveStatus ChannelId(%d) error:%v", in.GetChannelId(), err)
		return out, err
	}

	return
}

func convertLiveTogetherConfig(in *ugc_live_together.GetChannelLiveInfoResp) (out *pb.GetLiveTogetherConfigResp) {
	out = &pb.GetLiveTogetherConfigResp{
		AdsConfig:    convertAdsConfig(in.GetAdsConfig()),
		LiveConfig:   convertLiveConfig(in.GetLiveConfig(), in.GetLiveStatus()),
		BubbleConfig: convertBubbleConfig(in.GetBubbleConfig()),
	}
	return
}

func convertAdsConfig(in *ugc_live_together.AdsConfig) (out *pb.AdsConfig) {
	out = &pb.AdsConfig{
		Id:        in.GetId(),
		Link:      in.GetLink(),
		UpdatedAt: in.GetUpdatedAt(),
		LiveId:    in.GetLiveId(),
	}
	return
}

func convertLiveConfig(in *ugc_live_together.LiveConfig, status *ugc_live_together.LiveStatus) (out *pb.LiveConfig) {
	out = &pb.LiveConfig{
		Id:               in.GetId(),
		ConfigUpdatedAt:  in.GetUpdatedAt(),
		LiveStatus:       pb.UGCLiveTogetherStatus(status.GetLiveStatus()),
		StatusChangeAt:   status.GetStatusChangeAt(),
		BeginAt:          in.GetBeginAt(),
		EndAt:            in.GetEndAt(),
		TabId:            in.GetTabId(),
		LiveLink:         in.GetLiveLink(),
		EntranceText:     in.GetEntranceText(),
		EntrancePic:      in.GetEntrancePic(),
		FloatPic:         in.GetFloatPic(),
		VideoTitle:       in.GetVideoTitle(),
		VideoHeight:      in.GetVideoWidth(),
		VideoWidth:       in.GetVideoLength(),
		PlayFormat:       pb.LiveConfig_PlayFormat(in.GetPlayFormat()),
		HasStrongGuide:   in.GetHasStrongGuide(),
		GuideGif:         in.GetGuideGif(),
		GuideTitle:       in.GetGuideTitle(),
		LimitCloseSecond: in.GetLimitCloseSecond(),
	}
	return
}

func convertBubbleConfig(in *ugc_live_together.BubbleConfig) (out *pb.BubbleConfig) {
	out = &pb.BubbleConfig{
		Id:        in.GetId(),
		UpdatedAt: in.GetUpdatedAt(),
		TabId:     in.GetTabId(),
		BeginAt:   in.GetBeginAt(),
		EndAt:     in.GetEndAt(),
		Text:      in.GetText(),
	}
	return
}
