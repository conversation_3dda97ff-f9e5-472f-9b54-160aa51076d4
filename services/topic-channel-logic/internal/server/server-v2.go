package server

import (
	"context"
	"fmt"
	"golang.52tt.com/kaihei-pkg/supervision"
	channel_go "golang.52tt.com/protocol/services/channel-go"
	channelPlayTabPb "golang.52tt.com/protocol/services/channel-play-tab"
	configserverPB "golang.52tt.com/protocol/services/configserver"
	"golang.52tt.com/services/topic-channel-logic/internal/switch_cache"
	"golang.52tt.com/services/topic-channel-logic/internal/tab_cache"
	"math"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"golang.52tt.com/pkg/cost_time_reporter"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	appChannelPB "golang.52tt.com/protocol/app/channel"
	gaChannelPB "golang.52tt.com/protocol/app/channel"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/protocol/common/status"
	channelPB "golang.52tt.com/protocol/services/topic_channel/channel"
	genPB "golang.52tt.com/protocol/services/topic_channel/recommendation_gen"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/topic-channel-logic/internal/conf"
	topicUtils "golang.52tt.com/services/topic-channel-logic/internal/utils"
)

const (
	//createTodayCountPrefix = "create-today-"
	costReportNameSpace = "userline_topic_channel_logic"
)

func (s *Server) QuickFormTeam(ctx context.Context, in *topic_channel.QuickFormTeamReq) (*topic_channel.QuickFormTeamResp, error) {
	out := &topic_channel.QuickFormTeamResp{}
	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)

	if s.serviceConfigT.GetPublicSwitchConfig().GetTopicChannelHomePageSwitch(serviceInfo.MarketID) {
		// 监管需求，屏蔽快速匹配功能
		log.DebugWithCtx(ctx, "QuickFormTeam 开启监管 marketid %v, uid %v", serviceInfo.MarketID, serviceInfo.UserID)
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelQuickMatchNoChannel)
	}

	costReporter := cost_time_reporter.NewTimeCostCounterWithName("topic-channel-logic/QuickFormTeam", costReportNameSpace)
	defer func() {
		if len(costReporter.TimeCostList) > 0 {
			costReporter.ReportByHistogram(protogrpc.NewContextWithInfo(ctx))
		}
	}()

	userId := serviceInfo.UserID
	if in.RequestUid != 0 {
		userId = in.RequestUid
	}
	if in.TabId == InfElemID {
		in.TabId = 0
	}
	log.InfoWithCtx(ctx, "QuickFormTeam uid(%v) in(%v)", userId, in)

	if in.TabId > 0 {
		if serviceInfo.ClientType == protocol.ClientTypeANDROID && serviceInfo.ClientVersion < protocol.FormatClientVersion(5, 0, 3) ||
			serviceInfo.ClientType == protocol.ClientTypeIOS && serviceInfo.ClientVersion <= protocol.FormatClientVersion(5, 0, 3) {
			match := false
			for _, id := range s.v2Config.NotMiniGameTabIds {
				if id == in.TabId {
					match = true
					break
				}
			}
			if !match {
				return out, protocol.NewExactServerError(nil, status.ErrTopicChannelQuickMatchVersionMiniGameRequired)
			}
		}
	}

	defer func() {
		if out.GetChannelId() == 0 {
			log.WarnWithCtx(ctx, "QuickFormTeam user %d match channel empty", serviceInfo.UserID)
		} else {
			log.InfoWithCtx(ctx, "QuickFormTeam user %d match channel %d", serviceInfo.UserID, out.GetChannelId())
		}
	}()
	if !switch_cache.GetSwitchStatusByType(configserverPB.SwitchBusinessType_GAME_UGC_QUICK_MATCH) {
		// 先调江正，超时或无返回再调topic channel随便拿个
		var genOption []*genPB.BlockOption
		for _, o := range in.BlockOption {
			if o.ElemId > 0 && o.ElemId != InfElemID {
				genOption = append(genOption, &genPB.BlockOption{BlockId: o.BlockId, ElemId: o.ElemId})
			}
		}
		// 先调江正，超时或无返回再调topic channel拿一堆
		cx, cancel := context.WithTimeout(ctx, 800*time.Millisecond)
		if s.serviceConfigT.GetEnv() == conf.Testing {
			cx, cancel = context.WithTimeout(ctx, 3000*time.Millisecond)
		}
		defer cancel()

		warnLogUtil := topicUtils.NewWarnLog()
		genResp, err := s.genRecommendationClient.GetRecommendationList(cx, &genPB.GetRecommendationListReq{
			Uid: userId, Limit: 1, TabId: in.TabId, BlockOptions: genOption, GetMode: genPB.GetRecommendationListReq_NEXTPAGE,
			ChannelEnterSource: uint32(gaChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_QUICK_MATCH),
			ClientType:         uint32(serviceInfo.ClientType), ClientVersion: serviceInfo.ClientVersion, MarketId: serviceInfo.MarketID,
			Env:              genPB.GetRecommendationListReq_Environment(s.v2Config.GenServerEnv),
			ChannelPackageId: in.ChannelPackageId,
			// RegulatoryLevel:  genPB.REGULATORY_LEVEL(s.getUserRegulatoryLevel(cx, userId)),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "QuickFormTeam gen GetRecommendationList userId(%v) tabId(%v) err(%v)", userId, in.TabId, err)
		}
		costReporter.Tick("rcmd_cost_time")
		warnLogUtil.WarnLog(fmt.Sprintf("QuickFormTeam userId:%d, tabId:%d, err:%v", userId, in.TabId, err))

		if err == nil {
			if len(genResp.ChannelId) == 0 {
				log.WarnWithCtx(ctx, "QuickFormTeam rcmd channel empty")
				return out, nil
			}
			cid := genResp.GetChannelId()[0]

			punished, serr := s.isPunishChannel(ctx, userId, cid)
			if serr != nil {
				log.ErrorWithCtx(ctx, "QuickFormTeam rcmd isPunishChannel cid(%d) err: %v", cid, serr)
				return out, serr
			}
			if punished {
				log.WarnWithCtx(ctx, "QuickFormTeam rcmd channel %d punished", cid)
				return out, nil
			}

			out.ChannelId = cid
			costReporter.Tick("filter_strategy_cost_time")
		}
		if out.ChannelId > 0 {
			log.InfoWithCtx(ctx, "QuickFormTeam rcmd uid:%d, in:%s, out:%s", userId, in.String(), out.String())
			return out, nil
		}
	}

	// recommendation gen没找到房间，继续试试
	// if len(genResp.ChannelId) == 0 && genResp.BottomReached {
	//	return out, nil
	// }
	var channelList []*channelPB.ChannelInfo
	if in.TabId > 0 && in.TabId != InfElemID {
		var options []*channelPB.BlockOption
		for _, o := range in.BlockOption {
			if o.ElemId > 0 && o.ElemId != InfElemID {
				options = append(options, &channelPB.BlockOption{BlockId: o.BlockId, ElemId: o.ElemId})
			}
		}
		resp, err := s.tcChannelClient.GetRecommendChannelListByTab(ctx, &channelPB.GetRecommendChannelListByTabReq{
			Uid: userId, Limit: 1, TabId: in.TabId, BlockOptions: options, NotClearHistory: true, NotCheckHistory: true, NotSaveHistory: true,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "QuickFormTeam GetRecommendChannelListByTab userId(%v) err(%v)", userId, err)
			return out, err
		}
		channelList = resp.ChannelList
	} else {
		var tabIds []uint32
		if serviceInfo.ClientType == protocol.ClientTypeANDROID && serviceInfo.ClientVersion < protocol.FormatClientVersion(5, 0, 3) ||
			serviceInfo.ClientType == protocol.ClientTypeIOS && serviceInfo.ClientVersion <= protocol.FormatClientVersion(5, 0, 3) {
			tabIds = s.v2Config.NotMiniGameTabIds
		}
		resp, err := s.tcChannelClient.GetRecommendChannelList(ctx, &channelPB.GetRecommendChannelListReq{
			Uid: userId, Limit: 1, NotClearHistory: true, NotCheckHistory: true, NotSaveHistory: true, TabIdList: tabIds,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "QuickFormTeam GetRecommendChannelList userId(%v) err(%v)", userId, err)
			return out, err
		}
		channelList = resp.ChannelList
	}

	if len(channelList) > 0 {
		cid := channelList[0].GetId()

		punished, serr := s.isPunishChannel(ctx, userId, cid)
		if serr != nil {
			log.ErrorWithCtx(ctx, "QuickFormTeam tc isPunishChannel cid(%d) err: %v", cid, serr)
			return out, serr
		}
		if punished {
			log.WarnWithCtx(ctx, "QuickFormTeam tc channel %d punished", cid)
			return out, nil
		}

		out.ChannelId = cid
	}
	costReporter.Tick("fallback_strategy_cost_time")
	log.InfoWithCtx(ctx, "QuickFormTeam fallback uid:%d, in:%s, out:%s", userId, in.String(), out.String())
	return out, nil
}

func (s *Server) ListTabBlocks(ctx context.Context, in *topic_channel.ListTabBlocksReq) (*topic_channel.ListTabBlocksResp, error) {
	out := &topic_channel.ListTabBlocksResp{}
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	log.DebugWithCtx(ctx, "ListTabBlocks uid(%v) in(%v)\n", serviceInfo.UserID, in)

	// 不限，直接返回空
	if in.TabId == math.MaxUint32 {
		return out, nil
	}

	var tabInfo []*tabPB.Tab
	if in.TagId != 0 {
		tabInfoByTag, terr := s.tcTabClient.FiniteTabsByTags(ctx, &tabPB.FiniteTabsByTagsReq{TagId: []uint32{in.TagId}})
		if terr != nil {
			log.ErrorWithCtx(ctx, "ListTabBlocks FiniteTabsByTags error TagId(%v) err: %v\n", in.TagId, terr)
			return out, terr
		}
		tabInfo = tabInfoByTag.GetTabs()
	} else {
		finiteTabsResp, terr := s.tcTabClient.FiniteTabs(ctx, &tabPB.FiniteTabsReq{Tabs: []*tabPB.Tab{{Id: in.TabId}}})
		if terr != nil {
			log.ErrorWithCtx(ctx, "ListTabBlocks FiniteTabs userId(%v) tabId(%v) err(%v)", serviceInfo.UserID, in.TabId, terr)
			return out, terr
		}
		tabInfo = finiteTabsResp.GetTabs()
	}

	if len(tabInfo) == 0 {
		log.WarnWithCtx(ctx, "ListTabBlocks serviceInfo:%s, in:%s", serviceInfo.String(), in.String())
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelTabNotFound)
	}

	// 版本控制
	if s.v2Config.ServerEnv != "staging" && tabInfo[0].GetLinkId() == 0 {
		// 生产环境的时候，需要根据策略过滤
		if isFilter := s.supervisor.MiniGameStageStrategy(tabInfo[0], serviceInfo, tab_cache.GetWhiteList()); isFilter {
			return out, nil
		}
	}

	tabType := tabPB.TabType_NORMAL
	matchType := tabPB.Tab_QUICK
	var tabVer uint32
	if len(tabInfo) > 0 {
		tabType = tabPB.TabType(tabInfo[0].TabType)
		matchType = tabInfo[0].MatchType
		tabVer = tabInfo[0].Version
	}
	// 目前默认只有小游戏走临时房匹配,以后可能会有其他类型房间
	if matchType == tabPB.Tab_TEMPORARYHOUSE {
		out.MatchType = topic_channel.ListTabBlocksResp_TEMPORARYHOUSE
		out.ChannelSource = uint32(appChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_QUICK_MATCH_V2)
	} else {
		out.MatchType = topic_channel.ListTabBlocksResp_QUICK
		out.ChannelSource = uint32(appChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_QUICK_MATCH)
	}

	bs, err := s.tcTabClient.Blocks(ctx, &tabPB.BlocksReq{
		TabId: tabInfo[0].Id,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ListTabBlocks userId (%v) Blocks err(%v)", serviceInfo.UserID, err)
		return out, err
	}

	// iOS有bug的版本(删除block，会导致iOS旧版无法发布房间)
	bugVer := protocol.FormatClientVersion(5, 4, 17)

	blocks := make([]*topic_channel.Block, 0, 100)
	for _, b := range bs.GetBlocks() {
		if in.GetMode() != topic_channel.ListTabBlocksReq_FILER_IN_RADAR && b.BlockType == 1 {
			// 仅在新版开黑列表和有问题的旧版iOS中展示的信息
			if serviceInfo.ClientType == protocol.ClientTypeIOS && in.GetMode() == topic_channel.ListTabBlocksReq_PUBCHANNEL && serviceInfo.ClientVersion <= bugVer {
			} else {
				continue
			}
		}

		// todo PC端先过滤掉输入框类型的展示，后续有改动再说
		if b.GetMode() == tabPB.Block_USER_INPUT {
			continue
		}

		if tabType == tabPB.TabType_MINIGAME && in.GetMode() != topic_channel.ListTabBlocksReq_MATCHTEAMMATE {
			// 只有小游戏并且是快速匹配才显示二级字段
			continue
		}
		if in.GetMode() == topic_channel.ListTabBlocksReq_ZAIYA_MATCH_TEAMMATE {
			if strings.Contains(b.GetTitle(), "模式") || strings.Contains(b.GetTitle(), "人数") {
				continue
			}
		}

		block := &topic_channel.Block{}
		block.Id = b.GetId()
		block.Title = b.GetTitle()
		block.ControlTeamSize = b.GetControlTeamSize()
		if in.GetMode() == topic_channel.ListTabBlocksReq_PUBCHANNEL || in.GetMode() == topic_channel.ListTabBlocksReq_FILER_IN_RADAR {
			if b.GetMode() == tabPB.Block_SINGLE {
				block.Mode = topic_channel.Block_SINGLE
			} else if b.GetMode() == tabPB.Block_MULTI {
				block.Mode = topic_channel.Block_MULTI
			}
		} else if in.GetMode() == topic_channel.ListTabBlocksReq_MATCHTEAMMATE || in.GetMode() == topic_channel.ListTabBlocksReq_FILTER {
			block.Mode = topic_channel.Block_SINGLE
		}
		if in.GetMode() == topic_channel.ListTabBlocksReq_PUBCHANNEL {
			// 最多选N个，当mostSelectNum不为零时，block.Mode无效
			block.MostSelectNum = b.GetMostSelectNum()
		}

		elems := make([]*topic_channel.Elem, 0, 100)
		if (in.GetMode() == topic_channel.ListTabBlocksReq_MATCHTEAMMATE && tabType != tabPB.TabType_MINIGAME) ||
			in.GetMode() == topic_channel.ListTabBlocksReq_ZAIYA_MATCH_TEAMMATE ||
			in.GetMode() == topic_channel.ListTabBlocksReq_FILTER ||
			in.GetMode() == topic_channel.ListTabBlocksReq_FILER_IN_RADAR {
			elem := &topic_channel.Elem{
				Relations: &topic_channel.Relation{},
			}
			elem.Id = InfElemID
			elem.Title = "不限"
			elem.Mode = topic_channel.Elem_INFINITE
			elems = append(elems, elem)
		}
		for _, e := range b.GetElems() {
			elem := &topic_channel.Elem{
				Relations: &topic_channel.Relation{},
			}
			// 如果是小游戏并且二级字段默认字段,不显示
			if e.Title == "default_model" {
				continue
			}
			elem.Title = e.GetTitle()
			elem.Id = e.GetId()
			elem.Mode = topic_channel.Elem_NORMAL
			if in.GetMode() == topic_channel.ListTabBlocksReq_PUBCHANNEL {
				elem.Relations.BlockId = e.GetContacts().GetBlockId()
				elem.Relations.Before = e.GetContacts().GetBefore()
				elem.Relations.After = e.GetContacts().GetAfter()
			}
			elem.MiniGameModel = e.MiniGameModel
			elem.TeamSize = e.TeamSize

			elems = append(elems, elem)

		}
		if len(elems) == 0 { // 小游戏默认二级字段不显示block二级标题
			continue
		}
		block.Elems = elems
		if serviceInfo.ClientType == protocol.ClientTypeIOS && s.v2Config.IsIOSReviewing {
			// 苹果监管需要临时写死，带安卓，android字眼不返回
			if strings.Contains(block.Title, "安卓") || strings.Contains(strings.ToLower(block.Title), "android") {
				continue
			}
			var newElems []*topic_channel.Elem
			for _, elem := range block.Elems {
				if !strings.Contains(elem.Title, "安卓") && !strings.Contains(strings.ToLower(elem.Title), "android") {
					newElems = append(newElems, elem)
				}
			}
			block.Elems = newElems
			if len(newElems) == 0 {
				continue
			}
		}

		if serviceInfo.ClientType == protocol.ClientTypeIOS && s.v2Config.IosReplaceAndroidString {
			// 苹果监管需要临时写死，带安卓，android字眼不返回
			block.Title = strings.Replace(block.Title, "安卓手Q", "安Q", -1)
			block.Title = strings.Replace(block.Title, "安卓微信", "安微", -1)
			for i := 0; i < len(block.Elems); i++ {
				block.Elems[i].Title = strings.Replace(block.Elems[i].Title, "安卓手Q", "安Q", -1)
				block.Elems[i].Title = strings.Replace(block.Elems[i].Title, "安卓微信", "安微", -1)
			}
		}

		blocks = append(blocks, block)
	}

	out.TabId = tabInfo[0].Id
	out.Blocks = blocks
	out.TabVersion = tabVer
	out.TabType = topic_channel.ListTabBlocksResp_TabType(tabType)
	out.FreshOption = s.serviceConfigT.GetPublicSwitchConfig().GetFreshOption()

	if s.serviceConfigT.Environment == conf.Production {
		out.IsHiddenGeoOption = s.serviceConfigT.GetPublicSwitchConfig().IsHiddenGeoOption()
	} else {
		out.IsHiddenGeoOption = s.serviceConfigT.GetPublicSwitchConfig().IsHiddenGeoOptTest()
	}

	log.InfoWithCtx(ctx, "ListTabBlocks uid(%v) out(%v)", serviceInfo.UserID, out)
	return out, nil
}

func (s *Server) GetFormTeamInfo(ctx context.Context, in *topic_channel.GetFormTeamInfoReq) (*topic_channel.GetFormTeamInfoResp, error) {
	out := &topic_channel.GetFormTeamInfoResp{}
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	userId := serviceInfo.UserID
	log.DebugWithCtx(ctx, "GetFormTeamInfo uid(%v) in(%v)", userId, in)

	onlineResp, err := s.tcChannelClient.GetOnlineInfo(ctx, &channelPB.GetOnlineInfoReq{OnlineUserCount: 30})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFormTeamInfo GetOnlineInfo err(%v)", err)
		return out, err
	}

	if onlineResp.RoomCount > 0 {
		out.TotalOnlineCount = s.v2Config.OnlineMultiple*onlineResp.RoomCount + s.v2Config.OnlineAddend + uint32(rand.Intn(10)) //nolint:gosec
	}

	if len(onlineResp.OnlineUserList) > 0 {
		user, err := s.accountClient.GetUsersMap(ctx, onlineResp.OnlineUserList)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetFormTeamInfo GetUsersMap uids(%v) err(%v)", onlineResp.OnlineUserList, err)
			return out, err
		}
		for _, u := range user {
			out.UserList = append(out.UserList, &topic_channel.GetFormTeamInfoResp_ExampleUser{Uid: u.Uid, Account: u.Username})
		}
	}
	out.UserType = topic_channel.GetFormTeamInfoResp_NORMAL
	out.DefaultTabId = s.v2Config.DefaultSelectTabId
	out.DefaultTabName = s.v2Config.DefaultSelectTabName
	user, err := s.accountClient.GetUser(ctx, userId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFormTeamInfo GetUser userId(%v) err(%v)", userId, err)
		return out, err
	}
	//tagResp, e := s.userTagClient.GetUserTag(ctx, userId)
	//if e != nil {
	//	log.ErrorWithCtx(ctx, "GetFormTeamInfo GetUserTag userId(%v) err(%v)", userId, e)
	//	return out, e
	//}
	cardResp, e := s.gameCardClient.GetGameCard(ctx, userId)
	if e != nil {
		log.ErrorWithCtx(ctx, "GetFormTeamInfo GetGameCard userId(%d) in(%s) err(%v)", userId, in.String(), e)
		return out, e
	}
	// log.DebugWithCtx(ctx,"GetFormTeamInfo uid(%v) tag length(%v)", userId, len(tagResp))
	log.DebugWithCtx(ctx, "GetFormTeamInfo uid(%v) tag(%v)", userId, cardResp)
	var hasMiniGameCard bool
	for _, x := range cardResp {
		// 没有东西可以判断只能用名字...................
		if x.GameCardName == "社交桌游" {
			out.DefaultTabId = s.v2Config.DefaultSelectMiniGameId
			out.DefaultTabName = s.v2Config.DefaultSelectMiniGameTabName
			out.UserType = topic_channel.GetFormTeamInfoResp_TABLE_GAMING
			hasMiniGameCard = true
			break
		}
	}
	var isMiniGameUI bool
	if user.RegisteredAt >= uint32(time.Now().Add(-time.Second*time.Duration(s.v2Config.QuickMatchNewUserDuration)).Unix()) {
		// - 注册3天内，“快速匹配”功能的底图变更为小游戏风格的底图，3天后恢复默认底图
		// - 自然流量倾向玩小游戏的用户的“快速匹配”功能变更为小游戏风格底图
		// - 小游戏渠道包用户的“快速匹配”功能变更为渠道对应的小游戏风格底图
		if strings.ToLower(in.ChannelPackageId) != "" && strings.ToLower(in.ChannelPackageId) != "official" {
			// 是渠道包
			// channel package -> tab id
			//tabResp, err := s.tcTabClient.Tabs(ctx, 0, 100)
			//if err != nil {
			//	log.WarnWithCtx(ctx, "GetFormTeamInfo Tabs userId(%v) err(%v)", userId, err)
			//} else {
			tabMap := tab_cache.GetTabIdMap()
			for _, t := range tabMap {
				for _, id := range t.ChannelPackageId {
					if (id == in.ChannelPackageId || strings.HasSuffix(in.ChannelPackageId, id)) && id != "" {
						out.PackageTabInfoJson = fmt.Sprintf(`{"tabId":"%d","tabName":"%s"}`, t.Id, t.Name)
						out.UserType = topic_channel.GetFormTeamInfoResp_CHANNEL_PACKAGE
						out.DefaultTabId = t.Id
						out.DefaultTabName = t.Name
						isMiniGameUI = true
						break
					}
				}
				//}
			}
		}
		if !isMiniGameUI {
			//// 上面没有匹配到，再看看普通包是不是选了社交桌游
			//for _, x := range tagResp {
			//	// 没有东西可以判断只能用名字...................
			//	if x.TagName == "社交桌游" {
			//		isMiniGameUI = true
			//		break
			//	}
			//}
			isMiniGameUI = hasMiniGameCard
		}
	}

	out.BackgroundUrl, out.GuideImageUrl, out.BubbleImageUrl = s.quickMatchHandler.getInfo(strings.ToLower(in.ChannelPackageId), isMiniGameUI)

	if in.CheckGameCardStatus {
		if user.RegisteredAt < s.v2Config.V2Timestamp {
			out.FillGameCard = true
		}
	}

	return out, nil
}

// GetTabList v5.0.3版本使用的获取游戏玩法
func (s *Server) GetTabList(ctx context.Context, in *topic_channel.GetTabListReq) (*topic_channel.GetTabListResp, error) {
	out := &topic_channel.GetTabListResp{}
	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := serviceInfo.UserID
	log.InfoWithCtx(ctx, "GetTabList uid(%v) in(%v)", uid, in)

	// 监管配置过滤
	supConfInst := &supervision.SupervisoryConf{
		RegisterLimitTime:      s.serviceConfigT.GetPublicSwitchConfig().GetRegisterLimitTime(),
		UserLevelLimit:         s.serviceConfigT.GetPublicSwitchConfig().GetUserLevelLimit(),
		RealNameStandardStatus: s.serviceConfigT.GetPublicSwitchConfig().GetRealNameStandardStatus(),
	}
	tabFilter, categoryFilter := s.supervisor.GetFilterMap(ctx, serviceInfo, in.ChannelPkg, supConfInst)
	tabMap := tab_cache.GetTabIdMap()

	switch in.GetReturnedMode() {
	case topic_channel.GetTabListReq_FASTPC:
		var err error
		out.TabCategory, err = s.getFastPCTabListPC(ctx, tabMap, serviceInfo, tabFilter, categoryFilter)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetTabList uid (%+v) err(%v)", uid, err)
			return out, err
		}
		return out, nil
	}

	req := &tabPB.GetCategoryTitleReq{
		Skip:  in.GetPage() * in.GetCount(),
		Limit: in.GetCount(),
	}
	categoryList, err := s.tcTabClient.GetCategoryTitleList(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTabList uid (%+v) err(%v)", uid, err)
		return out, err
	}

	tabList := make([]*topic_channel.GetTabListResp_TabCategory, 0)
	if in.ReturnedMode == topic_channel.GetTabListReq_INFINITE {
		opt := &topic_channel.GetTabListResp_TabCategory{}
		opt.CategoryId = 1111 // 无意义
		opt.CategoryName = ""
		specialTab := make([]*topic_channel.Tab, 0)
		elem := &topic_channel.Tab{}
		elem.Name = "不限"
		elem.Id = math.MaxUint32
		elem.TabProperty = topic_channel.Tab_INFINITE
		specialTab = append(specialTab, elem)
		opt.TabDetail = specialTab
		tabList = append(tabList, opt)
	}

	displayList := s.getDisplayMinorityGame(in.SelfGameList)
	displayTabList := make([]*topic_channel.Tab, 0)
	var minorityGameCategory uint32
	for _, v := range displayList {
		if in.ReturnedMode == topic_channel.GetTabListReq_MORECARDS {
			break
		}
		minorityGameCategory = v.TabInfo.CategoryId
		elem := convertTab(v.TabInfo)

		displayTabList = append(displayTabList, elem)
	}

	var tabInfos []*tabPB.Tab
	platform := protocol.NewTerminalType(serviceInfo.TerminalType).Platform()
	for _, b := range categoryList.GetCategoryList() {
		// 监管开启时，未成年人忽略游戏
		if categoryFilter[b.CategoryId] {
			continue
		}

		if platformTypeNotMatch(b.PlatformType, platform) {
			continue
		}

		ut := &topic_channel.GetTabListResp_TabCategory{
			SpecialCategoryMapping: b.GetSpecialCategoryMapping(),
		}
		// 需要根据版本判断调用区分更多的tab信息能够独立配置 v-5.2.2
		if ok := AmazingVersion(serviceInfo.ClientType, serviceInfo.ClientVersion, in.ReturnedMode, 5, 2, 1); ok {
			if uint32(b.CategoryType) != uint32(tabPB.Scene_MORE_TABS) {
				continue
			}
			rsp, err := s.channelPlayTabClient.GetQuickMatchConfig(ctx, &channelPlayTabPb.GetQuickMatchConfigReq{
				ConfigType: channelPlayTabPb.QuickMatchConfigType_MoreCardTabList,
				Page:       1,
				Limit:      50,
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "channelPlayTabClient.GetQuickMatchConfig fail, uid (%+v) err(%v)", uid, err)
				return out, err
			}

			newTabInfos := make([]*tabPB.Tab, 0, len(rsp.GetSceneInfo()))
			for _, sceneTab := range rsp.GetSceneInfo() {
				if sceneTab.CategoryId != b.CategoryId {
					continue
				}
				if v, ok := tabMap[sceneTab.GetTabId()]; ok {
					newTabInfos = append(newTabInfos, v)
				}
			}
			tabInfos = newTabInfos
			if len(tabInfos) == 0 {
				// 分类下没有游戏，则不显示分类
				continue
			}

		} else {
			if uint32(b.CategoryType) != uint32(tabPB.Scene_NO_DEFAULT) {
				continue
			}

			tabInfos = tab_cache.GetCategoryTabMap(b.CategoryId)
			if len(tabInfos) == 0 {
				// 分类下没有游戏，则不显示分类
				continue
			}
			tabInfos = sortTabsByPlatform(tabInfos, platform)
		}

		ut.CategoryId = b.CategoryId
		ut.CategoryName = b.Title
		ut.CanSelectNum = b.GetCanSelectNum()
		tabs := make([]*topic_channel.Tab, 0)
		replaceTab := make(map[uint32]bool, 0)

		for _, t := range tabInfos {
			if t.LinkId > 0 {
				replaceTab[t.LinkId] = true
			}
		}

		for _, b := range tabInfos {
			if s.v2Config.ServerEnv != "staging" {
				// 生产环境的时候，需要根据策略过滤
				if isFilter := s.supervisor.MiniGameStageStrategy(b, serviceInfo, tab_cache.GetWhiteList()); isFilter {
					continue
				}
			}
			// 监管开启时，未成年人忽略游戏 由于是从游戏列表拉到的必须再过滤一次
			if tabFilter[b.GetId()] || categoryFilter[b.GetCategoryId()] {
				continue
			}

			// IsNeedMinorityGame=true需要外显所有小众游戏，否则跳过小众游戏
			if !in.GetIsNeedMinorityGame() && b.IsMinorityGame {
				continue
			}

			if platformTypeNotMatch(b.PlatformType, platform) {
				continue
			}

			if b.HomePageType != tabPB.HomePageType_HomePageTypeGAME && b.RoomLabel == "" {
				continue
			}

			if in.ReturnedMode == topic_channel.GetTabListReq_MORECARDS {
				if b.LinkId == 0 {
					// 没被关联的游戏
					if _, ok := replaceTab[b.Id]; ok {
						continue
					}
				}
			}

			t := convertTab(b)

			if in.ReturnedMode == topic_channel.GetTabListReq_MORECARDS {
				if b.TabType == tabPB.Tab_MINIGAME {
					s.RLock()
					if v, ok := s.miniGameConfig[b.TagId]; ok {
						t.MiniGameConfig = v
					}
					s.RUnlock()
				}
			}
			tabs = append(tabs, t)
		}

		if len(tabs) == 0 {
			continue
		}

		// 如果不需要外显所有小众游戏，走旧逻辑强插客户端传参带上的部分小众游戏
		if !in.GetIsNeedMinorityGame() && b.CategoryId == minorityGameCategory {
			tabs = append(displayTabList, tabs...)
		}

		if in.ReturnedMode == topic_channel.GetTabListReq_MORECARDS {
			targetVersion := s.serviceConfigT.GetOption().QuickMatchABTestConf.StartVersion
			isAbTest := s.isNewVersion(ctx, serviceInfo.ClientVersion, targetVersion) && s.isInAbtest(ctx, serviceInfo.UserID)
			if isAbTest {
				tabs = reSort(tabs)
			}
		}

		ut.TabDetail = tabs
		ut.TabType = topic_channel.GetTabListResp_TabType(tabs[0].TabType)
		tabList = append(tabList, ut)
	}
	out.TabCategory = tabList
	return out, nil
}

// IsNewVersion 旧版本(6.15.0之前)不分对照实验组
func (s *Server) isNewVersion(ctx context.Context, curVersion uint32, targetVersion string) bool {
	verDetail := strings.Split(targetVersion, ".")
	if len(verDetail) < 3 {
		return false
	}
	major, minor, patch := getMajorMinorPatch(verDetail)
	filterVer := protocol.FormatClientVersion(uint8(major), uint8(minor), uint16(patch))
	log.DebugWithCtx(ctx, "IsOldVersion, major:%v, minor:%v, patch:%v, clientVersion:%v, filterVer:%v", major,
		minor, patch, curVersion, filterVer)

	return curVersion >= filterVer
}

func getMajorMinorPatch(verDetail []string) (major, minor, patch int) {
	if len(verDetail) > 2 {
		major, _ = strconv.Atoi(verDetail[0])
		minor, _ = strconv.Atoi(verDetail[1])
		patch, _ = strconv.Atoi(verDetail[2])
	}
	return
}

func (s *Server) isInAbtest(ctx context.Context, uid uint32) bool {
	abConf := s.serviceConfigT.GetOption().QuickMatchABTestConf
	lab, err := s.newAbtestCli.GetABTestTypeMapSimpleByUidAndLayerTag(ctx, uid, abConf.LayerTag)
	if err != nil {
		log.ErrorWithCtx(ctx, "IsInAbtest err:%s, uid:%d", err.Error(), uid)
		return false
	}
	log.DebugWithCtx(ctx, "IsInAbtest info, uid:%d, lab:%+v", uid, lab)
	if lab == nil || lab.ExptvID != abConf.ExptvId {
		return false
	}
	return true
}

func reSort(in []*topic_channel.Tab) []*topic_channel.Tab {
	// 当前数据库数据符合的只有几十个
	//sort.SliceStable(out.TabCategory, func(i, j int) bool {
	//	return out.TabCategory[i].TabType > out.TabCategory[j].TabType
	//})
	miniGameTabs := make([]*topic_channel.Tab, 0, len(in))
	otherTabs := make([]*topic_channel.Tab, 0, len(in))
	for _, tabInfo := range in {
		if tabInfo.TabType == topic_channel.Tab_MINI_GAME {
			miniGameTabs = append(miniGameTabs, tabInfo)
		} else {
			otherTabs = append(otherTabs, tabInfo)
		}
	}
	miniGameTabs = append(miniGameTabs, otherTabs...)
	return miniGameTabs
}

func (s *Server) isPunishChannel(ctx context.Context, uid, cid uint32) (punished bool, err error) {
	if s.serviceConfigT.GetPublicSwitchConfig().GetCheckPunish() {
		channelInfoResp, rpcErr := s.channelGoClient.GetChannelSimpleInfo(ctx, &channel_go.GetChannelSimpleInfoReq{
			ChannelId: cid,
			OpUid:     uid,
		})
		if rpcErr != nil {
			log.ErrorWithCtx(ctx, "isPunishChannel GetChannelSimpleInfo cid(%d) err: %v", cid, rpcErr)
			err = rpcErr
			return
		}
		channelInfo := channelInfoResp.GetChannelSimple()
		checkResp, rpcErr := s.punishUserCli.CheckPunishUser(ctx, []uint32{channelInfo.GetBindId()}, 0)
		if rpcErr != nil {
			log.ErrorWithCtx(ctx, "isPunishChannel CheckPunishUser uid(%d) err: %v", uid, rpcErr)
			err = rpcErr
			return
		}

		result := checkResp.GetUserPunishResult()
		if result != nil && result[channelInfo.GetBindId()] {
			punished = true
		}
	}

	return
}

// 极速版PC玩法数据
func (s *Server) getFastPCTabListPC(ctx context.Context, tabMap map[uint32]*tabPB.Tab, serviceInfo *protogrpc.ServiceInfo, tabFilter map[uint32]bool, categoryFilter map[uint32]bool) ([]*topic_channel.GetTabListResp_TabCategory, error) {
	res := make([]*topic_channel.GetTabListResp_TabCategory, 0)
	fastCategoryConfigs, err := s.channelPlayTabClient.GetFastPCCategoryConfig(ctx, &channelPlayTabPb.GetFastPCCategoryConfigReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFastPCTabListPC GetFastPCCategoryConfig error: %v", err)
		return res, err
	}
	for _, c := range fastCategoryConfigs {
		tabDetail := make([]*topic_channel.Tab, 0)
		for _, tabId := range c.GetTabIds() {
			tabInfo, ok := tabMap[tabId]
			if !ok {
				log.InfoWithCtx(ctx, "GetFastPCTabListPC tabId(%v) not found", tabId)
				continue
			}
			// 监管过滤
			if tabFilter[tabId] || categoryFilter[tabInfo.GetCategoryId()] {
				log.InfoWithCtx(ctx, "GetFastPCTabListPC tabId(%v) filter", tabId)
				continue
			}
			// 玩法过滤
			if s.supervisor.MiniGameStageStrategy(tabInfo, serviceInfo, tab_cache.GetWhiteList()) ||
				tabInfo.GetHomePageType() == tabPB.HomePageType_HomePageTypeNone {
				log.InfoWithCtx(ctx, "GetFastPCTabListPC GamePageFilterStrategy tabId:%d", tabId)
				continue
			}
			tabDetail = append(tabDetail, convertTab(tabInfo))
		}
		if len(tabDetail) == 0 {
			log.InfoWithCtx(ctx, "GetFastPCTabListPC category(%v) tabDetail empty", c.GetId())
			continue
		}

		res = append(res, &topic_channel.GetTabListResp_TabCategory{
			CategoryId:            c.GetId(),
			CategoryName:          c.GetName(),
			TabDetail:             tabDetail,
			PcFastCategoryMapping: uint32(c.GetCategoryType()),
			PcFastJumpType:        uint32(s.serviceConfigT.GetOption().GetFastPCCategoryJumpType(c.GetId())),
			CategoryImageUrl:      c.GetIcon(),
		})
	}
	return res, nil
}
