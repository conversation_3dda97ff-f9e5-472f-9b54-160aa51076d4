package server

import (
	"context"
	"encoding/json"
	gameServerV2Client "golang.52tt.com/clients/game-server-v2"
	channel_go "golang.52tt.com/protocol/services/channel-go"
	music_topic_channel "golang.52tt.com/protocol/services/music-topic-channel"
	"math"
	"math/rand"
	"strconv"
	"sync"
	"time"

	account_go "golang.52tt.com/clients/account-go"
	censoring_proxy "golang.52tt.com/clients/censoring-proxy"
	channelListening "golang.52tt.com/clients/channel-listening"
	channel_play_tab "golang.52tt.com/clients/channel-play-tab"
	"golang.52tt.com/clients/configserver"
	device_info_service "golang.52tt.com/clients/datacenter/device-info-service"
	"golang.52tt.com/clients/expsvr"
	gangup_channel "golang.52tt.com/clients/gangup-channel"
	hobby_channel "golang.52tt.com/clients/hobby-channel"
	singaround "golang.52tt.com/clients/sing-a-round"
	rcmdChannelLabel "golang.52tt.com/clients/topic-channel/rcmd-channel-label"
	ttc_proxy "golang.52tt.com/clients/ttc-proxy"
	ugc_live_together "golang.52tt.com/clients/ugc-live-together"
	user_music_rank_cli "golang.52tt.com/clients/user-music-rank"
	userline_common_api "golang.52tt.com/clients/userline-common-api"
	"golang.52tt.com/protocol/services/demo/echo"
	genPB "golang.52tt.com/protocol/services/topic_channel/recommendation_gen"
	"golang.52tt.com/services/topic-channel-logic/internal/client"
	"golang.52tt.com/services/topic-channel-logic/internal/mgr/channel_push"
	"golang.52tt.com/services/topic-channel-logic/internal/mgr/feedback"
	"golang.52tt.com/services/topic-channel-logic/internal/mgr/tab_list"
	"golang.52tt.com/services/topic-channel-logic/internal/tab_cache"
	grpcOP "google.golang.org/grpc"

	channel_ktv "golang.52tt.com/clients/channel-ktv"
	channelol_stat_go "golang.52tt.com/clients/channelol-stat-go"
	gameCard "golang.52tt.com/clients/game-card"
	punishuser "golang.52tt.com/clients/punish-user"
	"golang.52tt.com/clients/rcmd/music_channel"
	"golang.52tt.com/pkg/bylink"
	"golang.52tt.com/services/topic-channel-logic/internal/breaker"

	"golang.52tt.com/clients/abtest"
	channel_level "golang.52tt.com/clients/channel-level"
	"golang.52tt.com/services/topic-channel-logic/internal/speedlimit"

	"github.com/Shopify/sarama"
	"github.com/golang/groupcache/singleflight"
	"golang.52tt.com/clients/ugc/friendship"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/services/topic-channel-logic/internal/conf"

	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/channel"
	openGame "golang.52tt.com/clients/channel-open-game"
	openGameRecord "golang.52tt.com/clients/channel-open-game-record"
	"golang.52tt.com/clients/channelim"
	"golang.52tt.com/clients/channelmic"
	"golang.52tt.com/clients/channelmusic"
	"golang.52tt.com/clients/channelol"
	"golang.52tt.com/clients/entertainmentrecommendback"
	tmpOpenGame "golang.52tt.com/clients/tmp-channel-open-game"
	tcChannel "golang.52tt.com/clients/topic-channel/channel"
	recommendation_gen "golang.52tt.com/clients/topic-channel/recommendation-gen"
	miniGameGen "golang.52tt.com/clients/topic-channel/recommendation-mini-game-gen"
	tcTab "golang.52tt.com/clients/topic-channel/tab"
	"golang.52tt.com/kaihei-pkg/supervision"
	newAbtest "golang.52tt.com/pkg/abtest"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	gaChannelPB "golang.52tt.com/protocol/app/channel"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/protocol/common/status"
	channelimPB "golang.52tt.com/protocol/services/channelim"
	channelmicPB "golang.52tt.com/protocol/services/channelmicsvr"
	entertainmentRecommendBack "golang.52tt.com/protocol/services/entertainmentRecommendBackSvr"
	channelPB "golang.52tt.com/protocol/services/topic_channel/channel"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	fall_back_cache "golang.52tt.com/services/topic-channel-logic/internal/switch_cache"
	"golang.52tt.com/services/topic-channel/notify"
)

var (
	InfElemID uint32 = math.MaxUint32
)

const DefaultRoomName = "快来和我一起玩吧"
const (
	locationPrefix      = "同在"
	getUserInfoErrorMsg = "获取用户信息失败"
)

type StartConfig struct {
	// [optional] from startup arguments
	ChannelTimeoutDisappearDuration    uint64 `json:"channel_timeout_disappear_duration"`
	ChannelBackgroundDisappearDuration uint64 `json:"channel_background_disappear_duration"`
	LoopTimeoutDisappearDuration       int64  `json:"loop_timeout_disappear_duration"`

	TabWhiteList map[uint32]bool `json:"tab_white_list"`
	Environment  string          `json:"environment"`

	// from config file
	//TopRecommendChannelConfig *topRecommendChannelConfig `json:"top_recommend_channel_config"`

	V2Config *v2Config `json:"v2_config"`

	ThirdPartyGameConfig map[uint32]*thirdPartyGameConfig `json:"third_party_game_config"`
}

func (sc *StartConfig) genDefaultValue() {
	/*
		if sc.TopRecommendChannelConfig == nil {
			sc.TopRecommendChannelConfig = &topRecommendChannelConfig{
				uidModValue:     0,
				uidSuffix:       []uint32{},
				registerStartAt: 0,
				newUserDuration: 0,
				specialUidSet:   make(map[uint32]bool),
			}
		}*/
	if sc.V2Config == nil {
		sc.V2Config = &v2Config{}
	}
	sc.V2Config.defaultValue()

	if sc.ChannelTimeoutDisappearDuration == 0 {
		sc.ChannelTimeoutDisappearDuration = 3600
	}
	if sc.ChannelBackgroundDisappearDuration == 0 {
		sc.ChannelBackgroundDisappearDuration = 30
	}
	if sc.LoopTimeoutDisappearDuration == 0 {
		sc.LoopTimeoutDisappearDuration = 30
	}
	if sc.ThirdPartyGameConfig == nil {
		sc.ThirdPartyGameConfig = make(map[uint32]*thirdPartyGameConfig)
	}
	if sc.Environment == "" {
		sc.Environment = conf.Production
	}
}

type Server struct {
	ugcLiveTogetherClient *ugc_live_together.Client

	channelClient       channel.IClient
	accountClient       account.IClient
	accountgoClient     *account_go.Client
	channelmicClient    *channelmic.Client
	channelolClient     *channelol.Client
	channelOLStatClient channelol_stat_go.IClient
	tcTabClient         tcTab.IClient
	tcChannelClient     tcChannel.IClient
	channelGoClient     channel_go.ChannelGoClient

	channelimClient              *channelim.Client
	entertainmentRecommendClient *entertainmentrecommendback.Client
	genRecommendationClient      recommendation_gen.IClient
	channelMusicClient           *channelmusic.Client
	channelOpenGameClient        *openGame.Client
	tmpChannelOpenGameClient     *tmpOpenGame.Client
	channelOpenGameRecordClient  *openGameRecord.Client
	friendshipClient             friendship.IClient
	channelLevelClient           channel_level.IClient
	fanoutFollowNotifyUtil       notify.NotifyUtil
	abtestClient                 *abtest.Client
	speedLimit                   *speedlimit.SpeedLimit
	realnameClient               *ttc_proxy.Client
	punishUserCli                punishuser.IClient
	channelKtvCli                *channel_ktv.Client
	gameCardClient               *gameCard.Client
	channelPlayTabClient         *channel_play_tab.Client
	safetyConfig                 SafetyConfig
	rcmdChannelLabelClient       *rcmdChannelLabel.Client

	musicTopicChannelClient music_topic_channel.MusicChannelClient

	recommendPage  uint32
	recommendIndex int

	serverId                           string
	acquireLoopDuration                uint64
	channelTimeoutDisappearDuration    uint64
	channelBackgroundDisappearDuration uint64
	loopTimeoutDisappearDuration       int64
	loopBackgroundDisappearDuration    int64
	//loopTimeoutReleaseDuration         int64

	roomHandler       *roomConfigHandler
	quickMatchHandler *quickMatchConfigHandler
	//topChannelConfig     *topRecommendChannelConfig
	v2Config             *v2Config
	thirdPartyGameConfig map[uint32]*thirdPartyGameConfig
	tabWhiteList         map[uint32]bool

	singleFilter *singleflight.Group
	sync.RWMutex
	miniGameConfig map[uint32]*topic_channel.MiniGameConfig

	recommendHourRoom *miniGameGen.Client

	//channelTeamClient *channel_team.Client
	serviceConfigT *conf.ServiceConfigT
	newAbtestCli   *newAbtest.ABTestClient

	feedbackMgr *feedback.FeedbackMgr
	tabListMgr  *tab_list.TabListMgr

	supervisor              *supervision.Supervisory
	DistributionChannelInst *channel_push.DistributionChannel
}

// GetDialogV2 deprecate
func (s *Server) GetDialogV2(ctx context.Context, in *topic_channel.GetDialogV2Req) (*topic_channel.GetDialogV2Resp, error) {
	out := &topic_channel.GetDialogV2Resp{}
	return out, nil
}

func (s *Server) ShutDown() {}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	cfg.genDefaultValue()
	log.InfoWithCtx(ctx, "server startup with cfg: %+v\n, cfg.v2Config:%v\n, cfg.thirdPartyConfig:%v\n",
		*cfg, cfg.V2Config, cfg.ThirdPartyGameConfig)
	return newServer(ctx, cfg)
}

func newServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	rand.Seed(time.Now().UnixNano())
	realnameClient, err := ttc_proxy.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "New realnameauth err")
		return nil, nil
	}

	userlineCommonApiClient, _ := userline_common_api.NewClient()

	channelCli := channel.NewClient()

	accountCli, err := account.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "failed to create account client, %v", err)
		return nil, err
	}
	accountgoCli, err := account_go.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "account_go.NewClient() failed to create account client, %v", err)
		return nil, err
	}
	channelmicCli := channelmic.NewClient()

	channelolCli := channelol.NewClient()
	channelOLStatClient, _ := channelol_stat_go.NewClient()

	channelMusicCli := channelmusic.NewClient()

	opts := []grpcOP.DialOption{grpcOP.WithBlock()}
	tcTabCli, err := tcTab.NewClient(opts...)
	if err != nil {
		log.ErrorWithCtx(ctx, "New tab Client err(%v)", err)
		return nil, err
	}

	tcChannelCli, err := tcChannel.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "New channel Client err(%v)", err)
		return nil, err
	}

	/*	tcRecommendCli, err := tcRecommend.NewClient()
		if err != nil {
			log.ErrorWithCtx(ctx,"New recommendation Client err(%v)", err)
			return nil, err
		}
	*/
	channelimCli := channelim.NewClient()

	entertainmentRecommendClient := entertainmentrecommendback.NewClient()

	genClient, err := recommendation_gen.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "New recommendation_gen Client err(%v)", err)
		//return nil, err
	}

	recommendHourRoomCli, err := miniGameGen.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "New recommendation_mini_game_gen Client err(%v)", err)
		//return nil, err
	}

	openGameCli, err := openGame.NewClient(opts...)
	if err != nil {
		log.ErrorWithCtx(ctx, "New channel-open-game Client err(%v)", err)
		//return nil, err
	}
	tmpOpenGameCli, err := tmpOpenGame.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "New tmp-channel-open-game Client err(%v)", err)
		//return nil, err
	}
	openGameRecordCli, err := openGameRecord.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "New channel-open-game-record Client err(%v)", err)
		//return nil, err
	}

	friendshipClient, err := friendship.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "New friendship Client err(%v)", err)
		return nil, err
	}

	abtestClient, err := abtest.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "abtest.NewClient %v", err)
		return nil, err
	}

	gameServerCli, err := gameServerV2Client.NewClient(opts...)
	if err != nil {
		log.ErrorWithCtx(ctx, "gameServerV2Client.NewConfClient() failed err:%v", err)
		return nil, err
	}

	channelKtvCli, _ := channel_ktv.NewClient()
	gameCardClient, _ := gameCard.NewClient()
	rcmdMusicChannelClient, _ := music_channel.NewClient()
	rcmdChannelLabelClient, _ := rcmdChannelLabel.NewClient(opts...)

	musicTopicChannelClient, err := music_topic_channel.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "music_topic_channel.NewClient() err:%v", err)
		return nil, err
	}

	safetyConf := NewSafetyConfig()
	roomHandler := newRoomConfigHandler()

	roomHandler.start()
	quickMatchHandler := &quickMatchConfigHandler{}
	quickMatchHandler.start()

	notifyUtil := notify.NewNotifyUtil()

	producerConf := sarama.NewConfig()
	producerConf.Producer.RequiredAcks = sarama.WaitForAll
	producerConf.Producer.Return.Successes = true
	producerConf.Producer.Return.Errors = true
	producerConf.ChannelBufferSize = 2048

	sf := new(singleflight.Group)

	channelLevelClient, _ := channel_level.NewClient()
	punishUserCli, _ := punishuser.NewClient()
	// 接入熔断
	err = breaker.Setup(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "Setup breaker err(%v)", err)
		return nil, err
	}

	sc := &conf.ServiceConfigT{}
	err = sc.Parse(ctx, cfg.Environment)
	if err != nil {
		return nil, err
	}

	// 百灵数据统计 初始化
	bylinkCollect, err := bylink.NewKfkCollector()
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to new kfk collector: %v", err)
		return nil, err
	}
	bylink.InitGlobalCollector(bylinkCollect)
	gangupChannelClient, err := gangup_channel.NewClient()
	if err != nil {
		log.Errorf("channel.NewClient() err:%v", err)
		return nil, err
	}
	abConf := sc.GetOption().QuickMatchABTestConf
	newAbtestCli := newAbtest.NewABTestClient(abConf.Url, abConf.AppId, abConf.LayerTag)
	channelPlayTabClient, _ := channel_play_tab.NewClient(opts...)
	ugcLiveTogetherClient, err := ugc_live_together.NewClient(opts...)
	if err != nil {
		log.ErrorWithCtx(ctx, "ugcLiveTogetherClient.NewClient() err: %v", err)
		return nil, err
	}
	expsvrClient := expsvr.NewClient()
	deviceInfoClient, _ := device_info_service.NewClientToDeviceService()
	channelGoClient, err := channel_go.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "channel_go.NewClient() err:%v", err)
		return nil, err
	}
	feedbackMgr := feedback.NewFeedbackMgr(sc, tcChannelCli, channelGoClient, gangupChannelClient, rcmdChannelLabelClient, tcTabCli, accountgoCli, deviceInfoClient)
	tabListMgr := tab_list.NewTabListMgr(sc, gameCardClient)

	censoringClient := censoring_proxy.NewClient(opts...)
	supervisorInst, err := supervision.NewSupervisory(accountgoCli, expsvrClient, realnameClient, censoringClient,
		accountCli, channelPlayTabClient, cfg.Environment)
	if err != nil {
		return nil, err
	}
	SingClient, _ := singaround.NewClient(opts...)
	UserMusicRankClient, _ := user_music_rank_cli.NewClient(opts...)
	ChannelListeningCli, _ := channelListening.NewClient(opts...)
	HobbyChannelClient, _ := hobby_channel.NewClient(opts...)
	configServerClient, err := configserver.NewClient(opts...)
	if err != nil {
		log.Errorf("configserver.NewClient() err:%v", err)
		return nil, err
	}

	DistributionChannelInst := &channel_push.DistributionChannel{
		FriendshipClient:        friendshipClient,
		GenRecommendationClient: genClient,
		GameCardClient:          gameCardClient,
		RcmdMusicChannelClient:  rcmdMusicChannelClient,
		PunishUserCli:           punishUserCli,
		ChannelmicClient:        channelmicCli,
		SingClient:              SingClient,
		KTVClient:               channelKtvCli,
		UserMusicRankClient:     UserMusicRankClient,
		ChannelListeningCli:     ChannelListeningCli,
		HobbyChannelClient:      HobbyChannelClient,
		ChannelOlStat:           channelOLStatClient,
		UserlineCommonApiClient: userlineCommonApiClient,
		AccountClient:           accountCli,
		TCTabClient:             tcTabCli,
		SupervisoryInst:         supervisorInst,
		TCChannelClient:         tcChannelCli,
		ChannelClient:           channelGoClient,
		ServiceConfigT:          sc,
		ConfigServerClient:      configServerClient,
	}
	var s = &Server{
		//cfg:                          cfg,
		ugcLiveTogetherClient: ugcLiveTogetherClient,
		channelClient:         channelCli,
		channelGoClient:       channelGoClient,
		accountClient:         accountCli,
		channelmicClient:      channelmicCli,
		channelolClient:       channelolCli,
		channelOLStatClient:   channelOLStatClient,
		tcTabClient:           tcTabCli,
		tcChannelClient:       tcChannelCli,
		//tcRecommendClient:            tcRecommendCli,
		channelimClient:              channelimCli,
		channelOpenGameClient:        openGameCli,
		tmpChannelOpenGameClient:     tmpOpenGameCli,
		channelOpenGameRecordClient:  openGameRecordCli,
		entertainmentRecommendClient: entertainmentRecommendClient,
		genRecommendationClient:      genClient,
		channelMusicClient:           channelMusicCli,
		friendshipClient:             friendshipClient,
		abtestClient:                 abtestClient,
		fanoutFollowNotifyUtil:       notifyUtil,
		channelLevelClient:           channelLevelClient,
		realnameClient:               realnameClient,
		punishUserCli:                punishUserCli,
		channelKtvCli:                channelKtvCli,
		gameCardClient:               gameCardClient,
		channelPlayTabClient:         channelPlayTabClient,
		rcmdChannelLabelClient:       rcmdChannelLabelClient,
		speedLimit:                   speedlimit.NewSpeedLimit(2, 40),

		musicTopicChannelClient: musicTopicChannelClient,
		//topicChannelProducer: topicChannelProducer,

		safetyConfig: safetyConf,

		recommendPage:  3,
		recommendIndex: 3,

		serverId:                           strconv.FormatInt(time.Now().UnixNano(), 10),
		acquireLoopDuration:                150,
		channelTimeoutDisappearDuration:    cfg.ChannelTimeoutDisappearDuration,
		channelBackgroundDisappearDuration: cfg.ChannelBackgroundDisappearDuration,
		loopTimeoutDisappearDuration:       cfg.LoopTimeoutDisappearDuration,
		loopBackgroundDisappearDuration:    30,

		roomHandler:       roomHandler,
		quickMatchHandler: quickMatchHandler,

		v2Config: cfg.V2Config,
		//topChannelConfig:  cfg.TopRecommendChannelConfig,
		tabWhiteList:      cfg.TabWhiteList,
		singleFilter:      sf,
		miniGameConfig:    make(map[uint32]*topic_channel.MiniGameConfig),
		recommendHourRoom: recommendHourRoomCli,
		serviceConfigT:    sc,
		newAbtestCli:      newAbtestCli,

		feedbackMgr:             feedbackMgr,
		tabListMgr:              tabListMgr,
		supervisor:              supervisorInst,
		DistributionChannelInst: DistributionChannelInst,
		accountgoClient:         accountgoCli,
	}
	log.InfoWithCtx(ctx, "minigameIds (%+v)", s.v2Config.MiniGameIds)

	// load third party game config
	//otherGameOrigConfig, err := cfg.DIY("third_party_game_config")
	//if err != nil {
	//	log.WarnWithCtx(ctx, "get third_party_game_config err(%v)", err)
	//}
	//s.thirdPartyGameConfig = loadThirdPartyGameConfig(otherGameOrigConfig)
	//if s.thirdPartyGameConfig != nil {
	//	log.InfoWithCtx(ctx, "loadThirdPartyGameConfig thirdPartyGameConfig (%+v)", s.thirdPartyGameConfig)
	//}
	s.thirdPartyGameConfig = cfg.ThirdPartyGameConfig

	// 需要考虑执行效率，避免定时执行造成请求积压，时间间隔大于context的时间即可
	s.getMiniGameConfig(ctx)

	// 缓存小游戏配置
	go func() {
		ticker := time.NewTicker(time.Second * 10)
		for range ticker.C {
			s.getMiniGameConfig(ctx)
		}
	}()
	err = client.Setup(configServerClient, channelPlayTabClient, tcTabCli, gameServerCli, openGameCli)
	if err != nil {
		log.ErrorWithCtx(ctx, "client init fail err %v", err)
		return s, err
	}
	err = tab_cache.NewCache(ctx, sc)
	if err != nil {
		log.ErrorWithCtx(ctx, "tab_cache.NewCache err(%v)", err)
		return nil, err
	}
	err = fall_back_cache.NewSwitchCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "fall_back_cache.NewSwitchCache err(%v)", err)
		return nil, err
	}
	return s, nil
}

func (s *Server) GetTopicChannelInfo(ctx context.Context,
	in *topic_channel.GetTopicChannelInfoReq) (out *topic_channel.GetTopicChannelInfoResp, err error) {
	out = &topic_channel.GetTopicChannelInfoResp{}
	out.ChannelId = in.ChannelId
	out.IsInGround = false
	out.ShiftRoomDuration = s.serviceConfigT.GetOption().ShiftRoomDuration
	//out.FreezeDuration = s.serviceConfigT.GetOption().FreezeDuration
	var TabInfo *tabPB.FiniteTabsResp
	var prepareChannelResp *entertainmentRecommendBack.GetChannelTagResp
	var topicResp *channelPB.GetChannelByIdsResp

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	userId := serviceInfo.UserID
	log.InfoWithCtx(ctx, "GetTopicChannelInfo serviceinfo(%s) in(%s)", serviceInfo.String(), in.String())

	//非主题房，返回推荐房tabname,新版本
	if in.ChannelType == gaChannelPB.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE {
		prepareChannelResp, err = s.entertainmentRecommendClient.GetChannelTag(ctx, userId, &entertainmentRecommendBack.GetChannelTagReq{ChannelId: &in.ChannelId})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetTopicChannelInfo GetChannelTag channelId(%v) userId(%v) err(%v)", in.ChannelId, userId, err)
			return
		}
		if prepareChannelResp.TagInfo != nil {
			out.TabName = prepareChannelResp.TagInfo.GetName()
			return
		}
	}

	// 开黑房和老版本，老版本字段ChannelType为默认值0
	if in.ChannelType == gaChannelPB.ChannelType_USER_CHANNEL_TYPE || in.ChannelType == gaChannelPB.ChannelType_INVALID_CHANNEL_TYPE ||
		in.ChannelType == gaChannelPB.ChannelType_TEMP_KH_CHANNEL_TYPE {
		topicResp, err = s.tcChannelClient.GetChannelByIds(ctx, &channelPB.GetChannelByIdsReq{Ids: []uint32{in.ChannelId}, ReturnAll: true})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetTopicChannelInfo GetChannelByIds uid(%v) channelId(%v) err(%v)", userId, in.ChannelId, err)
			return
		}

		if int32(in.ChannelInfoType) == int32(topic_channel.GetTopicChannelInfoReq_PLAY_TYPE) {
			// 新版本看老版本
			if len(topicResp.Info) == 0 {
				//pc端有可能未选玩法创房
				log.InfoWithCtx(ctx, "GetTopicChannelInfo GetChannelByIds uid(%v), channelId(%v), serviceInfo(%v)"+
					" get no channelInfo", userId, in.GetChannelId(), serviceInfo.String())
				var roomMod *channelmicPB.GetChannelMicModeResp
				switchPlayInfo := &topic_channel.SwitchPlayInfo{}
				roomMod, err = s.channelmicClient.GetChannelMicMode(ctx, userId, &channelmicPB.GetChannelMicModeReq{ChannelId: in.GetChannelId()})
				if err != nil {
					log.ErrorWithCtx(ctx, "GetTopicChannelInfo GetChannelMicMode uid(%v) channelId(%v) err(%v)", userId, in.ChannelId, err)
					return
				}
				log.DebugWithCtx(ctx, "GetTopicChannelInfo GetChannelMicMode uid(%v) mic model (%v)", userId, roomMod.MicMode)

				if roomMod.MicMode == uint32(gaChannelPB.EChannelMicMode_HQ_KH_MIC_SPACE_MODE) {
					TabInfo, err = s.tcTabClient.FiniteTabs(ctx, &tabPB.FiniteTabsReq{Tabs: []*tabPB.Tab{{Id: s.v2Config.OtherGameTabId}}})
					if err != nil {
						log.ErrorWithCtx(ctx, "GetTopicChannelInfo FiniteTabs uid(%v) channelId(%v) tabId(%v) err(%v)", userId, in.ChannelId, s.v2Config.OtherGameTabId, err)
						return
					}
				} else if roomMod.MicMode == uint32(gaChannelPB.EChannelMicMode_FUN_MIC_SPACE_MODE) {
					TabInfo, err = s.tcTabClient.FiniteTabs(ctx, &tabPB.FiniteTabsReq{Tabs: []*tabPB.Tab{{Id: s.v2Config.ChatTabId}}})
					if err != nil {
						log.ErrorWithCtx(ctx, "GetTopicChannelInfo FiniteTabs uid(%v) channelId(%v) tabId(%v) err(%v)", userId, in.ChannelId, s.v2Config.ChatTabId, err)
						return
					}
				}

				if TabInfo != nil && len(TabInfo.GetTabs()) > 0 {
					//是否展示发布按钮,首页配置未未分类，也不展示发布按钮
					if TabInfo.GetTabs()[0].GetHomePageType() == tabPB.HomePageType_HomePageTypeNone {
						out.ShowPublishButton = false
					} else {
						out.ShowPublishButton = TabInfo.GetTabs()[0].GetShowPublishButton()
					}
					out.CategoryType = topic_channel.CategoryType(TabInfo.Tabs[0].GetCategoryMapping()) //分类标识
					out.TabName = TabInfo.GetTabs()[0].GetName()
					out.TabId = TabInfo.GetTabs()[0].GetId()
					out.TagId = TabInfo.GetTabs()[0].GetTagId()
					switchPlayInfo.RoomModel = gaChannelPB.EChannelMicMode(TabInfo.GetTabs()[0].GetMicMod())
					out.TabType = topic_channel.GetTopicChannelInfoResp_NORMAL // 普通分类
					welcomeText := GetWelcomeText(TabInfo.GetTabs()[0].GetNewcomerWelcome())

					out.WelcomeText = welcomeText

					// 如果是小游戏，需要带上指定游戏可跳转第三方游戏的配置信息(下载游戏功能，菲菲v-5.2.1)
					if otherGameConf, ok := s.thirdPartyGameConfig[out.TabId]; ok {
						out.ThirdPartyGame = convertOtherGameConf(otherGameConf)
						log.DebugWithCtx(ctx, "GetTopicChannelInfo uid(%v) ThirdPartyGame(%v)", userId, out.ThirdPartyGame)
					}

				} else {
					out.TabId = s.serviceConfigT.GetPublicSwitchConfig().GetDefaultTab()
					out.TabName = "未选择"
				}
				out.SwitchPlayInfo = switchPlayInfo
				log.DebugWithCtx(ctx, "GetTopicChannelInfo uid(%v) topic channel info resp (%+v)", userId, out)
				return
			}
		}

		log.DebugWithCtx(ctx, "GetTopicChannelInfo uid(%v) topicResp(%v)", userId, topicResp.GetInfo())
		if len(topicResp.Info) > 0 {
			//主题房

			out.TabId = topicResp.Info[0].TabId
			/*			var tabResp *tabPB.FiniteTabsResp
						tabResp, err = s.tcTabClient.FiniteTabs(ctx, &tabPB.FiniteTabsReq{Tabs: []*tabPB.Tab{{Id: topicResp.Info[0].TabId}}})
						if err != nil {
							log.ErrorWithCtx(ctx, "GetTopicChannelInfo FiniteTabs uid(%v) channelId(%v) tabId(%v) err(%v)", userId, in.ChannelId, out.TabId, err)
							return
						}*/
			tabInfo := tab_cache.GetTabIdMap()[topicResp.Info[0].TabId]
			log.DebugWithCtx(ctx, "GetTopicChannelInfo uid(%v) FiniteTabs tabResp(%v)", userId, tabInfo)
			if tabInfo != nil {
				out.CategoryType = topic_channel.CategoryType(tabInfo.GetCategoryMapping()) // 分类标识
				out.TabName = tabInfo.Name
				out.TagId = tabInfo.TagId
				out.TabId = topicResp.Info[0].TabId
				//是否展示发布按钮，首页配置未分类，不展示发布按钮
				if tabInfo.GetHomePageType() == tabPB.HomePageType_HomePageTypeNone {
					out.ShowPublishButton = false
				} else {
					out.ShowPublishButton = tabInfo.GetShowPublishButton()
				}
			} else {
				out.TabId = s.serviceConfigT.GetPublicSwitchConfig().GetDefaultTab()
				out.TabName = "未选择"
			}
			for _, d := range topicResp.Info[0].DisplayType {
				switch d {
				case channelPB.ChannelDisplayType_DISPLAY_AT_MAIN_PAGE:
					if !topicResp.Info[0].IsPrivate {
						out.IsInGround = true
					}
					out.PlayingOption = append(out.PlayingOption, topic_channel.PlayingOption_AT_MAIN_PAGE)
				case channelPB.ChannelDisplayType_DISPLAY_AT_FIND_FRIEND:
					out.PlayingOption = append(out.PlayingOption, topic_channel.PlayingOption_FRIEND)
				}
			}

			out.IsPrivate = topicResp.Info[0].IsPrivate
			if len(topicResp.Info[0].DisplayType) == 0 {
				out.IsInGround = !topicResp.Info[0].IsPrivate
			}
			blockResp, err := s.tcTabClient.Blocks(ctx, &tabPB.BlocksReq{TabId: topicResp.GetInfo()[0].GetTabId()})
			if err != nil {
				log.ErrorWithCtx(ctx, "GetTopicChannelInfo Blocks userId(%v) tabId(%v) err(%v)", userId, topicResp.GetInfo()[0].GetTabId(), err)
				return out, protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError)
			}
			log.DebugWithCtx(ctx, "GetTopicChannelInfo uid(%v) Blocks blockResp(%v)", userId, blockResp)
			if out.IsInGround {
				out.HeadDesc, _ = getChannelDesc(topicResp.GetInfo()[0].GetBlockOptions(), blockResp.GetBlocks())
			}

			//统一风格
			out.TeamDesc = s.getPublishInfoDesc(ctx, topicResp.GetInfo()[0].GetTabId(), topicResp.GetInfo()[0].GetBlockOptions())
			//out.TeamDesc = getChannelDescV3(topicResp.GetInfo()[0].GetBlockOptions(), blockResp.GetBlocks())

			if (int32(in.ChannelInfoType) == int32(topic_channel.GetTopicChannelInfoReq_PLAY_TYPE)) && tabInfo != nil {
				switchPlayInfo := &topic_channel.SwitchPlayInfo{}

				switchPlayInfo.RoomModel = gaChannelPB.EChannelMicMode(tabInfo.GetMicMod())

				out.SwitchPlayInfo = switchPlayInfo
			}
			if tabInfo == nil {
				return out, nil
			}
			welcomeText := GetWelcomeText(tabInfo.GetNewcomerWelcome())
			out.ShowTeamDesc = true

			out.WelcomeText = welcomeText
			switch tabInfo.TabType {
			case tabPB.Tab_NORMAL:
				out.TabType = topic_channel.GetTopicChannelInfoResp_NORMAL
			case tabPB.Tab_GAME:
				out.TabType = topic_channel.GetTopicChannelInfoResp_GAME
			case tabPB.Tab_MINIGAME:
				//小游戏进房上报给元挺做召回
				out.TabType = topic_channel.GetTopicChannelInfoResp_MINI_GAME
			default:
				log.WarnWithCtx(ctx, "GetTopicChannelInfo invalid tab type %d", tabInfo.GetTabType())
			}

			// 如果是小游戏，需要带上指定游戏可跳转第三方游戏的配置信息(下载游戏功能，菲菲v-5.2.1)
			if otherGameConf, ok := s.thirdPartyGameConfig[out.TabId]; ok {
				out.ThirdPartyGame = convertOtherGameConf(otherGameConf)
				log.DebugWithCtx(ctx, "GetTopicChannelInfo uid(%v) other ThirdPartyGame (%v)", userId, out.ThirdPartyGame)
			}
		} else {
			prepareChannelResp, err = s.entertainmentRecommendClient.GetChannelTag(ctx, userId, &entertainmentRecommendBack.GetChannelTagReq{ChannelId: &in.ChannelId})
			if err != nil {
				log.ErrorWithCtx(ctx, "GetTopicChannelInfo GetChannelTag channelId(%v) userId(%v) err(%v)", in.ChannelId, userId, err)
				return
			}
			if prepareChannelResp.TagInfo != nil {
				out.TabName = prepareChannelResp.TagInfo.GetName()
				return
			} else {
				log.ErrorWithCtx(ctx, "GetTopicChannelInfo GetChannelByIds uid(%v), channelId(%v), serviceInfo(%v)"+
					" get no channelInfo", userId, in.GetChannelId(), serviceInfo.String())
			}
		}
		log.DebugWithCtx(ctx, "GetTopicChannelInfo uid(%v) topic channel info resp (%+v)", userId, out)
	}
	// 除了娱乐房和开黑房，其它房间没有tabName
	return
}

// GetTopicChannelRoomName 获取指定约玩主题房标签对应的roomName
func (s *Server) GetTopicChannelRoomName(ctx context.Context,
	in *topic_channel.GetTopicChannelRoomNameReq) (*topic_channel.GetTopicChannelRoomNameResp,
	error) {

	out := &topic_channel.GetTopicChannelRoomNameResp{
		Info: make([]*topic_channel.ChannelNameInfo, 0, 100),
	}

	tabs := make([]*tabPB.Tab, 0, len(in.GetTabId()))
	for _, id := range in.GetTabId() {
		elem := &tabPB.Tab{
			Id: id,
		}
		tabs = append(tabs, elem)
	}

	resp, err := s.tcTabClient.FiniteTabs(ctx, &tabPB.FiniteTabsReq{
		Tabs: tabs,
	})
	if err != nil {
		log.Errorln(err)
		return nil, err
	}
	roomNameMap := tab_cache.GetRoomNameConfigMap()
	for _, tab := range resp.GetTabs() {
		info := &topic_channel.ChannelNameInfo{}

		info.TabId = tab.GetId()
		info.Version = tab.GetVersion()

		if nameConfigs, ok := roomNameMap[tab.GetId()]; ok {
			for _, nameConfig := range nameConfigs {
				info.RoomName = append(info.GetRoomName(), nameConfig.GetName())
			}
		} else {
			//log.ErrorWithCtx(ctx, "GetTopicChannelRoomName tabId(%d) tabName(%s) has no roomNameConfigInfo", tab.GetId(), tab.GetName())
			info.RoomName = append(info.GetRoomName(), DefaultRoomName)
		}

		out.Info = append(out.Info, info)
	}
	log.InfoWithCtx(ctx, "in(%s) out(%s)", in.String(), out.String())
	return out, nil
}

func (s *Server) GetBannerList(ctx context.Context, in *gaChannelPB.GetChannelAdvReq) (out *gaChannelPB.GetChannelAdvResp, err error) {
	out = new(gaChannelPB.GetChannelAdvResp)
	var resp *tabPB.GetSimpleBannerResp
	resp, err = s.tcTabClient.GetSimpleBanner(ctx, &tabPB.GetSimpleBannerReq{NeedCache: true})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetBannerList err(%v)", err)
		return
	}
	for _, item := range resp.GetBannerList() {
		out.AdvList = append(out.AdvList, &gaChannelPB.ChannelAdv{PicUrl: item.ImageUri, AdvUrl: item.JumpUri})
	}

	return
}

func (s *Server) sendDismissMsg(ctx context.Context, userId uint32, channelId uint32, name, nickName, content string, msgType gaChannelPB.ChannelMsgType) error {
	optMsg := &gaChannelPB.HideTopicChannelMsg{
		ContentMsg: content,
	}
	optMsgBin, err := optMsg.Marshal()
	if err != nil {
		log.ErrorWithCtx(ctx, "sendDismissMsg marshal err: %v,", err)
		return err
	}

	_, _, err = s.channelimClient.SendCommonMessage(ctx, userId, channelId, &channelimPB.ChannelCommonMsg{
		FromUid:      userId,
		FromAccount:  name,
		FromNick:     nickName,
		Time:         uint64(time.Now().Unix()),
		ToChannelId:  channelId,
		Type:         uint32(msgType),
		Origin:       0,
		Content:      "",
		PbOptContent: optMsgBin,
	}, true, uint32(msgType))
	if err != nil {
		log.ErrorWithCtx(ctx, "sendDismissMsg SendCommonMessage err: %v", err)
		return err
	}

	return nil
}

// v5.5.0版本后只有发布会触发此推送
/*func (s *Server) sendCreateMsg(ctx context.Context, userId uint32, channelId uint32, name string, nickName string, tabId uint32, tabName string) error {
	msg := &gaChannelPB.CreateTopicChannelMsg{
		ChannelId: channelId,
		AdminMsg:  "房间已在首页展示，队友正陆续赶来，要做个nice的人哟，踢人等不友善行为会降低房间排序",
		PlayerMsg: "向首页发布了“" + tabName + "”房",
		UserId:    userId,
		TabId:     tabId,
		TabName:   tabName,
	}
	msgBin, err := msg.Marshal()
	if err != nil {
		log.ErrorWithCtx(ctx, "sendCreateMsg marshal err: %v,", err)
		return err
	}

	_, _, err = s.channelimClient.SendCommonMessage(ctx, userId, channelId, &channelimPB.ChannelCommonMsg{
		FromUid:      userId,
		FromAccount:  name,
		FromNick:     nickName,
		Time:         uint64(time.Now().Unix()),
		ToChannelId:  channelId,
		Type:         uint32(gaChannelPB.ChannelMsgType_CHANNEL_CREATE_TOPIC_CHANNEL),
		Origin:       0,
		Content:      "",
		PbOptContent: msgBin,
	}, true, uint32(gaChannelPB.ChannelMsgType_CHANNEL_CREATE_TOPIC_CHANNEL))
	if err != nil {
		log.ErrorWithCtx(ctx, "sendCreateMsg SendCommonMessage err: %v", err)
		return err
	}

	return nil
}*/

func (s *Server) sendChangeNameMsg(ctx context.Context, userId uint32, channelName string, channelId uint32, name string, nickName string) error {

	modifyName := map[string]interface{}{
		"sub_type":     uint32(gaChannelPB.ChannelMsgSubType_CHANNEL_MSG_SUB_CHANGE_NAME),
		"channel_name": channelName,
	}
	modifyNameBin, err := json.Marshal(modifyName)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendChangeTabMsg marshal err: %v,", err)
		return err
	}

	_, _, err = s.channelimClient.SendCommonMessage(ctx, userId, channelId, &channelimPB.ChannelCommonMsg{
		FromUid:     userId,
		FromAccount: name,
		FromNick:    nickName,
		Time:        uint64(time.Now().Unix()),
		ToChannelId: channelId,
		Type:        uint32(gaChannelPB.ChannelMsgType_CHANNEL_CONFIG_MODIFY_MSG),
		Origin:      0,
		Content:     string(modifyNameBin),
	}, true, uint32(gaChannelPB.ChannelMsgSubType_CHANNEL_MSG_SUB_CHANGE_NAME))
	if err != nil {
		log.ErrorWithCtx(ctx, "sendChangeNameMsg SendCommonMessage err: %v", err)
		return err
	}

	return nil
}

func (s *Server) sendChangeTabMsg(ctx context.Context, userId uint32, channelId uint32,
	tabId uint32, name string, nickName string, tabName string, isPrivate, isInGround bool,
	headDesc string, micMod uint32, tabType uint32, welcomeText string, playingOption []topic_channel.PlayingOption, thirdPartyGame *topic_channel.ThirdPartyGame, tagId uint32, teamDesc string) error {

	modifyTab := map[string]interface{}{
		"sub_type":         uint32(gaChannelPB.ChannelMsgSubType_CHANNEL_MSG_SUB_CHANGE_TOPIC_CHANNEL_TAB_ID),
		"tab_id":           tabId,
		"tab_name":         tabName,
		"is_in_ground":     isInGround,
		"is_private":       isPrivate,
		"head_desc":        headDesc,
		"mic_mod":          micMod,
		"tab_type":         tabType,
		"welcome_text":     welcomeText,
		"playing_option":   playingOption,
		"third_party_game": thirdPartyGame,
		"tag_id":           tagId,
		"show_team_desc":   true,
		"team_desc":        teamDesc,
	}

	log.DebugWithCtx(ctx, "sendChangeTabMsg modifyTab (%v)\n", utils.ToJson(modifyTab))

	modifyTabBin, err := json.Marshal(modifyTab)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendChangeTabMsg marshal err: %v,", err)
		return err
	}

	_, _, err = s.channelimClient.SendCommonMessage(ctx, userId, channelId, &channelimPB.ChannelCommonMsg{
		FromUid:     userId,
		FromAccount: name,
		FromNick:    nickName,
		Time:        uint64(time.Now().Unix()),
		ToChannelId: channelId,
		Type:        uint32(gaChannelPB.ChannelMsgType_CHANNEL_CONFIG_MODIFY_MSG),
		Origin:      0,
		Content:     string(modifyTabBin),
	}, true, uint32(gaChannelPB.ChannelMsgSubType_CHANNEL_MSG_SUB_CHANGE_TOPIC_CHANNEL_TAB_ID))
	if err != nil {
		log.ErrorWithCtx(ctx, "sendChangeTabMsg SendCommonMessage err: %v", err)
		return err
	}

	return nil
}

func (s *Server) checkRealName(ctx context.Context, uid uint32) (realAdult bool, err error) {
	resp, err := s.realnameClient.GetUserIdentityInfo(ctx, uint64(uid))
	if err != nil {
		log.ErrorWithCtx(ctx, "checkRealName GetUserIdentityInfo error :%v", err)
		return false, err
	}

	if s.serviceConfigT.GetPublicSwitchConfig().IsRealName(resp.Status) && resp.IsAdult {
		realAdult = true
	}
	return
}

func notMatchTab(reqTabId, channelTabId uint32, minorityGame map[uint32]*tabPB.Tab) bool {
	if reqTabId > 0 && reqTabId < InfElemID {
		// 很遗憾，tab id不一样，会被过滤
		if channelTabId != reqTabId {
			_, isMinorityGame := minorityGame[channelTabId]
			if reqTabId == tab_cache.MinorityGameParentId && isMinorityGame {
				// 请求的是其他游戏且房间是小众游戏房间要例外
				return false
			}
			return true
		}
	}
	return false
}

func getTraceInfoRecallFlag(genResp *genPB.GetRecommendationListResp, channelId uint32) uint32 {
	if genResp != nil && genResp.GetChannelInfoMap() != nil {
		if info := genResp.GetChannelInfoMap()[channelId]; info != nil {
			return info.GetRecallFlag()
		}
	}
	return 0
}

func (s *Server) Close() {
	//s.isServerEnd = true
	//for i := 0; i < s.loopCount; i++ {
	//	<-s.closeCh
	//}
}
