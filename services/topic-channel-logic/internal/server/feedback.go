package server

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/protocol/common/status"
)

func (s *Server) GetNegativeFeedBackOption(ctx context.Context, in *pb.GetNegativeFeedBackOptionReq) (out *pb.GetNegativeFeedBackOptionResp, err error) {
	out = &pb.GetNegativeFeedBackOptionResp{}

	svInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}

	out.FeedbackOptions, err = s.feedbackMgr.GetNegativeFeedBackOption(ctx, svInfo.UserID, uint32(svInfo.ClientType), in.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNegativeFeedBackOption GetNegativeFeedBackOption channelId %d error:%v", in.GetChannelId(), err)
		return out, err
	}

	log.InfoWithCtx(ctx, "GetNegativeFeedBackOption in:%s out: %s", in.String(), out.String())
	return
}

func (s *Server) NegativeFeedBack(ctx context.Context, in *pb.NegativeFeedBackReq) (out *pb.NegativeFeedBackResp, err error) {
	out = &pb.NegativeFeedBackResp{}
	svInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}

	out.MatchedKeywords, err = s.feedbackMgr.NegativeFeedBack(ctx, in, svInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "NegativeFeedBack NegativeFeedBack in:%s error:%v", in.String(), err)
		return out, err
	}
	log.InfoWithCtx(ctx, "NegativeFeedBack in:%s	out :%s", in.String(), out.String())

	return out, err
}
