package server

import (
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/topic-channel-logic/internal/tab_cache"
	"sort"
)

type GameInfo struct {
	GameId         uint32 `db:"game_id" json:"game_id"`
	Icon           string `db:"icon" json:"icon"`
	Score          uint32 `db:"score" json:"score,omitempty"`
	GameName       string `db:"game_name" json:"game_name,omitempty"`
	TabId          uint32 `db:"tab_id" json:"tab_id"`
	IsMinorityGame bool   `json:"is_minority_game"`
}

func sortTabsByPlatform(tabs []*tabPB.Tab, platform protocol.Platform) []*tabPB.Tab {
	if platform == protocol.PC {
		sort.Slice(tabs, func(i, j int) bool {
			return tabs[i].PcSort < tabs[j].PcSort
		})
	} else {
		sort.Slice(tabs, func(i, j int) bool {
			return tabs[i].Sort < tabs[j].Sort
		})
	}
	return tabs
}

func platformTypeNotMatch(platformType tabPB.PlatformType, userPlatform protocol.Platform) bool {
	/*
		if !(platformType == tabPB.PlatformType_ALL ||
			(platformType == tabPB.PlatformType_ANDROID_IOS && userPlatform == protocol.MOBILE) ||
			(platformType == tabPB.PlatformType_PC && userPlatform == protocol.PC)) {
			return true
		}
		return false
	*/
	return !(platformType == tabPB.PlatformType_ALL ||
		(platformType == tabPB.PlatformType_ANDROID_IOS && userPlatform == protocol.MOBILE) ||
		(platformType == tabPB.PlatformType_PC && userPlatform == protocol.PC))
}

// 获取需要外显的小众游戏
func (s *Server) getDisplayMinorityGame(selfGameList []uint32) []*tab_cache.MinorityGameInfo {
	selfInfoList := s.getSelfMinorityGame(selfGameList)
	if len(selfInfoList) > DisplayMinorityLength {
		selfInfoList = selfInfoList[:DisplayMinorityLength]
	}

	return selfInfoList
}

// 获取手机已有的小众游戏
func (s *Server) getSelfMinorityGame(selfGameList []uint32) []*tab_cache.MinorityGameInfo {
	minorityGameMap := tab_cache.GetMinorityGame()
	selfInfoList := make([]*tab_cache.MinorityGameInfo, 0, len(selfGameList))
	for _, v := range selfGameList {
		val, ok := minorityGameMap[v]
		if ok {
			selfInfoList = append(selfInfoList, val)
		}
	}

	sort.SliceStable(selfInfoList, func(i, j int) bool {
		return selfInfoList[i].GameScore > selfInfoList[j].GameScore
	})
	return selfInfoList
}

// 传入的selfInfoList默认是已根据gameScore拍寻的
func (s *Server) getHiddenMinorityGameTab(serviceInfo *grpc.ServiceInfo) []*tab_cache.MinorityGameInfo {
	minorityGameMap := tab_cache.GetMinorityGame()
	tabs := make([]*tabPB.Tab, 0, len(minorityGameMap))
	for _, v := range minorityGameMap {
		tabs = append(tabs, v.TabInfo)
	}
	platform := protocol.NewTerminalType(serviceInfo.TerminalType).Platform()
	tabs = sortTabsByPlatform(tabs, platform)

	defaultGameInfoList := make([]*tab_cache.MinorityGameInfo, 0, len(minorityGameMap))
	for _, tabInfo := range tabs {
		if s.v2Config.ServerEnv != "staging" {
			// 生产环境的时候，需要根据策略过滤
			if isFilter := s.supervisor.MiniGameStageStrategy(tabInfo, serviceInfo, tab_cache.GetWhiteList()); isFilter {
				continue
			}
		}
		defaultGameInfoList = append(defaultGameInfoList, &tab_cache.MinorityGameInfo{
			TabInfo: tabInfo,
		})
	}

	return defaultGameInfoList
}

func (s *Server) sortMinorityGameTab(selfGameList []uint32) []*tab_cache.MinorityGameInfo {
	minorityGameMap := tab_cache.GetMinorityGame()
	existMap := make(map[uint32]bool)
	selfInfoList := make([]*tab_cache.MinorityGameInfo, 0, len(selfGameList))
	for _, v := range selfGameList {
		val, ok := minorityGameMap[v]
		if ok {
			selfInfoList = append(selfInfoList, val)
		}
		existMap[v] = true
	}

	sort.SliceStable(selfInfoList, func(i, j int) bool {
		return selfInfoList[i].GameScore > selfInfoList[j].GameScore
	})

	defaultGameInfoList := make([]*tab_cache.MinorityGameInfo, 0, len(minorityGameMap))
	for k, v := range minorityGameMap {
		if val, ok := existMap[k]; ok && val {
			continue
		}
		defaultGameInfoList = append(defaultGameInfoList, v)
	}

	sort.SliceStable(defaultGameInfoList, func(i, j int) bool {
		return defaultGameInfoList[i].GameScore > defaultGameInfoList[j].GameScore
	})

	res := append(selfInfoList, defaultGameInfoList...)
	return res
}
