package server

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/protocol/common/status"
)

func (s *Server) DistributionTopicChannel(ctx context.Context,
	in *topic_channel.DistributionTopicChannelReq) (out *topic_channel.DistributionTopicChannelResp, err error) {
	out = new(topic_channel.DistributionTopicChannelResp)
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
		return
	}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "DistributionTopicChannel in:%+v out:%+v serviceInfo: %+v err:%+v", in, out, serviceInfo, err)
		} else {
			log.DebugWithCtx(ctx, "DistributionTopicChannel in:%+v out:%+v serviceInfo: %+v", in, out, serviceInfo)
		}

	}()

	// 判断邀请进房开关状态
	if !s.DistributionChannelInst.CanDistributionChannel(ctx, serviceInfo) {
		log.InfoWithCtx(ctx, "DistributionTopicChannel CanDistributionChannel false, in:%s svcInfo:%s", in.String(), serviceInfo.String())
		return out, nil
	}

	friendUid, relationUid, channelId, metaId, err := s.DistributionChannelInst.DistributionTopicChannel(ctx, serviceInfo, in)
	if err != nil {
		log.ErrorWithCtx(ctx, "DistributionTopicChannel err: %v", err)
		return
	}
	out.Footprint = metaId

	if channelId == 0 {
		return
	}

	item, err := s.DistributionChannelInst.GetChannelPopUpInfo(ctx, serviceInfo, channelId, friendUid, relationUid, in.GetBaseReq().GetMarketId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelPopUpInfo err: %v", err)
		return
	}

	if item != nil {
		out.Items = []*topic_channel.TopicChannelItemV3{item}
	}
	return
}
