package server

import (
	"context"
	"fmt"
	"golang.52tt.com/kaihei-pkg/supervision"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol/grpc"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	channelPB "golang.52tt.com/protocol/services/topic_channel/channel"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/topic-channel-logic/internal/tab_cache"
	"golang.52tt.com/services/topic-channel-logic/internal/utils"
	"math/rand"
)

func (s *Server) GetQuickMatchTabList(ctx context.Context, in *topic_channel.GetQuickMatchTabListReq) (out *topic_channel.GetQuickMatchTabListResp, err error) {
	out = &topic_channel.GetQuickMatchTabListResp{}

	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetQuickMatchTabList get serviceInfo err")
		return
	}

	log.DebugWithCtx(ctx, "GetQuickMatchTabList in: uid:%d, %v", serviceInfo.UserID, in)
	quickMatchABTestConf := s.serviceConfigT.GetOption().QuickMatchABTestConf
	targetVersion := quickMatchABTestConf.StartVersion
	isAbTest := utils.IsHigherVersion(serviceInfo.ClientVersion, targetVersion) && s.isInAbtest(ctx, serviceInfo.UserID)
	out.IsAbTest = isAbTest

	//获取各个来源配置的tab数据
	tabListRes, err := s.tabListMgr.GetQuickMatchTabList(ctx, serviceInfo, in, s.supervisor)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetQuickMatchTabList in:%s, error: %v", in.String(), err)
		return out, err
	}
	var finalTabs []*tabPB.Tab
	//各来源数据处理、合并去重(过滤,数量限制)
	supConfInst := &supervision.SupervisoryConf{
		RegisterLimitTime:      s.serviceConfigT.GetPublicSwitchConfig().GetRegisterLimitTime(),
		UserLevelLimit:         s.serviceConfigT.GetPublicSwitchConfig().GetUserLevelLimit(),
		RealNameStandardStatus: s.serviceConfigT.GetPublicSwitchConfig().GetRealNameStandardStatus(),
	}
	tabFilter, categoryFilter := s.supervisor.GetFilterMap(ctx, serviceInfo, in.GetChannelPkg(), supConfInst)
	finalTabs = tabListRes.GenFinalTabs(ctx, serviceInfo, tabFilter, categoryFilter)

	//业务需求处理，限制返回的小游戏玩法数量，根据版本以及ab实验整理返回的tab数据
	finalTabs = s.dataProcess(isAbTest, in.GetEntryType(), serviceInfo, finalTabs, tabListRes.ForceInsertTab)
	//数据结构转换
	miniGameConfMap := tab_cache.GetMiniGameConfMap()
	tabItem := convertToQuickMatchItems(finalTabs, miniGameConfMap)
	//补充数据
	if in.EntryType == topic_channel.GetQuickMatchTabListReq_HOME_PAGE {
		tabItem, err = s.getQuickMatchItemSecondaryText(ctx, tabItem)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetQuickMatchTabList GetQuickMatchItemSecondaryText error: %v", err)
			return out, err
		}

	}

	if in.EntryType == topic_channel.GetQuickMatchTabListReq_HOME_ENTRY {
		if len(tabItem) < 1 {
			return out, nil
		}
		out.SecondaryText, out.BigImage = s.getEntryTextAndImage(tabItem[0].ItemText)
	}
	out.TabItems = tabItem

	log.DebugWithCtx(ctx, "GetQuickMatchTabList out: %v", out)
	return out, nil
}

const (
	aGroupTabListLength      = 6 // 首页客户端实验A，B
	bGroupTabListLength      = 9 // B侧长度
	ABTestGroupTabListLength = 8 // 快速匹配入口新实验组侧长度

)

// AB测数据长度不同
func getTargetLength(entryType topic_channel.GetQuickMatchTabListReq_EntryType, isAbTest bool) int {
	if entryType == topic_channel.GetQuickMatchTabListReq_HOME_PAGE {
		return aGroupTabListLength
	}
	if entryType == topic_channel.GetQuickMatchTabListReq_HOME_ENTRY {
		if isAbTest {
			return ABTestGroupTabListLength
		}
		return bGroupTabListLength
	}
	return 0
}

// 数据整理
func (s *Server) dataProcess(isAbTest bool, entryType topic_channel.GetQuickMatchTabListReq_EntryType,
	serviceInfo *grpc.ServiceInfo, tabInfos []*tabPB.Tab, homePageFilterTab *tabPB.Tab) []*tabPB.Tab {
	if len(tabInfos) == 0 {
		return nil
	}
	count := 0
	gameCount := 0
	targetLength := getTargetLength(entryType, isAbTest)
	targetVersion := s.serviceConfigT.GetOption().QuickMatchABTestConf.StartVersion
	isNewVersion := utils.IsHigherVersion(serviceInfo.ClientVersion, targetVersion)
	repeatedMap := make(map[uint32]bool)
	if isAbTest {
		// 实验组的数据排序
		tabInfos = moveMiniGameTab(tabInfos)
	}

	var res []*tabPB.Tab
	for _, tabInfo := range tabInfos {
		if tabInfo == nil {
			continue
		}
		repeatedMap[tabInfo.GetId()] = true
		if tabInfo.TabType != tabPB.Tab_MINIGAME {
			count++
		} else {
			// 不需要返回那么多小游戏
			if gameCount >= targetLength {
				continue
			}
			gameCount++
		}
		res = append(res, tabInfo)
		// 返回的数据要多于展示的数据，安卓客户端审核时会屏蔽展示小游戏玩法
		if len(res) >= targetLength && count >= targetLength {
			break
		}
		// 旧版本返回的数据量不变
		if !isNewVersion && len(res) >= targetLength {
			break
		}
		repeatedMap[tabInfo.GetId()] = true
	}
	if homePageFilterTab != nil && !repeatedMap[homePageFilterTab.GetId()] {
		res = append(res, homePageFilterTab)
	}

	return res
}

// 将小游戏玩法排到最前
func moveMiniGameTab(tabInfos []*tabPB.Tab) []*tabPB.Tab {
	// 当前数据库数据符合的只有几十个
	//sort.SliceStable(tabInfos, func(i, j int) bool {
	//	return tabInfos[i].TabType > tabInfos[j].TabType
	//})
	miniGameTabs := make([]*tabPB.Tab, 0, len(tabInfos))
	otherTabs := make([]*tabPB.Tab, 0, len(tabInfos))
	for _, tabInfo := range tabInfos {
		if tabInfo.TabType == tabPB.Tab_MINIGAME {
			miniGameTabs = append(miniGameTabs, tabInfo)
		} else {
			otherTabs = append(otherTabs, tabInfo)
		}
	}
	miniGameTabs = append(miniGameTabs, otherTabs...)
	return miniGameTabs
}

func convertToQuickMatchItems(tabInfos []*tabPB.Tab, miniGameConf map[uint32]*topic_channel.MiniGameConfig) []*topic_channel.QuickMatchItem {
	if len(tabInfos) == 0 {
		return nil
	}
	quickMatchItems := make([]*topic_channel.QuickMatchItem, 0, len(tabInfos))
	for _, tabInfo := range tabInfos {
		quickMatchItem := &topic_channel.QuickMatchItem{
			ItemText:   tabInfo.GetName(),
			MaskLayer:  tabInfo.GetMaskLayer(),
			TabId:      tabInfo.GetId(),
			CategoryId: tabInfo.GetCategoryId(),
		}
		if tabInfo.TabType == tabPB.Tab_MINIGAME {
			conf, ok := miniGameConf[tabInfo.TagId]
			if ok {
				quickMatchItem.MiniGameConfig = conf
				quickMatchItem.CardType = topic_channel.CardType_MINIGAMECARD
			}
		} else {
			quickMatchItem.CardType = topic_channel.CardType_UGCCARD
		}
		//B测要大图
		quickMatchItem.ImageUrl = tabInfo.CardsImageUrl
		//A测要小图
		quickMatchItem.ItemIcon = tabInfo.SmallCardUrl

		quickMatchItems = append(quickMatchItems, quickMatchItem)
	}
	return quickMatchItems

}

// 首页显示多少人在玩
func (s *Server) getQuickMatchItemSecondaryText(ctx context.Context, tabItems []*topic_channel.QuickMatchItem) ([]*topic_channel.QuickMatchItem, error) {
	tabIds := make([]uint32, len(tabItems))
	for i, v := range tabItems {
		tabIds[i] = v.TabId
	}
	roomUserNumMap, err := s.getRoomUserNumMap(ctx, tabIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetQuickMatchItemSecondaryText getRoomUserNumMap error: %v", err)
		return tabItems, err
	}
	for i, v := range tabItems {
		tabItems[i].SecondaryText = fmt.Sprintf("%d人在玩", calculateRoomUserNum(roomUserNumMap[v.TabId]))
	}
	return tabItems, err
}

func (s *Server) getRoomUserNumMap(ctx context.Context, tabIds []uint32) (roomUserNumMap map[uint32]int64, err error) {
	roomUserNumMap = make(map[uint32]int64, 0)
	roomUserNumResp, err := s.tcChannelClient.GetChannelRoomUserNumber(ctx, &channelPB.GetChannelRoomUserNumberReq{TabId: tabIds})
	if err != nil {
		log.ErrorWithCtx(ctx, "getRoomUserCount GetChannelRoomUserNumber error: %v", err)
		return roomUserNumMap, err
	}

	for _, v := range roomUserNumResp.RoomUserInfo {
		roomUserNumMap[v.TabId] = v.TotalUserNumber
	}
	return
}

// - 2021年11月5日谢梓鹏定-V5.6版本发布
// - 「当前处于发布中的房间数」*「5」+ 「100~350随机数」
// - 2021年11月17日谢梓鹏修改-V5.6版本发布
// - 「当前处于发布中的房间数」*「35」+ 「100~350随机数」
func calculateRoomUserNum(realChannelNum int64) int64 {
	return realChannelNum*35 + rand.Int63n(250) + 100 //nolint:gosec
}

// B组新用户承接图及其文案
func (s *Server) getEntryTextAndImage(tabName string) (text, image string) {
	image = s.serviceConfigT.GetOption().GetQuickEntryRoomCard(tabName)
	if image != "" {
		text = fmt.Sprintf("秒配一个%s房吧！", tabName)
	}
	return
}
