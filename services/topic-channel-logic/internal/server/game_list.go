package server

import (
	"context"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/protocol/common/status"
	music_topic_channel "golang.52tt.com/protocol/services/music-topic-channel"
	rcmdPb "golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
	"golang.52tt.com/services/topic-channel-logic/internal/conf"
	"golang.52tt.com/services/topic-channel-logic/internal/tab_cache"
	"time"
)

const (
	tabInfoErrMsg               = "tabId参数错误"
	GameLabelSearchGuideDefault = "搜索玩法"
)

func (s *Server) LabelSearch(ctx context.Context, in *topic_channel.LabelSearchReq) (out *topic_channel.LabelSearchResp, err error) {
	log.DebugWithCtx(ctx, "LabelSearch in: %v", in)
	out = &topic_channel.LabelSearchResp{}

	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
		return
	}
	uid := serviceInfo.UserID
	if in.GetTabId() == 0 && len(in.GetFilterId()) == 0 {
		log.WarnWithCtx(ctx, "LabelSearch invalid req:%s", in.String())
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelTabNotFound, tabInfoErrMsg)
	}

	var tabIds []uint32
	if len(in.GetFilterId()) > 0 {
		filterInfoRsp, err := s.musicTopicChannelClient.GetFilterInfoByFilterIds(ctx, &music_topic_channel.GetFilterInfoByFilterIdsReq{
			FilterIds: []string{in.GetFilterId()},
			NeedCheck: true,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "LabelSearch GetFilterInfoByFilterIds userId(%v) in(%s) err(%v)", uid, in.String(), err)
			return out, err
		}
		tabIds = filterInfoRsp.GetFilterMap()[in.GetFilterId()].GetTabIds()
	}
	labelSearchReq := rcmdPb.LabelSearchReq{Uid: uid, TabId: in.GetTabId(), Text: in.GetText(), TabIds: tabIds}
	log.DebugWithCtx(ctx, "LabelSearch LabelSearchReq(%v)", utils.ToJson(labelSearchReq))
	rcmdResp, err := s.rcmdChannelLabelClient.LabelSearch(ctx, &labelSearchReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "LabelSearch rcmdLabelSearch userId(%v) tabId(%v) text(%v) err(%v)", uid, labelSearchReq.GetTabId(), labelSearchReq.GetText(), err)
		return out, err
	}
	log.InfoWithCtx(ctx, "LabelSearch rcmdLabelSearch req:%+v, userId:%d,rcmdResp:%+v", in, uid, rcmdResp.String())
	out.Labels = convertRcmdGameLabel(rcmdResp.Labels)
	return
}

func convertRcmdGameLabel(in []*rcmdPb.GameLabel) []*topic_channel.GameLabel {
	res := make([]*topic_channel.GameLabel, len(in))
	for i, v := range in {
		res[i] = &topic_channel.GameLabel{
			ValValue:    v.GetVal(),
			DisplayName: v.GetDisplayName(),
			Type:        topic_channel.GameLabelType_HotLabel,
			LabelType:   v.GetLabelType(),
		}
	}
	return res
}

func convertGameLabelToRcmd(in []*topic_channel.GameLabel) []*rcmdPb.GameLabel {
	res := make([]*rcmdPb.GameLabel, len(in))
	for i, v := range in {
		res[i] = &rcmdPb.GameLabel{
			Val:         v.GetValValue(),
			DisplayName: v.GetDisplayName(),
			Type:        rcmdPb.GameLabelType(v.GetType()),
			LabelType:   v.GetLabelType(),
		}
	}
	return res
}

func (s *Server) GetGameLabels(ctx context.Context, in *topic_channel.GetGameLabelsReq) (out *topic_channel.GetGameLabelsResp, err error) {
	log.DebugWithCtx(ctx, "GetGameLabels in: %v", in)
	out = &topic_channel.GetGameLabelsResp{}

	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
		return
	}
	uid := serviceInfo.UserID

	if in.GetTabId() == 0 || in.GetTabId() == InfElemID {
		out.Enable = false
		log.InfoWithCtx(ctx, "GetGameLabels tabId: %v, 屏蔽玩法入口", in.GetTabId())
		return
	}
	var newCtx context.Context
	var cancel context.CancelFunc
	if s.serviceConfigT.GetEnv() == conf.Testing {
		newCtx, cancel = context.WithTimeout(ctx, 2000*time.Millisecond)
		defer cancel()
	} else {
		newCtx, cancel = context.WithTimeout(ctx, 500*time.Millisecond)
		defer cancel()
	}
	getGameLabelsReq := rcmdPb.GetGameLabelsReq{Uid: uid, TabId: in.GetTabId(), BrowseLabels: convertGameLabelToRcmd(in.GetBrowseLabels().GetBrowseLabels())}
	log.DebugWithCtx(ctx, "GetGameLabels GetGameLabels GetGameLabelsReq(%v) \n", utils.ToJson(getGameLabelsReq))
	rcmdResp, lowErr := s.rcmdChannelLabelClient.GetGameLabels(newCtx, &getGameLabelsReq)
	if lowErr != nil {
		log.ErrorWithCtx(ctx, "GetGameLabels 请求推荐失败，屏蔽玩法入口 rcmdGetGameLabels userId(%v) tabId(%v) lowErr(%v)",
			uid, getGameLabelsReq.GetTabId(), lowErr)

		out.Enable = false
		return
	}
	out.Labels = convertRcmdGameLabel(rcmdResp.Labels)
	out.Enable = rcmdResp.GetEnable()

	tab, ok := tab_cache.GetTabIdMap()[in.GetTabId()]
	if ok {
		out.HideFilter = tab.HideFilter
	}
	if !out.Enable && out.HideFilter {
		out.HideFilter = false
	}

	log.InfoWithCtx(ctx, "LabelSearch uid(%v) len(%v) gameLabels(%v) enable(%v) hideFilter(%v)\n", uid, len(out.GetLabels()),
		out.GetLabels(), out.GetEnable(), out.GetHideFilter())
	return
}

func (s *Server) GetLabelSearchGuide(ctx context.Context, in *topic_channel.GetLabelSearchGuideReq) (out *topic_channel.GetLabelSearchGuideResp, err error) {
	out = &topic_channel.GetLabelSearchGuideResp{}
	if in.GetTabId() == 0 || in.GetTabId() == InfElemID {
		//返回默认文案吧
		out.Hint = GameLabelSearchGuideDefault
		out.HintType = topic_channel.HintType_DEFAULT_HINT
		return
	}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
		return
	}
	uid := serviceInfo.UserID
	newCtx, cancel := context.WithTimeout(ctx, 500*time.Millisecond)
	defer cancel()
	rcmdResp, lowErr := s.rcmdChannelLabelClient.GetSearchHint(newCtx, &rcmdPb.GetSearchHintReq{
		Uid:   uid,
		TabId: in.GetTabId(),
	})
	if lowErr != nil {
		log.ErrorWithCtx(ctx, "GetLabelSearchGuide rcmdGetSearchHint userId(%v) tabId(%v) err(%v)", uid, in.GetTabId(), lowErr)

		//返回默认文案
		out.Hint = GameLabelSearchGuideDefault
		out.HintType = topic_channel.HintType_DEFAULT_HINT
		return
	}
	if rcmdResp.GetType() == rcmdPb.GetSearchHintResp_DEFAULT {
		out.Hint = GameLabelSearchGuideDefault
	} else {
		out.Hint = rcmdResp.GetHint()
	}
	out.HintType = topic_channel.HintType(rcmdResp.GetType())
	return
}
