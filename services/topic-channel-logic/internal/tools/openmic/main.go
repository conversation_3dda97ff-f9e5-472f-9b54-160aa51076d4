package main

import (
	"bufio"
	"context"
	"golang.52tt.com/clients/channelmic"
	"golang.52tt.com/pkg/log"
	channel_go "golang.52tt.com/protocol/services/channel-go"
	"google.golang.org/grpc"
	"os"
	"strconv"
	"time"
)

var ccl channel_go.ChannelGoClient
var cmCli *channelmic.Client

func main() {
	ccl, _ = channel_go.NewClient(context.Background())
	cmCli = channelmic.NewClient(grpc.WithBlock())

	fileHandle, err := os.Open("channelList")
	if err != nil {
		log.ErrorWithCtx(nil, "readEachLine Open File error :%v", err)
		return
	}

	defer fileHandle.Close()
	lineScanner := bufio.NewScanner(fileHandle)
	start := time.Now()

	for lineScanner.Scan() {
		channelInt, err := strconv.Atoi(lineScanner.Text())
		if err != nil {
			log.ErrorWithCtx(nil, "%s change type error :%v", lineScanner.Text(), err)
			return
		}
		channelId := uint32(channelInt)
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		infoResp, err := ccl.GetChannelSimpleInfo(context.Background(), &channel_go.GetChannelSimpleInfoReq{
			ChannelId: channelId,
			OpUid:     0,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "channelId:%d GetChannelSimpleInfo error:%v", channelId, err)
			return
		}
		info := infoResp.GetChannelSimple()

		resp, err := cmCli.BatchSetChannelMicSpaceStatus(ctx, 0, info.GetBindId(), channelId, 1, []uint32{1, 2, 3, 4, 5, 6, 7, 8, 9})
		if err != nil {
			log.ErrorWithCtx(ctx, "channelId:%d BatchSetChannelMicSpaceStatus error:%v", channelId, err)
			return
		}
		log.InfoWithCtx(ctx, "channelId:%d reset success resp %v\n", channelId, resp)
		duration := int(time.Now().Sub(start).Seconds())
		if duration%15 == 0 {
			log.InfoWithCtx(ctx, "sleeping...")
			time.Sleep(2 * time.Second)
		}
	}

}
