package label

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mongo"
	"golang.52tt.com/services/invite-room/internal/mgr/label/store"
	"golang.52tt.com/services/invite-room/internal/mgr/label/store/entity"
)

type LabelsMgr struct {
	dao *store.LabelStore
}

func NewLabelMgr(database string, mongoClient *mongo.ClientImpl) *LabelsMgr {

	dao := store.NewLabelStore(database, mongoClient)

	return &LabelsMgr{
		dao: dao,
	}
}

func (l *LabelsMgr) UpsertLabels(ctx context.Context, labelInfo *entity.FilterLabel) error {
	return l.dao.UpsertLabels(ctx, labelInfo)
}

func (l *LabelsMgr) BatchGetLabels(ctx context.Context, tabIds []uint32, getAll bool) ([]entity.FilterLabel, error) {
	return l.dao.GetLabelsByTabIds(ctx, tabIds, getAll)
}
