package control

import (
	"context"
	userline_common_api "golang.52tt.com/protocol/services/userline-common-api"
	"math/rand"
	"net/http"
	"time"

	"golang.52tt.com/protocol/app/channel"

	"golang.52tt.com/clients/account"
	banuser "golang.52tt.com/protocol/services/banusersvr"

	"golang.52tt.com/pkg/mapreduce"

	"golang.52tt.com/pkg/protocol"

	"golang.52tt.com/pkg/log"
	banuserPB "golang.52tt.com/protocol/services/banusersvr"
	darkPB "golang.52tt.com/protocol/services/darkserver"
	found "golang.52tt.com/protocol/services/playerfound"
	"golang.52tt.com/services/master-apprentice-http/conf"
	"golang.52tt.com/services/master-apprentice-http/models"
	api "golang.52tt.com/services/master-apprentice-http/models/gen-go"

	"golang.52tt.com/pkg/web"
)

var s *models.ModelServer

func GetReserveApprentices(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, _ := context.WithTimeout(context.Background(), 10*time.Second)
	s = models.GetModelServer()
	out := new(api.GetReserveApprenticesResp)
	masterId := authInfo.UserID

	num, limit := 20, uint32(40)

	var interval int64 = 60 * 60 * 24 * 3
	now := time.Now()
	nowUnix := now.Unix()
	begin := nowUnix - rand.Int63n(interval)

	actResp, err := s.MasterapprenticeClient.GetActConfig(ctx)
	if err != nil {
		log.Errorf("GetActConfig err(%v)", err)
		_ = web.ServeAPICodeJson(w, int32(err.Code()), err.Error(), nil)
		return
	}
	t, _ := time.ParseInLocation("2006-01-02 15:04:05", actResp.BeginTime, time.Local)
	activityBeginAt := t.Unix()

	sub := nowUnix - activityBeginAt
	if sub > 0 && sub < interval {
		begin = nowUnix - rand.Int63n(sub)
	}

	if conf.Environment == conf.Debug {
		begin = time.Now().AddDate(0, 0, -300).Unix()
		num, limit = 10, uint32(40)
	}

	uids, err := s.MasterapprenticeClient.BatchGetOnlineReserveApprenticeV2(ctx, masterId, uint32(begin), 0, limit)
	if err != nil {
		log.Errorf("BatchGetReserveApprentice err(%v)", err)
		_ = web.ServeAPICodeJson(w, int32(err.Code()), err.Error(), nil)
		return
	}
	log.Debugf("BatchGetOnlineReserveApprenticeV2 masteruid(%v) uids(%v)", masterId, uids)
	if len(uids) == 0 {
		_ = web.ServeAPIJson(w, out)
		return
	}

	userBannedStatus := map[uint32]*banuser.BannedStatus{}
	blackUserMap := make(map[uint32]struct{})
	userMap := map[uint32]*account.User{}
	apprenticeToMaster := make(map[uint32]uint32)
	recvLimitMap := make(map[uint32]bool)
	inviteds := make(map[uint32]struct{})
	usertagMap := make(map[uint32][]*userline_common_api.UserTagBase)

	batchErr := mapreduce.Finish(func() (err error) {
		userBannedStatus, err = s.BanuserClient.GetUserBannedStatuses(ctx, masterId, uids)
		if err != nil {
			log.Errorf("GetUserBannedStatuses masterId(%d) uids(%s) err(%v)", masterId, uids, err)
			return err
		}
		return
	}, func() (err error) {
		resp, err := s.DarkClient.BatchBlackUserListCheck(ctx, darkPB.BatchGetBlackUserListReq{UidList: uids})
		if err != nil {
			log.Errorf("BatchUserBehaviorCheck masterId(%d) uids(%s) err(%v)", masterId, uids, err)
			return
		}

		for _, uidBehavior := range resp.UidBehaviorList {
			if uidBehavior.Status {
				blackUserMap[uidBehavior.Uid] = struct{}{}
			}
		}
		return
	}, func() (err error) {
		allUIDs := append(uids, masterId)
		userMap, err = s.AccountClient.GetUsersMap(ctx, allUIDs)
		if err != nil {
			log.Errorf("GetUsersMap masterId(%d) uids(%s) err(%v)", masterId, uids, err)
			return err
		}
		return
	}, func() (err error) {
		apprenticeToMaster, err = s.MasterapprenticeClient.BatchGetUserMasterInfo(ctx, uids)
		if err != nil {
			log.Errorf("BatchGetUserMasterInfo masterId(%d) uids(%s) err(%v)", masterId, uids, err)
			return
		}
		return
	}, func() (err error) {
		recvLimitMap, err = s.ImStrangerCli.BatchCheckUserRecvLimit(ctx, authInfo.UserID, uids)
		if err != nil {
			log.Errorf("BatchCheckUserRecvLimit masterId(%d) err(%v)", masterId, err)
			return
		}
		return
	}, func() (err error) {
		inviteds, err = s.ImStrangerCli.GetStrangerGreetDetail(ctx, masterId)
		if err != nil {
			log.Errorf("GetStrangerGreetDetail masterId(%d) err(%v)", masterId, err)
			return
		}
		return
	}, func() (err error) {
		usertagMap, err = s.UserlineCommonApiClient.BatGetUserTag(ctx, append(uids, masterId), false)
		if err != nil {
			log.Errorf("GetStrangerGreetDetail masterId(%d) err(%v)", masterId, err)
			return err
		}
		return
	})
	if batchErr != nil {
		_ = web.ServeAPICodeJson(w, int32(-2), batchErr.Error(), nil)
		return
	}

	out.InvitedNum = uint32(len(inviteds))

	invalidUids := make([]uint32, 0, len(uids))

	//

	var femaleApprentices, maleApprentices []*api.ReserveApprentice

	for _, uid := range uids {
		if _, ok := inviteds[uid]; ok {
			continue
		}

		if isLimit, ok := recvLimitMap[uid]; ok && isLimit {
			continue
		}

		var age int64 = -1
		tags := usertagMap[uid]
		for i := 0; i < len(tags); i++ {
			if tags[i].TagType == uint32(channel.UserTagType_USERTAG_TYPE_BIRTHDAY) && !tags[i].IsDel {
				birthTime, err := time.Parse("2006-01-02", tags[i].TagName)
				if err != nil {
					log.Errorf("time.Parse err: %s, TagName: %s", err.Error(), tags[i].TagName)
					continue
				}

				age = (nowUnix - birthTime.Unix()) / (365 * 86400)
				log.Debugf("birthDay:%d,age:%d", birthTime.Unix(), age)
				break
			}
		}
		// if age != -1 && age < 15 {
		// 	invalidUids = append(invalidUids, uid)
		// 	continue
		// }

		// 暂时不加男性限制
		//if user, ok := userMap[uid]; !ok || user.Sex != int32(accountPB.USER_SEX_USER_SEX_MALE) {
		//	invalidUids = append(invalidUids, uid)
		//	continue
		//}

		if apprenticeToMaster[uid] != 0 {
			invalidUids = append(invalidUids, uid)
			continue
		}

		if bannedStatus, ok := userBannedStatus[uid]; !ok || bannedStatus.Status != uint32(banuserPB.BAN_STATUS_BAN_ST_NORMAL) {
			invalidUids = append(invalidUids, uid)
			continue
		}

		if _, ok := blackUserMap[uid]; ok {
			invalidUids = append(invalidUids, uid)
			continue
		}

		apprentice := api.ReserveApprentice{
			Uid:      uid,
			Nickname: userMap[uid].Nickname,
			Account:  userMap[uid].Username,
			Gender:   uint32(userMap[uid].Sex),
		}
		if apprentice.Gender == 0 {
			femaleApprentices = append(femaleApprentices, &apprentice)
		} else {
			maleApprentices = append(maleApprentices, &apprentice)
		}
	}
	//完美洗牌
	apprentices := make([]*api.ReserveApprentice, 0, limit)
	sameSexCount := num * 2 / 10
	diffSexCount := num * 8 / 10
	if userMap[masterId].GetSex() == 0 { //
		if len(femaleApprentices) > sameSexCount {
			items := femaleApprentices[0:sameSexCount]
			apprentices = append(apprentices, items...)
		}
		if len(maleApprentices) > diffSexCount {
			items := maleApprentices[0:diffSexCount]
			apprentices = append(apprentices, items...)
		}
	} else { //男生
		if len(maleApprentices) > sameSexCount {
			items := maleApprentices[0:sameSexCount]
			apprentices = append(apprentices, items...)
		}
		if len(femaleApprentices) > diffSexCount {
			items := femaleApprentices[0:diffSexCount]
			apprentices = append(apprentices, items...)
		}
	}
	log.Debugf("femaleApprentices (%v) maleApprentices(%v) apprentices(%v) sameSexCount %v diffSexCount %v", femaleApprentices, maleApprentices, apprentices, sameSexCount, diffSexCount)
	n := len(apprentices)
	if n == 0 {
		_ = web.ServeAPIJson(w, out)
		return
	}
	for i := 0; i <= n/2; i++ {
		j := rand.Intn(n)
		apprentices[i], apprentices[j] = apprentices[j], apprentices[i]
	}

	if len(invalidUids) > 0 {
		err = s.MasterapprenticeClient.BatchDelReserveApprentice(ctx, invalidUids)
		if err != nil {
			log.Errorf("BatchDelReserveApprentice err(%v)", err)
		}
	}
	apprenticeIds := make([]uint32, 0, len(apprentices))
	for _, v := range apprentices {
		apprenticeIds = append(apprenticeIds, v.Uid)
	}

	settingsMap, err := s.PlayerFoundCli.GetPlayerFoundSettingWithType(ctx, apprenticeIds, found.Settings_CITY_TAG)
	if err != nil {
		log.Errorf("GetPlayerFoundSetting err(%s)", err.Error())
		_ = web.ServeAPICodeJson(w, int32(err.Code()), err.Error(), nil)
		return
	}
	ip2City, err := s.GetCityTab(ctx, append(apprenticeIds, authInfo.UserID))
	if err != nil {
		log.Errorf("GetCityTab err(%v) player(%v)", err, apprenticeIds)
		_ = web.ServeAPICodeJson(w, int32(err.Code()), err.Error(), nil)
		return
	}

	uidTabIDFilter := make(map[uint32]uint32)
	hasAttachmentFilter := s.GetHasAttachmentFilter(ctx, apprenticeIds)
	texts, err1 := s.GetRecommendNote(ctx, masterId, apprenticeIds, uidTabIDFilter, hasAttachmentFilter, usertagMap)
	err = protocol.ToServerError(err1)
	if err != nil {
		log.Errorf("getRecommendNote err(%v)", err)
		_ = web.ServeAPICodeJson(w, int32(err.Code()), err.Error(), nil)
		return
	}

	for i, uid := range apprenticeIds {
		if setting, ok := settingsMap[uid]; ok && setting && ip2City[authInfo.UserID] == ip2City[uid] {
			apprentices[i].City = ip2City[uid]
		}
		if text, ok := texts[uid]; ok {
			apprentices[i].Recommendation = text.TopText
		}
	}
	out.Apprentices = apprentices
	log.Debugf("GetReserveApprentices masteruid(%v) out(%v)", masterId, out)
	_ = web.ServeAPIJson(w, out)
}
