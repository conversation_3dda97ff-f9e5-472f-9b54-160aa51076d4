// Copy from playerlogic
package models

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	userline_common_api "golang.52tt.com/protocol/services/userline-common-api"
	"math/rand"
	"strings"
	"time"
	"unicode/utf8"

	"golang.52tt.com/clients/ugc/content"
	pb "golang.52tt.com/protocol/services/ugc/content"
	firendshipPB "golang.52tt.com/protocol/services/ugc/friendship"

	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"

	"golang.52tt.com/clients/account"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/mapreduce"
)

const (
	whoPlayType uint32 = 2
	gameType    uint32 = 4
)

var (
	newerTextList = []string{
		"新人报道，请多指教，求带呀~",
		"我的小确幸是成为你的第一个玩伴",
		"求波关注~带你玩TT呀",
		"来TT不要怕，提我的名字带你飞。",
	}
	endTextMap = map[string]string{
		"扩列找陪伴": "扩列找陪伴，聊天秒回，dd我",
		"扩列找玩伴": "扩列找陪伴，聊天秒回，dd我",
		"开黑找队友": "开黑找队友哟，专业喊666",
		"上分找大神": "大神带带我，我躺赢的样子超甜",
	}

	endProvideTextList = []string{
		"我们匹配度高达%s，有兴趣交个朋友不？",
		"哇！我们的匹配度竟然高达%s",
	}
)

type RecommendNote struct {
	Uid             uint32   //uid
	TextList        []string //推荐语列表
	TopText         string   //首页的推荐语
	SignText        string   //签名
	PlayMethodText  string   //推荐玩法
	NewerText       string   //新人推薦
	GameText        string   //游戏卡片
	GamePlayingText string   //在玩游戏卡片
	EndText         string   //兜底文案
	StrategyName    string   //策略名字
	FirendText      string   //期待好友的情况
	ProvideText     string   //发放玩伴的推荐语
	GameNameConfig  map[string]int
	GameNameList    []string
	AreaConfig      map[string]string
	DanConfig       map[string]int
	DanNameConfig   map[string]string
}

// GetRecommendNote 获取推荐语
func (s *ModelServer) GetRecommendNote(ctx context.Context, uid uint32, othersIDs []uint32, uidTabIDFilter map[uint32]uint32, uidHasAttachmentFilter map[uint32]bool,
	usertagMap map[uint32][]*userline_common_api.UserTagBase) (map[uint32]*RecommendNote, error) {
	if uid == 0 || len(othersIDs) == 0 || uidTabIDFilter == nil {
		return nil, fmt.Errorf("getRecommend param error %d,%d,%d", uid, len(othersIDs), uidTabIDFilter)
	}

	//構造recommend
	recommendNote := genRecommendNoteByUserTagMap(usertagMap)
	userFilter := map[uint32]*account.User{}
	tabsFilter := map[uint32]string{}
	followFilter := map[uint32]*firendshipPB.Counts{}

	err := mapreduce.Finish(
		func() (err error) {
			//个人信息获取
			userFilter, err = s.AccountClient.GetUsersMap(ctx, othersIDs)
			if err != nil {
				log.Errorf("PlayerProvided  GetRecommendNote GetUsersMap uid: %d,othersIDs:%+v,merr: %s", uid, othersIDs, err.Error())
				return err
			}
			log.Debugf("userFilter:%+v", userFilter)
			return nil
		}, func() (err error) {
			//进游戏房推荐策略
			skip := uint32(0)
			limit := uint32(10000)
			tabsRsp, err := s.TabTTClient.TabsForTT(ctx, skip, limit)
			if err != nil {
				log.Errorf("PlayerProvided  TabsForTT GetUsersMap uid: %d,othersIDs:%+v,merr: %s", uid, othersIDs, err.Error())
				return err
			}
			tabsFilter = getTabsFilter(tabsRsp.Tabs)
			return nil
		}, func() (err error) {
			//粉丝数获取
			followFilter, err = s.FriendshipCli.BatchGetUserCounts(ctx, othersIDs)
			if err != nil {
				log.Errorf("PlayerProvided  friendshipCli BatchGetUserCounts uid: %d,othersIDs:%+v,merr: %s", uid, othersIDs, err.Error())
				return err
			}
			return nil
		},
	)
	if err != nil {
		log.Errorf("recommendNote mapreducer err %v", err)
		return recommendNote, err
	}
	uInfo := recommendNote[uid]
	for key := range recommendNote {
		//新人推薦
		recommendNote[key].buildNewerText(uidHasAttachmentFilter, userFilter)
		//游戏卡的构造
		recommendNote[key].buildGameText(uInfo)
		//进房游戏推荐
		recommendNote[key].buildPlayMethodText(uidTabIDFilter, tabsFilter)
		//个性签名设置
		recommendNote[key].buildSignText(userFilter)
		//玩伴数
		recommendNote[key].buildPlayerNum(followFilter)

		//在玩卡片
		recommendNote[key].buildGamePlayingText()
		//设置兜底
		recommendNote[key].buildEndText()
		//设置找人玩的展示
		recommendNote[key].buildTopText()
		//推荐设置 這個一定是要最后设置
		recommendNote[key].buildTextList()
		//增加发放玩伴的推荐语 也要在其他设置好后进行设置
		recommendNote[key].buildProvideText()
		log.Infof("recommendNote note build uid:%d,key:%d,note:%+v\n", uid, key, recommendNote[key])
	}
	return recommendNote, nil
}

// 获取附件filter 看是否有相关的
func (s *ModelServer) GetHasAttachmentFilter(ctx context.Context, uids []uint32) map[uint32]bool {
	hasAttachmentFilter := make(map[uint32]bool, len(uids))
	//获取最前的几条记录
	postFilter := make(map[string]uint32)
	//cache管理
	userPostInfos, err := s.PostHistoryClient.BatchGetUserTopNPostHistories(ctx, uids, 4)
	if err != nil {
		log.Errorf("postHistoryClent.BatchGetUserTopNPostHistories err:%s, uids:%+v", err.Error(), uids)
		return hasAttachmentFilter
	}
	var allPostIDs []string
	for _, v := range userPostInfos {
		for _, v2 := range v.Records {
			postFilter[v2.GetPostId()] = v.GetUid()
		}
	}
	for postID := range postFilter {
		allPostIDs = append(allPostIDs, postID)
	}

	if len(allPostIDs) == 0 {
		return hasAttachmentFilter
	}
	//获取帖子状态
	postInfos, err := s.ContentClient.BatchGetPostListById(ctx, &pb.BatchGetPostListByIdReq{PostIdList: allPostIDs, ContentType: pb.ContentType_ORIGIN_TEXT})
	if err != nil {
		log.Errorf("ContentClient.BatchGetPostListById err:%v", err)
		return hasAttachmentFilter
	}
	for _, postInfo := range postInfos {

		if postInfo.Status == content.Deleted {
			log.Warnf("postInfo.Status del err:%+v", postInfo)
			continue
		}
		if postInfo.PostOrigin == pb.PostOrigin_POST_ORIG_REGISTER {
			log.Debugf("PostOrigin reg:%+v", postInfo)
			continue
		}
		if postInfo.Status == content.None || postInfo.Status == content.WaitForReview || postInfo.Status == content.PreparedButWaitForReview || postInfo.Status == content.Suspicious || postInfo.Status == content.Illegal || postInfo.Status == content.Banned {
			log.Warnf("postInfo.Status err:%+v", postInfo)
			continue
		}

		for _, v2 := range postInfo.GetAttachments() {
			//有缩略图
			if v2.GetType() == pb.AttachmentInfo_IMAGE || v2.GetType() == pb.AttachmentInfo_GIF || v2.GetType() == pb.AttachmentInfo_VIDEO {
				//这个uid有缩略图
				hasAttachmentFilter[postFilter[postInfo.GetPostId()]] = true
				break
			}
		}
	}
	log.Debugf("hasAttachmentFilter:%+v userPostInfos:%+v postInfos:%+v", hasAttachmentFilter, userPostInfos, postInfos)
	return hasAttachmentFilter
}

func (note *RecommendNote) Init(v *userline_common_api.UserTagBase, danConfig map[string]map[string]int) {
	v.TagName = convertOctonaryUtf8(v.TagName)
	if v.TagType == whoPlayType {
		note.StrategyName = v.TagName
	}
	//4.玩游戏
	if v.TagType == gameType {
		//进入游戏段位判断
		note.GameNameConfig[v.TagName] = 1
		note.GameNameList = append(note.GameNameList, v.TagName)
		item := userline_common_api.UserGameTagExt{}
		err := proto.Unmarshal(v.TagInfo, &item)
		if err != nil {
			log.Errorf("UserGameTagExt Unmarshal err:%s", err.Error())
			return
		}
		//这里8进制
		for _, v1 := range item.OptList {
			optName := convertOctonaryUtf8(v1.OptName)
			if optName == "段位" {
				//设置段位配置表
				for i, v2 := range v1.ValueConfList {
					if _, ok := danConfig[v.TagName]; !ok {
						danConfig[v.TagName] = make(map[string]int, 5)
					}
					danConfig[v.TagName][convertOctonaryUtf8(v2)] = i
				}

			}
		}
		//note的游戏配置
		for _, v1 := range item.OptList {
			optName := convertOctonaryUtf8(v1.OptName)
			if optName == "段位" {
				for _, v2 := range v1.ValueUsersetList {
					note.DanConfig[v.TagName] = danConfig[v.TagName][convertOctonaryUtf8(v2)]
					note.DanNameConfig[v.TagName] = convertOctonaryUtf8(v2)
				}
			}
			if optName == "区服" {
				//设置区服配置表
				for _, v2 := range v1.ValueUsersetList {
					note.AreaConfig[v.TagName] = convertOctonaryUtf8(v2)
				}
			}
		}
	}
}

func (note *RecommendNote) buildPlayerNum(followFilter map[uint32]*firendshipPB.Counts) {
	if v, ok := followFilter[note.Uid]; ok {
		if v.GetFollowerCount() < 5 || v.GetFollowingCount() < 5 {
			note.FirendText = "我是个没有玩伴的可怜人，愿意成为我的玩伴吗"
		}
	} else {
		note.FirendText = "我是个没有玩伴的可怜人，愿意成为我的玩伴吗"
	}
}

// 在顶的列表
func (note *RecommendNote) buildTopText() {
	optArr := []string{note.GameText, note.GamePlayingText, note.NewerText, note.FirendText, note.EndText}
	for _, v := range optArr {
		if v != "" {
			note.TopText = v
			return
		}
	}
}

// 个人信息页 推荐语列表
func (note *RecommendNote) buildTextList() {
	optArr := []string{note.GamePlayingText, note.NewerText, note.GameText, note.PlayMethodText, note.SignText}
	for _, v := range optArr {
		if v != "" {
			note.TextList = append(note.TextList, v)
		}
	}
	if len(note.TextList) == 0 {
		//if note.EndText != "" {
		note.TextList = append(note.TextList, note.EndText)
		//}
	}
	//最多5个
	if len(note.TextList) > 5 {
		note.TextList = note.TextList[0:5]
	}
}

// 兜底逻辑
func (note *RecommendNote) buildEndText() {
	if v, ok := endTextMap[note.StrategyName]; ok {
		note.EndText = fmt.Sprintf("%s%s", "", v)
	}
}

func (note *RecommendNote) buildProvideText() {
	var provideTextList = []string{
		note.GamePlayingText,
		note.GameText,
	}
	for _, v := range provideTextList {
		if v != "" {
			note.ProvideText = v
			return
		}
	}
	prefix := endProvideTextList[rand.Intn(len(endProvideTextList))]
	precent := fmt.Sprintf("%d%%", 85+rand.Intn(10))
	endNote := fmt.Sprintf(prefix, precent)
	note.ProvideText = endNote
	return
}

// 沒發過貼的新人的推薦
func (note *RecommendNote) buildNewerText(userAttahmentFilter map[uint32]bool, userFilter map[uint32]*account.User) {
	//log.Infof("userAttahmentFilter %+v: userFilter %+v", userAttahmentFilter, userFilter)
	if userAttahmentFilter == nil {
		return
	}
	exist := userAttahmentFilter[note.Uid]
	//没有附件 且存在
	if u, ok := userFilter[note.Uid]; !exist && ok && time.Now().Unix()-int64(u.GetRegisteredAt()) < 7*24*3600 {
		note.NewerText = newerTextList[rand.Intn(len(newerTextList))]

	}
	if note.NewerText != "" {
		note.NewerText = fmt.Sprintf("%s%s", "", newerTextList[rand.Intn(len(newerTextList))])
	}

}

func (note *RecommendNote) buildGamePlayingText() {
	texts := make([]string, 0, 3)
	//段位
	for _, gameName := range note.GameNameList {
		//游戏名 +  段位名
		newGameName := gameName
		if v, ok := note.DanNameConfig[gameName]; ok {
			newGameName = newGameName + "/" + v
		}
		newGameName = fmt.Sprintf("%s", newGameName)
		texts = append(texts, newGameName)
	}
	if len(texts) == 1 {
		arr := strings.Split(texts[0], "/")
		if len(arr) == 1 {
			note.GamePlayingText = fmt.Sprintf("我喜欢玩%s，开黑请dd", arr[0])
		} else {
			note.GamePlayingText = fmt.Sprintf("我喜欢玩%s，段位是%s，开黑请dd", arr[0], arr[1])
		}

	} else if len(texts) == 2 {
		for i := range texts {
			texts[i] = fmt.Sprintf("%s", texts[i])
		}
		note.GamePlayingText = strings.Join(texts, "、")
		note.GamePlayingText = fmt.Sprintf("最近在玩%s", note.GamePlayingText)
	}

	if len(note.GamePlayingText) != 0 {
		note.GamePlayingText = fmt.Sprintf("%s%s", "", note.GamePlayingText)
	}
}

// 依赖u来构造
func (note *RecommendNote) buildGameText(u *RecommendNote) {
	//段位处理
	if note.GameText == "" {
		for gameName, level := range note.DanConfig {
			if text := gameDanConfigText(u.DanConfig, gameName, level); text != "" {
				note.GameText = text
				break
			}
		}
	}
	//区服处理
	if note.GameText == "" {
		for gameName, area := range note.AreaConfig {
			if text := gameAreaConfigText(u.AreaConfig, gameName, area); text != "" {
				note.GameText = text
				break
			}
		}
	}
	// //都玩过xx游戏处理
	// if note.GameText == "" {
	// 	if text := gameNameConfigText(u.GameNameConfig, note.GameNameConfig); text != "" {
	// 		note.GameText = text
	// 	}
	// }
	if note.GameText != "" {
		note.GameText = fmt.Sprintf("%s%s", "", note.GameText)
	}

}

// 依赖两个filter
func (note *RecommendNote) buildPlayMethodText(uidTabIDFilter map[uint32]uint32, tabFilter map[uint32]string) {
	if id, ok := uidTabIDFilter[note.Uid]; ok && id != 0 {
		if tagName, ok := tabFilter[id]; ok {
			note.PlayMethodText = fmt.Sprintf("我们最近都爱玩「%s」，要不马上开一把？", tagName)
		}
	}
	if note.PlayMethodText != "" {
		note.PlayMethodText = fmt.Sprintf("%s%s", "", note.PlayMethodText)
	}
}

// 签名设置
func (note *RecommendNote) buildSignText(userTagFilter map[uint32]*account.User) {
	if v, ok := userTagFilter[note.Uid]; ok {
		if utf8.ValidString(v.Signature) {
			note.SignText = v.Signature
		}
	}
	if note.SignText != "" {
		note.SignText = fmt.Sprintf("%s%s", "签名：", note.SignText)
	}
}

func genRecommendNoteByUserTagMap(tagFilter map[uint32][]*userline_common_api.UserTagBase) map[uint32]*RecommendNote {
	recommendNote := make(map[uint32]*RecommendNote, len(tagFilter))
	danConfig := make(map[string]map[string]int, 10)
	for id, list := range tagFilter {
		note := &RecommendNote{
			Uid:            id,
			AreaConfig:     make(map[string]string, 5),
			DanConfig:      make(map[string]int, 5),
			GameNameConfig: make(map[string]int, 5),
			GameNameList:   make([]string, 0, 2),
			DanNameConfig:  make(map[string]string, 2),
		}
		//该游戏的配置
		for _, v := range list {
			note.Init(v, danConfig)
		}
		recommendNote[id] = note
	}
	return recommendNote
}

func gameAreaConfigText(userAreaConfig map[string]string, gameName, area string) string {
	if a1, ok := userAreaConfig[gameName]; ok && a1 == area {
		return fmt.Sprintf("我们%s区服相同，开黑组队可滴滴", gameName)
	}
	return ""
}

func convertOctonaryUtf8(in string) string {
	return string([]rune(in))
}

func gameDanConfigText(userDanConfig map[string]int, gameName string, level int) string {
	if lv1, ok := userDanConfig[gameName]; ok {
		abs := lv1 - level
		if abs < 0 {
			abs = -abs
		}
		if abs == 0 {
			return fmt.Sprintf("我们%s段位相同，上分请选我", gameName)
		}
		if abs <= 1 {
			return fmt.Sprintf("我们%s段位相近，一起上分呀", gameName)
		}
	}
	return ""
}

func getTabsFilter(tabsRsp []*tabPB.Tab) map[uint32]string {
	tabFilter := make(map[uint32]string, len(tabsRsp))
	for i := range tabsRsp {
		if tabsRsp[i].GetId() != 0 && (tabsRsp[i].TabType == tabPB.Tab_MINIGAME || tabsRsp[i].TabType == tabPB.Tab_NORMAL && convertOctonaryUtf8(tabsRsp[i].Name) == "扩列聊天") {
			tabFilter[tabsRsp[i].GetId()] = convertOctonaryUtf8(tabsRsp[i].Name)
		}
	}
	//log.Infof(" tabFilter:%+v", tabFilter)
	return tabFilter
}
