package models

import (
	"context"
	"fmt"
	imstrangergo "golang.52tt.com/clients/imstranger-go"
	userline_common_api "golang.52tt.com/clients/userline-common-api"
	"math/rand"
	"time"

	"golang.52tt.com/pkg/protocol"
	lbsPb "golang.52tt.com/protocol/services/lbs/lbs_supervise"

	beego "github.com/astaxie/beego/config"
	"github.com/go-redis/redis"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/anti"
	"golang.52tt.com/clients/banuser"
	"golang.52tt.com/clients/darkserver"
	lbs_supervise "golang.52tt.com/clients/lbs-supervise"
	master_apprentice "golang.52tt.com/clients/master-apprentice"
	playerfound "golang.52tt.com/clients/player-found"
	"golang.52tt.com/clients/topic-channel/tab"
	"golang.52tt.com/clients/ugc/content"
	"golang.52tt.com/clients/ugc/friendship"
	"golang.52tt.com/clients/ugc/post_history"
	nConfig "golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	tracing "golang.52tt.com/pkg/tracing/jaeger"
	"golang.52tt.com/services/master-apprentice-http/conf"
	"golang.52tt.com/services/master-apprentice-http/models/cache"
)

var modelClients = new(ModelServer)

func init() {

}

func GetModelServer() *ModelServer {
	return modelClients
}

type ModelServer struct {
	sc          *conf.ServerConfig
	CacheClient *cache.Client

	MasterapprenticeClient  *master_apprentice.Client
	DarkClient              *darkserver.Client
	BanuserClient           *banuser.Client
	AccountClient           *account.Client
	AntiClient              *anti.Client
	LocClient               *lbs_supervise.Client
	PlayerFoundCli          *playerfound.Client
	UserlineCommonApiClient *userline_common_api.Client
	TabTTClient             *tab.Client
	FriendshipCli           *friendship.Client
	PostHistoryClient       *post_history.Client
	ContentClient           *content.Client
	ImStrangerCli           *imstrangergo.Client
}

func (c *ModelServer) InitWithConfigerEx(config nConfig.Configer) error {
	c.sc = new(conf.ServerConfig)
	err := c.sc.Parse(config)
	if err != nil {
		return err
	}
	redisClient := redis.NewClient(&redis.Options{
		Addr:     c.sc.GetRedisConfig().Addr(),
		PoolSize: c.sc.GetRedisConfig().PoolSize,
	})
	log.Debugf("Initialized redis connection pool to %s", c.sc.GetRedisConfig().Addr())
	redisTracer := tracing.Init("httpgo_redis")
	c.CacheClient = cache.NewCacheClient(redisClient, redisTracer)

	c.MasterapprenticeClient, err = master_apprentice.NewClient()
	if err != nil {
		log.Errorf("master_apprentice.NewClient %v", err)
		return err
	}
	c.AccountClient, err = account.NewClient()
	if err != nil {
		log.Errorf("account.NewClient %+v", err)
		return err
	}
	c.DarkClient, err = darkserver.NewClient()
	if err != nil {
		log.Errorf("darkserver.NewClient %+v", err)
		return err
	}
	c.BanuserClient = banuser.NewClient()
	c.PlayerFoundCli, err = playerfound.NewClient()
	if err != nil {
		log.Errorf("playerfound.NewClient err %s", err.Error())
		return err
	}
	c.AntiClient = anti.NewClient()
	c.LocClient, _ = lbs_supervise.NewClient()
	c.UserlineCommonApiClient, _ = userline_common_api.NewClient()
	c.TabTTClient, err = tab.NewClient()
	if err != nil {
		log.Errorf("TabTTClient.NewClient err %s", err.Error())
		return err
	}
	//粉丝数
	c.FriendshipCli, err = friendship.NewClient()
	if err != nil {
		log.Errorf("friendship.NewClient err %s", err.Error())
		return err
	}
	c.PostHistoryClient, _ = post_history.NewClient()
	c.ContentClient, _ = content.NewClient()

	c.ImStrangerCli, err = imstrangergo.NewClient()
	if err != nil {
		log.Errorf("imstranger.NewClient err:%s", err.Error())
		return err
	}

	rand.Seed(time.Now().UnixNano())

	fmt.Printf("server config:%+v\n", c.sc)
	return nil
}

func (c *ModelServer) InitWithConfiger(config beego.Configer) error {
	c.sc = new(conf.ServerConfig)
	err := c.sc.Parse(config)
	if err != nil {
		return err
	}
	redisClient := redis.NewClient(&redis.Options{
		Addr:     c.sc.GetRedisConfig().Addr(),
		PoolSize: c.sc.GetRedisConfig().PoolSize,
	})
	log.Debugf("Initialized redis connection pool to %s", c.sc.GetRedisConfig().Addr())
	redisTracer := tracing.Init("httpgo_redis")
	c.CacheClient = cache.NewCacheClient(redisClient, redisTracer)

	fmt.Printf("server config:%+v\n", c.sc)
	return nil
}

func (c *ModelServer) GetServerConfig() *conf.ServerConfig {
	return c.sc
}

func (s *ModelServer) GetCityTab(ctx context.Context, uids []uint32) (map[uint32]string, protocol.ServerError) {
	rsp, err := s.AntiClient.BatchGetUserLastLoginInfo(ctx, 0, uids)
	if err != nil {
		log.Errorf("antiClient.BatchGetUserLastLoginInfo err: %s, uids:%+v", err.Error(), uids)
		return nil, err
	}
	userIps := make(map[uint32]string)
	ips := make([]string, 0)
	for _, info := range rsp.GetInfoList() {
		userIps[info.GetUid()] = info.GetClientIp()
		ips = append(ips, info.GetClientIp())
	}
	req := &lbsPb.GetLocationbyIpReq{
		IpList: ips,
	}
	locRsp, err := s.LocClient.GetLocationbyIp(ctx, req)
	if err != nil {
		log.Errorf("GetLocationbyIp err: %s, req:%+v", err.Error(), req)
		return nil, err
	}
	ip2loc := make(map[string]string)
	for _, info := range locRsp.GetDetailList() {
		if info.GetExist() {
			ip2loc[info.GetIp()] = info.GetAdrDetail().GetCity()
		}
	}
	userLocs := make(map[uint32]string)
	for uid, ip := range userIps {
		if loc, ok := ip2loc[ip]; ok {
			userLocs[uid] = loc
		}
	}
	return userLocs, nil
}
