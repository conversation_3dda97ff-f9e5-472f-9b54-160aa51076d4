package eventlink

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/publisher"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"

	"golang.52tt.com/pkg/config"
	eventlink "golang.52tt.com/pkg/kafka_eventlink"
	"golang.52tt.com/pkg/log"
	ugc_community_event "golang.52tt.com/protocol/services/ugc-community/event"
	"golang.52tt.com/services/ugc-community/internal/event"
)

//go:generate ifacemaker -f publisher.go -s eventlinkPublisher -p event -i EventPublisher -o ../publisher.go
type eventlinkPublisher struct {
	publishers map[string]eventlink.IELKafkaProducer
}

func NewEventPublisher(ctx context.Context, cfgList ...*config.KafkaConfig) (event.EventPublisher, error) {
	p := &eventlinkPublisher{
		publishers: make(map[string]eventlink.IELKafkaProducer),
	}
	for _, cfg := range cfgList {
		for _, topic := range cfg.TopicList() {
			publisher, err := eventlink.NewEventLinkKafkaProducer(cfg.ClientID, topic, cfg.BrokerList())
			if err != nil {
				log.ErrorWithCtx(ctx, "NewEventPublisher NewEventLinkKafkaProducer topic(%s) cfg(%+v) err: %v", topic, cfg, err)
				return nil, err
			}

			p.publishers[topic] = publisher
		}
	}

	return p, nil
}

func (p *eventlinkPublisher) Publish(ctx context.Context, msg *publisher.ProducerMessage) publisher.PublishResult {
	producer, ok := p.publishers[msg.Topic]
	if !ok {
		return publisher.NewErrorResult(fmt.Errorf("topic %s handler not found", msg.Topic))
	}

	return producer.Publish(ctx, msg)
}

func (p *eventlinkPublisher) PublishAttitudeEvent(ctx context.Context, ev *ugc_community_event.AttitudeEvent) error {
	bin, err := proto.Marshal(ev)
	if err != nil {
		return err
	}

	msg := &publisher.ProducerMessage{
		Topic: "ugc_community_attitude_event",
		Key:   publisher.StringEncoder(strconv.Itoa(int(ev.GetUserId()))),
		Value: publisher.ByteEncoder(bin),
	}

	if result := p.Publish(ctx, msg); result.Err != nil {
		return result.Err
	}

	log.InfoWithCtx(ctx, "PublishAttitudeEvent event(%+v) finished", ev)
	return nil
}

func (p *eventlinkPublisher) PublishOfficialHandlePostEvent(ctx context.Context, ev *ugc_community_event.OfficialHandlePostEvent) error {
	bin, err := proto.Marshal(ev)
	if err != nil {
		return err
	}

	msg := &publisher.ProducerMessage{
		Topic: "ugc_community_official_handle_post",
		Key:   publisher.StringEncoder(ev.GetPostInfo()[0].GetPostId()),
		Value: publisher.ByteEncoder(bin),
	}

	if result := p.Publish(ctx, msg); result.Err != nil {
		return result.Err
	}

	log.InfoWithCtx(ctx, "PublishOfficialHandlePostEvent event(%+v) finished", ev)
	return nil
}

func (p *eventlinkPublisher) PublishPostEvent(ctx context.Context, ev ugc_community_event.PostEvent_Event, posts ...*ugc_community_event.PostEvent_Post) error {
	e := &ugc_community_event.PostEvent{
		Event: ev,

		Uid:         metainfo.GetServiceInfo(ctx).UserID(),
		TriggeredAt: time.Now().Unix(),

		Posts: posts,
	}
	bin, err := proto.Marshal(e)
	if err != nil {
		return err
	}

	msg := &publisher.ProducerMessage{
		Topic: "ugc_community_post_event",
		Key:   publisher.StringEncoder(strconv.Itoa(int(e.GetUid()))),
		Value: publisher.ByteEncoder(bin),
	}

	if result := p.Publish(ctx, msg); result.Err != nil {
		return result.Err
	}

	log.InfoWithCtx(ctx, "PublishPostEvent event(%+v) finished", e)
	return nil
}

func (p *eventlinkPublisher) PublishCommentEvent(ctx context.Context, ev *ugc_community_event.CommentEvent) error {
	bin, err := proto.Marshal(ev)
	if err != nil {
		return err
	}
	msg := &publisher.ProducerMessage{
		Topic: "ugc_community_comment_event",
		Key:   publisher.StringEncoder(strconv.Itoa(int(ev.GetUserId()))),
		Value: publisher.ByteEncoder(bin),
	}
	if result := p.Publish(ctx, msg); result.Err != nil {
		log.ErrorWithCtx(ctx, "PublishCommentEvent err: %v, ev:%v", result.Err, ev)
		return result.Err
	}
	log.InfoWithCtx(ctx, "PublishCommentEvent event(%+v) finished", ev)
	return nil
}

func (p *eventlinkPublisher) PublishTopicEvent(ctx context.Context, action ugc_community_event.TopicEvent_Action, topics ...*ugc_community_event.TopicEvent_Topic) error {
	ev := &ugc_community_event.TopicEvent{
		Action:      action,
		Topics:      topics,
		TriggeredAt: time.Now().Unix(),
	}
	bin, err := proto.Marshal(ev)
	if err != nil {
		log.ErrorWithCtx(ctx, "PublishTopicEvent Marshal ev(%+v) err: %v", ev, err)
		return err
	}

	msg := &publisher.ProducerMessage{
		Topic: "ugc_community_topic_event",
		Key:   publisher.StringEncoder(strconv.Itoa(int(metainfo.GetServiceInfo(ctx).UserID()))),
		Value: publisher.ByteEncoder(bin),
	}
	if result := p.Publish(ctx, msg); result.Err != nil {
		log.ErrorWithCtx(ctx, "PublishTopicEvent event(%+v) err: %v", ev, err)
		return result.Err
	}

	log.InfoWithCtx(ctx, "PublishTopicEvent event(%+v) finished", ev)
	return nil
}

func (p *eventlinkPublisher) PublishSubjectEvent(ctx context.Context, ev *ugc_community_event.SubjectEvent) error {
	bin, err := proto.Marshal(ev)
	if err != nil {
		log.ErrorWithCtx(ctx, "PublishSubjectEvent Marshal ev(%+v) err: %v", ev, err)
		return err
	}

	msg := &publisher.ProducerMessage{
		Topic: "ugc_community_subject_event",
		Value: publisher.ByteEncoder(bin),
	}
	if result := p.Publish(ctx, msg); result.Err != nil {
		log.ErrorWithCtx(ctx, "PublishSubjectEvent event(%+v) err: %v", ev, err)
		return result.Err
	}

	log.InfoWithCtx(ctx, "PublishSubjectEvent event(%+v) finished", ev)
	return nil
}
