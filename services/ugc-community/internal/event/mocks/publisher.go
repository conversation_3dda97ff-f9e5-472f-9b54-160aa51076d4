// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/ugc-community/internal/event (interfaces: EventPublisher)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	sarama "github.com/IBM/sarama"
	gomock "github.com/golang/mock/gomock"
	publisher "gitlab.ttyuyin.com/tt-infra/middleware/kafka/publisher"
	event "golang.52tt.com/protocol/services/ugc-community/event"
)

// MockEventPublisher is a mock of EventPublisher interface.
type MockEventPublisher struct {
	ctrl     *gomock.Controller
	recorder *MockEventPublisherMockRecorder
}

// MockEventPublisherMockRecorder is the mock recorder for MockEventPublisher.
type MockEventPublisherMockRecorder struct {
	mock *MockEventPublisher
}

// NewMockEventPublisher creates a new mock instance.
func NewMockEventPublisher(ctrl *gomock.Controller) *MockEventPublisher {
	mock := &MockEventPublisher{ctrl: ctrl}
	mock.recorder = &MockEventPublisherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEventPublisher) EXPECT() *MockEventPublisherMockRecorder {
	return m.recorder
}

// Publish mocks base method.
func (m *MockEventPublisher) Publish(arg0 context.Context, arg1 *sarama.ProducerMessage) publisher.PublishResult {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Publish", arg0, arg1)
	ret0, _ := ret[0].(publisher.PublishResult)
	return ret0
}

// Publish indicates an expected call of Publish.
func (mr *MockEventPublisherMockRecorder) Publish(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Publish", reflect.TypeOf((*MockEventPublisher)(nil).Publish), arg0, arg1)
}

// PublishAttitudeEvent mocks base method.
func (m *MockEventPublisher) PublishAttitudeEvent(arg0 context.Context, arg1 *event.AttitudeEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishAttitudeEvent", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishAttitudeEvent indicates an expected call of PublishAttitudeEvent.
func (mr *MockEventPublisherMockRecorder) PublishAttitudeEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishAttitudeEvent", reflect.TypeOf((*MockEventPublisher)(nil).PublishAttitudeEvent), arg0, arg1)
}

// PublishCommentEvent mocks base method.
func (m *MockEventPublisher) PublishCommentEvent(arg0 context.Context, arg1 *event.CommentEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishCommentEvent", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishCommentEvent indicates an expected call of PublishCommentEvent.
func (mr *MockEventPublisherMockRecorder) PublishCommentEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishCommentEvent", reflect.TypeOf((*MockEventPublisher)(nil).PublishCommentEvent), arg0, arg1)
}

// PublishOfficialHandlePostEvent mocks base method.
func (m *MockEventPublisher) PublishOfficialHandlePostEvent(arg0 context.Context, arg1 *event.OfficialHandlePostEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishOfficialHandlePostEvent", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishOfficialHandlePostEvent indicates an expected call of PublishOfficialHandlePostEvent.
func (mr *MockEventPublisherMockRecorder) PublishOfficialHandlePostEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishOfficialHandlePostEvent", reflect.TypeOf((*MockEventPublisher)(nil).PublishOfficialHandlePostEvent), arg0, arg1)
}

// PublishPostEvent mocks base method.
func (m *MockEventPublisher) PublishPostEvent(arg0 context.Context, arg1 event.PostEvent_Event, arg2 ...*event.PostEvent_Post) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PublishPostEvent", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishPostEvent indicates an expected call of PublishPostEvent.
func (mr *MockEventPublisherMockRecorder) PublishPostEvent(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishPostEvent", reflect.TypeOf((*MockEventPublisher)(nil).PublishPostEvent), varargs...)
}

// PublishSubjectEvent mocks base method.
func (m *MockEventPublisher) PublishSubjectEvent(arg0 context.Context, arg1 *event.SubjectEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishSubjectEvent", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishSubjectEvent indicates an expected call of PublishSubjectEvent.
func (mr *MockEventPublisherMockRecorder) PublishSubjectEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishSubjectEvent", reflect.TypeOf((*MockEventPublisher)(nil).PublishSubjectEvent), arg0, arg1)
}

// PublishTopicEvent mocks base method.
func (m *MockEventPublisher) PublishTopicEvent(arg0 context.Context, arg1 event.TopicEvent_Action, arg2 ...*event.TopicEvent_Topic) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PublishTopicEvent", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishTopicEvent indicates an expected call of PublishTopicEvent.
func (mr *MockEventPublisherMockRecorder) PublishTopicEvent(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishTopicEvent", reflect.TypeOf((*MockEventPublisher)(nil).PublishTopicEvent), varargs...)
}
