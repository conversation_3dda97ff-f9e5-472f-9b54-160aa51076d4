// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/ugc-community/internal/mgr/post (interfaces: Manager)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	ugc_community "golang.52tt.com/protocol/services/ugc-community"
	entity "golang.52tt.com/services/ugc-community/internal/entity"
)

// MockManager is a mock of Manager interface.
type MockManager struct {
	ctrl     *gomock.Controller
	recorder *MockManagerMockRecorder
}

// MockManagerMockRecorder is the mock recorder for MockManager.
type MockManagerMockRecorder struct {
	mock *MockManager
}

// NewMockManager creates a new mock instance.
func NewMockManager(ctrl *gomock.Controller) *MockManager {
	mock := &MockManager{ctrl: ctrl}
	mock.recorder = &MockManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockManager) EXPECT() *MockManagerMockRecorder {
	return m.recorder
}

// AddAigcBizPost mocks base method.
func (m *MockManager) AddAigcBizPost(arg0 context.Context, arg1 []*entity.AigcBizPost) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAigcBizPost", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddAigcBizPost indicates an expected call of AddAigcBizPost.
func (mr *MockManagerMockRecorder) AddAigcBizPost(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAigcBizPost", reflect.TypeOf((*MockManager)(nil).AddAigcBizPost), arg0, arg1)
}

// BatchDeletePost mocks base method.
func (m *MockManager) BatchDeletePost(arg0 context.Context, arg1 ugc_community.BatchDeletePostRequest_Source, arg2 entity.PostList) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeletePost", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDeletePost indicates an expected call of BatchDeletePost.
func (mr *MockManagerMockRecorder) BatchDeletePost(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeletePost", reflect.TypeOf((*MockManager)(nil).BatchDeletePost), arg0, arg1, arg2)
}

// CreatePost mocks base method.
func (m *MockManager) CreatePost(arg0 context.Context, arg1 *entity.Post) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePost", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreatePost indicates an expected call of CreatePost.
func (mr *MockManagerMockRecorder) CreatePost(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePost", reflect.TypeOf((*MockManager)(nil).CreatePost), arg0, arg1)
}

// DelAigcBizPost mocks base method.
func (m *MockManager) DelAigcBizPost(arg0 context.Context, arg1 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelAigcBizPost", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelAigcBizPost indicates an expected call of DelAigcBizPost.
func (mr *MockManagerMockRecorder) DelAigcBizPost(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelAigcBizPost", reflect.TypeOf((*MockManager)(nil).DelAigcBizPost), arg0, arg1)
}

// GetPost mocks base method.
func (m *MockManager) GetPost(arg0 context.Context, arg1 string) (*entity.Post, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPost", arg0, arg1)
	ret0, _ := ret[0].(*entity.Post)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPost indicates an expected call of GetPost.
func (mr *MockManagerMockRecorder) GetPost(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPost", reflect.TypeOf((*MockManager)(nil).GetPost), arg0, arg1)
}

// GetPostList mocks base method.
func (m *MockManager) GetPostList(arg0 context.Context, arg1 []string) (entity.PostList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPostList", arg0, arg1)
	ret0, _ := ret[0].(entity.PostList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostList indicates an expected call of GetPostList.
func (mr *MockManagerMockRecorder) GetPostList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostList", reflect.TypeOf((*MockManager)(nil).GetPostList), arg0, arg1)
}

// GetPostListInAuditTimeRange mocks base method.
func (m *MockManager) GetPostListInAuditTimeRange(arg0 context.Context, arg1 [2]int64, arg2 string, arg3 uint32) (entity.PostList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPostListInAuditTimeRange", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(entity.PostList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostListInAuditTimeRange indicates an expected call of GetPostListInAuditTimeRange.
func (mr *MockManagerMockRecorder) GetPostListInAuditTimeRange(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostListInAuditTimeRange", reflect.TypeOf((*MockManager)(nil).GetPostListInAuditTimeRange), arg0, arg1, arg2, arg3)
}

// IncrPostAttitude mocks base method.
func (m *MockManager) IncrPostAttitude(arg0 context.Context, arg1 string, arg2 int32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrPostAttitude", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrPostAttitude indicates an expected call of IncrPostAttitude.
func (mr *MockManagerMockRecorder) IncrPostAttitude(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrPostAttitude", reflect.TypeOf((*MockManager)(nil).IncrPostAttitude), arg0, arg1, arg2)
}

// IncrPostComment mocks base method.
func (m *MockManager) IncrPostComment(arg0 context.Context, arg1 string, arg2 int32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrPostComment", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrPostComment indicates an expected call of IncrPostComment.
func (mr *MockManagerMockRecorder) IncrPostComment(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrPostComment", reflect.TypeOf((*MockManager)(nil).IncrPostComment), arg0, arg1, arg2)
}

// OfficialHandlePost mocks base method.
func (m *MockManager) OfficialHandlePost(arg0 context.Context, arg1 []*ugc_community.OfficialHandlePostRequest_PostInfo, arg2 entity.PostList) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OfficialHandlePost", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// OfficialHandlePost indicates an expected call of OfficialHandlePost.
func (mr *MockManagerMockRecorder) OfficialHandlePost(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficialHandlePost", reflect.TypeOf((*MockManager)(nil).OfficialHandlePost), arg0, arg1, arg2)
}

// SearchAigcPost mocks base method.
func (m *MockManager) SearchAigcPost(arg0 context.Context, arg1 *ugc_community.SearchPostRequest) (entity.PostList, map[string]*entity.PostBizStateTable, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchAigcPost", arg0, arg1)
	ret0, _ := ret[0].(entity.PostList)
	ret1, _ := ret[1].(map[string]*entity.PostBizStateTable)
	ret2, _ := ret[2].(string)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// SearchAigcPost indicates an expected call of SearchAigcPost.
func (mr *MockManagerMockRecorder) SearchAigcPost(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchAigcPost", reflect.TypeOf((*MockManager)(nil).SearchAigcPost), arg0, arg1)
}

// SetHasAICommentStatus mocks base method.
func (m *MockManager) SetHasAICommentStatus(arg0 context.Context, arg1 string, arg2 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetHasAICommentStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetHasAICommentStatus indicates an expected call of SetHasAICommentStatus.
func (mr *MockManagerMockRecorder) SetHasAICommentStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetHasAICommentStatus", reflect.TypeOf((*MockManager)(nil).SetHasAICommentStatus), arg0, arg1, arg2)
}

// UpdatePostAuditResult mocks base method.
func (m *MockManager) UpdatePostAuditResult(arg0 context.Context, arg1 *entity.Post, arg2 ugc_community.AuditResult, arg3 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePostAuditResult", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePostAuditResult indicates an expected call of UpdatePostAuditResult.
func (mr *MockManagerMockRecorder) UpdatePostAuditResult(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePostAuditResult", reflect.TypeOf((*MockManager)(nil).UpdatePostAuditResult), arg0, arg1, arg2, arg3)
}
