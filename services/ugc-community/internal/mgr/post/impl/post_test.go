package impl

import (
	"context"
	"encoding/json"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"
	"gitlab.ttyuyin.com/tyr/x/metadata/serviceinfo"
	"go.mongodb.org/mongo-driver/bson/primitive"
	pb "golang.52tt.com/protocol/services/ugc-community"
	ugc_community_event "golang.52tt.com/protocol/services/ugc-community/event"
	"golang.52tt.com/services/ugc-community/internal/entity"
	"golang.52tt.com/services/ugc-community/internal/event"
	event_mocks "golang.52tt.com/services/ugc-community/internal/event/mocks"
	"golang.52tt.com/services/ugc-community/internal/mgr/post/store"
	store_mocks "golang.52tt.com/services/ugc-community/internal/mgr/post/store/mocks"
)

func Test_manager_CreatePost(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		postStore      = store_mocks.NewMockPostStore(ctrl)
		eventPublisher = event_mocks.NewMockEventPublisher(ctrl)
	)

	now := time.Now()
	info := &entity.Post{
		ID:        primitive.NewObjectID(),
		CreatedAt: now,
		UpdatedAt: now,
		Uid:       1,
		Type:      pb.PostType_POST_TYPE_TEXT,
		State:     pb.PostState_POST_STATE_PUBLIC,
		Origin:    pb.PostOrigin_POST_ORIGIN_AIGC_COMMUNITY_USER,
		Content:   "content",
		BizData: entity.PostBizData{
			Type:   pb.PostBizType_POST_BIZ_TYPE_AIGC_COMMUNITY,
			Format: pb.PostBizData_FORMAT_PROTOBUF,
		},
		AuditAt:     now,
		AuditResult: pb.AuditResult_AUDIT_RESULT_PASS,
	}

	type fields struct {
		postStore      store.PostStore
		eventPublisher event.EventPublisher
	}
	type args struct {
		ctx  context.Context
		info *entity.Post
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		mock    func()
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				postStore:      postStore,
				eventPublisher: eventPublisher,
			},
			args: args{
				ctx: metainfo.SetServiceInfo(context.Background(), func(writer serviceinfo.ServiceInfoWriter) {
					writer.SetUserID(1)
				}),
				info: info,
			},
			mock: func() {
				postStore.EXPECT().Add(gomock.Any(), info).Return(nil)
				eventPublisher.EXPECT().PublishPostEvent(gomock.Any(), ugc_community_event.PostEvent_EVENT_PUBLISHED, info.Event(false)).Return(nil)
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &manager{
				postStore:      tt.fields.postStore,
				eventPublisher: tt.fields.eventPublisher,
			}
			tt.mock()
			if err := mgr.CreatePost(tt.args.ctx, tt.args.info); (err != nil) != tt.wantErr {
				t.Errorf("manager.CreatePost() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_manager_BatchDeletePost(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		psotStore      = store_mocks.NewMockPostStore(ctrl)
		eventPublisher = event_mocks.NewMockEventPublisher(ctrl)
	)

	now := time.Now()
	posts := entity.PostList{
		{
			ID:        primitive.NewObjectID(),
			CreatedAt: now,
			UpdatedAt: now,
			Uid:       1,
			Type:      pb.PostType_POST_TYPE_TEXT,
			State:     pb.PostState_POST_STATE_PUBLIC,
			Origin:    pb.PostOrigin_POST_ORIGIN_AIGC_COMMUNITY_USER,
			Content:   "content",
			BizData: entity.PostBizData{
				Type:   pb.PostBizType_POST_BIZ_TYPE_AIGC_COMMUNITY,
				Format: pb.PostBizData_FORMAT_PROTOBUF,
			},
			AuditAt:     now,
			AuditResult: pb.AuditResult_AUDIT_RESULT_PASS,
		},
	}

	idList := make([]primitive.ObjectID, 0, len(posts))
	for _, info := range posts {
		idList = append(idList, info.ID)
	}

	type fields struct {
		postStore      store.PostStore
		eventPublisher event.EventPublisher
	}
	type args struct {
		ctx    context.Context
		source pb.BatchDeletePostRequest_Source
		posts  entity.PostList
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		mock    func()
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				postStore:      psotStore,
				eventPublisher: eventPublisher,
			},
			args: args{
				ctx: metainfo.SetServiceInfo(context.Background(), func(s serviceinfo.ServiceInfoWriter) {
					s.SetUserID(1)
				}),
				source: pb.BatchDeletePostRequest_SOURCE_USER,
				posts:  posts,
			},
			mock: func() {
				psotStore.EXPECT().Delete(gomock.Any(), idList).Return(nil)
				eventPublisher.EXPECT().
					PublishPostEvent(
						gomock.Any(),
						ugc_community_event.PostEvent_EVENT_DELETED,
						posts.Event(),
					).
					Return(nil)
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &manager{
				postStore:      tt.fields.postStore,
				eventPublisher: tt.fields.eventPublisher,
			}
			tt.mock()
			if err := mgr.BatchDeletePost(tt.args.ctx, tt.args.source, tt.args.posts); (err != nil) != tt.wantErr {
				t.Errorf("manager.BatchDeletePost() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_manager_GetPost(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		postStore = store_mocks.NewMockPostStore(ctrl)
	)

	now := time.Now()
	info := &entity.Post{
		ID:        primitive.NewObjectID(),
		CreatedAt: now,
		UpdatedAt: now,
		Uid:       1,
		Type:      pb.PostType_POST_TYPE_TEXT,
		State:     pb.PostState_POST_STATE_PUBLIC,
		Origin:    pb.PostOrigin_POST_ORIGIN_AIGC_COMMUNITY_USER,
		Content:   "content",
		BizData: entity.PostBizData{
			Type:   pb.PostBizType_POST_BIZ_TYPE_AIGC_COMMUNITY,
			Format: pb.PostBizData_FORMAT_PROTOBUF,
		},
		AuditAt:       now,
		AuditResult:   pb.AuditResult_AUDIT_RESULT_PASS,
		CommentCount:  10,
		AttitudeCount: 10,
	}

	type fields struct {
		postStore store.PostStore
	}
	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		mock    func()
		want    *entity.Post
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				postStore: postStore,
			},
			args: args{
				ctx: context.Background(),
				id:  info.ID.Hex(),
			},
			mock: func() {
				postStore.EXPECT().Get(gomock.Any(), info.ID).Return(info, nil)
			},
			want:    info,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &manager{
				postStore: tt.fields.postStore,
			}
			tt.mock()
			got, err := mgr.GetPost(tt.args.ctx, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("manager.GetPost() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("manager.GetPost() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_manager_GetPostList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		postStore = store_mocks.NewMockPostStore(ctrl)
	)

	now := time.Now()
	list := entity.PostList{
		{
			ID:        primitive.NewObjectID(),
			CreatedAt: now,
			UpdatedAt: now,
			Uid:       1,
			Type:      pb.PostType_POST_TYPE_TEXT,
			State:     pb.PostState_POST_STATE_PUBLIC,
			Origin:    pb.PostOrigin_POST_ORIGIN_AIGC_COMMUNITY_USER,
			Content:   "content 111",
			BizData: entity.PostBizData{
				Type:   pb.PostBizType_POST_BIZ_TYPE_AIGC_COMMUNITY,
				Format: pb.PostBizData_FORMAT_PROTOBUF,
			},
			AuditAt:       now,
			AuditResult:   pb.AuditResult_AUDIT_RESULT_PASS,
			CommentCount:  10,
			AttitudeCount: 10,
		},
		{
			ID:        primitive.NewObjectID(),
			CreatedAt: now,
			UpdatedAt: now,
			Uid:       1,
			Type:      pb.PostType_POST_TYPE_TEXT,
			State:     pb.PostState_POST_STATE_PUBLIC,
			Origin:    pb.PostOrigin_POST_ORIGIN_AIGC_COMMUNITY_USER,
			Content:   "content 222",
			BizData: entity.PostBizData{
				Type:   pb.PostBizType_POST_BIZ_TYPE_AIGC_COMMUNITY,
				Format: pb.PostBizData_FORMAT_PROTOBUF,
			},
			AuditAt:       now,
			AuditResult:   pb.AuditResult_AUDIT_RESULT_PASS,
			CommentCount:  10,
			AttitudeCount: 10,
		},
	}

	idList := make([]string, 0, len(list))
	objectIdList := make([]primitive.ObjectID, 0, len(list))
	for _, info := range list {
		idList = append(idList, info.ID.Hex())
		objectIdList = append(objectIdList, info.ID)
	}

	type fields struct {
		postStore store.PostStore
	}
	type args struct {
		ctx    context.Context
		idList []string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		mock    func()
		want    entity.PostList
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				postStore: postStore,
			},
			args: args{
				ctx:    context.Background(),
				idList: idList,
			},
			mock: func() {
				postStore.EXPECT().Find(gomock.Any(), objectIdList).Return(list, nil)
			},
			want:    list,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &manager{
				postStore: tt.fields.postStore,
			}
			tt.mock()
			got, err := mgr.GetPostList(tt.args.ctx, tt.args.idList)
			if (err != nil) != tt.wantErr {
				t.Errorf("manager.GetPostList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("manager.GetPostList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_manager_GetPostListInAuditTimeRange(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		postStore = store_mocks.NewMockPostStore(ctrl)
	)

	now := time.Now()
	list := entity.PostList{
		{
			ID:        primitive.NewObjectID(),
			CreatedAt: now,
			UpdatedAt: now,
			Uid:       1,
			Type:      pb.PostType_POST_TYPE_TEXT,
			State:     pb.PostState_POST_STATE_PUBLIC,
			Origin:    pb.PostOrigin_POST_ORIGIN_AIGC_COMMUNITY_USER,
			Content:   "content 111",
			BizData: entity.PostBizData{
				Type:   pb.PostBizType_POST_BIZ_TYPE_AIGC_COMMUNITY,
				Format: pb.PostBizData_FORMAT_PROTOBUF,
			},
			AuditAt:       now,
			AuditResult:   pb.AuditResult_AUDIT_RESULT_PASS,
			CommentCount:  10,
			AttitudeCount: 10,
		},
		{
			ID:        primitive.NewObjectID(),
			CreatedAt: now,
			UpdatedAt: now,
			Uid:       1,
			Type:      pb.PostType_POST_TYPE_TEXT,
			State:     pb.PostState_POST_STATE_PUBLIC,
			Origin:    pb.PostOrigin_POST_ORIGIN_AIGC_COMMUNITY_USER,
			Content:   "content 222",
			BizData: entity.PostBizData{
				Type:   pb.PostBizType_POST_BIZ_TYPE_AIGC_COMMUNITY,
				Format: pb.PostBizData_FORMAT_PROTOBUF,
			},
			AuditAt:       now,
			AuditResult:   pb.AuditResult_AUDIT_RESULT_PASS,
			CommentCount:  10,
			AttitudeCount: 10,
		},
	}

	type fields struct {
		postStore store.PostStore
	}
	type args struct {
		ctx            context.Context
		auditTimeRange [2]int64
		id             string
		limit          uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		mock    func()
		want    entity.PostList
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				postStore: postStore,
			},
			args: args{
				ctx:            context.Background(),
				auditTimeRange: [2]int64{now.Add(-10 * time.Second).Unix(), now.Unix()},
				id:             "",
				limit:          100,
			},
			mock: func() {
				postStore.EXPECT().FindInAuditAtRange(gomock.Any(), gomock.Any(), primitive.NilObjectID, uint32(100)).Return(list, nil)
			},
			want:    list,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &manager{
				postStore: tt.fields.postStore,
			}
			tt.mock()
			got, err := mgr.GetPostListInAuditTimeRange(tt.args.ctx, tt.args.auditTimeRange, tt.args.id, tt.args.limit)
			if (err != nil) != tt.wantErr {
				t.Errorf("manager.GetPostListInAuditTimeRange() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("manager.GetPostListInAuditTimeRange() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_manager_IncrPostComment(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		postStore = store_mocks.NewMockPostStore(ctrl)
	)

	id := primitive.NewObjectID()

	type fields struct {
		postStore store.PostStore
	}
	type args struct {
		ctx context.Context
		id  string
		inc int32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		mock    func()
		want    uint32
		wantErr bool
	}{
		{
			name: "incr",
			fields: fields{
				postStore: postStore,
			},
			args: args{
				ctx: context.Background(),
				id:  id.Hex(),
				inc: 1,
			},
			mock: func() {
				postStore.EXPECT().IncrCommentCount(gomock.Any(), id, uint32(1)).Return(uint32(1), nil)
			},
			want:    1,
			wantErr: false,
		},
		{
			name: "decr",
			fields: fields{
				postStore: postStore,
			},
			args: args{
				ctx: context.Background(),
				id:  id.Hex(),
				inc: -1,
			},
			mock: func() {
				postStore.EXPECT().DecrCommentCount(gomock.Any(), id, uint32(1)).Return(uint32(0), nil)
			},
			want:    0,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &manager{
				postStore: tt.fields.postStore,
			}
			tt.mock()
			got, err := mgr.IncrPostComment(tt.args.ctx, tt.args.id, tt.args.inc)
			if (err != nil) != tt.wantErr {
				t.Errorf("manager.IncrPostComment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("manager.IncrPostComment() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_manager_IncrPostAttitude(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		postStore = store_mocks.NewMockPostStore(ctrl)
	)

	id := primitive.NewObjectID()

	type fields struct {
		postStore store.PostStore
	}
	type args struct {
		ctx context.Context
		id  string
		inc int32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		mock    func()
		want    uint32
		wantErr bool
	}{
		{
			name: "incr",
			fields: fields{
				postStore: postStore,
			},
			args: args{
				ctx: context.Background(),
				id:  id.Hex(),
				inc: 1,
			},
			mock: func() {
				postStore.EXPECT().IncrAttitudeCount(gomock.Any(), id, uint32(1)).Return(uint32(1), nil)
			},
			want:    1,
			wantErr: false,
		},
		{
			name: "decr",
			fields: fields{
				postStore: postStore,
			},
			args: args{
				ctx: context.Background(),
				id:  id.Hex(),
				inc: -1,
			},
			mock: func() {
				postStore.EXPECT().DecrAttitudeCount(gomock.Any(), id, uint32(1)).Return(uint32(0), nil)
			},
			want:    0,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &manager{
				postStore: tt.fields.postStore,
			}
			tt.mock()
			got, err := mgr.IncrPostAttitude(tt.args.ctx, tt.args.id, tt.args.inc)
			if (err != nil) != tt.wantErr {
				t.Errorf("manager.IncrPostAttitude() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("manager.IncrPostAttitude() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_manager_UpdatePostAuditResult(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		postStore      = store_mocks.NewMockPostStore(ctrl)
		eventPublisher = event_mocks.NewMockEventPublisher(ctrl)
	)

	now := time.Now()

	info := &entity.Post{
		ID:        primitive.NewObjectID(),
		CreatedAt: now,
		UpdatedAt: now,
		Uid:       1,
		Type:      pb.PostType_POST_TYPE_TEXT,
		State:     pb.PostState_POST_STATE_PUBLIC,
		Origin:    pb.PostOrigin_POST_ORIGIN_AIGC_COMMUNITY_USER,
		Content:   "content",
		BizData: entity.PostBizData{
			Type:   pb.PostBizType_POST_BIZ_TYPE_AIGC_COMMUNITY,
			Format: pb.PostBizData_FORMAT_PROTOBUF,
		},
		AuditAt:     now,
		AuditResult: pb.AuditResult_AUDIT_RESULT_REVIEW,
	}

	type fields struct {
		postStore      store.PostStore
		eventPublisher event.EventPublisher
	}
	type args struct {
		ctx    context.Context
		info   *entity.Post
		result pb.AuditResult
	}
	tests := []struct {
		name    string
		fields  fields
		mock    func()
		args    args
		wantErr bool
	}{
		{
			name: "pass",
			fields: fields{
				postStore:      postStore,
				eventPublisher: eventPublisher,
			},
			mock: func() {
				post := *info
				_ = post.UpdateAuditResult(pb.AuditResult_AUDIT_RESULT_PASS)

				postStore.EXPECT().UpdateAuditResult(gomock.Any(), info.ID, pb.AuditResult_AUDIT_RESULT_PASS).Return(nil)
				eventPublisher.EXPECT().PublishPostEvent(gomock.Any(), ugc_community_event.PostEvent_EVENT_PUBLISHED, post.Event(false)).Return(nil)
			},
			args: args{
				ctx: metainfo.SetServiceInfo(context.Background(), func(s serviceinfo.ServiceInfoWriter) {
					s.SetUserID(1)
				}),
				info:   info,
				result: pb.AuditResult_AUDIT_RESULT_PASS,
			},
			wantErr: false,
		},
		{
			name: "reject",
			fields: fields{
				postStore:      postStore,
				eventPublisher: eventPublisher,
			},
			mock: func() {
				post := *info
				_ = post.UpdateAuditResult(pb.AuditResult_AUDIT_RESULT_REJECT)

				postStore.EXPECT().UpdateAuditResult(gomock.Any(), info.ID, pb.AuditResult_AUDIT_RESULT_REJECT).Return(nil)
				eventPublisher.EXPECT().PublishPostEvent(gomock.Any(), ugc_community_event.PostEvent_EVENT_BAN, post.Event(false)).Return(nil)
			},
			args: args{
				ctx: metainfo.SetServiceInfo(context.Background(), func(s serviceinfo.ServiceInfoWriter) {
					s.SetUserID(1)
				}),
				info:   info,
				result: pb.AuditResult_AUDIT_RESULT_REJECT,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &manager{
				postStore:      tt.fields.postStore,
				eventPublisher: tt.fields.eventPublisher,
			}
			tt.mock()
			if err := mgr.UpdatePostAuditResult(tt.args.ctx, tt.args.info, tt.args.result, false); (err != nil) != tt.wantErr {
				t.Errorf("manager.UpdatePostAuditResult() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_managers(t *testing.T) {
	bizData := &pb.AigcCommunityPost{
		RoleId: 0,
		ChatRecords: []*pb.AigcCommunityPost_ChatRecord{
			{
				Sender:  1,
				Content: "1",
			},
			{
				Sender:  2,
				Content: "2",
			},
		},
	}

	b, err := json.Marshal(bizData.ChatRecords)
	if err != nil {
		t.Error(err)
	}
	t.Log(string(b))
}
