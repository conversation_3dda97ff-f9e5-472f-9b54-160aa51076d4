package ttconfig

import (
	"gitlab.ttyuyin.com/avengers/tyr/core/config/ttconfig"
	"golang.52tt.com/pkg/log"
	"sync/atomic"
)

type UgcCommunityConfig struct {
	// 热评阈值
	HotCommentThreshold int `json:"hot_comment_threshold"`
}

// Format
// 配置加载时调用Format报错则返回错误
// 配置更新时调用Format报错则放弃此次更新
func (cfg *UgcCommunityConfig) Format() error {
	return nil
}

var (
	atomicUgcCommunityConfig *atomic.Value
)

func init() {
	//if err := InitUgcCommunityConfig(); err != nil {
	//    panic(err)
	//}
}

// InitUgcCommunityConfig 可以选择外部初始化或者直接init函数初始化
func InitUgcCommunityConfig() error {
	cfg := &UgcCommunityConfig{}
	atomCfg, err := ttconfig.AtomLoad("ugc-community", cfg)
	if nil != err {
		return err
	}
	atomicUgcCommunityConfig = atomCfg
	return nil
}

var defaultUgcCommunityConfig = &UgcCommunityConfig{}

func GetUgcCommunityConfig() *UgcCommunityConfig {
	if atomicUgcCommunityConfig == nil {
		log.Errorf("GetUgcCommunityConfig is nil")
		return defaultUgcCommunityConfig
	}
	return atomicUgcCommunityConfig.Load().(*UgcCommunityConfig)
}

func (cfg *UgcCommunityConfig) GetHotCommentThreshold() int {
	if cfg == nil || cfg.HotCommentThreshold == 0 {
		return 30
	}
	return cfg.HotCommentThreshold
}
