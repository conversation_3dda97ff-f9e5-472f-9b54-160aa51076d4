// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/game-hall/internal/mgr/msg/cache (interfaces: IMsgRedisDao)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	redis "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	cache "golang.52tt.com/services/game-hall/internal/mgr/msg/cache"
)

// MockIMsgRedisDao is a mock of IMsgRedisDao interface.
type MockIMsgRedisDao struct {
	ctrl     *gomock.Controller
	recorder *MockIMsgRedisDaoMockRecorder
}

// MockIMsgRedisDaoMockRecorder is the mock recorder for MockIMsgRedisDao.
type MockIMsgRedisDaoMockRecorder struct {
	mock *MockIMsgRedisDao
}

// NewMockIMsgRedisDao creates a new mock instance.
func NewMockIMsgRedisDao(ctrl *gomock.Controller) *MockIMsgRedisDao {
	mock := &MockIMsgRedisDao{ctrl: ctrl}
	mock.recorder = &MockIMsgRedisDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIMsgRedisDao) EXPECT() *MockIMsgRedisDaoMockRecorder {
	return m.recorder
}

// AddCancelMsgRecord mocks base method.
func (m *MockIMsgRedisDao) AddCancelMsgRecord(arg0 context.Context, arg1 []*cache.CancelMsgRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCancelMsgRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddCancelMsgRecord indicates an expected call of AddCancelMsgRecord.
func (mr *MockIMsgRedisDaoMockRecorder) AddCancelMsgRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCancelMsgRecord", reflect.TypeOf((*MockIMsgRedisDao)(nil).AddCancelMsgRecord), arg0, arg1)
}

// AddCleanRecord mocks base method.
func (m *MockIMsgRedisDao) AddCleanRecord(arg0 context.Context, arg1 []uint32, arg2 map[uint32]uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCleanRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddCleanRecord indicates an expected call of AddCleanRecord.
func (mr *MockIMsgRedisDaoMockRecorder) AddCleanRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCleanRecord", reflect.TypeOf((*MockIMsgRedisDao)(nil).AddCleanRecord), arg0, arg1, arg2)
}

// AddMsgRecord mocks base method.
func (m *MockIMsgRedisDao) AddMsgRecord(arg0 context.Context, arg1 *cache.MsgRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddMsgRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddMsgRecord indicates an expected call of AddMsgRecord.
func (mr *MockIMsgRedisDaoMockRecorder) AddMsgRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMsgRecord", reflect.TypeOf((*MockIMsgRedisDao)(nil).AddMsgRecord), arg0, arg1)
}

// AddTempMsg mocks base method.
func (m *MockIMsgRedisDao) AddTempMsg(arg0 context.Context, arg1 *cache.TempMsgRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddTempMsg", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddTempMsg indicates an expected call of AddTempMsg.
func (mr *MockIMsgRedisDaoMockRecorder) AddTempMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddTempMsg", reflect.TypeOf((*MockIMsgRedisDao)(nil).AddTempMsg), arg0, arg1)
}

// BatchGetBanUserRecords mocks base method.
func (m *MockIMsgRedisDao) BatchGetBanUserRecords(arg0 context.Context, arg1 []uint32) (map[uint32]*cache.BanUserRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetBanUserRecords", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*cache.BanUserRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetBanUserRecords indicates an expected call of BatchGetBanUserRecords.
func (mr *MockIMsgRedisDaoMockRecorder) BatchGetBanUserRecords(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBanUserRecords", reflect.TypeOf((*MockIMsgRedisDao)(nil).BatchGetBanUserRecords), arg0, arg1)
}

// BatchGetCancelRecord mocks base method.
func (m *MockIMsgRedisDao) BatchGetCancelRecord(arg0 context.Context, arg1, arg2 []uint32) ([]*cache.CancelMsgRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetCancelRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*cache.CancelMsgRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetCancelRecord indicates an expected call of BatchGetCancelRecord.
func (mr *MockIMsgRedisDaoMockRecorder) BatchGetCancelRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetCancelRecord", reflect.TypeOf((*MockIMsgRedisDao)(nil).BatchGetCancelRecord), arg0, arg1, arg2)
}

// BatchGetCleanRecord mocks base method.
func (m *MockIMsgRedisDao) BatchGetCleanRecord(arg0 context.Context, arg1 []uint32) (map[uint32]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetCleanRecord", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetCleanRecord indicates an expected call of BatchGetCleanRecord.
func (mr *MockIMsgRedisDaoMockRecorder) BatchGetCleanRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetCleanRecord", reflect.TypeOf((*MockIMsgRedisDao)(nil).BatchGetCleanRecord), arg0, arg1)
}

// CleanExpiredMsgRecord mocks base method.
func (m *MockIMsgRedisDao) CleanExpiredMsgRecord(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CleanExpiredMsgRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CleanExpiredMsgRecord indicates an expected call of CleanExpiredMsgRecord.
func (mr *MockIMsgRedisDaoMockRecorder) CleanExpiredMsgRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CleanExpiredMsgRecord", reflect.TypeOf((*MockIMsgRedisDao)(nil).CleanExpiredMsgRecord), arg0, arg1)
}

// DelMsgRecord mocks base method.
func (m *MockIMsgRedisDao) DelMsgRecord(arg0 context.Context, arg1 uint32, arg2 uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelMsgRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelMsgRecord indicates an expected call of DelMsgRecord.
func (mr *MockIMsgRedisDaoMockRecorder) DelMsgRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelMsgRecord", reflect.TypeOf((*MockIMsgRedisDao)(nil).DelMsgRecord), arg0, arg1, arg2)
}

// DelTempMsg mocks base method.
func (m *MockIMsgRedisDao) DelTempMsg(arg0 context.Context, arg1 uint32, arg2 uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelTempMsg", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelTempMsg indicates an expected call of DelTempMsg.
func (mr *MockIMsgRedisDaoMockRecorder) DelTempMsg(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelTempMsg", reflect.TypeOf((*MockIMsgRedisDao)(nil).DelTempMsg), arg0, arg1, arg2)
}

// GetCmder mocks base method.
func (m *MockIMsgRedisDao) GetCmder() redis.StrategyClient {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCmder")
	ret0, _ := ret[0].(redis.StrategyClient)
	return ret0
}

// GetCmder indicates an expected call of GetCmder.
func (mr *MockIMsgRedisDaoMockRecorder) GetCmder() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCmder", reflect.TypeOf((*MockIMsgRedisDao)(nil).GetCmder))
}

// GetLastMsgRecordByTime mocks base method.
func (m *MockIMsgRedisDao) GetLastMsgRecordByTime(arg0 context.Context, arg1 []uint32, arg2 int64) (map[uint32]*cache.MsgRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastMsgRecordByTime", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]*cache.MsgRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastMsgRecordByTime indicates an expected call of GetLastMsgRecordByTime.
func (mr *MockIMsgRedisDaoMockRecorder) GetLastMsgRecordByTime(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastMsgRecordByTime", reflect.TypeOf((*MockIMsgRedisDao)(nil).GetLastMsgRecordByTime), arg0, arg1, arg2)
}

// GetTempMsg mocks base method.
func (m *MockIMsgRedisDao) GetTempMsg(arg0 context.Context, arg1 uint32, arg2 uint64) (*cache.TempMsgRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTempMsg", arg0, arg1, arg2)
	ret0, _ := ret[0].(*cache.TempMsgRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTempMsg indicates an expected call of GetTempMsg.
func (mr *MockIMsgRedisDaoMockRecorder) GetTempMsg(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTempMsg", reflect.TypeOf((*MockIMsgRedisDao)(nil).GetTempMsg), arg0, arg1, arg2)
}

// SetBanUserRecord mocks base method.
func (m *MockIMsgRedisDao) SetBanUserRecord(arg0 context.Context, arg1 uint32, arg2 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetBanUserRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetBanUserRecord indicates an expected call of SetBanUserRecord.
func (mr *MockIMsgRedisDaoMockRecorder) SetBanUserRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBanUserRecord", reflect.TypeOf((*MockIMsgRedisDao)(nil).SetBanUserRecord), arg0, arg1, arg2)
}
