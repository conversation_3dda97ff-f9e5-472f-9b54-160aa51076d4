package pin_entity

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	game_hall "golang.52tt.com/protocol/services/game-hall"
	"time"
)

const (
	SourceAdd    = 1
	SourceUpdate = 2
)

type GameHallPinConf struct {
	ID             primitive.ObjectID `bson:"_id"`
	IsSelectAllTab bool               `bson:"is_select_all_tab"` // 是否选择全部Tab
	TabIds         []uint32           `bson:"tab_ids"`
	TTID           string             `bson:"ttid"`
	Uid            uint32             `bson:"uid"`
	Content        string             `bson:"content"`        // 文本内容
	HighlightText  []HighlightText    `bson:"highlight_text"` // 高亮文本内容
	Img            string             `bson:"img"`
	StartTime      int64              `bson:"start_time"`  // 开始时间, ms
	EndTime        int64              `bson:"end_time"`    // 结束时间, ms
	UpdateTime     int64              `bson:"update_time"` // 更新时间
	CreateTime     time.Time          `bson:"create_time"` // 创建时间
}

type HighlightText struct {
	Text string `bson:"text"` // 高亮内容
	Url  string `bson:"url"`  // 链接地址
}

func ConvertDaoToPb(data *GameHallPinConf) *game_hall.GameHallPinConfItem {
	now := time.Now().UnixMilli()
	status := game_hall.GameHallPinConfStatus_STATUS_UNSPECIFIED
	if data.StartTime > now {
		status = game_hall.GameHallPinConfStatus_STATUS_NOT_ACTIVE
	} else if data.StartTime <= now && data.EndTime >= now {
		status = game_hall.GameHallPinConfStatus_STATUS_ACTIVE
	} else {
		status = game_hall.GameHallPinConfStatus_STATUS_EXPIRED
	}

	highlightTexts := make([]*game_hall.GameHallPinConfItem_HighlightText, 0, len(data.HighlightText))
	for _, c := range data.HighlightText {
		highlightTexts = append(highlightTexts, &game_hall.GameHallPinConfItem_HighlightText{
			Highlight: c.Text,
			Url:       c.Url,
		})
	}

	return &game_hall.GameHallPinConfItem{
		Id:             data.ID.Hex(),
		IsSelectAllTab: data.IsSelectAllTab,
		TabIds:         data.TabIds,
		Ttid:           data.TTID,
		Uid:            data.Uid,
		Content:        data.Content,
		Text:           highlightTexts,
		Img:            data.Img,
		Status:         status,
		StartTime:      data.StartTime,
		EndTime:        data.EndTime,
		UpdateTime:     data.UpdateTime,
	}
}

func ConvertPbToDao(data *game_hall.GameHallPinConfItem, source int) (*GameHallPinConf, error) {
	var _id primitive.ObjectID
	if source == SourceAdd {
		_id = primitive.NewObjectID()

	} else {
		var err error
		_id, err = primitive.ObjectIDFromHex(data.GetId())
		if err != nil {
			return nil, err
		}
	}

	highlightTexts := make([]HighlightText, 0, len(data.GetText()))
	for _, c := range data.GetText() {
		highlightTexts = append(highlightTexts, HighlightText{
			Text: c.GetHighlight(),
			Url:  c.GetUrl(),
		})
	}

	now := time.Now()
	return &GameHallPinConf{
		ID:             _id,
		IsSelectAllTab: data.GetIsSelectAllTab(),
		TabIds:         data.GetTabIds(),
		TTID:           data.GetTtid(),
		Uid:            data.GetUid(),
		Content:        data.GetContent(),
		HighlightText:  highlightTexts,
		Img:            data.GetImg(),
		StartTime:      data.GetStartTime(),
		EndTime:        data.GetEndTime(),
		UpdateTime:     now.UnixMilli(),
		CreateTime:     now,
	}, nil
}
