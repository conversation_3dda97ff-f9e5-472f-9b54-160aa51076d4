package cache

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"strconv"
	"time"
)

type MsgRecord struct {
	MsgId    uint64
	SendTime int64
}

const (
	getUserJoinMemLimit = 50
	getHallJoinMemLimit = 3
)

// 每个tab下的组队和邀请消息
func getTeamAndInviteMsgKey(tabId uint32) string {
	return fmt.Sprintf("%s:%d", "game_hall:te_invite", tabId)
}

// 每条消息加入的用户
func getGameHallJoinMsgKey(tabId uint32, msgId uint64) string {
	return fmt.Sprintf("%s:%d:%d", "hall:join", tabId, msgId)
}

// 用户所有的报名用户
func getUserJoinMsgKey(tabId, sendUid uint32) string {
	return fmt.Sprintf("%s:%d:%d", "user:join", tabId, sendUid)
}

// 所以有发过组队和邀请进房的消息的玩法
func getTeamAndInviteTabKey() string {
	return "game_hall:te_invite:tab"
}

func (c *TeamRedisDao) GetAllTeamAndInviteTab(ctx context.Context) ([]uint32, error) {
	members, err := c.GetCmder().SMembers(ctx, getTeamAndInviteTabKey()).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAllTeamAndInviteTab SMembers err:%v", err)
		return nil, err
	}
	tabIds := make([]uint32, 0, len(members))
	for _, v := range members {
		tabId, aErr := strconv.Atoi(v)
		if aErr != nil {
			log.ErrorWithCtx(ctx, "GetAllTeamAndInviteTab strconv err:%v, val:%s", aErr, v)
			continue
		}
		tabIds = append(tabIds, uint32(tabId))
	}

	return tabIds, nil
}

func (c *TeamRedisDao) AddSendTeamAndInviteMsgRecord(ctx context.Context, tabId uint32, msgId uint64) error {
	addResult := make([]*redis.IntCmd, 0, 1)
	_, err := c.GetCmder().Pipelined(ctx, func(pipeLiner redis.Pipeliner) error {
		key := getTeamAndInviteMsgKey(tabId)
		addResult = append(addResult, pipeLiner.ZAdd(ctx, key, &redis.Z{
			Score:  float64(time.Now().UnixMilli()),
			Member: strconv.FormatUint(msgId, 10),
		}))
		// 添加有添加过消息的玩法
		pipeLiner.SAdd(ctx, getTeamAndInviteTabKey(), tabId)
		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AddSendTeamAndInviteMsgRecord ZAdd err:%v", err)
		return err
	}
	for _, v := range addResult {
		if r, e := v.Result(); e != nil {
			log.ErrorWithCtx(ctx, "AddSendTeamAndInviteMsgRecord add result err:%v, r:%d, val:%+v", e, r, v)
		}
	}
	return err
}

func (c *TeamRedisDao) GetTeamAndInviteMsgRevRank(ctx context.Context, tabId uint32, msgId uint64) (int64, error) {
	key := getTeamAndInviteMsgKey(tabId)
	return c.GetCmder().ZRevRank(ctx, key, strconv.FormatUint(msgId, 10)).Result()
}

func (c *TeamRedisDao) GetTeamAndInviteMsg(ctx context.Context, tabId uint32, start, stop int64) ([]string, error) {
	key := getTeamAndInviteMsgKey(tabId)
	cmdRes, err := c.GetCmder().ZRevRange(ctx, key, start, stop).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTeamAndInviteMsg ZRange err:%v", err)
		return nil, err
	}
	return cmdRes, nil
}

func (c *TeamRedisDao) ZRemExpireTeamAndInviteMsg(ctx context.Context, tabId uint32, minScore, maxScore string) error {
	key := getTeamAndInviteMsgKey(tabId)
	// 删除已过期无效索引数据
	count, err := c.GetCmder().ZRemRangeByScore(ctx, key, minScore, maxScore).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "ZRemExpireTeamAndInviteMsg err:%v, key:%s, min:%s, max:%s", err, key, minScore, maxScore)
	}
	log.InfoWithCtx(ctx, "ZRemExpireTeamAndInviteMsg expired info, count:%d, key:%s, min:%s, max:%s", count, key, minScore, maxScore)
	return nil
}

func (c *TeamRedisDao) AddJoinTeamRecord(ctx context.Context, sendUid, joinUid uint32, tabId uint32, msgId uint64, expire time.Duration) error {
	var addResult []*redis.IntCmd
	member := &redis.Z{
		Score:  float64(time.Now().UnixMilli()),
		Member: strconv.Itoa(int(joinUid)),
	}
	_, err := c.GetCmder().Pipelined(ctx, func(pipeLiner redis.Pipeliner) error {
		hallKey := getGameHallJoinMsgKey(tabId, msgId)
		addResult = append(addResult, pipeLiner.ZAdd(ctx, hallKey, member))
		pipeLiner.Expire(ctx, hallKey, expire)

		userKey := getUserJoinMsgKey(tabId, sendUid)
		addResult = append(addResult, pipeLiner.ZAdd(ctx, userKey, member))
		pipeLiner.Expire(ctx, userKey, expire)
		return nil
	})
	if err != nil {
		return err
	}
	for _, v := range addResult {
		if r, e := v.Result(); e != nil {
			log.ErrorWithCtx(ctx, "AddJoinTeamRecord add result err:%v, r:%d, val:%+v", e, r, v)
		}
	}
	return err
}

type HallJoinTeamRecord struct {
	Uids     []uint32
	Count    int64
	SelfJoin bool
}

func (c *TeamRedisDao) GetHallJoinTeamRecord(ctx context.Context, tabId uint32, msgIds []uint64, uid uint32, minScore, maxScore string) (map[uint64]*HallJoinTeamRecord, error) {
	type tempRes struct {
		valCmd       *redis.StringSliceCmd
		countCmd     *redis.IntCmd
		selfScoreCmd *redis.FloatCmd
	}
	var cmdResult = make(map[uint64]*tempRes)
	_, err := c.GetCmder().Pipelined(ctx, func(pipeLiner redis.Pipeliner) error {
		for _, msgId := range msgIds {
			key := getGameHallJoinMsgKey(tabId, msgId)
			valCmd := pipeLiner.ZRevRangeByScore(ctx, key, &redis.ZRangeBy{
				Min:    minScore,
				Max:    maxScore,
				Offset: 0,
				Count:  getHallJoinMemLimit,
			})

			cmdResult[msgId] = &tempRes{
				valCmd:       valCmd,
				countCmd:     pipeLiner.ZCard(ctx, key),
				selfScoreCmd: pipeLiner.ZScore(ctx, key, strconv.Itoa(int(uid))),
			}
		}
		return nil
	})
	if err != nil && err != redis.Nil {
		return nil, err
	}

	var addResult = make(map[uint64]*HallJoinTeamRecord)
	for msgId, cmd := range cmdResult {
		members, e := cmd.valCmd.Result()
		if e != nil {
			log.ErrorWithCtx(ctx, "GetHallJoinTeamRecord get result err:%v, msgId:%d, val:%+v", e, msgId, cmd.valCmd)
			continue
		}
		uids := make([]uint32, 0, len(members))
		for _, v := range members {
			uid, aErr := strconv.Atoi(v)
			if aErr != nil {
				log.ErrorWithCtx(ctx, "GetHallJoinTeamRecord strconv err:%v, val:%s", aErr, v)
				continue
			}
			uids = append(uids, uint32(uid))
		}

		addResult[msgId] = &HallJoinTeamRecord{
			Uids:     uids,
			Count:    cmd.countCmd.Val(),
			SelfJoin: cmd.selfScoreCmd.Val() > 0,
		}
	}

	return addResult, nil
}

type JoinTeamRecord struct {
	JoinUid  uint32
	JoinTime int64
}

func (c *TeamRedisDao) GetUserJoinTeamRecord(ctx context.Context, tabId uint32, sendUid uint32, minScore, maxScore string) ([]*JoinTeamRecord, error) {
	key := getUserJoinMsgKey(tabId, sendUid)
	cmdRes, err := c.GetCmder().ZRevRangeByScoreWithScores(ctx, key, &redis.ZRangeBy{
		Min:    minScore,
		Max:    maxScore,
		Offset: 0,
		Count:  getUserJoinMemLimit,
	}).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserJoinTeamRecord ZRevRangeByScore err:%v", err)
		return nil, err
	}
	res := make([]*JoinTeamRecord, 0, len(cmdRes))
	for _, x := range cmdRes {
		if member, ok := x.Member.(string); ok {
			uid, aErr := strconv.Atoi(member)
			if aErr != nil {
				log.ErrorWithCtx(ctx, "GetHallJoinTeamRecord strconv err:%v, val:%s", aErr, member)
				continue
			}
			res = append(res, &JoinTeamRecord{
				JoinTime: int64(x.Score),
				JoinUid:  uint32(uid),
			})
		}
	}
	return res, nil
}
