package cache

import (
	"context"
	"github.com/smartystreets/goconvey/convey"
	redismocks "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/mocks"
	"strconv"
	"testing"
	"time"
)

var redisDao *TeamRedisDao

func init() {
	redisCli := redismocks.Mock()
	if redisCli == nil {
		return
	}
	redisDao = NewRedisDao(redisCli)
}

func TestTeamRedisDao(t *testing.T) {
	convey.Convey("Test TeamRedisDao", t, func() {
		ctx := context.Background()
		tabId := uint32(1)
		msgId := uint64(1)
		sendUid := uint32(1)
		joinUid := uint32(2)
		expire := time.Duration(100) * time.Second

		minScore := "-inf"
		maxScore := "+inf"

		// Test AddSendTeamAndInviteMsgRecord
		err := redisDao.AddSendTeamAndInviteMsgRecord(ctx, tabId, msgId)
		convey.So(err, convey.ShouldBeNil)

		// Test GetTeamAndInviteMsgRevRank
		rank, err := redisDao.GetTeamAndInviteMsgRevRank(ctx, tabId, msgId)
		convey.So(err, convey.ShouldBeNil)
		convey.So(rank, convey.ShouldBeGreaterThanOrEqualTo, 0)

		// Test GetTeamAndInviteMsg
		msgs, err := redisDao.GetTeamAndInviteMsg(ctx, tabId, 0, 10)
		convey.So(err, convey.ShouldBeNil)
		convey.So(msgs, convey.ShouldContain, strconv.FormatUint(msgId, 10))

		// Test ZRemExpireTeamAndInviteMsg
		err = redisDao.ZRemExpireTeamAndInviteMsg(ctx, tabId, minScore, maxScore)
		convey.So(err, convey.ShouldBeNil)

		// Test AddJoinTeamRecord
		err = redisDao.AddJoinTeamRecord(ctx, sendUid, joinUid, tabId, msgId, expire)
		convey.So(err, convey.ShouldBeNil)

		// Test GetHallJoinTeamRecord
		records, err := redisDao.GetHallJoinTeamRecord(ctx, tabId, []uint64{msgId}, joinUid, minScore, maxScore)
		convey.So(err, convey.ShouldBeNil)
		convey.So(records[msgId].Uids, convey.ShouldContain, joinUid)

		// Test GetUserJoinTeamRecord
		userRecords, err := redisDao.GetUserJoinTeamRecord(ctx, tabId, sendUid, minScore, maxScore)
		convey.So(err, convey.ShouldBeNil)
		convey.So(userRecords, convey.ShouldNotBeEmpty)
	})
}
