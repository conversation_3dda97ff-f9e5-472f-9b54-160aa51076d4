package cache

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/monkey-send-chat/monkey_sender"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	channelPlayTabPb "golang.52tt.com/protocol/services/channel-play-tab"
	"golang.52tt.com/services/channel-play-middle/conf"
	"golang.52tt.com/services/channel-play-middle/internal/cache/cache_client"
	"time"

	"golang.52tt.com/pkg/log"
	mysteryplacePB "golang.52tt.com/protocol/services/mystery-place"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
)

var MinorityGameParentId uint32 //这个是一套环境一个不变的，所以这个就不用锁了

// 排序用
type MinorityGameInfo struct {
	TabInfo   *tabPB.Tab
	GameScore uint32
}

type tabCache struct {
	clientInst *cache_client.Client
	// 设置的小众游戏 key:tab_id
	minorityGameTabMap map[uint32]*tabPB.Tab

	tabIdMap map[uint32]*tabPB.Tab

	// baseBlocksMap 将tabId作为键，以map形式存储这个tab下的所有blockBase
	baseBlocksMap map[uint32][]*tabPB.Block

	// key:tabId value:剧本详情
	scenarioMap map[uint32]*mysteryplacePB.ScenarioInfo

	//key:tabId value 白名单uidMap
	tabWhiteListMap map[uint32]map[uint32]bool

	// 极速PC分类配置 key:tabId, value:[]*channel_play_tab_pb.FastPCCategoryConfig
	fastPCCategoryConfig []*channelPlayTabPb.FastPCCategoryConfig
}

var tabInfoCache = &tabCache{}

func setTabInfoCache(tmpCache *tabCache) {
	tabInfoCache = tmpCache
}

func GetTabInfoCache() *tabCache {
	return tabInfoCache
}

func (t *tabCache) GetTabNameById(tabId uint32) string {
	return t.tabIdMap[tabId].GetName()
}

func (t *tabCache) GetTabAliasNameById(tabId uint32) string {
	return t.tabIdMap[tabId].GetTabAliasName()
}

func (t *tabCache) GetBaseBlocksMap() map[uint32][]*tabPB.Block {
	return t.baseBlocksMap
}

func (t *tabCache) GetTabIdCache() map[uint32]*tabPB.Tab {
	return t.tabIdMap
}

func (t *tabCache) GetTabInfoCacheById(tabId uint32) *tabPB.Tab {
	return t.tabIdMap[tabId]
}

func (t *tabCache) GetMinorityGameTab() map[uint32]*tabPB.Tab {
	return t.minorityGameTabMap
}

func (t *tabCache) GetElemInfoMap(tabId uint32) map[uint32]*tabPB.Elem {
	res := make(map[uint32]*tabPB.Elem)
	baseBlocks, ok := t.baseBlocksMap[tabId]
	if !ok {
		return res
	}
	for _, block := range baseBlocks {
		for _, elem := range block.GetElems() {
			res[elem.GetId()] = elem
		}
	}

	return res
}

func sendCacheWarning(msg string) {
	sendErr := monkey_sender.GetNumMsgSenderByChatId(conf.PublicSwitchConfig.GetFallBackChatId(),
		conf.PublicSwitchConfig.GetFallBackWarnDuration()).SendMsg("channel-play-logic缓存", msg)
	if sendErr != nil {
		log.Errorf("refreshTabInfoCache monkey_sender fallback SendMsg err:%v", sendErr)
	}
}

func timerRefreshTabInfoCache(ctx context.Context) {
	defer func() {
		err := recover()
		if err != nil {
			log.Errorf("timerRefreshTabInfoCache refreshTabInfoCache err = %v", err)
		}
	}()
	if err := refreshTabInfoCache(ctx); err != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache err:%v", err)
	}
}

func refreshTabInfoCache(ctx context.Context) error {
	minorityGameTabMap := make(map[uint32]*tabPB.Tab)
	clientInst := tabInfoCache.clientInst
	mgTabs, err := clientInst.TCTabClient.GetMinorityGameTabs(ctx, &tabPB.GetMinorityGameTabsReq{})
	if err != nil {
		//报错不更新，不阻碍其他缓存信息更新,需要告警
		log.ErrorWithCtx(ctx, "UserLine Time Err: refreshTabInfoCache GetMinorityGameTabs error: %v", err)
		sendCacheWarning(fmt.Sprintf("GetMinorityGameTabs len:%d, err:%v", len(mgTabs.GetChildTab()), err))
		minorityGameTabMap = tabInfoCache.minorityGameTabMap
	} else {
		if len(mgTabs.GetChildTab()) == 0 {
			//报错不更新，不阻碍其他缓存信息更新,需要告警
			log.ErrorWithCtx(ctx, "UserLine Time Err: refreshTabInfoCache GetMinorityGameTabs error: len:0")
			sendCacheWarning(fmt.Sprintf("GetMinorityGameTabs err, len:%d", len(mgTabs.GetChildTab())))
			minorityGameTabMap = tabInfoCache.minorityGameTabMap
		} else {
			for _, v := range mgTabs.GetChildTab() {
				minorityGameTabMap[v.GetId()] = v
			}
			MinorityGameParentId = mgTabs.GetParentTab().GetId()
		}
	}

	tabResp, err := clientInst.TCTabClient.Tabs(ctx, 0, 1000)
	if err != nil {
		log.ErrorWithCtx(ctx, "UserLine Time Err: refreshTabInfoCache Tabs error: %v", err)
		sendCacheWarning(fmt.Sprintf("Tabs len:%d, err:%v", len(tabResp.GetTabs()), err))
		return err
	}
	if len(tabResp.GetTabs()) == 0 {
		log.ErrorWithCtx(ctx, "UserLine Time Err: refreshTabInfoCache Tabs error: len 0")
		sendCacheWarning(fmt.Sprintf("Tabs len:%d", len(tabResp.GetTabs())))
		return protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError)
	}
	//tabIds记录所有tabID
	tabIds := make([]uint32, 0)
	tabIdMap := make(map[uint32]*tabPB.Tab, len(tabResp.GetTabs()))

	for _, v := range tabResp.GetTabs() {
		tabIdMap[v.GetId()] = v
		tabIds = append(tabIds, v.GetId())
	}

	scenarioMap := make(map[uint32]*mysteryplacePB.ScenarioInfo)
	scenarioResp, err := clientInst.MysteryPlaceClient.ListScenarioInfo(ctx, &mysteryplacePB.ListScenarioInfoReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache ListScenarioInfo err: %v", err)
		scenarioMap = tabInfoCache.scenarioMap
	} else {
		for _, scenarioInfo := range scenarioResp.GetInfos() {
			scenarioMap[scenarioInfo.GetTabId()] = scenarioInfo
			for _, tabInfo := range scenarioInfo.GetTabInfos() {
				scenarioMap[tabInfo.GetTabId()] = scenarioInfo
			}
		}
	}

	baseBlocksMap, err := genBaseBlocksMap(ctx, tabIds)
	if err != nil {
		log.WarnWithCtx(ctx, " refreshTabInfoCache update baseBlocksMap err %v", err)
		//baseBlocksMap = tabInfoCache.baseBlocksMap
		sendCacheWarning(fmt.Sprintf("genBaseBlocksMap map len:%d, old map len:%d, err:%v", len(baseBlocksMap), len(tabInfoCache.baseBlocksMap), err))
		return err
	}

	whiteListMap, err := genTabWhiteListMap(ctx)
	if err != nil {
		//报错不更新，不阻碍其他缓存信息更新,需要告警
		log.ErrorWithCtx(ctx, "UserLine Time Err: refreshTabInfoCache update tabWhiteListMap  err %v", err)
		whiteListMap = tabInfoCache.tabWhiteListMap
	}

	fastPCCategoryConfig, fastTabErr := clientInst.ChannelPlayTabClient.GetFastPCCategoryConfig(ctx, &channelPlayTabPb.GetFastPCCategoryConfigReq{})
	if fastTabErr != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache GetFastPCCategoryConfig error: %v", fastTabErr)
	}

	tmpCache := &tabCache{
		clientInst:         tabInfoCache.clientInst,
		minorityGameTabMap: minorityGameTabMap,
		tabIdMap:           tabIdMap,

		baseBlocksMap:        baseBlocksMap,
		scenarioMap:          scenarioMap,
		tabWhiteListMap:      whiteListMap,
		fastPCCategoryConfig: fastPCCategoryConfig,
	}

	setTabInfoCache(tmpCache)

	log.InfoWithCtx(ctx, "refreshTabInfoCache tabIds:%v", tabIds)
	log.InfoWithCtx(ctx, "refreshTabInfoCache minorityGameTabMap:%d, tabIdMap:%d, baseBlocksMap:%d, len(scenarioMap):%d, tabWhiteListMap:%d",
		len(minorityGameTabMap), len(tabIdMap), len(baseBlocksMap), len(scenarioMap), len(whiteListMap))

	return nil
}

func genBaseBlocksMap(ctx context.Context, tabIds []uint32) (baseBlocksMap map[uint32][]*tabPB.Block, err error) {
	baseBlocksMap = make(map[uint32][]*tabPB.Block)
	resp, err := tabInfoCache.clientInst.TCTabClient.BatchGetBlocks(ctx, &tabPB.BatchGetBlocksReq{TabId: tabIds})
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache genBaseBlocksMap err %v", err)
		return baseBlocksMap, err
	}
	if len(resp.GetData()) == 0 {
		log.ErrorWithCtx(ctx, "genBaseBlocksMap err: len 0")
		return baseBlocksMap, protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError)
	}
	// k:tabId, val:blocks
	for k, v := range resp.GetData() {
		if _, ok := baseBlocksMap[k]; !ok {
			baseBlocksMap[k] = make([]*tabPB.Block, 0)
			baseBlocksMap[k] = append(baseBlocksMap[k], v.GetBlocks()...)
		}
	}
	return baseBlocksMap, nil
}

func NewCache(ctx context.Context, clientInst *cache_client.Client) error {
	tabInfoCache = &tabCache{
		clientInst: clientInst,
	}
	err := refreshTabInfoCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache error :%v", err)
		return err
	}
	go func() {
		ticker := time.NewTicker(time.Second * 30)
		defer func() {
			ticker.Stop()
		}()

		for range ticker.C {
			refreshCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			timerRefreshTabInfoCache(refreshCtx)
			cancel()
		}
	}()
	return nil
}

func GetScenarioMap() map[uint32]*mysteryplacePB.ScenarioInfo {
	return tabInfoCache.scenarioMap
}

func GetScenarioByTabId(tabId uint32) *mysteryplacePB.ScenarioInfo {
	m := GetScenarioMap()

	var s *mysteryplacePB.ScenarioInfo
	if m != nil {
		s = m[tabId]
	}

	return s
}

// 玩法白名单缓存,key tabId value uid数组
func genTabWhiteListMap(ctx context.Context) (map[uint32]map[uint32]bool, error) {
	resp, err := tabInfoCache.clientInst.ChannelPlayTabClient.BatchGetWhiteUidListByTabIds(ctx, &channelPlayTabPb.BatchGetWhiteUidListByTabIdsReq{
		TabIds:  []uint32{},
		Source:  "channel-play-logic_cache",
		NoCache: true,
	})
	if err != nil {
		return nil, err
	}
	listMap := make(map[uint32]map[uint32]bool, len(resp))
	for k, v := range resp {
		uidMap := make(map[uint32]bool, len(v))
		for _, uid := range v {
			uidMap[uid] = true
		}
		listMap[k] = uidMap
	}
	return listMap, nil
}

func (t *tabCache) GetWhiteList() map[uint32]map[uint32]bool {
	return tabInfoCache.tabWhiteListMap
}

func (t *tabCache) GetFastPCCategoryConfig() []*channelPlayTabPb.FastPCCategoryConfig {
	return t.fastPCCategoryConfig
}
