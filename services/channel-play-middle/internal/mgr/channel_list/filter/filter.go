package filter

import (
	"context"
	"golang.52tt.com/kaihei-pkg/blreport"
	"golang.52tt.com/kaihei-pkg/supervision"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol/grpc"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	channelPB "golang.52tt.com/protocol/services/channelsvr"
	tcPb "golang.52tt.com/protocol/services/topic_channel/channel"
	"golang.52tt.com/services/channel-play-middle/conf"
	"golang.52tt.com/services/channel-play-middle/internal/cache"
	"golang.52tt.com/services/channel-play-middle/internal/mgr/channel_list/model"
)

type ItemFilter struct {
	supervisorInst *supervision.Supervisory
}

func NewItemFilter(supervisorInst *supervision.Supervisory) *ItemFilter {
	return &ItemFilter{
		supervisorInst: supervisorInst,
	}
}

func (f *ItemFilter) DoFilter(ctx context.Context, serviceInfo *grpc.ServiceInfo, item *channel_play.TopicChannelItem,
	commonData *model.ChannelCommonHandleData) uint32 {
	channelFilterRet := f.doChannelFilter(ctx, item, commonData)
	if channelFilterRet != 0 {
		return channelFilterRet
	}
	return f.doTabFilter(ctx, serviceInfo, item, commonData)
}

func isPublishing(displayTypes []tcPb.ChannelDisplayType) bool {
	for _, v := range displayTypes {
		if v == tcPb.ChannelDisplayType_DISPLAY_AT_MAIN_PAGE {
			return true
		}
	}
	return false
}

func shouldSkipChannel(ctx context.Context, channelId uint32, channelSimpleInfo *channelPB.ChannelSimpleInfo) bool {
	if channelSimpleInfo == nil {
		log.ErrorWithCtx(ctx, "shouldSkipChannel channel(%d) can't get  channel info", channelId)

		return true
	} else if channelSimpleInfo.GetHasPwd() {
		//被锁房了就不返回
		log.WarnWithCtx(ctx, "shouldSkipChannel %d HasPwd", channelId)

		return true
	} else {
		return false
	}
}

func (f *ItemFilter) doChannelFilter(ctx context.Context, item *channel_play.TopicChannelItem,
	commonData *model.ChannelCommonHandleData) uint32 {
	channelId := item.GetChannelId()
	switchOpts := commonData.SwitchOpts

	// 过滤锁房
	if switchOpts.NeedChannelPwdFilter && shouldSkipChannel(ctx, channelId, commonData.ChannelInfoMap[channelId]) {
		//channel info拿不到或锁房了，不返回
		log.WarnWithCtx(ctx, "UgcHandler channelIds %v cant get channelInfo", channelId)
		return uint32(blreport.F_ID_ChannelPush_Channel_Lock)
	}
	// 过滤房主
	owner := commonData.ChannelInfoMap[channelId].GetBindId()
	//被惩罚用户不显示在开黑列表
	if commonData.PunishMap != nil && commonData.PunishMap[owner] {
		log.DebugWithCtx(ctx, "MakeListTopicChannelItems 被惩罚用户 %v", owner)
		return uint32(blreport.F_ID_ChannelPush_PunishUser)
	}
	cinfo, ok := commonData.TcInfoMap[channelId]
	if !ok {
		log.WarnWithCtx(ctx, "UgcHandler channelIds %v cant get tcInfo", channelId)
		return uint32(blreport.F_ID_ChannelPush_GetChannelInfo_Fail)
	}
	// 过滤非发布中房间
	if switchOpts.NeedDoPublishFilter && !isPublishing(cinfo.GetDisplayType()) {
		log.InfoWithCtx(ctx, "UgcHandler DoPublishFilter cInfo:%s", cinfo.String())
		return uint32(blreport.F_ID_ChannelPush_Publishing_Filter)
	}
	return 0
}

func (f *ItemFilter) doTabFilter(ctx context.Context, serviceInfo *grpc.ServiceInfo, item *channel_play.TopicChannelItem,
	commonData *model.ChannelCommonHandleData) uint32 {
	channelId := item.GetChannelId()
	cinfo, ok := commonData.TcInfoMap[channelId]
	if !ok {
		log.WarnWithCtx(ctx, "UgcHandler channelIds %v cant get tcInfo", channelId)
		return uint32(blreport.F_ID_ChannelPush_GetChannelInfo_Fail)
	}

	switchOpts := commonData.SwitchOpts
	if switchOpts.NeedDoTabFilter {

		if commonData.TabFilter[cinfo.GetTabId()] {
			log.WarnWithCtx(ctx, "UgcHandler TabFilter channelIds %v tabId %v is filtered", channelId, cinfo.GetTabId())
			return uint32(blreport.F_ID_ChannelPush_Tab_Push_Minors_Filter)
		}
		tabInfo := cache.GetTabInfoCache().GetTabInfoCacheById(cinfo.GetTabId())
		if commonData.CategoryFilter[tabInfo.GetCategoryId()] {
			log.WarnWithCtx(ctx, "UgcHandler CategoryFilter channelIds %v categoryId %v is filtered", channelId, tabInfo.GetCategoryId())
			return uint32(blreport.F_ID_ChannelPush_Tab_Push_Minors_Filter)
		}
		if f.supervisorInst.MiniGameStageStrategy(tabInfo, serviceInfo, cache.GetTabInfoCache().GetWhiteList()) &&
			!conf.PublicSwitchConfig.GetChannelListNoFilterTabSwitch(cinfo.GetTabId()) {
			log.InfoWithCtx(ctx, "UgcHandler DoTabFilter MiniGameStageStrategy cInfo:%s", cinfo.String())
			return uint32(blreport.F_ID_ChannelPush_Tab_Strategy_Filter)
		}
	}
	return 0
}
