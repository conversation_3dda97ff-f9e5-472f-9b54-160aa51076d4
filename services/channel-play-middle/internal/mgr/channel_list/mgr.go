package channel_list

import (
	"context"
	"fmt"
	account "golang.52tt.com/clients/account-go"
	"golang.52tt.com/kaihei-pkg/blreport"
	eventlink "golang.52tt.com/pkg/kafka_eventlink"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol/grpc"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	"golang.52tt.com/services/channel-play-middle/conf"
	"golang.52tt.com/services/channel-play-middle/internal/mgr/channel_list/channel_item"
	"golang.52tt.com/services/channel-play-middle/internal/mgr/channel_list/filter"
	"golang.52tt.com/services/channel-play-middle/internal/mgr/channel_list/model"
	"golang.52tt.com/services/channel-play-middle/internal/mgr/channel_list/view"
	"google.golang.org/protobuf/runtime/protoimpl"
	"google.golang.org/protobuf/types/known/anypb"
)

func NewChannelItemMgr(accountClient account.IClient, itemFilter *filter.ItemFilter, channelStatPro *eventlink.ELKafkaProducer) *ChannelItemMgr {

	viewFactory := view.NewViewFactory(accountClient)
	baseInfoAssembler := channel_item.NewBaseInfoAssembler(viewFactory)
	rcmdInfoAssembler := channel_item.NewRcmdInfoAssembler()
	return &ChannelItemMgr{
		baseInfoAssembler: baseInfoAssembler,
		rcmdInfoAssembler: rcmdInfoAssembler,
		itemFilter:        itemFilter,
		channelStatPro:    channelStatPro,
	}
}

type ChannelItemMgr struct {
	baseInfoAssembler *channel_item.CommonInfoAssembler
	rcmdInfoAssembler *channel_item.RcmdInfoAssembler
	itemFilter        *filter.ItemFilter
	channelStatPro    eventlink.IELKafkaProducer
}

func (m *ChannelItemMgr) BatchGetChannelItem(ctx context.Context, serviceInfo *grpc.ServiceInfo,
	commonData *model.ChannelCommonHandleData) map[uint32]*anypb.Any {

	itemMap := make(map[uint32]*anypb.Any, len(commonData.UgcChannelIds))
	channelItemGenerator := channel_item.NewChannelItem(commonData, m.baseInfoAssembler, m.rcmdInfoAssembler, serviceInfo)
	channelPushStatHandle := blreport.NewChannelPushStat(conf.PublicSwitchConfig.GetIsChannelStatPush(), m.channelStatPro)
	// 上报房间发布情况

	for _, cid := range commonData.UgcChannelIds {
		item, ret := m.GenAndFilterItem(ctx, cid, channelItemGenerator, serviceInfo, commonData)
		commonData.ReportData.SetChannelRet(cid, blreport.FilterID(ret))
		if ret == uint32(blreport.F_ID_ChannelPush_Suc) {
			anyMsg, err := anypb.New(protoimpl.X.ProtoMessageV2Of(item))
			if err != nil {
				log.ErrorWithCtx(ctx, "BatGetGameChannelViewMap anypb.New item:%s err:%v", item.String(), err)
				continue
			}
			itemMap[cid] = anyMsg
			//log.InfoWithCtx(ctx, "BatGetGameChannelViewMap cid:%d item:%s", cid, item.String())
		} else {
			log.WarnWithCtx(ctx, "BatGetGameChannelViewMap cid:%d filtered ret:%d switchOpts:%+v", cid, ret, commonData.SwitchOpts)
		}
	}

	m.ReportChannelInfo(ctx, commonData, channelPushStatHandle, len(itemMap))

	return itemMap
}

func (m *ChannelItemMgr) GenAndFilterItem(ctx context.Context, cid uint32, channelItemGenerator *channel_item.ChannelItem,
	serviceInfo *grpc.ServiceInfo, commonData *model.ChannelCommonHandleData) (item *channel_play.TopicChannelItem, ret uint32) {
	item, ret = channelItemGenerator.GenItem(ctx, cid)
	if ret != 0 {
		return item, ret
	}
	ret = m.itemFilter.DoFilter(ctx, serviceInfo, item, commonData)
	return item, ret

}

func (m *ChannelItemMgr) ReportChannelInfo(ctx context.Context, commonData *model.ChannelCommonHandleData,
	channelPushStatHandle *blreport.ChannelPushStat, rspChannelListLen int) {
	reqChannelListLen := len(commonData.UgcChannelIds)

	if diffLen := reqChannelListLen - rspChannelListLen; diffLen > 2 {
		log.InfoWithCtx(ctx, "BatGetGameChannelViewMap ReportPublishingFilter reqSource:%s reqChannelIds:%v "+
			" retMap:%v",
			commonData.ReqSource.String(), commonData.UgcChannelIds, commonData.ReportData.ChannelRetMap)
	} /*else {
		log.DebugWithCtx(ctx, "BatGetGameChannelViewMap ReportPublishingFilter  reqSource:%s reqChannelIds:%v"+
			" retMap:%v",
			commonData.ReqSource.String(), commonData.UgcChannelIds, commonData.ReportData.ChannelRetMap)
	}*/

	if !commonData.SwitchOpts.NeedReportData {
		return
	}

	var expireChidsLogText string

	retCountMap := make(map[blreport.FilterID]int, len(commonData.UgcChannelIds))
	for cid, ret := range commonData.ReportData.ChannelRetMap {
		retCountMap[ret]++
		var sucCnt uint32
		info := commonData.TcInfoMap[cid]
		if ret == blreport.F_ID_ChannelPush_Suc {
			sucCnt = 1
		}
		if ret == blreport.F_ID_ChannelPush_Publishing_Filter {
			expireChidsLogText += fmt.Sprintf("%d-%d-%d-%d,", info.GetId(), info.GetTabId(), info.GetCreateTime(), info.GetLastDismissTime())
		}
		publishTime := info.GetCreateTime()
		reasonDesc := ret.GetChannelPushDesc()
		channelPushStatHandle.AddItem(cid, publishTime, info.GetTabId(), commonData.ChannelInfoMap,
			reasonDesc, commonData.ReportData.GetReportDataAllStr(), info.GetTerminalType(),
			info.GetMarketId(), sucCnt, uint32(ret))

	}

	channelPushStatHandle.AddInfo(uint32(commonData.EnterSource), "gangup")
	channelPushStatHandle.AddKafka(ctx, commonData.ReportData.TabId)

	if expireChidsLogText != "" {
		commonData.ReportData.SetFilterlType(blreport.F_ID_ChannelList_Publishing_Filter)
		commonData.ReportData.SetChannelFilterInfo(expireChidsLogText)
		blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelListInfo(ctx, commonData.ReportData)
	}

	tabReasonFilterdNum := retCountMap[blreport.F_ID_ChannelPush_TabCache_NotExist] +
		retCountMap[blreport.F_ID_ChannelPush_Tab_Strategy_Filter] +
		retCountMap[blreport.F_ID_ChannelPush_Tab_Push_Minors_Filter] +
		retCountMap[blreport.F_ID_ChannelPush_Tab_NotMatch]

	channelReasonFilterNum := retCountMap[blreport.F_ID_ChannelPush_ChannelTab_NotExist] +
		retCountMap[blreport.F_ID_ChannelPush_Channel_Lock] +
		retCountMap[blreport.F_ID_ChannelPush_PunishUser] +
		retCountMap[blreport.F_ID_ChannelPush_GetChannelInfo_Fail]

	// 上报房间tab元素过滤原因聚合
	if tabReasonFilterdNum > 0 && tabReasonFilterdNum == reqChannelListLen {
		commonData.ReportData.SetFilterlType(blreport.F_ID_ChannelList_TabId_List_NIL)
		blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelListInfo(ctx, commonData.ReportData)
	} else if tabReasonFilterdNum > 0 && reqChannelListLen-tabReasonFilterdNum < 5 {
		if reqChannelListLen > 10 {
			commonData.ReportData.SetFilterlType(blreport.F_ID_ChannelList_TabId_List_Five_Less)
		} else {
			commonData.ReportData.SetFilterlType(blreport.F_ID_ChannelList_TabId_List_Five)
		}
		blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelListInfo(ctx, commonData.ReportData)
	} else {
		log.InfoWithCtx(ctx, "reqChannelListLen:%d tabReasonFilterdNum:%d", reqChannelListLen, tabReasonFilterdNum)
	}

	// 上报channel信息元素过滤原因聚合
	if channelReasonFilterNum > 0 && channelReasonFilterNum == reqChannelListLen {
		commonData.ReportData.SetFilterlType(blreport.F_ID_ChannelList_Channel_List_Nil)
		blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelListInfo(ctx, commonData.ReportData)

	} else if channelReasonFilterNum > 0 && reqChannelListLen-channelReasonFilterNum < 5 {
		commonData.ReportData.SetFilterlType(blreport.F_ID_ChannelList_Channel_List_Five)
		blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelListInfo(ctx, commonData.ReportData)
	} else {
		log.InfoWithCtx(ctx, "reqChannelListLen:%d channelReasonFilterNum:%d", reqChannelListLen, channelReasonFilterNum)
	}

}
