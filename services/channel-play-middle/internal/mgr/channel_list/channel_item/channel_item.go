package channel_item

import (
	"context"
	"golang.52tt.com/kaihei-pkg/blreport"
	"golang.52tt.com/pkg/protocol/grpc"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	"golang.52tt.com/services/channel-play-middle/internal/cache"
	"golang.52tt.com/services/channel-play-middle/internal/mgr/channel_list/model"
)

type ChannelItem struct {
	commonData        *model.ChannelCommonHandleData
	baseInfoAssembler *CommonInfoAssembler
	rcmdInfoAssembler *RcmdInfoAssembler
	svrInfo           *grpc.ServiceInfo
}

func NewChannelItem(commonData *model.ChannelCommonHandleData, baseInfoAssembler *CommonInfoAssembler,
	rcmdInfoAssembler *RcmdInfoAssembler, svrInfo *grpc.ServiceInfo) *ChannelItem {
	return &ChannelItem{
		commonData:        commonData,
		baseInfoAssembler: baseInfoAssembler,
		rcmdInfoAssembler: rcmdInfoAssembler,
		svrInfo:           svrInfo,
	}
}

func (c *ChannelItem) GenItem(ctx context.Context, cid uint32) (*channel_play.TopicChannelItem, uint32) {
	var ret uint32
	item, ret := c.baseInfoAssembler.GenBaseItem(ctx, c.commonData, cid, c.svrInfo)
	if ret != uint32(blreport.F_ID_ChannelPush_Suc) {
		return item, ret
	}

	rcmdItemInfo := c.rcmdInfoAssembler.GenRcmdItemInfo(ctx, cid, c.commonData)
	item.RcmdLabel = rcmdItemInfo.rcmdLabel
	item.TraceInfo = rcmdItemInfo.traceInfo
	if item.GetTabId() != 0 {
		if tabCacheInfo := cache.GetTabInfoCache().GetTabInfoCacheById(item.GetTabId()); tabCacheInfo != nil {
			item.MiddlePositionLabel = rcmdItemInfo.middlePositionLabel
			//if topic_channel.CategoryType(tabCacheInfo.GetCategoryMapping()) == topic_channel.CategoryType_Gangup_type {
			//	item.MiddlePositionLabel = rcmdItemInfo.middlePositionLabel
			//}
		}
	}
	item.UnderHeadImgLabel = rcmdItemInfo.underHeadImgLabel
	item.GeoInfo = rcmdItemInfo.geoInfo
	return item, uint32(blreport.F_ID_ChannelPush_Suc)
}
