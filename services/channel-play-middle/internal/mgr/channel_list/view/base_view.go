package view

import (
	"context"
	"golang.52tt.com/pkg/protocol/grpc"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	channelPB "golang.52tt.com/protocol/services/topic_channel/channel"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-middle/internal/mgr/channel_list/model"
)

const (
	BlockNoLimit        = "不限"
	InPutModeMaxElemNum = 4
)

type BaseViewHandlerI interface {
	GenChannelView(ctx context.Context, channelId uint32, svrInfo *grpc.ServiceInfo,
		commonData *model.ChannelCommonHandleData) (*channel_play.TopicChannelView, uint32)
}

func GetRoomCondition(blockOptions []*channelPB.BlockOption, tabInfo *tabPB.Tab) string {
	var condition string
	specialCondition := tabInfo.SpecialChannelCondition
	if specialCondition == nil {
		return tabInfo.DefaultChannelCondition
	}
	//用户选择了的字段
	userSelectedBlockMap := initUserChoseBlockIdMap(blockOptions)
	for blockId, elemMap := range userSelectedBlockMap {
		conditionMap, ok := specialCondition[blockId]
		if !ok {
			continue
		}
		if len(elemMap) > 1 {
			//多选,展示默认状态
			condition = tabInfo.DefaultChannelCondition
			return condition
		} else {
			for elemId := range elemMap {
				specialCon, ok := conditionMap.Condition[elemId]
				if ok {
					//用户选的elem，有配置个性化状态
					condition = specialCon
					return condition
				}
			}
		}
		//找到个性化状态，退出for
		break
	}
	//没有找到个性化状态，返回默认状态
	if condition == "" {
		condition = tabInfo.DefaultChannelCondition
	}
	return condition
}
