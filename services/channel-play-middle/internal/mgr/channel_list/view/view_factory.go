package view

import (
	account "golang.52tt.com/clients/account-go"
	channel_play "golang.52tt.com/protocol/app/channel-play"
)

type ViewFactory struct {
	ViewHandlerMap map[channel_play.ChannelViewType]BaseViewHandlerI
}

func NewViewFactory(accountClient account.IClient) *ViewFactory {
	mijingViewInst := NewMijingView(accountClient)
	defaultViewInst := NewDefaultView()
	ktvViewInst := NewKtvView()
	mobaViewInst := NewMobaView()
	musicMarshalViewInst := NewMusicMarshalView()
	return &ViewFactory{
		ViewHandlerMap: map[channel_play.ChannelViewType]BaseViewHandlerI{
			channel_play.ChannelViewType_ChannelView_MysteryEscape: mijingViewInst,
			channel_play.ChannelViewType_ChannelView_Default:       defaultViewInst,
			channel_play.ChannelViewType_ChannelView_Ktv:           ktvViewInst,
			channel_play.ChannelViewType_ChannelView_MOBA:          mobaViewInst,
			channel_play.ChannelViewType_ChanenlView_Marshal:       musicMarshalViewInst,
		},
	}
}

func (h *ViewFactory) GetHandler(viewType channel_play.ChannelViewType) BaseViewHandlerI {
	handler, ok := h.ViewHandlerMap[viewType]
	if ok {
		return handler
	} else {
		return h.ViewHandlerMap[channel_play.ChannelViewType_ChannelView_Default]
	}
}
