package view

import (
	"fmt"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	tcPb "golang.52tt.com/protocol/services/topic_channel/channel"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-middle/conf"
	"golang.52tt.com/services/channel-play-middle/internal/cache"
	"math"
	"strconv"
	"strings"
)

type VBlockHandler struct {
	selectedBlockOptions []*tcPb.BlockOption
	blocks               []*tabPB.Block
	viewType             channel_play.ChannelViewType
	listStyle            channel_play.ChannelListStyleType
	serviceInfo          *grpc.ServiceInfo

	//mobaTitleMap         map[string]string
	normalTitleMap       map[string]string
	allSelectBlockMap    map[string]bool
	userSelectedBlockMap map[uint32]map[uint32]BlockOptionVal
	topTitles            []*TitleInfo
}

type TitleInfo struct {
	Key          string
	Val          string
	ShowPosition tabPB.Block_ShowPosition
}

func NewVBlockHandler(
	blockOptions []*tcPb.BlockOption,
	blocks []*tabPB.Block,
	viewType channel_play.ChannelViewType,
	listStyle channel_play.ChannelListStyleType,
	serviceInfo *grpc.ServiceInfo,
) *VBlockHandler {
	handler := &VBlockHandler{
		selectedBlockOptions: blockOptions,
		blocks:               blocks,
		viewType:             viewType,
		listStyle:            listStyle,
		serviceInfo:          serviceInfo,
	}
	handler.GenBlockTitleMap(viewType, "/")
	return handler
}

func (v *VBlockHandler) GenBlockTitleMap(viewType channel_play.ChannelViewType, sepStr string) {
	blocks := v.blocks
	blockOptions := v.selectedBlockOptions
	//不限的block
	allSelectBlockMap := make(map[string]bool, len(blocks))
	//用户选择的
	userSelectedBlockMap := initUserChoseBlockIdMap(blockOptions)
	//系统预设的
	presetBlockMap := setPresetBlockMap(blocks)
	titleMap := make(map[string]string)
	//mobaTitleMap := make(map[string]string)
	topTitles := make([]*TitleInfo, 0)
	for _, block := range blocks {
		userSelectBlock := userSelectedBlockMap[block.GetId()]
		if len(userSelectBlock) < 1 {
			continue
		}
		//系统预设选项个数与用户勾选个数相同,且为全选
		if len(userSelectBlock) == len(presetBlockMap[block.GetId()]) && isAllSelect(block, userSelectBlock) {
			//titleMap[block.Title] = "不限"
			allSelectBlockMap[block.GetTitle()] = true
			/*
			   if v.IsMobaBlock(viewType, block.GetTitle()) {
			       mobaTitleMap[block.GetTitle()] = BlockNoLimit
			   } else {
			       titleMap[block.GetTitle()] = BlockNoLimit
			   }
			*/
			// 输入框类型走原来的展示逻辑
			if block.GetMode() != tabPB.Block_USER_INPUT && viewType == channel_play.ChannelViewType_ChannelView_MOBA &&
				(block.GetShowPosition() == tabPB.Block_Top || block.GetShowPosition() == tabPB.Block_TopAfterTabName) {
				topTitles = append(topTitles, &TitleInfo{
					Key:          block.GetTitle(),
					Val:          BlockNoLimit,
					ShowPosition: block.GetShowPosition(),
				})
			} else {
				titleMap[block.GetTitle()] = BlockNoLimit
			}
			continue
		}
		var optionVals []optionVal
		mostSelectNum := int(block.GetMostSelectNum())
		var userSelectedNum int
		for i, elem := range block.GetElems() {
			if block.GetMode() == tabPB.Block_USER_INPUT && userSelectedNum > InPutModeMaxElemNum { // 目前最多配置四项
				break
			}
			if (mostSelectNum > 0 && userSelectedNum >= mostSelectNum) || (mostSelectNum <= 0 &&
				block.GetMode() == tabPB.Block_SINGLE && userSelectedNum >= 1) {
				//配置了发布项最多选N项或者单选时，房间列表也只返回对应数量的展示项
				break
			}
			if userSelectBlock[elem.GetId()].IsSelectEd { //获取当前block下所有选中的elem数组的index（不是elem_id）即这个block都勾选了哪几位elem
				optionVals = append(optionVals, optionVal{
					id:  i,
					val: userSelectBlock[elem.Id].Val,
				})
				userSelectedNum++
			}
		}
		if len(optionVals) == 0 {
			continue
		}

		stringSet := genStringSet(block, optionVals)

		var str []string
		for _, slice := range stringSet {
			str = append(str, strings.Join(slice, "-"))
		}
		var title = strings.Join(str, sepStr)
		/*
		   if v.IsMobaBlock(viewType, block.GetTitle()) {
		       mobaTitleMap[block.GetTitle()] = title
		   } else {
		       titleMap[block.GetTitle()] = title
		   }
		*/
		if block.GetMode() != tabPB.Block_USER_INPUT && viewType == channel_play.ChannelViewType_ChannelView_MOBA &&
			(block.GetShowPosition() == tabPB.Block_Top || block.GetShowPosition() == tabPB.Block_TopAfterTabName) {
			topTitles = append(topTitles, &TitleInfo{
				Key:          block.GetTitle(),
				Val:          title,
				ShowPosition: block.GetShowPosition(),
			})
		} else {
			titleMap[block.GetTitle()] = title
		}
	}
	v.normalTitleMap = titleMap
	//v.mobaTitleMap = mobaTitleMap
	v.allSelectBlockMap = allSelectBlockMap
	v.userSelectedBlockMap = userSelectedBlockMap
	v.topTitles = topTitles
	//log.Debugf("getBlockTitleMap blockOptions(%+v) blocks(%+v) mobaTitleMap(%+v) titleMap(%+v) allSelectBlocks(%+v)",
	//	blockOptions, blocks, mobaTitleMap, titleMap, allSelectBlockMap)
}

/*
func (v *VBlockHandler) IsMobaBlock(viewType channel_play.ChannelViewType, title string) bool {
	if viewType != channel_play.ChannelViewType_ChannelView_MOBA {
		return false
	}
	if title == playServer || title == playerNum || title == playMode {
		return true
	}
	return false
}
*/

func (v *VBlockHandler) IsTopBlock(viewType channel_play.ChannelViewType, title string) bool {
	if viewType != channel_play.ChannelViewType_ChannelView_MOBA {
		return false
	}
	for _, topTitle := range v.topTitles {
		if topTitle.Key == title {
			return true
		}
	}
	return false
}

func (v *VBlockHandler) GetDescListByBusiness(tabInfo *tabPB.Tab, IdDesc string) []string {
	descList := make([]string, 0)
	if IdDesc != "" {
		descList = append(descList, IdDesc)
	}

	switch v.viewType {
	case channel_play.ChannelViewType_ChannelView_Default, channel_play.ChannelViewType_ChannelView_MOBA:
		descList = append(descList, v.genGangupDescList(tabInfo, v.serviceInfo)...)
	case channel_play.ChannelViewType_ChannelView_Ktv:
		descList = append(descList, v.genKtvViewDescList(v.listStyle)...)
	default:
	}

	return descList
}

func (v *VBlockHandler) genGangupDescList(tabInfo *tabPB.Tab, serviceInfo *grpc.ServiceInfo) []string {
	blocks := v.blocks
	descList := make([]string, 0)
	minorityGame := cache.GetTabInfoCache().GetMinorityGameTab()
	if len(blocks) == 0 {
		//只有小众游戏在没有发布选项时还需要有描述字段
		if mtInfo, ok := minorityGame[tabInfo.GetId()]; ok {
			//小众游戏名字即描述
			desc := "游戏 : " + mtInfo.Name
			descList = append(descList, desc)
		}
		return descList
	}

	if IsChannelListOldView(serviceInfo.ClientVersion, serviceInfo.MarketID) {
		return v.handleProblemVersion()
	}

	//开黑房（除小游戏）
	if tabInfo.GetHomePageType() == tabPB.HomePageType_HomePageTypeGAME && tabInfo.GetTabType() != tabPB.Tab_MINIGAME {
		descList = v.getGangupBusinessDescListByView()
	} else {
		descList = v.getDefaultDescList()
	}
	return descList
}

// 根据样式生成 发布字段列表，tabName
func (v *VBlockHandler) genKtvViewDescList(listStyleType channel_play.ChannelListStyleType) []string {

	if listStyleType == channel_play.ChannelListStyleType_Mix_Business_Home_Page {
		return nil
	} else {
		return v.getDefaultDescList()
	}
}

type optionVal struct {
	id  int    // 下标id
	val string // 用户发布输入的值
}

func (v *VBlockHandler) getDefaultDescList() []string {
	blocks := v.blocks
	normalBlockMap := v.normalTitleMap
	userSelectedMap := v.userSelectedBlockMap
	descList := make([]string, 0, len(blocks))
	for _, b := range blocks {
		if b.GetMode() == tabPB.Block_USER_INPUT {
			continue
		}
		if value, ok := normalBlockMap[b.GetTitle()]; ok {
			descList = append(descList, b.Title+": "+value)
		}
	}
	// 用户输入类型（战力评分）
	descList = append(descList, getUserInputDescList(blocks, userSelectedMap)...)
	return descList
}

func (v *VBlockHandler) handleProblemVersion() []string {

	blocks := v.blocks
	normalBlockMap := v.normalTitleMap
	allSelectBlockMap := v.allSelectBlockMap
	descList := make([]string, 0)
	for _, block := range blocks {
		value, ok := normalBlockMap[block.GetTitle()]
		if ok && !allSelectBlockMap[block.GetTitle()] {
			descList = append(descList, value)
			continue
		}
	}
	var huanyouDesc string
	if len(descList) > 0 {
		huanyouDesc = "想找：" + strings.Join(descList, "、")
		return []string{huanyouDesc}
	}
	return descList
}

// false 3行， true 1行
func IsChannelListOldView(nowVersion, marketId uint32) bool {
	switch app.BaseReq_MarketId(marketId) {
	case app.BaseReq_MARKET_HUANYOU:
		return IsHuanYouChannelListOldVersion(nowVersion)
	case app.BaseReq_MARKET_NONE:
		return IsTTChannelListOldVersion(nowVersion)
	default:
		return false //3行
	}
}

func IsTTChannelListOldVersion(nowVersion uint32) bool {
	var upperLimitVersion, lowerLimitVersion uint32
	upperMajor := 6
	upperMinor := 28
	upperPatch := 0
	downMajor := 6
	downMinor := 22
	downPatch := 0
	lowerLimitVersion = protocol.FormatClientVersion(uint8(downMajor), uint8(downMinor), uint16(downPatch))
	upperLimitVersion = protocol.FormatClientVersion(uint8(upperMajor), uint8(upperMinor), uint16(upperPatch))
	if nowVersion >= lowerLimitVersion && nowVersion < upperLimitVersion {
		return true
	}
	return false
}

func IsHuanYouChannelListOldVersion(nowVersion uint32) bool {
	var upperLimitVersion, lowerLimitVersion uint32
	upperMajor := 6
	upperMinor := 28
	upperPatch := 0
	downMajor := 6
	downMinor := 18
	downPatch := 0
	lowerLimitVersion = protocol.FormatClientVersion(uint8(downMajor), uint8(downMinor), uint16(downPatch))
	upperLimitVersion = protocol.FormatClientVersion(uint8(upperMajor), uint8(upperMinor), uint16(upperPatch))
	if nowVersion >= lowerLimitVersion && nowVersion < upperLimitVersion {
		return true
	}
	return false
}

func getUserInputDescList(blocks []*tabPB.Block, userSelectedMap map[uint32]map[uint32]BlockOptionVal) (descList []string) {
	// 加上新增字段的展示
	for _, b := range blocks {
		if b.GetMode() != tabPB.Block_USER_INPUT {
			continue
		}
		if userSelectedMap[b.GetId()] == nil {
			continue
		}

		elemDesc := ""
		for _, e := range b.GetElems() {
			if !userSelectedMap[b.GetId()][e.GetId()].IsSelectEd || userSelectedMap[b.GetId()][e.GetId()].Val == "" {
				continue
			}
			for _, f := range e.GetPublicFlags() {
				if f == tabPB.Elem_PublishFlagMiddleCardMode {
					//log.Debugf("getUserInputDescList, blockId:%d, elemId:%d, flag:%v", b.GetId(), e.GetId(), f)
					if elemDesc == "" {
						elemDesc = e.GetTitle() + ":" + userSelectedMap[b.GetId()][e.GetId()].Val
					} else {
						elemDesc += "/" + e.GetTitle() + ":" + userSelectedMap[b.GetId()][e.GetId()].Val
					}
					break
				}
			}
		}
		if elemDesc == "" {
			continue
		}
		descList = append(descList, b.Title+": "+elemDesc)
	}
	return
}

// 开黑游戏房descList处理逻辑
func (v *VBlockHandler) getGangupBusinessDescListByView() (
	descList []string) {
	blocks := v.blocks
	normalBlockMap := v.normalTitleMap
	allSelectBlockMap := v.allSelectBlockMap
	userSelectedMap := v.userSelectedBlockMap
	viewType := v.viewType

	descList = make([]string, 0, len(blocks))
	noLimitDescList := make([]string, 0, len(allSelectBlockMap))
	for _, b := range blocks {
		if b.GetMode() == tabPB.Block_USER_INPUT {
			continue
		}
		if allSelectBlockMap[b.GetTitle()] {
			noLimitDescList = append(noLimitDescList, b.GetTitle())
			continue
		}

		if value, ok := normalBlockMap[b.GetTitle()]; ok {
			descList = append(descList, b.Title+": "+value)
			continue
		}
	}
	// 用户输入类型（战力评分）
	descList = append(descList, getUserInputDescList(blocks, userSelectedMap)...)

	//没有选择了不限的
	if len(noLimitDescList) == 0 {
		return
	}

	noMobaDescList := make([]string, 0, len(noLimitDescList))
	for _, val := range noLimitDescList {
		if v.IsTopBlock(viewType, val) {
			// moba view 不需要在noLimitDescList中处理不限
			continue
		}
		noMobaDescList = append(noMobaDescList, val)
	}
	if len(noMobaDescList) > 0 {
		maxBlockNum := conf.ChannelPlayConfig.GetChannelListNoLimitBlockMaxNum()
		if len(noMobaDescList) > maxBlockNum {
			noMobaDescList = noMobaDescList[:maxBlockNum]
		}
		descList = append(descList, strings.Join(noMobaDescList, "/")+": "+BlockNoLimit)
	}
	return descList
}

type BlockOptionVal struct {
	IsSelectEd bool
	Val        string
}

// 用户选择的发布字段选项
func initUserChoseBlockIdMap(blockOptions []*tcPb.BlockOption) map[uint32]map[uint32]BlockOptionVal {
	var userSelectedBlockMap = map[uint32]map[uint32]BlockOptionVal{}
	for _, blockOption := range blockOptions {
		if userSelectedBlockMap[blockOption.GetBlockId()] == nil {
			userSelectedBlockMap[blockOption.GetBlockId()] = map[uint32]BlockOptionVal{
				blockOption.GetElemId(): {
					IsSelectEd: true,
					Val:        FormatNumber(blockOption.GetElemVal()),
				}}
		} else {
			userSelectedBlockMap[blockOption.GetBlockId()][blockOption.GetElemId()] = BlockOptionVal{
				IsSelectEd: true,
				Val:        FormatNumber(blockOption.GetElemVal()),
			}
		}
	}
	return userSelectedBlockMap
}

func FormatNumber(val string) string {
	num, err := strconv.Atoi(val)
	if err != nil {
		return ""
	}
	if num < 1000 {
		return strconv.Itoa(num)
	} else if num < 10000 {
		if num%1000 < 100 {
			return fmt.Sprintf("%dk", num/1000)
		} else {
			return fmt.Sprintf("%.1fk", math.Floor(float64(num)/1000*10)/10)
		}
	} else if num < 100000 {
		if num%10000 < 1000 {
			return fmt.Sprintf("%dw", num/10000)
		} else {
			return fmt.Sprintf("%.1fw", math.Floor(float64(num)/10000*10)/10)
		}

	} else {
		return ""
	}
}

func IsDisplayChannel(displayType []tcPb.ChannelDisplayType) bool {
	for _, v := range displayType {
		if v == tcPb.ChannelDisplayType_DISPLAY_AT_MAIN_PAGE {
			//仅展示发布的
			return true
		}
	}
	return false
}