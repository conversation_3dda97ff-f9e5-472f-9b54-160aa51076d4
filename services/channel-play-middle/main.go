package main

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/grpc"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/grpc/server" // server startup
	channel_operate_permission_checker "golang.52tt.com/protocol/services/channel-operate-permission-checker"
	pb "golang.52tt.com/protocol/services/channel-play-middle"

	"golang.52tt.com/services/channel-play-middle/internal"

	_ "golang.52tt.com/pkg/hub/tyr/compatible/server"
)

func main() {
	var (
		svr *internal.Server
		cfg = &internal.StartConfig{}
		err error
	)

	// config file support yaml & json, default channel-play-middle.json/yaml
	if err := server.NewServer("channel-play-middle", cfg).
		AddGrpcServer(server.NewBuildOption().
			WithInitializeFunc(func(ctx context.Context, s *grpc.Server) error {
				if svr, err = internal.NewServer(ctx, cfg); err != nil {
					return err
				}

				// register custom grpc server
				pb.RegisterChannelPlayMiddleServer(s, svr)

				//// 房间操作权限检查器
				channel_operate_permission_checker.RegisterChannelOperatePermissionCheckerServer(s, svr)

				return nil
			}),
		).
		WithCloseFunc(func(ctx context.Context) {
			// do something when server terminating
			svr.ShutDown()
		}).
		Start(); err != nil {
		log.Errorf("server start fail, err: %v", err)
	}
}
