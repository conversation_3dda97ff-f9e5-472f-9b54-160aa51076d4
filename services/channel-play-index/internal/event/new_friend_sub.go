package event

import (
	"context"
	"github.com/Shopify/sarama"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	context_info "golang.52tt.com/pkg/context-info"
	kafkaonline "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafka_online_update"
	"golang.52tt.com/services/channel-play-index/internal/config"
	"golang.52tt.com/services/channel-play-index/internal/mgr/new_friend_index"
	"runtime/debug"
	"time"
)

type NewFriendSub struct {
	Sub               subscriber.Subscriber
	newFriendIndexMgr *new_friend_index.NewFriendIndexMgr
}

func NewNewFriendSubscriber(ctx context.Context, cfg *conf.StartConfig, newFriendIndexMgr *new_friend_index.NewFriendIndexMgr) (*NewFriendSub, error) {
	newSubscriber := &NewFriendSub{
		newFriendIndexMgr: newFriendIndexMgr,
	}
	kafkaConf := kafka.DefaultConfig()
	kafkaConf.ClientID = cfg.NewFriendKafkaConfig.ClientID
	kafkaConf.Consumer.Offsets.Initial = sarama.OffsetNewest
	kafkaConf.Consumer.Return.Errors = true
	kafkaSub, err := kafka.NewSubscriber(cfg.NewFriendKafkaConfig.BrokerList(), kafkaConf)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewSubscriber friend kafka error: %v", err)
		return nil, err
	}
	if nil != cfg.NewFriendKafkaConfig {
		err := kafkaSub.SubscribeContext(cfg.NewFriendKafkaConfig.GroupID, cfg.NewFriendKafkaConfig.TopicList(), subscriber.ProcessorContextFunc(newSubscriber.onFriendUpdateEvent))
		if err != nil {
			log.ErrorWithCtx(ctx, "Subscribe friend kafka error: %v", err)
			return nil, err
		} else {
			log.InfoWithCtx(ctx, "Subscribe friend kafka suc:%v", cfg.NewFriendKafkaConfig)
		}
	}
	newSubscriber.Sub = kafkaSub

	return newSubscriber, nil
}

func (m *NewFriendSub) onFriendUpdateEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (err error, retry bool) {
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	ctx = context_info.GenReqId(ctx)
	defer func() {
		if e := recover(); e != nil {
			log.Errorf("errored to onFriendUpdateEvent err:%v, stack: %v", e, string(debug.Stack()))
		}
		cancel()
	}()

	eventMsg := &kafkaonline.FrinedUpdate{}
	err = proto.Unmarshal(msg.Value, eventMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "onFriendUpdateEvent Unmarshal error: %v", err)
		return nil, false
	}

	log.InfoWithCtx(ctx, "onFriendUpdateEvent eventMsg: %+v", eventMsg)
	// 添加好友才处理
	if eventMsg.GetIsAdd() {
		// eventMsg.GetEvTs() 拿出来是0，直接以当前时间戳处理
		happenTime := time.Now().UnixMilli()
		m.newFriendIndexMgr.AddNewFriend(ctx, eventMsg.GetUid(), eventMsg.GetTargetUid(), happenTime)
	}

	return
}
