package manager

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	channel_scheme_middle "golang.52tt.com/protocol/services/channel-scheme-middle"
	gangup_channel "golang.52tt.com/protocol/services/gangup-channel"
	music_channel "golang.52tt.com/protocol/services/music-topic-channel"
	"golang.52tt.com/protocol/services/topic_channel/channel"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/topic-channel/channel-server/confz"
	"golang.52tt.com/services/topic-channel/channel-server/rpc/client"
	"strings"
	"time"
)

func (s *TopicChannelMgr) SwitchChannelTab(ctx context.Context, in *channel.SwitchChannelTabReq, oldTabInfo *tabPB.Tab, tabInfo *tabPB.Tab) (err error) {
	// 先取消旧的玩法发布状态
	_, err = s.DismissChannelByTabId(ctx, in.GetChannelId(), oldTabInfo.GetId(), int(channel.DismissType_SwitchTab))
	if err != nil {
		log.WarnWithCtx(ctx, "SwitchChannelTab DismissChannel %v fail err; %v", in.GetChannelId(), err)
	}
	//先切玩法，玩法管理侧可能有其他的限制，防止切不成功数据不一致
	//调下游接口进行写操作
	var switchResp *channel_scheme_middle.SetCurChannelSchemeResp
	switchResp, err = client.ChannelSchemeMiddleClient.SetCurChannelScheme(ctx, &channel_scheme_middle.SetCurChannelSchemeReq{
		OpUid:                in.GetUid(),
		Cid:                  in.GetChannelId(),
		SchemeId:             tabInfo.GetId(),
		Source:               channel_scheme_middle.Source_SET_UGC_SWITCH,
		AppId:                in.GetAppId(),
		MarketId:             in.GetMarketId(),
		NotSetMicAndMinigame: false,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SwitchChannelTab SetCurChannelScheme  error %v, cid:%d, schemeId:%d", err, in.GetChannelId(), tabInfo.GetId())
		return err
	}
	log.InfoWithCtx(ctx, "SwitchChannelTab SetCurChannelScheme success, cid:%d, info:%v", in.GetChannelId(), switchResp)

	//设置麦位放在update channel info后面，因为channelapi.SetMicMode中上报数据到数据中心，会反过来查房间的tabId那些信息..
	// 放在设置麦位后面，会造成切换标签和加载小游戏模式不同步，只能讨论修改setmicMode不要上报数据了.所以update先放回后面 记于2020.2.14
	var serr = setRoomModel(ctx, in.GetUid(), in.GetChannelId(), tabInfo)
	if serr != nil {
		if strings.Contains(serr.Error(), "context canceled") && (in.GetSource() == channel.Source_CREATE || in.GetSource() == channel.Source_PUBLISH) {
			log.InfoWithCtx(ctx, "SwitchChannelTab setRoomModel userId(%v) tabId(%v) err(%v) code (%d)", in.GetUid(), tabInfo.GetId(), serr, serr.Code())
		} else {
			log.ErrorWithCtx(ctx, "SwitchChannelTab setRoomModel userId(%v) tabId(%v) err(%v) code (%d)", in.GetUid(), tabInfo.GetId(), serr, serr.Code())
		}

		if serr.Code() == status.ErrOpengameChangeGameTooOfen {
			return protocol.NewExactServerError(nil, status.ErrTopicChannelFrequentlySwitchPlay)
		}
		return serr
	}

	if tabInfo.GetHomePageType() == tabPB.HomePageType_HomePageTypeGAME {
		_, err = client.GangupChannelClient.SwitchChannelTab(ctx, &gangup_channel.SwitchChannelTabReq{
			Uid:        in.GetUid(),
			TabId:      tabInfo.GetId(),
			ChannelId:  in.GetChannelId(),
			AppId:      in.GetAppId(),
			MarketId:   in.GetMarketId(),
			SwitchTime: int64(switchResp.GetSwitchMsTs()),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "SwitchChannelTab SwitchGangupChannelTab channelId(%d) tabId(%d) error :%v", in.GetChannelId(), in.GetTabId(), err)
			return err
		}
	} else if tabInfo.GetHomePageType() == tabPB.HomePageType_HomePageTypeMUSIC {
		if confz.GetOption().IsOpenFastPcChatTabDoubleWrite() && tabInfo.GetId() == confz.GetOption().GetMuseChatTabId() {
			_, err = client.GangupChannelClient.SwitchChannelTab(ctx, &gangup_channel.SwitchChannelTabReq{
				Uid:        in.GetUid(),
				TabId:      tabInfo.GetId(),
				ChannelId:  in.GetChannelId(),
				AppId:      in.GetAppId(),
				MarketId:   in.GetMarketId(),
				SwitchTime: int64(switchResp.GetSwitchMsTs()),
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "SwitchChannelTab SwitchGangupChannelTab channelId(%d) tabId(%d) error :%v", in.GetChannelId(), in.GetTabId(), err)
				return err
			}
		}
		_, err = client.MusicChannelClient.SwitchChannelTab(ctx, &music_channel.SwitchChannelTabReq{
			Uid:       in.GetUid(),
			TabId:     tabInfo.GetId(),
			ChannelId: in.GetChannelId(),
			AppId:     in.GetAppId(),
			MarketId:  in.GetMarketId(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "SwitchChannelTab SwitchMusicChannelReleaseInfo channelId(%d) tabId(%d) error :%v", in.GetChannelId(), in.GetTabId(), err)
			return err
		}
	} else {
		log.InfoWithCtx(ctx, "SwitchChannelTab tabInfo.GetHomePageType() %v", tabInfo.GetHomePageType())
	}

	go func() {
		if oldTabInfo.GetHomePageType() == tabPB.HomePageType_HomePageTypeGAME {
			if oldTabInfo.GetHomePageType() != tabInfo.GetHomePageType() { //从开黑切换至别的玩法类型清除开黑信息，失败就失败吧
				ctx2, cancel := context.WithTimeout(context.Background(), 2*time.Second)
				defer cancel()
				_, _ = client.GangupChannelClient.CleanChannelInfo(ctx2, &gangup_channel.CleanChannelInfoReq{
					ChannelId: in.GetChannelId(),
				})
			}
		}
	}()

	ReportSwitchTab(ctx, in.GetChannelId(), tabInfo.GetId(), tabInfo.GetName(), tabInfo.GetHomePageType())
	return
}

func setRoomModel(ctx context.Context, uid, channelId uint32, tabInfo *tabPB.Tab) protocol.ServerError {
	tabType := tabInfo.GetTabType()
	tabName := tabInfo.GetName()
	_, err := client.UserTagClient.NotifyChannelPlayChange(ctx, channelId, uint32(tabType), tabName)
	if err != nil {
		log.WarnWithCtx(ctx, "setRoomModel  NotifyChannelPlayChange failed, userId(%v) channelID (%v) err: %v", uid, channelId, err)
		return protocol.ToServerError(err)
	}
	return nil
}

func (s *TopicChannelMgr) ServerTimeMs(ctx context.Context, cid uint32) uint64 {
	t, err := s.dao.Time(cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "cache.time failed:%v cid:%d", err, cid)
		t = time.Now()
	}

	tsMs := t.UnixNano() / int64(time.Millisecond)

	return uint64(tsMs)
}
