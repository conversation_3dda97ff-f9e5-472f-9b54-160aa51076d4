package team_mgr

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	"golang.52tt.com/pkg/log"
	game_hall_logic "golang.52tt.com/protocol/app/game-hall-logic"
	game_hall "golang.52tt.com/protocol/services/game-hall"
	config "golang.52tt.com/services/game-hall-logic/internal/config/ttconfig/game_hall_logic"
	"time"
)

const (
	maxShowMemCount = 3
	noJoinMemShow   = "等待举手报名"
)

func (m *TeamMgr) GenInviteRoomMsg(ctx context.Context, cid, tabId uint32) ([]byte, error) {
	tabInfo := m.localCache.GetTabInfoById(tabId)
	gameCardConf := m.localCache.GetGameCardConfById(tabInfo.GetGameInfo().GetGameCardId())
	expireTime := time.Now().Add(time.Minute * time.Duration(config.GetGameHallLogicConfig().GetInviteRoomMsgExpireMin(tabId))).Unix()
	inviteRoomMsg := &game_hall_logic.GameInviteRoomMsg{
		TabId:      tabId,
		TabName:    tabInfo.GetName(),
		Icon:       gameCardConf.GetGameIconImgUrl(),
		Text:       "我已在房，就等你了",
		ExpireTime: expireTime,
		Cid:        cid,
		BgColorNum: gameCardConf.GetGameBackColorNum(),
	}
	marshalMsg, mErr := proto.Marshal(inviteRoomMsg)
	if mErr != nil {
		log.ErrorWithCtx(ctx, "FillGameImMsg marshal failed msg:%s err:%v", inviteRoomMsg.String(), mErr)
		return nil, mErr
	}
	return marshalMsg, nil
}

func (m *TeamMgr) GenSendTeamMsgBytes(ctx context.Context, tabId uint32) ([]byte, error) {
	tabInfo := m.localCache.GetTabInfoById(tabId)
	gameCardConf := m.localCache.GetGameCardConfById(tabInfo.GetGameInfo().GetGameCardId())

	teamMsg := &game_hall_logic.GameFormTeamMsg{
		TabId:         tabId,
		TabName:       tabInfo.GetName(),
		Icon:          gameCardConf.GetGameIconImgUrl(),
		Text:          "一起玩！组队请滴滴",
		MemberAccount: nil,
		TeamInfo:      noJoinMemShow,
	}
	marshalMsg, err := proto.Marshal(teamMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenSendTeamMsgBytes marshal failed msg:%s err:%v", teamMsg.String(), err)
		return nil, err
	}
	return marshalMsg, nil
}

func (m *TeamMgr) GenJoinTeamMsgBytes(ctx context.Context, uid uint32, msgId uint64, tabId uint32) ([]byte, error) {
	tabInfo := m.localCache.GetTabInfoById(tabId)
	gameCardConf := m.localCache.GetGameCardConfById(tabInfo.GetGameInfo().GetGameCardId())

	teamMsg := &game_hall_logic.GameFormTeamMsg{
		TabId:         tabId,
		TabName:       tabInfo.GetName(),
		Icon:          gameCardConf.GetGameIconImgUrl(),
		Text:          "一起玩！组队请滴滴",
		MemberAccount: nil,
		TeamInfo:      noJoinMemShow,
		JoinUid:       uid,
	}
	teamMsg.MemberAccount, teamMsg.TeamInfo, _ = m.genJoinTeamAccountInfo(ctx, uid, msgId, tabId)

	marshalMsg, err := proto.Marshal(teamMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenJoinTeamMsgBytes marshal failed msg:%s err:%v", teamMsg.String(), err)
		return nil, err
	}
	return marshalMsg, nil
}

func (m *TeamMgr) genJoinTeamAccountInfo(ctx context.Context, uid uint32, msgId uint64, tabId uint32) (accounts []string, handsUpInfo string, err error) {
	teamRes, err := m.gameHallClient.BatchGetGameHallTeamList(ctx, &game_hall.BatchGetGameHallTeamListReq{
		MsgIds: []uint64{msgId},
		TabId:  tabId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GenJoinTeamMsgBytes BatchGetGameHallTeamList fail, msgId:%d, tabId:%d, err:%v", msgId, tabId, err)
		return nil, "", err
	}
	teamInfo := teamRes.GetMemInfo()[msgId]
	if len(teamInfo.GetJoinUid()) > 0 {
		userMap, aErr := m.accountCli.GetUsersMap(ctx, append(teamInfo.GetJoinUid(), uid))
		if aErr != nil {
			log.ErrorWithCtx(ctx, "GenJoinTeamAccountInfo GetUsersMap failed msgId:%d err:%v", msgId, aErr)
			return nil, "", aErr
		}
		accounts = make([]string, 0, maxShowMemCount)
		if teamInfo.GetIsSelfJoin() {
			accounts = append(accounts, userMap[uid].GetUsername())
		}

		for _, joinUid := range teamInfo.GetJoinUid() {
			if joinUid == uid {
				continue
			}
			if len(accounts) >= maxShowMemCount {
				break
			}
			// 添加非自己的用户
			if v, ok := userMap[joinUid]; ok {
				accounts = append(accounts, v.GetUsername())
			}
		}

		if teamInfo.GetMemCount() > 0 {
			handsUpInfo = fmt.Sprintf("%d人已举手", teamInfo.GetMemCount())
		}
	}
	return accounts, handsUpInfo, nil
}
