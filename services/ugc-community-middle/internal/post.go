package internal

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tyr/x/metadata"
	"golang.52tt.com/clients/obsgateway"
	"time"
	"unicode/utf8"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"
	"gitlab.ttyuyin.com/tyr/x/log"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	ugc_community "golang.52tt.com/protocol/services/ugc-community"
	pb "golang.52tt.com/protocol/services/ugc-community-middle"
	"golang.52tt.com/services/ugc-community-middle/internal/common"
)

var (
	invalidParamErr = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)

	publishPostInvalidTypeErr          = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "不支持的帖子类型")
	publishPostInvalidStateErr         = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "不支持的帖子状态")
	publishPostInvalidOriginErr        = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "不支持的发帖来源")
	publishPostTopicNotFoundErr        = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	publishPostMissAttachmentErr       = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "未上传附件")
	publishPostInvalidTopicNumErr      = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "不合法的话题数量")
	publishPostInvalidAttachmentErr    = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "不合法的附件参数")
	publishPostInvalidContentLengthErr = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "帖子内容长度不合法")
)

// PublishPost 发布帖子
func (s *Server) PublishPost(ctx context.Context, req *pb.PublishPostRequest) (resp *pb.PublishPostResponse, err error) {
	resp = new(pb.PublishPostResponse)
	svcInfo := metainfo.GetServiceInfo(ctx)
	log.InfoWithCtx(ctx, "PublishPost svcInfo(%+v) req: %+v", svcInfo, req)

	if svcInfo.UserID() == 0 {
		log.ErrorWithCtx(ctx, "PublishPost miss uid")
		return resp, invalidParamErr
	}
	if err = s.validatePublishPostRequest(ctx, req); err != nil {
		return resp, err
	}

	user, rpcErr := s.accountGoCli.GetUserByUid(ctx, svcInfo.UserID())
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "PublishPost GetUserByUid uid(%d) err: %v", svcInfo.UserID(), rpcErr)
		return resp, rpcErr
	}
	if user == nil {
		log.WarnWithCtx(ctx, "PublishPost GetUserByUid uid(%d) empty", svcInfo.UserID())
		return resp, invalidParamErr
	}

	reqPost := req.GetPost()
	post := &ugc_community.CreatePostRequest_Post{
		Id:          reqPost.GetId(),
		Uid:         svcInfo.UserID(),
		Type:        reqPost.GetType(),
		State:       reqPost.GetState(),
		Origin:      reqPost.GetOrigin(),
		Content:     reqPost.GetContent(),
		BizData:     reqPost.GetBizData(),
		Attachments: reqPost.GetAttachments(),
		TopicIdList: reqPost.GetTopicIdList(),
	}
	needScan, err := s.postMgr.CreatePost(ctx, post)
	if err != nil {
		log.ErrorWithCtx(ctx, "PublishPost CreatePost post(%+v) err: %v", post, err)
		return resp, err
	}

	// 送审
	if needScan {
		if err := s.postMgr.ScanPost(ctx, user, post); err != nil {
			log.ErrorWithCtx(ctx, "PublishPost ScanPost post(%+v) err: %v", post, err)
			return resp, err
		}
	}

	if req.GetTaskToken() != "" {
		go func() {
			asyncCtx, asyncCancel := context.WithTimeout(metadata.NewContext(ctx), 3*time.Second)
			defer asyncCancel()
			defer func() {
				if r := recover(); r != nil {
					log.Errorf("PublishPost HandlePostGuideTask panic err: %v", r)
				}
			}()
			s.postMgr.HandlePostGuideTask(asyncCtx, post, req.GetTaskToken())
		}()
	}

	log.InfoWithCtx(ctx, "PublishPost svcInfo(%+v) req(%+v) finished", svcInfo, req)
	return resp, nil
}

func (s *Server) genToken(ctx context.Context, postId string, uid uint32) (string, error) {
	req := &obsgateway.UploadTokenReq{
		App:        common.GetObsApp(),
		Scope:      common.GetObsScope(),
		CustomId:   fmt.Sprintf("%s:%d", postId, uid),
		Expiration: int32((time.Hour * 1).Seconds()),
	}
	token, err := s.obsgwClient.ClaimUploadToken(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenToken err:%v, req:%+v", err, req)
		return "", err
	}
	return token, nil
}

// GeneratePublishPostParam 生成发布帖子参数
func (s *Server) GeneratePublishPostParam(ctx context.Context, req *pb.GeneratePublishPostParamRequest) (resp *pb.GeneratePublishPostParamResponse, err error) {
	resp = new(pb.GeneratePublishPostParamResponse)
	log.InfoWithCtx(ctx, "GeneratePublishPostParam req: %+v", req)

	postID := primitive.NewObjectID().Hex()
	attachments := req.GetAttachments()

	for i, attachment := range attachments {
		attachment.Key = fmt.Sprintf("post/%s/%d", postID, i)
	}

	uid := metainfo.GetServiceInfo(ctx).UserID()
	obsToken, err := s.genToken(ctx, postID, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GeneratePublishPostParam genToken err: %v", err)
		return resp, err
	}

	resp.Id = postID
	resp.ObsApp = common.GetObsApp()
	resp.ObsScope = common.GetObsScope()
	resp.Attachments = attachments
	resp.ObsToken = obsToken

	log.InfoWithCtx(ctx, "GeneratePublishPostParam req(%+v) resp: %+v", req, resp)
	return resp, nil
}

func (s *Server) validatePublishPostRequest(ctx context.Context, req *pb.PublishPostRequest) error {
	const (
		maxTopicNum      = 3
		maxContentLength = 2000
	)

	post := req.GetPost()
	if post == nil {
		log.ErrorWithCtx(ctx, "validtaPublishPostRequest post nil")
		return invalidParamErr
	}

	switch post.GetOrigin() {
	case ugc_community.PostOrigin_POST_ORIGIN_AIGC_COMMUNITY_USER:
	case ugc_community.PostOrigin_POST_ORIGIN_AI_ROLE_HIGH_QUALITY_COMMENT:
	case ugc_community.PostOrigin_POST_ORIGIN_AI_ROLE_USER_COMMENT:
	default:
		log.ErrorWithCtx(ctx, "validatePublishPostRequest invalid post origin(%d)", post.GetOrigin())
		return publishPostInvalidOriginErr
	}

	switch post.GetState() {
	case ugc_community.PostState_POST_STATE_ANONY:
	case ugc_community.PostState_POST_STATE_PUBLIC:
	default:
		log.ErrorWithCtx(ctx, "validatePublishPostRequest invalid post state(%d)", post.GetState())
		return publishPostInvalidStateErr
	}

	contentLength := utf8.RuneCountInString(post.GetContent())
	switch post.GetType() {
	case ugc_community.PostType_POST_TYPE_TEXT:
		if contentLength == 0 || contentLength > maxContentLength {
			log.ErrorWithCtx(ctx, "validatePublishPostRequest invalid post content length(%d)", contentLength)
			return publishPostInvalidContentLengthErr
		}
	case ugc_community.PostType_POST_TYPE_IMAGE_TEXT:
		if contentLength > maxContentLength {
			log.ErrorWithCtx(ctx, "validatePublishPostRequest invalid post content length(%d)", contentLength)
			return publishPostInvalidContentLengthErr
		}
		if len(post.GetAttachments()) == 0 {
			log.ErrorWithCtx(ctx, "validatePublishPostRequest post attachments empty")
			return publishPostMissAttachmentErr
		}

		for _, attachment := range post.GetAttachments() {
			if attachment.GetType() != ugc_community.Attachment_TYPE_IMAGE || attachment.GetKey() == "" && attachment.GetUrl() == "" {
				log.ErrorWithCtx(ctx, "validatePublishPostRequest invalid attachment(%+v)", attachment)
				return publishPostInvalidAttachmentErr
			}
		}
	default:
		log.ErrorWithCtx(ctx, "validatePublishPostRequest invalid post type(%d)", post.GetType())
		return publishPostInvalidTypeErr
	}

	if topicNum := len(post.GetTopicIdList()); topicNum > 0 {
		if topicNum > maxTopicNum {
			log.WarnWithCtx(ctx, "validatePublishPostRequest invalid post topic num(%d)", topicNum)
			return publishPostInvalidTopicNumErr
		}

		batchGetTopicReq := &ugc_community.BatchGetTopicRequest{
			IdList: post.GetTopicIdList(),
		}
		batchGetTopicResp, err := s.ugcCommunityCli.BatchGetTopic(ctx, batchGetTopicReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "validatePublishPostRequest BatchGetTopic req(%+v) err: %v", batchGetTopicReq, err)
			return err
		}

		topicMap := batchGetTopicResp.GetTopics()
		for _, topicId := range post.GetTopicIdList() {
			if _, ok := topicMap[topicId]; !ok {
				log.ErrorWithCtx(ctx, "validatePublishPostRequest topic(%s) not found", topicId)
				return publishPostTopicNotFoundErr
			}
		}
	}

	if err := s.postMgr.ValidateBizData(ctx, post.GetBizData()); err != nil {
		log.ErrorWithCtx(ctx, "validatePublishPostRequest ValidateBizData bizData(%+v) err: %v", post.GetBizData(), err)
		return err
	}

	return nil
}
