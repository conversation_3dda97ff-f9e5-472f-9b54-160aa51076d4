package mgr

import (
	"fmt"
	tcPb "golang.52tt.com/protocol/services/topic_channel/channel"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"math"
	"strconv"
	"strings"
)

const (
	playServer = "区服"
	playMode   = "模式"
	playerNum  = "人数"

	BlockNoLimit           = "不限"
	InPutModeMaxElemNum    = 4
	NoLimitBlockDefaultNum = 3
)

func GenDescFirst(blockOptions []*tcPb.BlockOption, blocks []*tabPB.Block, isInvite bool) []string {
	desc := make([]string, 0, 3)
	if isInvite {
		return nil
	}
	mobaTitleMap, _, _, userSelectedMap := GetBlockTitleMap(blockOptions, blocks, "/")
	qufu := genDescAndColor(playServer, mobaTitleMap[playServer])
	mode := genDescAndColor(playMode, mobaTitleMap[playMode])
	renshu := genDescAndColor(playerNum, mobaTitleMap[playerNum])
	if len(qufu) > 0 {
		desc = append(desc, qufu)
	}
	if len(mode) > 0 {
		desc = append(desc, mode)
	}
	if len(renshu) > 0 {
		desc = append(desc, renshu)
	}

	userInputDescFirst := GetUserInputDescFirst(blocks, userSelectedMap)
	if len(userInputDescFirst) > 0 {
		desc = append(desc, userInputDescFirst...)
	}
	return desc
}

func GetDescListByBusiness(blockOptions []*tcPb.BlockOption, blocks []*tabPB.Block, isInvite bool) (descList []string) {
	if len(blocks) == 0 {
		return descList
	}
	mobaMap, normalBlockMap, allSelectBlockMap, userSelectedMap := GetBlockTitleMap(blockOptions, blocks, "/")

	descList = getGangupBusinessDescListByView(blocks, mobaMap, normalBlockMap, allSelectBlockMap, userSelectedMap, isInvite)

	return
}

func handleMobaDescValue(key, val string) string {
	if val == "不限" {
		val += key
	}
	return val
}

func genDescAndColor(key, val string) (res string) {
	if val == "" {
		return ""
	}
	res = handleMobaDescValue(key, val)
	return
}

func GetUserInputDescFirst(blocks []*tabPB.Block, userSelectedMap map[uint32]map[uint32]BlockOptionVal) []string {
	// 加上新增字段的展示
	userInputDescFirst := make([]string, 0, 2)
	for _, b := range blocks {
		if b.GetMode() != tabPB.Block_USER_INPUT {
			continue
		}
		if userSelectedMap[b.GetId()] == nil {
			continue
		}

		for _, e := range b.GetElems() {
			if !userSelectedMap[b.GetId()][e.GetId()].IsSelectEd || userSelectedMap[b.GetId()][e.GetId()].Val == "" {
				continue
			}
			for _, f := range e.GetPublicFlags() {
				if f == tabPB.Elem_PublishFlagTopCardMode {
					userInputDescFirst = append(userInputDescFirst, genDescAndColor(e.GetTitle(), e.GetTitle()+":"+userSelectedMap[b.GetId()][e.GetId()].Val))
					break
				}
			}
		}
	}
	return userInputDescFirst
}

type optionVal struct {
	id  int    // 下标id
	val string // 用户发布输入的值
}

// GetBlockTitleMap 获取block字符串map
func GetBlockTitleMap(blockOptions []*tcPb.BlockOption, blocks []*tabPB.Block, sepStr string) (mobaTitleMap, titleMap map[string]string,
	allSelectBlockMap map[string]bool, userSelectedBlockMap map[uint32]map[uint32]BlockOptionVal) {
	//不限的block
	allSelectBlockMap = make(map[string]bool, len(blocks))
	//用户选择的
	userSelectedBlockMap = initUserChoseBlockIdMap(blockOptions)
	//系统预设的
	presetBlockMap := setPresetBlockMap(blocks)
	titleMap = make(map[string]string, 0)
	mobaTitleMap = make(map[string]string, 0)
	for _, block := range blocks {
		userSelectBlock := userSelectedBlockMap[block.GetId()]
		if len(userSelectBlock) < 1 {
			continue
		}
		//系统预设选项个数与用户勾选个数相同,且为全选
		if len(userSelectBlock) == len(presetBlockMap[block.GetId()]) && isAllSelect(block, userSelectBlock) {
			//titleMap[block.Title] = "不限"
			allSelectBlockMap[block.GetTitle()] = true
			if IsMobaBlock(block.GetTitle()) {
				mobaTitleMap[block.GetTitle()] = BlockNoLimit
			} else {
				titleMap[block.GetTitle()] = BlockNoLimit
			}
			continue
		}
		var optionVals []optionVal
		mostSelectNum := int(block.GetMostSelectNum())
		var userSelectedNum int
		for i, elem := range block.GetElems() {
			if block.GetMode() == tabPB.Block_USER_INPUT && userSelectedNum > InPutModeMaxElemNum { // 目前最多配置四项
				break
			}
			if (mostSelectNum > 0 && userSelectedNum >= mostSelectNum) || (mostSelectNum <= 0 &&
				block.GetMode() == tabPB.Block_SINGLE && userSelectedNum >= 1) {
				//配置了发布项最多选N项或者单选时，房间列表也只返回对应数量的展示项
				break
			}
			if userSelectBlock[elem.GetId()].IsSelectEd { //获取当前block下所有选中的elem数组的index（不是elem_id）即这个block都勾选了哪几位elem
				optionVals = append(optionVals, optionVal{
					id:  i,
					val: userSelectBlock[elem.Id].Val,
				})
				userSelectedNum++
			}
		}
		if len(optionVals) == 0 {
			continue
		}

		stringSet := genStringSet(block, optionVals)

		var str []string
		for _, slice := range stringSet {
			str = append(str, strings.Join(slice, "-"))
		}
		var title = strings.Join(str, sepStr)
		if IsMobaBlock(block.GetTitle()) {
			mobaTitleMap[block.GetTitle()] = title
		} else {
			titleMap[block.GetTitle()] = title
		}
	}

	return
}

func IsMobaBlock(title string) bool {
	if title == playServer || title == playerNum || title == playMode {
		return true
	}
	return false
}

// 系统预设的
func setPresetBlockMap(blocks []*tabPB.Block) map[uint32]map[uint32]*tabPB.Elem {
	var presetBlockMap = map[uint32]map[uint32]*tabPB.Elem{}
	for _, block := range blocks {
		for _, elem := range block.Elems {
			if presetBlockMap[block.Id] == nil {
				presetBlockMap[block.Id] = map[uint32]*tabPB.Elem{}
			}
			presetBlockMap[block.Id][elem.Id] = elem
		}
	}
	return presetBlockMap
}

// 判断用户是否全选
func isAllSelect(block *tabPB.Block, userSelectBlock map[uint32]BlockOptionVal) bool {
	if block.GetMode() == tabPB.Block_USER_INPUT {
		return false
	}
	allSelected := true
	for _, elem := range block.GetElems() { //检查用户勾选的选项是否与预设选项完全相同
		if !userSelectBlock[elem.GetId()].IsSelectEd { //用户未勾选该选项
			allSelected = false
			break
		}
	}
	return allSelected
}

// 处理elem
func genStringSet(block *tabPB.Block, optionVals []optionVal) [][]string {
	stringSet := make([][]string, 0)
	if block.Title == "段位" || block.Title == "段位要求" {
		var lastIdx = -1
		for _, val := range optionVals {
			if lastIdx == -1 { //将第一个elem的tittle存入stringSet的第一位数组，二维数组的x轴上拼入
				stringSet = append(stringSet, []string{block.Elems[val.id].Title})
			} else if lastIdx+1 == val.id { //如果该elem与上个存入stringSet的elem是连着的
				if len(stringSet[len(stringSet)-1]) < 2 { //stringSet最后一位的数组长度为0或者1
					stringSet[len(stringSet)-1] = append(stringSet[len(stringSet)-1], block.Elems[val.id].Title) //二维数组y轴上拼入
				} else {
					stringSet[len(stringSet)-1][1] = block.Elems[val.id].Title //二维数组y轴上替换第二位
				}
			} else {
				stringSet = append(stringSet, []string{block.Elems[val.id].Title})
			}
			lastIdx = val.id
		}
	} else {
		for _, val := range optionVals {
			if len(val.val) > 0 {
				stringSet = append(stringSet, []string{block.Elems[val.id].Title + ":" + val.val})
			} else {
				stringSet = append(stringSet, []string{block.Elems[val.id].Title})
			}
		}
	}
	return stringSet
}

// 开黑游戏房descList处理逻辑
func getGangupBusinessDescListByView(blocks []*tabPB.Block, mobaBlockMap, normalBlockMap map[string]string,
	allSelectBlockMap map[string]bool, userSelectedMap map[uint32]map[uint32]BlockOptionVal, isInvite bool) (descList []string) {
	descList = make([]string, 0, len(blocks))
	noLimitDescList := make([]string, 0, len(allSelectBlockMap))
	for _, b := range blocks {
		if b.GetMode() == tabPB.Block_USER_INPUT {
			continue
		}
		if allSelectBlockMap[b.GetTitle()] {
			noLimitDescList = append(noLimitDescList, b.GetTitle())
			continue
		}

		if isInvite {
			value, ok := mobaBlockMap[b.GetTitle()]
			if ok {
				descList = append(descList, b.Title+": "+value)
			}
		}
		if value, ok := normalBlockMap[b.GetTitle()]; ok {
			descList = append(descList, b.Title+": "+value)
			continue
		}

	}
	// 用户输入类型（战力评分）
	descList = append(descList, getUserInputDescList(blocks, userSelectedMap, isInvite)...)

	//没有选择了不限的
	if len(noLimitDescList) == 0 {
		return
	}

	noMobaDescList := make([]string, 0, len(noLimitDescList))
	for _, v := range noLimitDescList {
		if IsMobaBlock(v) {
			// moba view 不需要在noLimitDescList中处理不限
			continue
		}
		noMobaDescList = append(noMobaDescList, v)
	}
	if len(noMobaDescList) > 0 {
		maxBlockNum := NoLimitBlockDefaultNum
		if len(noMobaDescList) > maxBlockNum {
			noMobaDescList = noMobaDescList[:maxBlockNum]
		}
		descList = append(descList, strings.Join(noMobaDescList, "/")+": "+BlockNoLimit)
	}
	return
}

func getUserInputDescList(blocks []*tabPB.Block, userSelectedMap map[uint32]map[uint32]BlockOptionVal, isInvite bool) (descList []string) {
	// 加上新增字段的展示
	for _, b := range blocks {
		if b.GetMode() != tabPB.Block_USER_INPUT {
			continue
		}
		if userSelectedMap[b.GetId()] == nil {
			continue
		}

		elemDesc := ""
		for _, e := range b.GetElems() {
			if !userSelectedMap[b.GetId()][e.GetId()].IsSelectEd || userSelectedMap[b.GetId()][e.GetId()].Val == "" {
				continue
			}
			for _, f := range e.GetPublicFlags() {
				if f == tabPB.Elem_PublishFlagMiddleCardMode || (isInvite && f == tabPB.Elem_PublishFlagTopCardMode) {
					if elemDesc == "" {
						elemDesc = e.GetTitle() + ":" + userSelectedMap[b.GetId()][e.GetId()].Val
					} else {
						elemDesc += "/" + e.GetTitle() + ":" + userSelectedMap[b.GetId()][e.GetId()].Val
					}
					break
				}
			}
		}
		if elemDesc == "" {
			continue
		}
		descList = append(descList, b.Title+": "+elemDesc)
	}
	return
}

type BlockOptionVal struct {
	IsSelectEd bool
	Val        string
}

// 用户选择的发布字段选项
func initUserChoseBlockIdMap(blockOptions []*tcPb.BlockOption) map[uint32]map[uint32]BlockOptionVal {
	var userSelectedBlockMap = map[uint32]map[uint32]BlockOptionVal{}
	for _, blockOption := range blockOptions {
		if userSelectedBlockMap[blockOption.GetBlockId()] == nil {
			userSelectedBlockMap[blockOption.GetBlockId()] = map[uint32]BlockOptionVal{
				blockOption.GetElemId(): {
					IsSelectEd: true,
					Val:        FormatNumber(blockOption.GetElemVal()),
				}}
		} else {
			userSelectedBlockMap[blockOption.GetBlockId()][blockOption.GetElemId()] = BlockOptionVal{
				IsSelectEd: true,
				Val:        FormatNumber(blockOption.GetElemVal()),
			}
		}
	}
	return userSelectedBlockMap
}

func FormatNumber(val string) string {
	num, err := strconv.Atoi(val)
	if err != nil {
		return ""
	}
	if num < 1000 {
		return strconv.Itoa(num)
	} else if num < 10000 {
		if num%1000 < 100 {
			return fmt.Sprintf("%dk", num/1000)
		} else {
			return fmt.Sprintf("%.1fk", math.Floor(float64(num)/1000*10)/10)
		}
	} else if num < 100000 {
		if num%10000 < 1000 {
			return fmt.Sprintf("%dw", num/10000)
		} else {
			return fmt.Sprintf("%.1fw", math.Floor(float64(num)/10000*10)/10)
		}

	} else {
		return ""
	}
}
