package server

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	PushNotification "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app/channel"
	pb "golang.52tt.com/protocol/app/melee-channel"
	"golang.52tt.com/protocol/app/push"
	push_notification "golang.52tt.com/protocol/services/push-notification/v2"
	rpcClient "golang.52tt.com/services/melee-channel-logic/rpc/client"
	"time"
)

func pushMicWhiteListChangeMsg(ctx context.Context, channelId uint32, userList []uint32, actionType pb.MicWhiteListChangeMsg_ActionType) error {
	now := time.Now().Unix()
	content := &pb.MicWhiteListChangeMsg{
		ChannelId:  channelId,
		UidList:    userList,
		ActionType: actionType,
		UpdateAt:   now,
	}

	pbOptData, err := proto.Marshal(content)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushMicWhiteListChangeMsg Marshal err(%v)", err)
		return err
	}

	return pushUtil(ctx, channelId, uint32(channel.ChannelMsgType_MELEE_CHANNEL_MIC_WHITE_LIST_CHANGE), pbOptData, now)
}

func pushApplyListChangeMsg(ctx context.Context, channelId uint32, userList []uint32, actionType pb.MicWhiteApplyListChangeMsg_ActionType) error {
	countResp, err := rpcClient.MeleeChannelClient.GetMicWhiteApplyCount(ctx, channelId)
	total := int32(countResp.GetCount())
	if err != nil {
		log.ErrorWithCtx(ctx, "")
		total = -1
	}

	now := time.Now().Unix()
	content := &pb.MicWhiteApplyListChangeMsg{
		ChannelId:  channelId,
		UidList:    userList,
		ActionType: actionType,
		UpdateAt:   now,
		Total:      total,
	}

	pbOptData, err := proto.Marshal(content)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushApplyListChangeMsg Marshal err(%v)", err)
		return err
	}

	return pushUtil(ctx, channelId, uint32(channel.ChannelMsgType_MELEE_CHANNEL_MIC_WHITE_APPLY_CHANGE), pbOptData, now)
}

func pushMicWhiteListSwitchChangeMsg(ctx context.Context, channelId uint32, switchStatus pb.MeleeChannelWhiteListSwitchStatus) error {
	now := time.Now().Unix()
	content := &pb.WhiteListSwitchStatusChangeMsg{
		ChannelId:    channelId,
		SwitchStatus: switchStatus,
		UpdateAt:     now,
	}

	pbOptData, err := proto.Marshal(content)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushMicWhiteListSwitchChangeMsg Marshal err(%v)", err)
		return err
	}

	return pushUtil(ctx, channelId, uint32(channel.ChannelMsgType_MELEE_CHANNEL_MIC_WHITE_LIST_SWITCH_CHANGE), pbOptData, now)
}

func pushBatKickMicOptMsg(ctx context.Context, channelId, micMode uint32, allMicList []*channel.SimpleMicrSpace, kickMicUserList []*channel.KickMicUserInfo) error {
	now := time.Now().Unix()
	content := &channel.BatKickMicOpt{
		AllMicList:      allMicList,
		MicMode:         micMode,
		KickMicUserList: kickMicUserList,
	}
	pbOptData, err := proto.Marshal(content)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushBatKickMicOptMsg Marshal err(%v)", err)
		return err
	}

	return pushUtil(ctx, channelId, uint32(channel.ChannelMsgType_Bat_CHANNEL_KICKED_MIC), pbOptData, now)
}

func pushUtil(ctx context.Context, channelId, pushType uint32, pbOptData []byte, now int64) error {
	bMsg := channel.ChannelBroadcastMsg{
		ToChannelId:  channelId,
		Time:         uint64(now),
		Type:         pushType,
		PbOptContent: pbOptData,
		//Content:      []byte(pbOptData),
	}

	mb, marErr := bMsg.Marshal()
	if marErr != nil {
		log.ErrorWithCtx(ctx, "pushChannelMsgBro Marshal failed %+v err %+v", bMsg, marErr)
		return marErr
	}

	pMsg := push.PushMessage{
		Cmd:     uint32(push.PushMessage_CHANNEL_MSG_BRO),
		Content: mb,
	}

	mp, marErr := pMsg.Marshal()
	if marErr != nil {
		log.ErrorWithCtx(ctx, "pushChannelMsgBro Marshal failed %+v err %+v", pMsg, marErr)
		return marErr
	}

	notification := &push_notification.CompositiveNotification{
		Sequence: uint32(time.Now().Unix()),
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
		},
		AppId:              uint32(protocol.TT),
		TerminalTypePolicy: PushNotification.DefaultPolicy,
		ProxyNotification: &push_notification.ProxyNotification{
			Type:       uint32(push_notification.ProxyNotification_PUSH),
			Payload:    mp,
			Policy:     push_notification.ProxyNotification_DEFAULT,
			ExpireTime: 60,
			PushLabel:  channel.ChannelMsgType(pushType).String(),
		},
	}

	log.DebugWithCtx(ctx, "pushUtil info, channelId:%d, notification:%+v", channelId, notification)
	err := rpcClient.PushClient.PushMulticast(ctx, uint64(channelId), fmt.Sprintf("%d@channel", channelId), []uint32{}, notification)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushUtil pushv2Client.PushMulticast failed %v cid %d pushType:%d", err, channelId, pushType)
		return err
	}

	return nil
}

func pushChannelRoomApplyListChange(ctx context.Context, channelId uint32, userList []uint32) error {
	now := time.Now().Unix()
	content := &pb.ChannelRoomApplicantChangeNotify{
		ChannelId: channelId,
	}
	pbOptData, err := proto.Marshal(content)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushChannelRoomApplyListChange Marshal err:%s, content:%+v", err.Error(), content)
		return err
	}

	bMsg := channel.ChannelBroadcastMsg{
		ToChannelId:  channelId,
		Time:         uint64(now),
		Type:         uint32(channel.ChannelMsgType_MELEE_CHANNEL_ENTER_ROOM_APPLY),
		PbOptContent: pbOptData,
	}
	mb, marErr := bMsg.Marshal()
	if marErr != nil {
		log.ErrorWithCtx(ctx, "pushChannelRoomApplyListChange Marshal fail, err:%v, msg:%+v", marErr, bMsg)
		return marErr
	}

	pMsg := push.PushMessage{
		Cmd:     uint32(push.PushMessage_CHANNEL_MSG_BRO),
		Content: mb,
	}

	mp, marErr := pMsg.Marshal()
	if marErr != nil {
		log.ErrorWithCtx(ctx, "pushChannelRoomApplyListChange Marshal fail, err:%v, msg:%+v", marErr, pMsg)
		return marErr
	}
	//log.DebugWithCtx(ctx, "pushChannelRoomApplyListChange info, pMsg:%v, bMsg:%v, content:%v", pMsg, bMsg, content)
	return pushChannelUsers(ctx, userList, mp, now, PushNotification.LabelMeleeChannelWhiteListChange)
}

func pushChannelRoomWhitelistNotify(ctx context.Context, channelId, uid uint32, msg string, enterRoomType int32) error {
	now := time.Now().Unix()
	content := &pb.AddChannelRoomWhitelistNotify{
		ChannelId:     channelId,
		Uid:           uid,
		Msg:           msg,
		EnterRoomType: enterRoomType,
	}
	mb, err := proto.Marshal(content)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushChannelRoomWhitelistNotify Marshal fail, err:%s, content:%+v", err.Error(), content)
		return err
	}
	pMsg := push.PushMessage{
		Cmd:     uint32(push.PushMessage_ENTER_ROOM_APPLY_SUC_PUSH),
		Content: mb,
	}
	mp, marErr := pMsg.Marshal()
	if marErr != nil {
		log.ErrorWithCtx(ctx, "pushChannelRoomWhitelistNotify Marshal fail, err:%s, msg:%+v", marErr.Error(), pMsg)
		return marErr
	}
	//log.DebugWithCtx(ctx, "pushChannelRoomWhitelistNotify info, pMsg:%v, content:%v", pMsg, content)
	return pushChannelUsers(ctx, []uint32{uid}, mp, now, PushNotification.LabelMeleeChannelWhiteListChange)
}

func pushChannelUsers(ctx context.Context, userList []uint32, mp []byte, now int64, pushLabel string) error {
	notification := &push_notification.CompositiveNotification{
		Sequence: uint32(now),
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
		},
		AppId:              uint32(protocol.TT),
		TerminalTypePolicy: PushNotification.DefaultPolicy,
		ProxyNotification: &push_notification.ProxyNotification{
			Type:       uint32(push_notification.ProxyNotification_PUSH),
			Payload:    mp,
			Policy:     push_notification.ProxyNotification_DEFAULT,
			ExpireTime: 60,
			PushLabel:  pushLabel,
		},
	}

	log.DebugWithCtx(ctx, "PushToUsers info, userList:%v, notification:%+v", userList, notification)
	err := rpcClient.PushClient.PushToUsers(ctx, userList, notification)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushToUsers fail, err:%s, userList:%v, notification:%+v", err.Error(), userList, notification)
		return err
	}
	return nil
}
