// Code generated by MockGen. DO NOT EDIT.
// Source: ./accelerator_cfg_api.go

// Package mgr is a generated GoMock package.
package mgr

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	entity "golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_cfg/entity"
)

// MockIAcceleratorCfgMgr is a mock of IAcceleratorCfgMgr interface.
type MockIAcceleratorCfgMgr struct {
	ctrl     *gomock.Controller
	recorder *MockIAcceleratorCfgMgrMockRecorder
}

// MockIAcceleratorCfgMgrMockRecorder is the mock recorder for MockIAcceleratorCfgMgr.
type MockIAcceleratorCfgMgrMockRecorder struct {
	mock *MockIAcceleratorCfgMgr
}

// NewMockIAcceleratorCfgMgr creates a new mock instance.
func NewMockIAcceleratorCfgMgr(ctrl *gomock.Controller) *MockIAcceleratorCfgMgr {
	mock := &MockIAcceleratorCfgMgr{ctrl: ctrl}
	mock.recorder = &MockIAcceleratorCfgMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIAcceleratorCfgMgr) EXPECT() *MockIAcceleratorCfgMgrMockRecorder {
	return m.recorder
}

// DoFilter mocks base method.
func (m *MockIAcceleratorCfgMgr) DoFilter(game []*entity.Game) []*entity.Game {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DoFilter", game)
	ret0, _ := ret[0].([]*entity.Game)
	return ret0
}

// DoFilter indicates an expected call of DoFilter.
func (mr *MockIAcceleratorCfgMgrMockRecorder) DoFilter(game interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DoFilter", reflect.TypeOf((*MockIAcceleratorCfgMgr)(nil).DoFilter), game)
}

// GetAcceleratorGameList mocks base method.
func (m *MockIAcceleratorCfgMgr) GetAcceleratorGameList(ctx context.Context, lastTag string, forceInsertGameIds []uint32, limit uint32) ([]*entity.Game, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAcceleratorGameList", ctx, lastTag, forceInsertGameIds, limit)
	ret0, _ := ret[0].([]*entity.Game)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetAcceleratorGameList indicates an expected call of GetAcceleratorGameList.
func (mr *MockIAcceleratorCfgMgrMockRecorder) GetAcceleratorGameList(ctx, lastTag, forceInsertGameIds, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcceleratorGameList", reflect.TypeOf((*MockIAcceleratorCfgMgr)(nil).GetAcceleratorGameList), ctx, lastTag, forceInsertGameIds, limit)
}

// GetAcceleratorGameListWithOption mocks base method.
func (m *MockIAcceleratorCfgMgr) GetAcceleratorGameListWithOption(ctx context.Context, lastTag string, limit uint32, options *entity.GameSearchOptions) ([]*entity.Game, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAcceleratorGameListWithOption", ctx, lastTag, limit, options)
	ret0, _ := ret[0].([]*entity.Game)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetAcceleratorGameListWithOption indicates an expected call of GetAcceleratorGameListWithOption.
func (mr *MockIAcceleratorCfgMgrMockRecorder) GetAcceleratorGameListWithOption(ctx, lastTag, limit, options interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcceleratorGameListWithOption", reflect.TypeOf((*MockIAcceleratorCfgMgr)(nil).GetAcceleratorGameListWithOption), ctx, lastTag, limit, options)
}

// GetEntrance mocks base method.
func (m *MockIAcceleratorCfgMgr) GetEntrance(ctx context.Context) (*entity.EntranceCfg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEntrance", ctx)
	ret0, _ := ret[0].(*entity.EntranceCfg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntrance indicates an expected call of GetEntrance.
func (mr *MockIAcceleratorCfgMgrMockRecorder) GetEntrance(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntrance", reflect.TypeOf((*MockIAcceleratorCfgMgr)(nil).GetEntrance), ctx)
}

// GetGameByIds mocks base method.
func (m *MockIAcceleratorCfgMgr) GetGameByIds(ctx context.Context, gameIds []uint32) ([]*entity.Game, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameByIds", ctx, gameIds)
	ret0, _ := ret[0].([]*entity.Game)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameByIds indicates an expected call of GetGameByIds.
func (mr *MockIAcceleratorCfgMgrMockRecorder) GetGameByIds(ctx, gameIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameByIds", reflect.TypeOf((*MockIAcceleratorCfgMgr)(nil).GetGameByIds), ctx, gameIds)
}

// SyncGameScores mocks base method.
func (m *MockIAcceleratorCfgMgr) SyncGameScores(ctx context.Context, allGames []*entity.Game, userCountMap map[uint32]uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncGameScores", ctx, allGames, userCountMap)
	ret0, _ := ret[0].(error)
	return ret0
}

// SyncGameScores indicates an expected call of SyncGameScores.
func (mr *MockIAcceleratorCfgMgrMockRecorder) SyncGameScores(ctx, allGames, userCountMap interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncGameScores", reflect.TypeOf((*MockIAcceleratorCfgMgr)(nil).SyncGameScores), ctx, allGames, userCountMap)
}

// SyncGames mocks base method.
func (m *MockIAcceleratorCfgMgr) SyncGames(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncGames", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// SyncGames indicates an expected call of SyncGames.
func (mr *MockIAcceleratorCfgMgrMockRecorder) SyncGames(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncGames", reflect.TypeOf((*MockIAcceleratorCfgMgr)(nil).SyncGames), ctx)
}

// UpdateEntrance mocks base method.
func (m *MockIAcceleratorCfgMgr) UpdateEntrance(ctx context.Context, entrance *entity.EntranceCfg) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEntrance", ctx, entrance)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateEntrance indicates an expected call of UpdateEntrance.
func (mr *MockIAcceleratorCfgMgrMockRecorder) UpdateEntrance(ctx, entrance interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEntrance", reflect.TypeOf((*MockIAcceleratorCfgMgr)(nil).UpdateEntrance), ctx, entrance)
}

// UpdateGame mocks base method.
func (m *MockIAcceleratorCfgMgr) UpdateGame(ctx context.Context, game *entity.Game) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGame", ctx, game)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateGame indicates an expected call of UpdateGame.
func (mr *MockIAcceleratorCfgMgrMockRecorder) UpdateGame(ctx, game interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGame", reflect.TypeOf((*MockIAcceleratorCfgMgr)(nil).UpdateGame), ctx, game)
}
