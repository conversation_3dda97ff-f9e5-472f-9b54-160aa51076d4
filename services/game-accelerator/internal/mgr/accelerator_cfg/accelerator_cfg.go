package accelerator_cfg

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/game-accelerator"
	"golang.52tt.com/services/game-accelerator/internal/infra/db"
	"golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_cfg/cache"
	"golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_cfg/cache/redis"
	"golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_cfg/entity"
	"golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_cfg/mongo"
	third_open_api "golang.52tt.com/services/game-accelerator/internal/third-open-api"
	"math"
	"sort"
	"strconv"
)

const (
	appListPageSize     = 10
	recommendLabelScore = 200000000000 // 推荐标签分数
	hotLabelScore       = 100000000000// 热门标签分数
	userCountWeight     = 100000       // 用户数量权重，分数计算时乘以这个值
)

//go:generate ifacemaker -f *.go -s acceleratorCfgMgr -p accelerator_cfg -o ./accelerator_cfg_api.go -i IAcceleratorCfgMgr
//go:generate mockgen -destination=../mocks/accelerator_cfg.go -package=mgr -source ./accelerator_cfg_api.go IAcceleratorCfgMgr
type acceleratorCfgMgr struct {
	appId     string
	appSecret string
	appUrl    string

	cfgStore  mongo.IAcceleratorCfgStore
	gameCache cache.AcceleratorGameCache

	thirdOpenApi third_open_api.IThirdOpenApi
}

func NewAcceleratorCfgMgr(ctx context.Context, appId, appSecret, appUrl string, mongoDB *db.MongoDB,
	redisDB *db.RedisDB, thirdOpenApi third_open_api.IThirdOpenApi) (IAcceleratorCfgMgr, error) {

	cfgStore, err := mongo.NewAcceleratorCfgStore(ctx, mongoDB)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewAcceleratorCfgStore err: %v", err)
		return nil, err
	}
	acceleratorGameCache := redis.NewAcceleratorGameCache(redisDB)

	return &acceleratorCfgMgr{
		appId:        appId,
		appSecret:    appSecret,
		appUrl:       appUrl,
		cfgStore:     cfgStore,
		gameCache:    acceleratorGameCache,
		thirdOpenApi: thirdOpenApi,
	}, nil
}

func (m *acceleratorCfgMgr) GetEntrance(ctx context.Context) (*entity.EntranceCfg, error) {
	return m.cfgStore.GetEntrance(ctx)
}

func (m *acceleratorCfgMgr) UpdateEntrance(ctx context.Context, entrance *entity.EntranceCfg) error {
	return m.cfgStore.UpsertEntrance(ctx, entrance)
}

func (m *acceleratorCfgMgr) UpdateGame(ctx context.Context, game *entity.Game) error {
	//if game.State != pb.State_STATE_NORMAL {
	//	err := m.gameCache.RemoveFromActivePool(ctx, []uint32{game.Id})
	//	if err != nil {
	//		log.ErrorWithCtx(ctx, "UpdateGame RemoveFromActivePool gameId:%d error: %v", game.Id, err)
	//		return err
	//	}
	//}
	return m.cfgStore.UpdateGame(ctx, game)
}

func (m *acceleratorCfgMgr) getApiGames(ctx context.Context) ([]*entity.Game, error) {
	// 转换音频为文本
	curPage := 1
	var total int
	gameList := make([]*entity.Game, 0, 1000)
	for {
		request := &third_open_api.ApiAppsRequest{
			ApiBaseRequest: third_open_api.ApiBaseRequest{
				Appid:     m.appId,
				Appsecret: m.appSecret,
				OsType:    uint32(third_open_api.Windows),
			},
			PerPage: appListPageSize,
			Page:    curPage,
		}
		apiAppsRsp, err := m.thirdOpenApi.GetAppInfo(ctx, request)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetApiGames GetAppInfo failed request:%v, err:%v", request, err)
			return nil, err
		}
		total = apiAppsRsp.Data.Total
		if len(apiAppsRsp.Data.Items) > 0 {
			for _, item := range apiAppsRsp.Data.Items {
				gameList = append(gameList, item.ToEntityGame())
			}
		}
		if len(apiAppsRsp.Data.Items) < appListPageSize {
			break
		}
		curPage++
	}
	if len(gameList) == total {
		return gameList, nil
	} else {
		log.ErrorWithCtx(ctx, "GetApiGames total:%d, gameList len:%d not match", total, len(gameList))
		return nil, protocol.NewExactServerError(nil, status.ErrSys, fmt.Sprintf("GetApiGames total:%d, gameList len:%d not match", total, len(gameList)))
	}

}

// SyncGames 同步新游戏列表数据
func (m *acceleratorCfgMgr) SyncGames(ctx context.Context) error {

	// 1. 获取第三方最新的游戏列表
	allApiGames, err := m.getApiGames(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "SyncGames failed err:%v", err)
		return err
	}

	apiGameMap := make(map[uint32]*entity.Game, len(allApiGames))
	for _, game := range allApiGames {
		apiGameMap[game.Id] = game
	}

	newGameIds := make([]uint32, 0, len(allApiGames))
	for _, game := range allApiGames {
		newGameIds = append(newGameIds, game.Id)
	}

	// 2. 获取数据库中已有的游戏列表
	allDbGames, err := m.cfgStore.SearchGames(ctx, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "SyncGames SearchGames failed err:%v", err)
		return err
	}
	dbGameMap := make(map[uint32]*entity.Game, len(allDbGames))
	for _, game := range allDbGames {
		dbGameMap[game.Id] = game
	}

	// 3. 原有数据中状态为屏蔽的游戏，维持为屏蔽状态
	for _, game := range allApiGames {
		if dbGame, exists := dbGameMap[game.Id]; exists {
			if dbGame.State == pb.AcceleratorGameState_ACCELERATOR_GAME_STATE_BANNED {
				game.State = pb.AcceleratorGameState_ACCELERATOR_GAME_STATE_BANNED
			}
		}
	}
	// 4. 新同步的游戏列表里没有的游戏，设置为下线状态
	var needOffGameIds []uint32
	for _, game := range allDbGames {
		if _, exists := apiGameMap[game.Id]; !exists {
			// 如果数据库中的游戏不在API列表中，设置为下线状态
			game.State = pb.AcceleratorGameState_ACCELERATOR_GAME_STATE_OFF_SHELF
			allApiGames = append(allApiGames, game)
			needOffGameIds = append(needOffGameIds, game.Id)
		}
	}

	// 2. 同步修改第三方配置信息到db
	err = m.cfgStore.SyncGames(ctx, allApiGames)
	if err != nil {
		log.ErrorWithCtx(ctx, "SyncGames SyncGames failed newGameIds:%v, err:%v", newGameIds, err)
		return err
	}
	log.InfoWithCtx(ctx, "SyncGames success newGameIds:%v needOffGames:%v", newGameIds, needOffGameIds)
	return nil
}

// GetAcceleratorGameList 分页拉取列表，无搜素
func (m *acceleratorCfgMgr) GetAcceleratorGameList(ctx context.Context, lastTag string, forceInsertGameIds []uint32, limit uint32) ([]*entity.Game, string, error) {
	var newTag string
	// 查询多一条数据，分页标识使用下一页的第一条数据的分数
	searchCount := limit + 1
	// 从redis排序池获取游戏id列表
	gameScoreInfos, err := m.gameCache.GetGamesByScore(ctx, lastTag, searchCount)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAcceleratorGameList GetGamesByScore lastTag:%s limit:%d error: %v", lastTag, limit, err)
		return nil, newTag, err
	}
	gameIds := make([]uint32, 0, len(gameScoreInfos)+len(forceInsertGameIds))
	repeatedMap := make(map[uint32]struct{}, len(gameScoreInfos)+len(forceInsertGameIds))
	for _, gameScoreInfo := range gameScoreInfos {
		if _, exists := repeatedMap[gameScoreInfo.GameId]; exists {
			continue
		}
		gameIds = append(gameIds, gameScoreInfo.GameId)
		repeatedMap[gameScoreInfo.GameId] = struct{}{}
	}
	if len(forceInsertGameIds) > 0 {
		for _, id := range forceInsertGameIds {
			if _, exists := repeatedMap[id]; exists {
				continue
			}
			gameIds = append(gameIds, id)
		}
	}
	gameMap, err := m.cfgStore.BatGetGameMap(ctx, gameIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAcceleratorGameList BatGetGameMap gameIds:%v error: %v", gameIds, err)
		return nil, newTag, err
	}

	if len(gameScoreInfos) == int(searchCount) {
		newTag = strconv.Itoa(int(gameScoreInfos[len(gameScoreInfos)-1].Score))
	}
	games := make([]*entity.Game, 0, len(gameScoreInfos))
	// 重新排序
	alreadyAddedMap := make(map[uint32]struct{}, len(gameIds))
	// 先添加forceInsertGameIds中的游戏
	if len(forceInsertGameIds) > 0 {
		for _, id := range forceInsertGameIds {
			if _, exists := alreadyAddedMap[id]; exists {
				continue
			}
			if game, ok := gameMap[id]; ok {
				games = append(games, game)
				alreadyAddedMap[id] = struct{}{}
			}
		}
	}
	for i, gameScoreInfo := range gameScoreInfos {
		if _, exists := alreadyAddedMap[gameScoreInfo.GameId]; exists {
			continue
		}
		if game, ok := gameMap[gameScoreInfo.GameId]; ok {
			games = append(games, game)
			alreadyAddedMap[gameScoreInfo.GameId] = struct{}{}
		}
		//gameScoreInfo中的最后一条不返回，仅用作生成分页标记
		if i >= int(limit)-1 {
			// 如果已经达到限制数量，返回结果
			break
		}
	}
	return games, newTag, nil
}

// GetAcceleratorGameListWithOption 分页拉取游戏列表，有筛选条件
func (m *acceleratorCfgMgr) GetAcceleratorGameListWithOption(ctx context.Context, lastTag string, limit uint32,
	searchOptions *entity.GameSearchOptions) ([]*entity.Game, string, error) {

	// 查出所有符合条件的游戏
	games, err := m.cfgStore.SearchGames(ctx, searchOptions)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAcceleratorGameListWithOption SearchGames error: %v", err)
		return nil, "", err
	}
	if len(games) == 0 {
		return nil, "", nil
	}
	// 如果游戏数量超过限制，则进行分页处理
	gameIds := make([]uint32, 0, len(games))
	gameInfoMap := make(map[uint32]*entity.Game, len(games))
	for _, game := range games {
		gameIds = append(gameIds, game.Id)
		gameInfoMap[game.Id] = game
	}
	scoreInfos, err := m.gameCache.GetGameScores(ctx, gameIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAcceleratorGameListWithOption GetGameScores error: %v", err)
		return nil, "", err
	}
	if len(scoreInfos) == 0 {
		return nil, "", nil
	}
	// 将scoreInfos 按照分数降序排序
	sort.Slice(scoreInfos, func(i, j int) bool {
		return scoreInfos[i].Score > scoreInfos[j].Score
	})
	lastScore := math.MaxInt64
	if lastTag != "" {
		lastScore, err = strconv.Atoi(lastTag)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAcceleratorGameListWithOption ParseInt lastTag:%s error: %v", lastTag, err)
		}
	}
	var newTag string
	returnGames := make([]*entity.Game, 0, limit)
	// 根据分数排序后的游戏id列表
	var showedNum int
	for _, scoreInfo := range scoreInfos {
		showedNum++
		game, ok := gameInfoMap[scoreInfo.GameId]
		if ok && scoreInfo.Score <= int64(lastScore) {
			returnGames = append(returnGames, game)
		}
		if len(returnGames) >= int(limit) {
			// 如果已经达到限制数量，返回结果
			break
		}
	}
	if len(scoreInfos) > showedNum {
		// 还有未展示的
		newTag = strconv.Itoa(int(scoreInfos[showedNum].Score))
	}
	return returnGames, newTag, nil
}

func (m *acceleratorCfgMgr) SyncGameScores(ctx context.Context, allGames []*entity.Game, userCountMap map[uint32]uint32) error {

	gameScoreMap := make(map[uint32]int64, len(allGames))
	for _, game := range allGames {
		// 计算分数
		userCount := userCountMap[game.Id]
		gameScoreMap[game.Id] = m.calculateScore(game, userCount)
	}
	err := m.gameCache.AddToActivePool(ctx, gameScoreMap)
	if err != nil {
		log.ErrorWithCtx(ctx, "SyncGameScores AddToActivePool error: %v", err)
		return err
	}
	return nil
}

// 计算公式 分数 = 推荐标签权重+热门标签权重+近14天加速过的用户数量*权重+游戏id 优先级推荐>热门 优先级相同，按近14天的加速人数
// 当标签、加速用户数量相同时，按游戏id降序排
func (m *acceleratorCfgMgr) calculateScore(game *entity.Game, userCount uint32) int64 {
	score := int64(userCount)*userCountWeight + int64(game.Id)%userCountWeight
	if len(game.Labels) > 0 {
		for _, label := range game.Labels {
			if label == pb.AcceleratorLabel_LABEL_RECOMMEND {
				score += recommendLabelScore
			} else if label == pb.AcceleratorLabel_LABEL_HOT {
				score += hotLabelScore
			} else {
				continue
			}
		}
	}
	return score
}

// 过滤逻辑
func (m *acceleratorCfgMgr) DoFilter(game []*entity.Game) []*entity.Game {

	filteredGames := make([]*entity.Game, 0, len(game))
	for _, g := range game {
		if g.State != pb.AcceleratorGameState_ACCELERATOR_GAME_STATE_NORMAL {
			continue // 只保留正常状态的游戏
		}
		filteredGames = append(filteredGames, g)
	}
	return filteredGames
}

func (m *acceleratorCfgMgr) GetGameByIds(ctx context.Context, gameIds []uint32) ([]*entity.Game, error) {

	gameMap, err := m.cfgStore.BatGetGameMap(ctx, gameIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameByIds BatGetGames gameIds:%v error: %v", gameIds, err)
		return nil, err
	}
	games := make([]*entity.Game, 0, len(gameMap))
	if len(gameMap) == 0 {
		return games, nil // 如果没有找到游戏，返回空切片
	}
	for _, v := range gameIds {
		if game, ok := gameMap[v]; ok {
			games = append(games, game)
		}
	}
	return games, nil
}
