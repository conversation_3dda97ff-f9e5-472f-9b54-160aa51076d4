package post_mgr

import (
	"context"
	channel_play_tab "golang.52tt.com/protocol/services/channel-play-tab"
	"testing"

	"github.com/golang/mock/gomock"

	gameUgcMock "golang.52tt.com/clients/mocks/game-ugc-content"
	iop "golang.52tt.com/clients/mocks/iop-proxy"
	"golang.52tt.com/clients/ugc/vote"
	game_ugc_content "golang.52tt.com/protocol/services/game-ugc-content"
	pb "golang.52tt.com/protocol/services/game-ugc-middle"
	iop_pb "golang.52tt.com/protocol/services/iop-proxy"
	votePB "golang.52tt.com/protocol/services/ugc/vote"
	"golang.52tt.com/services/game-ugc-middle/internal/client"
)

func ClientTestInit(gameUgcMock *gameUgcMock.MockIClient, ugcVoteMock *vote.MockIClient, iopMock *iop.MockIClient, channelPlayTabMock *channel_play_tab.MockChannelPlayTabClient) {
	//AccountGoCli = accountGo.NewIClient()
	//FrequencyClient = frequence.NewClient()
	//ContentClient, _ = content.NewClient()
	client.VoteCli = ugcVoteMock
	//CensorsProxyClient = cybros_arbiter.NewIClient()
	//DarkCli = darkserver.NewIClient()
	//FollowStreamBreakClient, _ = following_stream.NewClient()
	//BanuserClient = banuser.NewClient()
	client.GameUgcContentClient = gameUgcMock
	client.ChannelPlayTabClient = channelPlayTabMock
	client.IopCli = iopMock
}

func TestGetConfigTabTitle(t *testing.T) {
	type args struct {
		ctx         context.Context
		configTabId string
	}
	tests := []struct {
		name       string
		args       args
		wantTitles []string
	}{
		{
			name: "GetConfigTabTitle",
			args: args{
				ctx:         context.Background(),
				configTabId: "1",
			},
			wantTitles: []string{"1", "2", "3", "5", "4"},
		},
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	gameUgcMock := gameUgcMock.NewMockIClient(ctl)

	ClientTestInit(gameUgcMock, nil, nil, nil)

	gomock.InOrder(
		gameUgcMock.EXPECT().GetConfigTabInfoById(gomock.Any(), gomock.Any()).Return(&game_ugc_content.GetConfigTabInfoByIdResp{
			ConfigTabInfo: &game_ugc_content.ConfigTabInfo{
				ConfigTabId: "1",
				Titles:      []string{"1", "2", "3", "5", "4"},
			},
		}, nil),
	)
	//ctx := grpc.WithServiceInfo(context.Background(), &grpc.ServiceInfo{
	//	ClientType:    0,
	//	TerminalType:  0,
	//	UserID:        1,
	//	ClientVersion: 6,
	//})

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotTitles := GetConfigTabTitle(tt.args.ctx, tt.args.configTabId)
			t.Logf("titles:%v", gotTitles)
			if len(gotTitles) != len(tt.wantTitles) {
				t.Errorf("GetConfigTabTitle() = %v, want %v", gotTitles, tt.wantTitles)
			}
		})
	}
}

func Test_handleVote(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	voteMock := vote.NewMockIClient(ctl)

	ClientTestInit(nil, voteMock, nil, nil)

	gomock.InOrder(
		voteMock.EXPECT().CreateVote(gomock.Any(), gomock.Any()).Return(&votePB.CreateVoteRsp{}, nil),
	)

	type args struct {
		ctx    context.Context
		uid    uint32
		postId string
		vote   *pb.VoteInfo
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "handleVote",
			args: args{
				ctx:    context.Background(),
				uid:    1,
				postId: "1",
				vote: &pb.VoteInfo{
					Uid:       1,
					Title:     "1",
					ExpiryT:   0,
					ExpiredAt: 0,
					OptionId:  "",
					Options:   nil,
					Votes:     0,
					VoteT:     0,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := handleVote(tt.args.ctx, tt.args.uid, tt.args.postId, tt.args.vote); (err != nil) != tt.wantErr {
				t.Errorf("handleVote() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCheckUserIsBannedPost(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	gameUgcMock := gameUgcMock.NewMockIClient(ctl)
	iopMock := iop.NewMockIClient(ctl)
	channelPlayTabMock := channel_play_tab.NewMockChannelPlayTabClient(ctl)

	ClientTestInit(gameUgcMock, nil, iopMock, channelPlayTabMock)

	MatchGroupResp := []*iop_pb.MatchGroupResult{{
		Uid: uint64(1),
		Result: map[string]string{
			"crowd1": "0",
			"crowd2": "1",
		},
	}}

	gomock.InOrder(
		// case1: ban user
		channelPlayTabMock.EXPECT().GetActiveBanUserConfigWithCache(gomock.Any(), gomock.Any()).Return(&channel_play_tab.GetActiveBanUserConfigWithCacheResp{
			Configs: []*channel_play_tab.ActiveBanUserConfig{{
				ConfigType: channel_play_tab.BanConfigType_BanConfigTypeUser,
				Uid:        1,
				TabId:      1,
				BanReason:  "ban user",
			}},
		}, nil),
		// case1: ban crowd 2
		channelPlayTabMock.EXPECT().GetActiveBanUserConfigWithCache(gomock.Any(), gomock.Any()).Return(&channel_play_tab.GetActiveBanUserConfigWithCacheResp{
			Configs: []*channel_play_tab.ActiveBanUserConfig{{
				ConfigType:   channel_play_tab.BanConfigType_BanConfigTypeCrowdGroup,
				CrowdGroupId: "crowd1",
				TabId:        1,
				BanReason:    "ban crowd 1",
			}, {
				ConfigType:   channel_play_tab.BanConfigType_BanConfigTypeCrowdGroup,
				CrowdGroupId: "crowd2",
				TabId:        1,
				BanReason:    "ban crowd 2",
			}},
		}, nil),
		iopMock.EXPECT().MatchGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(MatchGroupResp, nil),
	)
	type args struct {
		ctx         context.Context
		tabId       uint32
		uid         uint32
		banPostType uint32
	}
	tests := []struct {
		name             string
		args             args
		wantIsBanned     bool
		wantBannedReason string
		wantErr          bool
	}{
		{
			name: "ban user",
			args: args{
				ctx:   context.Background(),
				tabId: 1,
				uid:   1,
			},
			wantIsBanned:     true,
			wantBannedReason: "ban user",
			wantErr:          false,
		},
		{
			name: "ban crowd 2",
			args: args{
				ctx:   context.Background(),
				tabId: 1,
				uid:   1,
			},
			wantIsBanned:     true,
			wantBannedReason: "ban crowd 2",
			wantErr:          false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotIsBanned, gotBannedReason, err := CheckUserIsBannedPost(tt.args.ctx, tt.args.tabId, tt.args.uid, tt.args.banPostType)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckUserIsBannedPost() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotIsBanned != tt.wantIsBanned {
				t.Errorf("CheckUserIsBannedPost() gotIsBanned = %v, want %v", gotIsBanned, tt.wantIsBanned)
			}
			if gotBannedReason != tt.wantBannedReason {
				t.Errorf("CheckUserIsBannedPost() gotBannedReason = %v, want %v", gotBannedReason, tt.wantBannedReason)
			}
		})
	}
}
