package post_mgr

import (
	"context"
	"encoding/hex"
	"fmt"
	"golang.52tt.com/pkg/iplocation"
	channel_play_tab "golang.52tt.com/protocol/services/channel-play-tab"
	"math/rand" //#nosec
	"sort"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"

	cybros_arbiter "golang.52tt.com/clients/censoring-proxy"
	"golang.52tt.com/clients/frequence"
	"golang.52tt.com/clients/ugc/content"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/sentinel"
	ga_ugc "golang.52tt.com/protocol/app/ugc"
	"golang.52tt.com/protocol/common/status"
	account_go "golang.52tt.com/protocol/services/account-go"
	censoring_proxy "golang.52tt.com/protocol/services/cybros/arbiter/v2"
	game_pal "golang.52tt.com/protocol/services/game-pal"
	gameContentPB "golang.52tt.com/protocol/services/game-ugc-content"
	pb "golang.52tt.com/protocol/services/game-ugc-middle"
	contentPB "golang.52tt.com/protocol/services/ugc/content"
	ope_config "golang.52tt.com/protocol/services/ugc/ope-config"
	topicPb "golang.52tt.com/protocol/services/ugc/topic"
	votePb "golang.52tt.com/protocol/services/ugc/vote"
	"golang.52tt.com/services/game-ugc-middle/internal/client"
	"golang.52tt.com/services/game-ugc-middle/internal/common/contentfmt"
)

const (
	timeLayout = "2006-01-02 15:04:05"

	protoMarshalType   = 1
	originGameDistrict = 1
	gameBusinessType   = 1
	InCrowdGroup       = "1"

	gamePalGreetingLimitToast = "今日打招呼次数用光啦，明天再来吧"
)

func UserBehaviorCheckV3(ctx context.Context, uid uint32) error {
	hit, err := client.DarkCli.UserBehaviorCheck(ctx, uid)
	if err == nil && hit == uid {
		log.InfoWithCtx(ctx, "UserBehaviorCheck userid - %d hit", uid)
		return protocol.NewExactServerError(nil, status.ErrAccountAbnormalFunctionInvalid)
	}
	return nil
}

func getLoc(ctx context.Context, serviceInfo metainfo.ServiceInfoReader) *contentPB.Location {
	userIp := serviceInfo.ClientIPAddr().String()
	loc, err := iplocation.GetIpLocation(ctx, userIp)
	if err != nil {
		log.WarnWithCtx(ctx, "getLoc GetIpLocation ip(%s) err: %v", userIp, err)
		return nil
	}
	if loc != nil {
		ipLoc := &contentPB.Location{
			CountryCode:  loc.Country.Code,
			Country:      loc.Country.Name,
			ProvinceCode: loc.Province.Code,
			Province:     loc.Province.Name,
			CityCode:     loc.City.Code,
			City:         loc.City.Name,
		}
		return ipLoc
	}
	return nil
}

func DoPost(ctx context.Context, in *pb.PostPostRequest) (resp *contentPB.AddPostResp, err error) {
	serviceInfo := metainfo.GetServiceInfo(ctx)

	uid := serviceInfo.UserID()

	// 限频
	if err = isReachFreqLimit(ctx, uid); err != nil {
		return nil, err
	}
	// 用户校验
	if err = UserBehaviorCheckV3(ctx, uid); err != nil {
		log.ErrorWithCtx(ctx, "PostPost uid: %d Failed: %v", uid, err)
		return nil, err
	}
	//用户获取
	user, err := client.AccountGoCli.GetUserByUid(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "PostPost GetUser uid(%d) err: %v", uid, err)
		return nil, err
	}

	var contentType int32
	info := contentfmt.ResolveContent(in.Content)
	if len(info) > 0 {
		contentType = int32(ga_ugc.ContentType_FORMATTED)
	}

	// 根据config_tab_id获取对应defaultTopic
	configTabInfo, err := client.GameUgcContentClient.GetConfigTabInfoById(ctx, &gameContentPB.GetConfigTabInfoByIdReq{
		ConfigTabId: in.GetConfigTabId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GameUgcContentClient.GetConfigTabInfoById fail, uid:%d, configTabId:%d, err: %s",
			uid, in.GetConfigTabId(), err.Error())
		return nil, err
	}
	businessInfo, _ := proto.Marshal(&gameContentPB.GamePostBussInfo{
		TabId:       in.GetTabId(),
		ConfigTabId: configTabInfo.GetConfigTabInfo().GetConfigTabId(),
		Title:       in.GetTitle(),
	})

	var extra *contentPB.Extra
	if in.GetExtra() != nil {
		extra = &contentPB.Extra{
			Width:  in.GetExtra().GetWidth(),
			Heigth: in.GetExtra().GetHeigth(),
		}
	}

	loc := getLoc(ctx, serviceInfo)

	topicId := in.GetTopicId()
	if len(topicId) == 0 {
		for _, topic := range configTabInfo.GetConfigTabInfo().GetBindTopics() {
			if topic.GetIsDefault() {
				topicId = topic.GetTopicId()
				break
			}
		}
	}
	log.DebugWithCtx(ctx, "PostPost in, uid:%d, configTabId:%s, in.topicId:%s, post topicId:%s", uid, in.GetConfigTabId(), in.GetTopicId(), topicId)
	req := &contentPB.AddPostReq{
		UserId:                uid,
		Type:                  contentPB.PostInfo_PostType(in.GetType()),
		Content:               in.GetContent(),
		AttachmentImageCount:  in.GetAttachmentImageCount(),
		AttachmentVideoCount:  in.GetAttachmentVideoCount(),
		AttachmentAudioCount:  in.GetAttachmentAudioCount(),
		Status:                content.Suspicious,
		DeviceId:              hex.EncodeToString(serviceInfo.DeviceID()), // 给数据中心
		Platform:              uint32(serviceInfo.ClientType()),           // 给数据中心
		ContentType:           contentType,
		PostOrigin:            contentPB.PostOrigin(in.GetOrigin()),
		PredefinedAttachments: getPredefinedAttachments(in.GetPredefinedAttachments()),
		Ttid:                  fmt.Sprintf("帐号：%s", user.GetAlias()), //目前用在视频水印上 可能在其他也很會用上 拼接上tt号
		Privacy:               contentPB.AttachmentDownloadPrivacy(in.Privacy),
		IsVote:                in.GetVote() != nil, // 帖子是否带有投票信息
		ClientIp:              serviceInfo.ClientIP(),
		MarketId:              serviceInfo.MarketID(),
		Extra:                 extra,
		IsObs:                 true,
		Title:                 in.GetTitle(),
		BusinessType:          gameBusinessType, //业务类型  0:通用广场 1:开黑 -> BusinessType
		CommonTopicInfos: []*contentPB.CommonTopicInfo{
			{
				TopicIds:  []string{topicId},
				TopicType: uint32(topicPb.TopicType_TypeGameDistrict),
			},
		},
		GeneralContents: []*contentPB.GeneralContent{{
			UnmarshalType: protoMarshalType,           //UnmarshalType UNMARSHAL_TYPE_PROTOBUF
			ContentOrigin: uint32(in.GetGameOrigin()), //ContentOrigin ORIGIN_GAME_DISTRICT
			Content:       businessInfo,
		}},
		ZonePolicy: contentPB.ZonePolicy(in.GetVisibleScope()),
	}
	if loc != nil {
		req.IpLoc = loc
	}
	resp, err = client.ContentClient.AddPost(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "PostPost err:%v, req:%+v, configTabInfo:%v", err, req, configTabInfo.GetConfigTabInfo())
		return
	}

	// 投票处理
	if err = handleVote(ctx, uid, resp.GetPostId(), in.GetVote()); err != nil {
		log.ErrorWithCtx(ctx, "PostPost handle vote err:%v, uid:%d, postId:%s, vote:%v", err, uid, resp.GetPostId(), in.GetVote())
		return
	}

	// 帖子添加跳转链接
	if err = addConfigTabPostTeleport(ctx, resp.GetPostId(), in.GetConfigTabId()); err != nil {
		log.WarnWithCtx(ctx, "PostPost addConfigTabPostTeleport uid(%d) postId(%s) err: %v", uid, resp.GetPostId(), err)
	}

	// 审核
	hasAttachment := in.GetAttachmentImageCount() > 0 || in.GetAttachmentAudioCount() > 0 || in.GetAttachmentVideoCount() > 0
	if !hasAttachment {
		_ = audiPost(ctx, resp.GetPostId(), in.GetTitle(), in.GetContent(), user, in.GetVote())
	}

	// 设置发帖频率
	go setFrequency(uid)

	log.InfoWithCtx(ctx, "PostPost out, uid:%d, post id:%s", uid, resp.GetPostId())
	return
}

func getPredefinedAttachments(attachments []*pb.PostAttachment) []*contentPB.AttachmentInfo {
	predefinedAttachments := make([]*contentPB.AttachmentInfo, 0, len(attachments))
	var t contentPB.AttachmentInfo_AttachmentType
	for _, m := range attachments {
		switch m.GetType() {
		case pb.PostAttachment_TYPE_AUDIO:
			t = contentPB.AttachmentInfo_AUDIO
		case pb.PostAttachment_TYPE_VIDEO:
			t = contentPB.AttachmentInfo_VIDEO
		case pb.PostAttachment_TYPE_IMAGE:
			t = contentPB.AttachmentInfo_IMAGE
		case pb.PostAttachment_TYPE_CMS:
			t = contentPB.AttachmentInfo_CMS
		case pb.PostAttachment_TYPE_GIF:
			t = contentPB.AttachmentInfo_GIF
		default:
			t = contentPB.AttachmentInfo_NONE
		}

		predefinedAttachments = append(predefinedAttachments, &contentPB.AttachmentInfo{
			Type:    t,
			Content: m.GetContent(),
			Extra:   m.GetExtraInfo(),
			Param:   m.GetParam(),
		})
	}
	return predefinedAttachments
}

func isReachFreqLimit(ctx context.Context, uid uint32) (err error) {
	freKey := strconv.Itoa(int(uid))
	ok, _, err := client.FrequencyClient.CheckFrequenceOk(ctx, uid, frequence.UgcPostPostCooldown, freKey)
	if err != nil {
		log.ErrorWithCtx(ctx, "PostPost: %d CheckFrequenceOk failed: %v", uid, err)
		if !sentinel.IsBreakerServerErr(protocol.ToServerError(err)) {
			return
		}
		return nil
	}
	if !ok {
		err = protocol.NewExactServerError(nil, status.ErrUgcPostPostCooldown)
		log.ErrorWithCtx(ctx, "PostPost: %d Frequency CoolDown limited", uid)
		return
	}
	return
}

func setFrequency(uid uint32) {
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	// frequency
	freKey := strconv.Itoa(int(uid))
	err := client.FrequencyClient.SetFrequence(ctx, uid, frequence.UgcPostPostCooldown, freKey)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetFrequency fail, uid:%d, err:%s", uid, err.Error())
	}
}

func handleVote(ctx context.Context, uid uint32, postId string, vote *pb.VoteInfo) error {
	if vote == nil {
		return nil
	}
	vote.Uid = uid
	if vote.GetVoteT() == pb.VoteInfo_VoteTNone {
		vote.VoteT = pb.VoteInfo_VoteTText
	}

	v := &votePb.Vote{
		PostId:    postId,
		Uid:       vote.GetUid(),
		ExpiryT:   votePb.Vote_ExpiryT(vote.GetExpiryT()),
		ExpiredAt: vote.GetExpiredAt(),
		Options:   []*votePb.Option{},
		Title:     vote.GetTitle(),
		VoteT:     votePb.Vote_VoteT(vote.GetVoteT()),
	}

	for _, opt := range vote.GetOptions() {
		v.Options = append(v.Options, &votePb.Option{
			ContentT: votePb.Option_ContentT(opt.GetOptionT()),
			Content:  opt.GetContent(),
			Votes:    opt.GetVotes(),
		})
	}

	resp, err := client.VoteCli.CreateVote(ctx, &votePb.CreateVoteReq{Vote: v})
	if err != nil {
		log.ErrorWithCtx(ctx, "contentLogicServer.createVote postId(%s) vote(%v) err: %v", postId, vote, err)
		return err
	}

	newVote := resp.GetVote()
	if newVote != nil {
		for i, option := range newVote.GetOptions() {
			vote.GetOptions()[i].Id = option.GetId()
		}
	}

	return nil
}

func audiPost(ctx context.Context, postId, title, content string, user *account_go.UserResp, vote *pb.VoteInfo) error {
	reqPostAudit := &censoring_proxy.CensoringReq{
		DataType: censoring_proxy.MixDataType_DATA_TYPE_TEXT,
		Context: &censoring_proxy.TaskContext{
			AppId:       cybros_arbiter.AppId,
			BelongObjId: user.GetAlias(),
			Scenes: []censoring_proxy.Scene{
				censoring_proxy.Scene_SCENE_DEFAULT,
			},
			Category:   cybros_arbiter.CategoryUgcPost,
			SourceInfo: cybros_arbiter.CategoryUgcPost,
			Source: &censoring_proxy.SourceInfo{
				Id: postId,
			},
			BizRecordId: postId,
			UserInfo: &censoring_proxy.User{
				Id:       uint64(user.GetUid()),
				Alias:    user.GetAlias(),
				Nickname: user.GetNickname(),
			},
			SceneCreateTime: time.Now().Format(timeLayout),
		},
	}
	reqPostAudit.Callback = &censoring_proxy.Callback{
		Params: make(map[string]string),
	}
	reqPostAudit.Callback.Params["quicksilver"] = "new"

	plainText := contentfmt.FormatPlainText(content)
	reqPostAudit.TextData = append(reqPostAudit.TextData, &censoring_proxy.TextData{
		Metadata: &censoring_proxy.Metadata{
			DataId: postId,
			Name:   "post",
		},
		Content: plainText + ";" + title,
	})

	if vote != nil {
		reqPostAudit.DataType = censoring_proxy.MixDataType_DATA_TYPE_MULTI_MEDIA

		texts := []*censoring_proxy.TextData{
			{
				Metadata: &censoring_proxy.Metadata{DataId: fmt.Sprintf("vote:%s", postId)},
				Content:  vote.GetTitle(),
			},
		}

		for _, option := range vote.GetOptions() {
			texts = append(texts, &censoring_proxy.TextData{
				Metadata: &censoring_proxy.Metadata{DataId: fmt.Sprintf("option:%s", option.GetId())},
				Content:  option.GetContent(),
			})
		}

		reqPostAudit.MultiMediaData = &censoring_proxy.MultiMediaData{TextDatas: texts}
	}

	auditResp, err := client.CensorsProxyClient.Censoring().AsyncScanMix(ctx, reqPostAudit)
	if err != nil {
		log.ErrorWithCtx(ctx, "AsyncScanMix post err:%v,  req:%+v", err, reqPostAudit)
		return err
	}
	//metrics.IncAuditCode(metrics.AuditPostText)
	log.InfoWithCtx(ctx, "AsyncScanMix post suc, req:%+v resp:%+v", reqPostAudit, auditResp)
	return nil
}

func GetConfigTabTitle(ctx context.Context, configTabId string) (titles []string) {
	configTabInfo, err := client.GameUgcContentClient.GetConfigTabInfoById(ctx, &gameContentPB.GetConfigTabInfoByIdReq{
		ConfigTabId: configTabId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetConfigTabTitle GetConfigTabInfoById fail, configTabId:%d, err: %s", configTabId, err.Error())
		return
	}
	titles = configTabInfo.GetConfigTabInfo().GetTitles()
	sort.Slice(titles, func(i, j int) bool {
		return rand.Intn(2) == 0 //#nosec
	})
	return
}

// CheckUserIsBannedPost banPostType: 1-专区禁止用户在某个玩法下发帖 2-禁止用户发布搭子卡/私聊
func CheckUserIsBannedPost(ctx context.Context, tabId, uid, banPostType uint32) (isBanned bool, bannedReason string, err error) {
	switch gameContentPB.BanPostType(banPostType) {
	case gameContentPB.BanPostType_BanPostTypeGamePalCard: // 搭子打招呼限制
		greetingResp, err := client.GamePalClient.GetUserGreeting(ctx, &game_pal.GetUserGreetingReq{Uid: uid})
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckUserIsBannedPost GetUserGreeting uid(%d) err: %v", uid, err)
			return false, "", err
		}

		greetingInfo := greetingResp.GetGreetingInfo()
		if greetingInfo.GetCount() >= greetingInfo.GetLimit() {
			return true, gamePalGreetingLimitToast, nil
		}
	default:
		log.DebugWithCtx(ctx, "CheckUserIsBannedPost banPostType %d skip", banPostType)
	}

	banPostInfo, err := client.ChannelPlayTabClient.GetActiveBanUserConfigWithCache(ctx, &channel_play_tab.GetActiveBanUserConfigWithCacheReq{
		TabId:       tabId,
		Uid:         uid,
		BanPostType: channel_play_tab.BanPostType(banPostType),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckUserIsBannedPost GetActiveBanUserConfigWithCache fail, tabId:%d, uid:%d, err:%v", tabId, uid, err)
		return false, "", protocol.NewExactServerError(nil, status.ErrSys)
	}

	log.DebugWithCtx(ctx, "CheckUserIsBannedPost ChannelPlayTabClient.GetActiveBanUserConfigWithCache uid:%d, banPostConfigs:%+v", uid, banPostInfo.GetConfigs())

	// 没有用户相关的禁止发帖配置
	if len(banPostInfo.GetConfigs()) == 0 {
		return
	}

	banUserPostConfigs := banPostInfo.GetConfigs()
	crowdGroupIds := make([]string, 0, len(banUserPostConfigs))
	for _, banUserPostConfig := range banUserPostConfigs {
		switch banUserPostConfig.GetConfigType() {
		// 如果有禁止用户发帖配置，直接返回
		case channel_play_tab.BanConfigType_BanConfigTypeUser:
			return true, banUserPostConfig.GetBanReason(), nil
		case channel_play_tab.BanConfigType_BanConfigTypeCrowdGroup:
			crowdGroupIds = append(crowdGroupIds, banUserPostConfig.GetCrowdGroupId())
		default:
			log.InfoWithCtx(ctx, "CheckUserIsBannedPost invalid BanConfigType:%v", banUserPostConfig.GetConfigType())
		}
	}

	// 没有禁止用户发帖配置，需要查询有没有包含用户在内的人群包被禁止发帖
	crowdResults, err := getMatchGroupInfo(ctx, uint64(uid), crowdGroupIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckUserIsBannedPost getMatchGroupInfo err: %v", err)
		return false, "", protocol.NewExactServerError(nil, status.ErrSys)
	}

	// 查找第一个用户所在人群包禁止发帖配置
	for _, banUserPostConfig := range banUserPostConfigs {
		if banUserPostConfig.GetConfigType() == channel_play_tab.BanConfigType_BanConfigTypeCrowdGroup &&
			crowdResults[banUserPostConfig.GetCrowdGroupId()] == InCrowdGroup {
			return true, banUserPostConfig.GetBanReason(), nil
		}
	}

	return
}

func getMatchGroupInfo(ctx context.Context, uid uint64, crowdGroupIds []string) (map[string]string, error) {
	log.DebugWithCtx(ctx, "getMatchGroupInfo, uid:%d, crowdGroupIds:%v", uid, crowdGroupIds)
	if len(crowdGroupIds) == 0 {
		return nil, nil
	}
	matchGroupResp, err := client.IopCli.MatchGroup(ctx, []uint64{uid}, crowdGroupIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "getMatchGroupInfo IopCli.MatchGroup err: %v", err)
		return nil, err
	}
	if len(matchGroupResp) == 0 {
		log.WarnWithCtx(ctx, "getMatchGroupInfo, uid:%d, crowdGroupIds:%v, matchGroupResp is nil", uid, crowdGroupIds)
		return nil, nil
	}
	log.InfoWithCtx(ctx, "getMatchGroupInfo uid:%d, crowdGroupIds:%+v, MatchGroupResp:%+v", uid, crowdGroupIds, matchGroupResp)
	return matchGroupResp[0].Result, nil
}

func addConfigTabPostTeleport(ctx context.Context, postId, configTabId string) error {
	/*
	   teleport, exist := config.GetFeedsConfig().GetConfigTabPostTeleport(configTabId)
	   if !exist {
	       log.DebugWithCtx(ctx, "addConfigTabPostTeleport configTabId(%s) miss teleport", configTabId)
	       return nil
	   }*/

	rpcCtx, rpcCancel := context.WithTimeout(ctx, time.Second)
	defer rpcCancel()
	resp, rpcErr := client.GameUgcContentClient.GetConfigTabInfoById(ctx, &gameContentPB.GetConfigTabInfoByIdReq{
		ConfigTabId: configTabId,
	})
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "addConfigTabPostTeleport GetConfigTabInfoById configTabId(%s) err: %v", configTabId, rpcErr)
		return rpcErr
	}
	teleport := resp.GetConfigTabInfo().GetPostTeleport()
	if teleport == nil || teleport.GetText() == "" || teleport.GetUrl() == "" {
		log.DebugWithCtx(ctx, "addConfigTabPostTeleport configTabId(%s) miss teleport", configTabId)
		return nil
	}

	log.InfoWithCtx(ctx, "addConfigTabPostTeleport postId(%s) configTabId(%s) match teleport: %+v", postId, configTabId, teleport)

	err := client.OpeConfigClient.AddPostTeleport(rpcCtx, &ope_config.AddPostTeleportReq{
		Teleport: &ope_config.PostTeleport{
			PostId:    postId,
			Text:      teleport.GetText(),
			Url:       teleport.GetUrl(),
			FeedTypes: []ope_config.FeedType{ope_config.FeedType_GameDistrict},
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "addConfigTabPostTeleport AddPostTeleport postId(%s) err: %v", postId, err)
		return err
	}

	return nil
}
