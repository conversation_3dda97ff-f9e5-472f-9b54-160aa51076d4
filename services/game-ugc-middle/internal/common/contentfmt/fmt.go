package contentfmt

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"golang.52tt.com/protocol/app/ugc"
)

type ResolveContentInfo struct {
	StartAt  int
	EndAt    int
	RichText *ugc.RichText
}

func ResolveContent(content string) []*ResolveContentInfo {
	var result []*ResolveContentInfo
	var index []int
	var matchFirst bool
	//先找出'&^'的下标
	for i, r := range content {
		if matchFirst {
			if r == '^' {
				index = append(index, i-1)
			}
			matchFirst = false
		} else {
			if r == '&' {
				matchFirst = true
			}
		}
	}

	for i := 0; i < len(index); i++ {
		if i+1 >= len(index) || len(content) <= index[i+1]+1 || index[i]+2 >= index[i+1] {
			continue
		}

		var data = content[index[i]+2 : index[i+1]]
		bys, err := base64.StdEncoding.DecodeString(data)
		if err != nil {
			continue
		}
		var text ugc.RichText
		err = json.Unmarshal(bys, &text)
		if err != nil {
			continue
		} else {
			var name = ugc.RichTextType_name[int32(text.Type)]
			if name != "" && name != ugc.RichTextType_name[int32(ugc.RichTextType_INVALID)] {
				result = append(result, &ResolveContentInfo{StartAt: index[i], EndAt: index[i+1] + 1, RichText: &text})
				i++
			}
		}

	}
	return result
}

func FormatPlainText(content string) string {
	info := ResolveContent(content)
	var idx = 0
	var buffer = bytes.NewBuffer(nil)
	for _, item := range info {
		if idx < item.StartAt && item.StartAt < len(content) {
			buffer.WriteString(content[idx:item.StartAt])
		}
		switch item.RichText.Type {
		case ugc.RichTextType_AT:
			buffer.WriteString("@" + item.RichText.AtData.Nickname + " ")
		default:
		}

		idx = item.EndAt + 1
	}
	if idx < len(content) {
		buffer.WriteString(content[idx:])
	}
	return buffer.String()
}
