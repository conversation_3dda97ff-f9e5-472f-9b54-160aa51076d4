package eventlink

import (
	"context"
	"strconv"

	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/aigc/aigc-group"
	"golang.52tt.com/services/aigc/aigc-group/internal/event"
)

func (eb *eventBus) PublishTemplateEvent(ctx context.Context, ev *pb.GroupTemplateChangeEvent) error {

	if err := eb.Publish(ctx, event.PubEventGroupTemplate, strconv.Itoa(int(ev.GetGroupTemplateList()[0].GetId())), ev); err != nil {
		return err
	}
	log.InfoWithCtx(ctx, "PublishTemplateEvent event(%+v) finished", ev)
	return nil
}
