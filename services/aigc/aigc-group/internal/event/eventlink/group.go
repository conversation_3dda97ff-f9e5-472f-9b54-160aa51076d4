package eventlink

import (
	"context"
	"strconv"

	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/aigc/aigc-group"
	"golang.52tt.com/services/aigc/aigc-group/internal/event"
)

func (eb *eventBus) PublishGroupEvent(ctx context.Context, ev pb.GroupEvent_Event, group *pb.GroupEvent_Group, members ...*pb.GroupEvent_Member) error {
	e := &pb.GroupEvent{
		Event:   ev,
		Group:   group,
		Members: members,
	}
	if err := eb.Publish(ctx, event.PubEventGroup, strconv.Itoa(int(group.GetId())), e); err != nil {
		return err
	}

	log.InfoWithCtx(ctx, "PublishGroupEvent event(%+v) finished", e)
	return nil
}
