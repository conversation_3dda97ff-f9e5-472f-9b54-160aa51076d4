package mongo

import (
	"context"
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/aigc/aigc-group"
	"golang.52tt.com/services/aigc/aigc-group/internal/entity"
	"golang.52tt.com/services/aigc/aigc-group/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-group/internal/store"
)

type templateMongoStore struct {
	templates *mongo.Collection
}

func NewTemplateMongoStore(ctx context.Context, mongoDB *db.MongoDB) store.TemplateStore {
	st := &templateMongoStore{
		templates: mongoDB.Database().Collection("templates"),
	}

	return st
}

func (s *templateMongoStore) Add(ctx context.Context, template *entity.GroupTemplate) error {
	_, err := s.templates.InsertOne(ctx, template)
	if err != nil {
		return err
	}
	return nil
}

func (s *templateMongoStore) Delete(ctx context.Context, templateId uint32) error {
	_, err := s.templates.DeleteOne(ctx, bson.M{"_id": templateId})
	return err
}

func (s *templateMongoStore) Update(ctx context.Context, template *entity.GroupTemplate) error {
	_, err := s.templates.UpdateByID(ctx,
		template.ID,
		bson.M{"$set": bson.M{
			"name":                template.Name,
			"character":           template.Character,
			"tags":                template.Tags,
			"avatar":              template.Avatar,
			"sex":                 template.Sex,
			"chat_background_img": template.ChatBackgroundImg,
			"home_background_img": template.HomeBackgroundImg,
			"group_icon":          template.GroupIcon,
			"exposed":             template.Exposed,
			"insert_pos":          template.InsertPos,
			"corner_icon":         template.CornerIcon,
			"category_id":         template.CategoryId,
			"config_like_num":     template.ConfigLikeNum,
			"role_ids":            template.RoleIds,
			"im_tab_tags":         template.ImTabTags,
			"script_info":         template.ScriptInfo,
			"suitable_sex":        template.SuitableSex,
			"ai_group_prologues":  template.AIGroupPrologues,
			"quick_speak_texts":   template.QuickSpeakTexts,
			"leave_strategy":      template.LeaveStrategy,
		}})
	return err
}

func (s *templateMongoStore) BatchGet(ctx context.Context, ids []uint32) ([]*entity.GroupTemplate, error) {
	var templates []*entity.GroupTemplate
	cursor, err := s.templates.Find(ctx, bson.M{"_id": bson.M{"$in": ids}})
	if err != nil {
		return nil, err
	}
	err = cursor.All(ctx, &templates)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGet templates ids:%v err: %v", ids, err)
		return nil, err
	}
	return templates, nil
}

func (s *templateMongoStore) GetByPage(ctx context.Context, page, pageSize int64,
	groupTypes []pb.GroupType) ([]*entity.GroupTemplate, int64, error) {
	filter := bson.M{}
	if len(groupTypes) > 0 {
		filter["group_type"] = bson.M{"$in": groupTypes}
	}
	total, err := s.templates.CountDocuments(ctx, filter)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetByPage CountDocuments page :%d pageSize:%d err: %v", page, pageSize, err)
		return nil, 0, err
	}

	if (page-1)*pageSize > total {
		return nil, total, nil
	}

	searchOption := options.Find().
		SetSkip((page - 1) * pageSize).
		SetLimit(pageSize).
		SetSort(bson.M{"_id": -1})

	cursor, err := s.templates.Find(ctx, filter, searchOption)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetByPage Find page :%d pageSize:%d err: %v", page, pageSize, err)
		return nil, total, err
	}
	var templates []*entity.GroupTemplate
	err = cursor.All(ctx, &templates)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetByPage page:%d pageSize:%d err: %v", page, pageSize, err)
		return nil, total, err
	}
	return templates, total, nil
}

func (s *templateMongoStore) SearchTemplate(ctx context.Context, opts *entity.TemplateSearchOption, pageSize int64) ([]*entity.GroupTemplate, string, error) {
	var templates []*entity.GroupTemplate
	filter := bson.D{
		bson.E{Key: "exposed", Value: true},
	}
	var nextCursor string
	if len(opts.GroupTypes) > 0 {
		filter = append(filter, bson.E{Key: "group_type", Value: bson.D{{Key: "$in", Value: opts.GroupTypes}}})
	}

	if opts.Sex != pb.GetScriptListByFilterRequest_SEX_UNSPECIFIED && opts.Sex != pb.GetScriptListByFilterRequest_SEX_ALL {
		filter = append(filter, bson.E{Key: "suitable_sex", Value: bson.D{{Key: "$in", Value: []int32{opts.ConvertPbSexToUint32(opts.Sex)}}}})
	}

	if len(opts.ExcludedIds) > 0 {
		filter = append(filter, bson.E{Key: "_id", Value: bson.D{{Key: "$nin", Value: opts.ExcludedIds}}})
	}

	if opts.MatchStrategy > 0 {
		switch opts.MatchStrategy {
		case pb.MatchStrategy_MATCH_STRATEGY_POOL:
			// 查询 script_info.match_strategy == opts.MatchStrategy 或 script_info.match_strategy 字段不存在
			filter = append(filter, bson.E{
				Key: "$or",
				Value: bson.A{
					bson.D{{Key: "script_info.match_strategy", Value: opts.MatchStrategy}},
					bson.D{{Key: "script_info.match_strategy", Value: bson.D{{Key: "$exists", Value: false}}}},
				},
			})
		case pb.MatchStrategy_MATCH_STRATEGY_GROUP:
			filter = append(filter, bson.E{
				Key:   "script_info.match_strategy",
				Value: pb.MatchStrategy_MATCH_STRATEGY_GROUP,
			})
		}
	}

	if len(opts.Cursor) != 0 {
		sort, lastId, err := opts.DecodeCursor(opts.Cursor)
		if err != nil {
			return templates, nextCursor, err
		}
		orConditions := bson.A{
			bson.D{{Key: "script_info.sort", Value: bson.D{{Key: "$gt", Value: sort}}}},
			bson.D{
				{Key: "script_info.sort", Value: sort},
				{Key: "_id", Value: bson.D{{Key: "$lt", Value: lastId}}},
			},
		}
		filter = append(filter, bson.E{Key: "$or", Value: orConditions})

	}

	searchOption := options.Find().SetSort(bson.D{
		{Key: "script_info.sort", Value: 1},
		{Key: "_id", Value: -1},
	}).SetLimit(pageSize)
	cursor, err := s.templates.Find(ctx, filter, searchOption)
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchTemplate Find opts :%s err: %v", opts.String(), err)
		return templates, nextCursor, err
	}
	err = cursor.All(ctx, &templates)
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchRole All opts:%s err: %v", opts, err)
		return templates, nextCursor, err
	}
	if len(templates) > 0 {
		lastTemplate := templates[len(templates)-1]
		if lastTemplate.ScriptInfo == nil {
			nextCursor = EncodeCursor(0, lastTemplate.ID)
		} else {
			if len(templates) >= int(pageSize) {
				nextCursor = EncodeCursor(templates[len(templates)-1].ScriptInfo.Sort, templates[len(templates)-1].ID)
			}
		}
	}

	return templates, nextCursor, nil
}

func EncodeCursor(sort, id uint32) string {
	return fmt.Sprintf("%d:%d", sort, id)
}
