package mongo

import (
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/aigc/aigc-group/internal/entity"
	"golang.52tt.com/services/aigc/aigc-group/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-group/internal/store"
)

type specialMsgStore struct {
	specialMsg *mongo.Collection
}

func NewSpecialMsgStore(ctx context.Context, mongoDB *db.MongoDB) store.SpecialMsgStore {
	st := &specialMsgStore{
		specialMsg: mongoDB.Database().Collection("special_msg"),
	}
	_, err := st.specialMsg.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			// 用于获取用户创建的群组
			Keys: bson.D{
				{Key: "group_id", Value: 1},
				{Key: "uid", Value: 1},
				{Key: "seq_id", Value: -1},
			},
		},
		{
			// 用于获取用户创建的群组
			Keys: bson.D{
				{Key: "group_id", Value: 1},
				{Key: "uid", Value: 1},
				{Key: "seq_id", Value: 1},
			},
		},

	})
	if err != nil {
		log.WarnWithCtx(ctx, "NewSpecialMsgStore collection(special_msg) CreateMany err: %v", err)
	}

	return st
}

func (s *specialMsgStore) Add(ctx context.Context, template *entity.SpecialMsg) (primitive.ObjectID, error) {
	res, err := s.specialMsg.InsertOne(ctx, template)
	if err != nil {
		return primitive.ObjectID{}, err
	}
	return res.InsertedID.(primitive.ObjectID), nil
}

func (s *specialMsgStore) GetList(ctx context.Context, opts *entity.PullSpecialMsgOpt) ([]*entity.SpecialMsg, error) {

	var list []*entity.SpecialMsg
	filter := bson.M{
		"group_id":      opts.GroupId,
		"uid":           opts.Uid,
		"business_type": opts.BusinessType,
	}
	searchOption := &options.FindOptions{}

	if opts.Reverse {
		if opts.StartSeqId > 0 {
			filter["seq_id"] = bson.M{"$lt": opts.StartSeqId}
		} else {
			filter["seq_id"] = bson.M{"$gt": 0} // 如果没有指定起始SeqId，则获取所有大于0的SeqId
		}
		searchOption.SetSort(bson.M{"seq_id": -1}) // 降序
	} else {
		filter["seq_id"] = bson.M{"$gt": opts.StartSeqId}
		searchOption.SetSort(bson.M{"seq_id": 1}) // 升序
	}
	if opts.Limit > 0 {
		searchOption.SetLimit(int64(opts.Limit))
	} else {
		searchOption.SetLimit(20) // 默认限制20条
	}

	cursor, err := s.specialMsg.Find(ctx, filter, searchOption)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetList opts:%v err:%v", opts, err)
		return list, err
	}
	err = cursor.All(ctx, &list)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetList opts:%v err:%v", opts, err)
		return list, err
	}
	return list, nil
}

func (s *specialMsgStore) BatGetLastSpecialMsg(ctx context.Context, groupIds []uint32, uid, businessType uint32) (map[uint32]*entity.SpecialMsg, error) {
	// 构建聚合管道
	pipeline := []bson.M{
		// 筛选条件：匹配指定Uid、BusinessType和GroupId列表
		{
			"$match": bson.M{
				"uid":           uid,
				"business_type": businessType,
				"group_id":      bson.M{"$in": groupIds},
			},
		},
		// 按 SeqId 降序排序
		{
			"$sort": bson.M{"seq_id": -1},
		},
		// 按GroupId分组，找出每个分组中SeqId最大的文档
		{
			"$group": bson.M{
				"_id": "$group_id",                // 按GroupId分组
				"doc": bson.M{"$first": "$$ROOT"}, // 对应的完整文档
			},
		},
		// 可选：重命名字段，使输出更清晰
		{
			"$project": bson.M{
				"_id":      0,                          // 排除默认的_id字段
				"group_id": "$_id",                     // 将分组键重命名为groupId
				"maxSeqId": bson.M{"$first": "$seqId"}, // 添加最大SeqId
				"message":  "$doc",                     // 包含完整文档
			},
		},
	}

	// 执行聚合查询
	cursor, err := s.specialMsg.Aggregate(ctx, pipeline)
	if err != nil {
		log.Fatalf("Aggregation error: %v", err)
	}
	type aggregatedResult struct {
		GroupId  uint32             `bson:"group_id"`
		MaxSeqId uint32             `bson:"maxSeqId"`
		Message  *entity.SpecialMsg `bson:"message"`
	}
	var results []*aggregatedResult
	// 处理查询结果
	if err := cursor.All(ctx, &results); err != nil {
		log.ErrorWithCtx(ctx, "BatGetLastSpecialMsg Aggregate uid:%d err: %v", uid, err)
		return nil, err
	}
	resMap := make(map[uint32]*entity.SpecialMsg, len(results))
	for _, result := range results {
		resMap[result.GroupId] = result.Message
	}
	return resMap, nil
}
