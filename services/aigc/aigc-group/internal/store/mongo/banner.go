package mongo

import (
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.52tt.com/services/aigc/aigc-group/internal/entity"
	"golang.52tt.com/services/aigc/aigc-group/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-group/internal/store"
	"time"
)

type hotBannerMongoStore struct {
	hotBanner *mongo.Collection
}

func NewHotBannerMongoStore(ctx context.Context, mongoDB *db.MongoDB) store.HotBannerStore {
	st := &hotBannerMongoStore{
		hotBanner: mongoDB.Database().Collection("hot_banner"),
	}
	
	return st
}

func (s *hotBannerMongoStore) Add(ctx context.Context, template *entity.HotBanner) (primitive.ObjectID, error) {
	res, err := s.hotBanner.InsertOne(ctx, template)
	if err != nil {
		return primitive.ObjectID{}, err
	}
	return res.InsertedID.(primitive.ObjectID), nil
}

func (s *hotBannerMongoStore) Delete(ctx context.Context, bannerId primitive.ObjectID) error {
	_, err := s.hotBanner.DeleteOne(ctx, bson.M{"_id": bannerId})
	return err
}

func (s *hotBannerMongoStore) Update(ctx context.Context, banner *entity.HotBanner) error {
	_, err := s.hotBanner.UpdateByID(ctx,
		banner.ID,
		bson.M{"$set": bson.M{
			"template_id":           banner.TemplateId,
			"top_title":             banner.TopTitle,
			"sub_title":             banner.SubTitle,
			"bar_color":             banner.BarColor,
			"button_color":          banner.ButtonColor,
			"update_at":             time.Now(),
			"button_title":          banner.ButtonTitle,
			"banner_background_img": banner.BannerBackgroundImg,
		}})
	return err
}

func (s *hotBannerMongoStore) GetAll(ctx context.Context) ([]*entity.HotBanner, error) {
	sortOptions := options.Find().SetSort(bson.D{
		{Key: "sort", Value: 1},
		{Key: "update_at", Value: -1},
	})
	var hotBanners []*entity.HotBanner
	// 执行查询
	cursor, err := s.hotBanner.Find(context.TODO(), bson.D{}, sortOptions)
	if err != nil {
		return nil, err
	}
	
	err = cursor.All(ctx, &hotBanners)
	if err != nil {
		return nil, err
	}
	return hotBanners, nil
}

func (s *hotBannerMongoStore) GetOne(ctx context.Context, id primitive.ObjectID) (*entity.HotBanner, error) {
	var hotBanner *entity.HotBanner
	err := s.hotBanner.FindOne(ctx, bson.M{"_id": id}).Decode(hotBanner)
	if err != nil {
		return nil, err
	}
	return hotBanner, nil
}

func (s *hotBannerMongoStore) Resort(ctx context.Context, ids []string) error {
	nowTime := time.Now()
	
	models := make([]mongo.WriteModel, len(ids))
	for i, id := range ids {
		objectId, _ := primitive.ObjectIDFromHex(id)
		models[i] = mongo.NewUpdateOneModel().
			SetFilter(bson.M{"_id": objectId}).
			SetUpdate(bson.M{"$set": bson.M{
				"sort":      i + 1,
				"update_at": nowTime,
			}})
	}
	_, err := s.hotBanner.BulkWrite(ctx, models, options.BulkWrite().SetOrdered(false))
	
	return err
}
