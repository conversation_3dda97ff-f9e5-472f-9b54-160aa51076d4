package mongo

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/aigc/aigc-group/internal/entity"
	"golang.52tt.com/services/aigc/aigc-group/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-group/internal/store"
)

type memberMongoStore struct {
	members *mongo.Collection
}

func NewMemberMongoStore(ctx context.Context, mongoDB *db.MongoDB) store.MemberStore {
	st := &memberMongoStore{
		members: mongoDB.Database().Collection("members"),
	}

	_, err := st.members.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			// 用于获取群组成员
			Keys: bson.D{
				{Key: "group_id", Value: 1},
				{Key: "_id", Value: 1},
			},
		},
		{
			// 用于获取用户加入的群组
			Keys: bson.D{
				{Key: "uid", Value: 1},
				{Key: "_id", Value: 1},
			},
		},
	})
	if err != nil {
		log.WarnWithCtx(ctx, "NewMemberMongoStore collection(members) CreateMany err: %v", err)
	}

	return st
}

func (s *memberMongoStore) Add(ctx context.Context, member *entity.GroupMember) error {
	_, err := s.members.InsertOne(ctx, member)
	return err
}

func (s *memberMongoStore) DeleteByGroupId(ctx context.Context, groupID uint32) error {
	_, err := s.members.DeleteMany(ctx, bson.M{"group_id": groupID})
	return err
}

func (s *memberMongoStore) CountByUid(ctx context.Context, uid uint32) (uint32, error) {
	res, err := s.members.CountDocuments(ctx, bson.M{"uid": uid})
	return uint32(res), err
}

func (s *memberMongoStore) Get(ctx context.Context, groupId, uid uint32) (*entity.GroupMember, error) {
	result := s.members.FindOne(ctx, bson.M{"group_id": groupId, "uid": uid})
	if err := result.Err(); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}

		return nil, err
	}

	var member entity.GroupMember
	return &member, result.Decode(&member)
}

func (s *memberMongoStore) BatchGet(ctx context.Context, groupId uint32, uidList []uint32) (entity.GroupMemberList, error) {
	cursor, err := s.members.Find(ctx, bson.M{"group_id": groupId, "uid": bson.M{"$in": uidList}})
	if err != nil {
		return nil, err
	}

	var list entity.GroupMemberList
	return list, cursor.All(ctx, &list)
}

func (s *memberMongoStore) ListByUid(ctx context.Context, uid uint32) (entity.GroupMemberList, error) {
	cursor, err := s.members.Find(ctx, bson.M{"uid": uid})
	if err != nil {
		return nil, err
	}

	var list entity.GroupMemberList
	return list, cursor.All(ctx, &list)
}

func (s *memberMongoStore) ListByGroupId(ctx context.Context, groupID uint32) (entity.GroupMemberList, error) {
	cursor, err := s.members.Find(ctx, bson.M{"group_id": groupID})
	if err != nil {
		return nil, err
	}

	var list entity.GroupMemberList
	return list, cursor.All(ctx, &list)
}

func (s *memberMongoStore) BatchDeleteByGroupId(ctx context.Context, groupIds []uint32) error {
	if len(groupIds) == 0 {
		return nil
	}

	_, err := s.members.DeleteMany(ctx, bson.M{"group_id": bson.M{"$in": groupIds}})
	return err
}

func (s *memberMongoStore) BatchListByGroupIds(ctx context.Context, groupIDs []uint32) (map[uint32]entity.GroupMemberList, error) {
	if len(groupIDs) == 0 {
		return nil, nil
	}

	cursor, err := s.members.Find(ctx, bson.M{"group_id": bson.M{"$in": groupIDs}})
	if err != nil {
		return nil, err
	}

	var allMembers entity.GroupMemberList
	if err := cursor.All(ctx, &allMembers); err != nil {
		return nil, err
	}

	result := make(map[uint32]entity.GroupMemberList)
	for _, member := range allMembers {
		if result[member.GroupID] == nil {
			result[member.GroupID] = entity.GroupMemberList{}
		}
		result[member.GroupID] = append(result[member.GroupID], member)
	}

	return result, nil
}

func (s *memberMongoStore) DeleteByGroupIdAndUid(ctx context.Context, groupID, uid uint32) error {
	_, err := s.members.DeleteOne(ctx, bson.M{"group_id": groupID, "uid": uid})
	return err
}
