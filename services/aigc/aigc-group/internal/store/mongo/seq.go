package mongo

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"golang.52tt.com/services/aigc/aigc-group/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-group/internal/store"
)

type seqMongoStore struct {
	sequences *mongo.Collection
}

func NewSeqMongoStore(ctx context.Context, mongoDB *db.MongoDB) store.SeqStore {
	st := &seqMongoStore{
		sequences: mongoDB.Database().Collection("sequences"),
	}

	return st
}

func (s *seqMongoStore) Generate(ctx context.Context, ns store.SeqNamespace) (uint32, error) {
	result := s.sequences.FindOneAndUpdate(
		ctx,
		bson.M{"_id": ns},
		bson.M{"$inc": bson.M{"seq": 1}},
		options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After),
	)
	if err := result.Err(); err != nil {
		return 0, err
	}

	var seq struct {
		Seq uint32 `bson:"seq"`
	}
	if err := result.Decode(&seq); err != nil {
		return 0, err
	}

	return seq.Seq, nil
}

func (s *seqMongoStore) BatGenerate(ctx context.Context, ns store.SeqNamespace, incrNum int) (uint32, error) {
	result := s.sequences.FindOneAndUpdate(
		ctx,
		bson.M{"_id": ns},
		bson.M{"$inc": bson.M{"seq": incrNum}},
		options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After),
	)
	if err := result.Err(); err != nil {
		return 0, err
	}
	
	var seq struct {
		Seq uint32 `bson:"seq"`
	}
	if err := result.Decode(&seq); err != nil {
		return 0, err
	}
	
	return seq.Seq, nil
}