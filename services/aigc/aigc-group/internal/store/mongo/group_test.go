package mongo

import (
	"context"
	"testing"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	pb "golang.52tt.com/protocol/services/aigc/aigc-group"
	"golang.52tt.com/services/aigc/aigc-group/internal/entity"
)

func Test_groupMongoStore_Add(t *testing.T) {
	type fields struct {
		groups *mongo.Collection
	}
	type args struct {
		ctx   context.Context
		group *entity.Group
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				groups: utGroupsCol,
			},
			args: args{
				ctx: context.Background(),
				group: &entity.Group{
					ID:        1,
					Type:      pb.GroupType_GROUP_TYPE_SINGLE_USER,
					CreatedAt: time.Now(),

					OwnerUid:   1,
					TemplateID: 1,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &groupMongoStore{
				groups: tt.fields.groups,
			}
			if err := s.Add(tt.args.ctx, tt.args.group); (err != nil) != tt.wantErr {
				t.<PERSON>("groupMongoStore.Add() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_groupMongoStore_Get(t *testing.T) {
	type fields struct {
		groups *mongo.Collection
	}
	type args struct {
		ctx context.Context
		id  uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				groups: utGroupsCol,
			},
			args: args{
				ctx: context.Background(),
				id:  1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &groupMongoStore{
				groups: tt.fields.groups,
			}
			got, err := s.Get(tt.args.ctx, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("groupMongoStore.Get() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			t.Logf("groupMongoStore.Get() = %v", got)
		})
	}
}

func Test_groupMongoStore_ListById(t *testing.T) {
	type fields struct {
		groups *mongo.Collection
	}
	type args struct {
		ctx    context.Context
		idList []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				groups: utGroupsCol,
			},
			args: args{
				ctx:    context.Background(),
				idList: []uint32{1},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &groupMongoStore{
				groups: tt.fields.groups,
			}
			got, err := s.ListById(tt.args.ctx, tt.args.idList)
			if (err != nil) != tt.wantErr {
				t.Errorf("groupMongoStore.ListById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			t.Logf("groupMongoStore.ListById() = %+v", got)
		})
	}
}

func Test_groupMongoStore_ListByOwnerUid(t *testing.T) {
	type fields struct {
		groups *mongo.Collection
	}
	type args struct {
		ctx        context.Context
		ownerUid   uint32
		templateId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				groups: utGroupsCol,
			},
			args: args{
				ctx:        context.Background(),
				ownerUid:   1,
				templateId: 1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &groupMongoStore{
				groups: tt.fields.groups,
			}
			got, err := s.ListByOwnerUid(tt.args.ctx, tt.args.ownerUid, tt.args.templateId)
			if (err != nil) != tt.wantErr {
				t.Errorf("groupMongoStore.ListByOwnerUid() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			t.Logf("groupMongoStore.ListByOwnerUid() = %v", got)
		})
	}
}

func Test_groupMongoStore_ListByTemplateId(t *testing.T) {
	type fields struct {
		groups *mongo.Collection
	}
	type args struct {
		ctx        context.Context
		templateId uint32
		id         uint32
		limit      uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				groups: utGroupsCol,
			},
			args: args{
				ctx:        context.Background(),
				templateId: 1,
				id:         0,
				limit:      100,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &groupMongoStore{
				groups: tt.fields.groups,
			}
			got, err := s.ListByTemplateId(tt.args.ctx, tt.args.templateId, tt.args.id, tt.args.limit)
			if (err != nil) != tt.wantErr {
				t.Errorf("groupMongoStore.ListByTemplateId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			t.Logf("groupMongoStore.ListByTemplateId() = %v", got)
		})
	}
}

func Test_groupMongoStore_IncrUserNum(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		groups *mongo.Collection
	}
	type args struct {
		ctx context.Context
		id  uint32
		num int32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				groups: utGroupsCol,
			},
			args: args{
				ctx: ctx,
				id:  1,
				num: 1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &groupMongoStore{
				groups: tt.fields.groups,
			}
			got, err := s.IncrUserNum(tt.args.ctx, tt.args.id, tt.args.num)
			if (err != nil) != tt.wantErr {
				t.Errorf("groupMongoStore.IncrUserNum() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("groupMongoStore.IncrUserNum() = %v", got)
		})
	}
}

func Test_groupMongoStore_Delete(t *testing.T) {
	type fields struct {
		groups *mongo.Collection
	}
	type args struct {
		ctx context.Context
		id  uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				groups: utGroupsCol,
			},
			args: args{
				ctx: context.Background(),
				id:  1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &groupMongoStore{
				groups: tt.fields.groups,
			}
			if err := s.Delete(tt.args.ctx, tt.args.id); (err != nil) != tt.wantErr {
				t.Errorf("groupMongoStore.Delete() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
