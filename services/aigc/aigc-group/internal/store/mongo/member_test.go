package mongo

import (
	"context"
	"testing"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"golang.52tt.com/services/aigc/aigc-group/internal/entity"
)

func Test_memberMongoStore_Add(t *testing.T) {
	type fields struct {
		members *mongo.Collection
	}
	type args struct {
		ctx    context.Context
		member *entity.GroupMember
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				members: utMembersCol,
			},
			args: args{
				ctx: context.Background(),
				member: &entity.GroupMember{
					ID:       primitive.NewObjectID(),
					JoinedAt: time.Now(),

					Uid:     1,
					GroupID: 1,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &memberMongoStore{
				members: tt.fields.members,
			}
			if err := s.Add(tt.args.ctx, tt.args.member); (err != nil) != tt.wantErr {
				t.Errorf("memberMongoStore.Add() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

/*
func Test_memberMongoStore_CountByUid(t *testing.T) {
	type fields struct {
		members *mongo.Collection
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				members: utMembersCol,
			},
			args: args{
				ctx: context.Background(),
				uid: 1,
			},
			want:    1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &memberMongoStore{
				members: tt.fields.members,
			}
			got, err := s.CountByUid(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("memberMongoStore.CountByUid() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("memberMongoStore.CountByUid() = %v, want %v", got, tt.want)
			}
		})
	}
}
*/
func Test_memberMongoStore_Get(t *testing.T) {
	type fields struct {
		members *mongo.Collection
	}
	type args struct {
		ctx     context.Context
		groupId uint32
		uid     uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				members: utMembersCol,
			},
			args: args{
				ctx:     context.Background(),
				groupId: 1,
				uid:     1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &memberMongoStore{
				members: tt.fields.members,
			}
			got, err := s.Get(tt.args.ctx, tt.args.groupId, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("memberMongoStore.Get() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			t.Logf("memberMongoStore.Get() = %v", got)
		})
	}
}

func Test_memberMongoStore_ListByUid(t *testing.T) {
	type fields struct {
		members *mongo.Collection
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				members: utMembersCol,
			},
			args: args{
				ctx: context.Background(),
				uid: 1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &memberMongoStore{
				members: tt.fields.members,
			}
			got, err := s.ListByUid(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("memberMongoStore.ListByUid() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			t.Logf("memberMongoStore.ListByUid() = %v", got)
		})
	}
}

func Test_memberMongoStore_ListByGroupId(t *testing.T) {
	type fields struct {
		members *mongo.Collection
	}
	type args struct {
		ctx     context.Context
		groupID uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				members: utMembersCol,
			},
			args: args{
				ctx:     context.Background(),
				groupID: 1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &memberMongoStore{
				members: tt.fields.members,
			}
			got, err := s.ListByGroupId(tt.args.ctx, tt.args.groupID)
			if (err != nil) != tt.wantErr {
				t.Errorf("memberMongoStore.ListByGroupId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			t.Logf("memberMongoStore.ListByGroupId() = %v", got)
		})
	}
}

func Test_memberMongoStore_DeleteByGroupId(t *testing.T) {
	type fields struct {
		members *mongo.Collection
	}
	type args struct {
		ctx     context.Context
		groupID uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				members: utMembersCol,
			},
			args: args{
				ctx:     context.Background(),
				groupID: 1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &memberMongoStore{
				members: tt.fields.members,
			}
			if err := s.DeleteByGroupId(tt.args.ctx, tt.args.groupID); (err != nil) != tt.wantErr {
				t.Errorf("memberMongoStore.DeleteByGroupId() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
