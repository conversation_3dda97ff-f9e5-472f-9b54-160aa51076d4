package mongo

import (
	"context"

	"go.mongodb.org/mongo-driver/mongo"

	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/aigc/aigc-group/internal/infra/db"
)

var (
	utGroupsCol    *mongo.Collection
	utMembersCol   *mongo.Collection
	utSequencesCol *mongo.Collection
)

func init() {
	mongoConfig := &config.MongoConfig{
		Addrs:       "10.34.6.29:27017",
		Database:    "aigc_group",
		MaxPoolSize: 1,
		UserName:    "aigc_group_rw",
		Password:    "v5cGjQ73K35H*c5",
	}

	utDB, err := db.NewMongoDB(context.Background(), mongoConfig)
	if err != nil {
		log.Fatalf("NewMongoDB config(%+v) err: %v", mongoConfig, err)
	}

	utGroupsCol = utDB.Database().Collection("groups")
	utMembersCol = utDB.Database().Collection("members")
	utSequencesCol = utDB.Database().Collection("sequences")
}
