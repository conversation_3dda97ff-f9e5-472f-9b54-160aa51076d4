package mongo

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/aigc/aigc-group/internal/entity"
	"golang.52tt.com/services/aigc/aigc-group/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-group/internal/store"
)

type groupMongoStore struct {
	groups    *mongo.Collection
	sequences *mongo.Collection
}

func NewGroupMongoStore(ctx context.Context, mongoDB *db.MongoDB) store.GroupStore {
	st := &groupMongoStore{
		groups:    mongoDB.Database().Collection("groups"),
		sequences: mongoDB.Database().Collection("sequences"),
	}

	_, err := st.groups.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			// 用于获取用户创建的群组
			Keys: bson.D{
				{Key: "owner_uid", Value: 1},
				{Key: "template_id", Value: 1},
			},
		},
		{
			// 用户模板删除后删除群组
			Keys: bson.D{
				{Key: "template_id", Value: 1},
				{Key: "_id", Value: 1},
			},
		},
	})
	if err != nil {
		log.WarnWithCtx(ctx, "NewGroupMongoStore collection(groups) CreateMany err: %v", err)
	}

	return st
}

func (s *groupMongoStore) Add(ctx context.Context, group *entity.Group) error {
	_, err := s.groups.InsertOne(ctx, group)
	return err
}

func (s *groupMongoStore) Delete(ctx context.Context, id uint32) error {
	_, err := s.groups.DeleteOne(ctx, bson.M{"_id": id})
	return err
}

func (s *groupMongoStore) Get(ctx context.Context, id uint32) (*entity.Group, error) {
	result := s.groups.FindOne(ctx, bson.M{"_id": id})
	if err := result.Err(); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}

		return nil, err
	}

	var info entity.Group
	return &info, result.Decode(&info)
}

func (s *groupMongoStore) ListById(ctx context.Context, idList []uint32) (entity.GroupList, error) {
	cursor, err := s.groups.Find(ctx, bson.M{"_id": bson.M{"$in": idList}})
	if err != nil {
		return nil, err
	}

	var list entity.GroupList
	return list, cursor.All(ctx, &list)
}

func (s *groupMongoStore) ListByOwnerUid(ctx context.Context, ownerUid, templateId uint32) (entity.GroupList, error) {
	filter := bson.M{
		"owner_uid": ownerUid,
	}
	if templateId > 0 {
		filter["template_id"] = templateId
	}

	cursor, err := s.groups.Find(ctx, filter, options.Find().SetSort(bson.M{"_id": 1}))
	if err != nil {
		return nil, err
	}

	var list entity.GroupList
	return list, cursor.All(ctx, &list)
}

func (s *groupMongoStore) ListByTemplateId(ctx context.Context, templateId, id, limit uint32) (entity.GroupList, error) {
	cursor, err := s.groups.Find(
		ctx,
		bson.M{"template_id": templateId, "_id": bson.M{"$gt": id}},
		options.Find().SetSort(bson.M{"_id": 1}).SetLimit(int64(limit)),
	)
	if err != nil {
		return nil, err
	}

	var list entity.GroupList
	return list, cursor.All(ctx, &list)
}

func (s *groupMongoStore) IncrUserNum(ctx context.Context, id uint32, num int32) (uint32, error) {
	result := s.groups.FindOneAndUpdate(ctx, bson.M{"_id": id}, bson.M{"$inc": bson.M{"user_num": num}}, options.FindOneAndUpdate().SetReturnDocument(options.After))
	if err := result.Err(); err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, store.ErrNotFound
		}

		return 0, err
	}

	var group entity.Group
	if err := result.Decode(&group); err != nil {
		return 0, err
	}

	return group.UserNum, nil
}

func (s *groupMongoStore) BatchDelete(ctx context.Context, ids []uint32) error {
	if len(ids) == 0 {
		return nil
	}

	_, err := s.groups.DeleteMany(ctx, bson.M{"_id": bson.M{"$in": ids}})
	return err
}
