// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-group/internal/store (interfaces: SeqStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	store "golang.52tt.com/services/aigc/aigc-group/internal/store"
)

// MockSeqStore is a mock of SeqStore interface.
type MockSeqStore struct {
	ctrl     *gomock.Controller
	recorder *MockSeqStoreMockRecorder
}

// MockSeqStoreMockRecorder is the mock recorder for MockSeqStore.
type MockSeqStoreMockRecorder struct {
	mock *MockSeqStore
}

// NewMockSeqStore creates a new mock instance.
func NewMockSeqStore(ctrl *gomock.Controller) *MockSeqStore {
	mock := &MockSeqStore{ctrl: ctrl}
	mock.recorder = &MockSeqStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSeqStore) EXPECT() *MockSeqStoreMockRecorder {
	return m.recorder
}

// BatGenerate mocks base method.
func (m *MockSeqStore) BatGenerate(arg0 context.Context, arg1 store.SeqNamespace, arg2 int) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGenerate", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGenerate indicates an expected call of BatGenerate.
func (mr *MockSeqStoreMockRecorder) BatGenerate(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGenerate", reflect.TypeOf((*MockSeqStore)(nil).BatGenerate), arg0, arg1, arg2)
}

// Generate mocks base method.
func (m *MockSeqStore) Generate(arg0 context.Context, arg1 store.SeqNamespace) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Generate", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Generate indicates an expected call of Generate.
func (mr *MockSeqStoreMockRecorder) Generate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Generate", reflect.TypeOf((*MockSeqStore)(nil).Generate), arg0, arg1)
}
