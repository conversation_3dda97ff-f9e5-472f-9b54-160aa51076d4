// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-group/internal/store (interfaces: TemplateStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	aigc_group "golang.52tt.com/protocol/services/aigc/aigc-group"
	entity "golang.52tt.com/services/aigc/aigc-group/internal/entity"
)

// MockTemplateStore is a mock of TemplateStore interface.
type MockTemplateStore struct {
	ctrl     *gomock.Controller
	recorder *MockTemplateStoreMockRecorder
}

// MockTemplateStoreMockRecorder is the mock recorder for MockTemplateStore.
type MockTemplateStoreMockRecorder struct {
	mock *MockTemplateStore
}

// NewMockTemplateStore creates a new mock instance.
func NewMockTemplateStore(ctrl *gomock.Controller) *MockTemplateStore {
	mock := &MockTemplateStore{ctrl: ctrl}
	mock.recorder = &MockTemplateStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTemplateStore) EXPECT() *MockTemplateStoreMockRecorder {
	return m.recorder
}

// Add mocks base method.
func (m *MockTemplateStore) Add(arg0 context.Context, arg1 *entity.GroupTemplate) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Add", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Add indicates an expected call of Add.
func (mr *MockTemplateStoreMockRecorder) Add(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Add", reflect.TypeOf((*MockTemplateStore)(nil).Add), arg0, arg1)
}

// BatchGet mocks base method.
func (m *MockTemplateStore) BatchGet(arg0 context.Context, arg1 []uint32) ([]*entity.GroupTemplate, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGet", arg0, arg1)
	ret0, _ := ret[0].([]*entity.GroupTemplate)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGet indicates an expected call of BatchGet.
func (mr *MockTemplateStoreMockRecorder) BatchGet(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGet", reflect.TypeOf((*MockTemplateStore)(nil).BatchGet), arg0, arg1)
}

// Delete mocks base method.
func (m *MockTemplateStore) Delete(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockTemplateStoreMockRecorder) Delete(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockTemplateStore)(nil).Delete), arg0, arg1)
}

// GetByPage mocks base method.
func (m *MockTemplateStore) GetByPage(arg0 context.Context, arg1, arg2 int64, arg3 []aigc_group.GroupType) ([]*entity.GroupTemplate, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByPage", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*entity.GroupTemplate)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetByPage indicates an expected call of GetByPage.
func (mr *MockTemplateStoreMockRecorder) GetByPage(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByPage", reflect.TypeOf((*MockTemplateStore)(nil).GetByPage), arg0, arg1, arg2, arg3)
}

// SearchTemplate mocks base method.
func (m *MockTemplateStore) SearchTemplate(arg0 context.Context, arg1 *entity.TemplateSearchOption, arg2 int64) ([]*entity.GroupTemplate, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchTemplate", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*entity.GroupTemplate)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// SearchTemplate indicates an expected call of SearchTemplate.
func (mr *MockTemplateStoreMockRecorder) SearchTemplate(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchTemplate", reflect.TypeOf((*MockTemplateStore)(nil).SearchTemplate), arg0, arg1, arg2)
}

// Update mocks base method.
func (m *MockTemplateStore) Update(arg0 context.Context, arg1 *entity.GroupTemplate) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockTemplateStoreMockRecorder) Update(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockTemplateStore)(nil).Update), arg0, arg1)
}
