// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-group/internal/store (interfaces: MemberStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	entity "golang.52tt.com/services/aigc/aigc-group/internal/entity"
)

// MockMemberStore is a mock of MemberStore interface.
type MockMemberStore struct {
	ctrl     *gomock.Controller
	recorder *MockMemberStoreMockRecorder
}

// MockMemberStoreMockRecorder is the mock recorder for MockMemberStore.
type MockMemberStoreMockRecorder struct {
	mock *MockMemberStore
}

// NewMockMemberStore creates a new mock instance.
func NewMockMemberStore(ctrl *gomock.Controller) *MockMemberStore {
	mock := &MockMemberStore{ctrl: ctrl}
	mock.recorder = &MockMemberStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMemberStore) EXPECT() *MockMemberStoreMockRecorder {
	return m.recorder
}

// Add mocks base method.
func (m *MockMemberStore) Add(arg0 context.Context, arg1 *entity.GroupMember) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Add", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Add indicates an expected call of Add.
func (mr *MockMemberStoreMockRecorder) Add(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Add", reflect.TypeOf((*MockMemberStore)(nil).Add), arg0, arg1)
}

// BatchGet mocks base method.
func (m *MockMemberStore) BatchGet(arg0 context.Context, arg1 uint32, arg2 []uint32) (entity.GroupMemberList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGet", arg0, arg1, arg2)
	ret0, _ := ret[0].(entity.GroupMemberList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGet indicates an expected call of BatchGet.
func (mr *MockMemberStoreMockRecorder) BatchGet(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGet", reflect.TypeOf((*MockMemberStore)(nil).BatchGet), arg0, arg1, arg2)
}

// CountByUid mocks base method.
func (m *MockMemberStore) CountByUid(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountByUid", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountByUid indicates an expected call of CountByUid.
func (mr *MockMemberStoreMockRecorder) CountByUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountByUid", reflect.TypeOf((*MockMemberStore)(nil).CountByUid), arg0, arg1)
}

// DeleteByGroupId mocks base method.
func (m *MockMemberStore) DeleteByGroupId(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByGroupId", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByGroupId indicates an expected call of DeleteByGroupId.
func (mr *MockMemberStoreMockRecorder) DeleteByGroupId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByGroupId", reflect.TypeOf((*MockMemberStore)(nil).DeleteByGroupId), arg0, arg1)
}

// Get mocks base method.
func (m *MockMemberStore) Get(arg0 context.Context, arg1, arg2 uint32) (*entity.GroupMember, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", arg0, arg1, arg2)
	ret0, _ := ret[0].(*entity.GroupMember)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockMemberStoreMockRecorder) Get(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockMemberStore)(nil).Get), arg0, arg1, arg2)
}

// ListByGroupId mocks base method.
func (m *MockMemberStore) ListByGroupId(arg0 context.Context, arg1 uint32) (entity.GroupMemberList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByGroupId", arg0, arg1)
	ret0, _ := ret[0].(entity.GroupMemberList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByGroupId indicates an expected call of ListByGroupId.
func (mr *MockMemberStoreMockRecorder) ListByGroupId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByGroupId", reflect.TypeOf((*MockMemberStore)(nil).ListByGroupId), arg0, arg1)
}

// ListByUid mocks base method.
func (m *MockMemberStore) ListByUid(arg0 context.Context, arg1 uint32) (entity.GroupMemberList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByUid", arg0, arg1)
	ret0, _ := ret[0].(entity.GroupMemberList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByUid indicates an expected call of ListByUid.
func (mr *MockMemberStoreMockRecorder) ListByUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByUid", reflect.TypeOf((*MockMemberStore)(nil).ListByUid), arg0, arg1)
}
