// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-group/internal/store (interfaces: GroupStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	entity "golang.52tt.com/services/aigc/aigc-group/internal/entity"
)

// MockGroupStore is a mock of GroupStore interface.
type MockGroupStore struct {
	ctrl     *gomock.Controller
	recorder *MockGroupStoreMockRecorder
}

// MockGroupStoreMockRecorder is the mock recorder for MockGroupStore.
type MockGroupStoreMockRecorder struct {
	mock *MockGroupStore
}

// NewMockGroupStore creates a new mock instance.
func NewMockGroupStore(ctrl *gomock.Controller) *MockGroupStore {
	mock := &MockGroupStore{ctrl: ctrl}
	mock.recorder = &MockGroupStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGroupStore) EXPECT() *MockGroupStoreMockRecorder {
	return m.recorder
}

// Add mocks base method.
func (m *MockGroupStore) Add(arg0 context.Context, arg1 *entity.Group) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Add", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Add indicates an expected call of Add.
func (mr *MockGroupStoreMockRecorder) Add(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Add", reflect.TypeOf((*MockGroupStore)(nil).Add), arg0, arg1)
}

// Delete mocks base method.
func (m *MockGroupStore) Delete(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockGroupStoreMockRecorder) Delete(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockGroupStore)(nil).Delete), arg0, arg1)
}

// Get mocks base method.
func (m *MockGroupStore) Get(arg0 context.Context, arg1 uint32) (*entity.Group, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", arg0, arg1)
	ret0, _ := ret[0].(*entity.Group)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockGroupStoreMockRecorder) Get(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockGroupStore)(nil).Get), arg0, arg1)
}

// ListById mocks base method.
func (m *MockGroupStore) ListById(arg0 context.Context, arg1 []uint32) (entity.GroupList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListById", arg0, arg1)
	ret0, _ := ret[0].(entity.GroupList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListById indicates an expected call of ListById.
func (mr *MockGroupStoreMockRecorder) ListById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListById", reflect.TypeOf((*MockGroupStore)(nil).ListById), arg0, arg1)
}

// ListByOwnerUid mocks base method.
func (m *MockGroupStore) ListByOwnerUid(arg0 context.Context, arg1, arg2 uint32) (entity.GroupList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByOwnerUid", arg0, arg1, arg2)
	ret0, _ := ret[0].(entity.GroupList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByOwnerUid indicates an expected call of ListByOwnerUid.
func (mr *MockGroupStoreMockRecorder) ListByOwnerUid(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByOwnerUid", reflect.TypeOf((*MockGroupStore)(nil).ListByOwnerUid), arg0, arg1, arg2)
}

// ListByTemplateId mocks base method.
func (m *MockGroupStore) ListByTemplateId(arg0 context.Context, arg1, arg2, arg3 uint32) (entity.GroupList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByTemplateId", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(entity.GroupList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByTemplateId indicates an expected call of ListByTemplateId.
func (mr *MockGroupStoreMockRecorder) ListByTemplateId(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByTemplateId", reflect.TypeOf((*MockGroupStore)(nil).ListByTemplateId), arg0, arg1, arg2, arg3)
}
