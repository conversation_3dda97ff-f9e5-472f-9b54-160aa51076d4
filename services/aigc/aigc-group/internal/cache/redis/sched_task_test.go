package redis

import (
	"context"
	"testing"
	"time"

	"golang.52tt.com/services/aigc/aigc-group/internal/entity"
	"golang.52tt.com/services/aigc/aigc-group/internal/infra/db"
)

func Test_schedTasksRedisCache_Add(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx   context.Context
		tasks []*entity.SchedTask
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx: ctx,
				tasks: []*entity.SchedTask{
					{
						Key:      "test",
						Type:     entity.SchedTaskTypeDetectGroupActivity,
						ExpireAt: time.Now(),
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &schedTasksRedisCache{
				cmder: tt.fields.cmder,
			}
			if err := c.Add(tt.args.ctx, tt.args.tasks...); (err != nil) != tt.wantErr {
				t.Errorf("schedTasksRedisCache.Add() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_schedTasksRedisCache_Scan(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx      context.Context
		taskType entity.SchedTaskType
		begin    int64
		end      int64
		offset   int64
		limit    int64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx:      ctx,
				taskType: 1,
				begin:    0,
				end:      time.Now().Unix(),
				offset:   0,
				limit:    100,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &schedTasksRedisCache{
				cmder: tt.fields.cmder,
			}
			got, err := c.Scan(tt.args.ctx, tt.args.taskType, tt.args.begin, tt.args.end, tt.args.offset, tt.args.limit)
			if (err != nil) != tt.wantErr {
				t.Errorf("schedTasksRedisCache.Scan() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("schedTasksRedisCache.Scan() = %v", got)
		})
	}
}

func Test_schedTasksRedisCache_Remove(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx      context.Context
		taskType entity.SchedTaskType
		keys     []string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx:      ctx,
				taskType: entity.SchedTaskTypeDetectGroupActivity,
				keys:     []string{"test"},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &schedTasksRedisCache{
				cmder: tt.fields.cmder,
			}
			if err := c.Remove(tt.args.ctx, tt.args.taskType, tt.args.keys...); (err != nil) != tt.wantErr {
				t.Errorf("schedTasksRedisCache.Remove() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
