package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"

	"golang.52tt.com/pkg/utils/strutil"
	account_go "golang.52tt.com/protocol/services/account-go"
	"golang.52tt.com/services/aigc/aigc-group/internal/entity"
	"golang.52tt.com/services/aigc/aigc-group/internal/infra/db"
)

const (
	matchPlayersKey    = "match:players"
	matchTemplRankKey  = "match:templ:rank"
	matchPlayerPoolKey = "match:player:pool"

	matchPlayerTTL = time.Hour
)

type (
	matchPlayer struct {
		Uid     uint32 `json:"uid"`
		Sex     int32  `json:"sex"`
		TemplID uint32 `json:"templ"`
	}
)

type matchPlayersCache struct {
	cmder *db.RedisDB
}

func NewMatchPlayersCache(redisDB *db.RedisDB) *matchPlayersCache {
	return &matchPlayersCache{
		cmder: redisDB,
	}
}

func (c *matchPlayersCache) Add(ctx context.Context, players ...*entity.MatchPlayer) error {
	now := time.Now()

	setCmdMap := make(map[uint32]*redis.BoolCmd)
	if _, err := c.cmder.TxPipelined(ctx, func(pl redis.Pipeliner) error {
		for _, player := range players {
			mp := &matchPlayer{
				Uid:     player.Uid,
				Sex:     int32(player.Sex),
				TemplID: player.TemplID,
			}

			setCmdMap[player.Uid] = pl.HSetNX(ctx, matchPlayersKey, strconv.Itoa(int(player.Uid)), mp.marshal())
		}

		return nil
	}); err != nil {
		return err
	}

	setResultMap := make(map[uint32]bool)
	for uid, cmd := range setCmdMap {
		setResultMap[uid] = cmd.Val()
	}

	if _, err := c.cmder.TxPipelined(ctx, func(pl redis.Pipeliner) error {
		for _, player := range players {
			mb := &redis.Z{
				Score:  float64(now.Unix()),
				Member: player.Uid,
			}

			// 写入玩家信息成功才加入匹配池，避免被覆盖
			if setResultMap[player.Uid] {
				_ = pl.ZAdd(ctx, matchPlayerPoolKey, mb)
				_ = pl.ZAdd(ctx, c.keyOfTemplPool(player.TemplID), mb)
				_ = pl.ZIncrBy(ctx, matchTemplRankKey, 1, strconv.Itoa(int(player.TemplID)))

				_ = pl.Expire(ctx, c.keyOfTemplPool(player.TemplID), matchPlayerTTL)
			}
		}

		_ = pl.Expire(ctx, matchPlayersKey, matchPlayerTTL)
		_ = pl.Expire(ctx, matchPlayerPoolKey, matchPlayerTTL)
		_ = pl.Expire(ctx, matchTemplRankKey, matchPlayerTTL)

		return nil
	}); err != nil {
		return err
	}

	return nil
}

func (c *matchPlayersCache) Delete(ctx context.Context, players ...*entity.MatchPlayer) error {
	_, err := c.cmder.TxPipelined(ctx, func(pl redis.Pipeliner) error {
		for _, player := range players {
			_ = pl.HDel(ctx, matchPlayersKey, strconv.Itoa(int(player.Uid)))
			_ = pl.ZRem(ctx, c.keyOfTemplPool(player.TemplID), player.Uid)
			_ = pl.ZRem(ctx, matchPlayerPoolKey, player.Uid)
			_ = pl.ZAddArgsIncr(ctx, matchTemplRankKey, redis.ZAddArgs{
				XX: true,
				Members: []redis.Z{
					{
						Score:  -1,
						Member: strconv.Itoa(int(player.TemplID)),
					},
				},
			})
		}

		_ = pl.ZRemRangeByScore(ctx, matchTemplRankKey, "-inf", "0")
		return nil
	})

	return err
}

func (c *matchPlayersCache) Get(ctx context.Context, uid uint32) (*entity.MatchPlayer, error) {
	val, err := c.cmder.HGet(ctx, matchPlayersKey, strconv.Itoa(int(uid))).Result()
	if err != nil {
		if redis.IsNil(err) {
			return nil, nil
		}

		return nil, err
	}
	if val == "" {
		return nil, nil
	}

	var mp matchPlayer
	if err := mp.unmarshal(val); err != nil {
		return nil, err
	}

	return &entity.MatchPlayer{
		Uid:     mp.Uid,
		Sex:     account_go.USER_SEX(mp.Sex),
		TemplID: mp.TemplID,
	}, nil
}

func (c *matchPlayersCache) Scan(ctx context.Context, begin, end, offset, limit int64) (entity.MatchPlayerList, error) {
	members, err := c.cmder.ZRangeByScore(ctx, matchPlayerPoolKey, &redis.ZRangeBy{
		Min:    strconv.Itoa(int(begin)),
		Max:    strconv.Itoa(int(end)),
		Offset: offset,
		Count:  limit,
	}).Result()
	if err != nil {
		return nil, err
	}
	if len(members) == 0 {
		return nil, nil
	}

	vals, err := c.cmder.HMGet(ctx, matchPlayersKey, members...).Result()
	if err != nil {
		return nil, err
	}

	players := make([]*entity.MatchPlayer, 0, len(vals))
	for _, val := range vals {
		if s, ok := val.(string); ok {
			var mp matchPlayer
			if err := mp.unmarshal(s); err != nil {
				return nil, err
			}

			players = append(players, &entity.MatchPlayer{
				Uid:     mp.Uid,
				Sex:     account_go.USER_SEX(mp.Sex),
				TemplID: mp.TemplID,
			})
		}
	}

	return players, nil
}

func (c *matchPlayersCache) ScanByTemplID(ctx context.Context, templID uint32, begin, end, offset, limit int64) (entity.MatchPlayerList, error) {
	members, err := c.cmder.ZRangeByScore(ctx, c.keyOfTemplPool(templID), &redis.ZRangeBy{
		Min:    strconv.Itoa(int(begin)),
		Max:    strconv.Itoa(int(end)),
		Offset: offset,
		Count:  limit,
	}).Result()
	if err != nil {
		return nil, err
	}
	if len(members) == 0 {
		return nil, nil
	}

	vals, err := c.cmder.HMGet(ctx, matchPlayersKey, members...).Result()
	if err != nil {
		return nil, err
	}

	players := make([]*entity.MatchPlayer, 0, len(vals))
	for _, val := range vals {
		if s, ok := val.(string); ok {
			var mp matchPlayer
			if err := mp.unmarshal(s); err != nil {
				return nil, err
			}

			players = append(players, &entity.MatchPlayer{
				Uid:     mp.Uid,
				Sex:     account_go.USER_SEX(mp.Sex),
				TemplID: mp.TemplID,
			})
		}
	}

	return players, nil
}

func (c *matchPlayersCache) RandByTemplID(ctx context.Context, templIDList []uint32, limit int) (map[uint32]entity.MatchPlayerList, error) {
	cmds := make(map[uint32]*redis.StringSliceCmd)
	if _, err := c.cmder.Pipelined(ctx, func(pl redis.Pipeliner) error {
		for _, templID := range templIDList {
			cmds[templID] = pl.ZRandMember(ctx, c.keyOfTemplPool(templID), limit, false)
		}

		return nil
	}); err != nil {
		return nil, err
	}

	var fields []string
	for _, cmd := range cmds {
		fields = append(fields, cmd.Val()...)
	}
	if len(fields) == 0 {
		return nil, nil
	}

	vals, err := c.cmder.HMGet(ctx, matchPlayersKey, fields...).Result()
	if err != nil {
		return nil, err
	}

	players := make(map[uint32]entity.MatchPlayerList)
	for _, val := range vals {
		if s, ok := val.(string); ok {
			var mp matchPlayer
			if err := mp.unmarshal(s); err != nil {
				return nil, err
			}

			players[mp.TemplID] = append(players[mp.TemplID], &entity.MatchPlayer{
				Uid:     mp.Uid,
				Sex:     account_go.USER_SEX(mp.Sex),
				TemplID: mp.TemplID,
			})
		}
	}

	return players, nil
}

func (c *matchPlayersCache) TopKTempl(ctx context.Context, k int64) ([]uint32, error) {
	members, err := c.cmder.ZRevRange(ctx, matchTemplRankKey, 0, k).Result()
	if err != nil {
		return nil, err
	}
	if len(members) == 0 {
		return nil, nil
	}

	templIDList := make([]uint32, 0, len(members))
	for _, member := range members {
		templID, _ := strutil.ToUint32(member)
		templIDList = append(templIDList, templID)
	}

	return templIDList, nil
}

func (c *matchPlayersCache) keyOfTemplPool(templID uint32) string {
	return fmt.Sprintf("match:player:templ:pool:%d", templID)
}

func (player *matchPlayer) marshal() string {
	data, _ := json.Marshal(player)
	return string(data)
}

func (player *matchPlayer) unmarshal(data string) error {
	if player == nil {
		panic("player is nil")
	}

	if err := json.Unmarshal([]byte(data), player); err != nil {
		return err
	}

	return nil
}
