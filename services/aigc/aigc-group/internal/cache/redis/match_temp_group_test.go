package redis

import (
	"context"
	"testing"
	"time"

	pb "golang.52tt.com/protocol/services/aigc/aigc-group"
	"golang.52tt.com/services/aigc/aigc-group/internal/entity"
	"golang.52tt.com/services/aigc/aigc-group/internal/infra/db"
)

func Test_matchTempGroupsCache_Add(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx   context.Context
		group *entity.MatchTempGroup
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx: ctx,
				group: &entity.MatchTempGroup{
					ID:      "1",
					Type:    pb.GroupType_GROUP_TYPE_MULTI_USER,
					TemplID: 999999,
					Members: entity.MatchTempGroupMemberList{
						{
							Uid:   1,
							Sex:   0,
							Ready: false,
						},
						{
							Uid:   2,
							Sex:   1,
							Ready: false,
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &matchTempGroupsCache{
				cmder: tt.fields.cmder,
			}
			if err := c.Add(tt.args.ctx, tt.args.group); (err != nil) != tt.wantErr {
				t.Errorf("matchTempGroupsCache.Add() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_matchTempGroupsCache_UpdateMemberReady(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx     context.Context
		groupID string
		member  *entity.MatchTempGroupMember
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx:     ctx,
				groupID: "1",
				member: &entity.MatchTempGroupMember{
					Uid:   1,
					Sex:   1,
					Ready: true,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &matchTempGroupsCache{
				cmder: tt.fields.cmder,
			}
			got, err := c.UpdateMember(tt.args.ctx, tt.args.groupID, tt.args.member)
			if (err != nil) != tt.wantErr {
				t.Errorf("matchTempGroupsCache.UpdateMemberReady() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			for _, member := range got {
				t.Logf("matchTempGroupsCache.UpdateMemberReady() = %+v", member)
			}
		})
	}
}

func Test_matchTempGroupsCache_Get(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx: ctx,
				id:  "1",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &matchTempGroupsCache{
				cmder: tt.fields.cmder,
			}
			got, err := c.Get(tt.args.ctx, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("matchTempGroupsCache.Get() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("matchTempGroupsCache.Get() = %+v", got)
		})
	}
}

func Test_matchTempGroupsCache_Scan(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx    context.Context
		begin  int64
		end    int64
		offset int64
		limit  int64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx:    ctx,
				begin:  0,
				end:    time.Now().Unix(),
				offset: 0,
				limit:  100,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &matchTempGroupsCache{
				cmder: tt.fields.cmder,
			}
			got, err := c.Scan(tt.args.ctx, tt.args.begin, tt.args.end, tt.args.offset, tt.args.limit)
			if (err != nil) != tt.wantErr {
				t.Errorf("matchTempGroupsCache.Scan() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("matchTempGroupsCache.Scan() = %+v", got)
		})
	}
}

func Test_matchTempGroupsCache_Delete(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx   context.Context
		group *entity.MatchTempGroup
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx: ctx,
				group: &entity.MatchTempGroup{
					ID:      "1",
					TemplID: 0,
					Members: entity.MatchTempGroupMemberList{
						{
							Uid: 1,
						},
						{
							Uid: 2,
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &matchTempGroupsCache{
				cmder: tt.fields.cmder,
			}
			if err := c.Delete(tt.args.ctx, tt.args.group); (err != nil) != tt.wantErr {
				t.Errorf("matchTempGroupsCache.Delete() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
