package redis

import (
	"context"
	"testing"
	"time"

	"golang.52tt.com/services/aigc/aigc-group/internal/entity"
	"golang.52tt.com/services/aigc/aigc-group/internal/infra/db"
)

func Test_matchPlayersCache_Add(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx     context.Context
		players []*entity.MatchPlayer
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx: ctx,
				players: []*entity.MatchPlayer{
					{
						Uid:     1,
						Sex:     1,
						TemplID: 999999,
					},
					{
						Uid:     2,
						Sex:     0,
						TemplID: 999999,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &matchPlayersCache{
				cmder: tt.fields.cmder,
			}
			if err := c.Add(tt.args.ctx, tt.args.players...); (err != nil) != tt.wantErr {
				t.Errorf("matchPlayersCache.Add() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_matchPlayersCache_Get(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx: ctx,
				uid: 2,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &matchPlayersCache{
				cmder: tt.fields.cmder,
			}
			got, err := c.Get(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("matchPlayersCache.Get() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("matchPlayersCache.Get() = %v", got)
		})
	}
}

func Test_matchPlayersCache_Scan(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx    context.Context
		begin  int64
		end    int64
		offset int64
		limit  int64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx:    ctx,
				begin:  0,
				end:    time.Now().Unix(),
				offset: 0,
				limit:  1000,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &matchPlayersCache{
				cmder: tt.fields.cmder,
			}
			got, err := c.Scan(tt.args.ctx, tt.args.begin, tt.args.end, tt.args.offset, tt.args.limit)
			if (err != nil) != tt.wantErr {
				t.Errorf("matchPlayersCache.Scan() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			for _, player := range got {
				t.Logf("matchPlayersCache.Scan() = %+v", player)
			}
		})
	}
}

func Test_matchPlayersCache_Delete(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx     context.Context
		players []*entity.MatchPlayer
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx: ctx,
				players: []*entity.MatchPlayer{
					{
						Uid:     1,
						TemplID: 999999,
					},
					{
						Uid:     2,
						Sex:     1,
						TemplID: 999999,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &matchPlayersCache{
				cmder: tt.fields.cmder,
			}
			if err := c.Delete(tt.args.ctx, tt.args.players...); (err != nil) != tt.wantErr {
				t.Errorf("matchPlayersCache.Delete() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_matchPlayersCache_RandByTemplID(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx         context.Context
		templIDList []uint32
		limit       int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx:         ctx,
				templIDList: []uint32{888888, 999999},
				limit:       10,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &matchPlayersCache{
				cmder: tt.fields.cmder,
			}
			got, err := c.RandByTemplID(tt.args.ctx, tt.args.templIDList, tt.args.limit)
			if (err != nil) != tt.wantErr {
				t.Errorf("matchPlayersCache.RandByTemplID() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("matchPlayersCache.RandByTemplID() = %v", got)
		})
	}
}
