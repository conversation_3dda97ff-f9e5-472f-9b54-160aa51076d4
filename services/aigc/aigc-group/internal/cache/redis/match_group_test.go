package redis

import (
	"context"
	"reflect"
	"testing"
	"time"

	account_go "golang.52tt.com/protocol/services/account-go"
	"golang.52tt.com/services/aigc/aigc-group/internal/entity"
	"golang.52tt.com/services/aigc/aigc-group/internal/infra/db"
)

func Test_matchGroupsCache_Add(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx       context.Context
		group     *entity.MatchGroup
		expiredAt time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx: ctx,
				group: &entity.MatchGroup{
					Id:      1,
					TemplId: 1,

					Any:    1,
					Male:   1,
					Female: 1,

					AnyLimit:    1,
					MaleLimit:   1,
					FemaleLimit: 1,
				},
				expiredAt: time.Now().Add(time.Minute),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &matchGroupsCache{
				cmder: tt.fields.cmder,
			}
			if err := c.Add(tt.args.ctx, tt.args.group, tt.args.expiredAt); (err != nil) != tt.wantErr {
				t.Errorf("matchGroupsCache.Add() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_matchGroupsCache_UpdateExpireTime(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx       context.Context
		templId   uint32
		groupId   uint32
		expiredAt time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx:       ctx,
				templId:   1,
				groupId:   1,
				expiredAt: time.Now().Add(time.Minute),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &matchGroupsCache{
				cmder: tt.fields.cmder,
			}
			if err := c.UpdateExpireTime(tt.args.ctx, tt.args.templId, tt.args.groupId, tt.args.expiredAt); (err != nil) != tt.wantErr {
				t.Errorf("matchGroupsCache.UpdateExpireTime() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_matchGroupsCache_DecrSex(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx     context.Context
		groupId uint32
		sex     account_go.USER_SEX
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *entity.MatchGroup
		want1   bool
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx:     ctx,
				groupId: 1,
				sex:     0,
			},
			want: &entity.MatchGroup{
				Id:      1,
				TemplId: 1,

				Any:    1,
				Male:   1,
				Female: 0,

				AnyLimit:    1,
				MaleLimit:   1,
				FemaleLimit: 1,
			},
			want1:   true,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &matchGroupsCache{
				cmder: tt.fields.cmder,
			}
			got, got1, err := c.DecrSex(tt.args.ctx, tt.args.groupId, tt.args.sex)
			if (err != nil) != tt.wantErr {
				t.Errorf("matchGroupsCache.DecrSex() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("matchGroupsCache.DecrSex() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("matchGroupsCache.DecrSex() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func Test_matchGroupsCache_IncrSex(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx     context.Context
		groupId uint32
		sex     account_go.USER_SEX
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx:     ctx,
				groupId: 1,
				sex:     0,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &matchGroupsCache{
				cmder: tt.fields.cmder,
			}
			got, err := c.IncrSex(tt.args.ctx, tt.args.groupId, tt.args.sex)
			if (err != nil) != tt.wantErr {
				t.Errorf("matchGroupsCache.IncrSex() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("matchGroupsCache.IncrSex() = %v", got)
		})
	}
}

func Test_matchGroupsCache_ScanByTemplId(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx     context.Context
		templId uint32
		begin   int64
		end     int64
		offset  int64
		limit   int64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx:     ctx,
				templId: 1,
				begin:   0,
				end:     time.Now().Add(time.Hour).Unix(),
				offset:  0,
				limit:   100,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &matchGroupsCache{
				cmder: tt.fields.cmder,
			}
			got, err := c.ScanByTemplId(tt.args.ctx, tt.args.templId, tt.args.begin, tt.args.end, tt.args.offset, tt.args.limit)
			if (err != nil) != tt.wantErr {
				t.Errorf("matchGroupsCache.ScanByTemplId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("matchGroupsCache.ScanByTemplId() = %v", got)
		})
	}
}

func Test_matchGroupsCache_Delete(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx     context.Context
		templId uint32
		groupId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx:     ctx,
				templId: 1,
				groupId: 1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &matchGroupsCache{
				cmder: tt.fields.cmder,
			}
			if err := c.Delete(tt.args.ctx, tt.args.templId, tt.args.groupId); (err != nil) != tt.wantErr {
				t.Errorf("matchGroupsCache.Delete() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
