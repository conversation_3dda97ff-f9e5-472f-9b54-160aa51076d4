package redis

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"

	"golang.52tt.com/services/aigc/aigc-group/internal/cache"
	"golang.52tt.com/services/aigc/aigc-group/internal/entity"
	"golang.52tt.com/services/aigc/aigc-group/internal/infra/db"
)

type schedTasksRedisCache struct {
	cmder *db.RedisDB
}

func NewSchedTasksRedisCache(cmder *db.RedisDB) cache.SchedTasksCache {
	return &schedTasksRedisCache{
		cmder: cmder,
	}
}

func (c *schedTasksRedisCache) Add(ctx context.Context, tasks ...*entity.SchedTask) error {
	_, err := c.cmder.TxPipelined(ctx, func(pl redis.Pipeliner) error {
		for _, task := range tasks {
			_ = pl.ZAdd(ctx, c.keyOfQueue(task.Type), &redis.Z{
				Score:  float64(task.ExpireAt.Unix()),
				Member: task.Key,
			})
		}
		return nil
	})

	return err
}

func (c *schedTasksRedisCache) Remove(ctx context.Context, taskType entity.SchedTaskType, keys ...string) error {
	_, err := c.cmder.ZRem(ctx, c.keyOfQueue(taskType), keys).Result()
	return err
}

func (c *schedTasksRedisCache) Scan(ctx context.Context, taskType entity.SchedTaskType, begin, end, offset, limit int64) (entity.SchedTaskList, error) {
	zs, err := c.cmder.ZRangeByScoreWithScores(ctx, c.keyOfQueue(taskType), &redis.ZRangeBy{
		Min:    strconv.Itoa(int(begin)),
		Max:    strconv.Itoa(int(end)),
		Offset: offset,
		Count:  limit,
	}).Result()
	if err != nil {
		return nil, err
	}
	if len(zs) == 0 {
		return nil, nil
	}

	tasks := make([]*entity.SchedTask, 0, len(zs))
	for _, z := range zs {
		if key, ok := z.Member.(string); ok {
			tasks = append(tasks, &entity.SchedTask{
				Key:      key,
				Type:     taskType,
				ExpireAt: time.Unix(int64(z.Score), 0),
			})
		}
	}

	return tasks, nil
}

func (c *schedTasksRedisCache) keyOfQueue(taskType entity.SchedTaskType) string {
	return fmt.Sprintf("sched:task:%d", taskType)
}
