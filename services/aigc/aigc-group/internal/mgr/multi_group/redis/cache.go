package redis

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"golang.52tt.com/pkg/log"
	"strconv"
	"time"
)

//go:generate ifacemaker -f cache.go -s Cache -p redis -i ICache -o cache_api.go
//go:generate mockgen -destination=../mocks/cache.go -package=mocks -mock_names=IStore=MockICache golang.52tt.com/services/aigc/aigc-group/internal/mgr/multi_group/redis ICache
type Cache struct {
	cmder redis.Cmdable
}

func NewCache(redisCli redis.Cmdable) *Cache {
	cache := &Cache{
		cmder: redisCli,
	}

	return cache
}

func (c *Cache) getUserMultiGroupChatCntKey(uid uint32) string {
	nowTime := time.Now()
	nowDay := time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), 0, 0, 0, 0, time.Local).Unix()
	return fmt.Sprintf("multi_group_chat_cnt_%d_%d", nowDay, uid)
}

func (c *Cache) IncrUserMultiGroupChatCnt(ctx context.Context, uid uint32) (uint32, error) {
	strKey := c.getUserMultiGroupChatCntKey(uid)
	nowCnt, err := c.cmder.IncrBy(ctx, strKey, 1).Result()
	if err != nil {
		return 0, err
	}
	if nowCnt > 200 {
		log.ErrorWithCtx(ctx, "cnt exceed limit, uid:%d, cnt:%d", uid, nowCnt)
	}
	err = c.cmder.Expire(ctx, strKey, 24*time.Hour).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "IncrUserMultiGroupChatCnt Expire err, uid:%d, cnt:%d", uid, nowCnt)
	}
	return uint32(nowCnt), nil
}

func (c *Cache) GetUserMultiGroupChatCnt(ctx context.Context, uid uint32) (int, error) {
	strKey := c.getUserMultiGroupChatCntKey(uid)
	res, err := c.cmder.Get(ctx, strKey).Result()
	if err != nil {
		if redis.IsNil(err) {
			return 0, nil
		}
		log.ErrorWithCtx(ctx, "GetUserMultiGroupChatCnt err:%v, uid:%d", err, uid)
		return 0, err
	}
	valInt, _ := strconv.Atoi(res)
	return valInt, nil
}

func (c *Cache) getMultiGroupUserRoleSelectKey(groupId uint32) string {
	return fmt.Sprintf("multi_group_role_select_%d", groupId)
}

func (c *Cache) SetMultiGroupUserRoleSelectInfo(ctx context.Context, groupId uint32, userSelectInfoMap map[string]interface{}) error {
	strKey := c.getMultiGroupUserRoleSelectKey(groupId)
	_, err := c.cmder.HMSet(ctx, strKey, userSelectInfoMap).Result()
	if err != nil {
		return err
	}
	/*err = c.cmder.Expire(ctx, strKey, 2*24*time.Hour).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "SetMultiGroupUserRoleSelectInfo Expire err, groupId:%d", groupId)
	}*/
	return nil
}

func (c *Cache) DelMultiGroupUserRoleSelectInfo(ctx context.Context, groupId uint32) error {
	strKey := c.getMultiGroupUserRoleSelectKey(groupId)
	err := c.cmder.Del(ctx, strKey).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelMultiGroupUserRoleSelectInfo err:%v", err)
		return err
	}
	return nil
}

func (c *Cache) GetMultiGroupUserRoleSelectInfo(ctx context.Context, groupId uint32) (map[string]string, error) {
	strKey := c.getMultiGroupUserRoleSelectKey(groupId)
	res, err := c.cmder.HGetAll(ctx, strKey).Result()
	if err != nil {
		if redis.IsNil(err) {
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "GetMultiGroupUserRoleSelectInfo HGetAll err:%v, groupId:%d", err, groupId)
		return nil, err
	}
	return res, nil
}

func (c *Cache) keyOfFakeCount(uid uint32) string {
	return fmt.Sprintf("fake:count:%d:%s", uid, time.Now().Format("20060102"))
}

func (c *Cache) GetFakeHelloCount(ctx context.Context, uid uint32) (int64, error) {
	key := c.keyOfFakeCount(uid)
	count, err := c.cmder.Get(ctx, key).Int64()
	if err != nil {
		if redis.IsNil(err) {
			return 0, nil // 如果不存在，则返回0
		}
		return count, err
	}
	return count, nil
}

func (c *Cache) IncrFakeHelloCount(ctx context.Context, uid uint32) (int64, error) {
	key := c.keyOfFakeCount(uid)
	count, err := c.cmder.Incr(ctx, key).Result()
	if err != nil {
		return 0, err
	}
	// 设置过期时间为24小时
	_ = c.cmder.Expire(ctx, key, 24*time.Hour)
	return count, nil
}
