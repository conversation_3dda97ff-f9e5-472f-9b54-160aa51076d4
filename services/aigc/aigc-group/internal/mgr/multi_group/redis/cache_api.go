// Code generated by ifacemaker; DO NOT EDIT.

package redis

import (
	"context"
)

// ICache ...
type ICache interface {
	IncrUserMultiGroupChatCnt(ctx context.Context, uid uint32) (uint32, error)
	GetUserMultiGroupChatCnt(ctx context.Context, uid uint32) (int, error)
	SetMultiGroupUserRoleSelectInfo(ctx context.Context, groupId uint32, userSelectInfoMap map[string]interface{}) error
	DelMultiGroupUserRoleSelectInfo(ctx context.Context, groupId uint32) error
	GetMultiGroupUserRoleSelectInfo(ctx context.Context, groupId uint32) (map[string]string, error)

	GetFakeHelloCount(ctx context.Context, uid uint32) (int64, error)
	IncrFakeHelloCount(ctx context.Context, uid uint32) (int64, error)
}
