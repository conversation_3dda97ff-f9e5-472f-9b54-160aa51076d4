package redis

import (
	"context"
	"github.com/smartystreets/goconvey/convey"
	redismocks "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/mocks"
	"testing"
)

var redisDao *Cache

func init() {
	redisCli := redismocks.Mock()
	if redisCli == nil {
		return
	}
	redisDao = NewCache(redisCli)
}

func TestMultiGroupUserRoleSelec(t *testing.T) {
	convey.Convey("TestMultiGroupUserRoleSelec", t, func() {
		ctx := context.Background()

		var groupId uint32 = 1
		mapInfo := make(map[string]interface{})
		mapInfo["123"] = "123"
		err := redisDao.SetMultiGroupUserRoleSelectInfo(ctx, groupId, mapInfo)
		convey.So(err, convey.ShouldBeNil)

		mapInfo2, err := redisDao.GetMultiGroupUserRoleSelectInfo(ctx, groupId)
		convey.So(err, convey.ShouldBeNil)
		//convey.So(mapInfo, convey.ShouldEqual, mapInfo2)
		t.Logf("mapInfo2:%v", mapInfo2)
	})
}
