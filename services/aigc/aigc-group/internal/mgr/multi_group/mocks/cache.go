// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-group/internal/mgr/multi_group/redis (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// DelMultiGroupUserRoleSelectInfo mocks base method.
func (m *MockICache) DelMultiGroupUserRoleSelectInfo(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelMultiGroupUserRoleSelectInfo", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelMultiGroupUserRoleSelectInfo indicates an expected call of DelMultiGroupUserRoleSelectInfo.
func (mr *MockICacheMockRecorder) DelMultiGroupUserRoleSelectInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelMultiGroupUserRoleSelectInfo", reflect.TypeOf((*MockICache)(nil).DelMultiGroupUserRoleSelectInfo), arg0, arg1)
}

// GetMultiGroupUserRoleSelectInfo mocks base method.
func (m *MockICache) GetMultiGroupUserRoleSelectInfo(arg0 context.Context, arg1 uint32) (map[string]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiGroupUserRoleSelectInfo", arg0, arg1)
	ret0, _ := ret[0].(map[string]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiGroupUserRoleSelectInfo indicates an expected call of GetMultiGroupUserRoleSelectInfo.
func (mr *MockICacheMockRecorder) GetMultiGroupUserRoleSelectInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiGroupUserRoleSelectInfo", reflect.TypeOf((*MockICache)(nil).GetMultiGroupUserRoleSelectInfo), arg0, arg1)
}

// GetUserMultiGroupChatCnt mocks base method.
func (m *MockICache) GetUserMultiGroupChatCnt(arg0 context.Context, arg1 uint32) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserMultiGroupChatCnt", arg0, arg1)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserMultiGroupChatCnt indicates an expected call of GetUserMultiGroupChatCnt.
func (mr *MockICacheMockRecorder) GetUserMultiGroupChatCnt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserMultiGroupChatCnt", reflect.TypeOf((*MockICache)(nil).GetUserMultiGroupChatCnt), arg0, arg1)
}

// IncrUserMultiGroupChatCnt mocks base method.
func (m *MockICache) IncrUserMultiGroupChatCnt(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrUserMultiGroupChatCnt", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrUserMultiGroupChatCnt indicates an expected call of IncrUserMultiGroupChatCnt.
func (mr *MockICacheMockRecorder) IncrUserMultiGroupChatCnt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrUserMultiGroupChatCnt", reflect.TypeOf((*MockICache)(nil).IncrUserMultiGroupChatCnt), arg0, arg1)
}

// SetMultiGroupUserRoleSelectInfo mocks base method.
func (m *MockICache) SetMultiGroupUserRoleSelectInfo(arg0 context.Context, arg1 uint32, arg2 map[string]interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMultiGroupUserRoleSelectInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetMultiGroupUserRoleSelectInfo indicates an expected call of SetMultiGroupUserRoleSelectInfo.
func (mr *MockICacheMockRecorder) SetMultiGroupUserRoleSelectInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMultiGroupUserRoleSelectInfo", reflect.TypeOf((*MockICache)(nil).SetMultiGroupUserRoleSelectInfo), arg0, arg1, arg2)
}
