package config

import (
	"sync/atomic"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/core/config/ttconfig"
	"gitlab.ttyuyin.com/tyr/x/log"
)

const defaultPageSize = 20

type AigcGroupConfig struct {
	// 用户加入群组数量上限
	UserJoinGroupLimit uint32 `json:"user_join_group_limit"`
	// 剧本列表pageSize
	ScriptListPageSize int64 `json:"script_list_page_size"`

	// 活跃群过期时间(秒)
	ActiveGroupExpire uint32 `json:"active_group_expire"`
	// 匹配玩家过期时间(秒)
	MatchPlayerExpire uint32 `json:"match_player_expire"`
	// 匹配临时群过期时间(秒)
	MatchTempGroupExpire uint32 `json:"match_temp_group_expire"`

	// 关注引导推送需要达到的发言次数
	FollowGuidePushUserSpeechNum uint32 `json:"follow_guide_push_user_speech_num"`
	// 用户每日可收到的关注引导次数
	FollowGuidePushUserDailyLimit uint32 `json:"follow_guide_push_user_daily_limit"`

	// 单人活跃群过期时间(秒)
	SingleActiveGroupExpire uint32 `json:"single_active_group_expire"`
	// 单人非活跃群过期时间(秒)
	SingleInactiveGroupExpire uint32 `json:"single_inactive_group_expire"`

	// 匹配群过期时间(秒)
	MatchGroupExpire uint32 `json:"match_group_expire"`
}

// Format
// 配置加载时调用Format报错则返回错误
// 配置更新时调用Format报错则放弃此次更新
func (s *AigcGroupConfig) Format() error {
	log.Infof("AigcGroupConfig.Format: %+v", s)
	return nil
}

var (
	atomicAigcGroupConfig *atomic.Value
)

// InitAigcGroupConfig
// 可以选择外部初始化或者直接init函数初始化
func InitAigcGroupConfig() error {
	cfg := &AigcGroupConfig{}
	atomCfg, err := ttconfig.AtomLoad("aigc-group", cfg)
	if nil != err {
		return err
	}
	atomicAigcGroupConfig = atomCfg
	return nil
}

func GetAigcGroupConfig() *AigcGroupConfig {
	return atomicAigcGroupConfig.Load().(*AigcGroupConfig)
}

func (s *AigcGroupConfig) GetUserJoinGroupLimit() uint32 {
	const defaultLimit = 100

	if s == nil || s.UserJoinGroupLimit == 0 {
		return defaultLimit
	}

	return s.UserJoinGroupLimit
}

func (s *AigcGroupConfig) GetScriptListPageSize() int64 {
	if s == nil || s.ScriptListPageSize <= 0 {
		return defaultPageSize
	}

	return s.ScriptListPageSize
}

func (s *AigcGroupConfig) GetActiveGroupExpire() time.Duration {
	if s == nil || s.ActiveGroupExpire == 0 {
		return 48 * time.Hour
	}

	return time.Duration(s.ActiveGroupExpire) * time.Second
}

func (s *AigcGroupConfig) GetMatchPlayerExpire() time.Duration {
	if s == nil || s.MatchPlayerExpire == 0 {
		return 30 * time.Minute
	}

	return time.Duration(s.MatchPlayerExpire) * time.Second
}

func (s *AigcGroupConfig) GetMatchTempGroupExpire() time.Duration {
	if s == nil || s.MatchTempGroupExpire == 0 {
		return 15 * time.Second
	}

	return time.Duration(s.MatchTempGroupExpire) * time.Second
}

func (s *AigcGroupConfig) GetFollowGuidePushUserDailyLimit() uint32 {
	if s == nil || s.FollowGuidePushUserDailyLimit == 0 {
		return 2
	}

	return s.FollowGuidePushUserDailyLimit
}

func (s *AigcGroupConfig) GetFollowGuidePushUserSpeechNum() uint32 {
	if s == nil || s.FollowGuidePushUserSpeechNum == 0 {
		return 3
	}

	return s.FollowGuidePushUserSpeechNum
}

func (s *AigcGroupConfig) GetSingleActiveGroupExpire() time.Duration {
	if s == nil || s.SingleActiveGroupExpire == 0 {
		return 24 * time.Hour
	}

	return time.Duration(s.SingleActiveGroupExpire) * time.Second
}

func (s *AigcGroupConfig) GetSingleInactiveGroupExpire() time.Duration {
	if s == nil || s.SingleInactiveGroupExpire == 0 {
		return 30 * time.Minute
	}

	return time.Duration(s.SingleInactiveGroupExpire) * time.Second
}

func (s *AigcGroupConfig) GetMatchGroupExpire() time.Duration {
	if s == nil || s.MatchGroupExpire == 0 {
		return 90 * time.Second
	}

	return time.Duration(s.MatchGroupExpire) * time.Second
}
