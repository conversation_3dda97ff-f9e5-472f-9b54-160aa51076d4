// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-group/internal/config (interfaces: Config)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	config "golang.52tt.com/services/aigc/aigc-group/internal/config/ttconfig"
)

// MockConfig is a mock of Config interface.
type MockConfig struct {
	ctrl     *gomock.Controller
	recorder *MockConfigMockRecorder
}

// MockConfigMockRecorder is the mock recorder for MockConfig.
type MockConfigMockRecorder struct {
	mock *MockConfig
}

// NewMockConfig creates a new mock instance.
func NewMockConfig(ctrl *gomock.Controller) *MockConfig {
	mock := &MockConfig{ctrl: ctrl}
	mock.recorder = &MockConfigMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConfig) EXPECT() *MockConfigMockRecorder {
	return m.recorder
}

// GetAigcGroupConfig mocks base method.
func (m *MockConfig) GetAigcGroupConfig() *config.AigcGroupConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAigcGroupConfig")
	ret0, _ := ret[0].(*config.AigcGroupConfig)
	return ret0
}

// GetAigcGroupConfig indicates an expected call of GetAigcGroupConfig.
func (mr *MockConfigMockRecorder) GetAigcGroupConfig() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAigcGroupConfig", reflect.TypeOf((*MockConfig)(nil).GetAigcGroupConfig))
}
