package redis

import (
	"context"
	"time"

	clusterTimer "gitlab.ttyuyin.com/tyr/x/cluster/timer"
	"gitlab.ttyuyin.com/tyr/x/cluster/timer/task"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/aigc/aigc-group/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-group/internal/timer"
)

type redisTimer struct {
	*clusterTimer.Timer
}

func NewRedisTimer(ctx context.Context, redisDB *db.RedisDB) (timer.Timer, error) {
	timerD, err := clusterTimer.NewTimerD(ctx, "aigc-group", clusterTimer.WithV8RedisCmdable(redisDB.Cmder()))
	if err != nil {
		log.Errorf("NewRedisTimer NewTimerD err:%v", err)
		return nil, err
	}

	return &redisTimer{
		Timer: timerD,
	}, nil
}

func (t *redisTimer) AddIntervalTask(name string, interval time.Duration, task task.Task) {
	t.Timer.AddIntervalTask(name, interval, task)
}

func (t *redisTimer) Stop() {
	t.Timer.Stop()
}
