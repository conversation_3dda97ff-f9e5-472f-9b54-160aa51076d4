package main

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/grpc"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/grpc/server" // server startup
	pb "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle"
	server2 "golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/server"


	_ "golang.52tt.com/pkg/hub/tyr/compatible/server"
)

func main() {
	var (
		svr *server2.Server
		cfg = &server2.StartConfig{}
		err error
	)

	// config file support yaml & json, default aigc-soulmate-middle.json/yaml
	if err := server.NewServer("aigc-soulmate-middle", cfg).
		AddGrpcServer(server.NewBuildOption().
			WithInitializeFunc(func(ctx context.Context, s *grpc.Server) error {
				if svr, err = server2.NewServer(ctx, cfg); err != nil {
					return err
				}

				// register custom grpc server
				pb.RegisterAigcSoulmateMiddleServer(s, svr)
				return nil
			}),
		).
		WithCloseFunc(func(ctx context.Context) {
			// do something when server terminating
			svr.ShutDown()
		}).
		Start(); err != nil {
		log.Errorf("server start fail, err: %v", err)
	}
}
