package report

import (
	"context"
	aigc_group "golang.52tt.com/protocol/services/aigc/aigc-group"

	pb "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle"
)

type (
	GroupMsgData struct {
		Uid        uint32
		RoleId     uint32
		SenderType pb.GroupSendType

		BizType pb.ImBusiType
		MsgType pb.ImMsgContentType
		Content string

		GroupId           uint32
		GroupTemplateId   uint32
		CmdType           pb.ImCmdType
		IsTrigger         bool
		IsUseUp           bool
		TransparentExtMsg string
		GroupTemplateType aigc_group.GroupType
	}
)

//go:generate mockgen -destination=mocks/reporter.go -package=mocks golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/report Reporter
type Reporter interface {
	Close()

	ReportGroupMsg(ctx context.Context, data GroupMsgData)
}
