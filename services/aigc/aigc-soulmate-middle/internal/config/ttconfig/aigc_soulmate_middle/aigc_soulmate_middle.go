package config

import (
	"encoding/json"
	"gitlab.ttyuyin.com/avengers/tyr/core/config/ttconfig"
	"golang.52tt.com/pkg/log"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	aigc_trigger "golang.52tt.com/protocol/services/aigc/aigc-trigger"
	"sync/atomic"
)

const (
	CurDayPet = "pet"
	CurDay    = "other"
)

type AigcSoulmateMiddleConfig struct {
	SpecifiedSentenceSwitch       bool     `json:"specified_sentence_switch"` // true 需要走白名单
	SpecifiedSentenceWhiteUidList []uint32 `json:"specified_sentence_white_uid_list"`

	CurDayCountCfgMap map[uint32]interface{} `json:"cur_day_count_cfg_map"` // key:businessType value:今日句数配置
}

type RoleCurDayCfg struct {
	RoleTypeCountMap map[string]uint32 `json:"role_type_count_map"` // key:roleType value:count
}

type GroupCurDayCfg struct {
	Count uint32 `json:"count"`
}

// Format
// 配置加载时调用Format报错则返回错误
// 配置更新时调用Format报错则放弃此次更新
func (s *AigcSoulmateMiddleConfig) Format() error {
	return nil
}

var (
	atomicAigcSoulmateMiddleConfig *atomic.Value
)

func init() {
	//if err := InitAigcSoulmateMiddleConfig(); err != nil {
	//    panic(err)
	//}
}

// InitAigcSoulmateMiddleConfig
// 可以选择外部初始化或者直接init函数初始化
func InitAigcSoulmateMiddleConfig() error {
	cfg := &AigcSoulmateMiddleConfig{}
	atomCfg, err := ttconfig.AtomLoad("aigc-soulmate-middle", cfg)
	if nil != err {
		return err
	}
	atomicAigcSoulmateMiddleConfig = atomCfg
	return nil
}

func GetAigcSoulmateMiddleConfig() *AigcSoulmateMiddleConfig {
	return atomicAigcSoulmateMiddleConfig.Load().(*AigcSoulmateMiddleConfig)
}

func (s *AigcSoulmateMiddleConfig) IsInWhiteList(uid uint32) bool {
	if s == nil || len(s.SpecifiedSentenceWhiteUidList) == 0 {
		return false
	}
	for _, u := range s.SpecifiedSentenceWhiteUidList {
		if u == uid {
			return true
		}
	}
	return false
}

func (s *AigcSoulmateMiddleConfig) GetSpecifiedSentenceSwitch() bool {
	if s == nil {
		return false
	}
	return s.SpecifiedSentenceSwitch
}

func (s *AigcSoulmateMiddleConfig) GetCurDayCfgCount(businessType, roleType uint32) uint32 {
	if s == nil || len(s.CurDayCountCfgMap) == 0 {
		return 0
	}
	if _, exists := s.CurDayCountCfgMap[businessType]; !exists {
		log.Errorf("GetCurDayCfgCount businessType:%d not exists", businessType)
		return 0
	}
	// 2. 将 interface{} 转回 JSON 字节
	data, err := json.Marshal(s.CurDayCountCfgMap[businessType])
	if err != nil {
		log.Errorf("序列化失败: %v", err)
		return 0
	}
	switch aigc_trigger.BusinessType(businessType) {
	case aigc_trigger.BusinessType_BUSINESS_TYPE_MUTI_PLAYER:
		var groupCfg GroupCurDayCfg
		if err := json.Unmarshal(data, &groupCfg); err != nil {
			log.Errorf("反序列化失败: %v", err)
			return 0
		}
		return groupCfg.Count

	case aigc_trigger.BusinessType_BUSINESS_TYPE_SINGLE_CHAT:
		// 3. 直接反序列化为目标结构体
		var roleCfg RoleCurDayCfg
		if err := json.Unmarshal(data, &roleCfg); err != nil {
			log.Errorf("反序列化失败: %v", err)
			return 0
		}
		if aigc_soulmate.AIRoleType(roleType) == aigc_soulmate.AIRoleType_AIRoleTypePet {
			return roleCfg.RoleTypeCountMap[CurDayPet]
		} else {
			return roleCfg.RoleTypeCountMap[CurDay]
		}
	default:
		return 0
	}

}
