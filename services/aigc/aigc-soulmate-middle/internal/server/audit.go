package server

import (
	"context"
	censoring_proxy "golang.52tt.com/clients/censoring-proxy"

	"golang.52tt.com/pkg/audit"
	"golang.52tt.com/pkg/log"

	pb "golang.52tt.com/protocol/services/chat-bot"
	cybros_arbiter_v2 "golang.52tt.com/protocol/services/cybros/arbiter/v2"
)

type Audit struct {
	CensoringProxy censoring_proxy.IClient
}

func NewAudit(censoringProxy censoring_proxy.IClient) *Audit {
	return &Audit{
		CensoringProxy: censoringProxy,
	}
}

func cybrosSuggestionToAuditResult(suggestion cybros_arbiter_v2.Suggestion) pb.AuditResult {
	var result pb.AuditResult
	switch suggestion {
	case cybros_arbiter_v2.Suggestion_REVIEW:
		result = pb.AuditResult_AuditResultReview
	case cybros_arbiter_v2.Suggestion_PASS:
		result = pb.AuditResult_AuditResultPass
	case cybros_arbiter_v2.Suggestion_REJECT:
		result = pb.AuditResult_AuditResultReject
	default:
		log.Warnf("cybrosSuggestionToAuditResult invalid suggestion %d", suggestion)
	}

	return result
}

func (c *Audit) SyncScanText(ctx context.Context, auditId uint32, scene audit.SceneCodeType, text, alias string) (pb.AuditResult, error) {
	resp, err := c.CensoringProxy.Text().SyncScanText(ctx, &cybros_arbiter_v2.SyncTextCheckReq{
		Context: &cybros_arbiter_v2.TaskContext{
			SceneCode:   string(scene),
			DeviceInfo:  &cybros_arbiter_v2.Device{},
			BelongObjId: alias,
			UserInfo: &cybros_arbiter_v2.User{
				Id: uint64(auditId),
			},
		},
		Text: text,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SyncScanText uid(%d) sceneCode(%s) text(%s) err: %v", auditId, scene, text, err)
		return pb.AuditResult_AuditResultReview, err
	}

	log.InfoWithCtx(ctx, "SyncScanText uid(%d) sceneCode(%s) text(%s) result: %d", auditId, scene, text, resp.GetResult())
	return cybrosSuggestionToAuditResult(cybros_arbiter_v2.Suggestion(resp.GetResult())), nil
}
