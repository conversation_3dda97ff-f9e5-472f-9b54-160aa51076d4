package server

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	aigc_common "golang.52tt.com/protocol/services/aigc/aigc-common"
	aigc_intimacy "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	aigc_push "golang.52tt.com/protocol/services/aigc/aigc-push"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	pb "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle"
	aigc_trigger "golang.52tt.com/protocol/services/aigc/aigc-trigger"
	config "golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/config/ttconfig/aigc_soulmate_middle"
)

func (s *Server) IsReachLimit(ctx context.Context, req *pb.IsReachLimitReq) (
	*pb.IsReachLimitResp, error) {
	out := &pb.IsReachLimitResp{}
	if req.GetUid() == 0 || req.GetEntity() == nil ||
		aigc_trigger.BusinessType(req.GetBusinessType()) == aigc_trigger.BusinessType_BUSINESS_TYPE_UNSPECIFIED {
		log.ErrorWithCtx(ctx, "IsReachLimit invalid req :%s", req.String())
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	var roleType uint32
	entityInst := req.GetEntity()
	uid := req.GetUid()
	if entityInst.GetType() == pb.Entity_TYPE_PARTNER {
		partnerResp, err := s.soulmateClient.GetAIPartner(ctx, &aigc_soulmate.GetAIPartnerReq{
			Id: entityInst.GetId(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "IsReachLimit GetAIPartner uid:%d entityInst:%s err: %v", uid, entityInst.String(), err)
			return out, err
		}
		roleType = uint32(partnerResp.GetPartner().GetRole().GetType())
	} else { // 默认当作多角色，单人群聊与多角色,树洞共享今日句数
		roleType = uint32(aigc_soulmate.AIRoleType_AIRoleTypeGame)
	}

	curCountResp, err := s.aigcCommonClient.BatGetCurSentenceCount(ctx, &aigc_common.BatGetCurSentenceCountRequest{
		Uid:          uid,
		Entity:       convertToCommonEntity(entityInst),
		BusinessType: req.GetBusinessType(),
		SentenceType: []uint32{
			uint32(pb.SentenceType_SENTENCE_TYPE_ROLE_SPECIFIED),
			uint32(pb.SentenceType_SENTENCE_TYPE_CUR_DAY),
		},
		RoleType:                roleType,
		NeedAvailableExtraCount: true,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "IsReachLimit BatGetCurSentenceCount req:%s err :%v", req.String(), err)
		return out, err
	}

	benefitResp, err := s.aigcIntimacyClient.GetBenefitConfig(ctx, &aigc_intimacy.GetBenefitConfigRequest{
		Entity: convertToIntimacyEntity(entityInst),
		Uid:    uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "IsReachLimit GetBenefitConfig uid:%d entityInst:%s err: %v", uid, entityInst.String(), err)
		return out, err
	}
	roleSpecifiedUserNum := curCountResp.GetCurNumMap()[uint32(pb.SentenceType_SENTENCE_TYPE_ROLE_SPECIFIED)]
	curDayUsedNum := curCountResp.GetCurNumMap()[uint32(pb.SentenceType_SENTENCE_TYPE_CUR_DAY)]
	extraAvailableNum := curCountResp.GetAvailableExtraNum()
	roleSpecifiedCfgNum := benefitResp.GetBenefits().GetExtraSendMsgCount()
	cfgNum := config.GetAigcSoulmateMiddleConfig().GetCurDayCfgCount(req.GetBusinessType(), roleType)

	if roleSpecifiedUserNum < roleSpecifiedCfgNum || curDayUsedNum < cfgNum || extraAvailableNum > 0 {
		out.IsReach = false
		log.InfoWithCtx(ctx, "IsReachLimit has sentence req:%s curNumMap:%s roleSpecifiedCfgNum:%d "+
			"cur_day_cfg_num:%d", req.String(), curCountResp.String(), roleSpecifiedCfgNum, cfgNum)
		return out, nil
	}
	out.IsReach = true
	log.InfoWithCtx(ctx, "IsReachLimit no sentence req:%s curNumMap:%s roleSpecifiedCfgNum:%d "+
		"cur_day_cfg_num:%d", req.String(), curCountResp.String(), roleSpecifiedCfgNum, cfgNum)
	return out, nil
}

func (s *Server) ConsumeSentenceCount(ctx context.Context, req *pb.ConsumeSentenceCountReq) (
	*pb.ConsumeSentenceCountResp, error) {
	out := &pb.ConsumeSentenceCountResp{}
	if req.GetUid() == 0 ||
		aigc_trigger.BusinessType(req.GetBusinessType()) == aigc_trigger.BusinessType_BUSINESS_TYPE_UNSPECIFIED {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	curCountResp, err := s.aigcCommonClient.BatGetCurSentenceCount(ctx, &aigc_common.BatGetCurSentenceCountRequest{
		Uid:          req.GetUid(),
		Entity:       convertToCommonEntity(req.GetEntity()),
		BusinessType: req.GetBusinessType(),
		SentenceType: []uint32{
			uint32(pb.SentenceType_SENTENCE_TYPE_ROLE_SPECIFIED),
			uint32(pb.SentenceType_SENTENCE_TYPE_CUR_DAY),
		},
		RoleType: req.GetRoleType(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ConsumeSentenceCount BatGetCurSentenceCount req:%s err :%v", req.String(), err)
		return out, err
	}

	//消耗的优先级为：专属句数 > 当日句数 > 额外句数
	var success, needSendSpecialTips, needSendCurDayTips, needSendExtraTips bool
	success, needSendSpecialTips = s.consumeSpecialCount(ctx, req.GetUid(), req.GetBusinessType(),
		curCountResp.GetCurNumMap()[uint32(pb.SentenceType_SENTENCE_TYPE_ROLE_SPECIFIED)], req.GetEntity())
	if !success {
		success, needSendCurDayTips = s.consumeCurDayCount(ctx, req.GetUid(), req.GetBusinessType(), req.GetRoleType(),
			curCountResp.GetCurNumMap()[uint32(pb.SentenceType_SENTENCE_TYPE_CUR_DAY)], req.GetEntity())
		if !success {
			if req.GetRoleType() == uint32(aigc_soulmate.AIRoleType_AIRoleTypePet) {
				// 桌宠不能使用额外回复次数
				needSendExtraTips = true
			} else {
				success, needSendExtraTips = s.consumeExtraCount(ctx, req.GetUid(), req.GetBusinessType())
				if success {
					out.UsedType = pb.SentenceType_SENTENCE_TYPE_EXTRA
				}
			}
		} else {
			out.UsedType = pb.SentenceType_SENTENCE_TYPE_CUR_DAY
		}
	} else {
		out.UsedType = pb.SentenceType_SENTENCE_TYPE_ROLE_SPECIFIED
	}
	out.Success = success
	out.NeedSpecialTip = needSendSpecialTips
	out.NeedCurDayTip = needSendCurDayTips
	out.NeedExtraTip = needSendExtraTips
	log.InfoWithCtx(ctx, "ConsumeSentenceCount finish curCountResp:%s  req:%s resp:%s",
		curCountResp.String(), req.String(), out.String())
	return out, nil
}

func (s *Server) consumeSpecialCount(ctx context.Context, uid, businessType, usedNum uint32, entityInst *pb.Entity) (success, needTips bool) {
	if entityInst == nil || entityInst.GetType() != pb.Entity_TYPE_PARTNER {
		return false, false
	}
	benefitResp, err := s.aigcIntimacyClient.GetBenefitConfig(ctx, &aigc_intimacy.GetBenefitConfigRequest{
		Entity: convertToIntimacyEntity(entityInst),
		Uid:    uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ConsumeSentenceCount GetBenefitConfig uid:%d entityInst:%s err: %v", uid, entityInst.String(), err)
		return false, false
	}

	if benefitResp.GetBenefits().GetExtraSendMsgCount() == 0 || usedNum > benefitResp.GetBenefits().GetExtraSendMsgCount() {
		return false, false
	} else if usedNum == benefitResp.GetBenefits().GetExtraSendMsgCount() {
		return false, false
	} else {
		consumeResp, err := s.aigcCommonClient.ConsumeSentenceCount(ctx, &aigc_common.ConsumeSentenceCountRequest{
			Uid:          uid,
			Entity:       convertToCommonEntity(entityInst),
			SentenceType: uint32(pb.SentenceType_SENTENCE_TYPE_ROLE_SPECIFIED),
			BusinessType: businessType,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "ConsumeSentenceCount ConsumeSentenceCount uid:%d entityInst:%s err: %v", uid, entityInst.String(), err)
			return false, false
		}
		success = true
		if consumeResp.GetCurNum() == benefitResp.GetBenefits().GetExtraSendMsgCount() {
			needTips = true
		}
	}

	return success, needTips
}

func (s *Server) consumeCurDayCount(ctx context.Context, uid, businessType, roleType, usedNum uint32, entityInst *pb.Entity) (success, needTips bool) {

	cfgNum := config.GetAigcSoulmateMiddleConfig().GetCurDayCfgCount(businessType, roleType)
	if cfgNum == 0 || usedNum >= cfgNum {
		return false, false
	}
	consumeResp, err := s.aigcCommonClient.ConsumeSentenceCount(ctx, &aigc_common.ConsumeSentenceCountRequest{
		Uid:          uid,
		SentenceType: uint32(pb.SentenceType_SENTENCE_TYPE_CUR_DAY),
		BusinessType: businessType,
		RoleType:     roleType,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ConsumeSentenceCount ConsumeSentenceCount uid:%d entityInst:%s err: %v", uid, entityInst.String(), err)
		return false, false
	}
	success = true
	if consumeResp.GetCurNum() == cfgNum {
		needTips = true
	}
	return success, needTips
}

func (s *Server) consumeExtraCount(ctx context.Context, uid, businessType uint32) (success, needTips bool) {
	consumeResp, err := s.aigcCommonClient.ConsumeSentenceCount(ctx, &aigc_common.ConsumeSentenceCountRequest{
		Uid:          uid,
		SentenceType: uint32(pb.SentenceType_SENTENCE_TYPE_EXTRA),
		BusinessType: businessType,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ConsumeSentenceCount ConsumeSentenceCount uid:%d  err: %v", uid, err)
		return false, false
	}
	// 无句数直接下发提示
	if !consumeResp.GetSuccess() {
		return false, true
	}
	// 刚好用完额外句数时下发提示
	resp, err := s.aigcCommonClient.BatGetCurSentenceCount(ctx, &aigc_common.BatGetCurSentenceCountRequest{
		Uid:                     uid,
		BusinessType:            businessType,
		NeedAvailableExtraCount: true,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ConsumeSentenceCount BatGetCurSentenceCount uid:%d err :%v", uid, err)
		return true, false
	}
	return true, resp.GetAvailableExtraNum() == 0
}

func (s *Server) GetTipsCount(ctx context.Context, req *pb.GetTipsCountRequest) (*pb.GetTipsCountResponse, error) {
	out := &pb.GetTipsCountResponse{}
	if req.GetUid() == 0 ||
		aigc_push.TipType(req.GetTipType()) == aigc_push.TipType_TIP_TYPE_UNSPECIFIED ||
		aigc_trigger.BusinessType(req.GetBusinessType()) == aigc_trigger.BusinessType_BUSINESS_TYPE_UNSPECIFIED {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	switch aigc_push.TipType(req.GetTipType()) {
	case aigc_push.TipType_TIP_TYPE_ROLE_SPECIAL:
		benefitResp, err := s.aigcIntimacyClient.GetBenefitConfig(ctx, &aigc_intimacy.GetBenefitConfigRequest{
			Entity: convertToIntimacyEntity(req.GetEntity()),
			Uid:    req.GetUid(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "ConsumeSentenceCount GetBenefitConfig req:%s err: %v", req.String(), err)
			return out, err
		}
		out.RoleSpecifiedCfgNum = benefitResp.GetBenefits().GetExtraSendMsgCount()

		usedCountRsp, err := s.aigcCommonClient.BatGetCurSentenceCount(ctx, &aigc_common.BatGetCurSentenceCountRequest{
			Uid:          req.GetUid(),
			SentenceType: []uint32{uint32(pb.SentenceType_SENTENCE_TYPE_CUR_DAY)},
			BusinessType: req.GetBusinessType(),
			RoleType:     req.GetRoleType(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetTipsCount BatGetCurSentenceCount req:%s err :%v", req.String(), err)
			return out, err
		}
		cfgNum := config.GetAigcSoulmateMiddleConfig().GetCurDayCfgCount(req.GetBusinessType(), req.GetRoleType())
		if cfgNum < usedCountRsp.GetCurNumMap()[uint32(pb.SentenceType_SENTENCE_TYPE_CUR_DAY)] {
			out.AvailableCurDayNum = 0
		} else {
			out.AvailableCurDayNum = cfgNum - usedCountRsp.GetCurNumMap()[uint32(pb.SentenceType_SENTENCE_TYPE_CUR_DAY)]
		}
	case aigc_push.TipType_TIP_TYPE_CUR_DAY:
		out.CurDayCfgNum = config.GetAigcSoulmateMiddleConfig().GetCurDayCfgCount(req.GetBusinessType(), req.GetRoleType())
		if req.GetRoleType() != uint32(aigc_soulmate.AIRoleType_AIRoleTypePet) {
			resp, err := s.aigcCommonClient.BatGetCurSentenceCount(ctx, &aigc_common.BatGetCurSentenceCountRequest{
				Uid:                     req.GetUid(),
				BusinessType:            req.GetBusinessType(),
				NeedAvailableExtraCount: true,
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "GetTipsCount BatGetCurSentenceCount req:%s err :%v", req.String(), err)
				return out, err
			}
			out.ExtraAvailableNum = resp.GetAvailableExtraNum()
		}

	default:
	}

	log.InfoWithCtx(ctx, "GetTipsCount finish req:%s resp:%s", req.String(), out.String())
	return out, nil

}
