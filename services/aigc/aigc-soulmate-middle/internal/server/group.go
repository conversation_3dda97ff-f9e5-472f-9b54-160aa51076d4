package server

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"
	web_im_logic "golang.52tt.com/protocol/app/web-im-logic"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/seqgen"
	"golang.52tt.com/pkg/audit"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	game_red_dot_logic "golang.52tt.com/protocol/app/game-red-dot-logic"
	"golang.52tt.com/protocol/common/status"
	aigc_group "golang.52tt.com/protocol/services/aigc/aigc-group"
	pb "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle"
	chat_bot "golang.52tt.com/protocol/services/chat-bot"
	game_red_dot "golang.52tt.com/protocol/services/game-red-dot"
	timelineV2Pb "golang.52tt.com/protocol/services/timeline-v2"
	"golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/report"
	"google.golang.org/grpc/codes"
)

func (s *Server) checkMsg(ctx context.Context, req *pb.SendGroupImMsgReq) error {
	if req.GetTimeLineMsg() == nil || req.GetTimeLineMsg().GetMsg() == nil {
		log.ErrorWithCtx(ctx, "checkMsg TimeLineMsg err:nil, req:%s", req.String())
		return protocol.NewExactServerError(codes.InvalidArgument, status.ErrRequestParamInvalid)
	}

	if req.GetTimeLineMsg().GetGroupSendType() == pb.GroupSendType_GroupSendTypeAI2User {
		/*if len(req.GetTimeLineMsg().GetRoleIds()) == 0 {  特殊消息不处理
			log.ErrorWithCtx(ctx, "checkMsg roleIds err:nil, req:%s", req.String())
			return protocol.NewExactServerError(codes.InvalidArgument, status.ErrRequestParamInvalid)
		}*/
	}
	if req.GetTimeLineMsg().GetGroupId() == 0 || req.GetTimeLineMsg().GetGroupTemplateId() == 0 {
		log.ErrorWithCtx(ctx, "checkMsg groupId or groupTemplateId err:0, req:%s", req.String())
		return protocol.NewExactServerError(codes.InvalidArgument, status.ErrRequestParamInvalid)
	}

	groupReq := &aigc_group.GetGroupMemberListRequest{
		GroupId: req.GetTimeLineMsg().GetGroupId(),
	}

	groupMembemResp, err := s.groupClient.GetGroupMemberList(ctx, groupReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkMsg GetGroupMember err:%v, req:%s", err, req.String())
		return err
	}
	roleMap := make(map[uint32]*aigc_group.GroupMember, len(groupMembemResp.GetList()))
	uidMap := make(map[uint32]*aigc_group.GroupMember, len(groupMembemResp.GetList()))
	for _, info := range groupMembemResp.GetList() {
		if info.GetType() == aigc_group.GroupMemberType_GROUP_MEMBER_TYPE_ROLE {
			roleMap[info.GetId()] = info
		} else if info.GetType() == aigc_group.GroupMemberType_GROUP_MEMBER_TYPE_USER {
			uidMap[info.GetId()] = info
		}
	}
	/*if groupMembemResp.GetList() == nil {
		log.ErrorWithCtx(ctx, "checkMsg GetGroupMember err:not exist, req:%s", err, req.String())
		return protocol.NewExactServerError(codes.InvalidArgument, status.ErrGroupNotExist)
	}*/
	if req.GetTimeLineMsg().GetGroupSendType() == pb.GroupSendType_GroupSendTypeUser2AI {
		if req.GetTimeLineMsg().GetUid() == 0 {
			log.ErrorWithCtx(ctx, "user2ai checkMsg err: uid:0, req:%s", req.String())
			return protocol.NewExactServerError(codes.InvalidArgument, status.ErrRequestParamInvalid)
		}
		for _, roleId := range req.GetTimeLineMsg().GetRoleIds() {
			if _, ok := roleMap[roleId]; !ok {
				log.ErrorWithCtx(ctx, "user2ai checkMsg err: role:%d not exist, req:%s", roleId, req.String())
				//return protocol.NewExactServerError(codes.InvalidArgument, status.ErrGroupMemberNotExist)
			}
		}
		if _, ok := uidMap[req.GetTimeLineMsg().GetUid()]; !ok {
			log.ErrorWithCtx(ctx, "user2ai checkMsg err: uid:%d not exist, req:%s", req.GetTimeLineMsg().GetUid(), req.String())
			return protocol.NewExactServerError(codes.InvalidArgument, status.ErrGroupMemberNotExist)
		}
	} else if req.GetTimeLineMsg().GetGroupSendType() == pb.GroupSendType_GroupSendTypeAI2User {
		for _, roleId := range req.GetTimeLineMsg().GetRoleIds() {
			if _, ok := roleMap[roleId]; !ok {
				log.ErrorWithCtx(ctx, "ai2user checkMsg err: role:%d not exist, req:%s", roleId, req.String())
				return protocol.NewExactServerError(codes.InvalidArgument, status.ErrGroupMemberNotExist)
			}
		}
		if req.GetTimeLineMsg().GetUid() != 0 {
			if _, ok := uidMap[req.GetTimeLineMsg().GetUid()]; !ok {
				log.ErrorWithCtx(ctx, "ai2user checkMsg err: uid:%d not exist, req:%s", req.GetTimeLineMsg().GetUid(), req.String())
				return protocol.NewExactServerError(codes.InvalidArgument, status.ErrGroupMemberNotExist)
			}
		}
	}
	if req.GetTimeLineMsg().GetMsg().GetSentAt() == 0 {
		req.GetTimeLineMsg().GetMsg().SentAt = time.Now().UnixMilli()
	}
	return nil
}

func (s *Server) generateDefaultSeqId(ctx context.Context, id uint32) (uint32, error) {
	seq, err := s.SeqgenV2Client.GenerateSequence(ctx, id, seqgen.NamespaceAiGroup, seqgen.KeyIm, 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "generateSeqId seqgen-v2.GenerateSequence failed, id:%d", id, err)
		return 0, err
	}
	return uint32(seq), nil
}

func (s *Server) writeMessage(ctx context.Context, namespace string, timelineId uint32, msg *pb.GroupTimeLineMsg) error {
	msgBin, err := proto.Marshal(msg)
	if err != nil {
		log.ErrorWithCtx(ctx, "writeMessage Marshal err:%v, namespace:%s, id:%d, msg:%+v", err, namespace, timelineId, msg)
		return protocol.NewExactServerError(codes.InvalidArgument, status.ErrRequestParamInvalid)
	}

	timelineReq := &timelineV2Pb.WriteTimelineMsgReq{
		Id:     timelineId,
		Suffix: namespace,
		Msg: &timelineV2Pb.TimelineMsg{
			Type:   uint32(timelineV2Pb.TimelineMsg_AI_GROUP),
			Seqid:  msg.GetMsg().GetSeqId(),
			MsgBin: msgBin,
		},
	}
	_, err = s.TimelineV2Client.WriteTimelineMsg(ctx, timelineReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "WriteTimelineMsg failed, err:%v, namespace:%s, id:%d, msg:%+v", err, namespace, timelineId, msg)
		return err
	}

	return nil
}

func (s *Server) addGroupUserRedDot(ctx context.Context, groupId uint32, needAddDotMembers []uint32) error {
	if len(needAddDotMembers) == 0 {
		return nil
	}
	// 遍历群聊用户列表，除发送者外的用户增加红点
	addRedDotParams := make([]*game_red_dot.BatchAddRedDotReq_AddRedDotParam, 0, len(needAddDotMembers))
	for _, member := range needAddDotMembers {

		addRedDotParams = append(addRedDotParams, &game_red_dot.BatchAddRedDotReq_AddRedDotParam{
			BizKey: fmt.Sprintf("%d_%d", member, groupId),
		})

	}
	if len(addRedDotParams) > 0 {
		_, err := s.redDotClient.BatchAddRedDot(ctx, &game_red_dot.BatchAddRedDotReq{
			BizType: uint32(game_red_dot_logic.RedDotBizType_RED_DOT_BIZ_TYPE_AIGC_GROUP),
			Params:  addRedDotParams,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "addGroupUserRedDot redDotClient.BatchAddRedDot err, err:%v", err)
			return err
		}
	}
	return nil
}

func (s *Server) SendGroupImMsg(ctx context.Context, req *pb.SendGroupImMsgReq) (*pb.SendGroupImMsgResp, error) {
	out := &pb.SendGroupImMsgResp{}

	err := s.checkMsg(ctx, req)
	if err != nil {
		//log.ErrorWithCtx(ctx, "SendGroupImMsg checkMsg err:%v, req:%+v", err, req)
		return out, err
	}

	var auditId uint32
	var belongId string
	var uid uint32
	var roleId uint32
	if req.GetTimeLineMsg().GetGroupSendType() == pb.GroupSendType_GroupSendTypeUser2AI {
		user, AccountErr := s.AccountGo.GetUserByUid(ctx, req.GetTimeLineMsg().GetUid())
		if AccountErr != nil {
			log.ErrorWithCtx(ctx, "SendGroupImMsg GetUserByUid err:%v, uid:%d", AccountErr, req.GetTimeLineMsg().GetUid())
			return out, AccountErr
		}
		auditId = user.GetUid()
		belongId = user.GetAlias()
		uid = user.GetUid()
	} else {
		auditId = req.GetTimeLineMsg().GetGroupId()
		belongId = strconv.Itoa(int(req.GetTimeLineMsg().GetGroupId()))
		if len(req.GetTimeLineMsg().GetRoleIds()) > 0 {
			roleId = req.GetTimeLineMsg().GetRoleIds()[0]
		}
	}

	var (
		needScan = req.GetOpt().GetWithAudit()
		scanText = req.GetOpt().GetAuditText()
	)
	switch req.GetTimeLineMsg().GetMsg().GetType() {
	case pb.ImMsgType_ImMsgTypeText:
		needScan = true
		scanText = req.GetTimeLineMsg().GetMsg().GetContent()
	case pb.ImMsgType_ImMsgTypeEmoticon, pb.ImMsgType_ImMsgAIPartner:
	default:
		log.WarnWithCtx(ctx, "SendGroupImMsg msg type err, type:%d, req:%s", req.GetTimeLineMsg().GetMsg().GetType(), req.String())
		return out, protocol.NewExactServerError(nil, status.ErrChatBotInvalidMsgType)
	}

	if needScan {
		if scanText == "" {
			log.ErrorWithCtx(ctx, "SendGroupImMsg scanText err:nil, req:%s", req.String())
		} else {
			result, err := s.AuditInst.SyncScanText(ctx, auditId, audit.SCENE_CODE_AI_PARTNER_MSG, scanText, belongId)
			if err != nil {
				log.ErrorWithCtx(ctx, "SyncScanText uid(%d) text(%s) err: %v, req:%s", auditId, req.GetTimeLineMsg().GetMsg().GetContent(), err, req.String())
				return out, err
			}
			if result != chat_bot.AuditResult_AuditResultPass {
				log.WarnWithCtx(ctx, "beforeSend SyncScanText user(%d) req:%s msg(%s) rejected", auditId, req.String(), req.GetTimeLineMsg().GetMsg().GetContent())
				return out, protocol.NewExactServerError(nil, status.ErrChatBotInvalidMsgContent)
			}
		}
	}
	seqId, err := s.generateDefaultSeqId(ctx, req.GetTimeLineMsg().GetGroupId())
	if err != nil {
		log.ErrorWithCtx(ctx, "SendGroupImMsg generateDefaultSeqId err:%v, in:%+v", err, req.String())
		return out, err
	}
	req.GetTimeLineMsg().GetMsg().SeqId = seqId
	out.SentAt = req.GetTimeLineMsg().GetMsg().GetSentAt()
	out.SeqId = seqId
	var specialMsgType web_im_logic.SpecialMsgType
	var specialToUid uint32
	if req.GetOpt().GetWithSpecialStorage() {
		specialMsgType, specialToUid, err = s.writeSpecialMsg(ctx, req.GetTimeLineMsg())
		if err != nil {
			log.ErrorWithCtx(ctx, "writeSpecialMsg err:%v, req:%s", err, req.String())
			return out, err
		}
	} else {
		err = s.writeMessage(ctx, seqgen.NamespaceAiGroup, req.GetTimeLineMsg().GetGroupId(), req.GetTimeLineMsg())
		if err != nil {
			log.ErrorWithCtx(ctx, "writeMessage err:%v, req:%s", err, req.String())
			return out, err
		}
		_, err = s.groupClient.UpdateGroupLatestSeqId(ctx, &aigc_group.UpdateGroupLatestSeqIdRequest{
			GroupId: req.GetTimeLineMsg().GetGroupId(),
			SeqId:   seqId,
		})
		if err != nil {
			log.WarnWithCtx(ctx, "UpdateGroupLatestSeqId err:%v, req:%s", err, req.String())
		}
	}
	var needRedDotMembers []uint32
	senderUid := req.GetTimeLineMsg().GetUid()
	if req.GetTimeLineMsg().GetGroupSendType() == pb.GroupSendType_GroupSendTypeAI2User {
		senderUid = 0
	}
	switch specialMsgType {
	case web_im_logic.SpecialMsgType_SPECIAL_MSG_TYPE_SPECIFIED_USER_VISIBLE:
		needRedDotMembers = append(needRedDotMembers, specialToUid)
	default:
		// 获取群聊用户列表
		groupMemberResp, err := s.groupClient.GetGroupMemberList(ctx, &aigc_group.GetGroupMemberListRequest{
			GroupId: req.GetTimeLineMsg().GetGroupId(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "addGroupUserRedDot groupClient.GetGroupMemberList err, err:%v", err)
			return out, err
		}
		for _, member := range groupMemberResp.GetList() {
			if member.GetType() == aigc_group.GroupMemberType_GROUP_MEMBER_TYPE_USER && member.GetId() != senderUid {
				needRedDotMembers = append(needRedDotMembers, member.GetId())
			}
		}
	}
	_ = s.addGroupUserRedDot(ctx, req.GetTimeLineMsg().GetGroupId(), needRedDotMembers)
	var templateType aigc_group.GroupType
	templateRsp, err := s.groupClient.GetGroupTemplateByIds(ctx, &aigc_group.GetGroupTemplateByIdsRequest{
		Ids: []uint32{req.GetTimeLineMsg().GetGroupTemplateId()},
	})
	if err != nil || len(templateRsp.GetList()) == 0 {
		log.ErrorWithCtx(ctx, "SendGroupImMsg GetGroupTemplateByIds err:%v, req:%s", err, req.String())
	} else {
		templateType = templateRsp.GetList()[0].GetGroupType()
	}

	reportData := report.GroupMsgData{
		Uid:               uid,
		RoleId:            roleId,
		BizType:           pb.ImBusiType(req.GetTimeLineMsg().GetMsg().GetImBusiType()),
		MsgType:           pb.ImMsgContentType(req.GetTimeLineMsg().GetMsg().GetContentType()),
		Content:           req.GetTimeLineMsg().GetMsg().GetContent(),
		SenderType:        req.GetTimeLineMsg().GetGroupSendType(),
		IsTrigger:         req.GetIsTrigger(),
		GroupId:           req.GetTimeLineMsg().GetGroupId(),
		GroupTemplateId:   req.GetTimeLineMsg().GetGroupTemplateId(),
		CmdType:           pb.ImCmdType(req.GetTimeLineMsg().GetMsg().GetImCmdType()),
		IsUseUp:           req.GetIsReachLimit(),
		TransparentExtMsg: req.GetTimeLineMsg().GetMsg().GetTransparentExtMsg(),
		GroupTemplateType: templateType,
	}
	s.reporter.ReportGroupMsg(ctx, reportData)

	log.InfoWithCtx(ctx, "SendGroupImMsg in:%s, out:%s", req.String(), out.String())
	return out, nil
}

func (s *Server) writeSpecialMsg(ctx context.Context, msg *pb.GroupTimeLineMsg) (web_im_logic.SpecialMsgType, uint32, error) {
	msgBin, err := proto.Marshal(msg)
	if err != nil {
		log.ErrorWithCtx(ctx, "writeSpecialMsg Marshal err:%v,  msg:%s", err, msg.String())
		return 0, 0, protocol.NewExactServerError(codes.InvalidArgument, status.ErrRequestParamInvalid)
	}
	if len(msg.GetMsg().GetTransparentExtMsg()) == 0 {
		log.ErrorWithCtx(ctx, "writeSpecialMsg TransparentExtMsg err:nil, msg:%s", msg.String())
		return 0, 0, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	transparentExtMsg := &web_im_logic.TransparentMsgExtInfo{}
	extByte, err := base64.StdEncoding.DecodeString(msg.GetMsg().GetTransparentExtMsg())
	if err != nil {
		log.ErrorWithCtx(ctx, "writeSpecialMsg DecodeString TransparentExtMsg err:%v, msg:%s", err, msg.String())
		return 0, 0, err
	}
	err = json.Unmarshal(extByte, transparentExtMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "writeSpecialMsg Unmarshal TransparentExtMsg err:%v, msg:%s", err, msg.String())
		return 0, 0, err
	}
	_, err = s.groupClient.AddSpecialMsg(ctx, &aigc_group.AddSpecialMsgRequest{
		GroupId: msg.GetGroupId(),
		Uid:     transparentExtMsg.GetUid(),
		SeqId:   msg.GetMsg().GetSeqId(),
		Msg:     msgBin,
		MsgType: transparentExtMsg.GetSpecialMsgType(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "writeSpecialMsg AddSpecialMsg err:%v, msg:%s", err, msg.String())
		return 0, 0, err
	}
	return web_im_logic.SpecialMsgType(transparentExtMsg.GetSpecialMsgType()), transparentExtMsg.GetUid(), nil

}
func (s *Server) GetGroupMsgList(ctx context.Context, req *pb.GetGroupMsgListReq) (*pb.GetGroupMsgListResp, error) {
	out := &pb.GetGroupMsgListResp{}

	if req.GetGroupId() == 0 {
		log.ErrorWithCtx(ctx, "GetGroupMsgList groupid err:0, req:%s", req.String())
		return out, protocol.NewExactServerError(codes.InvalidArgument, status.ErrRequestParamInvalid)
	}
	const maxLimit = 200 // timline-v2 2000
	limit := req.GetLimit()
	if limit == 0 {
		limit = 50
	} else {
		if limit > maxLimit {
			limit = maxLimit
		}
	}

	getSeq := req.GetSeqId()
	/*if getSeq != 0 {
		getSeq++
	}*/

	pullMsg := &timelineV2Pb.PullTimelineMsgReq{
		Id:         req.GetGroupId(),
		Suffix:     seqgen.NamespaceAiGroup,
		StartSeqid: getSeq,
		Limit:      limit,
		Reverse:    true,
	}
	reverseMsgResp, err := s.TimelineV2Client.PullTimelineMsg(ctx, pullMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMessages PullTimelineMsgReverse failed, err:%v, req:%+v", err, req)
		return out, err
	}
	uid := req.GetUid()
	if uid == 0 {
		uid = metainfo.GetServiceInfo(ctx).UserID()
	}
	var specialTimeLineMsgList []*pb.GroupTimeLineMsg
	if uid > 0 {
		specialMsgRsp, err := s.groupClient.PullSpecialMsg(ctx, &aigc_group.PullSpecialMsgRequest{
			GroupId:      req.GetGroupId(),
			Uid:          uid,
			StartSeq:     getSeq,
			BusinessType: uint32(web_im_logic.SpecialMsgType_SPECIAL_MSG_TYPE_SPECIFIED_USER_VISIBLE),
			Limit:        limit,
			Reverse:      true,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMessages BatSearchMsg failed, err:%v, req:%+v", err, req)
			return out, err
		}
		specialMsgList := specialMsgRsp.GetMsg()
		specialTimeLineMsgList = make([]*pb.GroupTimeLineMsg, 0, len(specialMsgList))
		if len(specialMsgList) > 0 {
			for _, msgBin := range specialMsgList {
				msg := &pb.GroupTimeLineMsg{}
				err = proto.Unmarshal(msgBin, msg)
				if err != nil {
					log.ErrorWithCtx(ctx, "GetMessages Unmarshal err:%v, msgBin:%+v", err, msgBin)
					continue
				}
				specialTimeLineMsgList = append(specialTimeLineMsgList, msg)
			}
		}
	}

	var count uint32
	timelineMsgList := make([]*pb.GroupTimeLineMsg, 0, len(reverseMsgResp.GetMsgList()))
	for _, timelineMsg := range reverseMsgResp.GetMsgList() {
		msg := &pb.GroupTimeLineMsg{}
		err = proto.Unmarshal(timelineMsg.GetMsgBin(), msg)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMessages Unmarshal err:%v, timelineMsg:%+v", err, timelineMsg)
			continue
		}
		timelineMsgList = append(timelineMsgList, msg)
		if count == limit {
			break
		}

		count++
		//out.TimeLineMsgs = append(out.TimeLineMsgs, msg)
	}
	out.TimeLineMsgs = mergeMsgList(specialTimeLineMsgList, timelineMsgList, limit)
	log.InfoWithCtx(ctx, "GetGroupMsgList req:%+v, len(resp):%+v", req, out)

	return out, nil
}

func mergeMsgList(specialMsgList []*pb.GroupTimeLineMsg, timelineMsgList []*pb.GroupTimeLineMsg, limit uint32) []*pb.GroupTimeLineMsg {
	if len(specialMsgList) == 0 {
		return timelineMsgList
	}
	if len(timelineMsgList) == 0 {
		return specialMsgList
	}

	// 合并特殊消息和普通消息,按seqId降序排列
	result := make([]*pb.GroupTimeLineMsg, 0, len(specialMsgList)+len(timelineMsgList))
	specialIndex, timelineIndex := 0, 0
	var count uint32
	for specialIndex < len(specialMsgList) && timelineIndex < len(timelineMsgList) && count < limit {
		if specialMsgList[specialIndex].GetMsg().GetSeqId() > timelineMsgList[timelineIndex].GetMsg().GetSeqId() {
			result = append(result, specialMsgList[specialIndex])
			specialIndex++
		} else if specialMsgList[specialIndex].GetMsg().GetSeqId() < timelineMsgList[timelineIndex].GetMsg().GetSeqId() {
			result = append(result, timelineMsgList[timelineIndex])
			timelineIndex++
		} else {
			//兜底，一般不会出现seqId相同，保留timeline消息
			result = append(result, timelineMsgList[timelineIndex])
			specialIndex++
			timelineIndex++
		}
		count++
	}
	for count < limit && specialIndex < len(specialMsgList) {
		result = append(result, specialMsgList[specialIndex])
		specialIndex++
	}
	for count < limit && timelineIndex < len(timelineMsgList) {
		result = append(result, timelineMsgList[timelineIndex])
		timelineIndex++
	}

	return result
}

func (s *Server) batchGetGroupLatestSeq(ctx context.Context, groupIdList []uint32) (map[uint32]uint32, error) {

	//seqMap, err := s.SeqgenV2Client.BatchRetrieveSequence(ctx, groupIdList, seqgen.NamespaceAiGroup, seqgen.KeyIm)
	//if err != nil {
	//	log.ErrorWithCtx(ctx, "SyncLogicMgr.batchGetGroupInfoLatestSeq BatchRetrieveSequence failed,groupIdList is %v,err is %v",
	//		groupIdList, err)
	//	return nil, err
	//}
	//
	//finalMap := make(map[uint32]uint32, len(seqMap))
	//for key, value := range seqMap {
	//	if value <= math.MaxUint32 {
	//		finalMap[key] = uint32(value)
	//	}
	//}
	//
	rsp, err := s.groupClient.BatGetGroupLatestSeqId(ctx, &aigc_group.BatGetGroupLatestSeqIdRequest{
		GroupIds: groupIdList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "batchGetGroupLatestSeq BatGetGroupLatestSeqId failed, groupIdList is %v, err is %v", groupIdList, err)
		return nil, err
	}
	return rsp.GetGroupSeqIdMap(), nil
}

func (s *Server) BatchGetGroupLastMsgList(ctx context.Context, req *pb.BatchGetGroupLastMsgListReq) (*pb.BatchGetGroupLastMsgListResp, error) {
	out := &pb.BatchGetGroupLastMsgListResp{}
	if len(req.GetGroupIds()) == 0 {
		return out, nil
	}
	seqMap, err := s.batchGetGroupLatestSeq(ctx, req.GetGroupIds())
	if err != nil {
		return out, err
	}
	log.DebugWithCtx(ctx, "batchGetGroupLatestSeq seqMap:%+v", seqMap)

	uid := metainfo.GetServiceInfo(ctx).UserID()
	specialRsp, err := s.groupClient.BatGetLastSpecialMsg(ctx, &aigc_group.BatGetLastSpecialMsgRequest{
		GroupIds:     req.GetGroupIds(),
		Uid:          uid,
		BusinessType: uint32(web_im_logic.SpecialMsgType_SPECIAL_MSG_TYPE_SPECIFIED_USER_VISIBLE),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMessagesBySeqId BatGetLastSpecialMsg failed, err:%v, req:%+v", err, req)
		return out, err
	}
	specialList := make([]*pb.GroupLastMsg, 0, len(specialRsp.GetGroupMsgMap()))
	for groupId, msgBytes := range specialRsp.GetGroupMsgMap() {
		timelineMsg := &pb.GroupTimeLineMsg{}
		err = proto.Unmarshal(msgBytes, timelineMsg)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMessagesBySeqId Unmarshal failed, err:%v, groupId:%d, msgBytes:%+v", err, groupId, msgBytes)
			continue
		}
		seqId, ok := seqMap[groupId]
		if !ok || seqId <= timelineMsg.GetMsg().GetSeqId() {
			specialList = append(specialList, &pb.GroupLastMsg{
				GroupId:     groupId,
				TimeLineMsg: timelineMsg,
			})
			delete(seqMap, groupId) // 删除已处理的groupId
		}
	}
	if len(specialList) > 0 {
		out.GroupMsgs = append(out.GroupMsgs, specialList...)
		log.DebugWithCtx(ctx, "BatchGetGroupLastMsgList specialList:%+v", specialList)
	}
	getReqList := make([]*timelineV2Pb.GetTimelineMsgReq, 0, len(req.GetGroupIds()))
	for groupId, seqId := range seqMap {
		getReqList = append(getReqList, &timelineV2Pb.GetTimelineMsgReq{
			Id:     strconv.FormatUint(uint64(groupId), 10),
			Suffix: seqgen.NamespaceAiGroup,
			SeqId:  uint64(seqId),
		})
	}
	timelineReq := &timelineV2Pb.BatchGetTimelineMsgReq{
		ReqList: getReqList,
	}

	timelineResp, err := s.TimelineV2Client.BatchGetTimelineMsg(ctx, timelineReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMessagesBySeqId BatchGetTimelineMsg failed, err:%v, req:%+v", err, req)
		return out, err
	}
	log.DebugWithCtx(ctx, "BatchGetTimelineMsgReq:%s, resp:%s", timelineReq.String(), timelineResp.String())

	for _, timelineMsg := range timelineResp.GetMsgList() {
		msg := &pb.GroupTimeLineMsg{}
		err := proto.Unmarshal(timelineMsg.GetMsgBin(), msg)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMessagesBySeqId Unmarshal failed, err:%v, timelineMsg:%+v", err, timelineMsg)
			continue
		}
		out.GroupMsgs = append(out.GroupMsgs, &pb.GroupLastMsg{
			GroupId:     msg.GetGroupId(),
			TimeLineMsg: msg,
		})
	}

	log.InfoWithCtx(ctx, "BatchGetGroupLastMsgList req:%s, resp:%s", req.String(), out.String())

	return out, nil
}
