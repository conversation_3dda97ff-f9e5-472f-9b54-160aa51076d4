package server

import (
	"context"
	"strconv"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	game_red_dot_logic "golang.52tt.com/protocol/app/game-red-dot-logic"
	"golang.52tt.com/protocol/common/status"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	pb "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle"
	game_red_dot "golang.52tt.com/protocol/services/game-red-dot"
)

func (s *Server) BatchAllocRoleUser(ctx context.Context, req *pb.BatchAllocRoleUserRequest) (*pb.BatchAllocRoleUserResponse, error) {
	resp := &pb.BatchAllocRoleUserResponse{}
	log.InfoWithCtx(ctx, "BatchAllocRoleUser req: %+v", req)

	if req.GetRoleId() == 0 || len(req.GetUidList()) == 0 {
		log.WarnWithCtx(ctx, "BatchAllocRoleUser invalid req: %+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "miss roleId or uidList")
	}

	getRoleReq := &aigc_soulmate.GetAIRoleReq{
		Id: req.GetRoleId(),
	}
	getRoleResp, err := s.soulmateClient.GetAIRole(ctx, getRoleReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchAllocRoleUser GetAIRole req(%+v) err: %v", getRoleReq, err)
		return resp, err
	}

	// 校验角色
	role := getRoleResp.GetRole()
	if role == nil {
		log.WarnWithCtx(ctx, "BatchAllocRoleUser role %d not found", req.GetRoleId())
		return resp, protocol.NewExactServerError(nil, status.ErrChatBotAiRoleNotFound)
	}
	if role.GetScope() != aigc_soulmate.AIRoleScope_AI_ROLE_SCOPE_USER_EXCLUSIVE {
		log.WarnWithCtx(ctx, "BatchAllocRoleUser invalid role: %+v", role)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "该角色不支持新增专属用户")
	}

	getUserReq := &aigc_soulmate.BatchGetRoleUserRequest{
		RoleId:  req.GetRoleId(),
		UidList: req.GetUidList(),
	}
	getUserResp, err := s.soulmateClient.BatchGetRoleUser(ctx, getUserReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchAllocRoleUser BatchGetRoleUser req(%+v) err: %v", getUserReq, err)
		return resp, err
	}

	uidMap := getUserResp.GetExists()
	if uidMap == nil {
		uidMap = make(map[uint32]bool)
	}

	// 过滤已添加的uid
	uidList := make([]uint32, 0, len(req.GetUidList()))
	for _, uid := range req.GetUidList() {
		if !uidMap[uid] {
			uidList = append(uidList, uid)
			uidMap[uid] = true
		}
	}
	if len(uidList) == 0 {
		log.WarnWithCtx(ctx, "BatchAllocRoleUser uidList empty")
		return resp, nil
	}

	// 批量添加角色用户
	addUserReq := &aigc_soulmate.BatchAddRoleUserRequest{
		RoleId:  req.GetRoleId(),
		UidList: uidList,
	}
	if _, err := s.soulmateClient.BatchAddRoleUser(ctx, addUserReq); err != nil {
		log.ErrorWithCtx(ctx, "BatchAllocRoleUser BatchAddRoleUser req(%+v) err: %v", addUserReq, err)
		return resp, err
	}

	// 批量给用户加红点
	addRedDotReq := &game_red_dot.BatchAddRedDotReq{
		BizType: uint32(game_red_dot_logic.RedDotBizType_RED_DOT_BIZ_TYPE_AIGC_EXCLUSIVE_ROLE),
		Params:  make([]*game_red_dot.BatchAddRedDotReq_AddRedDotParam, 0, len(req.GetUidList())),
	}
	for _, uid := range req.GetUidList() {
		addRedDotReq.Params = append(addRedDotReq.Params, &game_red_dot.BatchAddRedDotReq_AddRedDotParam{
			BizKey:         strconv.FormatUint(uint64(uid), 10),
			PushTargetType: game_red_dot.PushTargetType_PUSH_TARGET_TYPE_H5,
			PushExtraParam: &game_red_dot.PushExtraParam{
				Uid: uid,
			},
		})
	}
	if _, err := s.redDotClient.BatchAddRedDot(ctx, addRedDotReq); err != nil {
		log.ErrorWithCtx(ctx, "BatchAllocRoleUser BatchAddRedDot req(%+v) err: %v", addRedDotReq, err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "BatchAllocRoleUser uidList: %+v", uidList)
	return resp, nil
}

func (s *Server) BatchDeallocRoleUser(ctx context.Context, req *pb.BatchDeallocRoleUserRequest) (*pb.BatchDeallocRoleUserResponse, error) {
	resp := &pb.BatchDeallocRoleUserResponse{}
	log.InfoWithCtx(ctx, "BatchDeallocRoleUser req: %+v", req)

	if req.GetRoleId() == 0 || len(req.GetUidList()) == 0 {
		log.WarnWithCtx(ctx, "BatchDeallocRoleUser invalid req: %+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "miss roleId or uidList")
	}

	delUserReq := &aigc_soulmate.BatchDelRoleUserRequest{
		RoleId:  req.GetRoleId(),
		UidList: req.GetUidList(),
	}
	if _, err := s.soulmateClient.BatchDelRoleUser(ctx, delUserReq); err != nil {
		log.ErrorWithCtx(ctx, "BatchDeallocRoleUser req(%+v) err: %v", delUserReq, err)
		return resp, err
	}

	return resp, nil
}

func (s *Server) GetRoleAllocUserList(ctx context.Context, req *pb.GetRoleAllocUserListRequest) (*pb.GetRoleAllocUserListResponse, error) {
	resp := &pb.GetRoleAllocUserListResponse{}

	if req.GetRoleId() == 0 {
		log.WarnWithCtx(ctx, "GetRoleAllocUserList invalid req: %+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "miss roleId")
	}

	uidListReq := &aigc_soulmate.GetRoleUidListRequest{
		RoleId: req.GetRoleId(),

		Cursor: req.GetCursor(),
		Limit:  req.GetLimit(),
	}
	uidListResp, err := s.soulmateClient.GetRoleUidList(ctx, uidListReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRoleAllocUserLIst GetRoleUidList req(%+v) err: %v", uidListReq, err)
		return resp, err
	}

	if len(uidListResp.GetList()) > 0 {
		userMap, err := s.AccountGo.GetUsersMap(ctx, uidListResp.GetList())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetRoleAllocUserList GetUsersMap uidList(%+v) err: %v", uidListResp.GetList())
			return resp, err
		}
		for _, uid := range uidListResp.GetList() {
			user := userMap[uid]
			if user == nil {
				log.WarnWithCtx(ctx, "GetRoleAllocUserList user %d not found", uid)
				continue
			}

			resp.List = append(resp.List, &pb.User{
				Uid:  user.GetUid(),
				Ttid: user.GetAlias(),

				Nickname: user.GetNickname(),
			})
		}
	}

	resp.HasMore = uidListResp.GetHasMore()
	resp.NextCursor = uidListResp.GetNextCursor()
	return resp, nil
}
