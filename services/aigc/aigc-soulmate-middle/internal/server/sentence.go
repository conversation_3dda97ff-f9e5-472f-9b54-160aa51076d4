package server

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	aigc_common "golang.52tt.com/protocol/services/aigc/aigc-common"
	aigc_intimacy "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	aigc_soulmate_middle "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle"
	config "golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/config/ttconfig/aigc_soulmate_middle"
)

func (s *Server) GetSentenceAvailableCount(ctx context.Context, req *aigc_soulmate_middle.GetSentenceAvailableCountReq) (
	*aigc_soulmate_middle.GetSentenceAvailableCountResp, error) {
	out := &aigc_soulmate_middle.GetSentenceAvailableCountResp{}
	if len(req.GetType()) == 0 ||
		req.GetUid() == 0 ||
		req.GetEntity().GetId() == 0 ||
		req.GetEntity().GetType() == aigc_soulmate_middle.Entity_TYPE_UNSPECIFIED {
		log.WarnWithCtx(ctx, "GetSentenceAvailableCount invalid req: %s", req.String())
		return out, nil
	}
	// 开关打开切不在白名单中，不可用专属句数
	if config.GetAigcSoulmateMiddleConfig().GetSpecifiedSentenceSwitch() &&
		!config.GetAigcSoulmateMiddleConfig().IsInWhiteList(req.GetUid()) {
		log.WarnWithCtx(ctx, "GetSentenceAvailableCount switch open but not hit white req: %s", req.String())
		return out, nil
		
	}
	
	resp, err := s.aigcCommonClient.GetSentenceCountMap(ctx, genSentenceCountReq(req))
	if err != nil {
		log.ErrorWithCtx(ctx, "IsSentenceAvailable GetSentenceCountMap req:%s err: %v", req.String(), err)
		return out, err
	}
	
	benefitResp, err := s.aigcIntimacyClient.GetBenefitConfig(ctx, &aigc_intimacy.GetBenefitConfigRequest{
		Entity: convertToIntimacyEntity(req.GetEntity()),
		Uid:    req.GetUid(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "IsSentenceAvailable GetBenefitConfig req:%s err: %v", req.String(), err)
		return out, err
	}
	configNum := benefitResp.GetBenefits().GetExtraSendMsgCount()
	curCountMap := resp.GetCountMap()
	out.AvailableCountMap = make(map[uint32]*aigc_soulmate_middle.GetSentenceAvailableCountResp_SentenceCount, len(req.GetType()))
	for _, t := range req.GetType() {
		sentenceCount := &aigc_soulmate_middle.GetSentenceAvailableCountResp_SentenceCount{
			ConfigNum: configNum,
		}
		if curCountMap[uint32(t)] >= configNum {
			sentenceCount.AvailableNum = 0
		} else {
			sentenceCount.AvailableNum = configNum - curCountMap[uint32(t)]
		}
		out.AvailableCountMap[uint32(t)] = sentenceCount
	}
	
	log.InfoWithCtx(ctx, "IsSentenceAvailable success req: %s out:%s", req.String(), out.String())
	return out, nil
}

func genSentenceCountReq(req *aigc_soulmate_middle.GetSentenceAvailableCountReq) *aigc_common.GetSentenceCountMapRequest {
	types := make([]uint32, 0, len(req.GetType()))
	for _, t := range req.GetType() {
		types = append(types, uint32(t))
	}
	return &aigc_common.GetSentenceCountMapRequest{
		Uid:    req.GetUid(),
		Type:   types,
		Entity: convertToCommonEntity(req.GetEntity()),
	}
}

func convertToCommonEntity(entityInst *aigc_soulmate_middle.Entity) *aigc_common.Entity {
	
	return &aigc_common.Entity{
		Id:   entityInst.GetId(),
		Type: convertToCommonEntityType(entityInst.GetType()),
	}
}

func convertToCommonEntityType(inType aigc_soulmate_middle.Entity_Type) aigc_common.Entity_Type {
	var t aigc_common.Entity_Type
	switch inType {
	case aigc_soulmate_middle.Entity_TYPE_PARTNER:
		t = aigc_common.Entity_TYPE_PARTNER
	default:
		t = aigc_common.Entity_TYPE_UNSPECIFIED
	}
	return t
}

func convertToIntimacyEntity(entityInst *aigc_soulmate_middle.Entity) *aigc_intimacy.Entity {
	var t aigc_intimacy.Entity_Type
	switch entityInst.GetType() {
	case aigc_soulmate_middle.Entity_TYPE_PARTNER:
		t = aigc_intimacy.Entity_TYPE_PARTNER
	default:
		t = aigc_intimacy.Entity_TYPE_UNSPECIFIED
	}
	return &aigc_intimacy.Entity{
		Id:   entityInst.GetId(),
		Type: t,
	}
}

func (s *Server) GetUserSentenceCount(ctx context.Context, req *aigc_soulmate_middle.GetUserSentenceCountReq) (*aigc_soulmate_middle.GetUserSentenceCountResp, error) {
	out := &aigc_soulmate_middle.GetUserSentenceCountResp{}
	
	resp, err := s.aigcCommonClient.GetUserSentenceCount(ctx, &aigc_common.GetUserSentenceCountReq{
		Uid:          req.GetUid(),
		SentenceType: uint32(req.GetType()),
		EntityType:   convertToCommonEntityType(req.GetEntityType()),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserSentenceCount GetUserSentenceCount req:%s err: %v", req.String(), err)
		return out, err
	}
	out.Count = resp.GetCount()
	log.InfoWithCtx(ctx, "GetUserSentenceCount success req: %s out:%s", req.String(), out.String())
	return out, nil
}