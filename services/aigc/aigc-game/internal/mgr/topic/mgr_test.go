package topic

import (
	"context"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"

	"golang.52tt.com/services/aigc/aigc-game/internal/config"
	config_mocks "golang.52tt.com/services/aigc/aigc-game/internal/config/mocks"
	"golang.52tt.com/services/aigc/aigc-game/internal/entity"
)

func Test_manager_GetTopicList(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	bitableConfiger := config_mocks.NewMockBitableConfiger(ctrl)

	type fields struct {
		bitable config.BitableConfiger
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		mock    func()
		want    entity.TopicList
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				bitable: bitableConfiger,
			},
			args: args{
				ctx: ctx,
			},
			mock: func() {
				bitableConfiger.EXPECT().GetGameTopic().Return(
					map[uint32]*entity.Topic{
						1: {
							Id:    1,
							Name:  "name1",
							Order: 2,
						},
						2: {
							Id:    2,
							Name:  "name2",
							Order: 1,
						},
					},
				)
			},
			want: entity.TopicList{
				{
					Id:    2,
					Name:  "name2",
					Order: 1,
				},
				{
					Id:    1,
					Name:  "name1",
					Order: 2,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt.mock()
		t.Run(tt.name, func(t *testing.T) {
			m := &manager{
				bitable: tt.fields.bitable,
			}
			got, err := m.GetTopicList(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("manager.GetTopicList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("manager.GetTopicList() = %v, want %v", got, tt.want)
			}
		})
	}
}
