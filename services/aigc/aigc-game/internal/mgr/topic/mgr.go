package topic

import (
	"context"
	"sort"

	"gitlab.ttyuyin.com/tyr/x/log"

	"golang.52tt.com/services/aigc/aigc-game/internal/config"
	"golang.52tt.com/services/aigc/aigc-game/internal/entity"
	"golang.52tt.com/services/aigc/aigc-game/internal/mgr"
)

type manager struct {
	bitable config.BitableConfiger
}

func New(bitable config.BitableConfiger) mgr.TopicManager {
	return &manager{
		bitable: bitable,
	}
}

func (m *manager) GetTopicList(ctx context.Context) (entity.TopicList, error) {
	topicMap := m.bitable.GetGameTopic()
	log.InfoWithCtx(ctx, "GetAllTopic GetGameTopic: %+v", topicMap)

	list := make(entity.TopicList, 0, len(topicMap))
	for _, topic := range topicMap {
		list = append(list, topic)
	}

	sort.SliceStable(list, func(i, j int) bool {
		return list[i].Order < list[j].Order
	})

	return list, nil
}
