package mgr

import (
	"context"

	chat_bot "golang.52tt.com/protocol/services/chat-bot"
	"golang.52tt.com/services/aigc/aigc-game/internal/entity"
)

//go:generate mockgen -destination=mocks/game.go -package=mocks golang.52tt.com/services/aigc/aigc-game/internal/mgr GameManager
type GameManager interface {
	// 进入玩法
	EnterGame(ctx context.Context, game *entity.PlayGame) (string, error)
	// 发送角色玩法问候语
	SendRoleGameSystemMsg(ctx context.Context, partnerId, uid uint32, msg *chat_bot.SendingMsg) error

	// 获取玩法详情
	GetGame(ctx context.Context, id string) (*entity.Game, error)
	// 获取所有官方玩法列表
	GetOfficialGameList(ctx context.Context) (entity.GameList, error)

	// 获取角色下的热门玩法列表
	GetRoleTopGameList(ctx context.Context, roleId, topicId uint32) (entity.GameList, error)
	// 获取角色下的官方玩法列表
	GetRoleOfficialGameList(ctx context.Context, roleId uint32) (entity.GameList, error)
}

//go:generate mockgen -destination=mocks/topic.go -package=mocks golang.52tt.com/services/aigc/aigc-game/internal/mgr TopicManager
type TopicManager interface {
	// 获取所有主题列表
	GetTopicList(ctx context.Context) (entity.TopicList, error)
}
