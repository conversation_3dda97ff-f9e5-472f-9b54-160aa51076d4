package game

import (
	"context"
	"sort"
	"strconv"
	"time"

	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"gitlab.ttyuyin.com/tyr/x/log"

	pb "golang.52tt.com/protocol/services/aigc/aigc-game"
	chat_bot "golang.52tt.com/protocol/services/chat-bot"
	"golang.52tt.com/services/aigc/aigc-game/internal/config"
	"golang.52tt.com/services/aigc/aigc-game/internal/entity"
	"golang.52tt.com/services/aigc/aigc-game/internal/event"
	"golang.52tt.com/services/aigc/aigc-game/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-game/internal/store"
)

type manager struct {
	bitable  config.BitableConfiger
	ttConfig config.TTConfiger

	userGamesStore store.UserGamesStore
	playGamesStore store.PlayGamesStore

	eventBus event.Bus
}

func New(
	bitable config.BitableConfiger,
	ttConfig config.TTConfiger,

	userGamesStore store.UserGamesStore,
	playGamesStore store.PlayGamesStore,

	eventBus event.Bus,
) mgr.GameManager {
	return &manager{
		bitable:  bitable,
		ttConfig: ttConfig,

		userGamesStore: userGamesStore,
		playGamesStore: playGamesStore,

		eventBus: eventBus,
	}
}

func (m *manager) EnterGame(ctx context.Context, game *entity.PlayGame) (string, error) {
	if err := m.playGamesStore.Save(ctx, game); err != nil {
		return "", err
	}

	return uuid.New().String(), nil
}

func (m *manager) GetOfficialGameList(ctx context.Context) (entity.GameList, error) {
	gameMap := m.bitable.GetGameInfo()

	games := make(entity.GameList, 0, len(gameMap))
	for _, game := range gameMap {
		games = append(games, game)
	}

	sort.SliceStable(games, func(i, j int) bool {
		return games[i].Order < games[j].Order
	})

	return games, nil
}

func (m *manager) SendRoleGameSystemMsg(ctx context.Context, partnerId, uid uint32, msg *chat_bot.SendingMsg) error {
	return m.eventBus.Publish(
		ctx,
		event.PubEventSendSingleMsg,
		strconv.Itoa(int(partnerId)),
		&chat_bot.SingleMsgSendEvent{
			Uid:       uid,
			PartnerId: partnerId,

			SendingMsg: msg,

			Opt: &chat_bot.SendOption{},
		},
	)
}

func (m *manager) GetRoleOfficialGameList(ctx context.Context, roleId uint32) (entity.GameList, error) {
	var (
		gameMap = m.bitable.GetGameInfo()
		gameIds = m.bitable.GetRoleGameIds()[roleId]

		games = make(entity.GameList, 0, len(gameIds))
	)
	for _, gameId := range gameIds {
		game, ok := gameMap[gameId]
		if !ok {
			log.WarnWithCtx(ctx, "GetRoleOfficialGameList game(%s) not found", gameId)
			continue
		}

		game.RoleId = roleId
		games = append(games, game)
	}

	sort.SliceStable(games, func(i, j int) bool {
		return games[i].Order < games[j].Order
	})

	return games, nil
}

func (m *manager) GetRoleTopGameList(ctx context.Context, roleId, topicId uint32) (entity.GameList, error) {
	var (
		n          = int(m.ttConfig.GetTopGameN())
		topN       = 5 * n
		lastN      = 2 * n
		lastTime   = time.Now().Add(-m.ttConfig.GetTopGameRange())
		descMinLen = m.ttConfig.GetTopGameDescMinLen()

		games     = make(entity.GameList, 0, n)
		gameIdSet = make(map[string]struct{})
	)

	appendGame := func(game *entity.Game) {
		if _, ok := gameIdSet[game.ID]; !ok {
			games = append(games, game)
			gameIdSet[game.ID] = struct{}{}
		}
	}

	if topicId > 0 {
		// 获取官方玩法
		officialGames, err := m.GetRoleOfficialGameList(ctx, roleId)
		if err != nil {
			return nil, err
		}
		for _, officialGame := range officialGames {
			// 筛选出指定主题id的官方玩法
			if officialGame.TopicId == topicId {
				appendGame(officialGame)
			}
		}
		if len(games) >= n {
			return games[:n], nil
		}

		// 官方玩法不够，补充最近最多人玩过的用户玩法
		topPlayGames, err := m.playGamesStore.Top(ctx, roleId, topicId, lastTime, pb.GameSource_GAME_SOURCE_USER, uint32(topN))
		if err != nil {
			return nil, err
		}
		if len(topPlayGames) > 0 {
			var userGameIds []string
			for _, topPlayGame := range topPlayGames {
				userGameIds = append(userGameIds, topPlayGame.GameId)
			}

			userGameMap, err := m.userGamesStore.BatchGet(ctx, userGameIds)
			if err != nil {
				return nil, err
			}

			for _, topPlayGame := range topPlayGames {
				if userGame := userGameMap[topPlayGame.GameId]; userGame != nil && userGame.TopicId == topicId && userGame.Visible() && len(userGame.Desc) >= int(descMinLen) {
					appendGame(userGame)
				}
			}
		}
	} else {
		// 获取最近最多人玩过的玩法
		topPlayGames, err := m.playGamesStore.Top(ctx, roleId, topicId, lastTime, pb.GameSource_GAME_SOURCE_UNSPECIFIED, uint32(topN))
		if err != nil {
			return nil, err
		}
		if len(topPlayGames) > 0 {
			var userGameIds []string
			for _, topPlayGame := range topPlayGames {
				switch topPlayGame.Source {
				case pb.GameSource_GAME_SOURCE_USER:
					userGameIds = append(userGameIds, topPlayGame.GameId)
				}
			}
			userGameMap := make(entity.GameMap)
			if len(userGameIds) > 0 {
				if userGameMap, err = m.userGamesStore.BatchGet(ctx, userGameIds); err != nil {
					return nil, err
				}
			}

			officialGameMap := m.bitable.GetGameInfo()
			for _, topPlayGame := range topPlayGames {
				switch topPlayGame.Source {
				case pb.GameSource_GAME_SOURCE_USER:
					if game := userGameMap[topPlayGame.GameId]; game != nil && game.Visible() && len(game.Desc) >= int(descMinLen) {
						appendGame(game)
					}
				case pb.GameSource_GAME_SOURCE_OFFICIAL:
					if game := officialGameMap[topPlayGame.GameId]; game != nil {
						appendGame(game)
					}
				}
			}

			if len(games) >= int(topN) {
				return games[:topN], nil
			}
		}

		officialGames, err := m.GetRoleOfficialGameList(ctx, roleId)
		if err != nil {
			return nil, err
		}
		// 填充剩余的官方玩法
		for _, officialGame := range officialGames {
			if _, exists := gameIdSet[officialGame.ID]; !exists {
				appendGame(officialGame)
			}
		}
	}
	if len(games) >= n {
		return games[:n], nil
	}

	// 数量不够，补充最近创建的用户玩法
	lastUserGames, err := m.userGamesStore.Last(ctx, roleId, topicId, pb.GameState_GAME_STATE_PUBLIC, pb.GameExpose_GAME_EXPOSE_EXPOSED, descMinLen, uint32(lastN))
	if err != nil {
		return nil, err
	}
	for _, lastUserGame := range lastUserGames {
		if _, exists := gameIdSet[lastUserGame.ID]; !exists {
			appendGame(lastUserGame)
		}
	}
	if len(games) >= n {
		return games[:n], nil
	}

	return games, nil
}

func (m *manager) GetGame(ctx context.Context, id string) (*entity.Game, error) {
	if primitive.IsValidObjectID(id) {
		// 用户玩法
		return m.userGamesStore.Get(ctx, id)
	} else {
		// 官方玩法
		return m.bitable.GetGameInfo()[id], nil
	}
}
