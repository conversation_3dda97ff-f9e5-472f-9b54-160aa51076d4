package game

import (
	"context"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/gookit/goutil/timex"
	pb "golang.52tt.com/protocol/services/aigc/aigc-game"
	chat_bot "golang.52tt.com/protocol/services/chat-bot"
	"golang.52tt.com/services/aigc/aigc-game/internal/config"
	config_mocks "golang.52tt.com/services/aigc/aigc-game/internal/config/mocks"
	"golang.52tt.com/services/aigc/aigc-game/internal/entity"
	"golang.52tt.com/services/aigc/aigc-game/internal/event"
	event_mocks "golang.52tt.com/services/aigc/aigc-game/internal/event/mocks"
	"golang.52tt.com/services/aigc/aigc-game/internal/store"
	store_mocks "golang.52tt.com/services/aigc/aigc-game/internal/store/mocks"
)

func Test_manager_EnterGame(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	playGamesStore := store_mocks.NewMockPlayGamesStore(ctrl)

	type fields struct {
		playGamesStore store.PlayGamesStore
	}
	type args struct {
		ctx  context.Context
		game *entity.PlayGame
	}
	tests := []struct {
		name    string
		fields  fields
		mock    func()
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				playGamesStore: playGamesStore,
			},
			mock: func() {
				playGamesStore.EXPECT().Save(gomock.Any(), gomock.Any()).Return(nil)
			},
			args: args{
				ctx: ctx,
				game: &entity.PlayGame{
					Uid:      1,
					RoleId:   1,
					TopicId:  1,
					GameId:   "1",
					Source:   pb.GameSource_GAME_SOURCE_OFFICIAL,
					LastTime: time.Now(),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt.mock()
		t.Run(tt.name, func(t *testing.T) {
			m := &manager{
				playGamesStore: tt.fields.playGamesStore,
			}
			if _, err := m.EnterGame(tt.args.ctx, tt.args.game); (err != nil) != tt.wantErr {
				t.Errorf("manager.EnterGame() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_manager_GetGame(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	userGamesStore := store_mocks.NewMockUserGamesStore(ctrl)
	bitableConfiger := config_mocks.NewMockBitableConfiger(ctrl)

	type fields struct {
		bitable        config.BitableConfiger
		userGamesStore store.UserGamesStore
	}
	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		fields  fields
		mock    func()
		args    args
		want    *entity.Game
		wantErr bool
	}{
		{
			name: "official game",
			fields: fields{
				bitable: bitableConfiger,
			},
			mock: func() {
				bitableConfiger.EXPECT().GetGameInfo().Return(map[string]*entity.Game{
					"1": {
						ID:       "1",
						TopicId:  1,
						State:    pb.GameState_GAME_STATE_PUBLIC,
						Source:   pb.GameSource_GAME_SOURCE_OFFICIAL,
						Expose:   pb.GameExpose_GAME_EXPOSE_EXPOSED,
						Order:    1,
						Title:    "title",
						Subtitle: "subtitle",
						Desc:     "desc",
						Prompt:   "prompt",
						Prologue: "prologue",
						Bg:       "bg",
						Bgm:      "bgm",
						Icon:     "icon",
					},
				})
			},
			args: args{
				ctx: ctx,
				id:  "1",
			},
			want: &entity.Game{
				ID:       "1",
				TopicId:  1,
				State:    pb.GameState_GAME_STATE_PUBLIC,
				Source:   pb.GameSource_GAME_SOURCE_OFFICIAL,
				Expose:   pb.GameExpose_GAME_EXPOSE_EXPOSED,
				Order:    1,
				Title:    "title",
				Subtitle: "subtitle",
				Desc:     "desc",
				Prompt:   "prompt",
				Prologue: "prologue",
				Bg:       "bg",
				Bgm:      "bgm",
				Icon:     "icon",
			},
			wantErr: false,
		},
		{
			name: "user game",
			fields: fields{
				userGamesStore: userGamesStore,
			},
			mock: func() {
				userGamesStore.EXPECT().Get(gomock.Any(), "67e673f06b3d2a5fb3a3cb44").Return(&entity.Game{
					ID:       "67e673f06b3d2a5fb3a3cb44",
					TopicId:  1,
					State:    pb.GameState_GAME_STATE_PRIVATE,
					Source:   pb.GameSource_GAME_SOURCE_USER,
					Expose:   pb.GameExpose_GAME_EXPOSE_EXPOSED,
					Order:    1,
					Title:    "title",
					Subtitle: "subtitle",
					Desc:     "desc",
					Prompt:   "prompt",
					Prologue: "prologue",
				}, nil)
			},
			args: args{
				ctx: ctx,
				id:  "67e673f06b3d2a5fb3a3cb44",
			},
			want: &entity.Game{
				ID:       "67e673f06b3d2a5fb3a3cb44",
				TopicId:  1,
				State:    pb.GameState_GAME_STATE_PRIVATE,
				Source:   pb.GameSource_GAME_SOURCE_USER,
				Expose:   pb.GameExpose_GAME_EXPOSE_EXPOSED,
				Order:    1,
				Title:    "title",
				Subtitle: "subtitle",
				Desc:     "desc",
				Prompt:   "prompt",
				Prologue: "prologue",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt.mock()
		t.Run(tt.name, func(t *testing.T) {
			m := &manager{
				bitable:        tt.fields.bitable,
				userGamesStore: tt.fields.userGamesStore,
			}
			got, err := m.GetGame(tt.args.ctx, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("manager.GetGame() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("manager.GetGame() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_manager_GetRoleOfficialGameList(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	bitableConfiger := config_mocks.NewMockBitableConfiger(ctrl)

	gameMap := map[string]*entity.Game{
		"1": {
			ID:       "1",
			TopicId:  1,
			State:    pb.GameState_GAME_STATE_PUBLIC,
			Source:   pb.GameSource_GAME_SOURCE_OFFICIAL,
			Expose:   pb.GameExpose_GAME_EXPOSE_EXPOSED,
			Order:    2,
			Title:    "title",
			Subtitle: "subtitle",
			Desc:     "desc",
			Prompt:   "prompt",
			Prologue: "prologue",
			Bg:       "bg",
			Bgm:      "bgm",
			Icon:     "icon",
		},
		"2": {
			ID:       "2",
			TopicId:  1,
			State:    pb.GameState_GAME_STATE_PUBLIC,
			Source:   pb.GameSource_GAME_SOURCE_OFFICIAL,
			Expose:   pb.GameExpose_GAME_EXPOSE_EXPOSED,
			Order:    1,
			Title:    "title",
			Subtitle: "subtitle",
			Desc:     "desc",
			Prompt:   "prompt",
			Prologue: "prologue",
			Bg:       "bg",
			Bgm:      "bgm",
			Icon:     "icon",
		},
	}

	type fields struct {
		bitable config.BitableConfiger
	}
	type args struct {
		ctx    context.Context
		roleId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		mock    func()
		args    args
		want    entity.GameList
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				bitable: bitableConfiger,
			},
			mock: func() {
				bitableConfiger.EXPECT().GetRoleGameIds().Return(map[uint32][]string{
					1: {"1", "2"},
				})
				bitableConfiger.EXPECT().GetGameInfo().Return(gameMap)
			},
			args: args{
				ctx:    ctx,
				roleId: 1,
			},
			want: entity.GameList{
				gameMap["2"],
				gameMap["1"],
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt.mock()
		t.Run(tt.name, func(t *testing.T) {
			m := &manager{
				bitable: tt.fields.bitable,
			}
			got, err := m.GetRoleOfficialGameList(tt.args.ctx, tt.args.roleId)
			if (err != nil) != tt.wantErr {
				t.Errorf("manager.GetRoleOfficialGameList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("manager.GetRoleOfficialGameList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_manager_SendRoleGameSystemMsg(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	eventBus := event_mocks.NewMockBus(ctrl)

	type fields struct {
		eventBus event.Bus
	}
	type args struct {
		ctx       context.Context
		partnerId uint32
		uid       uint32
		msg       *chat_bot.SendingMsg
	}
	tests := []struct {
		name    string
		fields  fields
		mock    func()
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				eventBus: eventBus,
			},
			mock: func() {
				eventBus.EXPECT().Publish(gomock.Any(), event.PubEventSendSingleMsg, "1", gomock.Any()).Return(nil)
			},
			args: args{
				ctx:       ctx,
				partnerId: 1,
				uid:       1,
				msg:       &chat_bot.SendingMsg{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt.mock()
		t.Run(tt.name, func(t *testing.T) {
			m := &manager{
				eventBus: tt.fields.eventBus,
			}
			if err := m.SendRoleGameSystemMsg(tt.args.ctx, tt.args.partnerId, tt.args.uid, tt.args.msg); (err != nil) != tt.wantErr {
				t.Errorf("manager.SendRoleGameGreeting() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_manager_GetRoleTopGameList(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		bitable  = config_mocks.NewMockBitableConfiger(ctrl)
		ttConfig = config_mocks.NewMockTTConfiger(ctrl)

		userGamesStore = store_mocks.NewMockUserGamesStore(ctrl)
		playGamesStore = store_mocks.NewMockPlayGamesStore(ctrl)
	)

	var (
		officialGames = map[string]*entity.Game{
			"1": {
				ID:      "1",
				Source:  pb.GameSource_GAME_SOURCE_OFFICIAL,
				TopicId: 1,

				Desc: "hello",
			},
		}
		roleOfficialGameIds = map[uint32][]string{
			1: {"1"},
		}

		userGames = map[string]*entity.Game{
			"685a4a996ef257c989c314f2": {
				ID:      "685a4a996ef257c989c314f2",
				TopicId: 1,
				State:   pb.GameState_GAME_STATE_PUBLIC,
				Source:  pb.GameSource_GAME_SOURCE_USER,
				Expose:  pb.GameExpose_GAME_EXPOSE_EXPOSED,

				Desc: "hello",
			},
		}
	)

	bitable.EXPECT().GetGameInfo().Return(officialGames).AnyTimes()
	bitable.EXPECT().GetRoleGameIds().Return(roleOfficialGameIds).AnyTimes()

	ttConfig.EXPECT().GetTopGameRange().Return(timex.Day).AnyTimes()
	ttConfig.EXPECT().GetTopGameDescMinLen().Return(uint32(3)).AnyTimes()

	type fields struct {
		bitable  config.BitableConfiger
		ttConfig config.TTConfiger

		userGamesStore store.UserGamesStore
		playGamesStore store.PlayGamesStore
	}
	type args struct {
		ctx     context.Context
		roleId  uint32
		topicId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		mock    func()
		want    entity.GameList
		wantErr bool
	}{
		{
			name: "topicId > 0 && len(officialGames) >= n",
			fields: fields{
				bitable:  bitable,
				ttConfig: ttConfig,

				userGamesStore: userGamesStore,
				playGamesStore: playGamesStore,
			},
			mock: func() {
				ttConfig.EXPECT().GetTopGameN().Return(uint32(1))
			},
			args: args{
				ctx:     ctx,
				roleId:  1,
				topicId: 1,
			},
			want: entity.GameList{
				officialGames["1"],
			},
			wantErr: false,
		},
		{
			name: "topicId == 0 && len(topPlayGames) >= n",
			fields: fields{
				bitable:  bitable,
				ttConfig: ttConfig,

				userGamesStore: userGamesStore,
				playGamesStore: playGamesStore,
			},
			mock: func() {
				ttConfig.EXPECT().GetTopGameN().Return(uint32(2))

				playGamesStore.EXPECT().
					Top(gomock.Any(), uint32(1), uint32(0), gomock.Any(), pb.GameSource_GAME_SOURCE_UNSPECIFIED, gomock.Any()).
					Return(entity.TopPlayGameList{
						{
							GameId:  "1",
							Source:  pb.GameSource_GAME_SOURCE_OFFICIAL,
							UserNum: 2,
						},
						{
							GameId:  "685a4a996ef257c989c314f2",
							Source:  pb.GameSource_GAME_SOURCE_USER,
							UserNum: 1,
						},
					}, nil)

				userGamesStore.EXPECT().
					BatchGet(gomock.Any(), []string{"685a4a996ef257c989c314f2"}).
					Return(
						entity.GameMap{
							"685a4a996ef257c989c314f2": userGames["685a4a996ef257c989c314f2"],
						},
						nil,
					)
			},
			args: args{
				ctx:     ctx,
				roleId:  1,
				topicId: 0,
			},
			want: entity.GameList{
				officialGames["1"],
				userGames["685a4a996ef257c989c314f2"],
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt.mock()
		t.Run(tt.name, func(t *testing.T) {
			m := &manager{
				bitable:  tt.fields.bitable,
				ttConfig: tt.fields.ttConfig,

				userGamesStore: tt.fields.userGamesStore,
				playGamesStore: tt.fields.playGamesStore,
			}
			got, err := m.GetRoleTopGameList(tt.args.ctx, tt.args.roleId, tt.args.topicId)
			if (err != nil) != tt.wantErr {
				t.Errorf("manager.GetRoleTopGameList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("manager.GetRoleTopGameList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_manager_GetOfficialGameList(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	bitableConfiger := config_mocks.NewMockBitableConfiger(ctrl)

	gameMap := map[string]*entity.Game{
		"1": {
			ID:       "1",
			TopicId:  1,
			State:    pb.GameState_GAME_STATE_PUBLIC,
			Source:   pb.GameSource_GAME_SOURCE_OFFICIAL,
			Expose:   pb.GameExpose_GAME_EXPOSE_EXPOSED,
			Order:    2,
			Title:    "title",
			Subtitle: "subtitle",
			Desc:     "desc",
			Prompt:   "prompt",
			Prologue: "prologue",
			Bg:       "bg",
			Bgm:      "bgm",
			Icon:     "icon",
		},
		"2": {
			ID:       "2",
			TopicId:  1,
			State:    pb.GameState_GAME_STATE_PUBLIC,
			Source:   pb.GameSource_GAME_SOURCE_OFFICIAL,
			Expose:   pb.GameExpose_GAME_EXPOSE_EXPOSED,
			Order:    1,
			Title:    "title",
			Subtitle: "subtitle",
			Desc:     "desc",
			Prompt:   "prompt",
			Prologue: "prologue",
			Bg:       "bg",
			Bgm:      "bgm",
			Icon:     "icon",
		},
	}

	type fields struct {
		bitable config.BitableConfiger
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		mock    func()
		args    args
		want    entity.GameList
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				bitable: bitableConfiger,
			},
			mock: func() {
				bitableConfiger.EXPECT().GetGameInfo().Return(gameMap)
			},
			args: args{
				ctx: ctx,
			},
			want: entity.GameList{
				gameMap["2"],
				gameMap["1"],
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt.mock()
		t.Run(tt.name, func(t *testing.T) {
			m := &manager{
				bitable: tt.fields.bitable,
			}
			got, err := m.GetOfficialGameList(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("manager.GetRoleOfficialGameList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("manager.GetRoleOfficialGameList() = %v, want %v", got, tt.want)
			}
		})
	}
}
