// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-game/internal/mgr (interfaces: TopicManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	entity "golang.52tt.com/services/aigc/aigc-game/internal/entity"
)

// MockTopicManager is a mock of TopicManager interface.
type MockTopicManager struct {
	ctrl     *gomock.Controller
	recorder *MockTopicManagerMockRecorder
}

// MockTopicManagerMockRecorder is the mock recorder for MockTopicManager.
type MockTopicManagerMockRecorder struct {
	mock *MockTopicManager
}

// NewMockTopicManager creates a new mock instance.
func NewMockTopicManager(ctrl *gomock.Controller) *MockTopicManager {
	mock := &MockTopicManager{ctrl: ctrl}
	mock.recorder = &MockTopicManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTopicManager) EXPECT() *MockTopicManagerMockRecorder {
	return m.recorder
}

// GetTopicList mocks base method.
func (m *MockTopicManager) GetTopicList(arg0 context.Context) (entity.TopicList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTopicList", arg0)
	ret0, _ := ret[0].(entity.TopicList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTopicList indicates an expected call of GetTopicList.
func (mr *MockTopicManagerMockRecorder) GetTopicList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTopicList", reflect.TypeOf((*MockTopicManager)(nil).GetTopicList), arg0)
}
