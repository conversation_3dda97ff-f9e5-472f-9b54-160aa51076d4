// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-game/internal/mgr (interfaces: GameManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	chat_bot "golang.52tt.com/protocol/services/chat-bot"
	entity "golang.52tt.com/services/aigc/aigc-game/internal/entity"
)

// MockGameManager is a mock of GameManager interface.
type MockGameManager struct {
	ctrl     *gomock.Controller
	recorder *MockGameManagerMockRecorder
}

// MockGameManagerMockRecorder is the mock recorder for MockGameManager.
type MockGameManagerMockRecorder struct {
	mock *MockGameManager
}

// NewMockGameManager creates a new mock instance.
func NewMockGameManager(ctrl *gomock.Controller) *MockGameManager {
	mock := &MockGameManager{ctrl: ctrl}
	mock.recorder = &MockGameManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGameManager) EXPECT() *MockGameManagerMockRecorder {
	return m.recorder
}

// EnterGame mocks base method.
func (m *MockGameManager) EnterGame(arg0 context.Context, arg1 *entity.PlayGame) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnterGame", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EnterGame indicates an expected call of EnterGame.
func (mr *MockGameManagerMockRecorder) EnterGame(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnterGame", reflect.TypeOf((*MockGameManager)(nil).EnterGame), arg0, arg1)
}

// GetGame mocks base method.
func (m *MockGameManager) GetGame(arg0 context.Context, arg1 string) (*entity.Game, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGame", arg0, arg1)
	ret0, _ := ret[0].(*entity.Game)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGame indicates an expected call of GetGame.
func (mr *MockGameManagerMockRecorder) GetGame(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGame", reflect.TypeOf((*MockGameManager)(nil).GetGame), arg0, arg1)
}

// GetOfficialGameList mocks base method.
func (m *MockGameManager) GetOfficialGameList(arg0 context.Context) (entity.GameList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOfficialGameList", arg0)
	ret0, _ := ret[0].(entity.GameList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOfficialGameList indicates an expected call of GetOfficialGameList.
func (mr *MockGameManagerMockRecorder) GetOfficialGameList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOfficialGameList", reflect.TypeOf((*MockGameManager)(nil).GetOfficialGameList), arg0)
}

// GetRoleOfficialGameList mocks base method.
func (m *MockGameManager) GetRoleOfficialGameList(arg0 context.Context, arg1 uint32) (entity.GameList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRoleOfficialGameList", arg0, arg1)
	ret0, _ := ret[0].(entity.GameList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRoleOfficialGameList indicates an expected call of GetRoleOfficialGameList.
func (mr *MockGameManagerMockRecorder) GetRoleOfficialGameList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoleOfficialGameList", reflect.TypeOf((*MockGameManager)(nil).GetRoleOfficialGameList), arg0, arg1)
}

// GetRoleTopGameList mocks base method.
func (m *MockGameManager) GetRoleTopGameList(arg0 context.Context, arg1, arg2 uint32) (entity.GameList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRoleTopGameList", arg0, arg1, arg2)
	ret0, _ := ret[0].(entity.GameList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRoleTopGameList indicates an expected call of GetRoleTopGameList.
func (mr *MockGameManagerMockRecorder) GetRoleTopGameList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoleTopGameList", reflect.TypeOf((*MockGameManager)(nil).GetRoleTopGameList), arg0, arg1, arg2)
}

// SendRoleGameSystemMsg mocks base method.
func (m *MockGameManager) SendRoleGameSystemMsg(arg0 context.Context, arg1, arg2 uint32, arg3 *chat_bot.SendingMsg) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendRoleGameSystemMsg", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendRoleGameSystemMsg indicates an expected call of SendRoleGameSystemMsg.
func (mr *MockGameManagerMockRecorder) SendRoleGameSystemMsg(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendRoleGameSystemMsg", reflect.TypeOf((*MockGameManager)(nil).SendRoleGameSystemMsg), arg0, arg1, arg2, arg3)
}
