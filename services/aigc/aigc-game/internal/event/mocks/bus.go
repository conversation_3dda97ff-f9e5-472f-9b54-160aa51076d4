// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-game/internal/event (interfaces: Bus)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	subscriber "gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	event "golang.52tt.com/services/aigc/aigc-game/internal/event"
)

// MockBus is a mock of Bus interface.
type MockBus struct {
	ctrl     *gomock.Controller
	recorder *MockBusMockRecorder
}

// MockBusMockRecorder is the mock recorder for MockBus.
type MockBusMockRecorder struct {
	mock *MockBus
}

// NewMockBus creates a new mock instance.
func NewMockBus(ctrl *gomock.Controller) *MockBus {
	mock := &MockBus{ctrl: ctrl}
	mock.recorder = &MockBusMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBus) EXPECT() *MockBusMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockBus) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockBusMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockBus)(nil).Close))
}

// Publish mocks base method.
func (m *MockBus) Publish(arg0 context.Context, arg1 event.PubEvent, arg2 string, arg3 interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Publish", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// Publish indicates an expected call of Publish.
func (mr *MockBusMockRecorder) Publish(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Publish", reflect.TypeOf((*MockBus)(nil).Publish), arg0, arg1, arg2, arg3)
}

// Subscribe mocks base method.
func (m *MockBus) Subscribe(arg0 context.Context, arg1 event.SubName, arg2 subscriber.ProcessorContextFunc) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Subscribe", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Subscribe indicates an expected call of Subscribe.
func (mr *MockBusMockRecorder) Subscribe(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Subscribe", reflect.TypeOf((*MockBus)(nil).Subscribe), arg0, arg1, arg2)
}
