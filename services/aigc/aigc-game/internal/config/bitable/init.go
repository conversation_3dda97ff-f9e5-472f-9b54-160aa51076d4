package bitable

import (
	"context"

	"golang.52tt.com/pkg/bitable"
	lark_config_server "golang.52tt.com/protocol/services/lark-config-server"
	conf "golang.52tt.com/services/aigc/aigc-game/internal/config"
)

type configer struct {
	gameInfo         *bitable.Table
	gameTopic        *bitable.Table
	roleGameInfo     *bitable.Table
	roleGameRelation *bitable.Table
}

func New(ctx context.Context) (conf.BitableConfiger, error) {
	bitable.Init(lark_config_server.MustNewClient(ctx))

	c := &configer{}

	var err error
	if c.gameTopic, err = bitable.Load("aigc", "GameTopic", c.formatGameTopic); err != nil {
		return nil, err
	}
	if c.gameInfo, err = bitable.Load("aigc", "GameInfo", c.formatGameInfo); err != nil {
		return nil, err
	}
	if c.roleGameInfo, err = bitable.Load("aigc", "RoleGameInfo", c.formatRoleGameInfo); err != nil {
		return nil, err
	}
	if c.roleGameRelation, err = bitable.Load("aigc", "RoleGameRelation", c.formatRoleGameRelation); err != nil {
		return nil, err
	}

	return c, err
}
