package bitable

import (
	"fmt"
	"strconv"
	"strings"

	"gitlab.ttyuyin.com/tyr/x/log"

	lark_config_server "golang.52tt.com/protocol/services/lark-config-server"
)

func (c *configer) GetRoleGameIds() map[uint32][]string {
	gameIds := c.loadRoleGameIds()
	return gameIds
}

func (c *configer) formatRoleGameInfo(table *lark_config_server.Bitable) (any, error) {
	gameMap := make(map[string]*roleGameInfo)
	for _, row := range table.GetRows() {
		columns := row.GetColumns()

		id := columns["玩法ID"].GetText()
		if id == "" {
			continue
		}

		item := &roleGameInfo{
			RowId: row.GetId(),

			Id:   id,
			Name: columns["玩法名称"].GetText(),
		}

		// log.Infof("formatRoleGameInfo item: %+v", item)
		gameMap[item.RowId] = item
	}

	return gameMap, nil
}

func (c *configer) formatRoleGameRelation(table *lark_config_server.Bitable) (any, error) {
	gameMap := c.loadRoleGameInfo()

	roleGameIdSet := make(map[uint32]map[string]struct{})
	for _, row := range table.GetRows() {
		columns := row.GetColumns()

		var roleIds []uint32
		for _, v := range strings.Split(strings.ReplaceAll(columns["角色ID"].GetText(), "，", ","), ",") {
			if id, _ := strconv.ParseUint(strings.TrimSpace(v), 10, 64); id > 0 {
				roleIds = append(roleIds, uint32(id))
			}
		}

		gameIds := make([]string, 0, 20)
		for i := 1; i <= 20; i++ {
			gameRowIds := columns[fmt.Sprintf("玩法%d", i)].GetLink().GetRowIds()
			if len(gameRowIds) == 0 {
				continue
			}

			game := gameMap[gameRowIds[0]]
			if game == nil {
				log.Warnf("formatRoleGameRelationTable game(%s) not found", gameRowIds[0])
				continue
			}

			gameIds = append(gameIds, game.Id)
		}

		for _, roleId := range roleIds {
			if roleGameIdSet[roleId] == nil {
				roleGameIdSet[roleId] = make(map[string]struct{})
			}
			for _, gameId := range gameIds {
				roleGameIdSet[roleId][gameId] = struct{}{}
			}
		}
	}

	roleGameIds := make(map[uint32][]string)
	for roleId, gameIdSet := range roleGameIdSet {
		for gameId := range gameIdSet {
			roleGameIds[roleId] = append(roleGameIds[roleId], gameId)
		}
	}

	// log.Infof("formatRoleGameRelation roleGameIds: %+v", roleGameIds)
	return roleGameIds, nil
}

type roleGameInfo struct {
	RowId string

	Id   string
	Name string
}

func (c *configer) loadRoleGameInfo() map[string]*roleGameInfo {
	v, ok := c.roleGameInfo.Get().(map[string]*roleGameInfo)
	if !ok || v == nil {
		return make(map[string]*roleGameInfo)
	}
	return v
}

func (c *configer) loadRoleGameIds() map[uint32][]string {
	v, ok := c.roleGameRelation.Get().(map[uint32][]string)
	if !ok || v == nil {
		return make(map[uint32][]string)
	}
	return v
}
