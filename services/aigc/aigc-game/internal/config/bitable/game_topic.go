package bitable

import (
	"github.com/spf13/cast"

	"gitlab.ttyuyin.com/tyr/x/log"

	lark_config_server "golang.52tt.com/protocol/services/lark-config-server"
	"golang.52tt.com/services/aigc/aigc-game/internal/entity"
)

func (c *configer) GetGameTopic() map[uint32]*entity.Topic {
	mp := make(map[uint32]*entity.Topic)
	for _, v := range c.loadGameTopic() {
		mp[v.Id] = gameTopicEntity(v)
	}
	return mp
}

func (c *configer) formatGameTopic(table *lark_config_server.Bitable) (any, error) {
	topicMap := make(map[string]*gameTopic)
	for i, row := range table.GetRows() {
		columns := row.GetColumns()

		if text := columns["是否启用"].GetSingleSelect(); text != "是" {
			continue
		}

		id := cast.ToUint32(columns["主题ID"].GetAutoNumber())
		if id == 0 {
			log.Warnf("formatGameTopic row(%d) miss id", i)
			continue
		}

		item := &gameTopic{
			RowId: row.Id,

			Id:    id,
			Name:  columns["主题名称"].GetText(),
			Order: int(columns["排序"].GetNumber()),
		}

		// log.Infof("formatGameTopic item: %+v", item)
		topicMap[item.RowId] = item
	}

	return topicMap, nil
}

type gameTopic struct {
	RowId string

	Id    uint32 `json:"id,omitempty"`
	Name  string `json:"name,omitempty"`
	Order int    `json:"order,omitempty"`
}

func gameTopicEntity(topic *gameTopic) *entity.Topic {
	if topic == nil {
		return nil
	}

	return &entity.Topic{
		Id:    topic.Id,
		Name:  topic.Name,
		Order: uint32(topic.Order),
	}
}

func (c *configer) loadGameTopic() map[string]*gameTopic {
	v, ok := c.gameTopic.Get().(map[string]*gameTopic)
	if !ok || v == nil {
		return make(map[string]*gameTopic)
	}
	return v
}
