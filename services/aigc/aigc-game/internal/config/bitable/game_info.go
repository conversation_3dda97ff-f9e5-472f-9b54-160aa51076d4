package bitable

import (
	"gitlab.ttyuyin.com/tyr/x/log"

	pb "golang.52tt.com/protocol/services/aigc/aigc-game"
	lark_config_server "golang.52tt.com/protocol/services/lark-config-server"
	"golang.52tt.com/services/aigc/aigc-game/internal/entity"
)

func (c *configer) GetGameInfo() map[string]*entity.Game {
	mp := make(map[string]*entity.Game)
	for _, v := range c.loadGameInfo() {
		mp[v.Id] = gameEntity(v)
	}
	return mp
}

func (c *configer) formatGameInfo(table *lark_config_server.Bitable) (any, error) {
	topicMap := c.loadGameTopic()

	gameMap := make(map[string]*gameInfo)
	for i, row := range table.GetRows() {
		columns := row.GetColumns()

		id := columns["玩法ID"].GetText()
		if id == "" {
			log.Warnf("formatGameInfo row(%d) miss id", i)
			continue
		}

		if v := columns["状态"].GetSingleSelect(); v == "已删除" {
			log.Warnf("formatGameInfo game(%s) deleted", id)
			continue
		}

		item := &gameInfo{
			RowId: row.Id,

			Id:        id,
			Name:      columns["玩法名称"].GetText(),
			Prompt:    columns["Prompt"].GetText(),
			Icon:      columns["图标"].GetText(),
			Greeting:  columns["开场白"].GetText(),
			BGM:       columns["背景音乐"].GetText(),
			BG:        columns["背景图"].GetText(),
			SceneDesc: columns["前景描述"].GetText(),
			Order:     int(columns["排序"].GetNumber()),
			SubTitle:  columns["副标题"].GetText(),
		}
		if topicRowIds := columns["主题ID"].GetLink().GetRowIds(); len(topicRowIds) > 0 {
			if topic := topicMap[topicRowIds[0]]; topic != nil {
				item.TopicId = topic.Id
			}
		}

		// log.Infof("formatGameInfo item: %+v", item)
		gameMap[row.Id] = item
	}

	return gameMap, nil
}

type gameInfo struct {
	RowId string

	Id        string `json:"id,omitempty"`
	Name      string `json:"name,omitempty"`
	Prompt    string `json:"prompt,omitempty"`
	Icon      string `json:"icon,omitempty"`
	Greeting  string `json:"greeting,omitempty"`
	BGM       string `json:"bgm,omitempty"`
	BG        string `json:"bg,omitempty"`
	SceneDesc string `json:"scene_desc,omitempty"`
	Status    int    `json:"status,omitempty"`
	TopicId   uint32 `json:"topic_id,omitempty"`
	Order     int    `json:"order,omitempty"`
	SubTitle  string `json:"sub_title,omitempty"`
}

func gameEntity(game *gameInfo) *entity.Game {
	if game == nil {
		return nil
	}

	return &entity.Game{
		ID:       game.Id,
		TopicId:  game.TopicId,
		State:    pb.GameState_GAME_STATE_PUBLIC,
		Source:   pb.GameSource_GAME_SOURCE_OFFICIAL,
		Expose:   pb.GameExpose_GAME_EXPOSE_EXPOSED,
		Order:    uint32(game.Order),
		Title:    game.Name,
		Subtitle: game.SubTitle,
		Desc:     game.SceneDesc,
		Prompt:   game.Prompt,
		Prologue: game.Greeting,
		Bg:       game.BG,
		Bgm:      game.BGM,
		Icon:     game.Icon,
	}
}

func (c *configer) loadGameInfo() map[string]*gameInfo {
	v, ok := c.gameInfo.Get().(map[string]*gameInfo)
	if !ok || v == nil {
		return make(map[string]*gameInfo)
	}
	return v
}
