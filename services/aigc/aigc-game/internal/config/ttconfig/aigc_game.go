package ttconfig

import (
	"sync/atomic"
	"time"

	"github.com/gookit/goutil/timex"

	"gitlab.ttyuyin.com/avengers/tyr/core/config/ttconfig"

	conf "golang.52tt.com/services/aigc/aigc-game/internal/config"
)

type config struct {
	TopGameN          uint32 `json:"top_game_n"`            // 热门游戏数量
	TopGameRange      uint32 `json:"top_game_range"`        // 热门游戏时间范围，单位:秒
	TopGameDescMinLen uint32 `json:"top_game_desc_min_len"` // 热门游戏描述最小长度
}

func (c *config) Format() error {
	return nil
}

type configer struct {
	value *atomic.Value
}

func New() (conf.TTConfiger, error) {
	cfg := &config{}
	atomCfg, err := ttconfig.AtomLoad("aigc-game", cfg)
	if nil != err {
		return nil, err
	}

	c := &configer{
		value: atomCfg,
	}

	return c, nil
}

func (c *configer) load() *config {
	if c == nil || c.value == nil {
		return nil
	}
	return c.value.Load().(*config)
}

func (c *configer) GetTopGameN() uint32 {
	cfg := c.load()
	if cfg == nil || cfg.TopGameN == 0 {
		return 100
	}

	return cfg.TopGameN
}

func (c *configer) GetTopGameRange() time.Duration {
	cfg := c.load()
	if c == nil || cfg.TopGameRange == 0 {
		return timex.Day
	}

	return time.Duration(cfg.TopGameRange) * time.Second
}

func (c *configer) GetTopGameDescMinLen() uint32 {
	cfg := c.load()
	if cfg == nil || cfg.TopGameDescMinLen == 0 {
		return 20
	}

	return cfg.TopGameDescMinLen
}
