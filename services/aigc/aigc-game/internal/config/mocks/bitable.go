// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-game/internal/config (interfaces: BitableConfiger)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	entity "golang.52tt.com/services/aigc/aigc-game/internal/entity"
)

// MockBitableConfiger is a mock of BitableConfiger interface.
type MockBitableConfiger struct {
	ctrl     *gomock.Controller
	recorder *MockBitableConfigerMockRecorder
}

// MockBitableConfigerMockRecorder is the mock recorder for MockBitableConfiger.
type MockBitableConfigerMockRecorder struct {
	mock *MockBitableConfiger
}

// NewMockBitableConfiger creates a new mock instance.
func NewMockBitableConfiger(ctrl *gomock.Controller) *MockBitableConfiger {
	mock := &MockBitableConfiger{ctrl: ctrl}
	mock.recorder = &MockBitableConfigerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBitableConfiger) EXPECT() *MockBitableConfigerMockRecorder {
	return m.recorder
}

// GetGameInfo mocks base method.
func (m *MockBitableConfiger) GetGameInfo() map[string]*entity.Game {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameInfo")
	ret0, _ := ret[0].(map[string]*entity.Game)
	return ret0
}

// GetGameInfo indicates an expected call of GetGameInfo.
func (mr *MockBitableConfigerMockRecorder) GetGameInfo() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameInfo", reflect.TypeOf((*MockBitableConfiger)(nil).GetGameInfo))
}

// GetGameTopic mocks base method.
func (m *MockBitableConfiger) GetGameTopic() map[uint32]*entity.Topic {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameTopic")
	ret0, _ := ret[0].(map[uint32]*entity.Topic)
	return ret0
}

// GetGameTopic indicates an expected call of GetGameTopic.
func (mr *MockBitableConfigerMockRecorder) GetGameTopic() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameTopic", reflect.TypeOf((*MockBitableConfiger)(nil).GetGameTopic))
}

// GetRoleGameIds mocks base method.
func (m *MockBitableConfiger) GetRoleGameIds() map[uint32][]string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRoleGameIds")
	ret0, _ := ret[0].(map[uint32][]string)
	return ret0
}

// GetRoleGameIds indicates an expected call of GetRoleGameIds.
func (mr *MockBitableConfigerMockRecorder) GetRoleGameIds() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoleGameIds", reflect.TypeOf((*MockBitableConfiger)(nil).GetRoleGameIds))
}
