// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-game/internal/config (interfaces: TTConfiger)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
)

// MockTTConfiger is a mock of TTConfiger interface.
type MockTTConfiger struct {
	ctrl     *gomock.Controller
	recorder *MockTTConfigerMockRecorder
}

// MockTTConfigerMockRecorder is the mock recorder for MockTTConfiger.
type MockTTConfigerMockRecorder struct {
	mock *MockTTConfiger
}

// NewMockTTConfiger creates a new mock instance.
func NewMockTTConfiger(ctrl *gomock.Controller) *MockTTConfiger {
	mock := &MockTTConfiger{ctrl: ctrl}
	mock.recorder = &MockTTConfigerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTTConfiger) EXPECT() *MockTTConfigerMockRecorder {
	return m.recorder
}

// GetTopGameDescMinLen mocks base method.
func (m *MockTTConfiger) GetTopGameDescMinLen() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTopGameDescMinLen")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetTopGameDescMinLen indicates an expected call of GetTopGameDescMinLen.
func (mr *MockTTConfigerMockRecorder) GetTopGameDescMinLen() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTopGameDescMinLen", reflect.TypeOf((*MockTTConfiger)(nil).GetTopGameDescMinLen))
}

// GetTopGameN mocks base method.
func (m *MockTTConfiger) GetTopGameN() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTopGameN")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetTopGameN indicates an expected call of GetTopGameN.
func (mr *MockTTConfigerMockRecorder) GetTopGameN() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTopGameN", reflect.TypeOf((*MockTTConfiger)(nil).GetTopGameN))
}

// GetTopGameRange mocks base method.
func (m *MockTTConfiger) GetTopGameRange() time.Duration {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTopGameRange")
	ret0, _ := ret[0].(time.Duration)
	return ret0
}

// GetTopGameRange indicates an expected call of GetTopGameRange.
func (mr *MockTTConfigerMockRecorder) GetTopGameRange() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTopGameRange", reflect.TypeOf((*MockTTConfiger)(nil).GetTopGameRange))
}
