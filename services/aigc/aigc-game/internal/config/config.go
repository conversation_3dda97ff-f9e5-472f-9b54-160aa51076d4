package config

import (
	"time"

	"golang.52tt.com/services/aigc/aigc-game/internal/entity"
)

//go:generate mockgen -destination=mocks/bitable.go -package=mocks golang.52tt.com/services/aigc/aigc-game/internal/config BitableConfiger
type BitableConfiger interface {
	GetGameInfo() map[string]*entity.Game
	GetGameTopic() map[uint32]*entity.Topic
	GetRoleGameIds() map[uint32][]string
}

//go:generate mockgen -destination=mocks/tt.go -package=mocks golang.52tt.com/services/aigc/aigc-game/internal/config TTConfiger
type TTConfiger interface {
	GetTopGameN() uint32
	GetTopGameRange() time.Duration
	GetTopGameDescMinLen() uint32
}
