package entity

import (
	"time"

	pb "golang.52tt.com/protocol/services/aigc/aigc-game"
)

type Game struct {
	ID string

	CreatedAt time.Time
	UpdatedAt time.Time

	Uid     uint32
	RoleId  uint32
	TopicId uint32

	State  pb.GameState
	Source pb.GameSource
	Expose pb.GameExpose

	Order uint32

	Title    string
	Subtitle string
	Desc     string
	Prompt   string
	Prologue string

	Bg   string
	Bgm  string
	Icon string
}

func (g *Game) Visible() bool {
	return g != nil && (g.Source == pb.GameSource_GAME_SOURCE_OFFICIAL || g.State == pb.GameState_GAME_STATE_PUBLIC && g.Expose == pb.GameExpose_GAME_EXPOSE_EXPOSED)
}

type GameList []*Game

func (l GameList) MapById() GameMap {
	gameMap := make(GameMap, len(l))
	for _, game := range l {
		gameMap[game.ID] = game
	}
	return gameMap
}

type GameMap map[string]*Game
