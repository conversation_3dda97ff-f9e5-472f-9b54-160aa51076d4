package entity

import (
	"time"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/aigc/aigc-game"
)

type PlayGame struct {
	Uid     uint32
	RoleId  uint32
	TopicId uint32

	GameId string
	Source pb.GameSource

	LastTime time.Time
}

func NewPlayGame(uid, roleId, topicId uint32, gameId string, source pb.GameSource) (*PlayGame, error) {
	if uid == 0 || roleId == 0 || topicId == 0 || gameId == "" {
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "缺少参数")
	}
	switch source {
	case pb.GameSource_GAME_SOURCE_OFFICIAL, pb.GameSource_GAME_SOURCE_USER:
	default:
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "不支持的玩法来源")
	}

	return &PlayGame{
		Uid:     uid,
		RoleId:  roleId,
		TopicId: topicId,

		GameId: gameId,
		Source: source,

		LastTime: time.Now(),
	}, nil
}

type TopPlayGame struct {
	GameId string
	Source pb.GameSource

	UserNum uint32
}

type TopPlayGameList []*TopPlayGame
