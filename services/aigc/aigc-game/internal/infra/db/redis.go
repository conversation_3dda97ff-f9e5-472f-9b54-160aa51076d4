package db

import (
	"context"

	connect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"

	"golang.52tt.com/pkg/log"
)

type RedisDB struct {
	redis.Cmdable
}

func NewRedisDB(ctx context.Context, cfg *connect.RedisConfig) (*RedisDB, error) {
	client, err := connect.NewClient(ctx, cfg)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewRedisDB NewClient cfg(%+v) err: %v", cfg, err)
		return nil, err
	}

	c := &RedisDB{
		Cmdable: client,
	}

	return c, nil
}

func (r *RedisDB) Cmder() redis.Cmdable {
	return r.Cmdable
}

func (r *RedisDB) Close() error {
	return r.Cmdable.(redis.Client).Close()
}
