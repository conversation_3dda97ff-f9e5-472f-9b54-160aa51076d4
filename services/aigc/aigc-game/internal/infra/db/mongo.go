package db

import (
	"context"

	mongo_driver "go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/readpref"

	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mongo"

	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
)

type MongoDB struct {
	client   *mongo.ClientImpl
	database *mongo_driver.Database
}

func NewMongoDB(ctx context.Context, cfg *config.MongoConfig) (*MongoDB, error) {
	client, err := mongo.NewClient(ctx, cfg.OptionsForPrimaryPreferred())
	if err != nil {
		return nil, err
	}

	if err = client.Ping(ctx, readpref.Primary()); err != nil {
		log.ErrorWithCtx(ctx, "NewMongoDB Ping err: (%v)", err)
		return nil, err
	}

	db := &MongoDB{
		client:   client,
		database: client.Database(cfg.Database),
	}

	return db, nil
}

func (m *MongoDB) Database() *mongo_driver.Database {
	return m.database
}

func (m *MongoDB) Close(ctx context.Context) error {
	return m.client.Close(ctx)
}
