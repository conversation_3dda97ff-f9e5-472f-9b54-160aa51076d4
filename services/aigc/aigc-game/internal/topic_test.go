package internal

import (
	"context"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	pb "golang.52tt.com/protocol/services/aigc/aigc-game"
	"golang.52tt.com/services/aigc/aigc-game/internal/entity"
	"golang.52tt.com/services/aigc/aigc-game/internal/mgr"
	mock_mgr "golang.52tt.com/services/aigc/aigc-game/internal/mgr/mocks"
)

func TestServer_GetTopicList(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		topicMgr = mock_mgr.NewMockTopicManager(ctrl)
	)

	type fields struct {
		topicMgr mgr.TopicManager
	}
	type args struct {
		ctx context.Context
		req *pb.GetTopicListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		mock    func()
		want    *pb.GetTopicListResponse
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				topicMgr: topicMgr,
			},
			args: args{
				ctx: ctx,
				req: &pb.GetTopicListRequest{},
			},
			mock: func() {
				topicMgr.EXPECT().GetTopicList(gomock.Any()).Return(
					entity.TopicList{
						{
							Id:    1,
							Name:  "Topic1",
							Order: 1,
						},
						{
							Id:    2,
							Name:  "Topic2",
							Order: 2,
						},
					},
					nil,
				)
			},
			want: &pb.GetTopicListResponse{
				List: []*pb.Topic{
					{
						Id:   1,
						Name: "Topic1",
					},
					{
						Id:   2,
						Name: "Topic2",
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt.mock()
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				topicMgr: tt.fields.topicMgr,
			}
			got, err := s.GetTopicList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("Server.GetTopicList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Server.GetTopicList() = %v, want %v", got, tt.want)
			}
		})
	}
}
