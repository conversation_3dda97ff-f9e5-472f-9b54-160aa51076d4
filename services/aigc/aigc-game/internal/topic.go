package internal

import (
	"context"

	"gitlab.ttyuyin.com/tyr/x/log"

	pb "golang.52tt.com/protocol/services/aigc/aigc-game"
	"golang.52tt.com/services/aigc/aigc-game/internal/entity"
)

// GetTopicList 主题列表
func (s *Server) GetTopicList(ctx context.Context, req *pb.GetTopicListRequest) (*pb.GetTopicListResponse, error) {
	resp := new(pb.GetTopicListResponse)

	list, err := s.topicMgr.GetTopicList(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTopicList GetTopicList err: %v", err)
		return resp, err
	}

	for _, v := range list {
		resp.List = append(resp.List, topicPb(v))
	}

	return resp, nil
}

func topicPb(en *entity.Topic) *pb.Topic {
	topic := &pb.Topic{
		Id:   en.Id,
		Name: en.Name,
	}

	return topic
}
