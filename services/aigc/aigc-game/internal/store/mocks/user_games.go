// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-game/internal/store (interfaces: UserGamesStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	aigc_game "golang.52tt.com/protocol/services/aigc/aigc-game"
	entity "golang.52tt.com/services/aigc/aigc-game/internal/entity"
)

// MockUserGamesStore is a mock of UserGamesStore interface.
type MockUserGamesStore struct {
	ctrl     *gomock.Controller
	recorder *MockUserGamesStoreMockRecorder
}

// MockUserGamesStoreMockRecorder is the mock recorder for MockUserGamesStore.
type MockUserGamesStoreMockRecorder struct {
	mock *MockUserGamesStore
}

// NewMockUserGamesStore creates a new mock instance.
func NewMockUserGamesStore(ctrl *gomock.Controller) *MockUserGamesStore {
	mock := &MockUserGamesStore{ctrl: ctrl}
	mock.recorder = &MockUserGamesStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUserGamesStore) EXPECT() *MockUserGamesStoreMockRecorder {
	return m.recorder
}

// BatchGet mocks base method.
func (m *MockUserGamesStore) BatchGet(arg0 context.Context, arg1 []string) (entity.GameMap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGet", arg0, arg1)
	ret0, _ := ret[0].(entity.GameMap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGet indicates an expected call of BatchGet.
func (mr *MockUserGamesStoreMockRecorder) BatchGet(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGet", reflect.TypeOf((*MockUserGamesStore)(nil).BatchGet), arg0, arg1)
}

// CountByUid mocks base method.
func (m *MockUserGamesStore) CountByUid(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountByUid", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountByUid indicates an expected call of CountByUid.
func (mr *MockUserGamesStoreMockRecorder) CountByUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountByUid", reflect.TypeOf((*MockUserGamesStore)(nil).CountByUid), arg0, arg1)
}

// FindByUid mocks base method.
func (m *MockUserGamesStore) FindByUid(arg0 context.Context, arg1 uint32) (entity.GameList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByUid", arg0, arg1)
	ret0, _ := ret[0].(entity.GameList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByUid indicates an expected call of FindByUid.
func (mr *MockUserGamesStoreMockRecorder) FindByUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByUid", reflect.TypeOf((*MockUserGamesStore)(nil).FindByUid), arg0, arg1)
}

// Get mocks base method.
func (m *MockUserGamesStore) Get(arg0 context.Context, arg1 string) (*entity.Game, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", arg0, arg1)
	ret0, _ := ret[0].(*entity.Game)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockUserGamesStoreMockRecorder) Get(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockUserGamesStore)(nil).Get), arg0, arg1)
}

// Last mocks base method.
func (m *MockUserGamesStore) Last(arg0 context.Context, arg1, arg2 uint32, arg3 aigc_game.GameState, arg4 aigc_game.GameExpose, arg5, arg6 uint32) (entity.GameList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Last", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(entity.GameList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Last indicates an expected call of Last.
func (mr *MockUserGamesStoreMockRecorder) Last(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Last", reflect.TypeOf((*MockUserGamesStore)(nil).Last), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// Remove mocks base method.
func (m *MockUserGamesStore) Remove(arg0 context.Context, arg1 ...string) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Remove", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Remove indicates an expected call of Remove.
func (mr *MockUserGamesStoreMockRecorder) Remove(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Remove", reflect.TypeOf((*MockUserGamesStore)(nil).Remove), varargs...)
}

// Save mocks base method.
func (m *MockUserGamesStore) Save(arg0 context.Context, arg1 *entity.Game) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockUserGamesStoreMockRecorder) Save(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockUserGamesStore)(nil).Save), arg0, arg1)
}
