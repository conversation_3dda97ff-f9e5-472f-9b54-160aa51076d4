// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-game/internal/store (interfaces: PlayGamesStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	aigc_game "golang.52tt.com/protocol/services/aigc/aigc-game"
	entity "golang.52tt.com/services/aigc/aigc-game/internal/entity"
)

// MockPlayGamesStore is a mock of PlayGamesStore interface.
type MockPlayGamesStore struct {
	ctrl     *gomock.Controller
	recorder *MockPlayGamesStoreMockRecorder
}

// MockPlayGamesStoreMockRecorder is the mock recorder for MockPlayGamesStore.
type MockPlayGamesStoreMockRecorder struct {
	mock *MockPlayGamesStore
}

// NewMockPlayGamesStore creates a new mock instance.
func NewMockPlayGamesStore(ctrl *gomock.Controller) *MockPlayGamesStore {
	mock := &MockPlayGamesStore{ctrl: ctrl}
	mock.recorder = &MockPlayGamesStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPlayGamesStore) EXPECT() *MockPlayGamesStoreMockRecorder {
	return m.recorder
}

// Save mocks base method.
func (m *MockPlayGamesStore) Save(arg0 context.Context, arg1 *entity.PlayGame) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockPlayGamesStoreMockRecorder) Save(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockPlayGamesStore)(nil).Save), arg0, arg1)
}

// Top mocks base method.
func (m *MockPlayGamesStore) Top(arg0 context.Context, arg1, arg2 uint32, arg3 time.Time, arg4 aigc_game.GameSource, arg5 uint32) (entity.TopPlayGameList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Top", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(entity.TopPlayGameList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Top indicates an expected call of Top.
func (mr *MockPlayGamesStoreMockRecorder) Top(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Top", reflect.TypeOf((*MockPlayGamesStore)(nil).Top), arg0, arg1, arg2, arg3, arg4, arg5)
}
