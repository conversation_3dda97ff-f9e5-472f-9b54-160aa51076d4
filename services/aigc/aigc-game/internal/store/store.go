package store

import (
	"context"
	"time"

	pb "golang.52tt.com/protocol/services/aigc/aigc-game"
	"golang.52tt.com/services/aigc/aigc-game/internal/entity"
)

// UserGamesStore 用户玩法存储
//
//go:generate mockgen -destination=mocks/user_games.go -package=mocks golang.52tt.com/services/aigc/aigc-game/internal/store UserGamesStore
type UserGamesStore interface {
	Save(ctx context.Context, game *entity.Game) error
	Remove(ctx context.Context, id ...string) error

	Get(ctx context.Context, id string) (*entity.Game, error)
	BatchGet(ctx context.Context, idList []string) (entity.GameMap, error)
	FindByUid(ctx context.Context, uid uint32) (entity.GameList, error)

	Last(ctx context.Context, roleId, topicId uint32, state pb.GameState, expose pb.GameExpose, descMinLen, limit uint32) (entity.GameList, error)

	CountByUid(ctx context.Context, uid uint32) (uint32, error)
}

// PlayGamesStore 用户玩游戏记录存储
//
//go:generate mockgen -destination=mocks/play_games.go -package=mocks golang.52tt.com/services/aigc/aigc-game/internal/store PlayGamesStore
type PlayGamesStore interface {
	Save(ctx context.Context, playGame *entity.PlayGame) error

	Top(ctx context.Context, roleId, topicId uint32, lastTime time.Time, source pb.GameSource, limit uint32) (entity.TopPlayGameList, error)
}
