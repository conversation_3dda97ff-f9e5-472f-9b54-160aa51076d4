package mongo

import (
	"context"
	"testing"
	"time"

	"github.com/gookit/goutil/timex"
	"go.mongodb.org/mongo-driver/mongo"
	pb "golang.52tt.com/protocol/services/aigc/aigc-game"
	"golang.52tt.com/services/aigc/aigc-game/internal/entity"
)

func Test_playGamesStore_Save(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		playGames *mongo.Collection
	}
	type args struct {
		ctx      context.Context
		playGame *entity.PlayGame
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				playGames: utPlayGamesCol,
			},
			args: args{
				ctx: ctx,
				playGame: &entity.PlayGame{
					Uid:      1,
					GameId:   "1",
					TopicId:  1,
					Source:   pb.GameSource_GAME_SOURCE_OFFICIAL,
					RoleId:   1,
					LastTime: time.Now(),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &playGamesStore{
				playGames: tt.fields.playGames,
			}
			if err := s.Save(tt.args.ctx, tt.args.playGame); (err != nil) != tt.wantErr {
				t.Errorf("playGamesStore.Save() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_playGamesStore_Top(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		playGames *mongo.Collection
	}
	type args struct {
		ctx      context.Context
		roleId   uint32
		topicId  uint32
		lastTime time.Time
		src      pb.GameSource
		limit    uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				playGames: utPlayGamesCol,
			},
			args: args{
				ctx:      ctx,
				roleId:   1,
				topicId:  1,
				lastTime: time.Now().Add(-timex.Day),
				src:      pb.GameSource_GAME_SOURCE_UNSPECIFIED,
				limit:    10,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &playGamesStore{
				playGames: tt.fields.playGames,
			}
			got, err := s.Top(tt.args.ctx, tt.args.roleId, tt.args.topicId, tt.args.lastTime, tt.args.src, tt.args.limit)
			if (err != nil) != tt.wantErr {
				t.Errorf("playGamesStore.Top() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			for i, v := range got {
				t.Logf("playGamesStore.Top() [%d] = %v", i, v)
			}
		})
	}
}
