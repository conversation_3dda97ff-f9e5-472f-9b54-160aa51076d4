package mongo

import (
	"context"
	"log"

	"go.mongodb.org/mongo-driver/mongo"

	"golang.52tt.com/pkg/config"
	"golang.52tt.com/services/aigc/aigc-game/internal/infra/db"
)

var (
	utUserGamesCol *mongo.Collection
	utPlayGamesCol *mongo.Collection
)

func init() {
	mongoConfig := &config.MongoConfig{
		Addrs:       "10.34.6.29:27017",
		Database:    "chat-bot",
		MaxPoolSize: 1,
		UserName:    "chat_bot_rw",
		Password:    "npbW*qSG1fzq6n4",
	}

	utDB, err := db.NewMongoDB(context.Background(), mongoConfig)
	if err != nil {
		log.Fatalf("NewMongoDB config(%+v) err: %v", mongoConfig, err)
	}

	utUserGamesCol = utDB.Database().Collection("interactive_games")
	utPlayGamesCol = utDB.Database().Collection("play_games")
}
