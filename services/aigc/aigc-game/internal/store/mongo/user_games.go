package mongo

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.ttyuyin.com/tyr/x/log"

	pb "golang.52tt.com/protocol/services/aigc/aigc-game"
	"golang.52tt.com/services/aigc/aigc-game/internal/entity"
	"golang.52tt.com/services/aigc/aigc-game/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-game/internal/store"
)

type userGamesStore struct {
	userGames *mongo.Collection
}

func NewUserGameStore(ctx context.Context, db *db.MongoDB) (store.UserGamesStore, error) {
	st := &userGamesStore{
		userGames: db.Database().Collection("interactive_games"),
	}

	_, err := st.userGames.Indexes().CreateMany(ctx, (&userGame{}).indexes())
	if err != nil {
		log.ErrorWithCtx(ctx, "NewUserGamesStore collection(interactive_games) CreateMany err: %v", err)
		return nil, err
	}

	return st, nil
}

func (s *userGamesStore) Save(ctx context.Context, game *entity.Game) error {
	model := userGameModel(game)
	_, err := s.userGames.UpdateByID(ctx, model.Id, bson.M{"$set": model}, options.Update().SetUpsert(true))
	return err
}

func (s *userGamesStore) Remove(ctx context.Context, id ...string) error {
	_, err := s.userGames.DeleteMany(ctx, bson.M{"_id": bson.M{"$in": id}})
	return err
}

func (s *userGamesStore) Get(ctx context.Context, id string) (*entity.Game, error) {
	result := s.userGames.FindOne(ctx, bson.M{"_id": id})
	if err := result.Err(); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}

		return nil, err
	}

	var model userGame
	if err := result.Decode(&model); err != nil {
		return nil, err
	}

	return userGameEntity(&model), nil
}

func (s *userGamesStore) BatchGet(ctx context.Context, idList []string) (entity.GameMap, error) {
	cursor, err := s.userGames.Find(ctx, bson.M{"_id": bson.M{"$in": idList}})
	if err != nil {
		return nil, err
	}

	var models []*userGame
	if err := cursor.All(ctx, &models); err != nil {
		return nil, err
	}

	result := make(entity.GameMap, len(models))
	for _, model := range models {
		result[model.Id] = userGameEntity(model)
	}

	return result, nil
}

func (s *userGamesStore) FindByUid(ctx context.Context, uid uint32) (entity.GameList, error) {
	filter := bson.M{"uid": uid}
	cursor, err := s.userGames.Find(ctx, filter, options.Find().SetSort(bson.M{"updated_at": -1}))
	if err != nil {
		return nil, err
	}

	var models []*userGame
	if err := cursor.All(ctx, &models); err != nil {
		return nil, err
	}

	list := make(entity.GameList, 0, len(models))
	for _, model := range models {
		list = append(list, userGameEntity(model))
	}

	return list, err
}

func (s *userGamesStore) Last(ctx context.Context, roleId, topicId uint32, state pb.GameState, expose pb.GameExpose, descMinLen, limit uint32) (entity.GameList, error) {
	filter := bson.M{
		"role_id": roleId,
	}

	if topicId > 0 {
		filter["topic_id"] = topicId
	}
	if state > 0 {
		filter["state"] = uint32(state)
	}
	if expose > 0 {
		filter["exposure"] = uint32(expose)
	}
	if descMinLen > 0 {
		filter["$expr"] = bson.M{"$gte": bson.A{bson.M{"$strLenCP": "$desc"}, descMinLen}}
	}

	cursor, err := s.userGames.Find(
		ctx,
		filter,
		options.Find().
			SetSort(bson.D{{Key: "created_at", Value: -1}}).
			SetLimit(int64(limit)),
	)
	if err != nil {
		return nil, err
	}

	var models []*userGame
	if err := cursor.All(ctx, &models); err != nil {
		return nil, err
	}

	list := make(entity.GameList, 0, len(models))
	for _, model := range models {
		list = append(list, userGameEntity(model))
	}

	return list, nil
}

func (s *userGamesStore) CountByUid(ctx context.Context, uid uint32) (uint32, error) {
	filter := bson.M{"uid": uid}
	count, err := s.userGames.CountDocuments(ctx, filter)
	return uint32(count), err
}

type userGame struct {
	Id        string    `bson:"_id"`
	CreatedAt time.Time `bson:"created_at"`
	UpdatedAt time.Time `bson:"updated_at"`

	Uid     uint32 `bson:"uid"`
	RoleId  uint32 `bson:"role_id"`
	TopicId uint32 `bson:"topic_id"`

	State    uint32 `bson:"state"`
	Source   uint32 `bson:"source"`
	Exposure uint32 `bson:"exposure"`

	Desc     string `bson:"desc"`
	Title    string `bson:"title"`
	Prologue string `bson:"prologue"`
}

func (g *userGame) indexes() []mongo.IndexModel {
	return []mongo.IndexModel{
		{
			// 用于查询用户创建的玩法
			Keys: bson.D{
				{Key: "uid", Value: 1},
				{Key: "updated_at", Value: -1},
			},
		},
		{
			// 用于查询指定角色下的玩法
			Keys: bson.D{
				{Key: "role_id", Value: 1},
				{Key: "state", Value: 1},
				{Key: "created_at", Value: -1},
			},
		},
		{
			// 用于查询指定角色的指定主题下的玩法
			Keys: bson.D{
				{Key: "role_id", Value: 1},
				{Key: "topic_id", Value: 1},
				{Key: "state", Value: 1},
				{Key: "created_at", Value: -1},
			},
		},
	}
}

func userGameModel(game *entity.Game) *userGame {
	g := &userGame{
		Id: game.ID,

		CreatedAt: game.CreatedAt,
		UpdatedAt: game.UpdatedAt,

		Uid:     game.Uid,
		RoleId:  game.RoleId,
		TopicId: game.TopicId,

		State:    uint32(game.State),
		Source:   uint32(game.Source),
		Exposure: uint32(game.Expose),

		Desc:     game.Desc,
		Title:    game.Title,
		Prologue: game.Prologue,
	}

	return g
}

func userGameEntity(game *userGame) *entity.Game {
	g := &entity.Game{
		ID: game.Id,

		CreatedAt: game.CreatedAt,
		UpdatedAt: game.UpdatedAt,

		Uid:     game.Uid,
		RoleId:  game.RoleId,
		TopicId: game.TopicId,

		State:  pb.GameState(game.State),
		Source: pb.GameSource(game.Source),
		Expose: pb.GameExpose(game.Exposure),

		Title:    game.Title,
		Subtitle: game.Desc,
		Desc:     game.Desc,
		Prompt:   game.Desc,
		Prologue: game.Prologue,
	}

	return g
}
