package mongo

import (
	"context"
	"testing"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	pb "golang.52tt.com/protocol/services/aigc/aigc-game"
	"golang.52tt.com/services/aigc/aigc-game/internal/entity"
)

func Test_userGamesStore_Save(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	now := time.Now()

	type fields struct {
		userGames *mongo.Collection
	}
	type args struct {
		ctx  context.Context
		game *entity.Game
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				userGames: utUserGamesCol,
			},
			args: args{
				ctx: ctx,
				game: &entity.Game{
					ID:        "685a4a996ef257c989c314f2",
					CreatedAt: now,
					UpdatedAt: now,
					Uid:       1,
					RoleId:    1,
					TopicId:   1,
					State:     pb.GameState_GAME_STATE_PRIVATE,
					Source:    pb.GameSource_GAME_SOURCE_USER,
					Expose:    pb.GameExpose_GAME_EXPOSE_EXPOSED,
					Order:     1000,
					Title:     "title",
					Desc:      "desc",
					Prologue:  "prologue",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &userGamesStore{
				userGames: tt.fields.userGames,
			}
			if err := s.Save(tt.args.ctx, tt.args.game); (err != nil) != tt.wantErr {
				t.Errorf("userGamesStore.Save() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_userGamesStore_Get(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		userGames *mongo.Collection
	}
	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				userGames: utUserGamesCol,
			},
			args: args{
				ctx: ctx,
				id:  "685a4a996ef257c989c314f2",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &userGamesStore{
				userGames: tt.fields.userGames,
			}
			got, err := s.Get(tt.args.ctx, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("userGamesStore.Get() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("userGamesStore.Get() = %v", got)
		})
	}
}

func Test_userGamesStore_BatchGet(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		userGames *mongo.Collection
	}
	type args struct {
		ctx    context.Context
		idList []string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				userGames: utUserGamesCol,
			},
			args: args{
				ctx: ctx,
				idList: []string{
					"685a4a996ef257c989c314f2",
					"6855263375b55930b19de4cd",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &userGamesStore{
				userGames: tt.fields.userGames,
			}
			got, err := s.BatchGet(tt.args.ctx, tt.args.idList)
			if (err != nil) != tt.wantErr {
				t.Errorf("userGamesStore.BatchGet() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			for k, v := range got {
				t.Logf("userGamesStore.BatchGet() [%s] = %+v", k, v)
			}
		})
	}
}

func Test_userGamesStore_FindByUid(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		userGames *mongo.Collection
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				userGames: utUserGamesCol,
			},
			args: args{
				ctx: ctx,
				uid: 2645263,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &userGamesStore{
				userGames: tt.fields.userGames,
			}
			got, err := s.FindByUid(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("userGamesStore.FindByUid() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			for i, game := range got {
				t.Logf("userGamesStore.FindByUid() [%d] = %+v", i, game)
			}
		})
	}
}

func Test_userGamesStore_Last(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		userGames *mongo.Collection
	}
	type args struct {
		ctx        context.Context
		roleId     uint32
		topicId    uint32
		state      pb.GameState
		expose     pb.GameExpose
		descMinLen uint32
		limit      uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				userGames: utUserGamesCol,
			},
			args: args{
				ctx:        ctx,
				roleId:     1,
				topicId:    0,
				state:      0,
				expose:     0,
				descMinLen: 20,
				limit:      10,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &userGamesStore{
				userGames: tt.fields.userGames,
			}
			got, err := s.Last(tt.args.ctx, tt.args.roleId, tt.args.topicId, tt.args.state, tt.args.expose, tt.args.descMinLen, tt.args.limit)
			if (err != nil) != tt.wantErr {
				t.Errorf("userGamesStore.Last() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			for i, game := range got {
				t.Logf("userGamesStore.Last() [%d] = %+v", i, game)
			}
		})
	}
}

func Test_userGamesStore_CountByUid(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		userGames *mongo.Collection
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				userGames: utUserGamesCol,
			},
			args: args{
				ctx: ctx,
				uid: 1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &userGamesStore{
				userGames: tt.fields.userGames,
			}
			got, err := s.CountByUid(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("userGamesStore.CountByUid() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("userGamesStore.CountByUid() = %v", got)
		})
	}
}

func Test_userGamesStore_Remove(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		userGames *mongo.Collection
	}
	type args struct {
		ctx context.Context
		id  []string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				userGames: utUserGamesCol,
			},
			args: args{
				ctx: ctx,
				id:  []string{"685a4a996ef257c989c314f2"},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &userGamesStore{
				userGames: tt.fields.userGames,
			}
			if err := s.Remove(tt.args.ctx, tt.args.id...); (err != nil) != tt.wantErr {
				t.Errorf("userGamesStore.Remove() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
