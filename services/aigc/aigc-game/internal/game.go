package internal

import (
	"context"
	"time"

	"gitlab.ttyuyin.com/tyr/x/log"

	"golang.52tt.com/kaihei-pkg/aigc/msg/builder"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/aigc/aigc-game"
	"golang.52tt.com/services/aigc/aigc-game/internal/entity"
)

// EnterGame 进入玩法
func (s *Server) EnterGame(ctx context.Context, req *pb.EnterGameRequest) (*pb.EnterGameResponse, error) {
	resp := new(pb.EnterGameResponse)

	if req.GetUid() == 0 || req.GetPartnerId() == 0 || req.GetGame() == nil {
		log.ErrorWithCtx(ctx, "EnterGame invalid req: %+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "缺少必填参数")
	}

	reqGame := req.GetGame()
	playGame, err := entity.NewPlayGame(req.GetUid(), reqGame.GetRoleId(), reqGame.GetTopicId(), reqGame.GetId(), reqGame.GetSource())
	if err != nil {
		log.ErrorWithCtx(ctx, "EnterGame NewPlayGame err: %v", err)
		return resp, err
	}

	ctxId, err := s.gameMgr.EnterGame(ctx, playGame)
	if err != nil {
		log.ErrorWithCtx(ctx, "EnterGame EnterGame game(%+v) err: %v", playGame, err)
		return resp, err
	}

	if req.GetCtxId() == "" && (reqGame.GetGreeting() != "" || reqGame.GetSceneDesc() != "") {
		// 延时1s，为了确保用户进入了玩法后才发送出来
		time.AfterFunc(time.Second, func() {
			delayCtx, delayCancel := context.WithTimeout(context.Background(), time.Second)
			defer delayCancel()

			// 发送玩法前景描述
			if reqGame.GetSceneDesc() != "" {
				_ = s.gameMgr.SendRoleGameSystemMsg(delayCtx, req.GetPartnerId(), req.GetUid(), builder.BuildGameSceneMsg(ctxId, reqGame.GetId(), reqGame.GetSceneDesc()))
			}
			if reqGame.GetGreeting() != "" {
				// 发送玩法开场白
				_ = s.gameMgr.SendRoleGameSystemMsg(delayCtx, req.GetPartnerId(), req.GetUid(), builder.BuildGameGreetingMsg(ctxId, reqGame.GetId(), reqGame.GetGreeting()))
			}
		})

		resp.CtxId = ctxId
	} else {
		resp.CtxId = req.GetCtxId()
	}

	log.InfoWithCtx(ctx, "EnterGame req(%+v) resp: %+v", req, resp)
	return resp, nil
}

// GetOfficialGameList 官方玩法列表
func (s *Server) GetOfficialGameList(ctx context.Context, req *pb.GetOfficialGameListRequest) (*pb.GetOfficialGameListResponse, error) {
	resp := new(pb.GetOfficialGameListResponse)

	var list entity.GameList
	if req.GetRoleId() == 0 {
		var err error
		if list, err = s.gameMgr.GetOfficialGameList(ctx); err != nil {
			log.ErrorWithCtx(ctx, "GetOfficialGameList GetOfficialGameList err: %v", req.GetRoleId(), err)
			return resp, err
		}
	} else {
		var err error
		if list, err = s.gameMgr.GetRoleOfficialGameList(ctx, req.GetRoleId()); err != nil {
			log.ErrorWithCtx(ctx, "GetOfficialGameList GetRoleOfficialGameList roleId(%d) err: %v", req.GetRoleId(), err)
			return resp, err
		}
	}
	for _, v := range list {
		resp.List = append(resp.List, gamePb(v))
	}

	return resp, nil
}

// GetTopGameList Top玩法列表
func (s *Server) GetTopGameList(ctx context.Context, req *pb.GetTopGameListRequest) (*pb.GetTopGameListResponse, error) {
	resp := new(pb.GetTopGameListResponse)

	list, err := s.gameMgr.GetRoleTopGameList(ctx, req.GetRoleId(), req.GetTopicId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTopGameList GetRoleTopGameList roleId(%d) topicId(%d) err: %v", req.GetRoleId(), req.GetTopicId(), err)
		return resp, err
	}

	for _, v := range list {
		resp.List = append(resp.List, gamePb(v))
	}

	return resp, nil
}

// GetGameInfo 玩法详情
func (s *Server) GetGameInfo(ctx context.Context, req *pb.GetGameInfoRequest) (*pb.GetGameInfoResponse, error) {
	resp := new(pb.GetGameInfoResponse)

	game, err := s.gameMgr.GetGame(ctx, req.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameInfo GetGame id(%s) err: %v", req.GetId(), err)
		return resp, err
	}

	resp.Info = gamePb(game)
	return resp, nil
}

func gamePb(en *entity.Game) *pb.Game {
	game := &pb.Game{
		Id: en.ID,

		TopicId: en.TopicId,

		Source: en.Source,

		Title:    en.Title,
		Subtitle: en.Subtitle,
		Desc:     en.Desc,
		Prompt:   en.Prompt,
		Prologue: en.Prologue,
		Bg:       en.Bg,
		Bgm:      en.Bgm,
		Icon:     en.Icon,
	}

	return game
}
