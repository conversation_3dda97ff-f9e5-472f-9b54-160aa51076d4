package internal

import (
	"context"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	pb "golang.52tt.com/protocol/services/aigc/aigc-game"
	"golang.52tt.com/services/aigc/aigc-game/internal/entity"
	"golang.52tt.com/services/aigc/aigc-game/internal/mgr"
	mock_mgr "golang.52tt.com/services/aigc/aigc-game/internal/mgr/mocks"
)

func TestServer_GetGameInfo(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		gameMgr = mock_mgr.NewMockGameManager(ctrl)
	)

	type fields struct {
		gameMgr mgr.GameManager
	}
	type args struct {
		ctx context.Context
		req *pb.GetGameInfoRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		mock    func()
		want    *pb.GetGameInfoResponse
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				gameMgr: gameMgr,
			},
			args: args{
				ctx: ctx,
				req: &pb.GetGameInfoRequest{Id: "1"},
			},
			mock: func() {
				gameMgr.EXPECT().GetGame(gomock.Any(), "1").Return(
					&entity.Game{
						ID: "1",
					},
					nil,
				)
			},
			want: &pb.GetGameInfoResponse{
				Info: &pb.Game{
					Id: "1",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt.mock()
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				gameMgr: tt.fields.gameMgr,
			}
			got, err := s.GetGameInfo(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("Server.GetGameInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Server.GetGameInfo() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestServer_GetOfficialGameList(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		gameMgr = mock_mgr.NewMockGameManager(ctrl)
	)

	type fields struct {
		gameMgr mgr.GameManager
	}
	type args struct {
		ctx context.Context
		req *pb.GetOfficialGameListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		mock    func()
		want    *pb.GetOfficialGameListResponse
		wantErr bool
	}{
		{
			name: "roleId == 0",
			fields: fields{
				gameMgr: gameMgr,
			},
			args: args{
				ctx: ctx,
				req: &pb.GetOfficialGameListRequest{},
			},
			mock: func() {
				gameMgr.EXPECT().GetOfficialGameList(gomock.Any()).Return(
					entity.GameList{
						{
							ID:     "1",
							RoleId: 1,
						},
						{
							ID:     "2",
							RoleId: 2,
						},
					},
					nil,
				)
			},
			want: &pb.GetOfficialGameListResponse{
				List: []*pb.Game{
					{
						Id: "1",
					},
					{
						Id: "2",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "roleId != 0",
			fields: fields{
				gameMgr: gameMgr,
			},
			args: args{
				ctx: ctx,
				req: &pb.GetOfficialGameListRequest{
					RoleId: 1,
				},
			},
			mock: func() {
				gameMgr.EXPECT().GetRoleOfficialGameList(gomock.Any(), uint32(1)).Return(
					entity.GameList{
						{
							ID:     "1",
							RoleId: 1,
						},
					},
					nil,
				)
			},
			want: &pb.GetOfficialGameListResponse{
				List: []*pb.Game{
					{
						Id: "1",
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt.mock()
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				gameMgr: tt.fields.gameMgr,
			}
			got, err := s.GetOfficialGameList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("Server.GetOfficialGameList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Server.GetOfficialGameList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestServer_GetTopGameList(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		gameMgr = mock_mgr.NewMockGameManager(ctrl)
	)

	type fields struct {
		gameMgr mgr.GameManager
	}
	type args struct {
		ctx context.Context
		req *pb.GetTopGameListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		mock    func()
		want    *pb.GetTopGameListResponse
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				gameMgr: gameMgr,
			},
			args: args{
				ctx: ctx,
				req: &pb.GetTopGameListRequest{
					RoleId:  1,
					TopicId: 0,
				},
			},
			mock: func() {
				gameMgr.EXPECT().GetRoleTopGameList(gomock.Any(), uint32(1), uint32(0)).Return(
					entity.GameList{
						{
							ID:     "1",
							RoleId: 1,
						},
						{
							ID:     "2",
							RoleId: 2,
						},
					},
					nil,
				)
			},
			want: &pb.GetTopGameListResponse{
				List: []*pb.Game{
					{
						Id: "1",
					},
					{
						Id: "2",
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt.mock()
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				gameMgr: tt.fields.gameMgr,
			}
			got, err := s.GetTopGameList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("Server.GetTopGameList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Server.GetTopGameList() = %v, want %v", got, tt.want)
			}
		})
	}
}
