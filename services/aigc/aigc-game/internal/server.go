package internal

import (
	"context"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	middleware_event "gitlab.ttyuyin.com/tt-infra/middleware/event"

	"golang.52tt.com/pkg/config"
	"golang.52tt.com/protocol/services/demo/echo"
	bitable_config "golang.52tt.com/services/aigc/aigc-game/internal/config/bitable"
	"golang.52tt.com/services/aigc/aigc-game/internal/config/ttconfig"
	"golang.52tt.com/services/aigc/aigc-game/internal/event/eventlink"
	"golang.52tt.com/services/aigc/aigc-game/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-game/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-game/internal/mgr/game"
	"golang.52tt.com/services/aigc/aigc-game/internal/mgr/topic"
	"golang.52tt.com/services/aigc/aigc-game/internal/store/mongo"
)

type StartConfig struct {
	// from config file
	Mongo     *config.MongoConfig       `json:"mongo"`
	EventLink *middleware_event.Options `json:"event_link"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	mongoDb, err := db.NewMongoDB(ctx, cfg.Mongo)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewMongoDB cfg(%+v) err: %v", cfg.Mongo, err)
		return nil, err
	}

	bitableConfiger, err := bitable_config.New(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewBitableConfiger err: %v", err)
		return nil, err
	}

	ttConfiger, err := ttconfig.New()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewTTConfiger err: %v", err)
		return nil, err
	}

	userGamesStore, err := mongo.NewUserGameStore(ctx, mongoDb)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewUserGameStore err: %v", err)
		return nil, err
	}

	playGamesStore, err := mongo.NewPlayGamesStore(ctx, mongoDb)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewPlayGamesStore err: %v", err)
		return nil, err
	}

	eventBus, err := eventlink.New(ctx, cfg.EventLink)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewEventBus err: %v", err)
		return nil, err
	}

	gameMgr := game.New(bitableConfiger, ttConfiger, userGamesStore, playGamesStore, eventBus)
	topicMgr := topic.New(bitableConfiger)

	s := &Server{
		mongoDb: mongoDb,

		gameMgr:  gameMgr,
		topicMgr: topicMgr,
	}

	return s, nil
}

type Server struct {
	mongoDb *db.MongoDB

	gameMgr  mgr.GameManager
	topicMgr mgr.TopicManager
}

func (s *Server) ShutDown() {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if s.mongoDb != nil {
		_ = s.mongoDb.Close(ctx)
	}
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}
