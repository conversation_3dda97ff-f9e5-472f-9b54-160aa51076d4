package internal

import (
	"context"
	"encoding/json"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
	"testing"
)

var svrClient aigc_account.AigcAccountClient

func init() {
	logicClient := "aigc-account.52tt.local"
	// test
	//socialCC, _ := grpc.NewClient("*********:80", grpc.WithAuthority(logicClient),
	//	grpc.WithTransportCredentials(insecure.NewCredentials()))
	// staging
	socialCC, _ := grpc.NewClient("*********:80", grpc.WithAuthority(logicClient),
		grpc.WithTransportCredentials(insecure.NewCredentials()))

	svrClient = aigc_account.NewAigcAccountClient(socialCC)

}

func TestAddAiPost(t *testing.T) {
	md := metadata.Pairs("x-qw-traffic-mark", "tt-qa", "req_uid", "2630911")
	ctx := metadata.NewOutgoingContext(context.Background(), md)

	resp, err := svrClient.AddAiPost(ctx, &aigc_account.AddAiPostRequest{
		Uid:    2630911,
		PostId: "687e10edb0d1cf189083b558",
	})
	if err != nil {
		t.Errorf("AddAiPost() error = %v", err)
		return
	}

	respStr, _ := json.Marshal(resp)
	t.Logf("AddAiPost resp: %s", string(respStr))
}

func TestGetAiPost(t *testing.T) {
	md := metadata.Pairs("x-qw-traffic-mark", "tt-qa", "req_uid", "2630911")
	ctx := metadata.NewOutgoingContext(context.Background(), md)

	resp, err := svrClient.GetAiPost(ctx, &aigc_account.GetAiPostRequest{
		Page:  1,
		Size:  2,
		AiUid: []uint32{2630911},
	})
	if err != nil {
		t.Errorf("GetAiPost() error = %v", err)
		return
	}

	respStr, _ := json.Marshal(resp)
	t.Logf("GetAiPost resp: %s", string(respStr))
}

func TestAddUserAiChatRecord(t *testing.T) {
	md := metadata.Pairs("x-qw-traffic-mark", "tt-qa", "req_uid", "2630911")
	ctx := metadata.NewOutgoingContext(context.Background(), md)

	resp, err := svrClient.AddUserAiChatRecord(ctx, &aigc_account.AddUserAiChatRecordRequest{
		FromUid: 2630911,
		ToUid:   900000,
	})
	if err != nil {
		t.Errorf("AddUserAiChatRecord() error = %v", err)
		return
	}

	respStr, _ := json.Marshal(resp)
	t.Logf("AddUserAiChatRecord resp: %s", string(respStr))
}

func TestGetUserAiChatRecord(t *testing.T) {
	md := metadata.Pairs("x-qw-traffic-mark", "tt-qa", "req_uid", "2630911")
	ctx := metadata.NewOutgoingContext(context.Background(), md)

	resp, err := svrClient.GetUserAiChatRecord(ctx, &aigc_account.GetUserAiChatRecordRequest{
		FromUid: 2630911,
		ToUid:   900000,
	})
	if err != nil {
		t.Errorf("GetUserAiChatRecord() error = %v", err)
		return
	}

	respStr, _ := json.Marshal(resp)
	t.Logf("GetUserAiChatRecord resp: %s", string(respStr))
}

func TestAddUserAiInteractionRecord(t *testing.T) {
	md := metadata.Pairs("x-qw-traffic-mark", "tt-qa", "req_uid", "2630911")
	ctx := metadata.NewOutgoingContext(context.Background(), md)

	resp, err := svrClient.AddUserAiInteractionRecord(ctx, &aigc_account.AddUserAiInteractionRecordRequest{
		Uid:   2630911,
		AiUid: 900001,
	})
	if err != nil {
		t.Errorf("AddUserAiInteractionRecord() error = %v", err)
		return
	}

	respStr, _ := json.Marshal(resp)
	t.Logf("AddUserAiInteractionRecord resp: %s", string(respStr))
}

func TestGetUserAiInteractionRecord(t *testing.T) {
	md := metadata.Pairs("x-qw-traffic-mark", "tt-qa", "req_uid", "2630911")
	ctx := metadata.NewOutgoingContext(context.Background(), md)

	resp, err := svrClient.GetUserAiInteractionRecord(ctx, &aigc_account.GetUserAiInteractionRecordRequest{
		Uid: 2630911,
	})
	if err != nil {
		t.Errorf("GetUserAiInteractionRecord() error = %v", err)
		return
	}

	respStr, _ := json.Marshal(resp)
	t.Logf("GetUserAiInteractionRecord resp: %s", string(respStr))
}

func TestAddAiLikePost(t *testing.T) {
	md := metadata.Pairs("x-qw-traffic-mark", "tt-qa", "req_uid", "2630911")
	ctx := metadata.NewOutgoingContext(context.Background(), md)

	resp, err := svrClient.AddAiLikePost(ctx, &aigc_account.AddAiLikePostRequest{
		Uid:    2630911,
		PostId: "687e10edb0d1cf189083b540",
	})
	if err != nil {
		t.Errorf("AddAiLikePost() error = %v", err)
		return
	}

	respStr, _ := json.Marshal(resp)
	t.Logf("AddAiLikePost resp: %s", string(respStr))
}

func TestGetAiLikePostCount(t *testing.T) {
	md := metadata.Pairs("x-qw-traffic-mark", "tt-qa", "req_uid", "2630911")
	ctx := metadata.NewOutgoingContext(context.Background(), md)

	resp, err := svrClient.GetAiLikePostCount(ctx, &aigc_account.GetAiLikePostCountRequest{
		Uid: 2630911,
	})
	if err != nil {
		t.Errorf("GetAiLikePostCount() error = %v", err)
		return
	}

	respStr, _ := json.Marshal(resp)
	t.Logf("GetAiLikePostCount resp: %s", string(respStr))
}

func TestAddAiCommentPost(t *testing.T) {
	md := metadata.Pairs("x-qw-traffic-mark", "tt-qa", "req_uid", "2630911")
	ctx := metadata.NewOutgoingContext(context.Background(), md)

	resp, err := svrClient.AddAiCommentPost(ctx, &aigc_account.AddAiCommentPostRequest{
		Uid:    2630911,
		PostId: "687e10edb0d1cf189083b540",
	})
	if err != nil {
		t.Errorf("AddAiCommentPost() error = %v", err)
		return
	}

	respStr, _ := json.Marshal(resp)
	t.Logf("AddAiCommentPost resp: %s", string(respStr))
}

func TestGetAiCommentPostCount(t *testing.T) {
	md := metadata.Pairs("x-qw-traffic-mark", "tt-qa", "req_uid", "2630911")
	ctx := metadata.NewOutgoingContext(context.Background(), md)

	resp, err := svrClient.GetAiCommentPostCount(ctx, &aigc_account.GetAiCommentPostCountRequest{
		Uid: 2630911,
	})
	if err != nil {
		t.Errorf("GetAiCommentPostCount() error = %v", err)
		return
	}

	respStr, _ := json.Marshal(resp)
	t.Logf("GetAiCommentPostCount resp: %s", string(respStr))
}

func TestAddPostAiCommentRecord(t *testing.T) {
	md := metadata.Pairs("x-qw-traffic-mark", "tt-qa", "req_uid", "2630911")
	ctx := metadata.NewOutgoingContext(context.Background(), md)

	resp, err := svrClient.AddPostAiCommentRecord(ctx, &aigc_account.AddPostAiCommentRecordRequest{
		AiUid:  900000,
		PostId: "687e10edb0d1cf189083b540",
	})
	if err != nil {
		t.Errorf("AddPostAiCommentRecord() error = %v", err)
		return
	}

	respStr, _ := json.Marshal(resp)
	t.Logf("AddPostAiCommentRecord resp: %s", string(respStr))
}

func TestGetPostAiCommentCount(t *testing.T) {
	md := metadata.Pairs("x-qw-traffic-mark", "tt-qa", "req_uid", "2630911")
	ctx := metadata.NewOutgoingContext(context.Background(), md)

	resp, err := svrClient.GetPostAiCommentCount(ctx, &aigc_account.GetPostAiCommentCountRequest{
		PostId: "687e10edb0d1cf189083b540",
		AiUid:  900001,
	})
	if err != nil {
		t.Errorf("GetPostAiCommentCount() error = %v", err)
		return
	}

	respStr, _ := json.Marshal(resp)
	t.Logf("GetPostAiCommentCount resp: %s", string(respStr))
}

func TestGetAIAccountByGender(t *testing.T) {
	md := metadata.Pairs("x-qw-traffic-mark", "sk", "req_uid", "2630911")
	ctx := metadata.NewOutgoingContext(context.Background(), md)
	resp, err := svrClient.GetAIAccountByGender(ctx, &aigc_account.GetAIAccountByGenderRequest{
		Sex:   1,
		Limit: 10,
	})
	if err != nil {
		t.Errorf("GetAIAccountByGender() error = %v", err)
		return
	}
	respStr, _ := json.Marshal(resp)
	t.Logf("GetAIAccountByGender resp: %s", string(respStr))
}
