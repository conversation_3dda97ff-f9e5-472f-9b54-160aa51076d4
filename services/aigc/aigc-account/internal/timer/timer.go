package timer

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/timer"
	"golang.52tt.com/services/aigc/aigc-account/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-account/internal/mgr"
	"time"
)

const defaultFrequency = 3600 // 默认每小时执行一次

type RedisTimer struct {
	timerD     *timer.Timer
	accountMrg mgr.AIAccountManager
	frequency  uint32
}

func InitTimer(ctx context.Context, redisCli *db.RedisDB, accountMrg mgr.AIAccountManager, frequency uint32) (*RedisTimer, error) {
	svrTimer, err := timer.NewTimerD(ctx, "muse-social-community", timer.WithV8RedisCmdable(redisCli))
	if err != nil {
		return nil, err
	}

	if frequency == 0 {
		frequency = defaultFrequency
	}

	return &RedisTimer{
		timerD:     svrTimer,
		accountMrg: accountMrg,
		frequency:  frequency,
	}, nil
}

func (t *RedisTimer) Start() {

	// 同步ai账号性别
	t.timerD.AddIntervalTask("SyncAiAccountGender", time.Duration(t.frequency)*time.Second, timer.BuildFromLambda(func(ctx context.Context) {
		err := t.accountMrg.SyncAiAccountGender(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "SyncAiAccountGender failed: %v", err)
		} else {
			log.InfoWithCtx(ctx, "SyncAiAccountGender completed successfully")
		}
	}))

	t.timerD.Start()
}

func (t *RedisTimer) Stop() {
	t.timerD.Stop()
}
