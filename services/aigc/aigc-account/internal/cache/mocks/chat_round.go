// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-account/internal/cache (interfaces: ChatRoundCache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockChatRoundCache is a mock of ChatRoundCache interface.
type MockChatRoundCache struct {
	ctrl     *gomock.Controller
	recorder *MockChatRoundCacheMockRecorder
}

// MockChatRoundCacheMockRecorder is the mock recorder for MockChatRoundCache.
type MockChatRoundCacheMockRecorder struct {
	mock *MockChatRoundCache
}

// NewMockChatRoundCache creates a new mock instance.
func NewMockChatRoundCache(ctrl *gomock.Controller) *MockChatRoundCache {
	mock := &MockChatRoundCache{ctrl: ctrl}
	mock.recorder = &MockChatRoundCacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChatRoundCache) EXPECT() *MockChatRoundCacheMockRecorder {
	return m.recorder
}

// GetSpecialChatRound mocks base method.
func (m *MockChatRoundCache) GetSpecialChatRound(arg0 context.Context, arg1, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSpecialChatRound", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSpecialChatRound indicates an expected call of GetSpecialChatRound.
func (mr *MockChatRoundCacheMockRecorder) GetSpecialChatRound(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSpecialChatRound", reflect.TypeOf((*MockChatRoundCache)(nil).GetSpecialChatRound), arg0, arg1, arg2)
}

// GetTotalChatRound mocks base method.
func (m *MockChatRoundCache) GetTotalChatRound(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTotalChatRound", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTotalChatRound indicates an expected call of GetTotalChatRound.
func (mr *MockChatRoundCacheMockRecorder) GetTotalChatRound(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTotalChatRound", reflect.TypeOf((*MockChatRoundCache)(nil).GetTotalChatRound), arg0, arg1)
}

// IncrChatRound mocks base method.
func (m *MockChatRoundCache) IncrChatRound(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrChatRound", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrChatRound indicates an expected call of IncrChatRound.
func (mr *MockChatRoundCacheMockRecorder) IncrChatRound(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrChatRound", reflect.TypeOf((*MockChatRoundCache)(nil).IncrChatRound), arg0, arg1, arg2)
}
