// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-account/internal/cache (interfaces: ChatAiCache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockChatAiCache is a mock of ChatAiCache interface.
type MockChatAiCache struct {
	ctrl     *gomock.Controller
	recorder *MockChatAiCacheMockRecorder
}

// MockChatAiCacheMockRecorder is the mock recorder for MockChatAiCache.
type MockChatAiCacheMockRecorder struct {
	mock *MockChatAiCache
}

// NewMockChatAiCache creates a new mock instance.
func NewMockChatAiCache(ctrl *gomock.Controller) *MockChatAiCache {
	mock := &MockChatAiCache{ctrl: ctrl}
	mock.recorder = &MockChatAiCacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChatAiCache) EXPECT() *MockChatAiCacheMockRecorder {
	return m.recorder
}

// AddCommentPost mocks base method.
func (m *MockChatAiCache) AddCommentPost(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCommentPost", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddCommentPost indicates an expected call of AddCommentPost.
func (mr *MockChatAiCacheMockRecorder) AddCommentPost(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCommentPost", reflect.TypeOf((*MockChatAiCache)(nil).AddCommentPost), arg0, arg1, arg2)
}

// AddInteraction mocks base method.
func (m *MockChatAiCache) AddInteraction(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddInteraction", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddInteraction indicates an expected call of AddInteraction.
func (mr *MockChatAiCacheMockRecorder) AddInteraction(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddInteraction", reflect.TypeOf((*MockChatAiCache)(nil).AddInteraction), arg0, arg1, arg2)
}

// AddLikePost mocks base method.
func (m *MockChatAiCache) AddLikePost(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddLikePost", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddLikePost indicates an expected call of AddLikePost.
func (mr *MockChatAiCacheMockRecorder) AddLikePost(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddLikePost", reflect.TypeOf((*MockChatAiCache)(nil).AddLikePost), arg0, arg1, arg2)
}

// GetCommentPostCount mocks base method.
func (m *MockChatAiCache) GetCommentPostCount(arg0 context.Context, arg1 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCommentPostCount", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCommentPostCount indicates an expected call of GetCommentPostCount.
func (mr *MockChatAiCacheMockRecorder) GetCommentPostCount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommentPostCount", reflect.TypeOf((*MockChatAiCache)(nil).GetCommentPostCount), arg0, arg1)
}

// GetImChatRecord mocks base method.
func (m *MockChatAiCache) GetImChatRecord(arg0 context.Context, arg1, arg2 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetImChatRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetImChatRecord indicates an expected call of GetImChatRecord.
func (mr *MockChatAiCacheMockRecorder) GetImChatRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetImChatRecord", reflect.TypeOf((*MockChatAiCache)(nil).GetImChatRecord), arg0, arg1, arg2)
}

// GetInteractionAiList mocks base method.
func (m *MockChatAiCache) GetInteractionAiList(arg0 context.Context, arg1 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInteractionAiList", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInteractionAiList indicates an expected call of GetInteractionAiList.
func (mr *MockChatAiCacheMockRecorder) GetInteractionAiList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInteractionAiList", reflect.TypeOf((*MockChatAiCache)(nil).GetInteractionAiList), arg0, arg1)
}

// GetLikePostCount mocks base method.
func (m *MockChatAiCache) GetLikePostCount(arg0 context.Context, arg1 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLikePostCount", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLikePostCount indicates an expected call of GetLikePostCount.
func (mr *MockChatAiCacheMockRecorder) GetLikePostCount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLikePostCount", reflect.TypeOf((*MockChatAiCache)(nil).GetLikePostCount), arg0, arg1)
}

// GetPostAiCommentCount mocks base method.
func (m *MockChatAiCache) GetPostAiCommentCount(arg0 context.Context, arg1 string, arg2 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPostAiCommentCount", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostAiCommentCount indicates an expected call of GetPostAiCommentCount.
func (mr *MockChatAiCacheMockRecorder) GetPostAiCommentCount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostAiCommentCount", reflect.TypeOf((*MockChatAiCache)(nil).GetPostAiCommentCount), arg0, arg1, arg2)
}

// GetPostCommentAiList mocks base method.
func (m *MockChatAiCache) GetPostCommentAiList(arg0 context.Context, arg1 string) (map[uint32]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPostCommentAiList", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostCommentAiList indicates an expected call of GetPostCommentAiList.
func (mr *MockChatAiCacheMockRecorder) GetPostCommentAiList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostCommentAiList", reflect.TypeOf((*MockChatAiCache)(nil).GetPostCommentAiList), arg0, arg1)
}

// IncrPostAiCommentCount mocks base method.
func (m *MockChatAiCache) IncrPostAiCommentCount(arg0 context.Context, arg1 string, arg2 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrPostAiCommentCount", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrPostAiCommentCount indicates an expected call of IncrPostAiCommentCount.
func (mr *MockChatAiCacheMockRecorder) IncrPostAiCommentCount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrPostAiCommentCount", reflect.TypeOf((*MockChatAiCache)(nil).IncrPostAiCommentCount), arg0, arg1, arg2)
}

// RecordImChat mocks base method.
func (m *MockChatAiCache) RecordImChat(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordImChat", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordImChat indicates an expected call of RecordImChat.
func (mr *MockChatAiCacheMockRecorder) RecordImChat(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordImChat", reflect.TypeOf((*MockChatAiCache)(nil).RecordImChat), arg0, arg1, arg2)
}
