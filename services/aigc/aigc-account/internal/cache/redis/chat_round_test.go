package redis

import (
	"context"
	"testing"
	"time"

	"golang.52tt.com/services/aigc/aigc-account/internal/infra/db"
)

func Test_chatRoundRedisCache_IncrChatRound(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx   context.Context
		uid   uint32
		aiUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx:   ctx,
				uid:   12345,
				aiUid: 67890,
			},
			wantErr: false,
		},
		{
			name: "different_user",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx:   ctx,
				uid:   54321,
				aiUid: 98765,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &chatRoundRedisCache{
				cmder: tt.fields.cmder,
			}
			if err := c.IncrChatRound(tt.args.ctx, tt.args.uid, tt.args.aiUid); (err != nil) != tt.wantErr {
				t.Errorf("chatRoundRedisCache.IncrChatRound() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_chatRoundRedisCache_GetSpecialChatRound(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx   context.Context
		uid   uint32
		aiUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx:   ctx,
				uid:   12345,
				aiUid: 67890,
			},
			want:    1, // Expected 0 for non-existent key
			wantErr: false,
		},
		{
			name: "different_ai",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx:   ctx,
				uid:   12345,
				aiUid: 11111,
			},
			want:    0, // Expected 0 for non-existent key
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &chatRoundRedisCache{
				cmder: tt.fields.cmder,
			}
			got, err := c.GetSpecialChatRound(tt.args.ctx, tt.args.uid, tt.args.aiUid)
			if (err != nil) != tt.wantErr {
				t.Errorf("chatRoundRedisCache.GetSpecialChatRound() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("chatRoundRedisCache.GetSpecialChatRound() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_chatRoundRedisCache_GetTotalChatRound(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx: ctx,
				uid: 12345,
			},
			want:    1, // Expected 0 for non-existent key
			wantErr: false,
		},
		{
			name: "different_user",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx: ctx,
				uid: 54321,
			},
			want:    1, // Expected 0 for non-existent key
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &chatRoundRedisCache{
				cmder: tt.fields.cmder,
			}
			got, err := c.GetTotalChatRound(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("chatRoundRedisCache.GetTotalChatRound() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("chatRoundRedisCache.GetTotalChatRound() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_chatRoundRedisCache_keyOfTotalRound(t *testing.T) {
	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		uid uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				uid: 12345,
			},
			want: "total_round_12345_" + time.Now().Format("20060102"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &chatRoundRedisCache{
				cmder: tt.fields.cmder,
			}
			if got := c.keyOfTotalRound(tt.args.uid); got != tt.want {
				t.Errorf("chatRoundRedisCache.keyOfTotalRound() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_chatRoundRedisCache_keyOfSpecifiedRound(t *testing.T) {
	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		uid uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				uid: 12345,
			},
			want: "specified_round_12345_" + time.Now().Format("20060102"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &chatRoundRedisCache{
				cmder: tt.fields.cmder,
			}
			if got := c.keyOfSpecifiedRound(tt.args.uid); got != tt.want {
				t.Errorf("chatRoundRedisCache.keyOfSpecifiedRound() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewChatRoundRedisCache(t *testing.T) {
	type args struct {
		database *db.RedisDB
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "ok",
			args: args{
				database: utDatabase,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := NewChatRoundRedisCache(tt.args.database)
			if got == nil {
				t.Errorf("NewChatRoundRedisCache() = nil, want non-nil")
			}
		})
	}
}
