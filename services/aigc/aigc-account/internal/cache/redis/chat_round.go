package redis

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/services/aigc/aigc-account/internal/cache"
	"golang.52tt.com/services/aigc/aigc-account/internal/infra/db"
	"strconv"
	"time"
)

const defaultExpireTime = 2 * 24 * time.Hour

type chatRoundRedisCache struct {
	cmder *db.RedisDB
}

func NewChatRoundRedisCache(database *db.RedisDB) cache.ChatRoundCache {
	c := &chatRoundRedisCache{
		cmder: database,
	}

	return c
}

func (c *chatRoundRedisCache) keyOfTotalRound(uid uint32) string {
	return fmt.Sprintf("total_round_%d_%s", uid, time.Now().Format("********"))
}

func (c *chatRoundRedisCache) keyOfSpecifiedRound(uid uint32) string {
	return fmt.Sprintf("specified_round_%d_%s", uid, time.Now().Format("********"))
}
func (c *chatRoundRedisCache) IncrChatRound(ctx context.Context, uid, aiUid uint32) error {
	var incrTotalRes *redis.IntCmd
	var incrOneRes *redis.IntCmd
	var expireTotalRes *redis.BoolCmd
	var expireOneRes *redis.BoolCmd
	_, err := c.cmder.Pipelined(ctx, func(pl redis.Pipeliner) error {
		incrTotalRes = pl.Incr(ctx, c.keyOfTotalRound(uid))
		incrOneRes = pl.HIncrBy(ctx, c.keyOfSpecifiedRound(uid), strconv.Itoa(int(aiUid)), 1)
		expireTotalRes = pl.Expire(ctx, c.keyOfTotalRound(uid), defaultExpireTime)
		expireOneRes = pl.Expire(ctx, c.keyOfSpecifiedRound(uid), defaultExpireTime)
		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "IncrChatRound uid:%d aiUid:%d error: %v", uid, aiUid, err)
		return err
	}
	if incrTotalRes.Err() != nil {
		log.ErrorWithCtx(ctx, "IncrChatRound uid:%d aiUid:%d incrTotalRes: %v", uid, aiUid, incrTotalRes.Err())
		return incrTotalRes.Err()
	}
	if incrOneRes.Err() != nil {
		log.ErrorWithCtx(ctx, "IncrChatRound uid:%d aiUid:%d incrOneRes: %v", uid, aiUid, incrOneRes.Err())
		return incrOneRes.Err()
	}
	if expireTotalRes.Err() != nil || expireOneRes.Err() != nil {
		log.WarnWithCtx(ctx, "IncrChatRound uid:%d aiUid:%d expireTotalRes: %v expireOneRes:%v",
			uid, aiUid, expireTotalRes.Err(), expireOneRes.Err())
	}

	return nil

}

func (c *chatRoundRedisCache) GetSpecialChatRound(ctx context.Context, uid, aiUid uint32, ) (uint32, error) {
	val, err := c.cmder.HGet(ctx, c.keyOfSpecifiedRound(uid), strconv.Itoa(int(aiUid))).Result()
	if err != nil {
		if redis.IsNil(err) {
			return 0, nil
		}
		log.ErrorWithCtx(ctx, "GetSpecialChatRound uid:%d aiUid:%d error: %v", uid, aiUid, err)
		return 0, err
	}
	count, err := strconv.Atoi(val)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSpecialChatRound uid:%d aiUid:%d strconv.Atoi error: %v", uid, aiUid, err)
		return 0, err
	}
	return uint32(count), nil
}

func (c *chatRoundRedisCache) GetTotalChatRound(ctx context.Context, uid uint32) (uint32, error) {
	val, err := c.cmder.Get(ctx, c.keyOfTotalRound(uid)).Result()
	if err != nil {
		if redis.IsNil(err) {
			return 0, nil
		}
		log.ErrorWithCtx(ctx, "GetTotalChatRound uid:%d error: %v", uid, err)
		return 0, err
	}
	count, err := strconv.Atoi(val)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTotalChatRound uid:%d strconv.Atoi error: %v", uid, err)
		return 0, err
	}
	return uint32(count), nil
}
