package internal

import (
	"context"
	"github.com/golang/mock/gomock"
	pb "golang.52tt.com/protocol/services/aigc/aigc-account"
	"golang.52tt.com/services/aigc/aigc-account/internal/entity"
	"golang.52tt.com/services/aigc/aigc-account/internal/mgr/mocks"
	"testing"
)

func TestServer_AddAiPost(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAiPostMgr := mocks.NewMockAiPostManager(ctrl)

	server := &Server{
		aiPostMgr: mockAiPostMgr,
	}

	ctx := context.Background()

	// Test case 1: 正常情况
	request := &pb.AddAiPostRequest{
		Uid:    12345,
		PostId: "post_123",
	}
	mockAiPostMgr.EXPECT().AddAiPost(gomock.Any(), uint32(12345), "post_123").Return(nil)

	resp, err := server.AddAiPost(ctx, request)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("AddAiPost() error = %v", err)
	}
	if resp == nil {
		t.Errorf("AddAiPost() response is nil")
	}

	// Test case 2: userId为0的情况
	request = &pb.AddAiPostRequest{
		Uid:    0,
		PostId: "post_123",
	}

	resp, err = server.AddAiPost(ctx, request)
	if err == nil {
		t.Errorf("AddAiPost() with userId=0 expected error, got nil")
	}
	if resp == nil {
		t.Errorf("AddAiPost() with userId=0 response is nil")
	}

	// Test case 3: postId为空的情况
	request = &pb.AddAiPostRequest{
		Uid:    12345,
		PostId: "",
	}

	resp, err = server.AddAiPost(ctx, request)
	if err == nil {
		t.Errorf("AddAiPost() with empty postId expected error, got nil")
	}
	if resp == nil {
		t.Errorf("AddAiPost() with empty postId response is nil")
	}
}

func TestServer_GetAiPost(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAiPostMgr := mocks.NewMockAiPostManager(ctrl)

	server := &Server{
		aiPostMgr: mockAiPostMgr,
	}

	ctx := context.Background()

	// Test case 1: 正常情况 - 默认分页参数
	request := &pb.GetAiPostRequest{
		Page:  1,
		Size:  20,
		AiUid: []uint32{1, 2},
	}
	mockPosts := []*entity.AIPost{
		{Uid: 1, PostId: "post_1"},
		{Uid: 2, PostId: "post_2"},
	}
	mockAiPostMgr.EXPECT().GetAiPostList(gomock.Any(), int64(1), int64(20), []uint32{1, 2}).Return(mockPosts, int64(3), nil)

	resp, err := server.GetAiPost(ctx, request)
	if err != nil {
		t.Errorf("GetAiPost() error = %v", err)
	}
	if resp == nil {
		t.Errorf("GetAiPost() response is nil")
	}

	// Test case 2: 正常情况 - 指定分页参数
	request = &pb.GetAiPostRequest{
		Page:  2,
		Size:  10,
		AiUid: []uint32{11, 12},
	}
	mockPosts = []*entity.AIPost{
		{Uid: 11, PostId: "post_11"},
		{Uid: 12, PostId: "post_12"},
	}
	mockAiPostMgr.EXPECT().GetAiPostList(gomock.Any(), int64(2), int64(10), []uint32{11, 12}).Return(mockPosts, int64(2), nil)

	resp, err = server.GetAiPost(ctx, request)
	if err != nil {
		t.Errorf("GetAiPost() error = %v", err)
	}
	if resp.TotalNum != 2 {
		t.Errorf("GetAiPost() TotalNum = %v, want 2", resp.TotalNum)
	}

	// Test case 3: 正常情况 - 无数据返回
	request = &pb.GetAiPostRequest{
		Page:  1,
		Size:  20,
		AiUid: []uint32{100, 101},
	}
	mockAiPostMgr.EXPECT().GetAiPostList(gomock.Any(), int64(1), int64(20), []uint32{100, 101}).Return([]*entity.AIPost{}, int64(0), nil)

	resp, err = server.GetAiPost(ctx, request)
	if err != nil {
		t.Errorf("GetAiPost() with no data error = %v", err)
	}
	if resp.TotalNum != 0 {
		t.Errorf("GetAiPost() with no data TotalNum = %v, want 0", resp.TotalNum)
	}

	// Test case 5: 正常情况 - 带用户ID过滤
	request = &pb.GetAiPostRequest{
		Page:  1,
		Size:  10,
		AiUid: []uint32{1, 2},
	}
	mockPosts = []*entity.AIPost{
		{Uid: 1, PostId: "user_post_1"},
		{Uid: 2, PostId: "user_post_2"},
	}
	mockAiPostMgr.EXPECT().GetAiPostList(gomock.Any(), int64(1), int64(10), []uint32{1, 2}).Return(mockPosts, int64(2), nil)

	resp, err = server.GetAiPost(ctx, request)
	if err != nil {
		t.Errorf("GetAiPost() with userIds error = %v", err)
	}
	if resp.TotalNum != 2 {
		t.Errorf("GetAiPost() with userIds TotalNum = %v, want 2", resp.TotalNum)
	}
}
