package internal

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	pb "golang.52tt.com/protocol/services/aigc/aigc-account"
	"golang.52tt.com/services/aigc/aigc-account/internal/entity"
	"golang.52tt.com/services/aigc/aigc-account/internal/mgr/mocks"
)

func TestServer_CreateAIAccount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockMgr := mocks.NewMockAIAccountManager(ctrl)
	s := &Server{
		aiAccountMgr: mockMgr,
	}

	ctx := context.Background()

	// 正常情况
	account := &pb.AIAccount{
		Uid:      1001,
		Password: "password",
		Ip:       "***********",
		PromptId: 1,
		TimbreId: 1,
		RoleId:   1,
		Prologue: "prologue1",
	}
	req := &pb.CreateAIAccountRequest{
		Account: account,
	}

	mockMgr.EXPECT().CreateAIAccount(gomock.Any(), gomock.Any()).DoAndReturn(
		func(_ context.Context, acc *entity.AIAccount) error {
			if acc.Uid != account.Uid || acc.Password != account.Password || acc.IP != account.Ip {
				t.Errorf("CreateAIAccount() got = %v, want %v", acc, account)
			}
			return nil
		},
	).Times(1)

	resp, err := s.CreateAIAccount(ctx, req)
	if err != nil {
		t.Errorf("CreateAIAccount() error = %v", err)
	}
	if resp == nil {
		t.Errorf("CreateAIAccount() got nil response")
	}

	// 参数校验失败情况
	invalidReq := &pb.CreateAIAccountRequest{
		Account: &pb.AIAccount{
			Uid:      0, // 无效的 uid
			Password: "password",
			Ip:       "***********",
		},
	}

	resp, err = s.CreateAIAccount(ctx, invalidReq)
	if err == nil {
		t.Errorf("CreateAIAccount() expected error for invalid params")
	}

	// 存储层错误情况
	mockErr := errors.New("mock error")
	mockMgr.EXPECT().CreateAIAccount(gomock.Any(), gomock.Any()).Return(mockErr)

	resp, err = s.CreateAIAccount(ctx, req)
	if err == nil {
		t.Errorf("CreateAIAccount() expected error from manager")
	}
	if err != mockErr {
		t.Errorf("CreateAIAccount() error = %v, want %v", err, mockErr)
	}
}

func TestServer_UpdateAIAccount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockMgr := mocks.NewMockAIAccountManager(ctrl)
	s := &Server{
		aiAccountMgr: mockMgr,
	}

	ctx := context.Background()

	// 正常情况
	account := &pb.AIAccount{
		Uid:      1001,
		Password: "new_password",
		Ip:       "***********",
		PromptId: 2,
		TimbreId: 2,
		RoleId:   2,
		Prologue: "prologue2",
	}
	req := &pb.UpdateAIAccountRequest{
		Account: account,
	}

	existingAccount := &entity.AIAccount{
		Uid:      1001,
		Password: "old_password",
		IP:       "***********",
		PromptId: 1,
		TimbreId: 1,
		RoleId:   1,
		Prologue: "prologue1",
	}

	mockMgr.EXPECT().GetAIAccount(gomock.Any(), uint32(1001)).Return(existingAccount, nil).Times(1)
	mockMgr.EXPECT().UpdateAIAccount(gomock.Any(), gomock.Any()).DoAndReturn(
		func(_ context.Context, acc *entity.AIAccount) error {
			if acc.Uid != account.Uid || acc.Password != account.Password || acc.IP != account.Ip {
				t.Errorf("UpdateAIAccount() got = %v, want %v", acc, account)
			}
			return nil
		},
	).Times(1)

	resp, err := s.UpdateAIAccount(ctx, req)
	if err != nil {
		t.Errorf("UpdateAIAccount() error = %v", err)
	}
	if resp == nil {
		t.Errorf("UpdateAIAccount() got nil response")
	}

	// 参数校验失败情况
	invalidReq := &pb.UpdateAIAccountRequest{
		Account: &pb.AIAccount{
			Uid:      0, // 无效的 uid
			Password: "password",
			Ip:       "***********",
		},
	}

	resp, err = s.UpdateAIAccount(ctx, invalidReq)
	if err == nil {
		t.Errorf("UpdateAIAccount() expected error for invalid params")
	}

	// 账号不存在情况
	mockMgr.EXPECT().GetAIAccount(gomock.Any(), uint32(1001)).Return(nil, nil)

	resp, err = s.UpdateAIAccount(ctx, req)
	if err == nil {
		t.Errorf("UpdateAIAccount() expected error for non-existent account")
	}

	// 获取账号错误情况
	mockErr := errors.New("mock error")
	mockMgr.EXPECT().GetAIAccount(gomock.Any(), uint32(1001)).Return(nil, mockErr)

	resp, err = s.UpdateAIAccount(ctx, req)
	if err == nil {
		t.Errorf("UpdateAIAccount() expected error from GetAIAccount")
	}
	if err != mockErr {
		t.Errorf("UpdateAIAccount() error = %v, want %v", err, mockErr)
	}

	// 更新账号错误情况
	mockMgr.EXPECT().GetAIAccount(gomock.Any(), uint32(1001)).Return(existingAccount, nil)
	mockMgr.EXPECT().UpdateAIAccount(gomock.Any(), gomock.Any()).Return(mockErr)

	resp, err = s.UpdateAIAccount(ctx, req)
	if err == nil {
		t.Errorf("UpdateAIAccount() expected error from UpdateAIAccount")
	}
	if err != mockErr {
		t.Errorf("UpdateAIAccount() error = %v, want %v", err, mockErr)
	}
}

func TestServer_BatchUnregisterAIAccount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockMgr := mocks.NewMockAIAccountManager(ctrl)
	s := &Server{
		aiAccountMgr: mockMgr,
	}

	ctx := context.Background()

	// 正常情况
	req := &pb.BatchUnregisterAIAccountRequest{
		UidList: []uint32{1001, 1002},
	}
	mockMgr.EXPECT().BatchUnregisterAIAccount(gomock.Any(), req.UidList).Return(nil).Times(1)
	resp, err := s.BatchUnregisterAIAccount(ctx, req)
	if err != nil {
		t.Errorf("BatchUnregisterAIAccount() error = %v", err)
	}
	if resp == nil {
		t.Errorf("BatchUnregisterAIAccount() got nil response")
	}

	// 空列表情况
	emptyReq := &pb.BatchUnregisterAIAccountRequest{
		UidList: []uint32{},
	}
	// 不设置mock期望，gomock会校验未被调用
	resp, err = s.BatchUnregisterAIAccount(ctx, emptyReq)
	if err != nil {
		t.Errorf("BatchUnregisterAIAccount() error = %v", err)
	}
	if resp == nil {
		t.Errorf("BatchUnregisterAIAccount() got nil response")
	}

	// 注销错误情况
	mockErr := errors.New("mock error")
	mockMgr.EXPECT().BatchUnregisterAIAccount(gomock.Any(), req.UidList).Return(mockErr).Times(1)
	resp, err = s.BatchUnregisterAIAccount(ctx, req)
	if err == nil {
		t.Errorf("BatchUnregisterAIAccount() expected error from BatchUnregisterAIAccount")
	}
	if err != mockErr {
		t.Errorf("BatchUnregisterAIAccount() error = %v, want %v", err, mockErr)
	}
}

func TestServer_GetPageAIAccount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockMgr := mocks.NewMockAIAccountManager(ctrl)
	s := &Server{
		aiAccountMgr: mockMgr,
	}

	ctx := context.Background()

	// 正常情况
	req := &pb.GetPageAIAccountRequest{
		Page: 1,
		Size: 10,
	}

	accounts := entity.AIAccountList{
		{
			Uid:      1001,
			Password: "password1",
			IP:       "***********",
			PromptId: 1,
			TimbreId: 1,
			RoleId:   1,
			Prologue: "prologue1",
		},
		{
			Uid:      1002,
			Password: "password2",
			IP:       "***********",
			PromptId: 2,
			TimbreId: 2,
			RoleId:   2,
			Prologue: "prologue2",
		},
	}

	mockMgr.EXPECT().GetPageAIAccount(gomock.Any(), uint32(1), uint32(10), gomock.Any()).Return(accounts, uint32(100), nil).Times(1)

	resp, err := s.GetPageAIAccount(ctx, req)
	if err != nil {
		t.Errorf("GetPageAIAccount() error = %v", err)
	}
	if resp == nil {
		t.Errorf("GetPageAIAccount() got nil response")
	}
	if len(resp.AccountList) != len(accounts) {
		t.Errorf("GetPageAIAccount() got %d accounts, want %d", len(resp.AccountList), len(accounts))
	}
	if resp.Total != 100 {
		t.Errorf("GetPageAIAccount() got total = %d, want %d", resp.Total, 100)
	}

	// 获取错误情况
	mockErr := errors.New("mock error")
	mockMgr.EXPECT().GetPageAIAccount(gomock.Any(), req.Page, req.Size, gomock.Any()).Return(nil, uint32(0), mockErr)

	resp, err = s.GetPageAIAccount(ctx, req)
	if err == nil {
		t.Errorf("GetPageAIAccount() expected error from GetPageAIAccount")
	}
	if err != mockErr {
		t.Errorf("GetPageAIAccount() error = %v, want %v", err, mockErr)
	}
}

func TestServer_GetAIAccount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockMgr := mocks.NewMockAIAccountManager(ctrl)
	s := &Server{
		aiAccountMgr: mockMgr,
	}

	ctx := context.Background()

	// 正常情况
	req := &pb.GetAIAccountRequest{
		Uid:       1001,
		ReqSource: pb.GetAIAccountSource_DEFAULT,
	}

	account := &entity.AIAccount{
		Uid:      1001,
		Password: "password",
		IP:       "***********",
		PromptId: 1,
		TimbreId: 1,
		RoleId:   1,
		Prologue: "prologue1",
	}

	mockMgr.EXPECT().GetAIAccount(gomock.Any(), req.Uid).Return(account, nil)

	resp, err := s.GetAIAccount(ctx, req)
	if err != nil {
		t.Errorf("GetAIAccount() error = %v", err)
	}
	if resp == nil || resp.Account == nil {
		t.Errorf("GetAIAccount() got nil response or nil account")
		return
	}
	if resp.Account.Uid != account.Uid {
		t.Errorf("GetAIAccount() got uid = %d, want %d", resp.Account.Uid, account.Uid)
	}

	// uid 为 0 情况
	emptyReq := &pb.GetAIAccountRequest{
		Uid: 0,
	}

	resp, err = s.GetAIAccount(ctx, emptyReq)
	if err != nil {
		t.Errorf("GetAIAccount() error = %v", err)
	}
	if resp == nil {
		t.Errorf("GetAIAccount() got nil response")
	}
	if resp.Account != nil {
		t.Errorf("GetAIAccount() got non-nil account for uid=0")
	}

	// 账号不存在情况
	mockMgr.EXPECT().GetAIAccount(gomock.Any(), req.Uid).Return(nil, nil)

	resp, err = s.GetAIAccount(ctx, req)
	if err != nil {
		t.Errorf("GetAIAccount() error = %v", err)
	}
	if resp == nil {
		t.Errorf("GetAIAccount() got nil response")
	}
	if resp.Account != nil {
		t.Errorf("GetAIAccount() got non-nil account for non-existent account")
	}

	// 获取错误情况
	mockErr := errors.New("mock error")
	mockMgr.EXPECT().GetAIAccount(gomock.Any(), req.Uid).Return(nil, mockErr)

	resp, err = s.GetAIAccount(ctx, req)
	if err == nil {
		t.Errorf("GetAIAccount() expected error from GetAIAccount")
	}
	if err != mockErr {
		t.Errorf("GetAIAccount() error = %v, want %v", err, mockErr)
	}
}

func TestServer_BatchGetAIAccount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockMgr := mocks.NewMockAIAccountManager(ctrl)
	s := &Server{
		aiAccountMgr: mockMgr,
	}

	ctx := context.Background()

	// 正常情况
	req := &pb.BatchGetAIAccountRequest{
		UidList: []uint32{1001, 1002},
	}

	accounts := entity.AIAccountList{
		{
			Uid:      1001,
			Password: "password1",
			IP:       "***********",
			PromptId: 1,
			TimbreId: 1,
			RoleId:   1,
			Prologue: "prologue1",
		},
		{
			Uid:      1002,
			Password: "password2",
			IP:       "***********",
			PromptId: 2,
			TimbreId: 2,
			RoleId:   2,
			Prologue: "prologue2",
		},
	}

	mockMgr.EXPECT().BatchGetAIAccount(gomock.Any(), req.UidList).Return(accounts, nil)

	resp, err := s.BatchGetAIAccount(ctx, req)
	if err != nil {
		t.Errorf("BatchGetAIAccount() error = %v", err)
	}
	if resp == nil {
		t.Errorf("BatchGetAIAccount() got nil response")
	}
	if len(resp.AccountList) != len(accounts) {
		t.Errorf("BatchGetAIAccount() got %d accounts, want %d", len(resp.AccountList), len(accounts))
	}

	// 空列表情况
	emptyReq := &pb.BatchGetAIAccountRequest{
		UidList: []uint32{},
	}

	resp, err = s.BatchGetAIAccount(ctx, emptyReq)
	if err != nil {
		t.Errorf("BatchGetAIAccount() error = %v", err)
	}
	if resp == nil {
		t.Errorf("BatchGetAIAccount() got nil response")
	}
	if len(resp.AccountList) != 0 {
		t.Errorf("BatchGetAIAccount() got %d accounts, want 0", len(resp.AccountList))
	}

	// 获取错误情况
	mockErr := errors.New("mock error")
	mockMgr.EXPECT().BatchGetAIAccount(gomock.Any(), req.UidList).Return(nil, mockErr)

	resp, err = s.BatchGetAIAccount(ctx, req)
	if err == nil {
		t.Errorf("BatchGetAIAccount() expected error from BatchGetAIAccount")
	}
	if err != mockErr {
		t.Errorf("BatchGetAIAccount() error = %v, want %v", err, mockErr)
	}
}

func TestServer_checkAIAccount(t *testing.T) {
	s := &Server{}
	ctx := context.Background()

	// 正常情况
	account := &pb.AIAccount{
		Uid:      1001,
		Password: "password",
		Ip:       "***********",
		Prologue: "prologue1",
	}

	err := s.checkAIAccount(ctx, account)
	if err != nil {
		t.Errorf("checkAIAccount() error = %v", err)
	}

	// uid 为 0 情况
	invalidAccount1 := &pb.AIAccount{
		Uid:      0,
		Password: "password",
		Ip:       "***********",
		Prologue: "prologue1",
	}

	err = s.checkAIAccount(ctx, invalidAccount1)
	if err == nil {
		t.Errorf("checkAIAccount() expected error for uid=0")
	}

	// 密码为空情况
	invalidAccount2 := &pb.AIAccount{
		Uid:      1001,
		Password: "",
		Ip:       "***********",
		Prologue: "prologue1",
	}

	err = s.checkAIAccount(ctx, invalidAccount2)
	if err == nil {
		t.Errorf("checkAIAccount() expected error for empty password")
	}

	// IP 为空情况
	invalidAccount3 := &pb.AIAccount{
		Uid:      1001,
		Password: "password",
		Ip:       "",
		Prologue: "prologue1",
	}

	err = s.checkAIAccount(ctx, invalidAccount3)
	if err != nil {
		t.Errorf("checkAIAccount() error = %v", err)
	}
}
