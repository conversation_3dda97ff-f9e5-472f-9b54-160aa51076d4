package internal

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	pb "golang.52tt.com/protocol/services/aigc/aigc-account"
)

func (s *Server) IncrChatRound(ctx context.Context, request *pb.IncrChatRoundRequest) (*pb.IncrChatRoundResponse, error) {
	out := &pb.IncrChatRoundResponse{}
	if request.GetUid() == 0 || request.GetAiUid() == 0 {
		log.WarnWithCtx(ctx, "IncrChatRound request uid or aiUid is zero: %s", request.String())
		return out, nil
	}
	if err := s.chatMgr.IncrChatRound(ctx, request.GetUid(), request.GetAiUid()); err != nil {
		log.ErrorWithCtx(ctx, "IncrChatRound IncrChatRound uid:%d aiUid:%d error: %v", request.GetUid(), request.GetAiUid(), err)
		return out, err
	}
	log.InfoWithCtx(ctx, "IncrChatRound request: %s resp: %s", request.String(), out.String())
	return out, nil
}

func (s *Server) GetChatRound(ctx context.Context, request *pb.GetChatRoundRequest) (*pb.GetChatRoundResponse, error) {
	out := &pb.GetChatRoundResponse{}
	if !request.GetNeedTotal() && request.GetUid() == 0 && request.GetAiUid() == 0 {
		log.WarnWithCtx(ctx, "GetChatRound request needTotal is false and uid or aiUid is zero: %s", request.String())
		return out, nil
	}
	if request.GetUid() != 0 && request.GetAiUid() != 0 {
		round, err := s.chatMgr.GetSpecialChatRound(ctx, request.GetUid(), request.GetAiUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChatRound GetSpecialChatRound uid:%d aiUid:%d error: %v", request.GetUid(), request.GetAiUid(), err)
			return out, err
		}
		out.CurRound = round
	}
	if request.GetNeedTotal() {
		totalRound, err := s.chatMgr.GetTotalChatRound(ctx, request.GetUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChatRound GetTotalChatRound uid:%d error: %v", request.GetUid(), err)
			return out, err
		}
		out.TotalRound = totalRound
	}
	log.InfoWithCtx(ctx, "GetChatRound request: %s resp: %s", request.String(), out.String())
	return out, nil
}

func (s *Server) UpdateChatTime(ctx context.Context, request *pb.UpdateChatTimeRequest) (*pb.UpdateChatTimeResponse, error) {
	out := &pb.UpdateChatTimeResponse{}

	if request.GetUid() == 0 || request.GetAiUid() == 0 {
		log.WarnWithCtx(ctx, "UpdateChatTime request uid or aiUid is zero: %s", request.String())
		return out, nil
	}
	if err := s.chatMgr.UpdateChatTime(ctx, request.GetUid(), request.GetAiUid()); err != nil {
		log.ErrorWithCtx(ctx, "UpdateChatTime UpdateChatTime uid:%d aiUid:%d error: %v", request.GetUid(), request.GetAiUid(), err)
		return out, err
	}

	log.InfoWithCtx(ctx, "UpdateChatTime request: %s resp: %s", request.String(), out.String())
	return out, nil
}

func (s *Server) GetLastChatTime(ctx context.Context, request *pb.GetLastChatTimeRequest) (*pb.GetLastChatTimeResponse, error) {
	out := &pb.GetLastChatTimeResponse{}
	if request.GetUid() == 0 || request.GetAiUid() == 0 {
		log.WarnWithCtx(ctx, "GetLastChatTime request uid or aiUid is zero: %s", request.String())
		return out, nil
	}
	lastChatTime, err := s.chatMgr.GetLastChatTime(ctx, request.GetUid(), request.GetAiUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLastChatTime GetLastChatTime uid:%d aiUid:%d error: %v", request.GetUid(), request.GetAiUid(), err)
		return out, err
	}
	out.LastChatTime = lastChatTime
	log.InfoWithCtx(ctx, "GetLastChatTime request: %s resp: %s", request.String(), out.String())
	return out, nil
}
