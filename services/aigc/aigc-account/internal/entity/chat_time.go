package entity

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	"strconv"
	"strings"
	"time"
)

type ChatTimeRecord struct {
	ID           string    `bson:"_id"`                                  // 主键，格式为 "uid_aiUid"
	LastSendTime time.Time `json:"last_send_time" bson:"last_send_time"` // 最后发送时间
}

func EncodeChatTimeId(uid, aiUid uint32) string {
	return fmt.Sprintf("%d_%d", uid, aiUid)
}
func DecodeChatTimeId(ctx context.Context, id string) (uid, aiUid uint32) {
	splits := strings.Split(id, "_")
	if len(splits) != 2 {
		log.ErrorWithCtx(ctx, "DecodeChatTimeId: invalid id format %s", id)
		return 0, 0
	}
	u1, err := strconv.Atoi(splits[0])
	if err != nil {
		log.ErrorWithCtx(ctx, "DecodeChatTimeId: invalid id %s, error: %v", id, err)
		return 0, 0
	}
	u2, err := strconv.Atoi(splits[1])
	if err != nil {
		log.ErrorWithCtx(ctx, "DecodeChatTimeId: invalid id %s, error: %v", id, err)
		return 0, 0
	}
	uid = uint32(u1)
	aiUid = uint32(u2)
	return

}
