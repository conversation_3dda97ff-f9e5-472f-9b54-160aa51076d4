package entity

import (
	"fmt"

	pb "golang.52tt.com/protocol/services/aigc/aigc-account"
)

type (
	AIAccountList []*AIAccount

	AIAccount struct {
		Uid               uint32 `bson:"_id"`                   // uid直接作为主键
		Password          string `bson:"password"`              // 密码明文，运营后台展示
		IP                string `bson:"ip"`                    // 配置ip地址，用于个人主页展示位置
		PromptId          uint32 `bson:"prompt_id"`             // 提示词ID，为空则AI不会回复用户
		TimbreId          uint32 `bson:"timbre_id"`             // 音色ID
		RoleId            uint32 `bson:"role_id"`               // 关联角色ID
		Prologue          string `bson:"prologue"`              // 开场白
		IsInRiskWhiteList bool   `bson:"is_in_risk_white_list"` // 是否在风控白名单中
		Identity          string `bson:"identity"`              // 标识
		Desc              string `bson:"desc"`                  // 说明

		CreateTime           int64    `bson:"create_time"`               // 创建时间
		UpdateTime           int64    `bson:"update_time"`               // 更新时间
		IsUnregister         bool     `bson:"is_unregister"`             // 是否已注销
		Sort                 uint32   `bson:"sort"`                      //排序
		AccountTags          []string `bson:"account_tags"`              //账号标签
		IsShowInChatCardWall bool     `bson:"is_show_in_chat_card_wall"` //是否展示在扩列墙
		Sex                  int32    `bson:"sex"`                       //性别
	}
)

const (
	defaultIP = "************" // 没配IP默认填一个广东省IP
)

// NewAIAccountFromPb 从pb对象创建实体
func NewAIAccountFromPb(pbAccount *pb.AIAccount) *AIAccount {
	aiAccount := &AIAccount{
		Uid:                  pbAccount.Uid,
		Password:             pbAccount.Password,
		IP:                   pbAccount.Ip,
		PromptId:             pbAccount.PromptId,
		TimbreId:             pbAccount.TimbreId,
		RoleId:               pbAccount.RoleId,
		Prologue:             pbAccount.Prologue,
		IsInRiskWhiteList:    pbAccount.IsInRiskWhiteList,
		Identity:             pbAccount.GetIdentity(),
		Desc:                 pbAccount.GetDesc(),
		Sort:                 pbAccount.Sort,
		AccountTags:          pbAccount.AccountTags,
		IsShowInChatCardWall: pbAccount.IsShowInChatCardWall,
	}

	if aiAccount.IP == "" {
		aiAccount.IP = defaultIP
	}

	return aiAccount
}

// Update 更新account信息
func (a *AIAccount) Update(pbAccount *pb.AIAccount) {
	a.Password = pbAccount.Password
	a.IP = pbAccount.Ip
	if a.IP == "" {
		a.IP = defaultIP
	}
	a.PromptId = pbAccount.PromptId
	a.TimbreId = pbAccount.TimbreId
	a.RoleId = pbAccount.RoleId
	a.Prologue = pbAccount.Prologue
	a.IsInRiskWhiteList = pbAccount.IsInRiskWhiteList
	a.Identity = pbAccount.GetIdentity()
	a.Desc = pbAccount.GetDesc()
	a.Sort = pbAccount.Sort
	a.AccountTags = pbAccount.AccountTags
	a.IsShowInChatCardWall = pbAccount.IsShowInChatCardWall
}

// String 返回AIAccount的字符串表示
func (a *AIAccount) String() string {
	return fmt.Sprintf(
		"{Uid:%d, IP:%s, PromptId:%d, TimbreId:%d, RoleId:%d, Prologue:%s, IsInRiskWhiteList:%t, CreateTime:%d, UpdateTime:%d}",
		a.Uid, a.IP, a.PromptId, a.TimbreId, a.RoleId, a.Prologue, a.IsInRiskWhiteList, a.CreateTime, a.UpdateTime,
	)
}

// ToPb 将实体转换为pb对象
func (a *AIAccount) ToPb(reqSource pb.GetAIAccountSource) *pb.AIAccount {
	pbAccount := &pb.AIAccount{
		Uid:                  a.Uid,
		Password:             a.Password,
		Ip:                   a.IP,
		PromptId:             a.PromptId,
		TimbreId:             a.TimbreId,
		RoleId:               a.RoleId,
		Prologue:             a.Prologue,
		IsUnregister:         a.IsUnregister,
		CreateTime:           a.CreateTime,
		UpdateTime:           a.UpdateTime,
		IsInRiskWhiteList:    a.IsInRiskWhiteList,
		Identity:             a.Identity,
		Desc:                 a.Desc,
		Sort:                 a.Sort,
		AccountTags:          a.AccountTags,
		IsShowInChatCardWall: a.IsShowInChatCardWall,
	}
	if reqSource != pb.GetAIAccountSource_ADMIN_BACKEND {
		pbAccount.Password = ""
	}
	return pbAccount
}

// ToPbList 将实体列表转换为pb对象列表
func (list AIAccountList) ToPbList(reqSource pb.GetAIAccountSource) []*pb.AIAccount {
	pbList := make([]*pb.AIAccount, 0, len(list))
	for _, account := range list {
		pbList = append(pbList, account.ToPb(reqSource))
	}
	return pbList
}
