package internal

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	pb "golang.52tt.com/protocol/services/aigc/aigc-account"
	"golang.52tt.com/services/aigc/aigc-account/internal/mgr/mocks"
)

func TestServer_IncrChatRound(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockChatMgr := mocks.NewMockChatManager(ctrl)

	server := &Server{
		chatMgr: mockChatMgr,
	}

	ctx := context.Background()

	// Test case 1: 正常情况
	request := &pb.IncrChatRoundRequest{
		Uid:   12345,
		AiUid: 67890,
	}
	mockChatMgr.EXPECT().IncrChatRound(gomock.Any(), uint32(12345), uint32(67890)).Return(nil)

	resp, err := server.IncrChatRound(ctx, request)
	if err != nil {
		t.Errorf("IncrChatRound() error = %v", err)
	}
	if resp == nil {
		t.<PERSON><PERSON><PERSON>("IncrChatRound() response is nil")
	}

	// Test case 2: uid为0的情况
	request = &pb.IncrChatRoundRequest{
		Uid:   0,
		AiUid: 67890,
	}

	resp, err = server.IncrChatRound(ctx, request)
	if err != nil {
		t.Errorf("IncrChatRound() with uid=0 error = %v", err)
	}
	if resp == nil {
		t.Errorf("IncrChatRound() with uid=0 response is nil")
	}

	// Test case 3: aiUid为0的情况
	request = &pb.IncrChatRoundRequest{
		Uid:   12345,
		AiUid: 0,
	}

	resp, err = server.IncrChatRound(ctx, request)
	if err != nil {
		t.Errorf("IncrChatRound() with aiUid=0 error = %v", err)
	}
	if resp == nil {
		t.Errorf("IncrChatRound() with aiUid=0 response is nil")
	}

	// Test case 4: 异常情况 - chatMgr返回错误
	request = &pb.IncrChatRoundRequest{
		Uid:   12345,
		AiUid: 67890,
	}
	mockErr := errors.New("mock error")
	mockChatMgr.EXPECT().IncrChatRound(gomock.Any(), uint32(12345), uint32(67890)).Return(mockErr)

	resp, err = server.IncrChatRound(ctx, request)
	if err == nil {
		t.Errorf("IncrChatRound() expected error, got nil")
	}
	if err != mockErr {
		t.Errorf("IncrChatRound() error = %v, want %v", err, mockErr)
	}
	if resp == nil {
		t.Errorf("IncrChatRound() response is nil")
	}
}

func TestServer_GetChatRound(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockChatMgr := mocks.NewMockChatManager(ctrl)

	server := &Server{
		chatMgr: mockChatMgr,
	}

	ctx := context.Background()

	// Test case 1: 正常情况 - 获取特定聊天轮数
	request := &pb.GetChatRoundRequest{
		Uid:       12345,
		AiUid:     67890,
		NeedTotal: false,
	}
	mockChatMgr.EXPECT().GetSpecialChatRound(gomock.Any(), uint32(12345), uint32(67890)).Return(uint32(5), nil)

	resp, err := server.GetChatRound(ctx, request)
	if err != nil {
		t.Errorf("GetChatRound() error = %v", err)
	}
	if resp == nil {
		t.Errorf("GetChatRound() response is nil")
	}
	if resp.CurRound != 5 {
		t.Errorf("GetChatRound() CurRound = %v, want 5", resp.CurRound)
	}

	// Test case 2: 正常情况 - 获取总聊天轮数
	request = &pb.GetChatRoundRequest{
		Uid:       12345,
		AiUid:     0,
		NeedTotal: true,
	}
	mockChatMgr.EXPECT().GetTotalChatRound(gomock.Any(), uint32(12345)).Return(uint32(10), nil)

	resp, err = server.GetChatRound(ctx, request)
	if err != nil {
		t.Errorf("GetChatRound() error = %v", err)
	}
	if resp == nil {
		t.Errorf("GetChatRound() response is nil")
	}
	if resp.TotalRound != 10 {
		t.Errorf("GetChatRound() TotalRound = %v, want 10", resp.TotalRound)
	}

	// Test case 3: 正常情况 - 同时获取特定和总聊天轮数
	request = &pb.GetChatRoundRequest{
		Uid:       12345,
		AiUid:     67890,
		NeedTotal: true,
	}
	mockChatMgr.EXPECT().GetSpecialChatRound(gomock.Any(), uint32(12345), uint32(67890)).Return(uint32(5), nil)
	mockChatMgr.EXPECT().GetTotalChatRound(gomock.Any(), uint32(12345)).Return(uint32(10), nil)

	resp, err = server.GetChatRound(ctx, request)
	if err != nil {
		t.Errorf("GetChatRound() error = %v", err)
	}
	if resp == nil {
		t.Errorf("GetChatRound() response is nil")
	}
	if resp.CurRound != 5 {
		t.Errorf("GetChatRound() CurRound = %v, want 5", resp.CurRound)
	}
	if resp.TotalRound != 10 {
		t.Errorf("GetChatRound() TotalRound = %v, want 10", resp.TotalRound)
	}

	// Test case 4: 参数无效的情况
	request = &pb.GetChatRoundRequest{
		Uid:       0,
		AiUid:     0,
		NeedTotal: false,
	}

	resp, err = server.GetChatRound(ctx, request)
	if err != nil {
		t.Errorf("GetChatRound() with invalid params error = %v", err)
	}
	if resp == nil {
		t.Errorf("GetChatRound() with invalid params response is nil")
	}

	// Test case 5: 异常情况 - GetSpecialChatRound返回错误
	request = &pb.GetChatRoundRequest{
		Uid:       12345,
		AiUid:     67890,
		NeedTotal: false,
	}
	mockErr := errors.New("mock error")
	mockChatMgr.EXPECT().GetSpecialChatRound(gomock.Any(), uint32(12345), uint32(67890)).Return(uint32(0), mockErr)

	resp, err = server.GetChatRound(ctx, request)
	if err == nil {
		t.Errorf("GetChatRound() expected error, got nil")
	}
	if err != mockErr {
		t.Errorf("GetChatRound() error = %v, want %v", err, mockErr)
	}

	// Test case 6: 异常情况 - GetTotalChatRound返回错误
	request = &pb.GetChatRoundRequest{
		Uid:       12345,
		AiUid:     0,
		NeedTotal: true,
	}
	mockChatMgr.EXPECT().GetTotalChatRound(gomock.Any(), uint32(12345)).Return(uint32(0), mockErr)

	resp, err = server.GetChatRound(ctx, request)
	if err == nil {
		t.Errorf("GetChatRound() expected error, got nil")
	}
	if err != mockErr {
		t.Errorf("GetChatRound() error = %v, want %v", err, mockErr)
	}
}

func TestServer_UpdateChatTime(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockChatMgr := mocks.NewMockChatManager(ctrl)

	server := &Server{
		chatMgr: mockChatMgr,
	}

	ctx := context.Background()

	// Test case 1: 正常情况
	request := &pb.UpdateChatTimeRequest{
		Uid:   12345,
		AiUid: 67890,
	}
	mockChatMgr.EXPECT().UpdateChatTime(gomock.Any(), uint32(12345), uint32(67890)).Return(nil)

	resp, err := server.UpdateChatTime(ctx, request)
	if err != nil {
		t.Errorf("UpdateChatTime() error = %v", err)
	}
	if resp == nil {
		t.Errorf("UpdateChatTime() response is nil")
	}

	// Test case 2: uid为0的情况
	request = &pb.UpdateChatTimeRequest{
		Uid:   0,
		AiUid: 67890,
	}

	resp, err = server.UpdateChatTime(ctx, request)
	if err != nil {
		t.Errorf("UpdateChatTime() with uid=0 error = %v", err)
	}
	if resp == nil {
		t.Errorf("UpdateChatTime() with uid=0 response is nil")
	}

	// Test case 3: aiUid为0的情况
	request = &pb.UpdateChatTimeRequest{
		Uid:   12345,
		AiUid: 0,
	}

	resp, err = server.UpdateChatTime(ctx, request)
	if err != nil {
		t.Errorf("UpdateChatTime() with aiUid=0 error = %v", err)
	}
	if resp == nil {
		t.Errorf("UpdateChatTime() with aiUid=0 response is nil")
	}

	// Test case 4: 异常情况 - chatMgr返回错误
	request = &pb.UpdateChatTimeRequest{
		Uid:   12345,
		AiUid: 67890,
	}
	mockErr := errors.New("mock error")
	mockChatMgr.EXPECT().UpdateChatTime(gomock.Any(), uint32(12345), uint32(67890)).Return(mockErr)

	resp, err = server.UpdateChatTime(ctx, request)
	if err == nil {
		t.Errorf("UpdateChatTime() expected error, got nil")
	}
	if err != mockErr {
		t.Errorf("UpdateChatTime() error = %v, want %v", err, mockErr)
	}
	if resp == nil {
		t.Errorf("UpdateChatTime() response is nil")
	}
}

func TestServer_GetLastChatTime(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockChatMgr := mocks.NewMockChatManager(ctrl)

	server := &Server{
		chatMgr: mockChatMgr,
	}

	ctx := context.Background()

	// Test case 1: 正常情况
	request := &pb.GetLastChatTimeRequest{
		Uid:   12345,
		AiUid: 67890,
	}
	expectedTime := int64(1640995200) // 2022-01-01 00:00:00
	mockChatMgr.EXPECT().GetLastChatTime(gomock.Any(), uint32(12345), uint32(67890)).Return(expectedTime, nil)

	resp, err := server.GetLastChatTime(ctx, request)
	if err != nil {
		t.Errorf("GetLastChatTime() error = %v", err)
	}
	if resp == nil {
		t.Errorf("GetLastChatTime() response is nil")
	}
	if resp.LastChatTime != expectedTime {
		t.Errorf("GetLastChatTime() LastChatTime = %v, want %v", resp.LastChatTime, expectedTime)
	}

	// Test case 2: uid为0的情况
	request = &pb.GetLastChatTimeRequest{
		Uid:   0,
		AiUid: 67890,
	}

	resp, err = server.GetLastChatTime(ctx, request)
	if err != nil {
		t.Errorf("GetLastChatTime() with uid=0 error = %v", err)
	}
	if resp == nil {
		t.Errorf("GetLastChatTime() with uid=0 response is nil")
	}

	// Test case 3: aiUid为0的情况
	request = &pb.GetLastChatTimeRequest{
		Uid:   12345,
		AiUid: 0,
	}

	resp, err = server.GetLastChatTime(ctx, request)
	if err != nil {
		t.Errorf("GetLastChatTime() with aiUid=0 error = %v", err)
	}
	if resp == nil {
		t.Errorf("GetLastChatTime() with aiUid=0 response is nil")
	}

	// Test case 4: 异常情况 - chatMgr返回错误
	request = &pb.GetLastChatTimeRequest{
		Uid:   12345,
		AiUid: 67890,
	}
	mockErr := errors.New("mock error")
	mockChatMgr.EXPECT().GetLastChatTime(gomock.Any(), uint32(12345), uint32(67890)).Return(int64(0), mockErr)

	resp, err = server.GetLastChatTime(ctx, request)
	if err == nil {
		t.Errorf("GetLastChatTime() expected error, got nil")
	}
	if err != mockErr {
		t.Errorf("GetLastChatTime() error = %v, want %v", err, mockErr)
	}
	if resp == nil {
		t.Errorf("GetLastChatTime() response is nil")
	}

	// Test case 5: 正常情况 - 无记录返回0
	request = &pb.GetLastChatTimeRequest{
		Uid:   12345,
		AiUid: 67890,
	}
	mockChatMgr.EXPECT().GetLastChatTime(gomock.Any(), uint32(12345), uint32(67890)).Return(int64(0), nil)

	resp, err = server.GetLastChatTime(ctx, request)
	if err != nil {
		t.Errorf("GetLastChatTime() with no record error = %v", err)
	}
	if resp == nil {
		t.Errorf("GetLastChatTime() with no record response is nil")
	}
	if resp.LastChatTime != 0 {
		t.Errorf("GetLastChatTime() with no record LastChatTime = %v, want 0", resp.LastChatTime)
	}
}
