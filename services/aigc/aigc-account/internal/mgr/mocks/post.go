// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-account/internal/mgr (interfaces: AiPostManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	entity "golang.52tt.com/services/aigc/aigc-account/internal/entity"
)

// MockAiPostManager is a mock of AiPostManager interface.
type MockAiPostManager struct {
	ctrl     *gomock.Controller
	recorder *MockAiPostManagerMockRecorder
}

// MockAiPostManagerMockRecorder is the mock recorder for MockAiPostManager.
type MockAiPostManagerMockRecorder struct {
	mock *MockAiPostManager
}

// NewMockAiPostManager creates a new mock instance.
func NewMockAiPostManager(ctrl *gomock.Controller) *MockAiPostManager {
	mock := &MockAiPostManager{ctrl: ctrl}
	mock.recorder = &MockAiPostManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAiPostManager) EXPECT() *MockAiPostManagerMockRecorder {
	return m.recorder
}

// AddAiPost mocks base method.
func (m *MockAiPostManager) AddAiPost(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAiPost", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddAiPost indicates an expected call of AddAiPost.
func (mr *MockAiPostManagerMockRecorder) AddAiPost(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAiPost", reflect.TypeOf((*MockAiPostManager)(nil).AddAiPost), arg0, arg1, arg2)
}

// AddCommentPost mocks base method.
func (m *MockAiPostManager) AddCommentPost(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCommentPost", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddCommentPost indicates an expected call of AddCommentPost.
func (mr *MockAiPostManagerMockRecorder) AddCommentPost(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCommentPost", reflect.TypeOf((*MockAiPostManager)(nil).AddCommentPost), arg0, arg1, arg2)
}

// AddInteraction mocks base method.
func (m *MockAiPostManager) AddInteraction(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddInteraction", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddInteraction indicates an expected call of AddInteraction.
func (mr *MockAiPostManagerMockRecorder) AddInteraction(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddInteraction", reflect.TypeOf((*MockAiPostManager)(nil).AddInteraction), arg0, arg1, arg2)
}

// AddLikePost mocks base method.
func (m *MockAiPostManager) AddLikePost(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddLikePost", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddLikePost indicates an expected call of AddLikePost.
func (mr *MockAiPostManagerMockRecorder) AddLikePost(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddLikePost", reflect.TypeOf((*MockAiPostManager)(nil).AddLikePost), arg0, arg1, arg2)
}

// AddUserAiChatRecord mocks base method.
func (m *MockAiPostManager) AddUserAiChatRecord(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserAiChatRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddUserAiChatRecord indicates an expected call of AddUserAiChatRecord.
func (mr *MockAiPostManagerMockRecorder) AddUserAiChatRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserAiChatRecord", reflect.TypeOf((*MockAiPostManager)(nil).AddUserAiChatRecord), arg0, arg1, arg2)
}

// GetAiPostList mocks base method.
func (m *MockAiPostManager) GetAiPostList(arg0 context.Context, arg1, arg2 int64, arg3 []uint32) ([]*entity.AIPost, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAiPostList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*entity.AIPost)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetAiPostList indicates an expected call of GetAiPostList.
func (mr *MockAiPostManagerMockRecorder) GetAiPostList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAiPostList", reflect.TypeOf((*MockAiPostManager)(nil).GetAiPostList), arg0, arg1, arg2, arg3)
}

// GetCommentPostCount mocks base method.
func (m *MockAiPostManager) GetCommentPostCount(arg0 context.Context, arg1 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCommentPostCount", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCommentPostCount indicates an expected call of GetCommentPostCount.
func (mr *MockAiPostManagerMockRecorder) GetCommentPostCount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommentPostCount", reflect.TypeOf((*MockAiPostManager)(nil).GetCommentPostCount), arg0, arg1)
}

// GetInteractionAiList mocks base method.
func (m *MockAiPostManager) GetInteractionAiList(arg0 context.Context, arg1 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInteractionAiList", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInteractionAiList indicates an expected call of GetInteractionAiList.
func (mr *MockAiPostManagerMockRecorder) GetInteractionAiList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInteractionAiList", reflect.TypeOf((*MockAiPostManager)(nil).GetInteractionAiList), arg0, arg1)
}

// GetLikePostCount mocks base method.
func (m *MockAiPostManager) GetLikePostCount(arg0 context.Context, arg1 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLikePostCount", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLikePostCount indicates an expected call of GetLikePostCount.
func (mr *MockAiPostManagerMockRecorder) GetLikePostCount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLikePostCount", reflect.TypeOf((*MockAiPostManager)(nil).GetLikePostCount), arg0, arg1)
}

// GetPostAiCommentCount mocks base method.
func (m *MockAiPostManager) GetPostAiCommentCount(arg0 context.Context, arg1 string, arg2 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPostAiCommentCount", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostAiCommentCount indicates an expected call of GetPostAiCommentCount.
func (mr *MockAiPostManagerMockRecorder) GetPostAiCommentCount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostAiCommentCount", reflect.TypeOf((*MockAiPostManager)(nil).GetPostAiCommentCount), arg0, arg1, arg2)
}

// GetPostCommentAiList mocks base method.
func (m *MockAiPostManager) GetPostCommentAiList(arg0 context.Context, arg1 string) (map[uint32]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPostCommentAiList", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostCommentAiList indicates an expected call of GetPostCommentAiList.
func (mr *MockAiPostManagerMockRecorder) GetPostCommentAiList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostCommentAiList", reflect.TypeOf((*MockAiPostManager)(nil).GetPostCommentAiList), arg0, arg1)
}

// GetUserAiChatRecord mocks base method.
func (m *MockAiPostManager) GetUserAiChatRecord(arg0 context.Context, arg1, arg2 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAiChatRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAiChatRecord indicates an expected call of GetUserAiChatRecord.
func (mr *MockAiPostManagerMockRecorder) GetUserAiChatRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAiChatRecord", reflect.TypeOf((*MockAiPostManager)(nil).GetUserAiChatRecord), arg0, arg1, arg2)
}

// IncrPostAiCommentCount mocks base method.
func (m *MockAiPostManager) IncrPostAiCommentCount(arg0 context.Context, arg1 string, arg2 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrPostAiCommentCount", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrPostAiCommentCount indicates an expected call of IncrPostAiCommentCount.
func (mr *MockAiPostManagerMockRecorder) IncrPostAiCommentCount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrPostAiCommentCount", reflect.TypeOf((*MockAiPostManager)(nil).IncrPostAiCommentCount), arg0, arg1, arg2)
}
