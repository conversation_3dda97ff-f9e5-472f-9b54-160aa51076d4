// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-account/internal/mgr (interfaces: ChatManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockChatManager is a mock of ChatManager interface.
type MockChatManager struct {
	ctrl     *gomock.Controller
	recorder *MockChatManagerMockRecorder
}

// MockChatManagerMockRecorder is the mock recorder for MockChatManager.
type MockChatManagerMockRecorder struct {
	mock *MockChatManager
}

// NewMockChatManager creates a new mock instance.
func NewMockChatManager(ctrl *gomock.Controller) *MockChatManager {
	mock := &MockChatManager{ctrl: ctrl}
	mock.recorder = &MockChatManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChatManager) EXPECT() *MockChatManagerMockRecorder {
	return m.recorder
}

// GetLastChatTime mocks base method.
func (m *MockChatManager) GetLastChatTime(arg0 context.Context, arg1, arg2 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastChatTime", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastChatTime indicates an expected call of GetLastChatTime.
func (mr *MockChatManagerMockRecorder) GetLastChatTime(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastChatTime", reflect.TypeOf((*MockChatManager)(nil).GetLastChatTime), arg0, arg1, arg2)
}

// GetSpecialChatRound mocks base method.
func (m *MockChatManager) GetSpecialChatRound(arg0 context.Context, arg1, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSpecialChatRound", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSpecialChatRound indicates an expected call of GetSpecialChatRound.
func (mr *MockChatManagerMockRecorder) GetSpecialChatRound(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSpecialChatRound", reflect.TypeOf((*MockChatManager)(nil).GetSpecialChatRound), arg0, arg1, arg2)
}

// GetTotalChatRound mocks base method.
func (m *MockChatManager) GetTotalChatRound(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTotalChatRound", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTotalChatRound indicates an expected call of GetTotalChatRound.
func (mr *MockChatManagerMockRecorder) GetTotalChatRound(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTotalChatRound", reflect.TypeOf((*MockChatManager)(nil).GetTotalChatRound), arg0, arg1)
}

// IncrChatRound mocks base method.
func (m *MockChatManager) IncrChatRound(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrChatRound", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrChatRound indicates an expected call of IncrChatRound.
func (mr *MockChatManagerMockRecorder) IncrChatRound(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrChatRound", reflect.TypeOf((*MockChatManager)(nil).IncrChatRound), arg0, arg1, arg2)
}

// UpdateChatTime mocks base method.
func (m *MockChatManager) UpdateChatTime(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateChatTime", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateChatTime indicates an expected call of UpdateChatTime.
func (mr *MockChatManagerMockRecorder) UpdateChatTime(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateChatTime", reflect.TypeOf((*MockChatManager)(nil).UpdateChatTime), arg0, arg1, arg2)
}
