// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-account/internal/mgr (interfaces: AIAccountManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	entity "golang.52tt.com/services/aigc/aigc-account/internal/entity"
)

// MockAIAccountManager is a mock of AIAccountManager interface.
type MockAIAccountManager struct {
	ctrl     *gomock.Controller
	recorder *MockAIAccountManagerMockRecorder
}

// MockAIAccountManagerMockRecorder is the mock recorder for MockAIAccountManager.
type MockAIAccountManagerMockRecorder struct {
	mock *MockAIAccountManager
}

// NewMockAIAccountManager creates a new mock instance.
func NewMockAIAccountManager(ctrl *gomock.Controller) *MockAIAccountManager {
	mock := &MockAIAccountManager{ctrl: ctrl}
	mock.recorder = &MockAIAccountManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAIAccountManager) EXPECT() *MockAIAccountManagerMockRecorder {
	return m.recorder
}

// BatchGetAIAccount mocks base method.
func (m *MockAIAccountManager) BatchGetAIAccount(arg0 context.Context, arg1 []uint32) (entity.AIAccountList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAIAccount", arg0, arg1)
	ret0, _ := ret[0].(entity.AIAccountList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAIAccount indicates an expected call of BatchGetAIAccount.
func (mr *MockAIAccountManagerMockRecorder) BatchGetAIAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAIAccount", reflect.TypeOf((*MockAIAccountManager)(nil).BatchGetAIAccount), arg0, arg1)
}

// BatchUnregisterAIAccount mocks base method.
func (m *MockAIAccountManager) BatchUnregisterAIAccount(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUnregisterAIAccount", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUnregisterAIAccount indicates an expected call of BatchUnregisterAIAccount.
func (mr *MockAIAccountManagerMockRecorder) BatchUnregisterAIAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUnregisterAIAccount", reflect.TypeOf((*MockAIAccountManager)(nil).BatchUnregisterAIAccount), arg0, arg1)
}

// CreateAIAccount mocks base method.
func (m *MockAIAccountManager) CreateAIAccount(arg0 context.Context, arg1 *entity.AIAccount) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAIAccount", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAIAccount indicates an expected call of CreateAIAccount.
func (mr *MockAIAccountManagerMockRecorder) CreateAIAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAIAccount", reflect.TypeOf((*MockAIAccountManager)(nil).CreateAIAccount), arg0, arg1)
}

// GetAIAccount mocks base method.
func (m *MockAIAccountManager) GetAIAccount(arg0 context.Context, arg1 uint32) (*entity.AIAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAIAccount", arg0, arg1)
	ret0, _ := ret[0].(*entity.AIAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAIAccount indicates an expected call of GetAIAccount.
func (mr *MockAIAccountManagerMockRecorder) GetAIAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAIAccount", reflect.TypeOf((*MockAIAccountManager)(nil).GetAIAccount), arg0, arg1)
}

// GetAIAccountsBySex mocks base method.
func (m *MockAIAccountManager) GetAIAccountsBySex(arg0 context.Context, arg1 int32, arg2 int64) (entity.AIAccountList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAIAccountsBySex", arg0, arg1, arg2)
	ret0, _ := ret[0].(entity.AIAccountList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAIAccountsBySex indicates an expected call of GetAIAccountsBySex.
func (mr *MockAIAccountManagerMockRecorder) GetAIAccountsBySex(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAIAccountsBySex", reflect.TypeOf((*MockAIAccountManager)(nil).GetAIAccountsBySex), arg0, arg1, arg2)
}

// GetPageAIAccount mocks base method.
func (m *MockAIAccountManager) GetPageAIAccount(arg0 context.Context, arg1, arg2, arg3 uint32) (entity.AIAccountList, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPageAIAccount", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(entity.AIAccountList)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetPageAIAccount indicates an expected call of GetPageAIAccount.
func (mr *MockAIAccountManagerMockRecorder) GetPageAIAccount(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPageAIAccount", reflect.TypeOf((*MockAIAccountManager)(nil).GetPageAIAccount), arg0, arg1, arg2, arg3)
}

// SyncAiAccountGender mocks base method.
func (m *MockAIAccountManager) SyncAiAccountGender(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncAiAccountGender", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SyncAiAccountGender indicates an expected call of SyncAiAccountGender.
func (mr *MockAIAccountManagerMockRecorder) SyncAiAccountGender(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncAiAccountGender", reflect.TypeOf((*MockAIAccountManager)(nil).SyncAiAccountGender), arg0)
}

// UpdateAIAccount mocks base method.
func (m *MockAIAccountManager) UpdateAIAccount(arg0 context.Context, arg1 *entity.AIAccount) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAIAccount", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAIAccount indicates an expected call of UpdateAIAccount.
func (mr *MockAIAccountManagerMockRecorder) UpdateAIAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAIAccount", reflect.TypeOf((*MockAIAccountManager)(nil).UpdateAIAccount), arg0, arg1)
}
