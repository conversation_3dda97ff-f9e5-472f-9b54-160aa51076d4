package account

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"golang.52tt.com/services/aigc/aigc-account/internal/entity"
	"golang.52tt.com/services/aigc/aigc-account/internal/store/mocks"
)

func TestManager_CreateAIAccount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockStore := mocks.NewMockAIAccountStore(ctrl)

	aiAccount := &entity.AIAccount{
		Uid:      1001,
		Password: "password",
		IP:       "***********",
		PromptId: 1,
		TimbreId: 1,
		RoleId:   1,
		Prologue: "test_prologue",
	}

	ctx := context.Background()

	// 正常情况
	mockStore.EXPECT().Add(gomock.Any(), aiAccount).Return(nil)

	mgr := NewManager(mockStore, nil)
	err := mgr.CreateAIAccount(ctx, aiAccount)
	if err != nil {
		t.<PERSON>rrorf("CreateAIAccount() error = %v", err)
	}

	// 异常情况：存储层返回错误
	mockErr := errors.New("mock error")
	mockStore.EXPECT().Add(gomock.Any(), aiAccount).Return(mockErr)

	err = mgr.CreateAIAccount(ctx, aiAccount)
	if err == nil {
		t.Errorf("CreateAIAccount() expected error, got nil")
	}
	if err != mockErr {
		t.Errorf("CreateAIAccount() error = %v, want %v", err, mockErr)
	}
}

func TestManager_UpdateAIAccount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockStore := mocks.NewMockAIAccountStore(ctrl)

	aiAccount := &entity.AIAccount{
		Uid:      1001,
		Password: "new_password",
		IP:       "***********",
		PromptId: 2,
		TimbreId: 2,
		RoleId:   2,
		Prologue: "new_prologue",
	}

	ctx := context.Background()

	// 正常情况
	mockStore.EXPECT().Update(gomock.Any(), aiAccount).Return(nil)

	mgr := NewManager(mockStore, nil)
	err := mgr.UpdateAIAccount(ctx, aiAccount)
	if err != nil {
		t.Errorf("UpdateAIAccount() error = %v", err)
	}

	// 异常情况：存储层返回错误
	mockErr := errors.New("mock error")
	mockStore.EXPECT().Update(gomock.Any(), aiAccount).Return(mockErr)

	err = mgr.UpdateAIAccount(ctx, aiAccount)
	if err == nil {
		t.Errorf("UpdateAIAccount() expected error, got nil")
	}
	if err != mockErr {
		t.Errorf("UpdateAIAccount() error = %v, want %v", err, mockErr)
	}
}

// func TestManager_BatchDeleteAIAccount(t *testing.T) {
// 	ctrl := gomock.NewController(t)
// 	defer ctrl.Finish()
//
// 	mockStore := mocks.NewMockAIAccountStore(ctrl)
//
// 	idList := []uint32{1001, 1002}
// 	ctx := context.Background()
//
// 	// 正常情况
// 	mockStore.EXPECT().BatchDelete(gomock.Any(), idList).Return(nil)
//
// 	mgr := NewManager(mockStore)
// 	err := mgr.BatchDeleteAIAccount(ctx, idList)
// 	if err != nil {
// 		t.Errorf("BatchDeleteAIAccount() error = %v", err)
// 	}
//
// 	// 空列表情况
// 	err = mgr.BatchDeleteAIAccount(ctx, []uint32{})
// 	if err != nil {
// 		t.Errorf("BatchDeleteAIAccount() with empty list error = %v", err)
// 	}
//
// 	// 异常情况：存储层返回错误
// 	mockErr := errors.New("mock error")
// 	mockStore.EXPECT().BatchDelete(gomock.Any(), idList).Return(mockErr)
//
// 	err = mgr.BatchDeleteAIAccount(ctx, idList)
// 	if err == nil {
// 		t.Errorf("BatchDeleteAIAccount() expected error, got nil")
// 	}
// 	if err != mockErr {
// 		t.Errorf("BatchDeleteAIAccount() error = %v, want %v", err, mockErr)
// 	}
// }

func TestManager_GetAIAccount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockStore := mocks.NewMockAIAccountStore(ctrl)

	aiAccount := &entity.AIAccount{
		Uid:      1001,
		Password: "password",
		IP:       "***********",
		PromptId: 1,
		TimbreId: 1,
		RoleId:   1,
		Prologue: "test_prologue",
	}

	ctx := context.Background()

	// 正常情况
	mockStore.EXPECT().Get(gomock.Any(), uint32(1001)).Return(aiAccount, nil)

	mgr := NewManager(mockStore, nil)
	got, err := mgr.GetAIAccount(ctx, 1001)
	if err != nil {
		t.Errorf("GetAIAccount() error = %v", err)
	}
	if got != aiAccount {
		t.Errorf("GetAIAccount() = %v, want %v", got, aiAccount)
	}

	// 异常情况：存储层返回错误
	mockErr := errors.New("mock error")
	mockStore.EXPECT().Get(gomock.Any(), uint32(1001)).Return(nil, mockErr)

	got, err = mgr.GetAIAccount(ctx, 1001)
	if err == nil {
		t.Errorf("GetAIAccount() expected error, got nil")
	}
	if got != nil {
		t.Errorf("GetAIAccount() = %v, want nil", got)
	}
	if err != mockErr {
		t.Errorf("GetAIAccount() error = %v, want %v", err, mockErr)
	}

	// 账号不存在情况
	mockStore.EXPECT().Get(gomock.Any(), uint32(9999)).Return(nil, nil)

	got, err = mgr.GetAIAccount(ctx, 9999)
	if err != nil {
		t.Errorf("GetAIAccount() error = %v, want nil", err)
	}
	if got != nil {
		t.Errorf("GetAIAccount() = %v, want nil", got)
	}
}

func TestManager_BatchGetAIAccount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockStore := mocks.NewMockAIAccountStore(ctrl)

	accounts := entity.AIAccountList{
		{
			Uid:      1001,
			Password: "password1",
			IP:       "***********",
			PromptId: 1,
			TimbreId: 1,
			RoleId:   1,
			Prologue: "test_prologue",
		},
		{
			Uid:      1002,
			Password: "password2",
			IP:       "***********",
			PromptId: 2,
			TimbreId: 2,
			RoleId:   2,
			Prologue: "test_prologue",
		},
	}

	idList := []uint32{1001, 1002}
	ctx := context.Background()

	// 正常情况
	mockStore.EXPECT().BatchGet(gomock.Any(), idList).Return(accounts, nil)

	mgr := NewManager(mockStore, nil)
	got, err := mgr.BatchGetAIAccount(ctx, idList)
	if err != nil {
		t.Errorf("BatchGetAIAccount() error = %v", err)
	}
	if len(got) != len(accounts) {
		t.Errorf("BatchGetAIAccount() = %v, want %v", got, accounts)
	}

	// 空列表情况
	got, err = mgr.BatchGetAIAccount(ctx, []uint32{})
	if err != nil {
		t.Errorf("BatchGetAIAccount() with empty list error = %v", err)
	}
	if got != nil {
		t.Errorf("BatchGetAIAccount() with empty list = %v, want nil", got)
	}

	// 异常情况：存储层返回错误
	mockErr := errors.New("mock error")
	mockStore.EXPECT().BatchGet(gomock.Any(), idList).Return(nil, mockErr)

	got, err = mgr.BatchGetAIAccount(ctx, idList)
	if err == nil {
		t.Errorf("BatchGetAIAccount() expected error, got nil")
	}
	if got != nil {
		t.Errorf("BatchGetAIAccount() = %v, want nil", got)
	}
	if err != mockErr {
		t.Errorf("BatchGetAIAccount() error = %v, want %v", err, mockErr)
	}

	// 返回空结果情况
	mockStore.EXPECT().BatchGet(gomock.Any(), idList).Return(entity.AIAccountList{}, nil)

	got, err = mgr.BatchGetAIAccount(ctx, idList)
	if err != nil {
		t.Errorf("BatchGetAIAccount() error = %v, want nil", err)
	}
	if got == nil || len(got) != 0 {
		t.Errorf("BatchGetAIAccount() = %v, want empty list", got)
	}
}

func TestManager_GetPageAIAccount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockStore := mocks.NewMockAIAccountStore(ctrl)

	accounts := entity.AIAccountList{
		{
			Uid:      1001,
			Password: "password1",
			IP:       "***********",
			PromptId: 1,
			TimbreId: 1,
			RoleId:   1,
			Prologue: "test_prologue",
		},
		{
			Uid:      1002,
			Password: "password2",
			IP:       "***********",
			PromptId: 2,
			TimbreId: 2,
			RoleId:   2,
			Prologue: "test_prologue",
		},
	}

	ctx := context.Background()

	// 正常情况
	mockStore.EXPECT().GetByPage(gomock.Any(), int64(1), int64(10), gomock.Any()).Return(accounts, int64(100), nil)

	mgr := NewManager(mockStore, nil)
	got, total, err := mgr.GetPageAIAccount(ctx, 1, 10, 0)
	if err != nil {
		t.Errorf("GetPageAIAccount() error = %v", err)
	}
	if len(got) != len(accounts) {
		t.Errorf("GetPageAIAccount() = %v, want %v", got, accounts)
	}
	if total != 100 {
		t.Errorf("GetPageAIAccount() total = %v, want %v", total, 100)
	}

	// 参数调整情况
	mockStore.EXPECT().GetByPage(gomock.Any(), int64(1), int64(100), gomock.Any()).Return(accounts, int64(100), nil)
	got, total, err = mgr.GetPageAIAccount(ctx, 0, 0, 0)
	if err != nil {
		t.Errorf("GetPageAIAccount() with adjusted params error = %v", err)
	}
	if len(got) != len(accounts) {
		t.Errorf("GetPageAIAccount() with adjusted params = %v, want %v", got, accounts)
	}
	if total != 100 {
		t.Errorf("GetPageAIAccount() with adjusted params total = %v, want %v", total, 100)
	}

	// 超大size情况
	mockStore.EXPECT().GetByPage(gomock.Any(), int64(1), int64(100), gomock.Any()).Return(accounts, int64(100), nil)
	got, total, err = mgr.GetPageAIAccount(ctx, 1, 200, 0)
	if err != nil {
		t.Errorf("GetPageAIAccount() with large size error = %v", err)
	}
	if len(got) != len(accounts) {
		t.Errorf("GetPageAIAccount() with large size = %v, want %v", got, accounts)
	}
	if total != 100 {
		t.Errorf("GetPageAIAccount() with large size total = %v, want %v", total, 100)
	}

	// 异常情况：存储层返回错误
	mockErr := errors.New("mock error")
	mockStore.EXPECT().GetByPage(gomock.Any(), int64(1), int64(10), gomock.Any()).Return(nil, int64(0), mockErr)

	got, total, err = mgr.GetPageAIAccount(ctx, 1, 10, 0)
	if err == nil {
		t.Errorf("GetPageAIAccount() expected error, got nil")
	}
	if got != nil {
		t.Errorf("GetPageAIAccount() = %v, want nil", got)
	}
	if total != 0 {
		t.Errorf("GetPageAIAccount() total = %v, want 0", total)
	}
	if err != mockErr {
		t.Errorf("GetPageAIAccount() error = %v, want %v", err, mockErr)
	}

	// 返回空结果情况
	mockStore.EXPECT().GetByPage(gomock.Any(), int64(1), int64(10), gomock.Any()).Return(entity.AIAccountList{}, int64(0), nil)

	got, total, err = mgr.GetPageAIAccount(ctx, 1, 10, 0)
	if err != nil {
		t.Errorf("GetPageAIAccount() error = %v, want nil", err)
	}
	if got == nil || len(got) != 0 {
		t.Errorf("GetPageAIAccount() = %v, want empty list", got)
	}
	if total != 0 {
		t.Errorf("GetPageAIAccount() total = %v, want 0", total)
	}
}

func TestManager_BatchUnregisterAIAccount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockStore := mocks.NewMockAIAccountStore(ctrl)

	idList := []uint32{2001, 2002}
	ctx := context.Background()

	// 正常情况
	mockStore.EXPECT().BatchUnregister(gomock.Any(), idList).Return(nil)

	mgr := NewManager(mockStore, nil)
	err := mgr.BatchUnregisterAIAccount(ctx, idList)
	if err != nil {
		t.Errorf("BatchUnregisterAIAccount() error = %v", err)
	}

	// 空列表情况
	err = mgr.BatchUnregisterAIAccount(ctx, []uint32{})
	if err != nil {
		t.Errorf("BatchUnregisterAIAccount() with empty list error = %v", err)
	}

	// 异常情况：存储层返回错误
	mockErr := errors.New("mock error")
	mockStore.EXPECT().BatchUnregister(gomock.Any(), idList).Return(mockErr)

	err = mgr.BatchUnregisterAIAccount(ctx, idList)
	if err == nil {
		t.Errorf("BatchUnregisterAIAccount() expected error, got nil")
	}
	if err != mockErr {
		t.Errorf("BatchUnregisterAIAccount() error = %v, want %v", err, mockErr)
	}
}
