package account

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/aigc/aigc-account/internal/entity"
	"golang.52tt.com/services/aigc/aigc-account/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-account/internal/rpc"
	"golang.52tt.com/services/aigc/aigc-account/internal/store"
)

type manager struct {
	clients      *rpc.Clients
	accountStore store.AIAccountStore
}

func NewManager(accountStore store.AIAccountStore, clients *rpc.Clients) mgr.AIAccountManager {
	return &manager{
		accountStore: accountStore,
		clients:      clients,
	}
}

// CreateAIAccount 创建AI账号
func (mgr *manager) CreateAIAccount(ctx context.Context, aiAccount *entity.AIAccount) error {
	err := mgr.accountStore.Add(ctx, aiAccount)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateAIAccount Add account(%+v) err: %v", aiAccount, err)
		return err
	}

	log.InfoWithCtx(ctx, "CreateAIAccount success, account: %+v", aiAccount)
	return nil
}

// UpdateAIAccount 更新AI账号
func (mgr *manager) UpdateAIAccount(ctx context.Context, aiAccount *entity.AIAccount) error {
	err := mgr.accountStore.Update(ctx, aiAccount)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAIAccount Update account(%+v) err: %v", aiAccount, err)
		return err
	}

	log.InfoWithCtx(ctx, "UpdateAIAccount success, account: %+v", aiAccount)
	return nil
}

// BatchDeleteAIAccount 批量删除AI账号
//func (mgr *manager) BatchDeleteAIAccount(ctx context.Context, idList []uint32) error {
//	if len(idList) == 0 {
//		return nil
//	}
//
//	err := mgr.accountStore.BatchDelete(ctx, idList)
//	if err != nil {
//		log.ErrorWithCtx(ctx, "BatchDeleteAIAccount BatchDelete idList(%+v) err: %v", idList, err)
//		return err
//	}
//
//	log.InfoWithCtx(ctx, "BatchDeleteAIAccount success, idList: %+v", idList)
//	return nil
//}

// BatchUnregisterAIAccount 批量注销AI账号
func (mgr *manager) BatchUnregisterAIAccount(ctx context.Context, idList []uint32) error {
	if len(idList) == 0 {
		return nil
	}

	err := mgr.accountStore.BatchUnregister(ctx, idList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchUnregisterAIAccount BatchUnregister idList(%+v) err: %v", idList, err)
		return err
	}

	log.InfoWithCtx(ctx, "BatchUnregisterAIAccount success, idList: %+v", idList)
	return nil
}

// GetPageAIAccount 分页获取AI账号列表
func (mgr *manager) GetPageAIAccount(ctx context.Context, page, size uint32, chatCardWallShowStatus uint32) (entity.AIAccountList, uint32, error) {
	if page == 0 {
		page = 1
	}
	if size == 0 || size > 100 {
		size = 100
	}
	accounts, total, err := mgr.accountStore.GetByPage(ctx, int64(page), int64(size), chatCardWallShowStatus)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPageAIAccount GetByPage page(%d) size(%d) err: %v", page, size, err)
		return nil, 0, err
	}

	return accounts, uint32(total), nil
}

// GetAIAccount 获取单个AI账号
func (mgr *manager) GetAIAccount(ctx context.Context, id uint32) (*entity.AIAccount, error) {
	account, err := mgr.accountStore.Get(ctx, id)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAIAccount Get id(%d) err: %v", id, err)
		return nil, err
	}

	return account, nil
}

// BatchGetAIAccount 批量获取AI账号
func (mgr *manager) BatchGetAIAccount(ctx context.Context, idList []uint32) (entity.AIAccountList, error) {
	if len(idList) == 0 {
		return nil, nil
	}

	accounts, err := mgr.accountStore.BatchGet(ctx, idList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetAIAccount BatchGet idList(%+v) err: %v", idList, err)
		return nil, err
	}

	return accounts, nil
}

func (mgr *manager) GetAIAccountsBySex(ctx context.Context, sex int32, limit int64) (entity.AIAccountList, error) {
	accounts, err := mgr.accountStore.GetAIAccountsBySex(ctx, sex, limit)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAIAccountsBySex GetAIAccountsBySex sex(%d) err: %v", sex, err)
		return nil, err
	}

	return accounts, nil
}

func (mgr *manager) SyncAiAccountGender(ctx context.Context) error {
	// 以分页的形式循环获取ai账号列表
	page := 1
	size := 100
	for {
		accounts, total, err := mgr.accountStore.GetByPage(ctx, int64(page), int64(size), 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "SyncAiAccountGender GetPageAIAccount err: %v", err)
			return err
		}
		if len(accounts) == 0 {
			break
		}

		err = mgr.updateAiAccountGender(ctx, accounts)
		if err != nil {
			log.ErrorWithCtx(ctx, "SyncAiAccountGender updateAiAccountGender err: %v", err)
			return err
		}

		log.InfoWithCtx(ctx, "SyncAiAccountGender page: %d, size: %d, total: %d", page, size, total)

		if len(accounts) < size {
			break
		}
		if int64(page*size) >= total {
			break
		}

		page++
	}

	return nil
}

func (mgr *manager) updateAiAccountGender(ctx context.Context, accountList []*entity.AIAccount) error {
	if len(accountList) == 0 {
		log.WarnWithCtx(ctx, "updateAiAccountGender accountList is empty")
		return nil
	}

	uidList := make([]uint32, 0, len(accountList))
	for _, account := range accountList {
		uidList = append(uidList, account.Uid)
	}

	// 批量获取账号信息
	userMap, err := mgr.clients.AccountGo.GetUsersMap(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "updateAiAccountGender GetUsersMap err: %v, uidList: %+v", err, uidList)
		return err
	}

	for _, account := range accountList {
		if _, ok := userMap[account.Uid]; !ok {
			log.WarnWithCtx(ctx, "updateAiAccountGender userMap not found for uid: %d", account.Uid)
			continue
		}

		err := mgr.accountStore.UpdateSex(ctx, account.Uid, userMap[account.Uid].GetSex())
		if err != nil {
			log.ErrorWithCtx(ctx, "updateAiAccountGender UpdateSex err: %v, uid: %d, sex: %d", err, account.Uid, account.Sex)
			return err
		}
	}

	return nil
}
