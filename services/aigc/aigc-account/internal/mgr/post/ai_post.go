package post

import (
	"context"
	"golang.52tt.com/services/aigc/aigc-account/internal/cache"
	"golang.52tt.com/services/aigc/aigc-account/internal/entity"
	"golang.52tt.com/services/aigc/aigc-account/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-account/internal/store"
)

type manager struct {
	postStore   store.AIPostStore
	chatAiCache cache.ChatAiCache
}

func NewManager(postStore store.AIPostStore, chatAiCache cache.ChatAiCache) mgr.AiPostManager {
	return &manager{
		postStore:   postStore,
		chatAiCache: chatAiCache,
	}
}

func (m *manager) AddAiPost(ctx context.Context, uid uint32, postId string) error {
	post := &entity.AIPost{
		Uid:    uid,
		PostId: postId,
	}
	return m.postStore.Add(ctx, post)
}

func (m *manager) GetAiPostList(ctx context.Context, page, size int64, uid []uint32) ([]*entity.AIPost, int64, error) {
	return m.postStore.GetList(ctx, page, size, uid)
}
