package post

import "context"

func (m *manager) AddUserAiChatRecord(ctx context.Context, fromUid, toUid uint32) error {
	return m.chatAiCache.RecordImChat(ctx, fromUid, toUid)
}

func (m *manager) GetUserAiChatRecord(ctx context.Context, fromUid, toUid uint32) (int64, error) {
	return m.chatAiCache.GetImChatRecord(ctx, fromUid, toUid)
}

func (m *manager) AddInteraction(ctx context.Context, uid, aiUid uint32) error {
	return m.chatAiCache.AddInteraction(ctx, uid, aiUid)
}

func (m *manager) GetInteractionAiList(ctx context.Context, uid uint32) ([]uint32, error) {
	return m.chatAiCache.GetInteractionAiList(ctx, uid)
}

func (m *manager) AddLikePost(ctx context.Context, uid uint32, postId string) error {
	return m.chatAiCache.AddLikePost(ctx, uid, postId)
}

func (m *manager) GetLikePostCount(ctx context.Context, uid uint32) (int64, error) {
	return m.chatAiCache.GetLikePostCount(ctx, uid)
}

func (m *manager) AddCommentPost(ctx context.Context, uid uint32, postId string) error {
	return m.chatAiCache.AddCommentPost(ctx, uid, postId)
}

func (m *manager) GetCommentPostCount(ctx context.Context, uid uint32) (int64, error) {
	return m.chatAiCache.GetCommentPostCount(ctx, uid)
}

func (m *manager) IncrPostAiCommentCount(ctx context.Context, postId string, aiUid uint32) (int64, error) {
	return m.chatAiCache.IncrPostAiCommentCount(ctx, postId, aiUid)
}

func (m *manager) GetPostAiCommentCount(ctx context.Context, postId string, aiUid uint32) (int64, error) {
	return m.chatAiCache.GetPostAiCommentCount(ctx, postId, aiUid)
}

func (m *manager) GetPostCommentAiList(ctx context.Context, postId string) (map[uint32]int64, error) {
	return m.chatAiCache.GetPostCommentAiList(ctx, postId)
}
