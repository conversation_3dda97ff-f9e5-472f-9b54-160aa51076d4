package chat

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"golang.52tt.com/services/aigc/aigc-account/internal/cache/mocks"
	"golang.52tt.com/services/aigc/aigc-account/internal/entity"
	storemocks "golang.52tt.com/services/aigc/aigc-account/internal/store/mocks"
)

func TestManager_GetLastChatTime(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockChatTimeStore := storemocks.NewMockChatTimeStore(ctrl)
	mockChatRoundCache := mocks.NewMockChatRoundCache(ctrl)

	ctx := context.Background()
	uid := uint32(12345)
	aiUid := uint32(67890)

	// Test case 1: 正常情况 - 有记录
	chatTimeRecord := &entity.ChatTimeRecord{
		ID:           "12345_67890",
		LastSendTime: time.Unix(**********, 0), // 2022-01-01 00:00:00
	}
	mockChatTimeStore.EXPECT().GetChatTime(gomock.Any(), uid, aiUid).Return(chatTimeRecord, nil)

	mgr := NewManager(mockChatTimeStore, mockChatRoundCache)
	result, err := mgr.GetLastChatTime(ctx, uid, aiUid)
	if err != nil {
		t.Errorf("GetLastChatTime() error = %v", err)
	}
	expectedTime := int64(**********)
	if result != expectedTime {
		t.Errorf("GetLastChatTime() = %v, want %v", result, expectedTime)
	}

	// Test case 2: 正常情况 - 无记录
	mockChatTimeStore.EXPECT().GetChatTime(gomock.Any(), uid, aiUid).Return(nil, nil)

	result, err = mgr.GetLastChatTime(ctx, uid, aiUid)
	if err != nil {
		t.Errorf("GetLastChatTime() error = %v", err)
	}
	if result != 0 {
		t.Errorf("GetLastChatTime() = %v, want 0", result)
	}

	// Test case 3: 异常情况 - 存储层返回错误
	mockErr := errors.New("mock error")
	mockChatTimeStore.EXPECT().GetChatTime(gomock.Any(), uid, aiUid).Return(nil, mockErr)

	result, err = mgr.GetLastChatTime(ctx, uid, aiUid)
	if err == nil {
		t.Errorf("GetLastChatTime() expected error, got nil")
	}
	if err != mockErr {
		t.Errorf("GetLastChatTime() error = %v, want %v", err, mockErr)
	}
	if result != 0 {
		t.Errorf("GetLastChatTime() = %v, want 0", result)
	}
}

func TestManager_UpdateChatTime(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockChatTimeStore := storemocks.NewMockChatTimeStore(ctrl)
	mockChatRoundCache := mocks.NewMockChatRoundCache(ctrl)

	ctx := context.Background()
	uid := uint32(12345)
	aiUid := uint32(67890)

	// Test case 1: 正常情况
	mockChatTimeStore.EXPECT().UpdateChatTime(gomock.Any(), uid, aiUid).Return(nil)

	mgr := NewManager(mockChatTimeStore, mockChatRoundCache)
	err := mgr.UpdateChatTime(ctx, uid, aiUid)
	if err != nil {
		t.Errorf("UpdateChatTime() error = %v", err)
	}

	// Test case 2: 异常情况 - 存储层返回错误
	mockErr := errors.New("mock error")
	mockChatTimeStore.EXPECT().UpdateChatTime(gomock.Any(), uid, aiUid).Return(mockErr)

	err = mgr.UpdateChatTime(ctx, uid, aiUid)
	if err == nil {
		t.Errorf("UpdateChatTime() expected error, got nil")
	}
	if err != mockErr {
		t.Errorf("UpdateChatTime() error = %v, want %v", err, mockErr)
	}
}

func TestManager_IncrChatRound(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockChatTimeStore := storemocks.NewMockChatTimeStore(ctrl)
	mockChatRoundCache := mocks.NewMockChatRoundCache(ctrl)

	ctx := context.Background()
	uid := uint32(12345)
	aiUid := uint32(67890)

	// Test case 1: 正常情况
	mockChatRoundCache.EXPECT().IncrChatRound(gomock.Any(), uid, aiUid).Return(nil)

	mgr := NewManager(mockChatTimeStore, mockChatRoundCache)
	err := mgr.IncrChatRound(ctx, uid, aiUid)
	if err != nil {
		t.Errorf("IncrChatRound() error = %v", err)
	}

	// Test case 2: 异常情况 - 缓存层返回错误
	mockErr := errors.New("mock error")
	mockChatRoundCache.EXPECT().IncrChatRound(gomock.Any(), uid, aiUid).Return(mockErr)

	err = mgr.IncrChatRound(ctx, uid, aiUid)
	if err == nil {
		t.Errorf("IncrChatRound() expected error, got nil")
	}
	if err != mockErr {
		t.Errorf("IncrChatRound() error = %v, want %v", err, mockErr)
	}
}

func TestManager_GetSpecialChatRound(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockChatTimeStore := storemocks.NewMockChatTimeStore(ctrl)
	mockChatRoundCache := mocks.NewMockChatRoundCache(ctrl)

	ctx := context.Background()
	uid := uint32(12345)
	aiUid := uint32(67890)

	// Test case 1: 正常情况
	expectedCount := uint32(5)
	mockChatRoundCache.EXPECT().GetSpecialChatRound(gomock.Any(), uid, aiUid).Return(expectedCount, nil)

	mgr := NewManager(mockChatTimeStore, mockChatRoundCache)
	result, err := mgr.GetSpecialChatRound(ctx, uid, aiUid)
	if err != nil {
		t.Errorf("GetSpecialChatRound() error = %v", err)
	}
	if result != expectedCount {
		t.Errorf("GetSpecialChatRound() = %v, want %v", result, expectedCount)
	}

	// Test case 2: 异常情况 - 缓存层返回错误
	mockErr := errors.New("mock error")
	mockChatRoundCache.EXPECT().GetSpecialChatRound(gomock.Any(), uid, aiUid).Return(uint32(0), mockErr)

	result, err = mgr.GetSpecialChatRound(ctx, uid, aiUid)
	if err == nil {
		t.Errorf("GetSpecialChatRound() expected error, got nil")
	}
	if err != mockErr {
		t.Errorf("GetSpecialChatRound() error = %v, want %v", err, mockErr)
	}
	if result != 0 {
		t.Errorf("GetSpecialChatRound() = %v, want 0", result)
	}
}

func TestManager_GetTotalChatRound(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockChatTimeStore := storemocks.NewMockChatTimeStore(ctrl)
	mockChatRoundCache := mocks.NewMockChatRoundCache(ctrl)

	ctx := context.Background()
	uid := uint32(12345)

	// Test case 1: 正常情况
	expectedCount := uint32(10)
	mockChatRoundCache.EXPECT().GetTotalChatRound(gomock.Any(), uid).Return(expectedCount, nil)

	mgr := NewManager(mockChatTimeStore, mockChatRoundCache)
	result, err := mgr.GetTotalChatRound(ctx, uid)
	if err != nil {
		t.Errorf("GetTotalChatRound() error = %v", err)
	}
	if result != expectedCount {
		t.Errorf("GetTotalChatRound() = %v, want %v", result, expectedCount)
	}

	// Test case 2: 异常情况 - 缓存层返回错误
	mockErr := errors.New("mock error")
	mockChatRoundCache.EXPECT().GetTotalChatRound(gomock.Any(), uid).Return(uint32(0), mockErr)

	result, err = mgr.GetTotalChatRound(ctx, uid)
	if err == nil {
		t.Errorf("GetTotalChatRound() expected error, got nil")
	}
	if err != mockErr {
		t.Errorf("GetTotalChatRound() error = %v, want %v", err, mockErr)
	}
	if result != 0 {
		t.Errorf("GetTotalChatRound() = %v, want 0", result)
	}
}

func TestNewManager(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockChatTimeStore := storemocks.NewMockChatTimeStore(ctrl)
	mockChatRoundCache := mocks.NewMockChatRoundCache(ctrl)

	mgr := NewManager(mockChatTimeStore, mockChatRoundCache)
	if mgr == nil {
		t.Errorf("NewManager() = nil, want non-nil")
	}

	// Verify that the manager implements the interface
	_, ok := mgr.(*manager)
	if !ok {
		t.Errorf("NewManager() returned wrong type")
	}
}
