package chat

import (
	"context"
	"golang.52tt.com/services/aigc/aigc-account/internal/cache"
	"golang.52tt.com/services/aigc/aigc-account/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-account/internal/store"
)

type manager struct {
	chatTimeStore  store.ChatTimeStore
	chatRoundCache cache.ChatRoundCache
}

func NewManager(chatTimeStore store.ChatTimeStore, chatRoundCache cache.ChatRoundCache) mgr.ChatManager {
	return &manager{
		chatRoundCache: chatRoundCache,
		chatTimeStore:  chatTimeStore,
	}
}

func (m *manager) GetLastChatTime(ctx context.Context, uid, aiUid uint32) (int64, error) {
	record, err := m.chatTimeStore.GetChatTime(ctx, uid, aiUid)
	if err != nil {
		return 0, err
	}
	if record == nil {
		return 0, nil // No record found
	}
	return record.LastSendTime.Unix(), nil
}

func (m *manager) UpdateChatTime(ctx context.Context, uid, aiUid uint32) error {
	return m.chatTimeStore.UpdateChatTime(ctx, uid, aiUid)
}

func (m *manager) IncrChatRound(ctx context.Context, uid, aiUid uint32) error {
	return m.chatRoundCache.IncrChatRound(ctx, uid, aiUid)
}

func (m *manager) GetSpecialChatRound(ctx context.Context, uid, aiUid uint32) (uint32, error) {
	return m.chatRoundCache.GetSpecialChatRound(ctx, uid, aiUid)
}

func (m *manager) GetTotalChatRound(ctx context.Context, uid uint32) (uint32, error) {
	return m.chatRoundCache.GetTotalChatRound(ctx, uid)

}
