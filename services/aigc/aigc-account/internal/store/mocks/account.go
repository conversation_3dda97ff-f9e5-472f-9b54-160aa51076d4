// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-account/internal/store (interfaces: AIAccountStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	entity "golang.52tt.com/services/aigc/aigc-account/internal/entity"
)

// MockAIAccountStore is a mock of AIAccountStore interface.
type MockAIAccountStore struct {
	ctrl     *gomock.Controller
	recorder *MockAIAccountStoreMockRecorder
}

// MockAIAccountStoreMockRecorder is the mock recorder for MockAIAccountStore.
type MockAIAccountStoreMockRecorder struct {
	mock *MockAIAccountStore
}

// NewMockAIAccountStore creates a new mock instance.
func NewMockAIAccountStore(ctrl *gomock.Controller) *MockAIAccountStore {
	mock := &MockAIAccountStore{ctrl: ctrl}
	mock.recorder = &MockAIAccountStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAIAccountStore) EXPECT() *MockAIAccountStoreMockRecorder {
	return m.recorder
}

// Add mocks base method.
func (m *MockAIAccountStore) Add(arg0 context.Context, arg1 *entity.AIAccount) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Add", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Add indicates an expected call of Add.
func (mr *MockAIAccountStoreMockRecorder) Add(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Add", reflect.TypeOf((*MockAIAccountStore)(nil).Add), arg0, arg1)
}

// BatchDelete mocks base method.
func (m *MockAIAccountStore) BatchDelete(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDelete", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDelete indicates an expected call of BatchDelete.
func (mr *MockAIAccountStoreMockRecorder) BatchDelete(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDelete", reflect.TypeOf((*MockAIAccountStore)(nil).BatchDelete), arg0, arg1)
}

// BatchGet mocks base method.
func (m *MockAIAccountStore) BatchGet(arg0 context.Context, arg1 []uint32) (entity.AIAccountList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGet", arg0, arg1)
	ret0, _ := ret[0].(entity.AIAccountList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGet indicates an expected call of BatchGet.
func (mr *MockAIAccountStoreMockRecorder) BatchGet(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGet", reflect.TypeOf((*MockAIAccountStore)(nil).BatchGet), arg0, arg1)
}

// BatchUnregister mocks base method.
func (m *MockAIAccountStore) BatchUnregister(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUnregister", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUnregister indicates an expected call of BatchUnregister.
func (mr *MockAIAccountStoreMockRecorder) BatchUnregister(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUnregister", reflect.TypeOf((*MockAIAccountStore)(nil).BatchUnregister), arg0, arg1)
}

// Get mocks base method.
func (m *MockAIAccountStore) Get(arg0 context.Context, arg1 uint32) (*entity.AIAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", arg0, arg1)
	ret0, _ := ret[0].(*entity.AIAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockAIAccountStoreMockRecorder) Get(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockAIAccountStore)(nil).Get), arg0, arg1)
}

// GetAIAccountsBySex mocks base method.
func (m *MockAIAccountStore) GetAIAccountsBySex(arg0 context.Context, arg1 int32, arg2 int64) (entity.AIAccountList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAIAccountsBySex", arg0, arg1, arg2)
	ret0, _ := ret[0].(entity.AIAccountList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAIAccountsBySex indicates an expected call of GetAIAccountsBySex.
func (mr *MockAIAccountStoreMockRecorder) GetAIAccountsBySex(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAIAccountsBySex", reflect.TypeOf((*MockAIAccountStore)(nil).GetAIAccountsBySex), arg0, arg1, arg2)
}

// GetByPage mocks base method.
func (m *MockAIAccountStore) GetByPage(arg0 context.Context, arg1, arg2 int64, arg3 uint32) (entity.AIAccountList, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByPage", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(entity.AIAccountList)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetByPage indicates an expected call of GetByPage.
func (mr *MockAIAccountStoreMockRecorder) GetByPage(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByPage", reflect.TypeOf((*MockAIAccountStore)(nil).GetByPage), arg0, arg1, arg2, arg3)
}

// Update mocks base method.
func (m *MockAIAccountStore) Update(arg0 context.Context, arg1 *entity.AIAccount) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockAIAccountStoreMockRecorder) Update(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockAIAccountStore)(nil).Update), arg0, arg1)
}

// UpdateSex mocks base method.
func (m *MockAIAccountStore) UpdateSex(arg0 context.Context, arg1 uint32, arg2 int32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSex", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSex indicates an expected call of UpdateSex.
func (mr *MockAIAccountStoreMockRecorder) UpdateSex(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSex", reflect.TypeOf((*MockAIAccountStore)(nil).UpdateSex), arg0, arg1, arg2)
}
