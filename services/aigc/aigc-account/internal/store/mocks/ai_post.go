// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-account/internal/store (interfaces: AIPostStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	entity "golang.52tt.com/services/aigc/aigc-account/internal/entity"
)

// MockAIPostStore is a mock of AIPostStore interface.
type MockAIPostStore struct {
	ctrl     *gomock.Controller
	recorder *MockAIPostStoreMockRecorder
}

// MockAIPostStoreMockRecorder is the mock recorder for MockAIPostStore.
type MockAIPostStoreMockRecorder struct {
	mock *MockAIPostStore
}

// NewMockAIPostStore creates a new mock instance.
func NewMockAIPostStore(ctrl *gomock.Controller) *MockAIPostStore {
	mock := &MockAIPostStore{ctrl: ctrl}
	mock.recorder = &MockAIPostStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAIPostStore) EXPECT() *MockAIPostStoreMockRecorder {
	return m.recorder
}

// Add mocks base method.
func (m *MockAIPostStore) Add(arg0 context.Context, arg1 *entity.AIPost) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Add", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Add indicates an expected call of Add.
func (mr *MockAIPostStoreMockRecorder) Add(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Add", reflect.TypeOf((*MockAIPostStore)(nil).Add), arg0, arg1)
}

// GetList mocks base method.
func (m *MockAIPostStore) GetList(arg0 context.Context, arg1, arg2 int64, arg3 []uint32) ([]*entity.AIPost, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*entity.AIPost)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetList indicates an expected call of GetList.
func (mr *MockAIPostStoreMockRecorder) GetList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetList", reflect.TypeOf((*MockAIPostStore)(nil).GetList), arg0, arg1, arg2, arg3)
}
