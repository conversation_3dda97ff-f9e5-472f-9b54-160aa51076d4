// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-account/internal/store (interfaces: ChatTimeStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	entity "golang.52tt.com/services/aigc/aigc-account/internal/entity"
)

// MockChatTimeStore is a mock of ChatTimeStore interface.
type MockChatTimeStore struct {
	ctrl     *gomock.Controller
	recorder *MockChatTimeStoreMockRecorder
}

// MockChatTimeStoreMockRecorder is the mock recorder for MockChatTimeStore.
type MockChatTimeStoreMockRecorder struct {
	mock *MockChatTimeStore
}

// NewMockChatTimeStore creates a new mock instance.
func NewMockChatTimeStore(ctrl *gomock.Controller) *MockChatTimeStore {
	mock := &MockChatTimeStore{ctrl: ctrl}
	mock.recorder = &MockChatTimeStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChatTimeStore) EXPECT() *MockChatTimeStoreMockRecorder {
	return m.recorder
}

// GetChatTime mocks base method.
func (m *MockChatTimeStore) GetChatTime(arg0 context.Context, arg1, arg2 uint32) (*entity.ChatTimeRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChatTime", arg0, arg1, arg2)
	ret0, _ := ret[0].(*entity.ChatTimeRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChatTime indicates an expected call of GetChatTime.
func (mr *MockChatTimeStoreMockRecorder) GetChatTime(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChatTime", reflect.TypeOf((*MockChatTimeStore)(nil).GetChatTime), arg0, arg1, arg2)
}

// UpdateChatTime mocks base method.
func (m *MockChatTimeStore) UpdateChatTime(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateChatTime", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateChatTime indicates an expected call of UpdateChatTime.
func (mr *MockChatTimeStoreMockRecorder) UpdateChatTime(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateChatTime", reflect.TypeOf((*MockChatTimeStore)(nil).UpdateChatTime), arg0, arg1, arg2)
}
