package mongo

import (
	"context"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/aigc/aigc-account/internal/entity"
	"golang.52tt.com/services/aigc/aigc-account/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-account/internal/store"
)

const (
	collectionAIAccount = "ai_account"
	DisplayChatCardWall = 1
	HideChatCardWall    = 2
)

type aiAccountMongoStore struct {
	col *mongo.Collection
}

func NewAIAccountMongoStore(ctx context.Context, mongoDB *db.MongoDB) store.AIAccountStore {
	st := &aiAccountMongoStore{
		col: mongoDB.Database().Collection(collectionAIAccount),
	}

	// 创建索引
	_, err := st.col.Indexes().CreateMany(ctx, []mongo.IndexModel{
		// 扩列墙筛选索引（有筛选条件时使用）
		{
			Keys: bson.D{
				{Key: "is_unregister", Value: 1},
				{Key: "is_show_in_chat_card_wall", Value: 1},
				{Key: "sort", Value: 1},
				{Key: "update_time", Value: -1},
			},
		},
		{
			Keys: bson.D{
				{Key: "is_unregister", Value: 1},
				{Key: "sort", Value: 1},
				{Key: "update_time", Value: -1},
			},
		},
	})
	if err != nil {
		log.WarnWithCtx(ctx, "NewAIAccountMongoStore collection(ai_account) CreateMany err: %v", err)
	}

	return st
}

func (s *aiAccountMongoStore) Add(ctx context.Context, account *entity.AIAccount) error {
	now := time.Now().Unix()
	account.CreateTime = now
	account.UpdateTime = now

	_, err := s.col.InsertOne(ctx, account)
	return err
}

func (s *aiAccountMongoStore) Update(ctx context.Context, account *entity.AIAccount) error {
	account.UpdateTime = time.Now().Unix()

	filter := bson.M{"_id": account.Uid}
	update := bson.M{"$set": bson.M{
		"update_time": account.UpdateTime,

		"password":                  account.Password,
		"ip":                        account.IP,
		"prompt_id":                 account.PromptId,
		"timbre_id":                 account.TimbreId,
		"role_id":                   account.RoleId,
		"prologue":                  account.Prologue,
		"is_in_risk_white_list":     account.IsInRiskWhiteList,
		"identity":                  account.Identity,
		"desc":                      account.Desc,
		"is_show_in_chat_card_wall": account.IsShowInChatCardWall,
		"account_tags":              account.AccountTags,
		"sort":                      account.Sort,
	}}

	_, err := s.col.UpdateOne(ctx, filter, update)
	return err
}

func (s *aiAccountMongoStore) BatchDelete(ctx context.Context, idList []uint32) error {
	if len(idList) == 0 {
		return nil
	}

	filter := bson.M{"_id": bson.M{"$in": idList}}
	_, err := s.col.DeleteMany(ctx, filter)
	return err
}

func (s *aiAccountMongoStore) Get(ctx context.Context, id uint32) (*entity.AIAccount, error) {
	filter := bson.M{"_id": id}

	var account entity.AIAccount
	err := s.col.FindOne(ctx, filter).Decode(&account)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			log.WarnWithCtx(ctx, "Get id(%d) not found", id)
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "Get id(%d) err: %v", id, err)
		return nil, err
	}

	return &account, nil
}

func (s *aiAccountMongoStore) BatchGet(ctx context.Context, idList []uint32) (entity.AIAccountList, error) {
	if len(idList) == 0 {
		return nil, nil
	}

	filter := bson.M{"_id": bson.M{"$in": idList}}

	cursor, err := s.col.Find(ctx, filter)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGet Find err: %v", err)
		return nil, err
	}

	var accounts entity.AIAccountList
	if err = cursor.All(ctx, &accounts); err != nil {
		log.ErrorWithCtx(ctx, "BatchGet cursor.All err: %v", err)
		return nil, err
	}

	return accounts, nil
}

func (s *aiAccountMongoStore) GetByPage(ctx context.Context, page, size int64, chatCardWallShowStatus uint32) (entity.AIAccountList, int64, error) {
	filter := bson.M{
		"is_unregister": false, // 只查询未注销的账号
	}
	switch chatCardWallShowStatus {
	case DisplayChatCardWall:
		filter["is_show_in_chat_card_wall"] = true
	case HideChatCardWall:
		filter["is_show_in_chat_card_wall"] = false
	default:
		// 不设置 is_show_in_chat_card_wall 表示不筛选该字段
		// 这样可以查询到所有文档，包括字段不存在的情况
	}
	total, err := s.col.CountDocuments(ctx, filter)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetByPage CountDocuments err: %v", err)
		return nil, 0, err
	}

	if (page-1)*size > total {
		return nil, total, nil
	}

	findOpts := options.Find().
		SetSkip((page - 1) * size).
		SetLimit(size).
		SetSort(bson.D{{Key: "sort", Value: 1}, {Key: "update_time", Value: -1}})

	cursor, err := s.col.Find(ctx, filter, findOpts)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetByPage Find err: %v", err)
		return nil, 0, err
	}

	var accounts entity.AIAccountList
	if err = cursor.All(ctx, &accounts); err != nil {
		log.ErrorWithCtx(ctx, "GetByPage cursor.All err: %v", err)
		return nil, 0, err
	}

	return accounts, total, nil
}

func (s *aiAccountMongoStore) BatchUnregister(ctx context.Context, idList []uint32) error {
	if len(idList) == 0 {
		return nil
	}

	filter := bson.M{"_id": bson.M{"$in": idList}}
	update := bson.M{"$set": bson.M{
		"is_unregister": true,
		"update_time":   time.Now().Unix(),
	}}
	_, err := s.col.UpdateMany(ctx, filter, update)
	return err
}

func (s *aiAccountMongoStore) UpdateSex(ctx context.Context, uid uint32, sex int32) error {
	filter := bson.M{"_id": uid}
	update := bson.M{"$set": bson.M{
		"sex":         sex,
		"update_time": time.Now().Unix(),
	}}
	_, err := s.col.UpdateOne(ctx, filter, update)
	return err
}

func (s *aiAccountMongoStore) GetAIAccountsBySex(ctx context.Context, sex int32, limit int64) (entity.AIAccountList, error) {
	filter := bson.M{"sex": sex} // 2表示女

	findOpts := options.Find().
		SetLimit(limit).
		SetSort(bson.M{"update_time": -1})

	cursor, err := s.col.Find(ctx, filter, findOpts)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFemaleAIAccounts Find err: %v", err)
		return nil, err
	}

	var accounts entity.AIAccountList
	if err = cursor.All(ctx, &accounts); err != nil {
		log.ErrorWithCtx(ctx, "GetFemaleAIAccounts cursor.All err: %v", err)
		return nil, err
	}

	return accounts, nil
}
