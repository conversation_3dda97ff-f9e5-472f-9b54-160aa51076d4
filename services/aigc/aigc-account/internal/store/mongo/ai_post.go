package mongo

import (
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/aigc/aigc-account/internal/entity"
	"golang.52tt.com/services/aigc/aigc-account/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-account/internal/store"
	"time"
)

const (
	collectionAIPost = "ai_post"
)

type aiPostMongoStore struct {
	col *mongo.Collection
}

func NewAIPostMongoStore(ctx context.Context, mongoDB *db.MongoDB) store.AIPostStore {
	st := &aiPostMongoStore{
		col: mongoDB.Database().Collection(collectionAIPost),
	}

	// 创建索引
	_, err := st.col.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			Keys: bson.D{
				{Key: "uid", Value: 1},
				{Key: "update_time", Value: -1},
			},
		},
	})
	if err != nil {
		log.WarnWithCtx(ctx, "NewAIPostMongoStore collection(ai_post) CreateMany err: %v", err)
	}

	return st
}

func (p *aiPostMongoStore) Add(ctx context.Context, post *entity.AIPost) error {
	now := time.Now().Unix()
	post.CreateTime = now
	post.UpdateTime = now
	post.ID = primitive.NewObjectID().Hex()

	filter := bson.M{"uid": post.Uid, "post_id": post.PostId}
	update := bson.M{
		"$set": bson.M{
			"update_time": now,
		},
		"$setOnInsert": bson.M{
			"uid":         post.Uid,
			"post_id":     post.PostId,
			"create_time": post.CreateTime,
			"_id":         post.ID,
			// 添加其他需要在插入时设置的字段
		},
	}
	opts := options.Update().SetUpsert(true)

	_, err := p.col.UpdateOne(ctx, filter, update, opts)
	return err
}

func (p *aiPostMongoStore) GetList(ctx context.Context, page, size int64, uidList []uint32) ([]*entity.AIPost, int64, error) {
	filter := bson.M{}
	if len(uidList) > 0 {
		filter["uid"] = bson.M{"$in": uidList}
	}
	count, err := p.col.CountDocuments(ctx, filter)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetList Count error: %v", err)
		return nil, 0, err
	}

	if count == 0 {
		log.InfoWithCtx(ctx, "GetList no posts found")
		return nil, 0, nil
	}
	if (page-1)*size > count {
		return nil, count, nil
	}

	findOpts := options.Find().
		SetSkip((page - 1) * size).
		SetLimit(size).
		SetSort(bson.M{"update_time": -1})
	cursor, err := p.col.Find(ctx, filter, findOpts)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetList Find error: %v", err)
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	var posts []*entity.AIPost
	if err = cursor.All(ctx, &posts); err != nil {
		log.ErrorWithCtx(ctx, "GetList Decode error: %v", err)
		return nil, 0, err
	}

	return posts, count, nil
}
