package mongo

import (
	"context"
	"testing"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"golang.52tt.com/services/aigc/aigc-account/internal/entity"
)

func Test_aiAccountMongoStore_Add(t *testing.T) {
	type fields struct {
		col *mongo.Collection
	}
	type args struct {
		ctx     context.Context
		account *entity.AIAccount
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				col: utAIAccountCol,
			},
			args: args{
				ctx: context.Background(),
				account: &entity.AIAccount{
					Uid:      1001,
					Password: "password",
					IP:       "***********",
					PromptId: 1,
					TimbreId: 1,
					RoleId:   1,
					Prologue: "test_prologue",
				},
			},
			wantErr: false,
		},
		{
			name: "duplicate_id",
			fields: fields{
				col: utAIAccountCol,
			},
			args: args{
				ctx: context.Background(),
				account: &entity.AIAccount{
					Uid:      1001, // 使用已存在的ID
					Password: "password2",
					IP:       "***********",
					PromptId: 2,
					TimbreId: 2,
					RoleId:   2,
					Prologue: "test_prologue",
				},
			},
			wantErr: true, // 预期会出错，因为ID重复
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &aiAccountMongoStore{
				col: tt.fields.col,
			}
			if err := s.Add(tt.args.ctx, tt.args.account); (err != nil) != tt.wantErr {
				t.Errorf("aiAccountMongoStore.Add() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}

	// 清理测试数据
	s := &aiAccountMongoStore{
		col: utAIAccountCol,
	}
	_, _ = s.col.DeleteMany(context.Background(), bson.M{"_id": 1001}) // 删除测试数据
}

func Test_aiAccountMongoStore_Get(t *testing.T) {
	// 先添加测试数据
	s := &aiAccountMongoStore{
		col: utAIAccountCol,
	}
	ctx := context.Background()

	account := &entity.AIAccount{
		Uid:      1002,
		Password: "password",
		IP:       "***********",
		PromptId: 1,
		TimbreId: 1,
		RoleId:   1,
		Prologue: "test_prologue",
	}
	_ = s.Add(ctx, account)

	type fields struct {
		col *mongo.Collection
	}
	type args struct {
		ctx context.Context
		id  uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
		wantNil bool
	}{
		{
			name: "ok",
			fields: fields{
				col: utAIAccountCol,
			},
			args: args{
				ctx: context.Background(),
				id:  1002,
			},
			wantErr: false,
			wantNil: false,
		},
		{
			name: "not_found",
			fields: fields{
				col: utAIAccountCol,
			},
			args: args{
				ctx: context.Background(),
				id:  9999, // 不存在的ID
			},
			wantErr: false,
			wantNil: true, // 预期返回nil，不会有错误
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &aiAccountMongoStore{
				col: tt.fields.col,
			}
			got, err := s.Get(tt.args.ctx, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("aiAccountMongoStore.Get() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if (got == nil) != tt.wantNil {
				t.Errorf("aiAccountMongoStore.Get() got = %v, wantNil %v", got, tt.wantNil)
				return
			}
			t.Logf("aiAccountMongoStore.Get() = %v", got)
		})
	}

	// 清理测试数据
	_, _ = s.col.DeleteMany(context.Background(), bson.M{"_id": 1002})
}

func Test_aiAccountMongoStore_Update(t *testing.T) {
	// 先添加测试数据
	s := &aiAccountMongoStore{
		col: utAIAccountCol,
	}
	ctx := context.Background()

	account := &entity.AIAccount{
		Uid:      1003,
		Password: "password",
		IP:       "***********",
		PromptId: 1,
		TimbreId: 1,
		RoleId:   1,
		Prologue: "test_prologue",
	}
	_ = s.Add(ctx, account)

	type fields struct {
		col *mongo.Collection
	}
	type args struct {
		ctx     context.Context
		account *entity.AIAccount
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				col: utAIAccountCol,
			},
			args: args{
				ctx: context.Background(),
				account: &entity.AIAccount{
					Uid:      1003,
					Password: "new_password",
					IP:       "***********",
					PromptId: 2,
					TimbreId: 2,
					RoleId:   2,
					Prologue: "new_prologue",
				},
			},
			wantErr: false,
		},
		{
			name: "not_found",
			fields: fields{
				col: utAIAccountCol,
			},
			args: args{
				ctx: context.Background(),
				account: &entity.AIAccount{
					Uid:      9999, // 不存在的ID
					Password: "new_password",
					IP:       "***********",
					PromptId: 2,
					TimbreId: 2,
					RoleId:   2,
					Prologue: "new_prologue",
				},
			},
			wantErr: false, // MongoDB 更新不存在的文档不会返回错误，而是影响行数为0
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &aiAccountMongoStore{
				col: tt.fields.col,
			}
			if err := s.Update(tt.args.ctx, tt.args.account); (err != nil) != tt.wantErr {
				t.Errorf("aiAccountMongoStore.Update() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}

	// 清理测试数据
	_, _ = s.col.DeleteMany(context.Background(), bson.M{"_id": 1003})
}

func Test_aiAccountMongoStore_BatchDelete(t *testing.T) {
	// 先添加测试数据
	s := &aiAccountMongoStore{
		col: utAIAccountCol,
	}
	ctx := context.Background()

	for i := uint32(1004); i <= 1006; i++ {
		account := &entity.AIAccount{
			Uid:      i,
			Password: "password",
			IP:       "***********",
			PromptId: 1,
			TimbreId: 1,
			RoleId:   1,
			Prologue: "test_prologue",
		}
		_ = s.Add(ctx, account)
	}

	type fields struct {
		col *mongo.Collection
	}
	type args struct {
		ctx    context.Context
		idList []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				col: utAIAccountCol,
			},
			args: args{
				ctx:    context.Background(),
				idList: []uint32{1004, 1005},
			},
			wantErr: false,
		},
		{
			name: "empty_list",
			fields: fields{
				col: utAIAccountCol,
			},
			args: args{
				ctx:    context.Background(),
				idList: []uint32{},
			},
			wantErr: false, // 空列表应该直接返回nil
		},
		{
			name: "not_found",
			fields: fields{
				col: utAIAccountCol,
			},
			args: args{
				ctx:    context.Background(),
				idList: []uint32{9999}, // 不存在的ID
			},
			wantErr: false, // MongoDB 删除不存在的文档不会返回错误
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &aiAccountMongoStore{
				col: tt.fields.col,
			}
			if err := s.BatchDelete(tt.args.ctx, tt.args.idList); (err != nil) != tt.wantErr {
				t.Errorf("aiAccountMongoStore.BatchDelete() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}

	// 清理剩余测试数据
	_, _ = s.col.DeleteMany(context.Background(), bson.M{"_id": bson.M{"$in": []uint32{1006}}})
}

func Test_aiAccountMongoStore_BatchGet(t *testing.T) {
	// 先添加测试数据
	s := &aiAccountMongoStore{
		col: utAIAccountCol,
	}
	ctx := context.Background()

	// 添加两个账号
	account1 := &entity.AIAccount{
		Uid:      2001,
		Password: "password1",
		IP:       "***********",
		PromptId: 1,
		TimbreId: 1,
		RoleId:   1,
		Prologue: "test_prologue",
	}
	account2 := &entity.AIAccount{
		Uid:      2002,
		Password: "password2",
		IP:       "***********",
		PromptId: 2,
		TimbreId: 2,
		RoleId:   2,
		Prologue: "test_prologue",
	}

	_ = s.Add(ctx, account1)
	_ = s.Add(ctx, account2)

	type fields struct {
		col *mongo.Collection
	}
	type args struct {
		ctx    context.Context
		idList []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantLen int
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				col: utAIAccountCol,
			},
			args: args{
				ctx:    context.Background(),
				idList: []uint32{2001, 2002},
			},
			wantLen: 2,
			wantErr: false,
		},
		{
			name: "partial_found",
			fields: fields{
				col: utAIAccountCol,
			},
			args: args{
				ctx:    context.Background(),
				idList: []uint32{2001, 9999}, // 一个存在，一个不存在
			},
			wantLen: 1, // 只会找到一个
			wantErr: false,
		},
		{
			name: "empty_list",
			fields: fields{
				col: utAIAccountCol,
			},
			args: args{
				ctx:    context.Background(),
				idList: []uint32{},
			},
			wantLen: 0,
			wantErr: false, // 空列表应该返回空结果，不会有错误
		},
		{
			name: "not_found",
			fields: fields{
				col: utAIAccountCol,
			},
			args: args{
				ctx:    context.Background(),
				idList: []uint32{9998, 9999}, // 都不存在
			},
			wantLen: 0, // 没有找到任何结果
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &aiAccountMongoStore{
				col: tt.fields.col,
			}
			got, err := s.BatchGet(tt.args.ctx, tt.args.idList)
			if (err != nil) != tt.wantErr {
				t.Errorf("aiAccountMongoStore.BatchGet() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if len(got) != tt.wantLen {
				t.Errorf("aiAccountMongoStore.BatchGet() len = %v, wantLen %v", len(got), tt.wantLen)
				return
			}
			t.Logf("aiAccountMongoStore.BatchGet() = %v", got)
		})
	}

	// 清理测试数据
	_, _ = s.col.DeleteMany(context.Background(), bson.M{"_id": bson.M{"$in": []uint32{2001, 2002}}})
}

func Test_aiAccountMongoStore_GetByPage(t *testing.T) {
	// 先添加测试数据
	s := &aiAccountMongoStore{
		col: utAIAccountCol,
	}
	ctx := context.Background()

	// 添加多个账号
	for i := uint32(3001); i <= 3010; i++ {
		account := &entity.AIAccount{
			Uid:      i,
			Password: "password",
			IP:       "***********",
			PromptId: 1,
			TimbreId: 1,
			RoleId:   1,
			Prologue: "test_prologue",
		}
		_ = s.Add(ctx, account)
	}

	type fields struct {
		col *mongo.Collection
	}
	type args struct {
		ctx                    context.Context
		page                   int64
		size                   int64
		chatCardWallShowStatus uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantLen int
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				col: utAIAccountCol,
			},
			args: args{
				ctx:  context.Background(),
				page: 1,
				size: 5,
			},
			wantLen: 5, // 第一页，每页5条，应该返回5条
			wantErr: false,
		},
		{
			name: "second_page",
			fields: fields{
				col: utAIAccountCol,
			},
			args: args{
				ctx:  context.Background(),
				page: 2,
				size: 5,
			},
			wantLen: 5, // 第二页，每页5条，应该返回5条
			wantErr: false,
		},
		//{
		//    name: "last_page",
		//    fields: fields{
		//        col: utAIAccountCol,
		//    },
		//    args: args{
		//        ctx:  context.Background(),
		//        page: 3,
		//        size: 5,
		//    },
		//    wantLen: 0, // 第三页，每页5条，应该没有数据了
		//    wantErr: false,
		//},
		{
			name: "out_of_range",
			fields: fields{
				col: utAIAccountCol,
			},
			args: args{
				ctx:  context.Background(),
				page: 10000,
				size: 5,
			},
			wantLen: 0, // 超出范围，应该返回空结果
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &aiAccountMongoStore{
				col: tt.fields.col,
			}
			got, total, err := s.GetByPage(tt.args.ctx, tt.args.page, tt.args.size, tt.args.chatCardWallShowStatus)
			if (err != nil) != tt.wantErr {
				t.Errorf("aiAccountMongoStore.GetByPage() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if len(got) != tt.wantLen {
				t.Errorf("aiAccountMongoStore.GetByPage() len = %v, wantLen %v", len(got), tt.wantLen)
				return
			}
			t.Logf("aiAccountMongoStore.GetByPage() = %v, total = %d", got, total)
		})
	}

	// 清理测试数据
	var idList []uint32
	for i := uint32(3001); i <= 3010; i++ {
		idList = append(idList, i)
	}
	_, _ = s.col.DeleteMany(context.Background(), bson.M{"_id": bson.M{"$in": idList}})
}

func Test_aiAccountMongoStore_BatchUnregister(t *testing.T) {
	// 先添加测试数据
	s := &aiAccountMongoStore{
		col: utAIAccountCol,
	}
	ctx := context.Background()

	for i := uint32(3001); i <= 3003; i++ {
		account := &entity.AIAccount{
			Uid:      i,
			Password: "password",
			IP:       "***********",
			PromptId: 1,
			TimbreId: 1,
			RoleId:   1,
			Prologue: "test_prologue",
		}
		_ = s.Add(ctx, account)
	}

	type fields struct {
		col *mongo.Collection
	}
	type args struct {
		ctx    context.Context
		idList []uint32
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
		checkId []uint32
	}{
		{
			name: "ok",
			fields: fields{
				col: utAIAccountCol,
			},
			args: args{
				ctx:    context.Background(),
				idList: []uint32{3001, 3002},
			},
			wantErr: false,
			checkId: []uint32{3001, 3002},
		},
		{
			name: "empty_list",
			fields: fields{
				col: utAIAccountCol,
			},
			args: args{
				ctx:    context.Background(),
				idList: []uint32{},
			},
			wantErr: false,
			checkId: nil,
		},
		{
			name: "not_found",
			fields: fields{
				col: utAIAccountCol,
			},
			args: args{
				ctx:    context.Background(),
				idList: []uint32{3999},
			},
			wantErr: false,
			checkId: []uint32{3999},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &aiAccountMongoStore{
				col: tt.fields.col,
			}
			err := s.BatchUnregister(tt.args.ctx, tt.args.idList)
			if (err != nil) != tt.wantErr {
				t.Errorf("aiAccountMongoStore.BatchUnregister() error = %v, wantErr %v", err, tt.wantErr)
			}
			// 检查注销结果
			for _, id := range tt.checkId {
				acc, _ := s.Get(tt.args.ctx, id)
				if acc != nil && id != 3999 && !acc.IsUnregister {
					t.Errorf("account %d is_unregister = %v, want true", id, acc.IsUnregister)
				}
			}
		})
	}

	// 清理测试数据
	_, _ = s.col.DeleteMany(context.Background(), bson.M{"_id": bson.M{"$in": []uint32{3001, 3002, 3003}}})
}
