package mongo

import (
	"context"
	"errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/aigc/aigc-account/internal/entity"
	"golang.52tt.com/services/aigc/aigc-account/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-account/internal/store"
	"time"
)

type chatTimeMongoStore struct {
	chatTimeRecord *mongo.Collection
}

func NewChatTimeMongoStore(ctx context.Context, mongoDB *db.MongoDB) store.ChatTimeStore {
	st := &chatTimeMongoStore{
		chatTimeRecord: mongoDB.Database().Collection("chat_time_record"),
	}

	return st
}

func (s *chatTimeMongoStore) UpdateChatTime(ctx context.Context, uid, aiUid uint32) error {
	id := entity.EncodeChatTimeId(uid, aiUid)
	update := bson.M{"$set": bson.M{"last_send_time": time.Now()}}
	result, err := s.chatTimeRecord.UpdateByID(ctx, id, update, options.Update().SetUpsert(true))
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateChatTime UpdateByID uid%d aiUid:%d error: %v", uid, aiUid, err)
		return err
	}
	log.InfoWithCtx(ctx, "UpdateChatTime UpdateByID uid:%d aiUid:%d result: %v", uid, aiUid, result)
	return nil
}

func (s *chatTimeMongoStore) GetChatTime(ctx context.Context, uid, aiUid uint32) (*entity.ChatTimeRecord, error) {
	id := entity.EncodeChatTimeId(uid, aiUid)
	var record entity.ChatTimeRecord
	err := s.chatTimeRecord.FindOne(ctx, bson.M{"_id": id}).Decode(&record)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil // No record found
		}
		log.ErrorWithCtx(ctx, "GetChatTime FindOne uid:%d aiUid:%d error: %v", uid, aiUid, err)
		return nil, err
	}
	return &record, nil
}
