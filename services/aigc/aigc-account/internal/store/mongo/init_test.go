package mongo

import (
	"context"

	"go.mongodb.org/mongo-driver/mongo"

	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/aigc/aigc-account/internal/infra/db"
)

var (
	utAIAccountCol *mongo.Collection
)

func init() {
	mongoConfig := &config.MongoConfig{
		Addrs:       "10.34.6.29:27017",
		Database:    "aigc_account",
		MaxPoolSize: 1,
		UserName:    "aigc_account_rw",
		Password:    "44jEiqkVo*pnSkC",
	}

	utDB, err := db.NewMongoDB(context.Background(), mongoConfig)
	if err != nil {
		log.Fatalf("NewMongoDB config(%+v) err: %v", mongoConfig, err)
	}

	utAIAccountCol = utDB.Database().Collection(collectionAIAccount)
}
