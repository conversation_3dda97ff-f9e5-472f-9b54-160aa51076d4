package internal

import (
	"context"
	"gitlab.ttyuyin.com/bizFund/bizFund/protocol/common/status"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/aigc/aigc-account"
)

func (s *Server) AddAiPost(ctx context.Context, req *pb.AddAiPostRequest) (resp *pb.AddAiPostResponse, err error) {
	resp = &pb.AddAiPostResponse{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "AddAiPost req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "AddAiPost req:%+v,resp:%+v", req, resp)
		}
	}()
	if req.GetUid() == 0 || len(req.GetPostId()) == 0 {
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "参数错误")
	}
	err = s.aiPostMgr.AddAiPost(ctx, req.GetUid(), req.GetPostId())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddAiPost AddAiPost userId:%d postId:%s error: %v", req.GetUid(), req.GetPostId(), err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "添加失败")
	}
	return
}

func (s *Server) GetAiPost(ctx context.Context, req *pb.GetAiPostRequest) (resp *pb.GetAiPostResponse, err error) {
	resp = &pb.GetAiPostResponse{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAiPost req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "GetAiPost req:%+v,resp:%+v", req, resp)
		}
	}()
	page := req.GetPage()
	if page == 0 {
		req.Page = 1
	}
	size := req.GetSize()
	if size == 0 {
		size = 20
	}

	list, total, err := s.aiPostMgr.GetAiPostList(ctx, int64(page), int64(size), req.GetAiUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAiPost GetAiPostList page:%d size:%d error: %v", page, size, err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "获取失败")
	}

	if total == 0 {
		return resp, nil
	}
	resp.TotalNum = uint32(total)

	for _, post := range list {
		resp.PostIds = append(resp.PostIds, post.PostId)
	}

	return
}

func (s *Server) AddUserAiChatRecord(ctx context.Context, req *pb.AddUserAiChatRecordRequest) (resp *pb.AddUserAiChatRecordResponse, err error) {
	resp = &pb.AddUserAiChatRecordResponse{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "AddUserAiChatRecord req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "AddUserAiChatRecord req:%+v,resp:%+v", req, resp)
		}
	}()

	err = s.aiPostMgr.AddUserAiChatRecord(ctx, req.GetFromUid(), req.GetToUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddUserAiChatRecord error: %v", err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "添加失败")
	}
	return
}

func (s *Server) GetUserAiChatRecord(ctx context.Context, req *pb.GetUserAiChatRecordRequest) (resp *pb.GetUserAiChatRecordResponse, err error) {
	resp = &pb.GetUserAiChatRecordResponse{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserAiChatRecord req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "GetUserAiChatRecord req:%+v,resp:%+v", req, resp)
		}
	}()

	recordTime, err := s.aiPostMgr.GetUserAiChatRecord(ctx, req.GetFromUid(), req.GetToUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserAiChatRecord error: %v", err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "获取失败")
	}
	resp.RecordTime = recordTime
	return
}

func (s *Server) AddUserAiInteractionRecord(ctx context.Context, req *pb.AddUserAiInteractionRecordRequest) (resp *pb.AddUserAiInteractionRecordResponse, err error) {
	resp = &pb.AddUserAiInteractionRecordResponse{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "AddUserAiInteractionRecord req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "AddUserAiInteractionRecord req:%+v,resp:%+v", req, resp)
		}
	}()

	err = s.aiPostMgr.AddInteraction(ctx, req.GetUid(), req.GetAiUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddUserAiInteractionRecord error: %v", err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "添加失败")
	}
	return
}

func (s *Server) GetUserAiInteractionRecord(ctx context.Context, req *pb.GetUserAiInteractionRecordRequest) (resp *pb.GetUserAiInteractionRecordResponse, err error) {
	resp = &pb.GetUserAiInteractionRecordResponse{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserAiInteractionRecord req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "GetUserAiInteractionRecord req:%+v,resp:%+v", req, resp)
		}
	}()

	aiList, err := s.aiPostMgr.GetInteractionAiList(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserAiInteractionRecord error: %v", err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "获取失败")
	}
	resp.AiUidList = aiList
	return
}

func (s *Server) AddAiLikePost(ctx context.Context, req *pb.AddAiLikePostRequest) (resp *pb.AddAiLikePostResponse, err error) {
	resp = &pb.AddAiLikePostResponse{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "AddAiLikePost req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "AddAiLikePost req:%+v,resp:%+v", req, resp)
		}
	}()

	err = s.aiPostMgr.AddLikePost(ctx, req.GetUid(), req.GetPostId())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddAiLikePost error: %v", err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "添加失败")
	}
	return
}

func (s *Server) GetAiLikePostCount(ctx context.Context, req *pb.GetAiLikePostCountRequest) (resp *pb.GetAiLikePostCountResponse, err error) {
	resp = &pb.GetAiLikePostCountResponse{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAiLikePostCount req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "GetAiLikePostCount req:%+v,resp:%+v", req, resp)
		}
	}()

	count, err := s.aiPostMgr.GetLikePostCount(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAiLikePostCount error: %v", err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "获取失败")
	}
	resp.LikePostCount = uint32(count)
	return
}

func (s *Server) AddAiCommentPost(ctx context.Context, req *pb.AddAiCommentPostRequest) (resp *pb.AddAiCommentPostResponse, err error) {
	resp = &pb.AddAiCommentPostResponse{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "AddAiCommentPost req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "AddAiCommentPost req:%+v,resp:%+v", req, resp)
		}
	}()

	err = s.aiPostMgr.AddCommentPost(ctx, req.GetUid(), req.GetPostId())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddAiCommentPost error: %v", err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "添加失败")
	}
	return
}

func (s *Server) GetAiCommentPostCount(ctx context.Context, req *pb.GetAiCommentPostCountRequest) (resp *pb.GetAiCommentPostCountResponse, err error) {
	resp = &pb.GetAiCommentPostCountResponse{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAiCommentPostCount req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "GetAiCommentPostCount req:%+v,resp:%+v", req, resp)
		}
	}()

	count, err := s.aiPostMgr.GetCommentPostCount(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAiCommentPostCount error: %v", err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "获取失败")
	}
	resp.CommentPostCount = uint32(count)
	return
}

func (s *Server) AddPostAiCommentRecord(ctx context.Context, req *pb.AddPostAiCommentRecordRequest) (resp *pb.AddPostAiCommentRecordResponse, err error) {
	resp = &pb.AddPostAiCommentRecordResponse{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "AddPostAiCommentRecord req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "AddPostAiCommentRecord req:%+v,resp:%+v", req, resp)
		}
	}()

	_, err = s.aiPostMgr.IncrPostAiCommentCount(ctx, req.GetPostId(), req.GetAiUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddPostAiCommentRecord error: %v", err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "添加失败")
	}
	return
}

func (s *Server) GetPostAiCommentCount(ctx context.Context, req *pb.GetPostAiCommentCountRequest) (resp *pb.GetPostAiCommentCountResponse, err error) {
	resp = &pb.GetPostAiCommentCountResponse{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPostAiCommentCount req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "GetPostAiCommentCount req:%+v,resp:%+v", req, resp)
		}
	}()

	count, err := s.aiPostMgr.GetPostAiCommentCount(ctx, req.GetPostId(), req.GetAiUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPostAiCommentCount error: %v", err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "获取失败")
	}
	resp.CommentCount = uint32(count)
	return
}

func (s *Server) GetPostAiCommentList(ctx context.Context, req *pb.GetPostAiCommentListRequest) (resp *pb.GetPostAiCommentListResponse, err error) {
	resp = &pb.GetPostAiCommentListResponse{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPostCommentAiList req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "GetPostCommentAiList req:%+v,resp:%+v", req, resp)
		}
	}()

	aiComments, err := s.aiPostMgr.GetPostCommentAiList(ctx, req.GetPostId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPostCommentAiList error: %v", err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "获取失败")
	}

	if len(aiComments) == 0 {
		return resp, nil
	}

	for aiUid, count := range aiComments {
		resp.AiCommentList = append(resp.AiCommentList, &pb.AiCommentData{
			AiUid: aiUid,
			Num:   uint32(count),
		})
	}
	return
}
