package rpc

import (
	"context"
	account_go "golang.52tt.com/clients/account-go"
	"golang.52tt.com/pkg/log"
)

type Clients struct {
	AccountGo account_go.IClient
}

func NewClients() (*Clients, error) {
	c := new(Clients)
	var (
		err error
		ctx = context.Background()
	)

	c.AccountGo, err = account_go.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients account_go.NewClient err: %v", err)
		return nil, err
	}

	return c, nil
}
