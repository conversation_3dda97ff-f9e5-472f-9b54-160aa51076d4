package aigcaccountserver

import (
	"context"
	"gitlab.ttyuyin.com/tyr/tt-ecosystem/startup/suit/smart"
	"golang.52tt.com/services/aigc/aigc-account/internal"
	"google.golang.org/grpc"

	pb "golang.52tt.com/protocol/services/aigc/aigc-account"
)

const ServerName = "aigc-account"

type Server struct {
	smartstartup.UnimplementedServer
	smartstartup.UnimplementedGrpcServer

	*internal.Server
	cfg internal.StartConfig
}

func (s *Server) Name() string {
	return ServerName
}

func (s *Server) RegisterGRPC(registrar *grpc.Server) error {
	pb.RegisterAigcAccountServer(registrar, s)

	return nil
}

func (s *Server) GRPCServerInterceptor() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{
		smartstartup.ServerInterceptor(),
	}
}

func (s *Server) Init(ctx context.Context) error {
	server, err := internal.NewServer(ctx, &s.cfg)
	if err != nil {
		return err
	}
	s.Server = server
	return nil
}

func (s *Server) Close(ctx context.Context) {
	s.ShutDown()
}

func (s *Server) RegisterConfig() map[string]*smartstartup.Config {
	return map[string]*smartstartup.Config{
		ServerName: {
			Config: &s.cfg,
		},
	}
}
