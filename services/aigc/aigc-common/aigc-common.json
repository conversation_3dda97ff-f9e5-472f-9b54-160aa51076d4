{"server.grpcListen": ":80", "server.adminListen": ":8078", "mongo": {"addrs": "**********:27017", "database": "aigc_common", "user_name": "aigc_common_rw", "password": "H35P*ntC8bncZ3Z", "max_pool_size": 5}, "event_link": {"publisher": {"attitude_event": {"kafka": {"clientID": "aigc-common", "brokers": ["hobby-channel-kafka-broker-01.database.svc.cluster.local:9092"]}, "topics": ["aigc_attitude_event"]}}}, "redis": {"host": "redis-test-tc-bj-tt-go-05.database.svc.cluster.local", "port": 6379, "protocol": "tcp", "ping_interval": 300, "database": 16}}