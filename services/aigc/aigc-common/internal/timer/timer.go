package timer

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata"
	pb "golang.52tt.com/protocol/services/aigc/aigc-common"
	"golang.52tt.com/services/aigc/aigc-common/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-common/internal/mgr"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"golang.52tt.com/pkg/timer"
)

type RedisTimer struct {
	timerD      *timer.Timer
	delayMsgMgr mgr.DelayMgr
}

func NewRedisTimer(ctx context.Context, delayMsgMgr mgr.DelayMgr, redisCli *db.RedisDB) (*RedisTimer, error) {

	timerD, err := timer.NewTimerD(ctx, "aigc-common", timer.WithV8RedisCmdable(redisCli))
	if err != nil {
		log.Errorf("NewTimerD err:%v", err)
		return nil, err
	}

	return &RedisTimer{
		timerD:      timerD,
		delayMsgMgr: delayMsgMgr,
	}, nil
}

func (t *RedisTimer) Start() error {

	// partner消息延时发送
	t.timerD.AddIntervalTask("CheckPartnerQueue", 1*time.Second, timer.BuildFromLambda(func(ctx context.Context) {
		_ = t.HandleDelayQueue(ctx, pb.PushTo_PUSH_TO_PARTNER)
	}))

	// 群聊消息延时发送
	t.timerD.AddIntervalTask("CheckGroupQueue", 1*time.Second, timer.BuildFromLambda(func(ctx context.Context) {
		_ = t.HandleDelayQueue(ctx, pb.PushTo_PUSH_TO_GROUP)
	}))

	t.timerD.Start()
	return nil
}

func (t *RedisTimer) Stop() {
	t.timerD.Stop()
}

func (t *RedisTimer) HandleDelayQueue(ctx context.Context, pushTo pb.PushTo) error {
	records, err := t.delayMsgMgr.PopFromPool(ctx, pushTo, 100)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleDelayQueue PopFromPool pushTo:%s err: %v", pushTo.String(), err)
		return err
	}
	if len(records) == 0 {
		//log.WarnWithCtx(ctx, "HandleDelayQueue pushTo:%s records is empty", pushTo.String())
		return nil
	}

	failedTaskIds := make([]string, 0, len(records))
	for _, record := range records {
		err = t.delayMsgMgr.SendDelayMsg(ctx, record)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleDelayQueue SendDelayMsg pushTo:%s err: %v, record: %+v", pushTo.String(), err, record)
			failedTaskIds = append(failedTaskIds, record.TaskId)
		}
	}
	if len(failedTaskIds) > 0 {
		newCtx := metadata.NewContext(ctx)
		go func() {
			err = t.delayMsgMgr.BatAddToPool(newCtx, failedTaskIds, pushTo, 1) // 重新入队，延时1s
			if err != nil {
				log.ErrorWithCtx(newCtx, "HandleDelayQueue BatAddToPool pushTo:%s failedTaskIds:%v err: %v", pushTo.String(), failedTaskIds, err)
			}
		}()

	}
	log.InfoWithCtx(ctx, "HandleDelayQueue pushTo:%s processed %d records, failedTaskIds: %v", pushTo.String(), len(records), failedTaskIds)
	return nil
}
