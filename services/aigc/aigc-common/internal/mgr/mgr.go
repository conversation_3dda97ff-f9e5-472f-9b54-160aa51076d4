package mgr

import (
	"context"
	pb "golang.52tt.com/protocol/services/aigc/aigc-common"
	"golang.52tt.com/services/aigc/aigc-common/internal/entity"
)

type AttitudeMgr interface {
	Like(ctx context.Context, uid uint32, objectType pb.ObjectType, objectId uint32, op pb.AttitudeOp) error
	HadAttitude(ctx context.Context, uid uint32, objectType pb.ObjectType, objectId uint32) (bool, error)
	GetAttitudeCounts(ctx context.Context, objects []*pb.Object) ([]*entity.ObjetLikeCount, error)
}

type SentenceMgr interface {
	AddSentenceCount(ctx context.Context, uid uint32, entityInst *pb.Entity, sentenceType uint32, count uint32) error
	GetSentenceCountByTypes(ctx context.Context, uid uint32, sentenceTypes []uint32, entityInst *pb.Entity) (map[string]uint32, error)
	GetUserCount(ctx context.Context, uid uint32, entityType uint32, sentenceType uint32) (uint32, error)
	AddUserCount(ctx context.Context, uid uint32, entityType uint32, sentenceType uint32) error
	AddBussCount(ctx context.Context, uid, businessType, sentenceType uint32, count int64) (uint32, error)
	BatAddBussCount(ctx context.Context, uids []uint32, businessType, sentenceType uint32, count int64) (map[uint32]uint32, error)

	AddExtraCount(ctx context.Context, record *entity.ExtraSentenceRecord) error
	ConsumeSentenceCount(ctx context.Context, uid, sentenceType, businessType, roleType uint32, entityInst *pb.Entity) (
		curNum uint32, success bool, err error)
	GetAvailableExtraCount(ctx context.Context, uid, businessType uint32) (uint32, error)
	BatGetTodaySentenceCount(ctx context.Context, uid, businessType, roleType uint32, sentenceType []uint32,
		entityInst *pb.Entity) (map[uint32]uint32, error)
	GetTodayUsedTotalCount(ctx context.Context, uid, businessType uint32) (uint32, error)
	GetHistoryTotalCount(ctx context.Context, uid, businessType uint32) (uint32, error)
	GetTotalAwardCountByTypes(ctx context.Context, uid, businessType uint32,
		rewardTypes []uint32) (map[uint32]uint32, error)
}

type DelayMgr interface {
	AddDelayMsg(ctx context.Context, record *entity.DelayRecord) error
	PopFromPool(ctx context.Context, pushTo pb.PushTo, count int64) ([]*entity.DelayRecord, error)
	CleanFinishTasks(ctx context.Context, taskIds ...string) error
	SendDelayMsg(ctx context.Context, record *entity.DelayRecord) error
	BatAddToPool(ctx context.Context, taskIds []string, pushTo pb.PushTo, delayTime int64) error
}
