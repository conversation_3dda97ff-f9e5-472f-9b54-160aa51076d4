package event

import (
	"context"
	pb "golang.52tt.com/protocol/services/aigc/aigc-common"
)

//go:generate mockgen -destination=mocks/event_bus.go -package=mocks golang.52tt.com/services/aigc/aigc-common/internal/event EventBus
type EventBus interface {
	Close()
	PublishAttitudeEvent(ctx context.Context, event *pb.AigcAttitudeEvent) error
	PublishDelayMsgEvent(ctx context.Context, event *pb.DelayMsgEvent) error
}
