package internal

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	redis_connect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	middleware_event "gitlab.ttyuyin.com/tt-infra/middleware/event"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/protocol/services/demo/echo"
	"golang.52tt.com/services/aigc/aigc-common/internal/cache/redis"
	tt_config "golang.52tt.com/services/aigc/aigc-common/internal/config/ttconfig"
	"golang.52tt.com/services/aigc/aigc-common/internal/event"
	"golang.52tt.com/services/aigc/aigc-common/internal/event/eventlink"
	"golang.52tt.com/services/aigc/aigc-common/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-common/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-common/internal/mgr/attitude"
	"golang.52tt.com/services/aigc/aigc-common/internal/mgr/delay"
	"golang.52tt.com/services/aigc/aigc-common/internal/mgr/sentence"
	"golang.52tt.com/services/aigc/aigc-common/internal/store/mongo"
	"golang.52tt.com/services/aigc/aigc-common/internal/timer"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var (
	errUnimplemented = status.Error(codes.Unimplemented, "")
)

type StartConfig struct {
	// from config file
	Mongo     *config.MongoConfig        `json:"mongo"`
	EventLink *middleware_event.Options  `json:"event_link"`
	Redis     *redis_connect.RedisConfig `json:"redis"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)
	eventBus, err := eventlink.NewEventBus(ctx, cfg.EventLink)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewEventPublisher cfg(%+v) err: %v", cfg, err)
		return nil, err
	}
	mongoDB, err := db.NewMongoDB(ctx, cfg.Mongo)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewMongoDB cfg(%+v) err", cfg, err)
		return nil, err
	}
	attitudeMongoStore := mongo.NewAttitudeMongoStore(ctx, mongoDB)

	attitudeMgr := attitude.NewAttitudeMgr(eventBus, attitudeMongoStore)

	redisDB, err := db.NewRedisDB(ctx, cfg.Redis)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewRedisDB cfg(%+v) err", cfg, err)
		return nil, err
	}
	sentenceMongoStore := mongo.NewSentenceMongoStore(ctx, mongoDB)

	sentenceCache := redis.NewSentenceRedisCache(redisDB)
	sentenceMgr := sentence.NewSentenceMgr(sentenceCache, sentenceMongoStore)

	delayCache := redis.NewDelayMsgRedisCache(redisDB)

	delayMgr := delay.NewDelayMgr(delayCache, eventBus)
	redisTimer, err := timer.NewRedisTimer(ctx, delayMgr, redisDB)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewRedisTimer err: %v", err)
		return nil, err
	}
	err = redisTimer.Start()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer redisTimer.Start err(%v)", err)
		return nil, err
	}
	err = tt_config.InitAigcCommonConfig()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer InitAigcCommonConfig err: %v", err)
		return nil, err
	}
	s := &Server{
		mongoDB:     mongoDB,
		eventBus:    eventBus,
		attitudeMgr: attitudeMgr,
		sentenceMgr: sentenceMgr,
		delayMgr:    delayMgr,
		redisTimer:  redisTimer,
	}

	return s, nil
}

type Server struct {
	mongoDB  *db.MongoDB
	eventBus event.EventBus

	attitudeMgr mgr.AttitudeMgr
	sentenceMgr mgr.SentenceMgr
	delayMgr    mgr.DelayMgr

	redisTimer *timer.RedisTimer
}

func (s *Server) ShutDown() {
	if s.mongoDB != nil {
		s.mongoDB.Close(context.Background())
	}
	if s.eventBus != nil {
		s.eventBus.Close()
	}

	if s.redisTimer != nil {
		s.redisTimer.Stop()
	}
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}
