package internal

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/aigc/aigc-common"
	"golang.52tt.com/services/aigc/aigc-common/internal/entity"
	"strconv"
)

// Attitude 点赞
func (s *Server) Attitude(ctx context.Context, req *pb.AttitudeRequest) (*pb.AttitudeResponse, error) {
	out := &pb.AttitudeResponse{}
	uid := metainfo.GetServiceInfo(ctx).UserID()

	if req.GetOp() == pb.AttitudeOp_ATTITUDE_OP_UNSPECIFIED ||
		req.GetAttitudeObject() == nil ||
		req.GetAttitudeObject().GetObjectType() == pb.ObjectType_OBJECT_TYPE_UNSPECIFIED ||
		req.GetAttitudeObject().GetObjectId() == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	err := s.attitudeMgr.Like(ctx, uid, req.GetAttitudeObject().GetObjectType(), req.GetAttitudeObject().GetObjectId(), req.GetOp())
	if err != nil {
		log.ErrorWithCtx(ctx, "Attitude uid:%d req:%s err:%v", uid, req.String(), err)
		return out, err
	}
	log.InfoWithCtx(ctx, "Attitude success req:%s", req.String())
	return out, nil
}

// GetAttitudeCount 获取点赞数
func (s *Server) GetAttitudeCount(ctx context.Context, req *pb.GetAttitudeCountRequest) (*pb.GetAttitudeCountResponse, error) {
	out := &pb.GetAttitudeCountResponse{}

	if len(req.GetObjects()) == 0 {
		log.WarnWithCtx(ctx, "GetAttitudeCount empty req:%s", req.String())
		return out, nil
	}
	likeCounts, err := s.attitudeMgr.GetAttitudeCounts(ctx, req.GetObjects())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAttitudeCount req:%s err:%v", req.String(), err)
		return out, err
	}
	out.AttitudeCountInfos = convertAttitudeCounts(ctx, likeCounts)
	log.InfoWithCtx(ctx, "GetAttitudeCount success req:%s out:%s", req.String(), out.String())
	return out, nil
}

func convertAttitudeCounts(ctx context.Context, likeCounts []*entity.ObjetLikeCount) []*pb.GetAttitudeCountResponse_AttitudeCountInfo {
	res := make([]*pb.GetAttitudeCountResponse_AttitudeCountInfo, 0, len(likeCounts))
	for _, v := range likeCounts {
		id, err := strconv.Atoi(v.ObjectId)
		if err != nil {
			log.ErrorWithCtx(ctx, "convertAttitudeCounts v:%v err:%v", v, err)
			continue
		}
		res = append(res, &pb.GetAttitudeCountResponse_AttitudeCountInfo{
			Object: &pb.Object{
				ObjectId:   uint32(id),
				ObjectType: v.ObjectType,
			},
			Count: v.Count,
		})
	}
	return res
}

// HadAttitude 是否已点赞
func (s *Server) HadAttitude(ctx context.Context, req *pb.HadAttitudeRequest) (*pb.HadAttitudeResponse, error) {
	out := &pb.HadAttitudeResponse{}
	uid := metainfo.GetServiceInfo(ctx).UserID()
	if req.GetAttitudeObject() == nil ||
		req.GetAttitudeObject().GetObjectType() == pb.ObjectType_OBJECT_TYPE_UNSPECIFIED ||
		req.GetAttitudeObject().GetObjectId() == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	var err error
	out.HadAttitude, err = s.attitudeMgr.HadAttitude(ctx, uid, req.GetAttitudeObject().GetObjectType(), req.GetAttitudeObject().GetObjectId())
	if err != nil {
		log.ErrorWithCtx(ctx, "HadAttitude uid:%d req:%s err:%v", uid, req.String(), err)
		return out, err
	}

	log.InfoWithCtx(ctx, "HadAttitude success req:%s out:%s", req.String(), out.String())
	return out, nil
}
