package internal

import (
	"context"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/aigc/aigc-common"
	aigc_soulmate_middle "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle"
	aigc_trigger "golang.52tt.com/protocol/services/aigc/aigc-trigger"
	config "golang.52tt.com/services/aigc/aigc-common/internal/config/ttconfig"
	"golang.52tt.com/services/aigc/aigc-common/internal/entity"
	"time"
)

func (s *Server) AddExtraCount(ctx context.Context, request *pb.AddExtraCountRequest) (*pb.AddExtraCountResponse, error) {
	out := &pb.AddExtraCountResponse{}
	if request.GetUid() == 0 || request.GetExtraCount() == 0 || request.GetExpireTime() <= 0 {
		log.ErrorWithCtx(ctx, "AddExtraCount request invalid: %s", request.String())
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	if !config.GetAigcCommonConfig().GetAddExtraCountSwitch() {
		log.InfoWithCtx(ctx, "AddExtraCount switch is off, request:%s", request.String())
		return out, nil
	}
	err := s.sentenceMgr.AddExtraCount(ctx, &entity.ExtraSentenceRecord{
		Id:           primitive.NewObjectID(),
		Uid:          request.GetUid(),
		BusinessType: request.GetBusinessType(),
		CurCount:     request.GetExtraCount(),
		ExpiredTime:  request.GetExpireTime() + time.Now().Unix(),
		CreateTime:   time.Now(),
		RewardType:   request.GetRewardType(),
		AwardCount:   request.GetExtraCount(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AddExtraCount request:%s err: %v", request.String(), err)
		return out, err
	}
	log.InfoWithCtx(ctx, "AddExtraCount success request:%s", request.String())
	return out, nil
}

func (s *Server) ConsumeSentenceCount(ctx context.Context, request *pb.ConsumeSentenceCountRequest) (*pb.ConsumeSentenceCountResponse, error) {
	out := &pb.ConsumeSentenceCountResponse{}
	if request.GetUid() == 0 ||
		aigc_trigger.BusinessType(request.GetBusinessType()) == aigc_trigger.BusinessType_BUSINESS_TYPE_UNSPECIFIED ||
		aigc_soulmate_middle.SentenceType(request.GetSentenceType()) == aigc_soulmate_middle.SentenceType_SENTENCE_TYPE_UNSPECIFIED {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	var success bool
	var curNum uint32
	var err error
	curNum, success, err = s.sentenceMgr.ConsumeSentenceCount(ctx, request.GetUid(), request.GetSentenceType(),
		request.GetBusinessType(), request.GetRoleType(), request.GetEntity())
	if err != nil {
		log.ErrorWithCtx(ctx, "ConsumeSentenceCount ConsumeSentenceCount request:%s err: %v", request.String(), err)
		return out, err
	}

	out.Success = success
	out.CurNum = curNum
	log.InfoWithCtx(ctx, "ConsumeSentenceCount success request:%s out:%s ", request.String(), out.String())
	return out, nil
}

// 专属句数、今日句数返回使用数量， 额外句数返回当前剩余有效句数
func (s *Server) BatGetCurSentenceCount(ctx context.Context, request *pb.BatGetCurSentenceCountRequest) (
	*pb.BatGetCurSentenceCountResponse, error) {
	out := &pb.BatGetCurSentenceCountResponse{}

	if request.GetUid() == 0 ||
		aigc_trigger.BusinessType(request.GetBusinessType()) == aigc_trigger.BusinessType_BUSINESS_TYPE_UNSPECIFIED ||
		(len(request.GetSentenceType()) == 0 && !request.GetNeedAvailableExtraCount()) {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	if len(request.GetSentenceType()) > 0 {
		countMap, err := s.sentenceMgr.BatGetTodaySentenceCount(ctx, request.GetUid(), request.GetBusinessType(),
			request.GetRoleType(), request.GetSentenceType(), request.GetEntity())
		if err != nil {
			log.ErrorWithCtx(ctx, "BatGetCurSentenceCount BatGetTodaySentenceCount request:%s err: %v", request.String(), err)
			return out, err
		}
		out.CurNumMap = countMap
	}
	if request.GetNeedAvailableExtraCount() {
		availableCount, err := s.sentenceMgr.GetAvailableExtraCount(ctx, request.GetUid(), request.GetBusinessType())
		if err != nil {
			log.ErrorWithCtx(ctx, "BatGetCurSentenceCount GetAvailableExtraCount request:%s err: %v", request.String(), err)
			return out, err
		}
		out.AvailableExtraNum = availableCount
	}
	log.InfoWithCtx(ctx, "BatGetCurSentenceCount success request:%s out:%s", request.String(), out.String())
	return out, nil
}

func (s *Server) GetUsedSentenceCount(ctx context.Context, req *pb.GetUsedSentenceCountReq) (
	*pb.GetUsedSentenceCountResp, error) {
	out := &pb.GetUsedSentenceCountResp{}
	if req.GetUid() == 0 || len(req.GetCountTypes()) == 0 ||
		aigc_trigger.BusinessType(req.GetBusinessType()) == aigc_trigger.BusinessType_BUSINESS_TYPE_UNSPECIFIED {
		log.ErrorWithCtx(ctx, "GetUsedSentenceCount request invalid: %s", req.String())
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	var needToday, needTotal bool
	var err error
	for _, countType := range req.GetCountTypes() {
		if countType == pb.CountType_COUNT_TYPE_TODAY {
			needToday = true
		} else if countType == pb.CountType_COUNT_TYPE_TOTAL {
			needTotal = true
		} else {
			log.WarnWithCtx(ctx, "GetUsedSentenceCount unknown countType: %s", countType.String())
		}
	}
	if needToday {
		out.TodayTotalCount, err = s.sentenceMgr.GetTodayUsedTotalCount(ctx, req.GetUid(), req.GetBusinessType())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUsedSentenceCount GetTodayUsedTotalCount request:%s err: %v", req.String(), err)
			return out, err
		}
	}
	if needTotal {
		out.TotalCount, err = s.sentenceMgr.GetHistoryTotalCount(ctx, req.GetUid(), req.GetBusinessType())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUsedSentenceCount GetTodayUsedTotalCount request:%s err: %v", req.String(), err)
			return out, err
		}
	}

	log.InfoWithCtx(ctx, "GetUsedSentenceCount success req:%s out:%s", req.String(), out.String())
	return out, nil
}

func (s *Server) GetExtraCountByRewardTypes(ctx context.Context, req *pb.GetExtraCountByRewardTypesRequest) (*pb.GetExtraCountByRewardTypesResponse, error) {
	out := &pb.GetExtraCountByRewardTypesResponse{}
	if req.GetUid() == 0 {
		log.ErrorWithCtx(ctx, "GetExtraCountByRewardTypes request invalid: %s", req.String())
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	totalNumMap, err := s.sentenceMgr.GetTotalAwardCountByTypes(ctx, req.GetUid(), req.GetBusinessType(), req.GetRewardTypes())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetExtraCountByRewardTypes GetTotalAwardCountByTypes request:%s err: %v", req.String(), err)
		return out, err
	}
	out.ExtraCountMap = totalNumMap
	log.InfoWithCtx(ctx, "GetExtraCountByRewardTypes success req:%s out:%s", req.String(), out.String())
	return out, nil
}
