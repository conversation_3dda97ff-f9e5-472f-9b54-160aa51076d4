package store

import (
	"context"
	pb "golang.52tt.com/protocol/services/aigc/aigc-common"
	"golang.52tt.com/services/aigc/aigc-common/internal/entity"
)

//go:generate mockgen -destination=mocks/attitude.go -package=mocks golang.52tt.com/services/aigc/aigc-common/internal/store AttitudeStore
type AttitudeStore interface {
	UpsertLike(ctx context.Context, like *entity.UserLike) error
	CancelLike(ctx context.Context, uid uint32, objectId string, objectType pb.ObjectType) error
	GetLike(ctx context.Context, uid uint32, objectId string, objectType pb.ObjectType) (*entity.UserLike, error)
	IncrLikes(ctx context.Context, objectId string, objectType pb.ObjectType) error
	DecrLikes(ctx context.Context, objectId string, objectType pb.ObjectType) error
	GetLikeCounts(ctx context.Context, objects []*entity.Object) ([]*entity.ObjetLikeCount, error)
}

//go:generate mockgen -destination=mocks/attitude.go -package=mocks golang.52tt.com/services/aigc/aigc-common/internal/store SentenceStore
type SentenceStore interface {
	AddRecord(ctx context.Context, extraRecord *entity.ExtraSentenceRecord) error
	FindAndUseExtra(ctx context.Context, uid uint32, businessType uint32, incr int) (success bool, err error)
	HasExtraCount(ctx context.Context, uid uint32, businessType uint32) bool
	GetTotalAvailableExtraCount(ctx context.Context, uid, businessType uint32) (uint32, error)
	BatUpdateTotalCount(ctx context.Context, uids []uint32, businessType uint32) error
	BatGetTotalCount(ctx context.Context, uids []uint32, businessType uint32) (map[uint32]uint32, error)
	GetTotalAwardCountByTypes(ctx context.Context, uid, businessType uint32,
		rewardTypes []uint32) (map[uint32]uint32, error)
}
