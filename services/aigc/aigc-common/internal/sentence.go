package internal

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/aigc/aigc-common"
	aigc_soulmate_middle "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle"
	aigc_trigger "golang.52tt.com/protocol/services/aigc/aigc-trigger"
	"golang.52tt.com/services/aigc/aigc-common/internal/cache/redis"
)

func (s *Server) GetSentenceCountMap(ctx context.Context, req *pb.GetSentenceCountMapRequest) (*pb.GetSentenceCountMapResponse, error) {
	out := &pb.GetSentenceCountMapResponse{}
	if req.GetUid() == 0 ||
		len(req.GetType()) == 0 ||
		req.GetEntity().GetType() == pb.Entity_TYPE_UNSPECIFIED ||
		req.GetEntity().GetId() == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	
	countMap, err := s.sentenceMgr.GetSentenceCountByTypes(ctx, req.GetUid(), req.GetType(), req.GetEntity())
	if err != nil {
		log.ErrorWithCtx(ctx, "IsSentenceAvailable GetSentenceCountByTypes req:%s err: %v", req.String(), err)
		return out, err
	}
	out.CountMap = make(map[uint32]uint32, len(req.GetType()))
	for _, t := range req.GetType() {
		out.CountMap[t] = countMap[redis.EncodeSpecifiedSentenceField(req.GetUid(), t, req.GetEntity())]
	}
	log.InfoWithCtx(ctx, "GetSentenceCountMap success req:%s resp:%s", req.String(), out.String())
	return out, nil
}

func (s *Server) AddSentenceCount(ctx context.Context, req *pb.AddSentenceCountRequest) (*pb.AddSentenceCountResponse, error) {
	out := &pb.AddSentenceCountResponse{}
	if req.GetUid() == 0 ||
		req.GetType() == uint32(aigc_soulmate_middle.SentenceType_SENTENCE_TYPE_UNSPECIFIED) ||
		req.GetEntity().GetType() == pb.Entity_TYPE_UNSPECIFIED ||
		req.GetEntity().GetId() == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	
	err := s.sentenceMgr.AddSentenceCount(ctx, req.GetUid(), req.GetEntity(), req.GetType(), 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddSentenceCount AddSentenceCount req:%s err: %v", req.String(), err)
		return out, err
	}
	err = s.sentenceMgr.AddUserCount(ctx, req.GetUid(), uint32(req.GetEntity().GetType()), req.GetType())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddSentenceCount AddUserCount req:%s err: %v", req.String(), err)
		return out, err
	}
	log.InfoWithCtx(ctx, "AddSentenceCount success req:%s", req.String())
	return out, nil
}

func (s *Server) GetUserSentenceCount(ctx context.Context, req *pb.GetUserSentenceCountReq) (*pb.GetUserSentenceCountResp, error) {
	out := &pb.GetUserSentenceCountResp{}
	if req.GetUid() == 0 ||
		req.GetEntityType() == pb.Entity_TYPE_UNSPECIFIED ||
		aigc_soulmate_middle.SentenceType(req.GetSentenceType()) == aigc_soulmate_middle.SentenceType_SENTENCE_TYPE_UNSPECIFIED {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	var err error
	out.Count, err = s.sentenceMgr.GetUserCount(ctx, req.GetUid(), uint32(req.GetEntityType()), req.GetSentenceType())
	if err != nil {
		log.ErrorWithCtx(ctx, "getUserSentenceCount GetUserCount req:%s err: %v", req.String(), err)
		return out, err
	}
	log.InfoWithCtx(ctx, "getUserSentenceCount success req:%s out:%s", req.String(), out.String())
	return out, nil
}

func (s *Server) AddBussSentenceCount(ctx context.Context, req *pb.AddBussSentenceCountRequest) (*pb.AddBussSentenceCountResponse, error) {
	out := &pb.AddBussSentenceCountResponse{}
	if req.GetUid() == 0 ||
		req.GetType() == uint32(aigc_soulmate_middle.SentenceType_SENTENCE_TYPE_UNSPECIFIED) ||
		aigc_trigger.BusinessType(req.GetBusinessType()) == aigc_trigger.BusinessType_BUSINESS_TYPE_UNSPECIFIED {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	curNum, err := s.sentenceMgr.AddBussCount(ctx, req.GetUid(), req.GetBusinessType(), req.GetType(), 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddBussSentenceCount AddBussCount req:%s err: %v", req.String(), err)
		return out, err
	}
	out.CurNum = curNum
	log.InfoWithCtx(ctx, "AddBussSentenceCount success req:%s sentenceNum curNum:%d ", req.String(), curNum)
	return out, nil
}

func (s *Server) DecrBussSentenceCount(ctx context.Context, req *pb.DecrBussSentenceCountRequest) (*pb.DecrBussSentenceCountResponse, error) {
	out := &pb.DecrBussSentenceCountResponse{}
	if req.GetUid() == 0 ||
		req.GetType() == uint32(aigc_soulmate_middle.SentenceType_SENTENCE_TYPE_UNSPECIFIED) ||
		aigc_trigger.BusinessType(req.GetBusinessType()) == aigc_trigger.BusinessType_BUSINESS_TYPE_UNSPECIFIED {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	curNum, err := s.sentenceMgr.AddBussCount(ctx, req.GetUid(), req.GetBusinessType(), req.GetType(), -1)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddBussSentenceCount AddBussCount req:%s err: %v", req.String(), err)
		return out, err
	}
	
	log.InfoWithCtx(ctx, "DecrBussSentenceCount success req:%s sentenceNum curNum:%d ", req.String(), curNum)
	return out, nil
}

func (s *Server) BatAddBussSentenceCount(ctx context.Context, req *pb.BatAddBussSentenceCountRequest) (*pb.BatAddBussSentenceCountResponse, error) {
	out := &pb.BatAddBussSentenceCountResponse{}
	if len(req.GetUids()) == 0 ||
		req.GetType() == uint32(aigc_soulmate_middle.SentenceType_SENTENCE_TYPE_UNSPECIFIED) ||
		aigc_trigger.BusinessType(req.GetBusinessType()) == aigc_trigger.BusinessType_BUSINESS_TYPE_UNSPECIFIED {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	
	curNumMap, err := s.sentenceMgr.BatAddBussCount(ctx, req.GetUids(), req.GetBusinessType(), req.GetType(), 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddBussSentenceCount AddBussCount req:%s err: %v", req.String(), err)
		return out, err
	}
	out.CurNumMap = curNumMap
	log.InfoWithCtx(ctx, "BatAddBussSentenceCount success req:%s sentenceNum curNum :%v ", req.String(), curNumMap)
	
	return out, nil
}
