package internal

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/aigc/aigc-common"
	"golang.52tt.com/services/aigc/aigc-common/internal/entity"
	"time"
)

func (s *Server) AddToDelayQueue(ctx context.Context, req *pb.AddToDelayQueueRequest) (*pb.AddToDelayQueueResponse, error) {
	out := &pb.AddToDelayQueueResponse{}
	if req.GetPushTo() == pb.PushTo_PUSH_TO_UNSPECIFIED {
		log.ErrorWithCtx(ctx, "AddToDelayQueue invalid req :%s", req.String())
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	record := &entity.DelayRecord{
		TaskId:         genTaskId(),
		ShouldSendTime: time.Now().Unix() + req.GetDelayTime(),
		DelayTime:      req.GetDelayTime(),
		Data:           req.GetData(),
		PushTo:         req.GetPushTo(),
		ReceiveMsg:     req.GetReceiveMsg(),
	}
	err := s.delayMgr.AddDelayMsg(ctx, record)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddToDelayQueue AddDelayMsg req:%s err: %v", req.String(), err)
		return out, err
	}
	log.InfoWithCtx(ctx, "AddToDelayQueue success req:%s", req.String())
	return out, nil
}

func genTaskId() string {
	return primitive.NewObjectID().Hex()
}
