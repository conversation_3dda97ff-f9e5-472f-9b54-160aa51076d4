package main

import (
	"context"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/grpc"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/grpc/server" // server startup

	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"

	"golang.52tt.com/services/aigc/aigc-intimacy/internal"

	_ "golang.52tt.com/pkg/hub/tyr/compatible/server"
)

func main() {
	var (
		svr *internal.Server
		cfg = &internal.StartConfig{}
		err error
	)

	// config file support yaml & json, default aigc-intimacy.json/yaml
	if err := server.NewServer("aigc-intimacy", cfg).
		AddGrpcServer(server.NewBuildOption().
			WithInitializeFunc(func(ctx context.Context, s *grpc.Server) error {
				if svr, err = internal.NewServer(ctx, cfg); err != nil {
					return err
				}

				// register custom grpc server
				pb.RegisterAigcIntimacyServer(s, svr)
				return nil
			}).WithEventLinkSubscription(),
		).
		WithCloseFunc(func(ctx context.Context) {
			// do something when server terminating
			svr.ShutDown()
		}).
		Start(); err != nil {
		log.Errorf("server start fail, err: %v", err)
	}
}
