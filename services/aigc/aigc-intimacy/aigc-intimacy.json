{"server.grpcListen": ":80", "server.adminListen": ":8078", "mongo": {"addrs": "10.34.6.51:27017", "database": "aigc_intimacy", "user_name": "aigc_intimacy_rw", "password": "5JH*URWSabeIvEq", "max_pool_size": 5}, "event_link": {"publisher": {"intimacy": {"kafka": {"clientID": "aigc-intimacy", "brokers": ["hobby-channel-kafka-broker-01.database.svc.cluster.local:9092"]}, "topics": ["switch_relation_event", "aigc_intimacy_level_up"]}}, "subscriber": {"chat_level_up": {"kafka": {"brokers": ["hobby-channel-kafka-broker-01.database.svc.cluster.local:9092"], "clientID": "aigc-intimacy-levelup"}, "topics": ["aigc_msg"], "groupID": "aigc-intimacy-levelup", "maxRetryTimes": 5, "processWorkerNum": 1}}}, "redis": {"host": "redis-test-tc-bj-tt-go-05.database.svc.cluster.local", "port": 6379, "protocol": "tcp", "ping_interval": 300, "database": 15}}