package mongo

import (
	"context"
	"github.com/pkg/errors"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/store"
	"time"
)

type chatBackgroundMongoStore struct {
	chatBackgroundCol *mongo.Collection
	backgroundBindCol *mongo.Collection
}

func NewBackgroundMongoStore(ctx context.Context, mongoDB *db.MongoDB) store.ChatBackgroundStore {
	st := &chatBackgroundMongoStore{
		chatBackgroundCol: mongoDB.Database().Collection("chat_background"),
		backgroundBindCol: mongoDB.Database().Collection("background_bind"),
	}
	_, err := st.chatBackgroundCol.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			// 查询角色绑定背景图
			Keys: bson.D{
				{Key: "bind_entity_type", Value: 1},
				{Key: "bind_entity_ids", Value: 1},
				{Key: "created_at", Value: -1},
			},
		},
		{
			// 根据名称查询背景图
			Keys: bson.D{
				{Key: "name", Value: 1},
			},
		},
	})
	if err != nil {
		log.WarnWithCtx(ctx, "NewBackgroundMongoStore collection(chatBackgroundCol) CreateMany err: %v", err)
	}

	return st
}

func (c *chatBackgroundMongoStore) Save(ctx context.Context, background *entity.ChatBackground) error {
	now := time.Now()
	_, err := c.chatBackgroundCol.UpdateByID(ctx,
		background.ID,
		bson.M{
			"$setOnInsert": bson.M{
				"created_at": now,
			},
			"$set": bson.M{
				"name":             background.Name,
				"image_url":        background.ImageUrl,
				"show_avatar":      background.ShowAvatar,
				"default_unlock":   background.DefaultUnlock,
				"unlock_condition": background.UnlockCondition,
				"bind_entity_type": background.BindEntityType,
				"bind_entity_ids":  background.BindEntityIds,
				"updated_at":       now,
			},
		},
		options.Update().SetUpsert(true),
	)

	return err
}

func (c *chatBackgroundMongoStore) Delete(ctx context.Context, id string) error {
	_, err := c.chatBackgroundCol.DeleteOne(ctx, bson.M{"_id": id})
	return err
}

func (c *chatBackgroundMongoStore) Get(ctx context.Context, id string) (*entity.ChatBackground, error) {
	background := &entity.ChatBackground{}
	err := c.chatBackgroundCol.FindOne(ctx, bson.M{"_id": id}).Decode(background)
	if errors.Is(err, mongo.ErrNoDocuments) {
		log.WarnWithCtx(ctx, "chatBackgroundMongoStore Get id(%d) not found", id)
		return nil, nil
	}

	return background, err
}

func (c *chatBackgroundMongoStore) BatchGet(ctx context.Context, ids []string) ([]*entity.ChatBackground, error) {
	filter := bson.M{}
	if len(ids) > 0 {
		filter["_id"] = bson.M{"$in": ids}
	}
	opts := options.Find().SetSort(bson.M{"_id": -1})

	var backgrounds []*entity.ChatBackground
	cursor, err := c.chatBackgroundCol.Find(ctx, filter, opts)
	if err != nil {
		log.ErrorWithCtx(ctx, "chatBackgroundMongoStore BatchGet chatBackgroundCol.Find err: %v", err)
		return nil, err
	}
	err = cursor.All(ctx, &backgrounds)
	if err != nil {
		log.ErrorWithCtx(ctx, "chatBackgroundMongoStore BatchGet cursor.All err: %v", err)
		return nil, err
	}

	return backgrounds, nil
}

func (c *chatBackgroundMongoStore) GetByPage(ctx context.Context, page, pageSize int64, needCount bool, name string) ([]*entity.ChatBackground, int64, error) {
	opts := options.Find().
		SetSkip((page - 1) * pageSize).
		SetLimit(pageSize).
		SetSort(bson.M{"_id": -1})

	filter := bson.M{}
	if name != "" {
		filter["name"] = bson.M{"$regex": primitive.Regex{Pattern: name}}
	}

	cursor, err := c.chatBackgroundCol.Find(ctx, filter, opts)
	if err != nil {
		log.ErrorWithCtx(ctx, "chatBackgroundMongoStore GetByPage chatBackgroundCol.Find err: %v", err)
		return nil, 0, err
	}

	var backgrounds []*entity.ChatBackground
	err = cursor.All(ctx, &backgrounds)
	if err != nil {
		log.ErrorWithCtx(ctx, "chatBackgroundMongoStore GetByPage cursor.All err: %v", err)
		return nil, 0, err
	}

	var total int64
	if needCount {
		total, err = c.chatBackgroundCol.CountDocuments(ctx, bson.M{})
		if err != nil {
			log.ErrorWithCtx(ctx, "chatBackgroundMongoStore GetByPage chatBackgroundCol.CountDocuments err: %v", err)
			return nil, 0, err
		}
	}

	return backgrounds, total, nil
}

func (c *chatBackgroundMongoStore) GetByBindRoleId(ctx context.Context, roleId uint32) ([]*entity.ChatBackground, error) {
	filter := bson.M{
		"$or": []bson.M{
			{
				// 绑定所有角色
				"bind_entity_type": pb.ChatBackground_BIND_ENTITY_TYPE_ALL_ROLE,
			},
			{
				// 绑定指定角色
				"bind_entity_type": pb.ChatBackground_BIND_ENTITY_TYPE_ROLE_LIST,
				"bind_entity_ids": bson.M{
					"$elemMatch": bson.M{
						"$eq": roleId,
					},
				},
			},
		},
	}
	opts := options.Find().SetSort(bson.M{"created_at": -1})

	cursor, err := c.chatBackgroundCol.Find(ctx, filter, opts)
	if err != nil {
		log.ErrorWithCtx(ctx, "chatBackgroundMongoStore GetByBindEntity chatBackgroundCol.Find err: %v", err)
		return nil, err
	}

	var backgrounds []*entity.ChatBackground
	err = cursor.All(ctx, &backgrounds)
	if err != nil {
		log.ErrorWithCtx(ctx, "chatBackgroundMongoStore GetByBindEntity cursor.All err: %v", err)
		return nil, err
	}

	return backgrounds, nil
}
