package mongo

import (
	"context"

	"go.mongodb.org/mongo-driver/mongo"

	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/infra/db"
)

var (
	utLevelCols              *mongo.Collection
	utGrowthLogCols          *mongo.Collection
	utLevelUpCondProgressCol *mongo.Collection
)

func init() {
	mongoConfig := &config.MongoConfig{
		Addrs:       "10.34.6.51:27017",
		Database:    "aigc_intimacy",
		MaxPoolSize: 1,
		UserName:    "aigc_intimacy_rw",
		Password:    "5JH*URWSabeIvEq",
	}

	utDB, err := db.NewMongoDB(context.Background(), mongoConfig)
	if err != nil {
		log.Fatalf("NewMongoDB config(%+v) err: %v", mongoConfig, err)
	}

	utLevelCols = utDB.Database().Collection("levels")
	utGrowthLogCols = utDB.Database().Collection("growth_logs")
	utLevelUpCondProgressCol = utDB.Database().Collection("level_up_cond_progress")
}
