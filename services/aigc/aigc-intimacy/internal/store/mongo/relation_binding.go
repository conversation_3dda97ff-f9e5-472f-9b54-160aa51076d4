package mongo

import (
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
)

func (r *relationshipMongoStore) UpsertRelationBinding(ctx context.Context, binding *entity.ObjectRelationBinding) error {
	res := r.relationBinding.FindOneAndUpdate(
		ctx,
		bson.M{"_id": binding.ID},
		bson.M{

			"$set": bson.M{
				"relation_id": binding.RelationID,
				"updated_at":  binding.UpdateAt,
			},
		},
		options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After),
	)
	return res.Err()
}

func (r *relationshipMongoStore) GetRelationBinding(ctx context.Context, entityInst *pb.Entity) (*entity.ObjectRelationBinding, error) {
	res := r.relationBinding.FindOne(ctx, bson.M{"_id": entity.EncodeID(0, entityInst)})
	if err := res.Err(); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}
		return nil, err
	}

	var binding entity.ObjectRelationBinding
	return &binding, res.Decode(&binding)
}

func (r *relationshipMongoStore) DeleteRelationBinding(ctx context.Context, entityInst *pb.Entity) error {
	_, err := r.relationBinding.DeleteOne(ctx, bson.M{"_id": entity.EncodeID(0, entityInst)})
	return err
}

func (r *relationshipMongoStore) BatchGetRelationBinding(ctx context.Context, entityInsts []*pb.Entity) (map[string]*entity.ObjectRelationBinding, error) {
	ids := make([]interface{}, 0, len(entityInsts))
	for _, entityInst := range entityInsts {
		ids = append(ids, entity.EncodeID(0, entityInst))
	}

	cursor, err := r.relationBinding.Find(ctx, bson.M{"_id": bson.M{"$in": ids}})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	bindings := make(map[string]*entity.ObjectRelationBinding, len(entityInsts))
	for cursor.Next(ctx) {
		var binding entity.ObjectRelationBinding
		if err := cursor.Decode(&binding); err != nil {
			return nil, err
		}
		bindings[binding.ID] = &binding
	}

	return bindings, nil
}
