package mongo

import (
	"context"
	"testing"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
)

func Test_levelMongoStore_CreateOrIncrValue(t *testing.T) {
	type fields struct {
		logs   *mongo.Collection
		levels *mongo.Collection
	}
	type args struct {
		ctx      context.Context
		level    *entity.Level
		value    uint32
		serialNo string
		reason   string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				logs:   utGrowthLogCols,
				levels: utLevelCols,
			},
			args: args{
				ctx: context.Background(),
				level: &entity.Level{
					ID:        "1:1:1",
					CreatedAt: time.Now(),
					Uid:       2405864,
					Level:     1,
				},
				value:    5,
				serialNo: primitive.NewObjectID().Hex(),
				reason:   "unit test",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &levelMongoStore{
				logs:   tt.fields.logs,
				levels: tt.fields.levels,
			}
			got, ok, err := s.CreateOrIncrValue(tt.args.ctx, tt.args.level, tt.args.value, tt.args.serialNo, tt.args.reason)
			if (err != nil) != tt.wantErr {
				t.Errorf("levelMongoStore.CreateOrIncrValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("levelMongoStore.CreateOrIncrValue() = %v, %v", got, ok)
		})
	}
}

func Test_levelMongoStore_UpdateLevel(t *testing.T) {
	type fields struct {
		levels *mongo.Collection
	}
	type args struct {
		ctx  context.Context
		id   string
		oLv  uint32
		oVal uint32
		nLv  uint32
		nVal uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				levels: utLevelCols,
			},
			args: args{
				ctx:  context.Background(),
				id:   "1:1:1",
				oLv:  1,
				oVal: 10,
				nLv:  2,
				nVal: 5,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &levelMongoStore{
				levels: tt.fields.levels,
			}
			got, err := s.UpdateLevel(tt.args.ctx, tt.args.id, tt.args.oLv, tt.args.oVal, tt.args.nLv, tt.args.nVal)
			if (err != nil) != tt.wantErr {
				t.Errorf("levelMongoStore.UpdateLevel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("levelMongoStore.UpdateLevel() = %v", got)
		})
	}
}
