package mongo

import (
	"context"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/store"
	"time"
)

type relationshipMongoStore struct {
	relationshipCol *mongo.Collection
	relationBinding *mongo.Collection
}

func NewRelationshipMongoStore(ctx context.Context, mongoDB *db.MongoDB) store.RelationshipStore {
	st := &relationshipMongoStore{
		relationshipCol: mongoDB.Database().Collection("relationship"),
		relationBinding: mongoDB.Database().Collection("relation_binding"),
	}

	return st
}

func (r *relationshipMongoStore) Save(ctx context.Context, relationship *entity.Relationship) error {
	now := time.Now()
	_, err := r.relationshipCol.UpdateByID(ctx,
		relationship.ID,
		bson.M{
			"$setOnInsert": bson.M{
				"created_at": now,
			},
			"$set": bson.M{
				"name":                    relationship.Name,
				"aigc_params":             relationship.AIGCParams,
				"bind_background_id":      relationship.BindBackgroundID,
				"unlock_condition":        relationship.UnlockCondition,
				"icon":                    relationship.Icon,
				"intimacy_background_img": relationship.IntimacyBackgroundImg,
				"updated_at":              now,
			},
		},
		options.Update().SetUpsert(true),
	)

	return err
}

func (r *relationshipMongoStore) Delete(ctx context.Context, id string) error {
	_, err := r.relationshipCol.DeleteOne(ctx, bson.M{"_id": id})
	return err
}

func (r *relationshipMongoStore) Get(ctx context.Context, id string) (*entity.Relationship, error) {
	relationship := &entity.Relationship{}
	err := r.relationshipCol.FindOne(ctx, bson.M{"_id": id}).Decode(relationship)
	if errors.Is(err, mongo.ErrNoDocuments) {
		log.WarnWithCtx(ctx, "relationshipMongoStore Get id(%d) not found", id)
		return nil, nil
	}

	return relationship, err
}

func (r *relationshipMongoStore) BatchGet(ctx context.Context, ids []string) ([]*entity.Relationship, error) {
	filter := bson.M{}
	if len(ids) > 0 {
		filter["_id"] = bson.M{"$in": ids}
	}
	opts := options.Find().SetSort(bson.M{"_id": -1})

	cursor, err := r.relationshipCol.Find(ctx, filter, opts)
	if err != nil {
		log.ErrorWithCtx(ctx, "relationshipMongoStore BatchGet relationshipCol.Find err: %v", err)
		return nil, err
	}

	var relationships []*entity.Relationship
	err = cursor.All(ctx, &relationships)
	if err != nil {
		log.ErrorWithCtx(ctx, "relationshipMongoStore BatchGet cursor.All err: %v", err)
		return nil, err
	}

	return relationships, nil
}
