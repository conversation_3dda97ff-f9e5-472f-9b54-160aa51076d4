package mongo

import (
	"context"
	"github.com/pkg/errors"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/store"
	"time"
)

type intimacyConditionMongoStore struct {
	intimacyConditionCol *mongo.Collection
}

func NewIntimacyConditionMongoStore(ctx context.Context, mongoDB *db.MongoDB) store.IntimacyConditionStore {
	st := &intimacyConditionMongoStore{
		intimacyConditionCol: mongoDB.Database().Collection("intimacy_condition"),
	}

	return st
}

func (i *intimacyConditionMongoStore) Save(ctx context.Context, condition *entity.IntimacyCondition) error {
	now := time.Now()
	_, err := i.intimacyConditionCol.UpdateByID(
		ctx,
		condition.ID,
		bson.M{
			"$setOnInsert": bson.M{
				"condition_type": condition.ConditionType,
				"created_at":     now,
			},
			"$set": bson.M{
				"require_count": condition.RequireCount,
				"value_add":     condition.ValueAdd,
				"daily_limit":   condition.DailyLimit,
				"icon":          condition.Icon,
				"title":         condition.Title,
				"desc":          condition.Desc,
				"updated_at":    now,
			},
		},
		options.Update().SetUpsert(true),
	)
	return err
}

func (i *intimacyConditionMongoStore) Delete(ctx context.Context, id string) error {
	_, err := i.intimacyConditionCol.DeleteOne(ctx, bson.M{"_id": id})
	return err
}

func (i *intimacyConditionMongoStore) Get(ctx context.Context, id string) (*entity.IntimacyCondition, error) {
	condition := &entity.IntimacyCondition{}
	err := i.intimacyConditionCol.FindOne(ctx, bson.M{"_id": id}).Decode(condition)
	if errors.Is(err, mongo.ErrNoDocuments) {
		log.WarnWithCtx(ctx, "intimacyConditionMongoStore Get id(%d) not found", id)
		return nil, nil
	}

	return condition, err
}

func (i *intimacyConditionMongoStore) BatchGet(ctx context.Context, ids []string) ([]*entity.IntimacyCondition, error) {
	filter := bson.M{}
	if len(ids) > 0 {
		filter["_id"] = bson.M{"$in": ids}
	}
	opts := options.Find().SetSort(bson.M{"_id": -1})

	cursor, err := i.intimacyConditionCol.Find(ctx, filter, opts)
	if err != nil {
		log.ErrorWithCtx(ctx, "intimacyConditionMongoStore BatchGet intimacyConditionCol.Find err: %v", err)
		return nil, err
	}

	var conditions []*entity.IntimacyCondition
	err = cursor.All(ctx, &conditions)
	if err != nil {
		log.ErrorWithCtx(ctx, "intimacyConditionMongoStore BatchGet cursor.All err: %v", err)
		return nil, err
	}

	return conditions, nil
}
