package mongo

import (
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
)

func (c *chatBackgroundMongoStore) UpsertBackgroundBinding(ctx context.Context, binding *entity.ObjectBackgroundBinding) error {
	res := c.backgroundBindCol.FindOneAndUpdate(
		ctx,
		bson.M{"_id": binding.ID},
		bson.M{

			"$set": bson.M{
				"background_id": binding.BackgroundID,
				"updated_at":    binding.UpdateAt,
			},
		},
		options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After),
	)
	return res.Err()
}

func (c *chatBackgroundMongoStore) GetBackgroundBinding(ctx context.Context, entityInst *pb.Entity) (*entity.ObjectBackgroundBinding, error) {
	res := c.backgroundBindCol.FindOne(ctx, bson.M{"_id": entity.EncodeID(0, entityInst)})
	if err := res.Err(); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}
		return nil, err
	}

	var binding entity.ObjectBackgroundBinding
	return &binding, res.Decode(&binding)
}

func (c *chatBackgroundMongoStore) DeleteBackgroundBinding(ctx context.Context, entityInst *pb.Entity) error {
	_, err := c.backgroundBindCol.DeleteOne(ctx, bson.M{"_id": entity.EncodeID(0, entityInst)})
	return err
}