package mongo

import (
	"context"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/store"
	"time"
)

type intimacyLevelConfMongoStore struct {
	intimacyLevelConfCol *mongo.Collection
}

func NewIntimacyLevelConfMongoStore(ctx context.Context, mongoDB *db.MongoDB) store.IntimacyLevelConfStore {
	st := &intimacyLevelConfMongoStore{
		intimacyLevelConfCol: mongoDB.Database().Collection("intimacy_level_conf"),
	}

	return st
}

func (i *intimacyLevelConfMongoStore) Save(ctx context.Context, level *entity.IntimacyLevelConf) error {
	now := time.Now()
	_, err := i.intimacyLevelConfCol.UpdateByID(
		ctx,
		level.ID,
		bson.M{
			"$setOnInsert": bson.M{
				"level":      level.Level,
				"created_at": now,
			},
			"$set": bson.M{
				"require_value": level.RequireValue,
				"benefits":      level.Benefits,
				"benefit_desc":  level.BenefitDesc,
				"updated_at":    now,
			},
		},
		options.Update().SetUpsert(true),
	)
	return err
}

func (i *intimacyLevelConfMongoStore) Delete(ctx context.Context, id uint32) error {
	_, err := i.intimacyLevelConfCol.DeleteOne(ctx, bson.M{"_id": id})
	return err
}

func (i *intimacyLevelConfMongoStore) Get(ctx context.Context, id uint32) (*entity.IntimacyLevelConf, error) {
	levelConf := &entity.IntimacyLevelConf{}
	err := i.intimacyLevelConfCol.FindOne(ctx, bson.M{"_id": id}).Decode(levelConf)
	if errors.Is(err, mongo.ErrNoDocuments) {
		log.WarnWithCtx(ctx, "intimacyLevelConfMongoStore Get id(%d) not found", id)
		return nil, nil
	}

	return levelConf, err
}

func (i *intimacyLevelConfMongoStore) BatchGet(ctx context.Context, ids []uint32) ([]*entity.IntimacyLevelConf, error) {
	filter := bson.M{}
	if len(ids) > 0 {
		filter["_id"] = bson.M{"$in": ids}
	}
	opts := options.Find().SetSort(bson.M{"_id": 1})

	cur, err := i.intimacyLevelConfCol.Find(ctx, filter, opts)
	if err != nil {
		log.ErrorWithCtx(ctx, "intimacyLevelConfMongoStore BatchGet intimacyLevelConfCol.Find err: %v", err)
		return nil, err
	}

	var levelConfs []*entity.IntimacyLevelConf
	err = cur.All(ctx, &levelConfs)
	if err != nil {
		log.ErrorWithCtx(ctx, "intimacyLevelConfMongoStore BatchGet cur.All err: %v", err)
		return nil, err
	}

	return levelConfs, nil
}

func (i *intimacyLevelConfMongoStore) GetLessThan(ctx context.Context, level uint32) ([]*entity.IntimacyLevelConf, error) {
	filter := bson.M{}
	filter["_id"] = bson.M{"$lte": level}

	opts := options.Find().SetSort(bson.M{"_id": 1})

	cur, err := i.intimacyLevelConfCol.Find(ctx, filter, opts)
	if err != nil {
		log.ErrorWithCtx(ctx, "intimacyLevelConfMongoStore GetLessThan intimacyLevelConfCol.Find err: %v", err)
		return nil, err
	}

	var levelConfs []*entity.IntimacyLevelConf
	err = cur.All(ctx, &levelConfs)
	if err != nil {
		log.ErrorWithCtx(ctx, "intimacyLevelConfMongoStore GetLessThan cur.All err: %v", err)
		return nil, err
	}

	return levelConfs, nil
}
