package event

import (
	"context"
	"fmt"
	"strings"

	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"

	middleware_subscriber "gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
)

type SubName string
type PubEvent string

const (
	PubEventLevelUp        PubEvent = "intimacy:aigc_intimacy_level_up"
	PubEventSwitchRelation PubEvent = "intimacy:switch_relation_event"

	SubNameChatLevelUp SubName = "chat_level_up"
)

//go:generate mockgen -destination=mocks/event_bus.go -package=mocks golang.52tt.com/services/aigc/aigc-intimacy/internal/event EventBus
type EventBus interface {
	Close()

	Publish(ctx context.Context, e PubEvent, key string, msg any) error
	Subscribe(ctx context.Context, name SubName, process middleware_subscriber.ProcessorContextFunc) error

	PublishSwitchRelationEvent(ctx context.Context, ev *pb.SwitchRelationEvent) error
}

func SplitPubEvent(e PubEvent) (name, topic string, err error) {
	ss := strings.Split(string(e), ":")
	if len(ss) != 2 {
		return "", "", fmt.Errorf("invalid event %s", e)
	}

	return ss[0], ss[1], nil
}
