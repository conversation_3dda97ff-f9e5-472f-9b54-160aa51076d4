package eventlink

import (
	"context"
	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/event"
	"strconv"

	"golang.52tt.com/pkg/log"
)

func (eb *eventBus) PublishSwitchRelationEvent(ctx context.Context, ev *pb.SwitchRelationEvent) error {

	if err := eb.Publish(ctx, event.PubEventSwitchRelation, strconv.Itoa(int(ev.GetUid())), ev); err != nil {
		return err
	}
	log.InfoWithCtx(ctx, "PublishSwitchRelationEvent event(%+v) finished", ev)
	return nil
}
