package internal

import (
	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
)

func assembleBackground(background *entity.ChatBackground) *pb.ChatBackground {
	return &pb.ChatBackground{
		Id:              background.ID,
		Name:            background.Name,
		ImageUrl:        background.ImageUrl,
		ShowAvatar:      background.ShowAvatar,
		DefaultUnlock:   background.DefaultUnlock,
		UnlockCondition: background.UnlockCondition,
		BindEntityType:  background.BindEntityType,
		BindEntityIds:   background.BindEntityIds,
	}
}

func assembleBackgroundList(backgrounds []*entity.ChatBackground) []*pb.ChatBackground {
	pbBackgrounds := make([]*pb.ChatBackground, 0, len(backgrounds))
	for _, background := range backgrounds {
		pbBackgrounds = append(pbBackgrounds, assembleBackground(background))
	}
	return pbBackgrounds
}

func assembleBackgroundMap(backgrounds []*entity.ChatBackground) map[string]*pb.ChatBackground {
	backgroundMap := make(map[string]*pb.ChatBackground, len(backgrounds))
	for _, background := range backgrounds {
		backgroundMap[background.ID] = assembleBackground(background)
	}
	return backgroundMap
}

func assembleRelationship(relationship *entity.Relationship) *pb.Relationship {
	return &pb.Relationship{
		Id:                    relationship.ID,
		Name:                  relationship.Name,
		AigcParams:            relationship.AIGCParams,
		BindBackgroundId:      relationship.BindBackgroundID,
		UnlockCondition:       relationship.UnlockCondition,
		Icon:                  relationship.Icon,
		IntimacyBackgroundImg: relationship.IntimacyBackgroundImg,
	}
}

func assembleRelationshipList(relationships []*entity.Relationship) []*pb.Relationship {
	pbRelationships := make([]*pb.Relationship, 0, len(relationships))
	for _, relationship := range relationships {
		pbRelationships = append(pbRelationships, assembleRelationship(relationship))
	}
	return pbRelationships
}

func assembleRelationshipMap(relationships []*entity.Relationship) map[string]*pb.Relationship {
	relationshipMap := make(map[string]*pb.Relationship, len(relationships))
	for _, relationship := range relationships {
		relationshipMap[relationship.ID] = assembleRelationship(relationship)
	}
	return relationshipMap
}

func assembleIntimacyLevelConf(levelConf *entity.IntimacyLevelConf) *pb.IntimacyLevelConf {
	return &pb.IntimacyLevelConf{
		Id:           levelConf.ID,
		Level:        levelConf.Level,
		RequireValue: levelConf.RequireValue,
		Benefits: &pb.LevelBenefits{
			ExtraSendMsgCount:   levelConf.Benefits.ExtraSendMsgCount,
			ExtraReadMindCount:  levelConf.Benefits.ExtraReadMindCount,
			UnlockRelationships: levelConf.Benefits.UnlockRelationships,
			UnlockBackgrounds:   levelConf.Benefits.UnlockBackgrounds,
		},
		BenefitDesc: levelConf.BenefitDesc,
	}
}

func assembleIntimacyLevelConfList(levelConfs []*entity.IntimacyLevelConf) []*pb.IntimacyLevelConf {
	pbLevelConfs := make([]*pb.IntimacyLevelConf, 0, len(levelConfs))
	for _, levelConf := range levelConfs {
		pbLevelConfs = append(pbLevelConfs, assembleIntimacyLevelConf(levelConf))
	}
	
	return pbLevelConfs
}

func assembleIntimacyCondition(condition *entity.IntimacyCondition) *pb.IntimacyCondition {
	return &pb.IntimacyCondition{
		Id:            condition.ID,
		ConditionType: condition.ConditionType,
		RequireCount:  condition.RequireCount,
		ValueAdd:      condition.ValueAdd,
		DailyLimit:    condition.DailyLimit,
		Icon:          condition.Icon,
		Title:         condition.Title,
		Desc:          condition.Desc,
	}
}

func assembleIntimacyConditionList(conditions []*entity.IntimacyCondition) []*pb.IntimacyCondition {
	pbConditions := make([]*pb.IntimacyCondition, 0, len(conditions))
	for _, condition := range conditions {
		pbConditions = append(pbConditions, assembleIntimacyCondition(condition))
	}
	
	return pbConditions
}

func assembleRelation(relation *entity.Relationship) *pb.Relationship {
	return &pb.Relationship{
		Id:                    relation.ID,
		Name:                  relation.Name,
		AigcParams:            relation.AIGCParams,
		BindBackgroundId:      relation.BindBackgroundID,
		UnlockCondition:       relation.UnlockCondition,
		Icon:                  relation.Icon,
		IntimacyBackgroundImg: relation.IntimacyBackgroundImg,
	}
}

func assembleLevelBenefits(benefit *entity.LevelBenefits) *pb.LevelBenefits {
	return &pb.LevelBenefits{
		ExtraSendMsgCount:   benefit.ExtraSendMsgCount,
		ExtraReadMindCount:  benefit.ExtraReadMindCount,
		UnlockRelationships: benefit.UnlockRelationships,
		UnlockBackgrounds:   benefit.UnlockBackgrounds,
	}
}
