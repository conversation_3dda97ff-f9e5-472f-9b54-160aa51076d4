package cache

import (
	"context"

	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
)

// ChatCache 聊天缓存
type ChatCache interface {
	IncrTodayRounds(ctx context.Context, id string) (uint32, error)

	GetTodayRounds(ctx context.Context, id string) (uint32, error)
}

// GrowthCache 亲密度增长缓存
type GrowthCache interface {
	IncrTodayIntimacy(ctx context.Context, id string, incr uint32) error

	GetTodayIntimacy(ctx context.Context, id string) (uint32, error)
}

// LevelUpCondProgressCache 升级条件进度缓存
type LevelUpCondProgressCache interface {
	Save(ctx context.Context, progress *entity.LevelUpCondProgress) error
	IncrCompletedNum(ctx context.Context, id string, completedNum uint32) (*entity.LevelUpCondProgress, error)
	IncrCompletedRound(ctx context.Context, id string, round, value uint32) (*entity.LevelUpCondProgress, error)

	Get(ctx context.Context, id string) (*entity.LevelUpCondProgress, error)
	FindByID(ctx context.Context, idList []string) (entity.LevelUpCondProgressList, error)
}
