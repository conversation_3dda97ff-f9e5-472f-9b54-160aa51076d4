package redis

import (
	"context"

	connect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"

	"golang.52tt.com/services/aigc/aigc-intimacy/internal/infra/db"
)

var utDatabase *db.RedisDB

func init() {
	var err error
	utDatabase, err = db.NewRedisDB(context.Background(), &connect.RedisConfig{
		Host:     "************",
		Port:     6379,
		DB:       15,
		PoolSize: 1,
	})
	if err != nil {
		panic(err)
	}
}
