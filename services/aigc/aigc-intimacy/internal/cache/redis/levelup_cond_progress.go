package redis

import (
	"context"
	"fmt"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"gitlab.ttyuyin.com/tyr/x/log"

	"golang.52tt.com/services/aigc/aigc-intimacy/internal/cache"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/infra/db"
)

const (
	progressDelayTTL = time.Hour
)

type levelUpCondProgressCache struct {
	cmder *db.RedisDB
}

func NewLevelUpCondProgressRedisCache(_ context.Context, database *db.RedisDB) cache.LevelUpCondProgressCache {
	c := &levelUpCondProgressCache{
		cmder: database,
	}

	return c
}

func (c *levelUpCondProgressCache) Save(ctx context.Context, progress *entity.LevelUpCondProgress) error {
	_, err := c.cmder.Pipelined(ctx, func(pl redis.Pipeliner) error {
		key := c.keyOfProgress(progress.ID)
		_ = pl.HSet(ctx, key, progress.Encode())
		_ = pl.ExpireAt(ctx, key, progress.EndAt.Add(progressDelayTTL))
		return nil
	})

	return err
}

func (c *levelUpCondProgressCache) IncrCompletedNum(ctx context.Context, id string, completedNum uint32) (*entity.LevelUpCondProgress, error) {
	var cmd *redis.StringStringMapCmd
	if _, err := c.cmder.Pipelined(ctx, func(pl redis.Pipeliner) error {
		key := c.keyOfProgress(id)
		_ = pl.HIncrBy(ctx, key, "num", int64(completedNum))
		cmd = pl.HGetAll(ctx, key)
		return nil
	}); err != nil {
		return nil, err
	}

	result, err := cmd.Result()
	if err != nil {
		return nil, err
	}
	if len(result) == 0 {
		return nil, nil
	}

	var p entity.LevelUpCondProgress
	return &p, p.Decode(result)
}

func (c *levelUpCondProgressCache) IncrCompletedRound(ctx context.Context, id string, round, value uint32) (*entity.LevelUpCondProgress, error) {
	var cmd *redis.StringStringMapCmd
	c.cmder.Pipelined(ctx, func(pl redis.Pipeliner) error {
		key := c.keyOfProgress(id)
		_ = pl.HIncrBy(ctx, key, "val", int64(value))
		_ = pl.HSet(ctx, key, "num", 0)
		_ = pl.HIncrBy(ctx, key, "round", int64(round))
		cmd = pl.HGetAll(ctx, key)
		return nil
	})

	result, err := cmd.Result()
	if err != nil {
		return nil, err
	}
	if len(result) == 0 {
		return nil, nil
	}

	var p entity.LevelUpCondProgress
	return &p, p.Decode(result)
}

func (c *levelUpCondProgressCache) Get(ctx context.Context, id string) (*entity.LevelUpCondProgress, error) {
	result, err := c.cmder.HGetAll(ctx, c.keyOfProgress(id)).Result()
	if err != nil {
		return nil, err
	}
	if len(result) == 0 {
		return nil, nil
	}

	var p entity.LevelUpCondProgress
	return &p, p.Decode(result)
}

func (c *levelUpCondProgressCache) FindByID(ctx context.Context, idList []string) (entity.LevelUpCondProgressList, error) {
	cmdList := make([]*redis.StringStringMapCmd, 0, len(idList))
	if _, err := c.cmder.Pipelined(ctx, func(pl redis.Pipeliner) error {
		for _, id := range idList {
			cmdList = append(cmdList, pl.HGetAll(ctx, c.keyOfProgress(id)))
		}

		return nil
	}); err != nil {
		return nil, err
	}

	list := make(entity.LevelUpCondProgressList, 0, len(cmdList))
	for _, cmd := range cmdList {
		result, err := cmd.Result()
		if err != nil {
			log.ErrorWithCtx(ctx, "FindByID(%s) err: %v", cmd.Val(), err)
			return nil, err
		}
		if len(result) == 0 {
			continue
		}

		var p entity.LevelUpCondProgress
		if err := p.Decode(result); err != nil {
			log.ErrorWithCtx(ctx, "FindByID Decode(%+v) err: %v", cmd.Val(), err)
			continue
		}

		list = append(list, &p)
	}

	return list, nil
}

func (c *levelUpCondProgressCache) keyOfProgress(id string) string {
	return fmt.Sprintf("lcp:%s", id)
}
