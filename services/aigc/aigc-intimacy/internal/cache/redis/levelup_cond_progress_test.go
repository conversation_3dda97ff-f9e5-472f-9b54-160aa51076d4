package redis

import (
	"context"
	"testing"
	"time"

	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/infra/db"
)

func Test_levelUpCondProgressCache_Save(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx      context.Context
		progress *entity.LevelUpCondProgress
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx: ctx,
				progress: &entity.LevelUpCondProgress{
					ID:      "1:1:1:67da20aa24f7cb25a0439f41",
					BeginAt: time.Now(),
					EndAt:   time.Now().Add(time.Minute),
					Uid:     1,
					CondID:  "67da20aa24f7cb25a0439f41",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &levelUpCondProgressCache{
				cmder: tt.fields.cmder,
			}
			if err := c.Save(tt.args.ctx, tt.args.progress); (err != nil) != tt.wantErr {
				t.Errorf("levelUpCondProgressCache.Save() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_levelUpCondProgressCache_IncrCompletedNum(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx          context.Context
		id           string
		completedNum uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx:          ctx,
				id:           "1:1:1:67da20aa24f7cb25a0439f41",
				completedNum: 1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &levelUpCondProgressCache{
				cmder: tt.fields.cmder,
			}
			got, err := c.IncrCompletedNum(tt.args.ctx, tt.args.id, tt.args.completedNum)
			if (err != nil) != tt.wantErr {
				t.Errorf("levelUpCondProgressCache.IncrCompletedNum() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("levelUpCondProgressCache.IncrCompletedNum() = %v", got)
		})
	}
}

func Test_levelUpCondProgressCache_IncrCompletedRound(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx   context.Context
		id    string
		round uint32
		value uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx:   ctx,
				id:    "1:1:1:67da20aa24f7cb25a0439f41",
				round: 1,
				value: 10,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &levelUpCondProgressCache{
				cmder: tt.fields.cmder,
			}
			got, err := c.IncrCompletedRound(tt.args.ctx, tt.args.id, tt.args.round, tt.args.value)
			if (err != nil) != tt.wantErr {
				t.Errorf("levelUpCondProgressCache.IncrAddedValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("levelUpCondProgressCache.IncrAddedValue() = %v", got)
		})
	}
}

func Test_levelUpCondProgressCache_FindByID(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx    context.Context
		idList []string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    entity.LevelUpCondProgressList
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx:    ctx,
				idList: []string{"1", "1:1:1:67da20aa24f7cb25a0439f41"},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &levelUpCondProgressCache{
				cmder: tt.fields.cmder,
			}
			got, err := c.FindByID(tt.args.ctx, tt.args.idList)
			if (err != nil) != tt.wantErr {
				t.Errorf("levelUpCondProgressCache.FindByID() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("levelUpCondProgressCache.FindByID() = %v", got)
		})
	}
}

func Test_levelUpCondProgressCache_Get(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	type fields struct {
		cmder *db.RedisDB
	}
	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				cmder: utDatabase,
			},
			args: args{
				ctx: ctx,
				id:  "1:1:1:67da20aa24f7cb25a0439f41",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &levelUpCondProgressCache{
				cmder: tt.fields.cmder,
			}
			got, err := c.Get(tt.args.ctx, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("levelUpCondProgressCache.Get() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("levelUpCondProgressCache.Get() = %v", got)
		})
	}
}
