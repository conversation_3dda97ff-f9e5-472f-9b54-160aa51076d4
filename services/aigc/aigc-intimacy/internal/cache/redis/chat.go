package redis

import (
	"context"
	"fmt"
	"time"

	"github.com/gookit/goutil/timex"

	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"

	"golang.52tt.com/services/aigc/aigc-intimacy/internal/cache"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/infra/db"
)

const (
	chatRoundTTL = timex.Day
)

type chatRedisCache struct {
	cmder *db.RedisDB
}

func NewChatRedisCache(_ context.Context, database *db.RedisDB) cache.ChatCache {
	c := &chatRedisCache{
		cmder: database,
	}

	return c
}

func (c *chatRedisCache) IncrTodayRounds(ctx context.Context, id string) (uint32, error) {
	var incrCmd *redis.IntCmd
	if _, err := c.cmder.Pipelined(ctx, func(pl redis.Pipeliner) error {
		key := c.keyOfTodayRound()
		incrCmd = pl.HIncrBy(ctx, key, id, 1)
		_ = pl.Expire(ctx, key, chatRoundTTL)
		return nil
	}); err != nil {
		return 0, err
	}

	return uint32(incrCmd.Val()), nil
}

func (c *chatRedisCache) GetTodayRounds(ctx context.Context, id string) (uint32, error) {
	val, err := c.cmder.HGet(ctx, c.keyOfTodayRound(), id).Uint64()
	if err != nil {
		if redis.IsNil(err) {
			return 0, nil
		}
	}

	return uint32(val), err
}

func (c *chatRedisCache) keyOfTodayRound() string {
	return fmt.Sprintf("chat:round:%s", timex.DateFormat(time.Now(), timex.DateOnlyLayout))
}
