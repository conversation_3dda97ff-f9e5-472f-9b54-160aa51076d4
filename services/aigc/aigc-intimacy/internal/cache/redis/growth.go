package redis

import (
	"context"
	"fmt"
	"time"

	"github.com/gookit/goutil/strutil"
	"github.com/gookit/goutil/timex"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"

	"golang.52tt.com/services/aigc/aigc-intimacy/internal/cache"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/infra/db"
)

const (
	growthIntimacyTTL = 24 * time.Hour
)

type growthRedisCache struct {
	cmder *db.RedisDB
}

func NewGrowthRedisCache(_ context.Context, database *db.RedisDB) cache.GrowthCache {
	c := &growthRedisCache{
		cmder: database,
	}

	return c
}

func (c *growthRedisCache) IncrTodayIntimacy(ctx context.Context, id string, incr uint32) error {
	if _, err := c.cmder.Pipelined(ctx, func(pl redis.Pipeliner) error {
		key := c.keyOfTodayIntimacy()
		_ = pl.HIncrBy(ctx, key, id, int64(incr))
		_ = pl.Expire(ctx, key, growthIntimacyTTL)
		return nil
	}); err != nil {
		return err
	}

	return nil
}

func (c *growthRedisCache) GetTodayIntimacy(ctx context.Context, id string) (uint32, error) {
	val, err := c.cmder.HGet(ctx, c.keyOfTodayIntimacy(), id).Result()
	if err != nil {
		if redis.IsNil(err) {
			return 0, nil
		}

		return 0, err
	}

	return uint32(strutil.Uint(val)), nil
}

func (c *growthRedisCache) keyOfTodayIntimacy() string {
	return fmt.Sprintf("growth:intimacy:%s", timex.DateFormat(time.Now(), timex.DateOnlyLayout))
}
