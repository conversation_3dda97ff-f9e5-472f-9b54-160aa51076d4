package report

import (
	"context"

	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
)

//go:generate mockgen -destination=mocks/reporter.go -package=mocks golang.52tt.com/services/aigc/aigc-intimacy/internal/report Reporter
type Reporter interface {
	Close()

	ReportLevelUp(ctx context.Context, uid uint32, en *pb.Entity, lv uint32)
	ReportSwitchRelation(ctx context.Context, uid uint32, en *pb.Entity, op pb.SwitchRelationRequest_Op)
}
