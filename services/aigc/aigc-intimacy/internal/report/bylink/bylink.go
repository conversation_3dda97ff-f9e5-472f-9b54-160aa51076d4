package bylink

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"
	ga "golang.52tt.com/protocol/app"
	
	"golang.52tt.com/pkg/bylink"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/report"
)

type bylinkReporter struct {
	collector bylink.Bylink
}

func NewBylinkReporter() (report.Reporter, error) {
	collector, err := bylink.NewKfkCollector()
	if err != nil {
		return nil, err
	}
	
	bylink.InitGlobalCollector(collector)
	
	return &bylinkReporter{
		collector: collector,
	}, nil
}

func (r *bylinkReporter) Close() {
	r.collector.Flush()
	r.collector.Close()
}

func (r *bylinkReporter) ReportLevelUp(ctx context.Context, uid uint32, en *pb.Entity, lv uint32) {
	data := map[string]interface{}{
		"uid":        uid,
		"partner_id": en.GetId(),
		
		"level": lv,
	}
	
	if err := bylink.Track(ctx, uint64(uid), "virtual_company_intimacy_upgrade_log", data, false); err != nil {
		log.ErrorWithCtx(ctx, "ReportLevelUp uid(%d) en(%+v) lv(%d) err: %v", uid, en, lv, err)
		return
	}
	
	bylink.Flush()
	log.InfoWithCtx(ctx, "ReportLevelUp data: %+v", data)
}

func toAppType(marketId uint32) string {
	switch ga.BaseReq_MarketId(marketId) {
	case ga.BaseReq_MARKET_HUANYOU:
		return "huanyou"
	case ga.BaseReq_MARKET_MAIKE:
		return "maike"
	case ga.BaseReq_MARKET_MIJING:
		return "mijing"
	case ga.BaseReq_MARKET_NONE:
		return "ttvoice"
	case ga.BaseReq_MARKET_ZAIYA:
		return "zaiya"
	default:
		return ""
	}
}

func (r *bylinkReporter) ReportSwitchRelation(ctx context.Context, uid uint32, en *pb.Entity, op pb.SwitchRelationRequest_Op) {
	data := map[string]interface{}{
		"uid":        uid,
		"partner_id": en.GetId(),
		"app_type":   toAppType(metainfo.GetServiceInfo(ctx).MarketID()),
	}
	switch op {
	case pb.SwitchRelationRequest_OP_SWITCH:
		data["op"] = "bind"
	case pb.SwitchRelationRequest_OP_DEL:
		data["op"] = "unbind"
	default:
		log.WarnWithCtx(ctx, "ReportSwitchRelation Unknown Op %d", op)
		return
	}
	
	if err := bylink.Track(ctx, uint64(uid), "virtual_company_bind_log", data, false); err != nil {
		log.ErrorWithCtx(ctx, "ReportSwitchRelation uid(%d) en(%+v) op(%d) err: %v", uid, en, op, err)
		return
	}
	
	bylink.Flush()
	log.InfoWithCtx(ctx, "ReportSwitchRelation data: %+v", data)
}
