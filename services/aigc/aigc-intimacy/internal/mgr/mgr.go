package mgr

import (
	"context"

	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
)

//go:generate mockgen -destination=mocks/background.go -package=mocks golang.52tt.com/services/aigc/aigc-intimacy/internal/mgr BackgroundManager
type BackgroundManager interface {
	UpsertChatBackground(ctx context.Context, background *entity.ChatBackground) error
	DeleteChatBackground(ctx context.Context, id string) error
	GetChatBackground(ctx context.Context, id string) (*entity.ChatBackground, error)
	GetChatBackgroundByPage(ctx context.Context, page, pageSize int64, needCount bool, name string) ([]*entity.ChatBackground, int64, error)
	BatchGetChatBackground(ctx context.Context, ids []string) ([]*entity.ChatBackground, error)
	GetChatBackgroundMap(ctx context.Context, ids []string) (map[string]*entity.ChatBackground, error)
	GetChatBackgroundByBindEntity(ctx context.Context, bindEntity *pb.BindEntity) ([]*entity.ChatBackground, error)

	SwitchBackground(ctx context.Context, background *entity.ObjectBackgroundBinding) error
	GetBackgroundBinding(ctx context.Context, entityInst *pb.Entity) (*entity.ObjectBackgroundBinding, error)
	DeleteBackgroundBinding(ctx context.Context, entityInst *pb.Entity) error
}

//go:generate mockgen -destination=mocks/relation.go -package=mocks golang.52tt.com/services/aigc/aigc-intimacy/internal/mgr RelationManager
type RelationManager interface {
	UpsertRelationship(ctx context.Context, relationship *entity.Relationship) error
	DeleteRelationship(ctx context.Context, id string) error
	GetRelationship(ctx context.Context, id string) (*entity.Relationship, error)
	BatchGetRelationship(ctx context.Context, ids []string) ([]*entity.Relationship, error)
	GetRelationshipMap(ctx context.Context, ids []string) (map[string]*entity.Relationship, error)
	GetAllRelationship(ctx context.Context) ([]*entity.Relationship, error)

	SwitchRelation(ctx context.Context, entityInst *pb.Entity, relationId string) error
	GetBindRelation(ctx context.Context, entityInst *pb.Entity) (*entity.ObjectRelationBinding, error)
	DeleteRelationBinding(ctx context.Context, entityInst *pb.Entity) error
	BatchGetRelationBinding(ctx context.Context, entityInsts []*pb.Entity) (map[string]*entity.ObjectRelationBinding, error)
}

//go:generate mockgen -destination=mocks/level.go -package=mocks golang.52tt.com/services/aigc/aigc-intimacy/internal/mgr LevelManager
type LevelManager interface {
	UpsertIntimacyLevelConf(ctx context.Context, intimacyLevelConf *entity.IntimacyLevelConf) error
	DeleteIntimacyLevelConf(ctx context.Context, id uint32) error
	GetIntimacyLevelConf(ctx context.Context, id uint32) (*entity.IntimacyLevelConf, error)
	BatchGetIntimacyLevelConf(ctx context.Context, ids []uint32) ([]*entity.IntimacyLevelConf, error)
	GetAllIntimacyLevelConf(ctx context.Context) ([]*entity.IntimacyLevelConf, error)
	GetIntimacyLevelConfLessThan(ctx context.Context, level uint32) ([]*entity.IntimacyLevelConf, error)

	AddIntimacyValue(ctx context.Context, uid uint32, en *pb.Entity, value uint32, serialNo, reason string) (added bool, err error)
	GetIntimacyLevel(ctx context.Context, uid uint32, en *pb.Entity) (*entity.Level, error)
	GetTodayGrowIntimacy(ctx context.Context, uid uint32, en *pb.Entity) (uint32, error)
}

//go:generate mockgen -destination=mocks/levelup.go -package=mocks golang.52tt.com/services/aigc/aigc-intimacy/internal/mgr LevelUpManager
type LevelUpManager interface {
	UpsertIntimacyCondition(ctx context.Context, intimacyCondition *entity.IntimacyCondition) error
	DeleteIntimacyCondition(ctx context.Context, id string) error
	GetIntimacyCondition(ctx context.Context, id string) (*entity.IntimacyCondition, error)
	BatchGetIntimacyCondition(ctx context.Context, ids []string) ([]*entity.IntimacyCondition, error)
	GetAllIntimacyCondition(ctx context.Context) ([]*entity.IntimacyCondition, error)

	HandleCondtion(ctx context.Context, uid uint32, en *pb.Entity, cond *entity.IntimacyCondition) (progress *entity.LevelUpCondProgress, completed bool, err error)
	CompleteCondition(ctx context.Context, uid uint32, en *pb.Entity, condID string, value uint32) (*entity.LevelUpCondProgress, error)
	BatchGetConditionProgress(ctx context.Context, uid uint32, en *pb.Entity, condIDList []string) (map[string]*entity.LevelUpCondProgress, error)

	RecordChatMsg(ctx context.Context, uid uint32, en *pb.Entity, msgId string) error
	GetTodayChatRounds(ctx context.Context, uid uint32, en *pb.Entity) (uint32, error)
}
