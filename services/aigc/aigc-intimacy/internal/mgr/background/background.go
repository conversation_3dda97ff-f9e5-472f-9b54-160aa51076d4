package background

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/store"
)

type manager struct {
	backgroundStore store.ChatBackgroundStore
}

func NewManager(backgroundStore store.ChatBackgroundStore) mgr.BackgroundManager {

	return &manager{
		backgroundStore: backgroundStore,
	}
}

func (m *manager) UpsertChatBackground(ctx context.Context, background *entity.ChatBackground) error {
	return m.backgroundStore.Save(ctx, background)
}

func (m *manager) DeleteChatBackground(ctx context.Context, id string) error {
	return m.backgroundStore.Delete(ctx, id)
}

func (m *manager) GetChatBackground(ctx context.Context, id string) (*entity.ChatBackground, error) {
	return m.backgroundStore.Get(ctx, id)
}

func (m *manager) GetChatBackgroundByPage(ctx context.Context, page, pageSize int64, needCount bool, name string) ([]*entity.ChatBackground, int64, error) {
	if page == 0 {
		page = 1
	}
	if pageSize == 0 || pageSize > 100 {
		pageSize = 100
	}
	return m.backgroundStore.GetByPage(ctx, page, pageSize, needCount, name)
}

func (m *manager) BatchGetChatBackground(ctx context.Context, ids []string) ([]*entity.ChatBackground, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	return m.backgroundStore.BatchGet(ctx, ids)
}

func (m *manager) GetChatBackgroundMap(ctx context.Context, ids []string) (map[string]*entity.ChatBackground, error) {
	backgrounds, err := m.BatchGetChatBackground(ctx, ids)
	if err != nil {
		return nil, err
	}
	backgroundMap := make(map[string]*entity.ChatBackground, len(backgrounds))
	for _, background := range backgrounds {
		backgroundMap[background.ID] = background
	}

	return backgroundMap, nil
}

func (m *manager) GetChatBackgroundByBindEntity(ctx context.Context, bindEntity *pb.BindEntity) ([]*entity.ChatBackground, error) {
	var backgrounds []*entity.ChatBackground
	var err error

	switch bindEntity.GetType() {
	case pb.BindEntity_ENTITY_TYPE_ROLE:
		backgrounds, err = m.backgroundStore.GetByBindRoleId(ctx, bindEntity.GetId())
	default:
		log.WarnWithCtx(ctx, "GetChatBackgroundByBindEntity invalid bind entity type:%v", bindEntity.GetType())
	}
	return backgrounds, err
}
