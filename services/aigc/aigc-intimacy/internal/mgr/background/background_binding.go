package background

import (
	"context"
	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
)

func (m *manager) SwitchBackground(ctx context.Context, background *entity.ObjectBackgroundBinding) error {
	return m.backgroundStore.UpsertBackgroundBinding(ctx, background)
}

func (m *manager) GetBackgroundBinding(ctx context.Context, entityInst *pb.Entity) (*entity.ObjectBackgroundBinding, error) {
	return m.backgroundStore.GetBackgroundBinding(ctx, entityInst)
}

func (m *manager) DeleteBackgroundBinding(ctx context.Context, entityInst *pb.Entity) error {
	return m.backgroundStore.DeleteBackgroundBinding(ctx, entityInst)
}