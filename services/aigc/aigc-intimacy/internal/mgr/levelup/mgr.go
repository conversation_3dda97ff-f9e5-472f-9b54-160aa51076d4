package levelup

import (
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/cache"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/store"
)

type manager struct {
	conditionStore store.IntimacyConditionStore

	chatCache     cache.ChatCache
	progressCache cache.LevelUpCondProgressCache
}

func NewManager(
	conditionStore store.IntimacyConditionStore,

	chatCache cache.ChatCache,
	progressCache cache.LevelUpCondProgressCache,
) mgr.LevelUpManager {
	return &manager{
		conditionStore: conditionStore,

		chatCache:     chatCache,
		progressCache: progressCache,
	}
}
