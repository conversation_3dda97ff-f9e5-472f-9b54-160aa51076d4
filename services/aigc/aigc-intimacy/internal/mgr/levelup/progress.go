package levelup

import (
	"context"

	"gitlab.ttyuyin.com/tyr/x/log"
	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
)

// HandleCondition 处理亲密度升级条件
func (m *manager) HandleCondtion(ctx context.Context, uid uint32, en *pb.Entity, cond *entity.IntimacyCondition) (progress *entity.LevelUpCondProgress, completed bool, err error) {
	p, err := m.progressCache.Get(ctx, entity.EncodeLevelUpCondProgressID(uid, en, cond.ID))
	if err != nil {
		return
	}

	np := p.Clone()
	if np == nil || np.IsExpired() {
		// 不存在进度或者进度已过期
		if np, err = entity.NewLevelUpCondProgress(uid, en, cond); err != nil {
			return
		}
	}
	if endAt := np.BeginAt.Add(cond.Duration()); endAt != np.EndAt {
		// 条件配置变更过
		np.EndAt = endAt
	}
	if np.Compare(p) {
		// 对比之前有变化
		if err = m.progressCache.Save(ctx, np); err != nil {
			return
		}

		log.InfoWithCtx(ctx, "HandleCondition Save progress(%+v)", np)
	}

	if np.AddedValue >= cond.DailyLimit {
		// 达到亲密度上限
		log.WarnWithCtx(ctx, "HandleCondition progress(%s) value(%d) >= limit(%d)", np.ID, np.AddedValue, cond.DailyLimit)
		return
	}

	// 增加完成次数
	if np, err = m.progressCache.IncrCompletedNum(ctx, np.ID, 1); err != nil {
		return
	}

	return np, np.CompletedNum >= cond.RequireCount, nil
}

// CompleteCondition 完成亲密度升级条件
func (m *manager) CompleteCondition(ctx context.Context, uid uint32, en *pb.Entity, condID string, value uint32) (*entity.LevelUpCondProgress, error) {
	return m.progressCache.IncrCompletedRound(ctx, entity.EncodeLevelUpCondProgressID(uid, en, condID), 1, value)
}

// GetConditionProgress 获取亲密度升级条件进度
func (m *manager) BatchGetConditionProgress(ctx context.Context, uid uint32, en *pb.Entity, condIDList []string) (map[string]*entity.LevelUpCondProgress, error) {
	idList := make([]string, 0, len(condIDList))
	for _, id := range condIDList {
		idList = append(idList, entity.EncodeLevelUpCondProgressID(uid, en, id))
	}

	list, err := m.progressCache.FindByID(ctx, idList)
	if err != nil {
		return nil, err
	}

	pm := make(map[string]*entity.LevelUpCondProgress)
	for _, p := range list {
		// 过滤掉已过期的进度
		if !p.IsExpired() {
			pm[p.CondID] = p
		}
	}

	return pm, nil
}
