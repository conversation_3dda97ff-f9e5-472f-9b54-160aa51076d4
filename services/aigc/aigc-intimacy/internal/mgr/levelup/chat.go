package levelup

import (
	"context"

	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
)

func (m *manager) RecordChatMsg(ctx context.Context, uid uint32, en *pb.Entity, msgId string) error {
	_, err := m.chatCache.IncrTodayRounds(ctx, entity.EncodeID(uid, en))
	return err
}

func (m *manager) GetTodayChatRounds(ctx context.Context, uid uint32, en *pb.Entity) (uint32, error) {
	return m.chatCache.GetTodayRounds(ctx, entity.EncodeID(uid, en))
}
