package levelup

import (
	"context"

	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
)

func (m *manager) UpsertIntimacyCondition(ctx context.Context, intimacyCondition *entity.IntimacyCondition) error {
	return m.conditionStore.Save(ctx, intimacyCondition)
}

func (m *manager) DeleteIntimacyCondition(ctx context.Context, id string) error {
	return m.conditionStore.Delete(ctx, id)
}

func (m *manager) GetIntimacyCondition(ctx context.Context, id string) (*entity.IntimacyCondition, error) {
	return m.conditionStore.Get(ctx, id)
}

func (m *manager) BatchGetIntimacyCondition(ctx context.Context, ids []string) ([]*entity.IntimacyCondition, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	return m.conditionStore.BatchGet(ctx, ids)
}

func (m *manager) GetAllIntimacyCondition(ctx context.Context) ([]*entity.IntimacyCondition, error) {
	return m.conditionStore.BatchGet(ctx, nil)
}
