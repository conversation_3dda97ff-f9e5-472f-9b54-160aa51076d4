package level

import (
	push "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/cache"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/event"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/report"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/store"
)

type manager struct {
	levelStore     store.LevelStore
	levelConfStore store.IntimacyLevelConfStore

	growthCache cache.GrowthCache

	eventBus event.EventBus

	pusher   push.IClient
	reporter report.Reporter
}

func NewManager(
	levelStore store.LevelStore,
	levelConfStore store.IntimacyLevelConfStore,

	growthCache cache.GrowthCache,

	eventBus event.EventBus,

	pusher push.IClient,
	repoter report.Reporter,
) mgr.LevelManager {
	return &manager{
		levelStore:     levelStore,
		levelConfStore: levelConfStore,

		growthCache: growthCache,

		eventBus: eventBus,

		pusher:   pusher,
		reporter: repoter,
	}
}
