package level

import (
	"context"

	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
)

func (m *manager) UpsertIntimacyLevelConf(ctx context.Context, intimacyLevelConf *entity.IntimacyLevelConf) error {
	return m.levelConfStore.Save(ctx, intimacyLevelConf)
}

func (m *manager) DeleteIntimacyLevelConf(ctx context.Context, id uint32) error {
	return m.levelConfStore.Delete(ctx, id)
}

func (m *manager) GetIntimacyLevelConf(ctx context.Context, id uint32) (*entity.IntimacyLevelConf, error) {
	return m.levelConfStore.Get(ctx, id)
}

func (m *manager) BatchGetIntimacyLevelConf(ctx context.Context, ids []uint32) ([]*entity.IntimacyLevelConf, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	return m.levelConfStore.BatchGet(ctx, ids)
}

func (m *manager) GetAllIntimacyLevelConf(ctx context.Context) ([]*entity.IntimacyLevelConf, error) {
	return m.levelConfStore.BatchGet(ctx, nil)
}

func (m *manager) GetIntimacyLevelConfLessThan(ctx context.Context, level uint32) ([]*entity.IntimacyLevelConf, error) {
	return m.levelConfStore.GetLessThan(ctx, level)
}
