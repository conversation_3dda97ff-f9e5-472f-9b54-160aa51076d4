package level

import (
	"context"
	"encoding/json"
	"sort"
	"time"

	bizLabel "gitlab.ttyuyin.com/bizFund/bizFund/pkg/push-notification"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	"gitlab.ttyuyin.com/tyr/x/log"

	PushNotification "golang.52tt.com/clients/push-notification/v2"
	gapush "golang.52tt.com/protocol/app/push"
	web_im_logic "golang.52tt.com/protocol/app/web-im-logic"
	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	pushPb "golang.52tt.com/protocol/services/push-notification/v2"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
)

// AddIntimacyValue 增加亲密度值
func (m *manager) AddIntimacyValue(ctx context.Context, uid uint32, en *pb.Entity, value uint32, serialNo, reason string) (bool, error) {
	lv, err := m.levelStore.Get(ctx, entity.EncodeID(uid, en))
	if err != nil {
		return false, err
	}
	if lv == nil {
		if lv, err = entity.NewLevel(uid, en); err != nil {
			return false, err
		}
	}

	cfgList, err := m.GetAllIntimacyLevelConf(ctx)
	if err != nil {
		return false, err
	}
	if len(cfgList) == 0 {
		log.WarnWithCtx(ctx, "AddIntimacyValue GetAllIntimacyLevelConf empty")
		return false, nil
	}

	// 检查满级
	if i := sort.Search(len(cfgList), func(i int) bool {
		return cfgList[i].Level > lv.Level
	}); i < 0 || i >= len(cfgList) {
		return false, nil
	}

	// 增加亲密值
	var ok bool
	if lv, ok, err = m.addLevelValue(ctx, lv, value, serialNo, reason); err != nil {
		return false, err
	}
	if !ok {
		log.WarnWithCtx(ctx, "AddIntimacyValue CreateOrIncrValue serialNo(%s) conflict", serialNo)
		return false, nil
	}

	// 检查升级
	nLv := calcNextLevel(lv, cfgList)
	if nLv.Level > lv.Level {
		if nLv, err = m.upgradeLevel(ctx, en, lv, nLv); err != nil {
			return false, err
		}
	}

	_ = m.sendLevelUpNotify(ctx, en, lv, nLv, cfgList)
	return true, nil
}

func (m *manager) GetIntimacyLevel(ctx context.Context, uid uint32, en *pb.Entity) (*entity.Level, error) {
	lv, err := m.levelStore.Get(ctx, entity.EncodeID(uid, en))
	if err != nil {
		return nil, err
	}
	if lv == nil {
		return entity.NewLevel(uid, en)
	}
	return lv, nil
}

func (m *manager) GetTodayGrowIntimacy(ctx context.Context, uid uint32, en *pb.Entity) (uint32, error) {
	return m.growthCache.GetTodayIntimacy(ctx, entity.EncodeID(uid, en))
}

func (m *manager) sendLevelUpNotify(ctx context.Context, en *pb.Entity, oLv, nLv *entity.Level, cfgList []*entity.IntimacyLevelConf) error {
	notify := &pb.LevelUpNotify{
		Uid:    nLv.Uid,
		Entity: en,

		Level: nLv.Level,
		Value: nLv.Value,

		LevelUpgraded: nLv.Level > oLv.Level,

		TriggeredAt: time.Now().Unix(),
	}
	if j := sort.Search(len(cfgList), func(i int) bool {
		return cfgList[i].Level > nLv.Level
	}); j >= 0 && j < len(cfgList) {
		notify.MaxValue = cfgList[j].RequireValue
	}

	data, err := json.Marshal(notify)
	if err != nil {
		return err
	}

	content, err := proto.Marshal(&web_im_logic.CommonH5PushMsg{
		PushType:    uint32(web_im_logic.H5PushType_H5_PUSH_TYPE_INTIMACY_LEVEL_UP),
		Data:        data,
		MarshalType: uint32(web_im_logic.CommonH5PushMsg_MARSHAL_TYPE_JSON),
	})
	if err != nil {
		return err
	}

	payload, err := proto.Marshal(&gapush.PushMessage{
		Cmd:     uint32(gapush.PushMessage_GAME_COMMON_H5_PUSH),
		Content: content,
	})
	if err != nil {
		return err
	}

	_, err = m.pusher.PushToUsers2(ctx, []uint32{nLv.Uid}, &pushPb.CompositiveNotification{
		Sequence:           uint32(time.Now().Unix()),
		TerminalTypePolicy: PushNotification.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &pushPb.ProxyNotification{
			Type:      uint32(pushPb.ProxyNotification_PUSH),
			Payload:   payload,
			PushLabel: bizLabel.LabelAigcIntimacyLevelUp,
		},
	})

	log.InfoWithCtx(ctx, "sendLevelUpNotify PushToUsers2 notify(%+v) finished", notify)
	return err
}

func (m *manager) addLevelValue(ctx context.Context, lv *entity.Level, value uint32, serialNo, reason string) (*entity.Level, bool, error) {
	nLv, ok, err := m.levelStore.CreateOrIncrValue(ctx, lv, value, serialNo, reason)
	if err != nil {
		return nil, false, err
	}
	if !ok {
		log.WarnWithCtx(ctx, "addLevelValue CreateOrIncrValue serialNo(%s) conflict", serialNo)
		return lv, false, nil
	}

	_ = m.growthCache.IncrTodayIntimacy(ctx, lv.ID, value)
	return nLv, true, nil
}

func (m *manager) upgradeLevel(ctx context.Context, en *pb.Entity, lv, nLv *entity.Level) (*entity.Level, error) {
	uLv, err := m.levelStore.UpdateLevel(ctx, lv.ID, lv.Level, lv.Value, nLv.Level, nLv.Value)
	if err != nil {
		return nil, err
	}
	if uLv == nil {
		log.WarnWithCtx(ctx, "upgradeLevel from lv(%+v) to nLv(%+v) failed", en, lv, nLv)
		return lv, nil
	}

	m.reporter.ReportLevelUp(ctx, lv.Uid, en, nLv.Level)
	return nLv, nil
}

func calcNextLevel(lv *entity.Level, cfgList []*entity.IntimacyLevelConf) *entity.Level {
	j := sort.Search(len(cfgList), func(i int) bool {
		return cfgList[i].Level > lv.Level
	})
	if j < 0 || j >= len(cfgList) {
		return lv
	}

	nLv := *lv
	for i := j; i < len(cfgList); i++ {
		if nLv.Value < cfgList[i].RequireValue {
			break
		}

		nLv.Level = cfgList[i].Level
		nLv.Value -= cfgList[i].RequireValue
	}

	return &nLv
}
