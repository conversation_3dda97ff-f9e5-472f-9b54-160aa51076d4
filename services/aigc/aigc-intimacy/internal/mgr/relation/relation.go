package relation

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"
	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/event"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/store"
)

type manager struct {
	relationStore store.RelationshipStore
	
	eventBus event.EventBus
}

func NewManager(relationStore store.RelationshipStore, eventBus event.EventBus) mgr.RelationManager {
	
	return &manager{
		relationStore: relationStore,
		eventBus:      eventBus,
	}
}

func (m *manager) SwitchRelation(ctx context.Context, entityInst *pb.Entity, relationId string) error {
	err := m.relationStore.UpsertRelationBinding(ctx, entity.NewObjectRelationBinding(entityInst, relationId))
	if err != nil {
		log.ErrorWithCtx(ctx, "SwitchRelation UpsertRelationBinding entityInst:%s err:%v", entityInst.String(), err)
		return err
	}
	
	uid := metainfo.GetServiceInfo(ctx).UserID()
	switchRelationEvent := &pb.SwitchRelationEvent{
		Entity:     entityInst,
		RelationId: relationId,
		Uid:        uid,
		Op:         pb.SwitchRelationRequest_OP_SWITCH,
	}
	err = m.eventBus.PublishSwitchRelationEvent(ctx, switchRelationEvent)
	if err != nil {
		log.ErrorWithCtx(ctx, "SwitchRelation PublishSwitchRelationEvent entityInst:%s err:%v", entityInst.String(), err)
	}
	return err
	
}

func (m *manager) UpsertRelationship(ctx context.Context, relationship *entity.Relationship) error {
	return m.relationStore.Save(ctx, relationship)
}

func (m *manager) DeleteRelationship(ctx context.Context, id string) error {
	return m.relationStore.Delete(ctx, id)
}

func (m *manager) GetRelationship(ctx context.Context, id string) (*entity.Relationship, error) {
	return m.relationStore.Get(ctx, id)
}

func (m *manager) BatchGetRelationship(ctx context.Context, ids []string) ([]*entity.Relationship, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	return m.relationStore.BatchGet(ctx, ids)
}

func (m *manager) GetRelationshipMap(ctx context.Context, ids []string) (map[string]*entity.Relationship, error) {
	relationships, err := m.BatchGetRelationship(ctx, ids)
	if err != nil {
		return nil, err
	}
	relationshipMap := make(map[string]*entity.Relationship, len(relationships))
	for _, relationship := range relationships {
		relationshipMap[relationship.ID] = relationship
	}
	
	return relationshipMap, nil
}

func (m *manager) GetAllRelationship(ctx context.Context) ([]*entity.Relationship, error) {
	return m.relationStore.BatchGet(ctx, nil)
}

func (m *manager) GetBindRelation(ctx context.Context, entityInst *pb.Entity) (*entity.ObjectRelationBinding, error) {
	return m.relationStore.GetRelationBinding(ctx, entityInst)
}

func (m *manager) DeleteRelationBinding(ctx context.Context, entityInst *pb.Entity) error {
	err := m.relationStore.DeleteRelationBinding(ctx, entityInst)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteRelationBinding entityInst:%s err:%v", entityInst.String(), err)
		return err
	}
	uid := metainfo.GetServiceInfo(ctx).UserID()
	switchRelationEvent := &pb.SwitchRelationEvent{
		Entity: entityInst,
		Uid:    uid,
		Op:     pb.SwitchRelationRequest_OP_DEL,
	}
	err = m.eventBus.PublishSwitchRelationEvent(ctx, switchRelationEvent)
	if err != nil {
		log.ErrorWithCtx(ctx, "SwitchRelation PublishSwitchRelationEvent entityInst:%s err:%v", entityInst.String(), err)
	}
	return err
}

func (m *manager) BatchGetRelationBinding(ctx context.Context, entityInsts []*pb.Entity) (map[string]*entity.ObjectRelationBinding, error) {
	return m.relationStore.BatchGetRelationBinding(ctx, entityInsts)
	
}
