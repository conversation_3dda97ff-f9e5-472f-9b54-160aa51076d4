package entity

import (
	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"time"
)

// ObjectRelationBinding 实例关系绑定表
type ObjectRelationBinding struct {
	ID         string    `bson:"_id"`
	RelationID string    `bson:"relation_id"`
	UpdateAt   time.Time `bson:"updated_at"`
}

// NewObjectRelationBinding 创建实例关系绑定表
func NewObjectRelationBinding(entity *pb.Entity, relationID string) *ObjectRelationBinding {
	return &ObjectRelationBinding{
		ID:         EncodeID(0, entity),
		RelationID: relationID,
		UpdateAt:   time.Now(),
	}
}
