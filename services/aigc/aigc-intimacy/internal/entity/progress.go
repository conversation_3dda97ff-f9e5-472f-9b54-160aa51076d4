package entity

import (
	"fmt"
	"strconv"
	"time"

	"github.com/gookit/goutil/strutil"
	"github.com/gookit/goutil/timex"

	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
)

// LevelUpCondProgress 升级条件进度
type LevelUpCondProgress struct {
	ID string

	BeginAt time.Time
	EndAt   time.Time

	Uid    uint32 // 关联的用户
	CondID string // 关联的升级条件

	AddedValue     uint32 // 增长的亲密度值
	CompletedNum   uint32 // 达成的次数
	CompletedRound uint32 // 达成的轮次
}

func NewLevelUpCondProgress(uid uint32, en *pb.Entity, cond *IntimacyCondition) (*LevelUpCondProgress, error) {
	dayStartTime := timex.TodayStart()
	p := &LevelUpCondProgress{
		ID: EncodeLevelUpCondProgressID(uid, en, cond.ID),

		BeginAt: dayStartTime,
		EndAt:   dayStartTime.Add(cond.Duration()),

		Uid:    uid,
		CondID: cond.ID,
	}

	return p, nil
}

func (p *LevelUpCondProgress) IsExpired() bool {
	now := time.Now()
	return now.Before(p.BeginAt) || now.After(p.EndAt)
}

func (p *LevelUpCondProgress) SerialNo() string {
	return fmt.Sprintf("%s:%d:%d:%d", p.ID, p.BeginAt.Unix(), p.EndAt.Unix(), p.CompletedRound+1)
}

func (p *LevelUpCondProgress) Duration() time.Duration {
	return p.EndAt.Sub(p.BeginAt)
}

func (p *LevelUpCondProgress) Encode() map[string]interface{} {
	return map[string]interface{}{
		"id": p.ID,

		"uid":  strconv.FormatUint(uint64(p.Uid), 10),
		"cond": p.CondID,

		"begin": strconv.FormatInt(p.BeginAt.Unix(), 10),
		"end":   strconv.FormatInt(p.EndAt.Unix(), 10),

		"val":   strconv.FormatUint(uint64(p.AddedValue), 10),
		"num":   strconv.FormatUint(uint64(p.CompletedNum), 10),
		"round": strconv.FormatUint(uint64(p.CompletedRound), 10),
	}
}

func (p *LevelUpCondProgress) Decode(data map[string]string) error {
	uid, err := strutil.ToUint(data["uid"])
	if err != nil {
		return err
	}

	beginAt, err := strutil.ToInt64(data["begin"])
	if err != nil {
		return err
	}

	endAt, err := strutil.ToInt64(data["end"])
	if err != nil {
		return err
	}

	p.ID = data["id"]

	p.Uid = uint32(uid)
	p.CondID = data["cond"]

	p.BeginAt = time.Unix(beginAt, 0)
	p.EndAt = time.Unix(endAt, 0)

	p.AddedValue = uint32(strutil.Uint(data["val"]))
	p.CompletedNum = uint32(strutil.Uint(data["num"]))
	p.CompletedRound = uint32(strutil.Uint(data["round"]))

	return nil
}

func (p *LevelUpCondProgress) Clone() *LevelUpCondProgress {
	if p == nil {
		return nil
	}

	np := *p
	return &np
}

func (p *LevelUpCondProgress) Compare(o *LevelUpCondProgress) bool {
	return (p != nil && o == nil || p != nil && o != nil && *p != *o)
}

func EncodeLevelUpCondProgressID(uid uint32, en *pb.Entity, condID string) string {
	return fmt.Sprintf("%s:%s", EncodeID(uid, en), condID)
}

type LevelUpCondProgressList []*LevelUpCondProgress
