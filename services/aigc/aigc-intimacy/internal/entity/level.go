package entity

import (
	"fmt"
	"strings"
	"time"

	"github.com/gookit/goutil/strutil"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
)

// Level 等级
type Level struct {
	ID        string    `bson:"_id"`
	CreatedAt time.Time `bson:"created_at"`

	Uid uint32 `bson:"uid"`

	Level uint32 `bson:"level"` // 亲密度等级
	Value uint32 `bson:"value"` // 当前等级内亲密度值

	Sum uint32 `bson:"sum"` // 亲密度总值
}

func NewLevel(uid uint32, en *pb.Entity) (*Level, error) {
	if uid == 0 {
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "miss Uid")
	}

	switch en.GetType() {
	case pb.Entity_TYPE_PARTNER:
	default:
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "unsupported Entity.Type")
	}
	if en.GetId() == 0 {
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "miss Entity.Id")
	}

	lv := &Level{
		ID:        EncodeID(uid, en),
		CreatedAt: time.Now(),

		Uid:   uid,
		Level: 1,
	}

	return lv, nil
}

func (l *Level) Event() *pb.LevelUpEvent {
	_, en := DecodeID(l.ID)
	return &pb.LevelUpEvent{
		Uid:    l.Uid,
		Entity: en,

		Level: l.Level,
		Value: l.Value,
	}
}

func EncodeID(uid uint32, en *pb.Entity) string {
	id := fmt.Sprintf("%d:%d", en.GetType(), en.GetId())
	if uid > 0 {
		id = fmt.Sprintf("%d:%s", uid, id)
	}

	return id
}

func DecodeID(id string) (uid uint32, en *pb.Entity) {
	en = new(pb.Entity)
	seg := strings.Split(id, ":")
	switch len(seg) {
	case 2:
		en.Type = pb.Entity_Type(strutil.SafeUint(seg[0]))
		en.Id = uint32(strutil.SafeUint(seg[1]))
	case 3:
		uid = uint32(strutil.SafeUint(seg[0]))
		en.Type = pb.Entity_Type(strutil.SafeUint(seg[1]))
		en.Id = uint32(strutil.SafeUint(seg[2]))
	}

	return
}
