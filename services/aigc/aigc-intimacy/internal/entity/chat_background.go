package entity

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"time"
)

// ChatBackground 聊天背景配置
type ChatBackground struct {
	ID        string    `bson:"_id,omitempty"`
	CreatedAt time.Time `bson:"created_at"` // 创建时间
	UpdatedAt time.Time `bson:"updated_at"` // 更新时间

	Name            string `bson:"name"`             // 背景名称
	ImageUrl        string `bson:"image_url"`        // 背景图片地址
	ShowAvatar      bool   `bson:"show_avatar"`      // 是否展示聊天角色头像
	DefaultUnlock   bool   `bson:"default_unlock"`   // 是否默认拥有
	UnlockCondition string `bson:"unlock_condition"` // 解锁条件外显文案

	BindEntityType pb.ChatBackground_BindEntityType `bson:"bind_entity_type"` // 绑定对象类型
	BindEntityIds  []uint32                         `bson:"bind_entity_ids"`  // 绑定对象id列表
}

func NewChatBackground(background *pb.ChatBackground) *ChatBackground {
	chatBackground := &ChatBackground{
		ID:              primitive.NewObjectID().Hex(),
		Name:            background.GetName(),
		ImageUrl:        background.GetImageUrl(),
		ShowAvatar:      background.GetShowAvatar(),
		DefaultUnlock:   background.GetDefaultUnlock(),
		UnlockCondition: background.GetUnlockCondition(),
		BindEntityType:  background.GetBindEntityType(),
		BindEntityIds:   deduplicateEntityIds(background.GetBindEntityIds()),
	}

	return chatBackground
}

func (c *ChatBackground) Update(background *pb.ChatBackground) {
	c.Name = background.GetName()
	c.ImageUrl = background.GetImageUrl()
	c.ShowAvatar = background.GetShowAvatar()
	c.DefaultUnlock = background.GetDefaultUnlock()
	c.UnlockCondition = background.GetUnlockCondition()
	c.BindEntityType = background.GetBindEntityType()
	c.BindEntityIds = deduplicateEntityIds(background.GetBindEntityIds())
}

func deduplicateEntityIds(entityIds []uint32) []uint32 {
	existMap := make(map[uint32]struct{})
	result := make([]uint32, 0)
	for _, id := range entityIds {
		if _, ok := existMap[id]; !ok {
			result = append(result, id)
			existMap[id] = struct{}{}
		}
	}
	return result
}

// ObjectBackgroundBinding 实例背景绑定表
type ObjectBackgroundBinding struct {
	ID           string    `bson:"_id"`
	BackgroundID string    `bson:"background_id"`
	UpdateAt     time.Time `bson:"update_at"`
}

func NewObjectBackgroundBinding(inst *pb.Entity, backgroundID string) *ObjectBackgroundBinding {
	return &ObjectBackgroundBinding{
		ID:           EncodeID(0, inst),
		BackgroundID: backgroundID,
		UpdateAt:     time.Now(),
	}
}
