package entity

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"time"
)

// Relationship 关系配置
type Relationship struct {
	ID        string    `bson:"_id,omitempty"`
	CreatedAt time.Time `bson:"created_at"` // 创建时间
	UpdatedAt time.Time `bson:"updated_at"` // 更新时间

	Name                  string `bson:"name"`                    // 关系名称
	AIGCParams            string `bson:"aigc_params"`             // AIGC传递模型参数
	BindBackgroundID      string `bson:"bind_background_id"`      // 绑定聊天背景id
	UnlockCondition       string `bson:"unlock_condition"`        // 解锁条件外显文案
	Icon                  string `bson:"icon"`                    // 外显icon
	IntimacyBackgroundImg string `bson:"intimacy_background_img"` // 亲密度页面展示背景图
}

func NewRelationship(relationship *pb.Relationship) *Relationship {
	return &Relationship{
		ID:                    primitive.NewObjectID().Hex(),
		Name:                  relationship.GetName(),
		AIGCParams:            relationship.GetAigcParams(),
		BindBackgroundID:      relationship.GetBindBackgroundId(),
		UnlockCondition:       relationship.GetUnlockCondition(),
		Icon:                  relationship.GetIcon(),
		IntimacyBackgroundImg: relationship.GetIntimacyBackgroundImg(),
	}
}

func (r *Relationship) Update(relationship *pb.Relationship) {
	r.Name = relationship.GetName()
	r.AIGCParams = relationship.GetAigcParams()
	r.BindBackgroundID = relationship.GetBindBackgroundId()
	r.UnlockCondition = relationship.GetUnlockCondition()
	r.Icon = relationship.GetIcon()
	r.IntimacyBackgroundImg = relationship.GetIntimacyBackgroundImg()
}
