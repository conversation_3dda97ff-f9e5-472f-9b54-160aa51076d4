package entity

import (
	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"time"
)

// IntimacyLevelConf 亲密度等级配置
type IntimacyLevelConf struct {
	ID        uint32    `bson:"_id,omitempty"` // 等级作为主键
	CreatedAt time.Time `bson:"created_at"`    // 创建时间
	UpdatedAt time.Time `bson:"updated_at"`    // 更新时间

	Level        uint32         `bson:"level"`         // 等级
	RequireValue uint32         `bson:"require_value"` // 达到等级所需亲密度值
	Benefits     *LevelBenefits `bson:"benefits"`      // 权益配置
	BenefitDesc  string         `bson:"benefit_desc"`  // 权益描述
}

// LevelBenefits 权益配置
type LevelBenefits struct {
	ExtraSendMsgCount   uint32   `bson:"extra_send_msg_count"`  // 额外句数
	ExtraReadMindCount  uint32   `bson:"extra_read_mind_count"` // 额外读心次数
	UnlockRelationships []string `bson:"unlock_relationships"`  // 解锁关系列表
	UnlockBackgrounds   []string `bson:"unlock_backgrounds"`    // 解锁背景列表
}

func NewIntimacyLevelConf(levelConf *pb.IntimacyLevelConf) *IntimacyLevelConf {
	return &IntimacyLevelConf{
		ID:           levelConf.GetLevel(),
		Level:        levelConf.GetLevel(),
		RequireValue: levelConf.GetRequireValue(),
		Benefits: &LevelBenefits{
			ExtraSendMsgCount:   levelConf.GetBenefits().GetExtraSendMsgCount(),
			ExtraReadMindCount:  levelConf.GetBenefits().GetExtraReadMindCount(),
			UnlockRelationships: deduplicateStrs(levelConf.GetBenefits().GetUnlockRelationships()),
			UnlockBackgrounds:   deduplicateStrs(levelConf.GetBenefits().GetUnlockBackgrounds()),
		},
		BenefitDesc: levelConf.GetBenefitDesc(),
	}
}

func (i *IntimacyLevelConf) Update(levelConf *pb.IntimacyLevelConf) {
	i.RequireValue = levelConf.GetRequireValue()
	i.Benefits = &LevelBenefits{
		ExtraSendMsgCount:   levelConf.GetBenefits().GetExtraSendMsgCount(),
		ExtraReadMindCount:  levelConf.GetBenefits().GetExtraReadMindCount(),
		UnlockRelationships: deduplicateStrs(levelConf.GetBenefits().GetUnlockRelationships()),
		UnlockBackgrounds:   deduplicateStrs(levelConf.GetBenefits().GetUnlockBackgrounds()),
	}
	i.BenefitDesc = levelConf.GetBenefitDesc()
}

func deduplicateStrs(strs []string) []string {
	existMap := make(map[string]struct{})
	result := make([]string, 0)
	for _, s := range strs {
		if _, ok := existMap[s]; !ok {
			result = append(result, s)
			existMap[s] = struct{}{}
		}
	}
	return result
}
