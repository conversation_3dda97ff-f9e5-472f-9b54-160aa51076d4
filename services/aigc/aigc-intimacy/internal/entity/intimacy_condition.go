package entity

import (
	"time"

	"github.com/gookit/goutil/timex"
	"go.mongodb.org/mongo-driver/bson/primitive"

	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
)

// IntimacyCondition 亲密度增加条件配置
type IntimacyCondition struct {
	ID        string    `bson:"_id,omitempty"`
	CreatedAt time.Time `bson:"created_at"` // 创建时间
	UpdatedAt time.Time `bson:"updated_at"` // 更新时间

	ConditionType pb.ConditionType `bson:"condition_type"` // 亲密度增加条件
	RequireCount  uint32           `bson:"require_count"`  // 达成条件数量
	ValueAdd      uint32           `bson:"value_add"`      // 增加亲密度值
	DailyLimit    uint32           `bson:"daily_limit"`    // 每日亲密度上限
	Icon          string           `bson:"icon"`           // 外显icon
	Title         string           `bson:"title"`          // 外显标题
	Desc          string           `bson:"desc"`           // 外显描述
}

func NewIntimacyCondition(condition *pb.IntimacyCondition) *IntimacyCondition {
	return &IntimacyCondition{
		ID:            primitive.NewObjectID().Hex(),
		ConditionType: condition.GetConditionType(),
		RequireCount:  condition.GetRequireCount(),
		ValueAdd:      condition.GetValueAdd(),
		DailyLimit:    condition.GetDailyLimit(),
		Icon:          condition.GetIcon(),
		Title:         condition.GetTitle(),
		Desc:          condition.GetDesc(),
	}
}

func (i *IntimacyCondition) Update(condition *pb.IntimacyCondition) {
	i.RequireCount = condition.GetRequireCount()
	i.ValueAdd = condition.GetValueAdd()
	i.DailyLimit = condition.GetDailyLimit()
	i.Icon = condition.GetIcon()
	i.Title = condition.GetTitle()
	i.Desc = condition.GetDesc()
}

func (i *IntimacyCondition) Duration() time.Duration {
	var duration time.Duration
	switch i.ConditionType {
	case pb.ConditionType_CONDITION_TYPE_CHAT_MSG_COUNT:
		duration = timex.Day
	case pb.ConditionType_CONDITION_TYPE_CHAT_DAY:
		duration = time.Duration(i.RequireCount) * timex.Day
	}

	return duration
}
