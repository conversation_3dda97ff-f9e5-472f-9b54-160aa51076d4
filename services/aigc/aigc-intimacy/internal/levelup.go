package internal

import (
	"context"
	"fmt"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	chat_bot "golang.52tt.com/protocol/services/chat-bot"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
)

func (s *Server) GetLevelUpConditionProgress(ctx context.Context, req *pb.GetLevelUpConditionProgressRequest) (*pb.GetLevelUpConditionProgressResponse, error) {
	resp := new(pb.GetLevelUpConditionProgressResponse)

	if req.GetUid() == 0 || req.GetEntity() == nil {
		log.WarnWithCtx(ctx, "GetLevelUpProgress invalid req: %+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	condList, err := s.levelUpMgr.GetAllIntimacyCondition(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLevelUpProgress GetAllIntimacyCondition err: %v", err)
		return resp, err
	}
	if len(condList) == 0 {
		return resp, nil
	}

	condIdList := make([]string, 0, len(condList))
	for _, cond := range condList {
		condIdList = append(condIdList, cond.ID)
	}

	progressMap, err := s.levelUpMgr.BatchGetConditionProgress(ctx, req.GetUid(), req.GetEntity(), condIdList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLevelUpProgress BatchGetConditionProgress uid(%d) entity(%+v) condIdList(%+v) err: %v", req.GetUid(), req.GetEntity(), condIdList, err)
		return resp, err
	}

	todayGrowIntimacy, err := s.levelMgr.GetTodayGrowIntimacy(ctx, req.GetUid(), req.GetEntity())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLevelUpProgress GetTodayGrowIntimacy uid(%d) entity(%+v) err: %v", req.GetUid(), req.GetEntity(), err)
		return resp, err
	}

	for _, cond := range condList {
		p := &pb.LevelUpConditionProgress{
			Condition: &pb.LevelUpConditionProgress_LevelUpCondition{
				Id:    cond.ID,
				Icon:  cond.Icon,
				Desc:  cond.Desc,
				Title: cond.Title,
			},
		}
		if progress := progressMap[cond.ID]; progress != nil {
			p.GrowthValue = progress.AddedValue
		}

		resp.List = append(resp.List, p)
	}

	resp.TodayGrowthValue = todayGrowIntimacy
	return resp, nil
}

// handleChatLevelUpCondtion 消费聊天消息，处理聊天升级条件
func (s *Server) handleChatLevelUpCondtion(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	var event chat_bot.AIMsgEvent
	if err := proto.Unmarshal(msg.Value, &event); err != nil {
		log.ErrorWithCtx(ctx, "handleChatLevelUpCondition Unmarshal err: %v", err)
		return err, false
	}

	log.InfoWithCtx(ctx, "handleChatLevelUpCondition event: %+v", event)

	if event.GetTriggerMsgType() != chat_bot.ImTriggerMsgType_ImTriggerMsgRespUser || event.GetSegIdx() != 0 {
		return nil, false
	}

	en := &pb.Entity{Id: event.GetSender().GetId()}
	switch event.GetSender().GetType() {
	case chat_bot.AIMsgEvent_Peer_TYPE_MULTIROLE_PARTNER:
		en.Type = pb.Entity_TYPE_PARTNER
	default:
		return nil, false
	}

	uid := event.GetReceiver().GetId()

	// 获取今日聊天轮次
	rounds, err := s.levelUpMgr.GetTodayChatRounds(ctx, uid, en)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleChatLevelUpCondition RecordChatMsg uid(%d) entity(%+v) err: %v", uid, en, err)
		return err, true
	}

	condList, err := s.levelUpMgr.GetAllIntimacyCondition(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleChatLevelUpCondition GetAllIntimacyCondition err: %v", err)
		return err, true
	}
	if len(condList) == 0 {
		log.WarnWithCtx(ctx, "handleChatLevelUpCondition GetAllIntimacyCondition empty")
		return nil, false
	}

	// 处理聊天升级条件
	for _, cond := range condList {
		var (
			progress  *entity.LevelUpCondProgress
			completed bool
		)
		switch cond.ConditionType {
		case pb.ConditionType_CONDITION_TYPE_CHAT_MSG_COUNT: // 每日聊天轮数
			if progress, completed, err = s.levelUpMgr.HandleCondtion(ctx, uid, en, cond); err != nil {
				log.ErrorWithCtx(ctx, "handleChatLevelUpCondition HandleCondition uid(%d) entity(%+v) cond(%s) err: %v", uid, en, cond.ID, err)
				return err, true
			}
		case pb.ConditionType_CONDITION_TYPE_CHAT_DAY: // 聊天天数
			if rounds == 0 {
				if progress, completed, err = s.levelUpMgr.HandleCondtion(ctx, uid, en, cond); err != nil {
					log.ErrorWithCtx(ctx, "handleChatLevelUpCondition HandleCondition uid(%d) entity(%+v) cond(%s) err: %v", uid, en, cond.ID, err)
					return err, true
				}
			}
		default:
			continue
		}
		if !completed {
			log.InfoWithCtx(ctx, "handleChatLevelUpCondition progress(%+v) uncompleted, skip", progress)
			continue
		}

		var (
			addedValue = min(cond.ValueAdd, cond.DailyLimit-progress.AddedValue)

			reason   = fmt.Sprintf("完成条件【%s】%d次", cond.Title, progress.CompletedRound+1)
			serialNo = progress.SerialNo()
		)
		added, err := s.levelMgr.AddIntimacyValue(ctx, uid, en, addedValue, serialNo, reason)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleChatLevelUpCondition AddIntimacyValue uid(%d) entity(%+v) value(%d) err: %v", uid, en, addedValue, err)
			return err, true
		}
		if !added {
			log.WarnWithCtx(ctx, "handleChatLevelUpCondition AddIntimacyValue uid(%d) entity(%+v) value(%d) serialNo(%s) failed", uid, en, addedValue, serialNo)
			continue
		}

		if _, err = s.levelUpMgr.CompleteCondition(ctx, uid, en, cond.ID, addedValue); err != nil {
			log.ErrorWithCtx(ctx, "handleChatLevelUpCondition CompleteCondition uid(%d) entity(%+v) condId(%s) value(%d) err: %v", uid, en, cond.ID, addedValue, err)
			return err, true
		}

		log.InfoWithCtx(ctx, "handleChatLevelUpCondition uid(%d) entity(%+v) cond(%s) addedValue: %d", uid, en, cond.ID, addedValue)
	}

	if err := s.levelUpMgr.RecordChatMsg(ctx, uid, en, ""); err != nil {
		log.ErrorWithCtx(ctx, "handleChatLevelUpCondition RecordChatMsg uid(%d) entity(%+v) err: %v", uid, en, err)
		return err, true
	}

	return nil, false
}
