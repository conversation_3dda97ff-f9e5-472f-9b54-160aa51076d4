package internal

import (
	"context"

	"gitlab.ttyuyin.com/tyr/x/log"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
)

// GetIntimacyLevel 查询亲密度等级
func (s *Server) GetLevel(ctx context.Context, req *pb.GetLevelRequest) (*pb.GetLevelResponse, error) {
	resp := new(pb.GetLevelResponse)

	if req.GetUid() == 0 || req.GetEntity() == nil {
		log.WarnWithCtx(ctx, "GetIntimacyLevel invalid req: %+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	lv, err := s.levelMgr.GetIntimacyLevel(ctx, req.GetUid(), req.GetEntity())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIntimacyLevel GetIntimacyLevel uid(%d) entity(%+v) err: %v", req.GetUid(), req.GetEntity(), err)
		return resp, err
	}

	resp.Level = lv.Level
	resp.Value = lv.Value
	resp.StartedAt = lv.CreatedAt.Unix()
	return resp, nil
}
