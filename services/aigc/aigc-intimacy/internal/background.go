package internal

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
	
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
)

func (s *Server) GetBindBackground(ctx context.Context, req *pb.GetBindBackgroundRequest) (*pb.GetBindBackgroundResponse, error) {
	out := &pb.GetBindBackgroundResponse{}
	if req.GetEntity() == nil ||
		req.GetEntity().GetType() == pb.Entity_TYPE_UNSPECIFIED ||
		req.GetEntity().GetId() == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	
	binding, err := s.backgroundMgr.GetBackgroundBinding(ctx, req.GetEntity())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetBindBackground GetBackgroundBinding req:%s err: %v", req.String(), err)
		return out, err
	}
	if binding == nil || binding.BackgroundID == "" {
		log.WarnWithCtx(ctx, "GetBindBackground no binding info req:%s binding:%v", req.String(), binding)
		return out, nil
	}
	backgroundInfo, err := s.backgroundMgr.GetChatBackground(ctx, binding.BackgroundID)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetBindBackground GetChatBackground req:%s err: %v", req.String(), err)
		return out, err
	}
	if backgroundInfo != nil {
		out.CurBackground = assembleBackground(backgroundInfo)
	}
	log.InfoWithCtx(ctx, "GetBindBackground success req:%s out:%s", req.String(), out.String())
	return out, nil
}

func (s *Server) GetAvailableBackground(ctx context.Context, req *pb.GetAvailableBackgroundRequest) (
	*pb.GetAvailableBackgroundResponse, error) {
	out := &pb.GetAvailableBackgroundResponse{}
	if req.GetEntity() == nil ||
		req.GetEntity().GetType() == pb.Entity_TYPE_UNSPECIFIED ||
		req.GetEntity().GetId() == 0 ||
		req.GetBindEntity() == nil ||
		req.GetBindEntity().GetType() == pb.BindEntity_ENTITY_TYPE_UNSPECIFIED ||
		req.GetBindEntity().GetId() == 0 ||
		req.GetUid() == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	var err error
	curBackground, err := s.backgroundMgr.GetBackgroundBinding(ctx, req.GetEntity())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAvailableBackground GetBackgroundBinding req:%s err: %v", req.String(), err)
		return out, err
	}
	
	availableMap, useAbleBackground, err := s.getAvailableBackGroundIdMap(ctx, req.GetUid(),
		req.GetEntity(), req.GetBindEntity())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAvailableBackground getAvailableBackGroundIdMap req:%s err: %v", req.String(), err)
		return out, err
	}
	for _, b := range useAbleBackground {
		if curBackground != nil && curBackground.BackgroundID == b.ID {
			out.CurBackground = assembleBackground(b)
		} else if b.DefaultUnlock {
			out.UnLockBackgrounds = append(out.UnLockBackgrounds, assembleBackground(b))
		} else if _, ok := availableMap[b.ID]; ok {
			out.UnLockBackgrounds = append(out.UnLockBackgrounds, assembleBackground(b))
		} else {
			out.LockBackgrounds = append(out.LockBackgrounds, assembleBackground(b))
		}
	}
	log.InfoWithCtx(ctx, "GetAvailableBackground success req:%s ", req.String())
	return out, nil
}

func (s *Server) getAvailableBackGroundIdMap(ctx context.Context, uid uint32, entity *pb.Entity,
	bindEntity *pb.BindEntity) (availableMap map[string]struct{}, useAbleBackground []*entity.ChatBackground, err error) {
	useAbleBackground, err = s.backgroundMgr.GetChatBackgroundByBindEntity(ctx, bindEntity)
	if err != nil {
		log.ErrorWithCtx(ctx, "getAvailableBackGroundIdMap GetChatBackgroundByBindEntity bindEntity:%s err: %v", bindEntity.String(), err)
		return availableMap, useAbleBackground, err
	}
	// 拉亲密度配置，区分是否背景解锁
	levelEntity, err := s.levelMgr.GetIntimacyLevel(ctx, uid, entity)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAvailableRelation GetIntimacyLevel uid:%d  entity:%s err: %v", uid, entity.String(), err)
		return availableMap, useAbleBackground, err
	}
	levelConfs, err := s.levelMgr.GetIntimacyLevelConfLessThan(ctx, levelEntity.Level)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAvailableRelation GetIntimacyLevelConfLessThan uid:%d  entity:%s err: %v", uid, entity.String(), err)
		return availableMap, useAbleBackground, err
	}
	
	relationIds := make([]string, 0, len(levelConfs))
	repeatedRelationMap := make(map[string]struct{})
	availableMap = make(map[string]struct{})
	if len(levelConfs) == 0 {
		return availableMap, nil, nil
	}
	for _, v := range levelConfs {
		for _, id := range v.Benefits.UnlockRelationships {
			if _, ok := repeatedRelationMap[id]; !ok {
				relationIds = append(relationIds, id)
				repeatedRelationMap[id] = struct{}{}
			}
		}
		for _, id := range v.Benefits.UnlockBackgrounds {
			if _, ok := availableMap[id]; !ok {
				availableMap[id] = struct{}{}
			}
		}
	}
	
	for _, v := range useAbleBackground {
		if v.DefaultUnlock {
			if _, ok := availableMap[v.ID]; !ok {
				availableMap[v.ID] = struct{}{}
			}
		}
	}
	if len(relationIds) == 0 {
		return availableMap, useAbleBackground, nil
	}
	relationMap, err := s.relationMgr.GetRelationshipMap(ctx, relationIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "getAvailableBackgroundsFromConf GetRelationshipMap relationIds:%v err: %v", relationIds, err)
		return availableMap, useAbleBackground, err
	}
	for _, v := range relationMap {
		if len(v.BindBackgroundID) > 0 {
			if _, ok := availableMap[v.BindBackgroundID]; !ok {
				availableMap[v.BindBackgroundID] = struct{}{}
			}
		}
	}
	return availableMap, useAbleBackground, nil
}

func (s *Server) SwitchChatBackground(ctx context.Context, req *pb.SwitchChatBackgroundRequest) (*pb.SwitchChatBackgroundResponse, error) {
	out := &pb.SwitchChatBackgroundResponse{}
	
	if req.GetEntity() == nil ||
		req.GetEntity().GetType() == pb.Entity_TYPE_UNSPECIFIED ||
		req.GetEntity().GetId() == 0 ||
		req.GetOp() == pb.SwitchChatBackgroundRequest_OP_UNSPECIFIED ||
		req.GetBindEntity() == nil ||
		req.GetBindEntity().GetType() == pb.BindEntity_ENTITY_TYPE_UNSPECIFIED ||
		req.GetBindEntity().GetId() == 0 {
		
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	
	switch req.GetOp() {
	case pb.SwitchChatBackgroundRequest_OP_SWITCH:
		if err := s.checkBackgroundUseAble(ctx, req.GetEntity(), req.GetBackgroundId(), req.GetBindEntity()); err != nil {
			return out, err
		}
		bindingEntity := entity.NewObjectBackgroundBinding(req.GetEntity(), req.GetBackgroundId())
		err := s.backgroundMgr.SwitchBackground(ctx, bindingEntity)
		if err != nil {
			log.ErrorWithCtx(ctx, "SwitchChatBackground SwitchBackground req:%s err: %v", req.String(), err)
			return out, err
		}
	case pb.SwitchChatBackgroundRequest_OP_DEL:
		err := s.backgroundMgr.DeleteBackgroundBinding(ctx, req.GetEntity())
		if err != nil {
			log.ErrorWithCtx(ctx, "SwitchChatBackground DeleteBackgroundBinding req:%s err: %v", req.String(), err)
			return out, err
		}
	default:
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
		
	}
	
	log.InfoWithCtx(ctx, "SwitchBackground success req:%s finished", req.String())
	return out, nil
}

func (s *Server) checkBackgroundUseAble(ctx context.Context, entityInst *pb.Entity, backgroundID string,
	bindEntity *pb.BindEntity) error {
	// 校验背景是否可用
	uid := metainfo.GetServiceInfo(ctx).UserID()
	
	availableMap, _, err := s.getAvailableBackGroundIdMap(ctx, uid, entityInst, bindEntity)
	if err != nil {
		log.ErrorWithCtx(ctx, "SwitchChatBackground getAvailableBackGroundIdMap err entityInst:%s ", entityInst.String())
		return err
	}
	if _, ok := availableMap[backgroundID]; !ok {
		log.WarnWithCtx(ctx, "SwitchChatBackground checkBackgroundBinding entityInst:%s", entityInst.String())
		info, err := s.backgroundMgr.GetChatBackground(ctx, backgroundID)
		if err != nil {
			log.ErrorWithCtx(ctx, "SwitchChatBackground GetChatBackground entityInst:%s err: %v", entityInst.String(), err)
			return err
		}
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, info.UnlockCondition+"解锁此背景")
	}
	return nil
}
