package internal

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
)

func (s *Server) SwitchRelation(ctx context.Context, req *pb.SwitchRelationRequest) (*pb.SwitchRelationResponse, error) {
	out := &pb.SwitchRelationResponse{}
	if req.GetEntity() == nil ||
		req.GetEntity().GetType() == pb.Entity_TYPE_UNSPECIFIED ||
		req.GetEntity().GetId() == 0 ||
		req.GetOp() == pb.SwitchRelationRequest_OP_UNSPECIFIED {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	
	switch req.GetOp() {
	case pb.SwitchRelationRequest_OP_SWITCH:
		if err := s.checkRelationUseAble(ctx, req.GetEntity(), req.GetRelationId()); err != nil {
			return out, err
		}
		
		err := s.relationMgr.SwitchRelation(ctx, req.GetEntity(), req.GetRelationId())
		if err != nil {
			log.ErrorWithCtx(ctx, "SwitchRelation SwitchRelation req:%s err: %v", req.String(), err)
			return out, err
		}
	case pb.SwitchRelationRequest_OP_DEL:
		err := s.relationMgr.DeleteRelationBinding(ctx, req.GetEntity())
		if err != nil {
			log.ErrorWithCtx(ctx, "SwitchRelation DeleteRelationBinding req:%s err: %v", req.String(), err)
			return out, err
		}
	default:
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	s.reporter.ReportSwitchRelation(ctx, metainfo.GetServiceInfo(ctx).UserID(), req.GetEntity(), req.GetOp())
	
	log.InfoWithCtx(ctx, "SwitchRelation SwitchRelation req:%s finished", req.String())
	return out, nil
}

func (s *Server) checkRelationUseAble(ctx context.Context, entityInst *pb.Entity, relationId string) error {
	// 校验背景是否可用
	uid := metainfo.GetServiceInfo(ctx).UserID()
	unlockMap, err := s.getUnlockRelationIdMap(ctx, uid, entityInst)
	if err != nil {
		log.ErrorWithCtx(ctx, "SwitchChatBackground getAvailableBackGroundIdMap err entityInst:%s ", entityInst.String())
		return err
	}
	if _, ok := unlockMap[relationId]; !ok {
		log.WarnWithCtx(ctx, "SwitchChatBackground checkBackgroundBinding entityInst:%s", entityInst.String())
		info, err := s.relationMgr.GetRelationship(ctx, relationId)
		if err != nil {
			log.ErrorWithCtx(ctx, "SwitchChatBackground GetChatBackground entityInst:%s err: %v", entityInst.String(), err)
			return err
		}
		if info == nil {
			log.WarnWithCtx(ctx, "SwitchChatBackground GetChatBackground entityInst:%s info is nil", entityInst.String())
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
		}
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, info.UnlockCondition+"解锁此背景")
	}
	return nil
}

func (s *Server) GetRelationsByEntity(ctx context.Context, req *pb.GetRelationsByEntityRequest) (*pb.GetRelationsByEntityResponse, error) {
	out := &pb.GetRelationsByEntityResponse{}
	
	if req.GetEntity() == nil ||
		req.GetEntity().GetType() == pb.Entity_TYPE_UNSPECIFIED ||
		req.GetEntity().GetId() == 0 ||
		req.GetUid() == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	curRelationBinding, err := s.relationMgr.GetBindRelation(ctx, req.GetEntity())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAvailableRelation GetBindRelation req:%s err: %v", req.String(), err)
		return out, err
	}
	
	allRelations, err := s.relationMgr.GetAllRelationship(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAvailableRelation GetAllRelationship req:%s err: %v", req.String(), err)
		return out, err
	}
	
	unlockMap, err := s.getUnlockRelationIdMap(ctx, req.GetUid(), req.GetEntity())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAvailableRelation getUnlockRelationIdMap req:%s err: %v", req.String(), err)
		return out, err
	}
	
	for _, relation := range allRelations {
		if curRelationBinding != nil && curRelationBinding.RelationID == relation.ID {
			out.CurRelation = assembleRelation(relation)
		}
		if _, ok := unlockMap[relation.ID]; ok {
			out.UnLockRelations = append(out.UnLockRelations, assembleRelation(relation))
		} else {
			out.LockRelations = append(out.LockRelations, assembleRelation(relation))
		}
	}
	log.InfoWithCtx(ctx, "GetAvailableRelation GetRelationsByEntity req:%s len(out):%d finished", req.String(), len(out.GetUnLockRelations()))
	return out, nil
}

func (s *Server) getUnlockRelationIdMap(ctx context.Context, uid uint32, entityInst *pb.Entity) (map[string]struct{}, error) {
	
	// 拉亲密度配置，区分是关系是否解锁
	levelEntity, err := s.levelMgr.GetIntimacyLevel(ctx, uid, entityInst)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAvailableRelation GetIntimacyLevel entityInst:%s err: %v", entityInst.String(), err)
		return nil, err
	}
	levelConfs, err := s.levelMgr.GetIntimacyLevelConfLessThan(ctx, levelEntity.Level)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAvailableRelation GetIntimacyLevelConfLessThan req:%s err: %v", entityInst.String(), err)
		return nil, err
	}
	unlockMap := make(map[string]struct{})
	if len(levelConfs) == 0 {
		return unlockMap, nil
	}
	for _, v := range levelConfs {
		for _, id := range v.Benefits.UnlockRelationships {
			if _, ok := unlockMap[id]; !ok {
				unlockMap[id] = struct{}{}
			}
		}
	}
	return unlockMap, nil
}

func (s *Server) BatchGetCurRelation(ctx context.Context, req *pb.BatchGetCurRelationRequest) (*pb.BatchGetCurRelationResponse, error) {
	out := &pb.BatchGetCurRelationResponse{}
	if len(req.GetEntityList()) == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	relationBindingMap, err := s.relationMgr.BatchGetRelationBinding(ctx, req.GetEntityList())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAvailableRelation BatchGetRelationBinding req:%s err: %v", req.String(), err)
		return out, err
	}
	if len(relationBindingMap) == 0 {
		log.InfoWithCtx(ctx, "GetAvailableRelation BatchGetRelationBinding req:%s relationBindingMap is nil", req.String())
		return out, nil
	}
	relationIds := make([]string, 0, len(relationBindingMap))
	repeatedMap := make(map[string]struct{}, len(relationBindingMap))
	for _, v := range relationBindingMap {
		if _, ok := repeatedMap[v.RelationID]; !ok {
			relationIds = append(relationIds, v.RelationID)
			repeatedMap[v.RelationID] = struct{}{}
		}
	}
	relationShipMap, err := s.relationMgr.GetRelationshipMap(ctx, relationIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAvailableRelation BatchGetRelationship req:%s err: %v", req.String(), err)
		return out, err
	}
	out.CurRelations = make([]*pb.BatchGetCurRelationResponse_CurRelationBindInfo, 0, len(relationBindingMap))
	for key, v := range relationBindingMap {
		if relation, ok := relationShipMap[v.RelationID]; ok {
			_, inst := entity.DecodeID(key)
			out.CurRelations = append(out.CurRelations, &pb.BatchGetCurRelationResponse_CurRelationBindInfo{
				Entity:   inst,
				Relation: assembleRelation(relation),
			})
		}
	}
	log.InfoWithCtx(ctx, "GetAvailableRelation BatchGetCurRelation req:%s len(out):%d finished", req.String(), len(out.GetCurRelations()))
	return out, nil
}
