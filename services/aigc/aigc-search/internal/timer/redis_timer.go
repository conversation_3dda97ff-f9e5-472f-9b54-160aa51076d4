package timer

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	_ "golang.52tt.com/pkg/monitor/smallprocess"
	"golang.52tt.com/pkg/timer"
	"golang.52tt.com/services/aigc/aigc-search/internal/mgr/group"
	"golang.52tt.com/services/aigc/aigc-search/internal/mgr/like_count"
	"golang.52tt.com/services/aigc/aigc-search/internal/mgr/role"
	"time"
)

type RedisTimer struct {
	timerD       *timer.Timer
	templateMgr  *group.TemplateManager
	roleMgr      *role.RoleManager
	likeCountMgr *like_count.LikeCountManager
}

func NewRedisTimer(ctx context.Context, templateMgr *group.TemplateManager, roleMgr *role.RoleManager,
	likeCountMgr *like_count.LikeCountManager) (*RedisTimer, error) {

	timerD, err := timer.NewTimerD(ctx, "aigc-search")
	if err != nil {
		log.Errorf("NewTimerD err:%v", err)
		return nil, err
	}

	return &RedisTimer{
		templateMgr:  templateMgr,
		roleMgr:      roleMgr,
		likeCountMgr: likeCountMgr,
		timerD:       timerD,
	}, nil
}

func (t *RedisTimer) Start() error {

	// 增量更新
	t.timerD.AddStarUpIntervalTask("WatchRoleInfoChange", time.Second*1, timer.BuildFromLambda(func(ctx context.Context) {
		_ = t.roleMgr.WatchRoleDBChange(ctx)
	}))
	t.timerD.AddStarUpIntervalTask("WatchGroupTemplateInfoChange", time.Second*1, timer.BuildFromLambda(func(ctx context.Context) {
		_ = t.templateMgr.WatchGroupTemplateInfoChange(ctx)
	}))

	t.timerD.AddStarUpIntervalTask("WatchLikeCountChange", time.Second*1, timer.BuildFromLambda(func(ctx context.Context) {
		_ = t.likeCountMgr.WatchLikeCountChange(ctx)
	}))

	// 每天凌晨五点全量更新
	dayCron := "0 0 5 * * *"
	err := t.timerD.AddTask(dayCron, "FullSyncMongoDataToES", timer.BuildFromLambda(func(ctx context.Context) {
		_ = t.roleMgr.FullSyncMongoDataToES()
		_ = t.templateMgr.FullSyncMongoDataToES()
	}))

	if err != nil {
		log.Errorf("redis timer start FullSyncMongoDataToES err:%v", err)
		return err
	}

	t.timerD.Start()
	return nil
}

func (t *RedisTimer) Stop() {
	t.timerD.Stop()
}
