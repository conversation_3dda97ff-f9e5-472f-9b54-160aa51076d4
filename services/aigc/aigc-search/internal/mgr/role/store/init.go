package store

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mongo"
	mongo_driver "go.mongodb.org/mongo-driver/mongo"
)

type AIRoleStore struct {
	client *mongo.ClientImpl

	roleCol *mongo_driver.Collection
}

func NewAIRoleStoreStore(database string, client *mongo.ClientImpl) *AIRoleStore {
	c := &AIRoleStore{
		client: client,
	}

	c.roleCol = client.Database(database).Collection("ai_role")

	return c
}

func (s *AIRoleStore) Close(ctx context.Context) error {
	return s.client.Close(ctx)
}
