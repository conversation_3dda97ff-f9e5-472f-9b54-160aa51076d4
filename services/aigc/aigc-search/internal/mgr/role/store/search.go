package store

import (
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	entity2 "golang.52tt.com/services/aigc/aigc-search/internal/mgr/role/entity"
)

const (
	homePageBanner = 1
	female         = 0
	male           = 1
	otherSex       = 2
)

func (s *AIRoleStore) RoleInfoChangeWatch(ctx context.Context) (*mongo.ChangeStream, error) {
	// 设置 Change Stream 选项
	pipeline := mongo.Pipeline{
		bson.D{
			{Key: "$match", Value: bson.D{
				{Key: "operationType", Value: bson.D{
					{Key: "$in", Value: bson.A{"update", "insert", "delete", "replace"}},
				}},
			}},
		},
	}
	opts := options.ChangeStream().SetFullDocument(options.UpdateLookup)

	// 创建 Change Stream
	changeStream, err := s.roleCol.Watch(ctx, pipeline, opts)
	if err != nil {
		log.ErrorWithCtx(ctx, "创建 Change Stream 失败: %v", err)
		return nil, err
	}
	return changeStream, nil
}

func (s *AIRoleStore) SearchRole(ctx context.Context, opts *entity2.RoleSearchOption, pageSize int64) ([]*entity2.Role, uint32, error) {
	var roles []*entity2.Role
	var lastId uint32
	filter := bson.M{}

	//时间必填
	filter["created_at"] = bson.M{"$gte": opts.StartTime, "$lte": opts.EndTime}

	if opts.RoleId != 0 {
		filter["_id"] = opts.RoleId
	}

	if opts.LastId != 0 {
		filter["_id"] = bson.M{"$lt": opts.LastId}
	}

	if opts.Sex != pb.SearchRoleSexEnum_SEARCH_ROLE_SEX_NONE {
		filter["sex"] = convertSex(opts.Sex)
	}
	if len(opts.CategoryId) > 0 {
		filter["category_id"] = opts.CategoryId
	}
	if opts.State != pb.AIRoleState_AIRoleStateNone {
		filter["state"] = uint32(opts.State)
	}

	if opts.FilterExposed == pb.FilterExpose_FILTER_EXPOSE_TRUE {
		filter["exposed"] = true
	}
	if opts.FilterExposed == pb.FilterExpose_FILTER_EXPOSE_FALSE {
		filter["exposed"] = false
	}
	if len(opts.AuditState) > 0 {
		filter["audit_result"] = bson.M{"$in": opts.AuditState}
	}

	searchOption := options.Find().SetSort(bson.D{
		primitive.E{Key: "_id", Value: -1}}).SetLimit(pageSize)
	cursor, err := s.roleCol.Find(ctx, filter, searchOption)
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchRole Find opts :%s err: %v", opts.String(), err)
		return roles, lastId, err
	}
	err = cursor.All(ctx, &roles)
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchRole All opts:%s err: %v", opts, err)
		return roles, lastId, err
	}
	if len(roles) > 0 {
		lastId = roles[len(roles)-1].ID
	}
	return roles, lastId, nil
}

func convertSex(sex pb.SearchRoleSexEnum) int32 {
	if sex == pb.SearchRoleSexEnum_SEARCH_ROLE_SEX_MALE {
		return male
	} else if sex == pb.SearchRoleSexEnum_SEARCH_ROLE_SEX_FEMALE {
		return female
	} else {
		return otherSex
	}
}
