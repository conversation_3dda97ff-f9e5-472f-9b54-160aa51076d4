package entity

import (
	"fmt"
	"time"

	pb "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
)

type RoleSearchOption struct {
	StartTime     int64
	EndTime       int64
	Sex           pb.SearchRoleSexEnum
	CategoryId    string
	State         pb.AIRoleState
	LastId        uint32
	FilterExposed pb.FilterExpose
	AuditState    []pb.AuditResult
	RoleId        uint32
}

func (o *RoleSearchOption) String() string {
	if o == nil {
		return "opts: nil"
	}
	return fmt.Sprintf("opts: StartTime:%s EndTime:%s Sex:%d CategoryId:%s State:%s LastId:%d IsExpoesd:%v RoleId:%d",
		time.Unix(o.StartTime, 0).Format("2006-01-02"), time.Unix(o.EndTime, 0).Format("2006-01-02"),
		o.Sex, o.CategoryId, o.State.String(), o.LastId, o.FilterExposed.String(), o.RoleId)
}
