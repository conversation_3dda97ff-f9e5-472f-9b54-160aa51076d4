package entity

import (
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	chat_bot "golang.52tt.com/protocol/services/chat-bot"
)

type ReportHandleMethod string

const (
	AuditHandleMethodNoHandle    ReportHandleMethod = "no_handle"     // 不做处理
	AuditHandleMethodAuditNoPass ReportHandleMethod = "audit_no_pass" // 将角色AuditResult字段设置为2
	AuditHandleMethodHideInHome  ReportHandleMethod = "hide_in_home"  // 将角色exposed字段设置为false
)

type AuditContent struct {
	Name      string `bson:"name"`
	Image     string `bson:"image"`
	Avatar    string `bson:"avatar"`
	Prologue  string `bson:"prologue"`
	Character string `bson:"character"`
}

type Greeting struct {
	Seq  uint32 `bson:"seq"`
	Text string `bson:"text"`
}

type Prologue struct {
	Text  string `bson:"text"`
	Audio string `bson:"audio"`
}

type Role struct {
	ID        uint32          `bson:"_id"`
	Uid       uint32          `bson:"uid"`
	Type      pb.AIRoleType   `bson:"type"`   // 角色类型 0:树洞 1:角色扮演
	Source    pb.AIRoleSource `bson:"source"` // 角色来源 1:官方 2:用户
	CreatedAt int64           `bson:"created_at"`
	UpdatedAt int64           `bson:"updated_at"`

	// 兼容旧版本树洞形象的字段
	Style       string `bson:"style,omitempty"`
	Intro       string `bson:"intro,omitempty"`
	DialogColor string `bson:"dialog_color,omitempty"`

	// 前端展示字段
	Sex             int32          `bson:"sex"`              // 角色性别 0:女 1:男 2:其他
	Tags            []string       `bson:"tags"`             // 角色标签
	State           pb.AIRoleState `bson:"state,omitempty"`  // 角色状态 1:公开 2:私有
	CategoryId      string         `bson:"category_id"`      // 角色分类ID
	CornerIcon      string         `bson:"corner_icon"`      // 角标 url
	EntranceTag     string         `bson:"entrance_tag"`     // 角色入口标签
	PrologueAudio   string         `bson:"prologue_audio"`   // 开场白语音 url
	CollectPrologue *Prologue      `bson:"collect_prologue"` // 领取页面开场白

	// 送审字段
	AuditAt      int64          `bson:"audit_at"`      // 送审时间，用于审核结果的幂等处理
	AuditResult  pb.AuditResult `bson:"audit_result"`  // 审核状态 0:审核中 1:审核通过 2:审核拒绝
	AuditContent AuditContent   `bson:"audit_content"` // 审核暂存内容

	// 角色送审信息
	Name      string `bson:"name"`      // 角色名称
	Image     string `bson:"image"`     // 角色背景图 url
	Avatar    string `bson:"avatar"`    // 角色头像 url
	Prologue  string `bson:"prologue"`  // 开场白
	Character string `bson:"character"` // 角色设定/说明

	// 中台字段
	Timbre          string             `bson:"timbre"`            // AIGC中台音色配置ID
	StoryId         string             `bson:"story_id"`          // 故事ID，关联AIGC中台的故事玩法
	StoryMode       pb.AIRoleStoryMode `bson:"story_mode"`        // 故事模式
	Exposed         bool               `bson:"exposed"`           // 是否曝光
	InsertPos       uint32             `bson:"insert_pos"`        // 角色强插位置
	Greetings       []Greeting         `bson:"greetings"`         // 第n次进入触发的打招呼文案
	PromptId        string             `bson:"prompt_id"`         // AIGC中台模型ID
	PromptVersion   string             `bson:"prompt_version"`    // AIGC中台模型版本
	EnableRcmdReply bool               `bson:"enable_rcmd_reply"` // 是否开启推荐回复

	// 角色点赞
	UserLikes   uint32 `bson:"user_likes"`   // 用户点赞数
	ConfigLikes int32  `bson:"config_likes"` // 配置点赞数

	// 群聊角色特殊属性字段
	GroupConfig *GroupRoleConfig `bson:"group_config,omitempty"`
}

// 群开场白
type AIGroupPrologue struct {
	// 开场白文本内容
	Text string `bson:"text"`
	// 开场白语音链接
	Audio string `bson:"audio"`
	// 开场白顺序
	Priority uint32 `bson:"priority"`
}

type GroupRoleConfig struct {
	// 群聊开场白
	Prologues []*AIGroupPrologue `bson:"prologues"`
	// 群聊角色描述
	ChatCharacter string `bson:"chat_character"`
	// 关系描述
	RelationCharacter string `bson:"relation_character"`
	// 角色描述
	Desc string `bson:"desc"`
}

func (r Role) TypeOf(types ...pb.AIRoleType) bool {
	for _, t := range types {
		if r.Type == t {
			return true
		}
	}

	return false
}

func (r Role) CreateByOfficial() bool {
	return r.Source == pb.AIRoleSource_AIRoleSourceOfficial
}

func (r Role) CreateByUser() bool {
	return r.Source == pb.AIRoleSource_AIRoleSourceUser
}

func (r Role) CanBeDeleted() bool {
	return r.Type != pb.AIRoleType_AIRoleTypePartner
}

func (r *Role) AuditPass(auditAt int64) bool {
	if r.AuditAt != auditAt {
		return false
	}
	if r.AuditResult == pb.AuditResult_AuditResultPass {
		return false
	}

	// 审核通过，取出暂存区内容覆盖
	r.Name, r.AuditContent.Name = r.AuditContent.Name, r.Name
	r.Image, r.AuditContent.Image = r.AuditContent.Image, r.Image
	r.Avatar, r.AuditContent.Avatar = r.AuditContent.Avatar, r.Avatar
	r.Prologue, r.AuditContent.Prologue = r.AuditContent.Prologue, r.Prologue
	r.Character, r.AuditContent.Character = r.AuditContent.Character, r.Character

	r.UpdatedAt = time.Now().Unix()
	r.AuditResult = pb.AuditResult_AuditResultPass
	return true
}

func (r *Role) AuditReject(auditAt int64) bool {
	if r.AuditAt != auditAt {
		return false
	}
	if r.AuditResult == pb.AuditResult_AuditResultReject {
		return false
	}

	// 复审不通过，恢复原内容
	if r.AuditResult == pb.AuditResult_AuditResultPass {
		r.Name, r.AuditContent.Name = r.AuditContent.Name, r.Name
		r.Image, r.AuditContent.Image = r.AuditContent.Image, r.Image
		r.Avatar, r.AuditContent.Avatar = r.AuditContent.Avatar, r.Avatar
		r.Prologue, r.AuditContent.Prologue = r.AuditContent.Prologue, r.Prologue
		r.Character, r.AuditContent.Character = r.AuditContent.Character, r.Character
	}

	r.UpdatedAt = time.Now().Unix()
	r.AuditResult = pb.AuditResult_AuditResultReject
	return true
}

func (r Role) ToChangeEvent(op chat_bot.AIRoleChangeEvent_Op) *chat_bot.AIRoleChangeEvent {
	ev := &chat_bot.AIRoleChangeEvent{
		Op: op,
		Id: r.ID,
		Role: &chat_bot.AIRoleChangeEvent_RoleInfo{
			Name:            r.Name,
			Source:          uint32(r.Source),
			Uid:             r.Uid,
			Type:            uint32(r.Type),
			State:           uint32(r.State),
			Sex:             uint32(r.Sex),
			BackgroundImage: r.Image,
			Avatar:          r.Avatar,
			Character:       r.Character,
			PromptId:        r.PromptId,
			PromptVersion:   r.PromptVersion,
			EnableRcmdReply: r.EnableRcmdReply,
			Prologue:        r.Prologue,
			PrologueVoice:   r.PrologueAudio,
			CategoryId:      r.CategoryId,
			Tags:            r.Tags,
			Timbre:          r.Timbre,
			NthTextList:     make([]*chat_bot.AIRoleChangeEvent_RoleNthText, 0, len(r.Greetings)),
			AuditResult:     uint32(r.AuditResult),
			InsertPos:       r.InsertPos,
			Exposed:         r.Exposed,
			StoryId:         r.StoryId,
		},
	}

	for _, greeting := range r.Greetings {
		ev.Role.NthTextList = append(ev.Role.NthTextList, &chat_bot.AIRoleChangeEvent_RoleNthText{
			Seq:  greeting.Seq,
			Text: greeting.Text,
		})
	}

	if r.Type == pb.AIRoleType_AIRoleTypeGroup && r.GroupConfig != nil {
		ev.Role.GroupRoleConfig = &chat_bot.AIRoleChangeEvent_GroupRoleConfig{
			ChatCharacter:     r.GroupConfig.ChatCharacter,
			RelationCharacter: r.GroupConfig.RelationCharacter,
			Desc:              r.GroupConfig.Desc,
		}
		for _, prologue := range r.GroupConfig.Prologues {
			ev.Role.GroupRoleConfig.Prologues = append(ev.Role.GroupRoleConfig.Prologues,
				&chat_bot.AIRoleChangeEvent_AIGroupPrologue{
					Text:     prologue.Text,
					Audio:    prologue.Audio,
					Priority: prologue.Priority,
				})
		}
	}
	return ev
}

func (r *Role) AuditRejectByConfig(auditAt int64, handleMethod ReportHandleMethod) bool {
	if r.AuditAt != auditAt {
		return false
	}
	switch handleMethod {
	case AuditHandleMethodNoHandle:
		return r.AuditPass(auditAt)
	case AuditHandleMethodHideInHome:
		return r.AuditFakeReject(auditAt)
	case AuditHandleMethodAuditNoPass:
		return r.AuditReject(auditAt)
	default:
		log.Errorf("unknown handle r:%+v method %s", r, handleMethod)
		return r.AuditReject(auditAt)
	}
}

func (r *Role) AuditFakeReject(auditAt int64) bool {
	if r.AuditAt != auditAt {
		return false
	}
	r.Exposed = false
	if r.AuditResult == pb.AuditResult_AuditResultPass {
		return true
	}

	// 审核通过，取出暂存区内容覆盖
	r.Name, r.AuditContent.Name = r.AuditContent.Name, r.Name
	r.Image, r.AuditContent.Image = r.AuditContent.Image, r.Image
	r.Avatar, r.AuditContent.Avatar = r.AuditContent.Avatar, r.Avatar
	r.Prologue, r.AuditContent.Prologue = r.AuditContent.Prologue, r.Prologue
	r.Character, r.AuditContent.Character = r.AuditContent.Character, r.Character

	r.AuditResult = pb.AuditResult_AuditResultPass
	return true
}

// Update 修改角色
func (r *Role) Update(role pb.UpdateAIRoleReq_Role) error {
	switch pb.AIRoleType(r.Type) {
	case pb.AIRoleType_AIRoleTypePartner:
		return r.updateSoulmate(role)
	case pb.AIRoleType_AIRoleTypeGame:
		return r.updateGame(role)
	case pb.AIRoleType_AIRoleTypePet:
		return r.updatePet(role)
	case pb.AIRoleType_AIRoleTypeGroup:
		return r.updateGroupRole(role)

	default:
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "不支持的来源")
	}
}

// updateGroup 修改群角色
// updatePet 修改桌宠角色
func (r *Role) updateGroupRole(role pb.UpdateAIRoleReq_Role) error {
	now := time.Now().Unix()

	r.UpdatedAt = now

	r.Sex = role.GetSex()
	r.Name = role.GetName()
	r.Avatar = role.GetAvatar()
	r.Timbre = role.GetTimbre()
	r.Character = role.GetCharacter()
	r.CategoryId = role.GetCategoryId()
	r.GroupConfig = &GroupRoleConfig{
		ChatCharacter:     role.GetGroupRoleConfig().GetChatCharacter(),
		RelationCharacter: role.GetGroupRoleConfig().GetRelationCharacter(),
		Desc:              role.GetGroupRoleConfig().GetDesc(),
	}
	for _, prologue := range role.GetGroupRoleConfig().GetPrologues() {
		r.GroupConfig.Prologues = append(r.GroupConfig.Prologues, &AIGroupPrologue{
			Text:     prologue.GetText(),
			Audio:    prologue.GetAudio(),
			Priority: prologue.GetPriority(),
		})
	}

	return nil
}

// updateSoulmate 修改树洞角色
func (r *Role) updateSoulmate(role pb.UpdateAIRoleReq_Role) error {
	now := time.Now().Unix()

	r.UpdatedAt = now

	r.Sex = role.GetSex()
	r.Tags = role.GetTags()
	r.Name = role.GetName()
	r.Image = role.GetImage()
	r.Avatar = role.GetAvatar()
	r.Timbre = role.GetTimbre()
	r.StoryId = role.GetStoryId()
	r.Exposed = role.GetExposed()
	r.InsertPos = role.GetInsertPos()
	r.Character = role.GetCharacter()
	r.CornerIcon = role.GetCornerIcon()
	r.CategoryId = role.GetCategoryId()
	r.PromptId = role.GetPromptId()
	r.PromptVersion = role.GetPromptVersion()
	r.EnableRcmdReply = role.GetEnableRcmdReply()
	r.Prologue = role.Prologue
	r.PrologueAudio = role.PrologueAudio

	r.Greetings = make([]Greeting, 0, len(role.GetGreetings()))
	for _, greeting := range role.GetGreetings() {
		r.Greetings = append(r.Greetings, Greeting{
			Seq:  greeting.GetSeq(),
			Text: greeting.GetText(),
		})
	}

	return nil
}

// updateGame 修改角色扮演角色
func (r *Role) updateGame(role pb.UpdateAIRoleReq_Role) error {
	now := time.Now().Unix()

	r.Sex = role.GetSex()
	r.Tags = role.GetTags()
	r.Timbre = role.GetTimbre()
	r.UpdatedAt = now
	r.CategoryId = role.GetCategoryId()

	switch pb.AIRoleSource(r.Source) {
	case pb.AIRoleSource_AIRoleSourceOfficial:
		r.Exposed = role.GetExposed()
		r.StoryId = role.GetStoryId()
		r.StoryMode = role.GetStoryMode()
		r.InsertPos = role.GetInsertPos()
		r.CornerIcon = role.GetCornerIcon()
		r.ConfigLikes = role.GetConfigLikeNum()
		r.EnableRcmdReply = role.GetEnableRcmdReply()
		r.PromptId = role.GetPromptId()
		r.PromptVersion = role.GetPromptVersion()
		r.PrologueAudio = role.GetPrologueAudio()

		r.Greetings = make([]Greeting, 0, len(role.GetGreetings()))
		for _, greeting := range role.GetGreetings() {
			r.Greetings = append(r.Greetings, Greeting{
				Seq:  greeting.GetSeq(),
				Text: greeting.GetText(),
			})
		}

		r.Name = role.GetName()
		r.Image = role.GetImage()
		r.Avatar = role.GetAvatar()
		r.Prologue = role.GetPrologue()
		r.Character = role.GetCharacter()
	case pb.AIRoleSource_AIRoleSourceUser:
		r.State = role.GetState()

		// 这几个字段是一起送审的，如果有一个字段变动了，就重新送审
		if r.Name != role.GetName() ||
			r.Image != role.GetImage() ||
			r.Avatar != role.GetAvatar() ||
			r.Prologue != role.GetPrologue() ||
			r.Character != role.GetCharacter() {

			r.AuditAt = now
			r.AuditResult = pb.AuditResult_AuditResultReview
			r.AuditContent = AuditContent{
				Name:      role.GetName(),
				Image:     role.GetImage(),
				Avatar:    role.GetAvatar(),
				Prologue:  role.GetPrologue(),
				Character: role.GetCharacter(),
			}
		}
	default:
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "不支持的来源")
	}

	return nil
}

// updatePet 修改桌宠角色
func (r *Role) updatePet(role pb.UpdateAIRoleReq_Role) error {
	now := time.Now().Unix()

	r.UpdatedAt = now

	r.Sex = role.GetSex()
	r.Tags = role.GetTags()
	r.Name = role.GetName()
	r.Avatar = role.GetAvatar()
	r.Timbre = role.GetTimbre()
	r.Character = role.GetCharacter()
	r.PromptId = role.GetPromptId()
	r.PromptVersion = role.GetPromptVersion()
	r.EntranceTag = role.GetEntranceTag()
	r.CornerIcon = role.GetCornerIcon()

	for _, greeting := range role.GetGreetings() {
		r.Greetings = append(r.Greetings, Greeting{
			Seq:  greeting.GetSeq(),
			Text: greeting.GetText(),
		})
	}

	if role.GetCollectPrologue() != nil {
		r.CollectPrologue = &Prologue{
			Text:  role.GetCollectPrologue().GetText(),
			Audio: role.GetCollectPrologue().GetAudio(),
		}
	}

	return nil
}

func CreateRole(uid uint32, role pb.CreateAIRoleReq_Role) (*Role, error) {
	switch role.GetType() {
	case pb.AIRoleType_AIRoleTypePartner: // 树洞
		return createSoulmateRole(role)
	case pb.AIRoleType_AIRoleTypeGame: // 角色扮演
		return createGameRole(uid, role)
	case pb.AIRoleType_AIRoleTypePet: // 桌宠
		return createPetRole(role)
	case pb.AIRoleType_AIRoleTypeGroup:
		return createGroupRole(role)
	default:
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "不支持的角色")
	}
}

// createGroupRole 创建群角色
func createGroupRole(role pb.CreateAIRoleReq_Role) (*Role, error) {
	now := time.Now().Unix()

	r := &Role{
		Type:        pb.AIRoleType_AIRoleTypeGroup,
		State:       pb.AIRoleState_AIRoleStatePublic,
		Source:      pb.AIRoleSource_AIRoleSourceOfficial,
		CreatedAt:   now,
		UpdatedAt:   now,
		AuditResult: pb.AuditResult_AuditResultPass,

		// 必填
		Sex:        role.GetSex(),
		Name:       role.GetName(),
		Avatar:     role.GetAvatar(),
		Character:  role.GetCharacter(),
		CategoryId: role.GetCategoryId(),
		Timbre:     role.GetTimbre(),
		GroupConfig: &GroupRoleConfig{
			ChatCharacter:     role.GetGroupRoleConfig().GetChatCharacter(),
			RelationCharacter: role.GetGroupRoleConfig().GetRelationCharacter(),
			Desc:              role.GetGroupRoleConfig().GetDesc(),
		},
	}
	for _, prologue := range role.GetGroupRoleConfig().GetPrologues() {
		r.GroupConfig.Prologues = append(r.GroupConfig.Prologues, &AIGroupPrologue{
			Text:     prologue.GetText(),
			Audio:    prologue.GetAudio(),
			Priority: prologue.GetPriority(),
		})
	}

	return r, nil
}

// createSoulmateRole 创建树洞角色
func createSoulmateRole(role pb.CreateAIRoleReq_Role) (*Role, error) {
	now := time.Now().Unix()

	r := &Role{
		Type:        pb.AIRoleType_AIRoleTypePartner,
		State:       pb.AIRoleState_AIRoleStatePublic,
		Source:      pb.AIRoleSource_AIRoleSourceOfficial,
		CreatedAt:   now,
		UpdatedAt:   now,
		AuditResult: pb.AuditResult_AuditResultPass,

		// 必填
		Sex:             role.GetSex(),
		Tags:            role.GetTags(),
		Name:            role.GetName(),
		Image:           role.GetImage(),
		Avatar:          role.GetAvatar(),
		Exposed:         role.GetExposed(),
		Character:       role.GetCharacter(),
		CategoryId:      role.GetCategoryId(),
		EnableRcmdReply: role.GetEnableRcmdReply(),

		// 选填
		Timbre:        role.GetTimbre(),
		StoryId:       role.GetStoryId(),
		InsertPos:     role.GetInsertPos(),
		Greetings:     make([]Greeting, 0, len(role.GetGreetings())),
		CornerIcon:    role.GetCornerIcon(),
		PromptId:      role.GetPromptId(),
		PromptVersion: role.GetPromptVersion(),
		Prologue:      role.GetPrologue(),
		PrologueAudio: role.GetPrologueAudio(),
	}
	for _, greeting := range role.GetGreetings() {
		r.Greetings = append(r.Greetings, Greeting{
			Seq:  greeting.GetSeq(),
			Text: greeting.GetText(),
		})
	}

	return r, nil
}

// createGameRole 创建角色扮演角色
func createGameRole(uid uint32, role pb.CreateAIRoleReq_Role) (*Role, error) {
	now := time.Now().Unix()

	// 用户创建角色必须有uid
	if role.GetSource() == pb.AIRoleSource_AIRoleSourceUser && uid == 0 {
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "createGameRole miss uid")
	}

	r := &Role{
		Uid:       uid,
		Sex:       role.GetSex(),
		Type:      pb.AIRoleType_AIRoleTypeGame,
		Source:    role.GetSource(),
		CreatedAt: now,
		UpdatedAt: now,

		Tags:       role.GetTags(),
		Timbre:     role.GetTimbre(),
		CategoryId: role.GetCategoryId(),
	}
	switch role.GetSource() {
	case pb.AIRoleSource_AIRoleSourceOfficial:
		// 官方角色默认公开
		r.State = pb.AIRoleState_AIRoleStatePublic

		r.Exposed = role.GetExposed()
		r.StoryId = role.GetStoryId()
		r.StoryMode = role.GetStoryMode()
		r.InsertPos = role.GetInsertPos()
		r.CornerIcon = role.GetCornerIcon()
		r.EnableRcmdReply = role.GetEnableRcmdReply()
		r.PromptId = role.GetPromptId()
		r.PromptVersion = role.GetPromptVersion()
		r.ConfigLikes = role.GetConfigLikeNum()

		r.Greetings = make([]Greeting, 0, len(role.GetGreetings()))
		for _, greeting := range role.GetGreetings() {
			r.Greetings = append(r.Greetings, Greeting{
				Seq:  greeting.GetSeq(),
				Text: greeting.GetText(),
			})
		}

		r.Name = role.GetName()
		r.Image = role.GetImage()
		r.Avatar = role.GetAvatar()
		r.Character = role.GetCharacter()
		r.Prologue = role.GetPrologue()
		r.PrologueAudio = role.GetPrologueAudio()
		r.AuditResult = pb.AuditResult_AuditResultPass
	case pb.AIRoleSource_AIRoleSourceUser:
		// 用户创建角色默认暴露到首页
		r.Exposed = true
		r.EnableRcmdReply = true

		r.State = role.GetState()

		r.AuditAt = now
		// 用户创建角色默认送审
		r.AuditResult = pb.AuditResult_AuditResultReview
		r.AuditContent = AuditContent{
			Name:      role.GetName(),
			Image:     role.GetImage(),
			Avatar:    role.GetAvatar(),
			Prologue:  role.GetPrologue(),
			Character: role.GetCharacter(),
		}
	default:
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "不支持的来源")
	}

	return r, nil
}

// createPetRole 创建桌宠角色
func createPetRole(role pb.CreateAIRoleReq_Role) (*Role, error) {
	now := time.Now().Unix()

	r := &Role{
		Type:        pb.AIRoleType_AIRoleTypePet,
		State:       pb.AIRoleState_AIRoleStatePublic,
		Source:      pb.AIRoleSource_AIRoleSourceOfficial,
		CreatedAt:   now,
		UpdatedAt:   now,
		AuditResult: pb.AuditResult_AuditResultPass,

		// 必填
		Sex:       role.GetSex(),
		Name:      role.GetName(),
		Avatar:    role.GetAvatar(),
		Timbre:    role.GetTimbre(),
		Character: role.GetCharacter(),
		PromptId:  role.GetPromptId(),

		// 选填
		Tags:          role.GetTags(),
		Greetings:     make([]Greeting, 0, len(role.GetGreetings())),
		EntranceTag:   role.GetEntranceTag(),
		PromptVersion: role.GetPromptVersion(),
		CornerIcon:    role.GetCornerIcon(),
	}
	for _, greeting := range role.GetGreetings() {
		r.Greetings = append(r.Greetings, Greeting{
			Seq:  greeting.GetSeq(),
			Text: greeting.GetText(),
		})
	}
	if role.GetCollectPrologue() != nil {
		r.CollectPrologue = &Prologue{
			Text:  role.GetCollectPrologue().GetText(),
			Audio: role.GetCollectPrologue().GetAudio(),
		}
	}

	return r, nil
}

type RoleList []*Role

func (list RoleList) Filter(filter func(role *Role) bool) RoleList {
	var res []*Role
	for _, role := range list {
		if filter(role) {
			res = append(res, role)
		}
	}

	return res
}
