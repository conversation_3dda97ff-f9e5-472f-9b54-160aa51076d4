package role

import (
	"context"
	"github.com/elastic/go-elasticsearch/v7"
	mongo2 "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mongo"
	"go.mongodb.org/mongo-driver/bson"
	"golang.52tt.com/pkg/log"
	searchPb "golang.52tt.com/protocol/services/aigc/aigc-search"
	pb "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	"golang.52tt.com/services/aigc/aigc-search/internal/mgr/esdao"
	"golang.52tt.com/services/aigc/aigc-search/internal/mgr/role/entity"
	"golang.52tt.com/services/aigc/aigc-search/internal/mgr/role/store"
	"math"
	"time"
)

type RoleManager struct {
	store *store.AIRoleStore
	esDao *esdao.EsDao
}

func NewRoleManager(database string, mongoClient *mongo2.ClientImpl, esCli *elasticsearch.Client) (*RoleManager, error) {
	mongoStore := store.NewAIRoleStoreStore(database, mongoClient)
	esDao := esdao.NewEsDao(esCli)
	mgr := &RoleManager{
		esDao: esDao,
		store: mongoStore,
	}
	return mgr, nil
}

func (m *RoleManager) WatchRoleDBChange(ctx context.Context) error {
	t := time.Now()
	log.InfoWithCtx(ctx, "WatchRoleDBChange start")
	defer func() {
		log.InfoWithCtx(ctx, "WatchRoleDBChange end, cost:%d", time.Since(t).String())
	}()
	changeStream, err := m.store.RoleInfoChangeWatch(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "WatchRoleDBChange RoleInfoChangeWatch err: %v", err)
		return err
	}
	defer changeStream.Close(ctx)

	// 监听变更
	for changeStream.Next(ctx) {
		var changeStreamInfo bson.M
		if err := changeStream.Decode(&changeStreamInfo); err != nil {
			log.ErrorWithCtx(ctx, "解码 Change Stream 失败: %v", err)
			break
		}
		if v, ok := changeStreamInfo["operationType"].(string); ok && v == "delete" {
			roleId := uint32(changeStreamInfo["documentKey"].(bson.M)["_id"].(int64))
			log.InfoWithCtx(ctx, "WatchRoleDBChange delete role(%d)", roleId)

			esErr := m.esDao.DelByID(ctx, []esdao.EsRole{{RoleId: roleId, EntityType: int(searchPb.AigcContentType_AIGC_CONTENT_TYPE_ROLE)}})
			if esErr != nil {
				log.ErrorWithCtx(ctx, "WatchRoleDBChange es DelByID id(%d) err: %v", roleId, esErr)
			}
			continue
		}

		data, ok := changeStreamInfo["fullDocument"].(bson.M)
		if !ok {
			log.ErrorWithCtx(ctx, "Change Stream 没有 fullDocument 字段")
			continue
		}
		log.DebugWithCtx(ctx, "Change Stream: %v", data)

		//role := transformRoleToEsRole(data)
		role := new(entity.Role)
		if err := bsonTransMapToStruct(data, role); err != nil {
			log.ErrorWithCtx(ctx, "WatchRoleDBChange bsonTransMapToStruct err: %v", err)
			continue
		}
		log.InfoWithCtx(ctx, "WatchRoleDBChange role(%+v)", role)
		if role.AuditResult != pb.AuditResult_AuditResultPass {
			if role.AuditResult == pb.AuditResult_AuditResultReject {
				esErr := m.esDao.DelByID(ctx, []esdao.EsRole{{RoleId: role.ID, EntityType: int(searchPb.AigcContentType_AIGC_CONTENT_TYPE_ROLE)}})
				if esErr != nil {
					log.ErrorWithCtx(ctx, "WatchRoleDBChange es DelByID id(%d) err: %v", role.ID, esErr)
				}
			}
			continue
		}

		esRole := esdao.EsRole{
			Name:       role.Name,
			Character:  role.Character,
			RoleId:     role.ID,
			Tags:       role.Tags,
			Type:       int(role.Type),
			Source:     int(role.Source),
			Sex:        int(role.Sex),
			State:      int(role.State),
			UserLikes:  int(role.UserLikes),
			CategoryId: role.CategoryId,
			Exposed:    role.Exposed,
			EntityType: int(searchPb.AigcContentType_AIGC_CONTENT_TYPE_ROLE),
		}
		err = m.esDao.UpsertRole(ctx, []esdao.EsRole{esRole})
		if err != nil {
			log.ErrorWithCtx(ctx, "WatchRoleDBChange es UpsertRole role(%+v) err: %v", esRole, err)
			continue
		}
	}
	if err := changeStream.Err(); err != nil {
		log.ErrorWithCtx(ctx, "Change Stream 出错: %v", err)
		return err
	}
	return nil
}

func bsonTransMapToStruct(source bson.M, role *entity.Role) error {
	jsonStr, err := bson.Marshal(source)
	if err != nil {
		return err
	}
	err = bson.Unmarshal(jsonStr, role)
	if err != nil {
		return err
	}
	return nil
}

func (m *RoleManager) FullSyncMongoDataToES() error {
	log.InfoWithCtx(context.Background(), "FullSyncMongoDataToES start")
	var (
		t           = time.Now()
		ctx         = context.Background()
		end         bool
		totalRole   int
		totalEs     int
		limit       = int64(500)
		totalFilter int
		lastId      uint32
	)
	for {
		role, id, err := m.store.SearchRole(ctx, &entity.RoleSearchOption{
			StartTime:  0,
			EndTime:    math.MaxUint32,
			LastId:     lastId,
			AuditState: []pb.AuditResult{pb.AuditResult_AuditResultPass},
		}, limit)
		if err != nil {
			log.ErrorWithCtx(ctx, "FullSyncMongoDataToES SearchRole fail, lastId:%s err:%+v", lastId, err)
			return err
		}
		totalRole += len(role)
		//log.InfoWithCtx(ctx, "SearchRole res len(role):%d id:%s, err:%+v", len(role), id, err)

		lastId = id
		esRoleInfos := make([]esdao.EsRole, 0, len(role))
		for _, v := range role {
			if v.AuditResult != pb.AuditResult_AuditResultPass {
				totalFilter++
				continue
			}
			//log.InfoWithCtx(ctx, "SearchRole role:%+v", *v)
			esRoleInfos = append(esRoleInfos, esdao.EsRole{
				Name:       v.Name,
				Character:  v.Character,
				RoleId:     v.ID,
				Tags:       v.Tags,
				Type:       int(v.Type),
				Source:     int(v.Source),
				Sex:        int(v.Sex),
				State:      int(v.State),
				UserLikes:  int(v.UserLikes),
				CategoryId: v.CategoryId,
				Exposed:    v.Exposed,
				EntityType: int(searchPb.AigcContentType_AIGC_CONTENT_TYPE_ROLE),
			})
		}
		if len(role) < int(limit) {
			end = true
		}
		totalEs += len(esRoleInfos)
		if len(esRoleInfos) > 0 {
			err = m.esDao.UpsertRole(ctx, esRoleInfos)
			if err != nil {
				log.ErrorWithCtx(ctx, "UpsertRole lastId:%s err:%+v", lastId, err)
				continue
			}
		}
		//log.InfoWithCtx(ctx, "transfer role to es success len(role):%d len(esRole):%d", len(role), len(esRoleInfos))
		if end {
			break
		}
	}
	log.InfoWithCtx(ctx, "FullSyncMongoDataToES totalRole:%d totalEs:%d totalFilter:%d, cost:%s",
		totalRole, totalEs, totalFilter, time.Since(t).String())

	return nil
}
