package mgr

import (
	"context"
	"github.com/elastic/go-elasticsearch/v7"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/aigc/aigc-search/internal/mgr/esdao"
)

const searchRolePageSize = 20

type ManagerOption func(context.Context, *Manager) error

type Manager struct {
	esDao *esdao.EsDao
}

func NewManager(esCli *elasticsearch.Client) (*Manager, error) {
	esDao := esdao.NewEsDao(esCli)
	mgr := &Manager{
		esDao: esDao,
	}
	return mgr, nil
}

type SearchContentResp struct {
	Id   uint32
	Type int
}

func (m *Manager) SearchRoleByContent(ctx context.Context, content string, lastId string, limit uint32) ([]*SearchContentResp, string, bool, error) {
	var loadFinish bool
	if limit > searchRolePageSize {
		limit = searchRolePageSize
	}
	esRoles, lastId, err := m.esDao.SearchItemByContent(ctx, content, lastId, limit)
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchItemByContent content(%v) err(%v)", content, err)
		return nil, lastId, loadFinish, err
	}
	if len(esRoles) < searchRolePageSize {
		loadFinish = true
	}
	if len(esRoles) == 0 {
		return nil, lastId, loadFinish, nil
	}
	res := make([]*SearchContentResp, 0, len(esRoles))
	for _, v := range esRoles {
		res = append(res, &SearchContentResp{
			Id:   v.RoleId,
			Type: v.EntityType,
		})
	}
	return res, lastId, loadFinish, nil
}
