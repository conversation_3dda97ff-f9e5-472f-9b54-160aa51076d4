package entity

import (
	pb "golang.52tt.com/protocol/services/aigc/aigc-group"
	"time"
)

type GroupTemplate struct {
	ID                uint32    `bson:"_id"`
	CreatedAt         time.Time `bson:"created_at"`          // 创建时间
	UpdatedAt         time.Time `bson:"updated_at"`          // 更新时间
	Name              string    `bson:"name"`                // 模板名称
	Character         string    `bson:"character"`           // 群设定
	Tags              []string  `bson:"tags"`                // 群标签
	Avatar            string    `bson:"avatar"`              // 模板头像
	Sex               int32     `bson:"sex"`                 // 角色性别 0:女 1:男 2:其他
	ChatBackgroundImg string    `bson:"chat_background_img"` // 聊天背景图
	HomeBackgroundImg string    `bson:"home_background_img"` // 首页背景图
	RelationCharacter string    `bson:"relation_character"`  // 关系网说明
	GroupIcon         string    `bson:"group_icon"`          // 群聊icon
	Exposed           bool      `bson:"exposed"`             // 是否展示/曝光到首页 true:展示 false:不展示
	InsertPos         uint32    `bson:"insert_pos"`          // 强插位置
	CornerIcon        string    `bson:"corner_icon"`         // 角标 url
	CategoryId        string    `bson:"category_id"`         // 群组所属分类ID
	ConfigLikeNum     int32     `bson:"config_like_num"`     // 配置点赞数
	RoleIds           []uint32  `bson:"role_ids"`            // 配置的角色id列表
	
	GroupType pb.GroupType `bson:"group_type"` // 群类型
	
}
