package store

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mongo"
	mongo_driver "go.mongodb.org/mongo-driver/mongo"
)

type GroupTemplateStore struct {
	client *mongo.ClientImpl

	templateCol *mongo_driver.Collection
}

func NewGroupTemplateStore(database string, client *mongo.ClientImpl) *GroupTemplateStore {
	c := &GroupTemplateStore{
		client: client,
	}

	c.templateCol = client.Database(database).Collection("templates")

	return c
}

func (s *GroupTemplateStore) Close(ctx context.Context) error {
	return s.client.Close(ctx)
}
