package store

import (
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/aigc/aigc-search/internal/mgr/group/entity"
)

func (s *GroupTemplateStore) GroupTemplateInfoChangeWatch(ctx context.Context) (*mongo.ChangeStream, error) {
	// 设置 Change Stream 选项
	pipeline := mongo.Pipeline{
		bson.D{
			{Key: "$match", Value: bson.D{
				{Key: "operationType", Value: bson.D{
					{Key: "$in", Value: bson.A{"update", "insert", "delete", "replace"}},
				}},
			}},
		},
	}
	opts := options.ChangeStream().SetFullDocument(options.UpdateLookup)
	
	// 创建 Change Stream
	changeStream, err := s.templateCol.Watch(ctx, pipeline, opts)
	if err != nil {
		log.ErrorWithCtx(ctx, "创建 Change Stream 失败: %v", err)
		return nil, err
	}
	return changeStream, nil
}

func (s *GroupTemplateStore) GetByPage(ctx context.Context, page, pageSize int64) ([]*entity.GroupTemplate, error) {
	searchOption := options.Find().
		SetSkip((page - 1) * pageSize).
		SetLimit(pageSize).
		SetSort(bson.M{"_id": -1})
	
	cursor, err := s.templateCol.Find(ctx, bson.M{}, searchOption)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetByPage Find page :%d pageSize:%d err: %v", page, pageSize, err)
		return nil, err
	}
	var templates []*entity.GroupTemplate
	err = cursor.All(ctx, &templates)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetByPage page:%d pageSize:%d err: %v", page, pageSize, err)
		return nil, err
	}
	return templates, nil
}
