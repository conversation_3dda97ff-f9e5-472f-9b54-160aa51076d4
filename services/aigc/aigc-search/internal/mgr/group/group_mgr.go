package group

import (
	"context"
	"github.com/elastic/go-elasticsearch/v7"
	mongo2 "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mongo"
	"go.mongodb.org/mongo-driver/bson"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/aigc/aigc-group"
	searchPb "golang.52tt.com/protocol/services/aigc/aigc-search"
	"golang.52tt.com/services/aigc/aigc-search/internal/mgr/esdao"
	"golang.52tt.com/services/aigc/aigc-search/internal/mgr/group/entity"
	"golang.52tt.com/services/aigc/aigc-search/internal/mgr/group/store"
	"time"
)

type TemplateManager struct {
	store *store.GroupTemplateStore
	esDao *esdao.EsDao
}

func NewGroupTemplateManager(database string, mongoClient *mongo2.ClientImpl, esCli *elasticsearch.Client) (*TemplateManager, error) {
	mongoStore := store.NewGroupTemplateStore(database, mongoClient)
	esDao := esdao.NewEsDao(esCli)
	mgr := &TemplateManager{
		esDao: esDao,
		store: mongoStore,
	}
	return mgr, nil
}

func (m *TemplateManager) WatchGroupTemplateInfoChange(ctx context.Context) error {
	t := time.Now()
	log.InfoWithCtx(ctx, "WatchGroupTemplateInfoChange start")
	defer func() {
		log.InfoWithCtx(ctx, "WatchGroupTemplateInfoChange end, cost:%d", time.Since(t).String())
	}()
	changeStream, err := m.store.GroupTemplateInfoChangeWatch(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "WatchGroupTemplateInfoChange GroupTemplateInfoChangeWatch err: %v", err)
		return err
	}
	defer changeStream.Close(ctx)

	// 监听变更
	for changeStream.Next(ctx) {
		var changeStreamInfo bson.M
		if err := changeStream.Decode(&changeStreamInfo); err != nil {
			log.ErrorWithCtx(ctx, "解码 Change Stream 失败: %v", err)
			break
		}
		if v, ok := changeStreamInfo["operationType"].(string); ok && v == "delete" {
			id := uint32(changeStreamInfo["documentKey"].(bson.M)["_id"].(int64))
			log.InfoWithCtx(ctx, "WatchGroupTemplateInfoChange delete id(%d)", id)

			esErr := m.esDao.DelByID(ctx, []esdao.EsRole{{RoleId: id, EntityType: int(searchPb.AigcContentType_AIGC_CONTENT_TYPE_GROUP_TEMPLATE)}})
			if esErr != nil {
				log.ErrorWithCtx(ctx, "WatchGroupTemplateInfoChange es DelByID id(%d) err: %v", id, esErr)
			}
			continue
		}

		data, ok := changeStreamInfo["fullDocument"].(bson.M)
		if !ok {
			log.ErrorWithCtx(ctx, "WatchGroupTemplateInfoChange Change Stream 没有 fullDocument 字段")
			continue
		}
		log.DebugWithCtx(ctx, "WatchGroupTemplateInfoChange Change Stream: %v", data)

		//role := transformRoleToEsRole(data)
		template := new(entity.GroupTemplate)
		if err := bsonTransMapToStruct(data, template); err != nil {
			log.ErrorWithCtx(ctx, "WatchGroupTemplateInfoChange bsonTransMapToStruct err: %v", err)
			continue
		}
		if template.GroupType != pb.GroupType_GROUP_TYPE_SINGLE_USER {
			continue
		}
		log.InfoWithCtx(ctx, "WatchGroupTemplateInfoChange role(%+v)", template)
		esRole := esdao.EsRole{
			Name:       template.Name,
			Character:  template.Character,
			RoleId:     template.ID,
			Tags:       template.Tags,
			Sex:        int(template.Sex),
			State:      1,
			CategoryId: template.CategoryId,
			Exposed:    template.Exposed,
			EntityType: int(searchPb.AigcContentType_AIGC_CONTENT_TYPE_GROUP_TEMPLATE),
		}
		err = m.esDao.UpsertRole(ctx, []esdao.EsRole{esRole})
		if err != nil {
			log.ErrorWithCtx(ctx, "WatchGroupTemplateInfoChange es UpsertRole role(%+v) err: %v", esRole, err)
			continue
		}
	}
	if err := changeStream.Err(); err != nil {
		log.ErrorWithCtx(ctx, "WatchGroupTemplateInfoChange Change Stream 出错: %v", err)
		return err
	}
	return nil
}

func bsonTransMapToStruct(source bson.M, template *entity.GroupTemplate) error {
	jsonStr, err := bson.Marshal(source)
	if err != nil {
		return err
	}
	err = bson.Unmarshal(jsonStr, template)
	if err != nil {
		return err
	}
	return nil
}

func (m *TemplateManager) FullSyncMongoDataToES() error {
	log.InfoWithCtx(context.Background(), "TemplateManager FullSyncMongoDataToES start")
	var (
		t         = time.Now()
		ctx       = context.Background()
		totalRole int
		page      = int64(1)
		pageSize  = int64(1000)
	)
	for {
		infos, err := m.store.GetByPage(ctx, page, pageSize)
		if err != nil {
			log.ErrorWithCtx(ctx, "TemplateManager FullSyncMongoDataToES GetByPage fail, page:%s err:%+v", page, err)
			return err
		}
		esRoleInfos := make([]esdao.EsRole, 0, len(infos))
		for _, v := range infos {
			esRoleInfos = append(esRoleInfos, esdao.EsRole{
				Name:       v.Name,
				Character:  v.Character,
				RoleId:     v.ID,
				Tags:       v.Tags,
				Sex:        int(v.Sex),
				State:      1,
				Type:       0,
				CategoryId: v.CategoryId,
				Exposed:    v.Exposed,
				EntityType: int(searchPb.AigcContentType_AIGC_CONTENT_TYPE_GROUP_TEMPLATE),
			})
		}
		totalRole += len(esRoleInfos)
		if len(esRoleInfos) > 0 {
			err = m.esDao.UpsertRole(ctx, esRoleInfos)
			if err != nil {
				log.ErrorWithCtx(ctx, "TemplateManager UpsertRole fail, err:%v, esRoleInfos:%+v", err, esRoleInfos)
				continue
			}
		}
		if len(infos) < int(pageSize) {
			break
		}
	}
	log.InfoWithCtx(ctx, "TemplateManager FullSyncMongoDataToES totalRole:%d, cost:%s", totalRole, time.Since(t).String())
	return nil
}
