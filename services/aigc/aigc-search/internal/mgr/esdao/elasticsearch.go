package esdao

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/elastic/go-elasticsearch/v7"
	"github.com/elastic/go-elasticsearch/v7/esapi"
	"golang.52tt.com/pkg/log"
	searchPb "golang.52tt.com/protocol/services/aigc/aigc-search"
	"io"
	"strconv"
	"strings"
	"time"
)

//go:generate ifacemaker -f elasticsearch.go -s EsDao -p esdao -i IEsDao -o ./esDao_api.go
//go:generate mockgen -destination=./mocks/esDao.go -package=mocks -mock_names=IEsDao=MockIEsDao golang.52tt.com/services/aigc/aigc-soulmate/internal/mgr/role/esdao IEsDao

const (
	roleIndex = "role_info_v2"
)

type EsDao struct {
	Cli *elasticsearch.Client
}

type EsRole struct {
	Name       string   `json:"name"`
	Character  string   `json:"character"`
	RoleId     uint32   `json:"role_id"` // 角色id或者群id
	Tags       []string `json:"tags"`
	Type       int      `json:"type"`        // 角色类型 0:树洞 1:角色扮演
	Source     int      `json:"source"`      // 角色来源 1:官方 2:用户
	Sex        int      `json:"sex"`         // 角色性别 0:女 1:男 2:其他
	State      int      `json:"state"`       // 角色状态 1:公开 2:私有
	UserLikes  int      `json:"user_likes"`  // 用户点赞数
	CategoryId string   `json:"category_id"` // 角色分类ID
	Exposed    bool     `json:"exposed"`     // 是否曝光
	EntityType int      `json:"entity_type"` // 0/1-角色（默认） 2-群组模版 3-群实例
}

type ESResp struct {
	Shards   _shards `json:"_shards"`
	Total    Total   `json:"total"`
	Hits     Hits    `json:"hits"`
	TimedOut bool    `json:"timed_out"`
	Took     int64   `json:"took"`
}

type _shards struct {
	Failed     int64 `json:"failed"`
	Skipped    int64 `json:"skipped"`
	Successful int64 `json:"successful"`
	Total      int64 `json:"total"`
}

type Total struct {
	Value    int64  `json:"value"`
	Relation string `json:"relation"`
}

type Hits struct {
	Hits     []Hit   `json:"hits"`
	MaxScore float64 `json:"max_score"`
}

type Hit struct {
	ID     string        `json:"_id"`
	Index  string        `json:"_index"`
	Score  float64       `json:"_score"`
	Source EsRole        `json:"_source"`
	Type   string        `json:"_type"`
	Sort   []interface{} `json:"sort"`
}

func NewEsDao(cli *elasticsearch.Client) *EsDao {
	p := &EsDao{
		Cli: cli,
	}
	//p.createIndex()
	return p
}

func getEsId(id uint32, entityType int) string {
	if entityType == int(searchPb.AigcContentType_AIGC_CONTENT_TYPE_UNKNOWN) || entityType == int(searchPb.AigcContentType_AIGC_CONTENT_TYPE_ROLE) {
		return strconv.Itoa(int(id))
	} else {
		return fmt.Sprintf("%d_%d", id, entityType)
	}
}

/**
 * @name:
 * @msg: 删除文档
 * @param {string} _id
 * @return {*}
 */
func (esDao *EsDao) DelByID(ctx context.Context, items []EsRole) error {
	if len(items) == 0 {
		return nil
	}
	// 构建批量删除请求
	var requestBody strings.Builder
	for _, item := range items {
		requestBody.WriteString(fmt.Sprintf(`{"delete": {"_index": "%s", "_id": "%s"}}`, roleIndex, getEsId(item.RoleId, item.EntityType)))
		requestBody.WriteString("\n")
	}
	res, err := esDao.Cli.Bulk(
		strings.NewReader(requestBody.String()),
		esDao.Cli.Bulk.WithIndex(roleIndex),
		esDao.Cli.Bulk.WithRefresh("true"), // 可选，根据需要刷新索引
	)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelByID bulk delete:%+v err:%+v", items, err)
		return err
	}
	log.InfoWithCtx(ctx, "DelByIDs ids(%+v) success resp:%s", items, res.String())
	return nil
}

/**
 * @name:
 * @msg: upsert文档
 * @param {*EsRole} item
 * @return {*}
 */
func (esDao *EsDao) UpsertRole(ctx context.Context, items []EsRole) error {
	if len(items) == 0 {
		return nil
	}
	// 构建批量删除请求
	var requestBody strings.Builder
	updateSql := `{"update":{"_id":"%s"}}`
	document := `{"doc": %s,"doc_as_upsert":true}`

	for _, item := range items {
		//item.Name = strings.ReplaceAll(item.Name, "\n", "~")
		//item.Character = strings.ReplaceAll(item.Character, "\n", "~")
		requestBody.WriteString(fmt.Sprintf(updateSql, getEsId(item.RoleId, item.EntityType)))
		requestBody.WriteString("\n")
		marshalData, err := json.Marshal(&item)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpsertRole json.Marshal item:%+v err:%+v", item, err)
			continue
		}
		w := fmt.Sprintf(document, string(marshalData))
		requestBody.WriteString(w)
		requestBody.WriteString("\n")
	}
	res, err := esDao.Cli.Bulk(
		strings.NewReader(requestBody.String()),
		esDao.Cli.Bulk.WithIndex(roleIndex),
		esDao.Cli.Bulk.WithRefresh("true"), // 可选，根据需要刷新索引
	)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpsertRole bulk UpsertRole:%+v err:%+v", items, err)
		return err
	}

	log.InfoWithCtx(ctx, "UpsertRole items(%+v) success resp:%s", items, res.String())
	return nil
}

func (esDao *EsDao) UpdateEsData(ctx context.Context, item EsRole) error {
	// 构建更新请求的 JSON 数据
	updateBody := map[string]interface{}{
		"doc": map[string]interface{}{
			"user_likes": item.UserLikes,
		},
	}
	body, err := json.Marshal(updateBody)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateEsData item:%+v err:%+v", item, err)
		return err
	}

	req := esapi.UpdateRequest{
		Index:      roleIndex,
		DocumentID: getEsId(item.RoleId, item.EntityType),
		Body:       bytes.NewReader(body),
	}

	// 执行请求
	res, err := req.Do(ctx, esDao.Cli)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateEsData item:%+v err:%+v", item, err)
		return err
	}
	if res.StatusCode != 200 {
		log.WarnWithCtx(ctx, "UpdateEsData item:%+v status code:%d", item, res.StatusCode)
	}
	log.InfoWithCtx(ctx, "UpdateEsData item:%+v success resp:%s", item, res.String())
	defer res.Body.Close()

	return nil
}

func encodeLastId(sortData []interface{}) string {
	count := len(sortData)
	if count == 2 {
		return fmt.Sprintf("%v:%v", sortData[0], sortData[1])
	} else if count == 3 {
		return fmt.Sprintf("%v:%v:%v", sortData[0], sortData[1], sortData[2])
	} else {
		return ""
	}
}

func decodeSearchAfter(lastId string, count int) string {
	sortStr := ""
	if lastId == "" {
		return sortStr
	}
	split := strings.Split(lastId, ":")
	if len(split) != count {
		return sortStr
	}
	if count == 2 {
		sortStr = fmt.Sprintf("[%s,%q]", split[0], split[1])

	} else {
		return ""
	}
	return sortStr
}

func (esDao *EsDao) SearchItemByContent(ctx context.Context, content, lastId string, pageSize uint32) ([]EsRole, string, error) {
	start := time.Now()
	// 计算接口耗时
	defer func() {
		elapsed := time.Since(start)
		if elapsed > time.Second {
			log.WarnWithCtx(ctx, "SearchItemByContent content:%s lastId:%s pageSize:%d elapsed:%s", content, lastId, pageSize, elapsed.String())
			return
		}
		log.InfoWithCtx(ctx, "SearchItemByContent content:%s lastId:%s pageSize:%d elapsed:%s", content, lastId, pageSize, elapsed.String())
	}()
	queryText := genFilterQueryByScriptScore(content, lastId, int(pageSize))
	log.DebugWithCtx(ctx, "SearchItemByContent, queryText:%s", queryText)

	res, err := esDao.Cli.Search(
		esDao.Cli.Search.WithIndex(roleIndex),
		esDao.Cli.Search.WithBody(strings.NewReader(queryText)))
	if err != nil {
		return nil, "", err
	}
	defer res.Body.Close()
	var result ESResp
	bs, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, "", err
	}

	err = json.Unmarshal(bs, &result)
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchItemByContent json Unmarshal response body byte length(%v),bs:(%s) result(%v) err(%v)",
			len(bs), bs, result, err)
		return nil, "", err
	}

	total := len(result.Hits.Hits)
	if total == 0 {
		return nil, "", nil
	}

	resRoles := make([]EsRole, 0, len(result.Hits.Hits))
	for _, v := range result.Hits.Hits {
		resRoles = append(resRoles, v.Source)
	}

	newLastId := ""
	if len(resRoles) > 0 {
		lastSort := result.Hits.Hits[len(result.Hits.Hits)-1].Sort
		newLastId = encodeLastId(lastSort)
	}
	// 如果搜索结果大于200条，打印日志，看看有没有返回超过200个角色的情况
	if result.Total.Value > 200 {
		log.WarnWithCtx(ctx, "SearchItemByContent content:%s total:%d", content, result.Total.Value)
	}
	return resRoles, newLastId, nil
}

// 使用script_score对搜索结果进行打分
func genFilterQueryByScriptScore(content, lastId string, pageSize int) string {
	var searchAfter string
	sortStr := decodeSearchAfter(lastId, 2)
	if sortStr != "" {
		searchAfter = fmt.Sprintf("\"search_after\":%s,", sortStr)
	}
	queryStr := `{
	"query": {
		"bool": {
			"filter": [{
				"term": {
					"state": 1
				}
			}, {
				"term": {
					"exposed": true
				}
			}, {
				"terms": {
					"type": [0, 1]
				}
			}, {
				"multi_match": {
					"type": "phrase",
					"query": "` + content + `",
					"fields": ["name", "character", "tags"]
				}
			}],
			"must": {
				"function_score": {
					"query": {
						"match_all": {}
					},
					"functions": [{
						"filter": {
							"term": {
								"name.keyword": "` + content + `"
							}
						},
						"script_score": {
			                "script" : {
			                  "source": "100+Math.log10(doc['user_likes'].value + 1)"
			                }
			            }
					}, {
						"filter": {
							"match_phrase": {
								"name": "` + content + `"
							}
						},
						"script_score": {
			                "script" : {
			                  "source": "90+Math.log10(doc['user_likes'].value + 1)"
			                }
			            }
					}, {
						"filter": {
							"term": {
								"character.keyword": "` + content + `"
							}
						},
						"script_score": {
			                "script" : {
			                  "source": "80+Math.log10(doc['user_likes'].value + 1)"
			                }
			            }
					}, {
						"filter": {
							"match_phrase": {
								"character": "` + content + `"
							}
						},
						"script_score": {
			                "script" : {
			                  "source": "70+Math.log10(doc['user_likes'].value + 1)"
			                }
			            }
					}, {
						"filter": {
							"term": {
								"tags.keyword": "` + content + `"
							}
						},
						"script_score": {
			                "script" : {
			                  "source": "60+Math.log10(doc['user_likes'].value + 1)"
			                }
			            }
					}, {
						"filter": {
							"match_phrase": {
								"tags": "` + content + `"
							}
						},
						"script_score": {
			                "script" : {
			                  "source": "50+Math.log10(doc['user_likes'].value + 1)"
			                }
			            }
					}],
					"score_mode": "max",
					"boost_mode": "replace"
				}
			}
		}
	},
	"sort":["_score", {"_id": "desc"}],
	"size":` + strconv.Itoa(pageSize) + `,
	` + searchAfter + `
	"_source":["role_id","entity_type"]
}`
	return queryStr
}
