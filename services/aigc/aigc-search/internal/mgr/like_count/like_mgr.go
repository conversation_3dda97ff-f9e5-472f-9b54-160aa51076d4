package like_count

import (
	"context"
	"github.com/elastic/go-elasticsearch/v7"
	mongo2 "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mongo"
	"go.mongodb.org/mongo-driver/bson"
	"golang.52tt.com/pkg/log"
	aigc_common "golang.52tt.com/protocol/services/aigc/aigc-common"
	searchPb "golang.52tt.com/protocol/services/aigc/aigc-search"
	"golang.52tt.com/services/aigc/aigc-search/internal/mgr/esdao"
	"golang.52tt.com/services/aigc/aigc-search/internal/mgr/like_count/entity"
	"golang.52tt.com/services/aigc/aigc-search/internal/mgr/like_count/store"
	"strconv"
	"time"
)

type LikeCountManager struct {
	store *store.LikeCountStore
	esDao *esdao.EsDao
}

func NewLikeCountManager(database string, mongoClient *mongo2.ClientImpl, esCli *elasticsearch.Client) (*LikeCountManager, error) {
	mongoStore := store.NewLikeCountStore(database, mongoClient)
	esDao := esdao.NewEsDao(esCli)
	mgr := &LikeCountManager{
		esDao: esDao,
		store: mongoStore,
	}
	return mgr, nil
}

func (m *LikeCountManager) WatchLikeCountChange(ctx context.Context) error {
	t := time.Now()
	log.InfoWithCtx(ctx, "WatchLikeCountChange start")
	defer func() {
		log.InfoWithCtx(ctx, "WatchLikeCountChange end, cost:%d", time.Since(t).String())
	}()
	changeStream, err := m.store.LikeCountChangeWatch(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "WatchLikeCountChange LikeCountChangeWatch err: %v", err)
		return err
	}
	defer changeStream.Close(ctx)

	// 监听变更
	for changeStream.Next(ctx) {
		var changeStreamInfo bson.M
		if err := changeStream.Decode(&changeStreamInfo); err != nil {
			log.ErrorWithCtx(ctx, "解码 Change Stream 失败: %v", err)
			break
		}
		if v, ok := changeStreamInfo["operationType"].(string); ok && v == "delete" {
			id := uint32(changeStreamInfo["documentKey"].(bson.M)["_id"].(int64))
			log.InfoWithCtx(ctx, "WatchLikeCountChange delete id(%d)", id)
			continue
		}

		data, ok := changeStreamInfo["fullDocument"].(bson.M)
		if !ok {
			log.ErrorWithCtx(ctx, "WatchLikeCountChange Change Stream 没有 fullDocument 字段, changeStreamInfo:%+v", changeStreamInfo)
			continue
		}
		log.DebugWithCtx(ctx, "WatchLikeCountChange Change Stream: %v", data)

		likeCount := new(entity.ObjetLikeCount)
		if err := bsonTransMapToStruct(data, likeCount); err != nil {
			log.ErrorWithCtx(ctx, "WatchLikeCountChange bsonTransMapToStruct err: %v, data:%+v", err, data)
			continue
		}
		log.InfoWithCtx(ctx, "WatchLikeCountChange role(%+v)", likeCount)
		roleId, err := strconv.Atoi(likeCount.ObjectId)
		if err != nil {
			log.ErrorWithCtx(ctx, "WatchLikeCountChange strconv.Atoi err: %v, likeCount", err, likeCount)
			continue
		}
		entityType := 0
		switch likeCount.ObjectType {
		case int(aigc_common.ObjectType_OBJECT_TYPE_GROUP_TEMPLATE):
			entityType = int(searchPb.AigcContentType_AIGC_CONTENT_TYPE_GROUP_TEMPLATE)
		default:
			log.ErrorWithCtx(ctx, "WatchLikeCountChange unknown ObjectType: %d", likeCount.ObjectType)
			continue
		}
		esRole := esdao.EsRole{
			RoleId:     uint32(roleId),
			UserLikes:  int(likeCount.Count),
			EntityType: entityType,
		}
		err = m.esDao.UpdateEsData(ctx, esRole)
		if err != nil {
			log.ErrorWithCtx(ctx, "WatchLikeCountChange es UpdateEsData role(%+v) err: %v", esRole, err)
			continue
		}
	}
	if err := changeStream.Err(); err != nil {
		log.ErrorWithCtx(ctx, "WatchLikeCountChange Change Stream 出错: %v", err)
		return err
	}
	return nil
}

func bsonTransMapToStruct(source bson.M, template *entity.ObjetLikeCount) error {
	jsonStr, err := bson.Marshal(source)
	if err != nil {
		return err
	}
	err = bson.Unmarshal(jsonStr, template)
	if err != nil {
		return err
	}
	return nil
}
