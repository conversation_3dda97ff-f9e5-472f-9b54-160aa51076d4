package store

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mongo"
	mongo_driver "go.mongodb.org/mongo-driver/mongo"
)

type LikeCountStore struct {
	client *mongo.ClientImpl

	likeCol *mongo_driver.Collection
}

func NewLikeCountStore(database string, client *mongo.ClientImpl) *LikeCountStore {
	c := &LikeCountStore{
		client: client,
	}

	c.likeCol = client.Database(database).Collection("attitude_count")

	return c
}

func (s *LikeCountStore) Close(ctx context.Context) error {
	return s.client.Close(ctx)
}
