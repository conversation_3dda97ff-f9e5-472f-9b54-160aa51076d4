package store

import (
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.52tt.com/pkg/log"
)

func (s *LikeCountStore) LikeCountChangeWatch(ctx context.Context) (*mongo.ChangeStream, error) {
	// 设置 Change Stream 选项
	pipeline := mongo.Pipeline{
		bson.D{
			{Key: "$match", Value: bson.D{
				{Key: "operationType", Value: bson.D{
					{Key: "$in", Value: bson.A{"update", "insert", "delete", "replace"}},
				}},
			}},
		},
	}
	opts := options.ChangeStream().SetFullDocument(options.UpdateLookup)

	// 创建 Change Stream
	changeStream, err := s.likeCol.Watch(ctx, pipeline, opts)
	if err != nil {
		log.ErrorWithCtx(ctx, "创建 Change Stream 失败: %v", err)
		return nil, err
	}
	return changeStream, nil
}
