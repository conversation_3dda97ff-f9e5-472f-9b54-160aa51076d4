package internal

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mongo"
	"golang.52tt.com/pkg/config"
	pb "golang.52tt.com/protocol/services/aigc/aigc-search"
	"golang.52tt.com/protocol/services/demo/echo"
	"golang.52tt.com/services/aigc/aigc-search/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-search/internal/mgr/group"
	"golang.52tt.com/services/aigc/aigc-search/internal/mgr/like_count"
	"golang.52tt.com/services/aigc/aigc-search/internal/mgr/role"
	"golang.52tt.com/services/aigc/aigc-search/internal/timer"
)

type StartConfig struct {
	// from config file
	RoleMongo      *config.MongoConfig         `json:"role_mongo"`
	GroupMongo     *config.MongoConfig         `json:"group_mongo"`
	LikeCountMongo *config.MongoConfig         `json:"like_count_mongo"`
	EsConfig       *config.ElasticSearchConfig `json:"es_config"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	roleMongoCli, err := mongo.NewClient(ctx, cfg.RoleMongo.OptionsForPrimaryPreferred())
	if err != nil {
		log.ErrorWithCtx(ctx, "init mongo fail, err: %v", err)
		return nil, err
	}

	groupMongoCli, err := mongo.NewClient(ctx, cfg.GroupMongo.OptionsForPrimaryPreferred())
	if err != nil {
		log.ErrorWithCtx(ctx, "init mongo fail, err: %v", err)
		return nil, err
	}

	likeCountMongoCli, err := mongo.NewClient(ctx, cfg.LikeCountMongo.OptionsForPrimaryPreferred())
	if err != nil {
		log.ErrorWithCtx(ctx, "init mongo fail, err: %v", err)
		return nil, err
	}

	esCli, err := cfg.EsConfig.NewDefaultV7Client()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewDefaultV7Client cfg(%+v) err: %v", cfg.EsConfig, err)
		return nil, err
	}

	esMgr, _ := mgr.NewManager(esCli)
	s := &Server{
		esMgr: esMgr,
	}

	roleMgr, _ := role.NewRoleManager(cfg.RoleMongo.Database, roleMongoCli, esCli)

	templateGroupMgr, _ := group.NewGroupTemplateManager(cfg.GroupMongo.Database, groupMongoCli, esCli)

	likeCountMgr, _ := like_count.NewLikeCountManager(cfg.LikeCountMongo.Database, likeCountMongoCli, esCli)

	redisTimer, err := timer.NewRedisTimer(ctx, templateGroupMgr, roleMgr, likeCountMgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewRedisTimer err(%v)", err)
		return nil, err
	}
	err = redisTimer.Start()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer redisTimer.Start err(%v)", err)
		return nil, err
	}

	return s, nil
}

type Server struct {
	esMgr *mgr.Manager
}

func (s *Server) ShutDown() {

}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *Server) AigcContentSearch(ctx context.Context, req *pb.AigcContentSearchReq) (*pb.AigcContentSearchResp, error) {
	out := &pb.AigcContentSearchResp{}
	log.InfoWithCtx(ctx, "AigcContentSearch req:%s", req.String())

	roles, lastId, loadFinish, err := s.esMgr.SearchRoleByContent(ctx, req.GetContent(), req.GetLastId(), req.GetLimit())
	if err != nil {
		log.ErrorWithCtx(ctx, "AigcContentSearch SearchRoleByContent req:%s err:%+v", req.String(), err)
		return out, err
	}
	out.LoadFinish = loadFinish
	out.LastId = lastId
	if len(roles) == 0 {
		log.InfoWithCtx(ctx, "AigcContentSearch len(roles)=0 req:%s resp:%s", req.String(), out.String())
		return out, nil
	}
	out.Items = make([]*pb.AigcSearchItem, 0, len(roles))
	for _, v := range roles {
		out.Items = append(out.Items, &pb.AigcSearchItem{
			Id:   v.Id,
			Type: pb.AigcContentType(v.Type),
		})
	}
	log.InfoWithCtx(ctx, "AigcContentSearch len(roles)=%d req:%s resp:%s", len(roles), req.String(), out.String())
	return out, nil
}
