// Code generated by tyrgo cv generator

package main

import (
	"gitlab.ttyuyin.com/tyr/x/log"
	localstartup "golang.52tt.com/pkg/framework/startup"
	"golang.52tt.com/services/aigc/aigc-account-middle/server"
)

func main() {
	err := localstartup.New(aigcaccountmiddleserver.ServerName).
		AddServer(&aigcaccountmiddleserver.Server{}).
		Start()
	if err != nil {
		log.Errorf("startup failed: %v", err)
	}
}
