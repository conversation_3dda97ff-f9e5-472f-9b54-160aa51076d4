package internal

import (
	"context"
	middleware_event "gitlab.ttyuyin.com/tt-infra/middleware/event"
	config "golang.52tt.com/services/aigc/aigc-account-middle/internal/config/ttconfig/aigc_account_middle"
	"golang.52tt.com/services/aigc/aigc-account-middle/internal/event"
	"golang.52tt.com/services/aigc/aigc-account-middle/internal/event/eventlink"
	"golang.52tt.com/services/aigc/aigc-account-middle/internal/report"
	"golang.52tt.com/services/aigc/aigc-account-middle/internal/report/bylink"
	"net"
	"time"

	"gitlab.ttyuyin.com/tyr/x/log"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
	pb "golang.52tt.com/protocol/services/aigc/aigc-account-middle"
	"golang.52tt.com/services/aigc/aigc-account-middle/internal/assembler"
	"golang.52tt.com/services/aigc/aigc-account-middle/internal/mgr"
	account_mgr "golang.52tt.com/services/aigc/aigc-account-middle/internal/mgr/account"
	aiPost_mgr "golang.52tt.com/services/aigc/aigc-account-middle/internal/mgr/ai_post"
	"golang.52tt.com/services/aigc/aigc-account-middle/internal/rpc"
)

type StartConfig struct {
	// from config file
	EventLink *middleware_event.Options `json:"event_link"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	err := config.InitAigcAccountMiddleConfig()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer InitAigcAccountMiddleConfig err: %v", err)
		return nil, err
	}

	clients, err := rpc.NewClients()
	if err != nil {
		log.Errorf("init rpc clients failed: %v", err)
		return nil, err
	}

	reporter, err := bylink.NewBylinkReporter()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewBylinkReporter err: %v", err)
		return nil, err
	}

	aiAccountMgr := account_mgr.NewManager(clients, reporter)
	aiPostMgr := aiPost_mgr.NewManager(clients)
	eventBus, err := eventlink.NewEventBus(ctx, cfg.EventLink)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewEventBus cfg(%+v) err: %v", cfg, err)
		return nil, err
	}

	s := &Server{
		clients:      clients,
		aiAccountMgr: aiAccountMgr,
		eventBus:     eventBus,
		reporter:     reporter,
		aiPostMgr:    aiPostMgr,
	}
	if err := eventBus.Subscribe(ctx, event.SubNameCommImEvent, s.onCommImEvent); err != nil {
		log.ErrorWithCtx(ctx, "NewServer Subscribe name(%s) err: %v", event.SubNameCommImEvent, err)
		return nil, err
	}

	if err := eventBus.Subscribe(ctx, event.SubNamUgcFollowingUpdate, s.onUgcFollowingUpdate); err != nil {
		log.ErrorWithCtx(ctx, "NewServer Subscribe name(%s) err: %v", event.SubNamUgcFollowingUpdate, err)
		return nil, err
	}

	if err := eventBus.Subscribe(ctx, event.SubNameCybrosArbiterCallbackQuicksilver, s.handleCybrosArbiterCallbackEvent); err != nil {
		log.ErrorWithCtx(ctx, "NewServer Subscribe name(%s) err: %v", event.SubNameCybrosArbiterCallbackQuicksilver, err)
		return nil, err
	}

	if err := eventBus.Subscribe(ctx, event.SubNameAiPostCommentSub, s.handleAiCommentPostEvent); err != nil {
		log.ErrorWithCtx(ctx, "NewServer Subscribe name(%s) err: %v", event.SubNameAiPostCommentSub, err)
		return nil, err
	}

	return s, nil
}

type Server struct {
	clients      *rpc.Clients
	aiAccountMgr mgr.AIAccountManager
	aiPostMgr    mgr.AIPostManager

	eventBus event.EventBus
	reporter report.Reporter
}

func (s *Server) ShutDown() {
	if s.eventBus != nil {
		s.eventBus.Close()
	}
}

// CreateAIAccount 创建AI账号
func (s *Server) CreateAIAccount(ctx context.Context, req *pb.CreateAIAccountRequest) (*pb.CreateAIAccountResponse, error) {
	resp := &pb.CreateAIAccountResponse{}

	// 日志隐藏敏感信息
	logReq := *req
	if req.GetAccount() != nil {
		accountCopy := *req.GetAccount()
		accountCopy.Password = ""
		logReq.Account = &accountCopy
	}
	log.InfoWithCtx(ctx, "CreateAIAccount req: %+v", logReq)

	// 参数校验
	if err := s.validateCreateAIAccountRequest(ctx, req); err != nil {
		log.ErrorWithCtx(ctx, "CreateAIAccount validateCreateAIAccountRequest failed: %v", err)
		return resp, err
	}

	var err error
	resp.Id, err = s.aiAccountMgr.CreateAIAccount(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateAIAccount failed: %v", err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "CreateAIAccount success, id: %d", resp.GetId())
	return resp, nil
}

// validateCreateAIAccountRequest 创建AI账号请求参数校验
func (s *Server) validateCreateAIAccountRequest(ctx context.Context, req *pb.CreateAIAccountRequest) error {
	account := req.GetAccount()
	// 校验必填字段
	if len(account.GetPassword()) == 0 {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "密码不能为空")
	}
	if len(account.GetAvatar()) == 0 {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "头像不能为空")
	}
	if len(account.GetNickname()) == 0 {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "昵称不能为空")
	}
	//if account.GetPrologue() == "" {
	//	return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "开场白不能为空")
	//}
	//if account.GetPhone() == "" {
	//	return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "手机号不能为空")
	//}
	if len(account.GetIp()) > 0 {
		// 检查IP格式
		if net.ParseIP(account.GetIp()) == nil {
			log.ErrorWithCtx(ctx, "validateCreateAIAccountRequest invalid ip(%s)", account.GetIp())
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "IP格式不正确, 正确格式如: ***********")
		}
	}
	if len(account.GetBirthday()) > 0 {
		// 检查生日格式
		if _, err := time.Parse("2006-01-02", account.GetBirthday()); err != nil {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "生日格式不正确, 正确格式如: 2006-01-02")
		}
	}

	if len(account.GetPhone()) > 0 {
		uid, _, err := s.clients.Account.GetUidByPhone(ctx, account.GetPhone())
		if err != nil && err.Code() != status.ErrAccountNotExist {
			log.ErrorWithCtx(ctx, "validateCreateAIAccountRequest GetUidByPhone err: %v, phone: %s", err, account.GetPhone())
			return err
		}
		if uid != 0 {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "手机号已被注册")
		}
	}

	return nil
}

// UpdateAIAccount 更新AI账号
func (s *Server) UpdateAIAccount(ctx context.Context, req *pb.UpdateAIAccountRequest) (*pb.UpdateAIAccountResponse, error) {
	resp := &pb.UpdateAIAccountResponse{}

	// 日志隐藏敏感信息
	logReq := *req
	if req.GetAccount() != nil {
		accountCopy := *req.GetAccount()
		accountCopy.Password = ""
		logReq.Account = &accountCopy
	}
	log.InfoWithCtx(ctx, "UpdateAIAccount req: %+v", logReq)

	if req.GetUpdateFlags() == nil || len(req.GetUpdateFlags()) == 0 {
		return resp, nil
	}

	// 参数校验
	if err := s.validateUpdateAIAccountRequest(ctx, req); err != nil {
		log.ErrorWithCtx(ctx, "UpdateAIAccount validateUpdateAIAccountRequest failed: %v", err)
		return resp, err
	}

	err := s.aiAccountMgr.UpdateAIAccount(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAIAccount failed: %v", err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "UpdateAIAccount success, id: %d", req.GetAccount().GetUid())
	return resp, nil
}

// validateUpdateAIAccountRequest 更新AI账号请求参数校验
func (s *Server) validateUpdateAIAccountRequest(ctx context.Context, req *pb.UpdateAIAccountRequest) error {
	account := req.GetAccount()
	if account.GetUid() == 0 {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "id不能为空")
	}

	if isFieldNeedUpdate(req.GetUpdateFlags(), "nickname") && len(account.GetNickname()) == 0 {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "昵称不能为空")
	}

	if isFieldNeedUpdate(req.GetUpdateFlags(), "avatar") && len(account.GetAvatar()) == 0 {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "头像不能为空")
	}

	//if isFieldNeedUpdate(req.GetUpdateFlags(), "prologue") && len(account.GetPrologue()) == 0 {
	//	return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "开场白不能为空")
	//}

	if isFieldNeedUpdate(req.GetUpdateFlags(), "birthday") && len(account.GetBirthday()) > 0 {
		if _, err := time.Parse("2006-01-02", account.GetBirthday()); err != nil {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "生日格式不正确, 正确格式如: 2006-01-02")
		}
	}

	if isFieldNeedUpdate(req.GetUpdateFlags(), "ip") && len(account.GetIp()) > 0 {
		if net.ParseIP(account.GetIp()) == nil {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "IP格式不正确, 正确格式如: ***********")
		}
	}

	if isFieldNeedUpdate(req.GetUpdateFlags(), "phone") && len(account.GetPhone()) > 0 {
		uid, _, err := s.clients.Account.GetUidByPhone(ctx, account.GetPhone())
		if err != nil && err.Code() != status.ErrAccountNotExist {
			log.ErrorWithCtx(ctx, "validateCreateAIAccountRequest GetUidByPhone err: %v, phone: %s", err, account.GetPhone())
			return err
		}
		if uid != 0 {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "手机号已被注册")
		}
	}

	return nil
}

// isFieldNeedUpdate 判断字段是否需要更新
func isFieldNeedUpdate(updateFlags map[string]bool, field string) bool {
	if updateFlags == nil {
		return false
	}
	return updateFlags[field]
}

// UnregisterAIAccount 注销AI账号
func (s *Server) UnregisterAIAccount(ctx context.Context, req *pb.UnregisterAIAccountRequest) (*pb.UnregisterAIAccountResponse, error) {
	resp := &pb.UnregisterAIAccountResponse{}
	log.InfoWithCtx(ctx, "UnregisterAIAccount req: %+v", req)

	// 参数校验
	if req.GetUid() == 0 {
		log.WarnWithCtx(ctx, "UnregisterAIAccount req.Uid is 0")
		return resp, nil
	}

	err := s.aiAccountMgr.UnregisterAIAccount(ctx, req.GetUid(), req.GetTtid())
	if err != nil {
		log.ErrorWithCtx(ctx, "UnregisterAIAccount failed: %v", err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "UnregisterAIAccount success, uid: %d", req.GetUid())
	return resp, nil
}

// GetPageAIAccount 获取AI账号列表（运营后台）
func (s *Server) GetPageAIAccount(ctx context.Context, req *pb.GetPageAIAccountRequest) (*pb.GetPageAIAccountResponse, error) {
	resp := &pb.GetPageAIAccountResponse{}
	log.InfoWithCtx(ctx, "GetPageAIAccount req: %+v", req)

	aiAccountResp, err := s.clients.AigcAccount.GetPageAIAccount(ctx, &aigc_account.GetPageAIAccountRequest{
		Page:                   req.GetPage(),
		Size:                   req.GetSize(),
		ChatCardWallShowStatus: req.GetChatCardWallShowStatus(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPageAIAccount GetPageAIAccount err: %v, req: %+v", err, req)
		return resp, err
	}

	// 组装AI账号列表
	aiAccountAssembler := assembler.NewAIAccountInfoAssembler(s.clients)
	aiAccountList, err := aiAccountAssembler.AssembleAIAccountInfoList(ctx, aiAccountResp.GetAccountList())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPageAIAccount AssembleAIAccountInfoList err: %v, req: %+v", err, req)
		return resp, err
	}

	resp.AccountList = aiAccountList
	resp.Total = aiAccountResp.GetTotal()

	log.InfoWithCtx(ctx, "GetPageAIAccount success, req(%+v) len(resp.AccountList): %d, total: %d", req, len(resp.AccountList), resp.Total)
	return resp, nil
}

// GetAIAccount 根据uid获取AI账号
func (s *Server) GetAIAccount(ctx context.Context, req *pb.GetAIAccountRequest) (*pb.GetAIAccountResponse, error) {
	resp := &pb.GetAIAccountResponse{}
	log.InfoWithCtx(ctx, "GetAIAccount req: %+v", req)

	if req.GetUid() == 0 {
		log.WarnWithCtx(ctx, "GetAIAccount req.Uid is 0")
		return resp, nil
	}

	aiAccountResp, err := s.clients.AigcAccount.GetAIAccount(ctx, &aigc_account.GetAIAccountRequest{
		Uid:       req.GetUid(),
		ReqSource: req.GetReqSource(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAIAccount GetAIAccount err: %v, req: %+v", err, req)
		return resp, err
	}

	// 组装AI账号信息
	aiAccountAssembler := assembler.NewAIAccountInfoAssembler(s.clients)
	aiAccount, err := aiAccountAssembler.AssembleAIAccountInfo(ctx, aiAccountResp.GetAccount())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAIAccount AssembleAIAccountInfo err: %v, req: %+v", err, req)
		return resp, err
	}

	resp.Account = aiAccount

	// 日志隐藏敏感信息
	logResp := *resp
	if resp.GetAccount() != nil {
		accountCopy := *resp.GetAccount()
		accountCopy.Password = ""
		logResp.Account = &accountCopy
	}
	log.InfoWithCtx(ctx, "GetAIAccount success, req(%+v), resp(%+v)", req, logResp)
	return resp, nil
}

// BatchGetAIAccount 批量获取AI账号
func (s *Server) BatchGetAIAccount(ctx context.Context, req *pb.BatchGetAIAccountRequest) (*pb.BatchGetAIAccountResponse, error) {
	resp := &pb.BatchGetAIAccountResponse{}
	log.InfoWithCtx(ctx, "BatchGetAIAccount req: %+v", req)

	if len(req.GetUidList()) == 0 {
		log.WarnWithCtx(ctx, "BatchGetAIAccount req.UidList is empty")
		return resp, nil
	}

	aiAccountResp, err := s.clients.AigcAccount.BatchGetAIAccount(ctx, &aigc_account.BatchGetAIAccountRequest{
		UidList: req.GetUidList(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetAIAccount BatchGetAIAccount err: %v, req: %+v", err, req)
		return resp, err
	}

	// 组装AI账号列表
	aiAccountAssembler := assembler.NewAIAccountInfoAssembler(s.clients)
	aiAccountList, err := aiAccountAssembler.AssembleAIAccountInfoList(ctx, aiAccountResp.GetAccountList())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetAIAccount AssembleAIAccountInfoList err: %v, req: %+v", err, req)
		return resp, err
	}

	resp.AccountList = aiAccountList

	log.InfoWithCtx(ctx, "BatchGetAIAccount success, req(%+v), len(resp.AccountList): %d", req, len(resp.GetAccountList()))
	return resp, nil
}
