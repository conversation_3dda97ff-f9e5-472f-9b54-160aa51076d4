package internal

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	aigcAccountpb "golang.52tt.com/protocol/services/aigc/aigc-account"
	pb "golang.52tt.com/protocol/services/aigc/aigc-account-middle"
)

func (s *Server) AddAiPost(ctx context.Context, req *pb.AddAiPostReq) (resp *pb.AddAiPostResp, err error) {
	resp = &pb.AddAiPostResp{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "AddAiPost req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "AddAiPost req:%+v,resp:%+v", req, resp)
		}
	}()
	if req.GetUid() == 0 || len(req.GetPostId()) == 0 {
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "参数错误")
	}
	_, err = s.clients.AigcAccount.AddAiPost(ctx, &aigcAccountpb.AddAiPostRequest{
		Uid:    req.GetUid(),
		PostId: req.GetPostId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AddAiPost AddAiPost userId:%d postId:%s error: %v", req.GetUid(), req.GetPostId(), err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "添加失败")
	}
	return
}

func (s *Server) GetAiPost(ctx context.Context, req *pb.GetAiPostReq) (resp *pb.GetAiPostResp, err error) {
	resp = &pb.GetAiPostResp{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAiPost req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "GetAiPost req:%+v,resp:%+v", req, resp)
		}
	}()
	if req.GetPage() == 0 {
		req.Page = 1
	}
	if req.GetSize() == 0 {
		req.Size = 20
	}

	list, total, err := s.aiPostMgr.BatchGetAiPost(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAiPost GetAiPostList error: %v", err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "获取失败")
	}

	if total == 0 {
		return resp, nil
	}
	resp.TotalNum = total
	resp.PostList = list

	return
}
