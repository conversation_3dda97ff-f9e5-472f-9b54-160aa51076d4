// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-account-middle/internal/report (interfaces: Reporter)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	report "golang.52tt.com/services/aigc/aigc-account-middle/internal/report"
)

// MockReporter is a mock of Reporter interface.
type MockReporter struct {
	ctrl     *gomock.Controller
	recorder *MockReporterMockRecorder
}

// MockReporterMockRecorder is the mock recorder for MockReporter.
type MockReporterMockRecorder struct {
	mock *MockReporter
}

// NewMockReporter creates a new mock instance.
func NewMockReporter(ctrl *gomock.Controller) *MockReporter {
	mock := &MockReporter{ctrl: ctrl}
	mock.recorder = &MockReporterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReporter) EXPECT() *MockReporterMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockReporter) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockReporterMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockReporter)(nil).Close))
}

// ReportAIAccountInfo mocks base method.
func (m *MockReporter) ReportAIAccountInfo(arg0 context.Context, arg1 report.AIAccountData) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ReportAIAccountInfo", arg0, arg1)
}

// ReportAIAccountInfo indicates an expected call of ReportAIAccountInfo.
func (mr *MockReporterMockRecorder) ReportAIAccountInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportAIAccountInfo", reflect.TypeOf((*MockReporter)(nil).ReportAIAccountInfo), arg0, arg1)
}

// ReportMsg mocks base method.
func (m *MockReporter) ReportMsg(arg0 context.Context, arg1 report.AIMsgReportData) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ReportMsg", arg0, arg1)
}

// ReportMsg indicates an expected call of ReportMsg.
func (mr *MockReporterMockRecorder) ReportMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportMsg", reflect.TypeOf((*MockReporter)(nil).ReportMsg), arg0, arg1)
}
