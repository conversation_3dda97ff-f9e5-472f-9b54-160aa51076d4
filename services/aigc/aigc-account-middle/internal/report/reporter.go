package report

import (
	"context"
	account_go "golang.52tt.com/clients/account-go"
)

const (
	OpTypeCreate = 1 // 创建AI账号
	OpTypeUpdate = 2 // 更新AI账号

	SysBussType               = "系统消息"
	UserRelyBussType          = "用户回复"
	SeeByMyselfMsgType        = "用户自见"
	SeeByMyselfReplyMsgType   = "用户自见"
	MsgAfterLimitReplyMsgType = "到达上限"
	MsgAfterLimitMsgType      = "到达上限"
	PrologueReplyMsgType      = "开场白"
)

type (
	AIAccountData struct {
		Uid       uint32
		Nickname  string
		TTID      string
		OpType    uint32 // 1:创建, 2:修改
		Signature string
		Password  string
		Sex       uint32
		Avatar    string
	}
)

type AIMsgReportData struct {
	Uid              uint32
	Content          string
	RespTime         uint32
	MessageId        uint32
	Extra            string
	AIAccount        *account_go.User
	AigcBussType     string
	AigcMsgType      string
	AigcReplyMsgType string

	ReplyMsgTs uint32
	ReplyMsgId uint32
	ReplyMsgContent string
}

//go:generate mockgen -destination=mocks/reporter.go -package=mocks golang.52tt.com/services/aigc/aigc-account-middle/internal/report Reporter
type Reporter interface {
	Close()

	ReportAIAccountInfo(ctx context.Context, data AIAccountData)
	ReportMsg(ctx context.Context, data AIMsgReportData)
}
