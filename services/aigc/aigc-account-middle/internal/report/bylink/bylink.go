package bylink

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"golang.52tt.com/services/aigc/aigc-account-middle/internal/report"

	"golang.52tt.com/pkg/bylink"
)

type bylinkReporter struct {
	collector bylink.Bylink
}

func NewBylinkReporter() (report.Reporter, error) {
	collector, err := bylink.NewKfkCollector()
	if err != nil {
		return nil, err
	}

	bylink.InitGlobalCollector(collector)

	return &bylinkReporter{
		collector: collector,
	}, nil
}

func (r *bylinkReporter) Close() {
	r.collector.Flush()
	r.collector.Close()
}

func (r *bylinkReporter) ReportAIAccountInfo(ctx context.Context, data report.AIAccountData) {
	dataMap := map[string]any{
		"uid":       data.Uid,
		"nick_name": data.Nickname,
		"ttid":      data.TTID,
		"op_type":   data.OpType,
		"signature": data.Signature,
		"password":  data.Password,
		"sex":       data.Sex,
		"headshot":  data.Avatar,
	}

	err := bylink.Track(ctx, uint64(data.Uid), "aigc_ai_user_create_log", dataMap, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportAIAccountInfo Track error: %v, data: %+v", err, dataMap)
		return
	}

	log.InfoWithCtx(ctx, "ReportAIAccountInfo success, data: %+v", dataMap)
}

func (r *bylinkReporter) ReportMsg(ctx context.Context, data report.AIMsgReportData) {
	if data.AIAccount == nil {
		log.WarnWithCtx(ctx, "ReportMsg ReportMsg data is nil data: %+v", data)
	}
	dataMap := map[string]any{
		"uid":            data.Uid,
		"content":        data.Content,
		"session_id":     fmt.Sprintf("session_%d_%d", data.Uid, data.AIAccount.GetUid()),
		"resp_time":      data.RespTime,
		"message_id":     data.MessageId,
		"extra":          data.Extra,
		"account_id":     data.AIAccount.GetUid(),
		"account_name":   data.AIAccount.GetUsername(),
		"buss_type":      data.AigcBussType,
		"user_msg_type":  data.AigcMsgType,
		"reply_msg_type": data.AigcReplyMsgType,

		"reply_msg_ts":      data.ReplyMsgTs,
		"reply_msg_id":      data.ReplyMsgId,
		"reply_msg_content": data.ReplyMsgContent,
	}

	err := bylink.Track(ctx, uint64(data.Uid), "aigc_ai_account_msg_data_report", dataMap, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportMsg Track error: %v, data: %+v", err, dataMap)
		return
	}

	log.InfoWithCtx(ctx, "ReportMsg success, data: %+v", dataMap)
}
