package assembler

import (
	"context"
	"testing"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/services/helper-from-cpp/albumhelper"

	"golang.52tt.com/protocol/services/expsvr"

	"github.com/golang/mock/gomock"

	account_go "golang.52tt.com/protocol/services/account-go"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"

	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/photo_album_go"
	mock_account_go "golang.52tt.com/clients/mocks/account-go"
	mock_expsvr "golang.52tt.com/clients/mocks/expsvr"
	mock_user_tag_go "golang.52tt.com/clients/mocks/user-tag-go"

	avatar_pb "golang.52tt.com/protocol/services/avatar"
	"golang.52tt.com/services/aigc/aigc-account-middle/internal/rpc"
)

func setupMockClients(ctrl *gomock.Controller) *rpc.Clients {
	clients := &rpc.Clients{}
	clients.AccountGo = mock_account_go.NewMockIClient(ctrl)
	clients.UserTagGo = mock_user_tag_go.NewMockIClient(ctrl)
	clients.Exp = mock_expsvr.NewMockIClient(ctrl)
	clients.Avatar = avatar_pb.NewMockAvatarClient(ctrl)
	clients.PhotoAlbumGo = photo_album_go.NewMockPhotoAlbumGoClient(ctrl)
	return clients
}

func TestAIAccountInfoAssembler_MockFullChain_Normal(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	clients := setupMockClients(ctrl)

	mockAccountGo := clients.AccountGo.(*mock_account_go.MockIClient)
	mockUserTagGo := clients.UserTagGo.(*mock_user_tag_go.MockIClient)
	mockExp := clients.Exp.(*mock_expsvr.MockIClient)
	mockAvatar := clients.Avatar.(*avatar_pb.MockAvatarClient)
	mockPhotoAlbumGo := clients.PhotoAlbumGo.(*photo_album_go.MockPhotoAlbumGoClient)

	uid := uint32(1)
	uname := "u"
	alias := "a"
	phone := "p"
	nick := "n"
	sex := int32(1)
	sig := "s"
	userResp := &account_go.UserResp{
		Uid:       uid,
		Username:  uname,
		Alias:     alias,
		Phone:     phone,
		Nickname:  nick,
		Sex:       sex,
		Signature: sig,
	}
	mockAccountGo.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(map[uint32]*account_go.UserResp{uid: userResp}, nil)
	mockUserTagGo.EXPECT().BatGetUserTag(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
	mockExp.EXPECT().BatGetUserExp(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]*expsvr.UserExp{
		uid: {Level: 1, Exp: 100},
	}, nil).AnyTimes()
	mockAvatar.EXPECT().BatchGetAvatarVersionByAccount(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
	mockPhotoAlbumGo.EXPECT().BatchGetPhotoAlbum(gomock.Any(), gomock.Any()).Return(&photo_album_go.BatchGetPhotoAlbumResponse{
		PhotoAlbumMap: map[uint32]*photo_album_go.ImgKeyInfo{
			uid: {
				NewImgKeys: "key1;key2",
			},
		},
	}, nil).AnyTimes()

	dl := NewDataLoader(clients)
	dl.avatarMap[uid] = "mock-avatar-url"
	a := &AIAccountInfoAssembler{dataLoader: dl}

	ai := &aigc_account.AIAccount{
		Uid:        uid,
		Password:   "pwd",
		Ip:         "127.0.0.1",
		PromptId:   2,
		TimbreId:   3,
		RoleId:     4,
		Prologue:   "hi",
		CreateTime: 10,
		UpdateTime: 20,
	}
	info, err := a.AssembleAIAccountInfo(context.Background(), ai)
	if err != nil {
		t.Fatalf("unexpected err: %v", err)
	}
	if info == nil {
		t.Fatalf("info should not be nil")
	}
	if info.Uid != ai.Uid || info.Password != ai.Password || info.Ip != ai.Ip {
		t.Errorf("basic fields not match: got %+v", info)
	}
	if info.Ttid != alias || info.Phone != phone || info.Nickname != nick || info.Sex != uint32(sex) || info.Signature != sig {
		t.Errorf("user fields not match: got %+v", info)
	}
	if len(info.PhotoImgUrls) != 2 || info.PhotoImgUrls[0] != albumhelper.ToLongPhotoObsUrl("photo-album", "key1", 0, 0) || info.PhotoImgUrls[1] != albumhelper.ToLongPhotoObsUrl("photo-album", "key2", 0, 0) {
		t.Errorf("photo album urls not match: got %+v", info.PhotoImgUrls)
	}
}

func TestAIAccountInfoAssembler_MockFullChain_AccountGoError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	clients := setupMockClients(ctrl)
	mockAccountGo := clients.AccountGo.(*mock_account_go.MockIClient)
	mockUserTagGo := clients.UserTagGo.(*mock_user_tag_go.MockIClient)

	uid := uint32(1)
	mockErr := protocol.NewExactServerError(nil, 0, "mock error")
	mockAccountGo.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(nil, mockErr)
	mockUserTagGo.EXPECT().BatGetUserTag(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

	dl := NewDataLoader(clients)
	a := &AIAccountInfoAssembler{dataLoader: dl}

	ai := &aigc_account.AIAccount{Uid: uid}
	info, err := a.AssembleAIAccountInfo(context.Background(), ai)
	if err == nil {
		t.Errorf("expect error, got nil")
	}
	if info != nil {
		t.Errorf("expect nil info, got %+v", info)
	}
}

func TestAIAccountInfoAssembler_MockFullChain_AccountGoEmpty(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	clients := setupMockClients(ctrl)
	mockAccountGo := clients.AccountGo.(*mock_account_go.MockIClient)
	mockUserTagGo := clients.UserTagGo.(*mock_user_tag_go.MockIClient)

	uid := uint32(1)
	mockAccountGo.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(map[uint32]*account_go.UserResp{}, nil)
	mockUserTagGo.EXPECT().BatGetUserTag(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

	dl := NewDataLoader(clients)
	a := &AIAccountInfoAssembler{dataLoader: dl}

	ai := &aigc_account.AIAccount{Uid: uid}
	info, err := a.AssembleAIAccountInfo(context.Background(), ai)
	if err != nil {
		t.Errorf("expect nil error, got %v", err)
	}
	if info != nil {
		t.Errorf("expect nil info, got %+v", info)
	}
}
