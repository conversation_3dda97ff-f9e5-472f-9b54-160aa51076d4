package assembler

import (
	"context"
	"strings"

	"golang.52tt.com/pkg/iplocation"
	"golang.52tt.com/protocol/services/avatar"
	"golang.52tt.com/services/aigc/aigc-account-middle/internal/rpc"
	"golang.52tt.com/services/helper-from-cpp/albumhelper"

	avatar_pkg "gitlab.ttyuyin.com/bizFund/bizFund/pkg/avatar"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/photo_album_go"
	account_go "golang.52tt.com/clients/account-go"
	user_tag_v2 "golang.52tt.com/protocol/app/user-tag-v2"

	"gitlab.ttyuyin.com/tyr/x/log"
)

// DataLoader 数据加载器
type DataLoader struct {
	clients *rpc.Clients

	// key: uid
	userMap          map[uint32]*account_go.User // 用户信息
	avatarMap        map[uint32]string           // 用户头像URL
	birthdayMap      map[uint32]string           // 生日标签信息
	photoAlbumUrlMap map[uint32][]string         // 相册图片URL
	expLevelMap      map[uint32]uint32           // 等级

	ipLocationMap map[string]iplocation.Location // IP地址对应的位置
}

func NewDataLoader(clients *rpc.Clients) *DataLoader {
	return &DataLoader{
		clients:          clients,
		userMap:          make(map[uint32]*account_go.User),
		avatarMap:        make(map[uint32]string),
		birthdayMap:      make(map[uint32]string),
		photoAlbumUrlMap: make(map[uint32][]string),
		expLevelMap:      make(map[uint32]uint32),
		ipLocationMap:    make(map[string]iplocation.Location),
	}
}

// LoadUsersInfo 加载用户基本信息
func (dl *DataLoader) LoadUsersInfo(ctx context.Context, uidList []uint32) ([]uint32, error) {
	if len(uidList) == 0 {
		return nil, nil
	}

	userMap, err := dl.clients.AccountGo.GetUsersMap(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "LoadUsersInfo GetUsersMap err: %v, uidList: %v", err, uidList)
		return nil, err
	}

	validUidList := make([]uint32, 0, len(userMap))
	for uid, userInfo := range userMap {
		if userInfo.GetIsUnregister() {
			log.WarnWithCtx(ctx, "LoadUsersInfo user %d is unregistered", uid)
			continue
		}
		dl.userMap[uid] = userInfo
		validUidList = append(validUidList, uid)
	}

	return validUidList, err
}

// LoadAvatars 加载用户头像
func (dl *DataLoader) LoadAvatars(ctx context.Context) error {
	if len(dl.userMap) == 0 {
		return nil
	}

	usernameList := make([]string, 0, len(dl.userMap))
	usernameToUidMap := make(map[string]uint32)
	for uid, user := range dl.userMap {
		if user != nil {
			username := user.GetUsername()
			usernameList = append(usernameList, username)
			usernameToUidMap[username] = uid
		}
	}

	if len(usernameList) == 0 {
		return nil
	}

	// 获取头像版本
	avatarVersions, err := dl.clients.Avatar.BatchGetAvatarVersionByAccount(ctx, &avatar.BatchGetAvatarVersionByAccountReq{
		AccountList: usernameList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "LoadAvatars BatchGetAvatarVersionByAccount err: %v, usernameList: %v", err, usernameList)
		return err
	}

	for _, avatarVersion := range avatarVersions.GetVersionList() {
		uid := usernameToUidMap[avatarVersion.GetAccount()]
		dl.avatarMap[uid] = avatar_pkg.GetAvatarUrl(ctx, avatarVersion.GetAccount(), avatarVersion.GetVersion(), 0, 0)
	}

	for uid, userInfo := range dl.userMap {
		if avatarUrl, ok := dl.avatarMap[uid]; avatarUrl == "" || !ok {
			// 如果没有头像URL，则展示默认头像
			dl.avatarMap[uid] = avatar_pkg.GetAvatarUrl(ctx, userInfo.GetUsername(), "", 0, 0)
		}
	}

	return nil
}

// LoadBirthdayTags 加载用户生日标签
func (dl *DataLoader) LoadBirthdayTags(ctx context.Context, uidList []uint32) error {
	if len(uidList) == 0 {
		return nil
	}

	userTags, err := dl.clients.UserTagGo.BatGetUserTag(ctx, uidList, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "LoadBirthdayTags BatGetUserTag err: %v, uidList: %v", err, uidList)
		return err
	}

	for _, userTag := range userTags {
		for _, tagItem := range userTag.GetTagList() {
			if tagItem.GetTagType() == uint32(user_tag_v2.UserTagTypeV2_TAG_TYPE_BIRTHDAY) {
				dl.birthdayMap[userTag.GetUid()] = tagItem.GetTagName()
			}
		}
	}

	return nil
}

// LoadPhotoAlbums 加载用户相册
func (dl *DataLoader) LoadPhotoAlbums(ctx context.Context, uidList []uint32) error {
	if len(uidList) == 0 {
		return nil
	}

	photoAlbumResp, err := dl.clients.PhotoAlbumGo.BatchGetPhotoAlbum(ctx, &photo_album_go.BatchGetPhotoAlbumRequest{
		UidList: uidList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "LoadPhotoAlbums BatchGetPhotoAlbum err: %v, uidList: %v", err, uidList)
		return err
	}

	for uid, imgKeyInfo := range photoAlbumResp.GetPhotoAlbumMap() {
		imgKeyList := strings.Split(imgKeyInfo.GetNewImgKeys(), ";")
		dl.photoAlbumUrlMap[uid] = make([]string, 0, len(imgKeyList))
		for _, imgKey := range imgKeyList {
			if imgKey == "" {
				continue
			}
			// 生成相册图片的URL
			dl.photoAlbumUrlMap[uid] = append(dl.photoAlbumUrlMap[uid], albumhelper.ToLongPhotoObsUrl("photo-album", imgKey, 0, 0))
		}
	}

	return nil
}

// LoadExpLevels 加载用户经验值信息
func (dl *DataLoader) LoadExpLevels(ctx context.Context, uidList []uint32) error {
	if len(uidList) == 0 {
		return nil
	}

	expInfos, err := dl.clients.Exp.BatGetUserExp(ctx, 0, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "LoadExpLevels BatGetUserExp err: %v, uidList: %v", err, uidList)
		return err
	}

	for uid, expInfo := range expInfos {
		dl.expLevelMap[uid] = expInfo.GetLevel()
	}

	return nil
}

// LoadIpLocations 加载IP位置信息
func (dl *DataLoader) LoadIpLocations(ctx context.Context, ipList []string) error {
	if len(ipList) == 0 {
		return nil
	}

	ipLocationMap, err := iplocation.BatchGetIpLocation(ctx, ipList)
	if err != nil {
		log.ErrorWithCtx(ctx, "LoadIpLocations BatchGetIpLocation err: %v, ipList: %v", err, ipList)
		return err
	}

	dl.ipLocationMap = ipLocationMap

	return nil
}

// GetUser 获取用户信息
func (dl *DataLoader) GetUser(uid uint32) *account_go.User {
	return dl.userMap[uid]
}

// GetAvatar 获取用户头像URL
func (dl *DataLoader) GetAvatar(uid uint32) string {
	return dl.avatarMap[uid]
}

// GetBirthdayTag 获取用户生日标签
func (dl *DataLoader) GetBirthdayTag(uid uint32) string {
	return dl.birthdayMap[uid]
}

// GetPhotoAlbumUrls 获取用户相册图片URL列表
func (dl *DataLoader) GetPhotoAlbumUrls(uid uint32) []string {
	return dl.photoAlbumUrlMap[uid]
}

// GetExpLevel 获取用户等级
func (dl *DataLoader) GetExpLevel(uid uint32) uint32 {
	return dl.expLevelMap[uid]
}

// GetIpLocation 获取IP位置
func (dl *DataLoader) GetIpLocation(ip string) iplocation.Location {
	return dl.ipLocationMap[ip]
}
