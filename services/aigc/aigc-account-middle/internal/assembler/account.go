package assembler

import (
	"context"

	"golang.52tt.com/services/aigc/aigc-account-middle/internal/rpc"

	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
	pb "golang.52tt.com/protocol/services/aigc/aigc-account-middle"

	"gitlab.ttyuyin.com/tyr/x/log"
	"golang.org/x/sync/errgroup"
)

type AIAccountInfoAssembler struct {
	dataLoader *DataLoader
}

// NewAIAccountInfoAssembler 创建AI账号信息组装器
func NewAIAccountInfoAssembler(clients *rpc.Clients) *AIAccountInfoAssembler {
	return &AIAccountInfoAssembler{
		dataLoader: NewDataLoader(clients),
	}
}

type AIAccountList []*aigc_account.AIAccount

// 从AIAccountList中获取uid列表
func (l AIAccountList) GetUidList() []uint32 {
	uidList := make([]uint32, 0, len(l))
	for _, aiAccount := range l {
		if aiAccount != nil {
			uidList = append(uidList, aiAccount.GetUid())
		}
	}
	return uidList
}

// 从AIAccountList中获取ip列表
func (l AIAccountList) GetIpList() []string {
	ipList := make([]string, 0, len(l))
	for _, aiAccount := range l {
		if aiAccount != nil && aiAccount.GetIp() != "" {
			ipList = append(ipList, aiAccount.GetIp())
		}
	}
	return ipList
}

// AssembleAIAccountInfo 组装AI账号信息
func (assembler *AIAccountInfoAssembler) AssembleAIAccountInfo(ctx context.Context, aiAccount *aigc_account.AIAccount) (*pb.AIAccountInfo, error) {
	if aiAccount == nil {
		return nil, nil
	}

	result, err := assembler.AssembleAIAccountInfoList(ctx, AIAccountList{aiAccount})
	if err != nil {
		return nil, err
	}
	if len(result) == 0 {
		return nil, nil
	}
	return result[0], nil
}

// AssembleAIAccountInfoList 批量组装AI账号信息
func (assembler *AIAccountInfoAssembler) AssembleAIAccountInfoList(ctx context.Context, aiAccountList AIAccountList) ([]*pb.AIAccountInfo, error) {
	if len(aiAccountList) == 0 {
		return []*pb.AIAccountInfo{}, nil
	}

	// 提取uid列表和ip列表
	var (
		uidList = aiAccountList.GetUidList()
		ipList  = aiAccountList.GetIpList()
		err     error
	)

	// 先加载userInfo
	if uidList, err = assembler.dataLoader.LoadUsersInfo(ctx, uidList); err != nil {
		log.ErrorWithCtx(ctx, "AssembleAIAccountInfoList LoadUsersInfo failed: %v", err)
		return nil, err
	}

	// 并发加载其他数据
	eg, egCtx := errgroup.WithContext(ctx)

	// 加载头像信息
	eg.Go(func() error {
		return assembler.dataLoader.LoadAvatars(egCtx)
	})

	// 加载生日标签
	eg.Go(func() error {
		return assembler.dataLoader.LoadBirthdayTags(egCtx, uidList)
	})

	// 加载相册图片
	eg.Go(func() error {
		return assembler.dataLoader.LoadPhotoAlbums(egCtx, uidList)
	})

	// 加载等级信息
	eg.Go(func() error {
		return assembler.dataLoader.LoadExpLevels(egCtx, uidList)
	})

	// 加载IP位置
	eg.Go(func() error {
		return assembler.dataLoader.LoadIpLocations(egCtx, ipList)
	})

	if err := eg.Wait(); err != nil {
		log.ErrorWithCtx(ctx, "AssembleAIAccountInfoList parallel data loading failed: %v", err)
		return nil, err
	}

	// 组装数据
	result := make([]*pb.AIAccountInfo, 0, len(aiAccountList))
	for _, aiAccount := range aiAccountList {
		if aiAccount == nil {
			continue
		}

		uid := aiAccount.GetUid()
		userInfo := assembler.dataLoader.GetUser(uid)
		if userInfo == nil {
			log.WarnWithCtx(ctx, "AssembleAIAccountInfoList userInfo(uid:%d) not found", uid)
			continue
		}

		ipProvince := "未知"
		if location := assembler.dataLoader.GetIpLocation(aiAccount.GetIp()); location.Province.Name != "" {
			ipProvince = location.Province.Name
		}

		accountInfo := &pb.AIAccountInfo{
			Uid:        aiAccount.GetUid(),
			Password:   aiAccount.GetPassword(),
			Ip:         aiAccount.GetIp(),
			PromptId:   aiAccount.GetPromptId(),
			TimbreId:   aiAccount.GetTimbreId(),
			RoleId:     aiAccount.GetRoleId(),
			Prologue:   aiAccount.GetPrologue(),
			CreateTime: aiAccount.GetCreateTime(),
			UpdateTime: aiAccount.GetUpdateTime(),
			Identity:   aiAccount.GetIdentity(),
			Desc:       aiAccount.GetDesc(),

			Ttid:      userInfo.GetAlias(),
			Phone:     userInfo.GetPhone(),
			Nickname:  userInfo.GetNickname(),
			Sex:       uint32(userInfo.GetSex()),
			Signature: userInfo.GetSignature(),

			Avatar:               assembler.dataLoader.GetAvatar(uid),
			Birthday:             assembler.dataLoader.GetBirthdayTag(uid),
			PhotoImgUrls:         assembler.dataLoader.GetPhotoAlbumUrls(uid),
			ExpLevel:             assembler.dataLoader.GetExpLevel(uid),
			IpLocation:           ipProvince,
			Sort:                 aiAccount.GetSort(),
			AccountTags:          aiAccount.GetAccountTags(),
			IsShowInChatCardWall: aiAccount.GetIsShowInChatCardWall(),
		}
		result = append(result, accountInfo)
	}

	return result, nil
}
