package internal

import (
	"github.com/golang/mock/gomock"
	account_mock "golang.52tt.com/clients/mocks/account"
	"golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_chat"

	im_api_mock "golang.52tt.com/clients/mocks/im-api"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
	"golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_feature_router"
	user_online "golang.52tt.com/protocol/services/user-online"
	config "golang.52tt.com/services/aigc/aigc-account-middle/internal/config/ttconfig/aigc_account_middle"
	"golang.52tt.com/services/aigc/aigc-account-middle/internal/rpc"
)

func init() {
	// Initialize config for tests
	config.SetAigcAccountMiddleConfig(&config.AigcAccountMiddleConfig{
		ChatRoundThreshold:    10,
		SendPrologueThreshold: 3600, // 1 hour
	})
}

func setupMockClients(ctrl *gomock.Controller) *rpc.Clients {
	clients := &rpc.Clients{}
	clients.AigcAccount = aigc_account.NewMockAigcAccountClient(ctrl)
	clients.AigcChatClient = aigc_chat.NewMockAigcChatServiceClient(ctrl)
	clients.AigcRouterClient = aigc_feature_router.NewMockAIGCFeatureRouterClient(ctrl)
	clients.UserOnlineClient = user_online.NewMockUserOnlineClient(ctrl)
	clients.ImApiClient = im_api_mock.NewMockIClient(ctrl)
	clients.Account = account_mock.NewMockIClient(ctrl)
	return clients
}

/*
func TestServer_onCommImEvent(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	clients := setupMockClients(ctrl)
	server := &Server{
		clients: clients,
	}

	ctx := context.Background()

	// Test case 1: 正常情况 - 用户发送消息给AI
	timelineMsg := &timelineV2Pb.TimelineMsg{
		MsgBin: createValidImMsgBin(t),
	}
	timelineMsgData, _ := proto.Marshal(timelineMsg)

	commImEvent := &kafka_im.CommImEvent{
		EventType: kafka_im.CommImEvent_EVENT_TYPE_1V1_SEND_IM,
		DataType:  kafka_im.CommImEvent_DATA_TYPE_TIMELINE_MSG,
		Data:      timelineMsgData,
	}
	eventData, _ := proto.Marshal(commImEvent)

	msg := &subscriber.ConsumerMessage{
		Value: eventData,
	}

	// Mock AI account check
	clients.AigcAccount.(*aigc_account.MockAigcAccountClient).EXPECT().
		BatchGetAIAccount(gomock.Any(), gomock.Any()).
		Return(&aigc_account.BatchGetAIAccountResponse{
			AccountList: []*aigc_account.AIAccount{
				{Uid: 67890, Prologue: "Hello!"},
			},
		}, nil)

	// Mock update chat time
	clients.AigcAccount.(*aigc_account.MockAigcAccountClient).EXPECT().
		UpdateChatTime(gomock.Any(), gomock.Any()).
		Return(&aigc_account.UpdateChatTimeResponse{}, nil)

	// Mock abtest check
	clients.UserOnlineClient.(*user_online.MockUserOnlineClient).EXPECT().
		GetLatestOnlineInfo(gomock.Any(), gomock.Any()).
		Return(&user_online.GetLatestOnlineInfoResp{
			OnlineInfo: &user_online.OnlineInfo{
				DeviceIdHex: "device123",
				MarketId:    1,
			},
		}, nil)

	clients.AigcRouterClient.(*aigc_feature_router.MockAIGCFeatureRouterClient).EXPECT().
		GetIfAIAccountEnabled(gomock.Any(), gomock.Any()).
		Return(&aigc_feature_router.GetIfAIAccountEnabledResp{
			IsAiAccountEnabled: true,
		}, nil)

	// Mock chat round check
	clients.AigcAccount.(*aigc_account.MockAigcAccountClient).EXPECT().
		GetChatRound(gomock.Any(), gomock.Any()).
		Return(&aigc_account.GetChatRoundResponse{
			TotalRound: 5, // Less than threshold
		}, nil)

	// Mock send to chat service
	clients.AigcChatClient.(*aigc_chat.MockAigcChatServiceClient).EXPECT().
		AIAccountRecvUserMsg(gomock.Any(), gomock.Any()).
		Return(&aigc_chat.AIAccountRecvUserMsgResp{}, nil)

	err, retry := server.onCommImEvent(ctx, msg)
	if err != nil {
		t.Errorf("onCommImEvent() error = %v", err)
	}
	if retry {
		t.Errorf("onCommImEvent() retry = %v, want false", retry)
	}

	// Test case 2: 错误情况 - 无效的protobuf数据
	invalidMsg := &subscriber.ConsumerMessage{
		Value: []byte("invalid protobuf data"),
	}

	err, retry = server.onCommImEvent(ctx, invalidMsg)
	if err == nil {
		t.Errorf("onCommImEvent() with invalid data expected error, got nil")
	}
	if retry {
		t.Errorf("onCommImEvent() with invalid data retry = %v, want false", retry)
	}

	// Test case 3: 非发送消息事件
	nonSendEvent := &kafka_im.CommImEvent{
		EventType: kafka_im.CommImEvent_EVENT_TYPE_1V1_RECV_IM,
		DataType:  kafka_im.CommImEvent_DATA_TYPE_TIMELINE_MSG,
		Data:      timelineMsgData,
	}
	nonSendEventData, _ := proto.Marshal(nonSendEvent)

	nonSendMsg := &subscriber.ConsumerMessage{
		Value: nonSendEventData,
	}

	err, retry = server.onCommImEvent(ctx, nonSendMsg)
	if err != nil {
		t.Errorf("onCommImEvent() with non-send event error = %v", err)
	}
	if retry {
		t.Errorf("onCommImEvent() with non-send event retry = %v, want false", retry)
	}
}

func TestServer_handleUserSendAIMsg(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	clients := setupMockClients(ctrl)
	server := &Server{
		clients: clients,
	}

	ctx := context.Background()
	imMsg := &timelinePb.ImMsg{
		FromId:      12345,
		ToId:        67890,
		Content:     "Hello AI",
		ServerMsgId: 123456,
		Type:        uint32(im.IM_MSG_TYPE_TEXT_MSG),
		Ext:         []byte("{}"),
		TargetMsgId: 0,
	}

	// Test case 1: 正常情况
	// Mock update chat time
	clients.AigcAccount.(*aigc_account.MockAigcAccountClient).EXPECT().
		UpdateChatTime(gomock.Any(), &aigc_account.UpdateChatTimeRequest{
			Uid:   12345,
			AiUid: 67890,
		}).
		Return(&aigc_account.UpdateChatTimeResponse{}, nil)

	// Mock abtest check
	clients.UserOnlineClient.(*user_online.MockUserOnlineClient).EXPECT().
		GetLatestOnlineInfo(gomock.Any(), &user_online.GetLatestOnlineInfoReq{
			Uid: 12345,
		}).
		Return(&user_online.GetLatestOnlineInfoResp{
			OnlineInfo: &user_online.OnlineInfo{
				DeviceIdHex: "device123",
				MarketId:    1,
			},
		}, nil)

	clients.AigcRouterClient.(*aigc_feature_router.MockAIGCFeatureRouterClient).EXPECT().
		GetIfAIAccountEnabled(gomock.Any(), gomock.Any()).
		Return(&aigc_feature_router.GetIfAIAccountEnabledResp{
			IsAiAccountEnabled: true,
		}, nil)

	// Mock chat round check
	clients.AigcAccount.(*aigc_account.MockAigcAccountClient).EXPECT().
		GetChatRound(gomock.Any(), &aigc_account.GetChatRoundRequest{
			Uid:       12345,
			NeedTotal: true,
		}).
		Return(&aigc_account.GetChatRoundResponse{
			TotalRound: 5, // Less than threshold
		}, nil)

	// Mock send to chat service
	clients.AigcChatClient.(*aigc_chat.MockAigcChatServiceClient).EXPECT().
		AIAccountRecvUserMsg(gomock.Any(), &aigc_chat.AIAccountRecvUserMsgReq{
			Uid:         12345,
			AiAccountId: 67890,
			Content:     "Hello AI",
			MsgId:       123456,
			MsgType:     uint32(im.IM_MSG_TYPE_TEXT_MSG),
			Extra:        []byte("{}"),
			TargetMsgId: 0,
		}).
		Return(&aigc_chat.AIAccountRecvUserMsgResp{}, nil)

	err, retry := server.handleUserSendAIMsg(ctx, imMsg)
	if err != nil {
		t.Errorf("handleUserSendAIMsg() error = %v", err)
	}
	if retry {
		t.Errorf("handleUserSendAIMsg() retry = %v, want false", retry)
	}

	// Test case 2: 用户不在AB测试中
	clients.UserOnlineClient.(*user_online.MockUserOnlineClient).EXPECT().
		GetLatestOnlineInfo(gomock.Any(), gomock.Any()).
		Return(&user_online.GetLatestOnlineInfoResp{
			OnlineInfo: &user_online.OnlineInfo{
				DeviceIdHex: "device123",
				MarketId:    1,
			},
		}, nil)

	clients.AigcRouterClient.(*aigc_feature_router.MockAIGCFeatureRouterClient).EXPECT().
		GetIfAIAccountEnabled(gomock.Any(), gomock.Any()).
		Return(&aigc_feature_router.GetIfAIAccountEnabledResp{
			IsAiAccountEnabled: false,
		}, nil)

	clients.AigcAccount.(*aigc_account.MockAigcAccountClient).EXPECT().
		UpdateChatTime(gomock.Any(), gomock.Any()).
		Return(&aigc_account.UpdateChatTimeResponse{}, nil)

	err, retry = server.handleUserSendAIMsg(ctx, imMsg)
	if err != nil {
		t.Errorf("handleUserSendAIMsg() with abtest disabled error = %v", err)
	}
	if retry {
		t.Errorf("handleUserSendAIMsg() with abtest disabled retry = %v, want false", retry)
	}

	// Test case 3: 聊天轮数超过阈值
	clients.AigcAccount.(*aigc_account.MockAigcAccountClient).EXPECT().
		UpdateChatTime(gomock.Any(), gomock.Any()).
		Return(&aigc_account.UpdateChatTimeResponse{}, nil)

	clients.UserOnlineClient.(*user_online.MockUserOnlineClient).EXPECT().
		GetLatestOnlineInfo(gomock.Any(), gomock.Any()).
		Return(&user_online.GetLatestOnlineInfoResp{
			OnlineInfo: &user_online.OnlineInfo{
				DeviceIdHex: "device123",
				MarketId:    1,
			},
		}, nil)

	clients.AigcRouterClient.(*aigc_feature_router.MockAIGCFeatureRouterClient).EXPECT().
		GetIfAIAccountEnabled(gomock.Any(), gomock.Any()).
		Return(&aigc_feature_router.GetIfAIAccountEnabledResp{
			IsAiAccountEnabled: true,
		}, nil)

	clients.AigcAccount.(*aigc_account.MockAigcAccountClient).EXPECT().
		GetChatRound(gomock.Any(), gomock.Any()).
		Return(&aigc_account.GetChatRoundResponse{
			TotalRound: 15, // Greater than threshold (10)
		}, nil)

	err, retry = server.handleUserSendAIMsg(ctx, imMsg)
	if err != nil {
		t.Errorf("handleUserSendAIMsg() with round limit error = %v", err)
	}
	if retry {
		t.Errorf("handleUserSendAIMsg() with round limit retry = %v, want false", retry)
	}
}

func TestServer_filterMsg(t *testing.T) {
	server := &Server{}
	ctx := context.Background()

	// Test case 1: 正常消息
	normalMsg := &timelinePb.ImMsg{
		FromId: 12345,
		ToId:   67890,
		Label:  0,
	}
	if server.filterMsg(ctx, normalMsg) {
		t.Errorf("filterMsg() with normal message = true, want false")
	}

	// Test case 2: FromId为0
	invalidFromMsg := &timelinePb.ImMsg{
		FromId: 0,
		ToId:   67890,
		Label:  0,
	}
	if !server.filterMsg(ctx, invalidFromMsg) {
		t.Errorf("filterMsg() with FromId=0 = false, want true")
	}

	// Test case 3: ToId为0
	invalidToMsg := &timelinePb.ImMsg{
		FromId: 12345,
		ToId:   0,
		Label:  0,
	}
	if !server.filterMsg(ctx, invalidToMsg) {
		t.Errorf("filterMsg() with ToId=0 = false, want true")
	}

	// Test case 4: 新关注消息
	newFollowerMsg := &timelinePb.ImMsg{
		FromId: 12345,
		ToId:   67890,
		Label:  uint32(im.MsgLabel_NEW_FOLLOWER),
	}
	if !server.filterMsg(ctx, newFollowerMsg) {
		t.Errorf("filterMsg() with NEW_FOLLOWER label = false, want true")
	}
}

func TestServer_isInAbtest(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	clients := setupMockClients(ctrl)
	server := &Server{
		clients: clients,
	}

	ctx := context.Background()
	uid := uint32(12345)
	source := aigc_feature_router.Source_SourceIM

	// Test case 1: 正常情况 - 在AB测试中
	clients.UserOnlineClient.(*user_online.MockUserOnlineClient).EXPECT().
		GetLatestOnlineInfo(gomock.Any(), &user_online.GetLatestOnlineInfoReq{
			Uid: uid,
		}).
		Return(&user_online.GetLatestOnlineInfoResp{
			OnlineInfo: &user_online.OnlineInfo{
				DeviceIdHex: "device123",
				MarketId:    1,
			},
		}, nil)

	clients.AigcRouterClient.(*aigc_feature_router.MockAIGCFeatureRouterClient).EXPECT().
		GetIfAIAccountEnabled(gomock.Any(), &aigc_feature_router.GetIfAIAccountEnabledReq{
			Uid:         uid,
			DeviceId:    "device123",
			Source:      source,
			MarketId:    1,
			IsNotSubmit: false,
		}).
		Return(&aigc_feature_router.GetIfAIAccountEnabledResp{
			IsAiAccountEnabled: true,
		}, nil)

	result := server.isInAbtest(ctx, uid, source)
	if !result {
		t.Errorf("isInAbtest() = false, want true")
	}

	// Test case 2: GetLatestOnlineInfo返回错误
	clients.UserOnlineClient.(*user_online.MockUserOnlineClient).EXPECT().
		GetLatestOnlineInfo(gomock.Any(), gomock.Any()).
		Return(nil, errors.New("mock error"))

	result = server.isInAbtest(ctx, uid, source)
	if result {
		t.Errorf("isInAbtest() with GetLatestOnlineInfo error = true, want false")
	}

	// Test case 3: OnlineInfo为nil
	clients.UserOnlineClient.(*user_online.MockUserOnlineClient).EXPECT().
		GetLatestOnlineInfo(gomock.Any(), gomock.Any()).
		Return(&user_online.GetLatestOnlineInfoResp{
			OnlineInfo: nil,
		}, nil)

	result = server.isInAbtest(ctx, uid, source)
	if result {
		t.Errorf("isInAbtest() with nil OnlineInfo = true, want false")
	}

	// Test case 4: GetIfAIAccountEnabled返回错误
	clients.UserOnlineClient.(*user_online.MockUserOnlineClient).EXPECT().
		GetLatestOnlineInfo(gomock.Any(), gomock.Any()).
		Return(&user_online.GetLatestOnlineInfoResp{
			OnlineInfo: &user_online.OnlineInfo{
				DeviceIdHex: "device123",
				MarketId:    1,
			},
		}, nil)

	clients.AigcRouterClient.(*aigc_feature_router.MockAIGCFeatureRouterClient).EXPECT().
		GetIfAIAccountEnabled(gomock.Any(), gomock.Any()).
		Return(nil, errors.New("mock error"))

	result = server.isInAbtest(ctx, uid, source)
	if result {
		t.Errorf("isInAbtest() with GetIfAIAccountEnabled error = true, want false")
	}

	// Test case 5: 不在AB测试中
	clients.UserOnlineClient.(*user_online.MockUserOnlineClient).EXPECT().
		GetLatestOnlineInfo(gomock.Any(), gomock.Any()).
		Return(&user_online.GetLatestOnlineInfoResp{
			OnlineInfo: &user_online.OnlineInfo{
				DeviceIdHex: "device123",
				MarketId:    1,
			},
		}, nil)

	clients.AigcRouterClient.(*aigc_feature_router.MockAIGCFeatureRouterClient).EXPECT().
		GetIfAIAccountEnabled(gomock.Any(), gomock.Any()).
		Return(&aigc_feature_router.GetIfAIAccountEnabledResp{
			IsAiAccountEnabled: false,
		}, nil)

	result = server.isInAbtest(ctx, uid, source)
	if result {
		t.Errorf("isInAbtest() with disabled feature = true, want false")
	}
}

func TestServer_onUgcFollowingUpdate(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	clients := setupMockClients(ctrl)
	server := &Server{
		clients: clients,
	}

	ctx := context.Background()

	// Test case 1: 正常情况 - 用户关注AI账号
	followEvent := &ugc_event.FollowEvent{
		FromUserId: 12345,
		ToUserId:   67890,
		IsDeleted:  false,
	}
	eventData, _ := proto.Marshal(followEvent)

	msg := &subscriber.ConsumerMessage{
		Value: eventData,
	}

	// Mock AI account check
	clients.AigcAccount.(*aigc_account.MockAigcAccountClient).EXPECT().
		BatchGetAIAccount(gomock.Any(), &aigc_account.BatchGetAIAccountRequest{
			UidList: []uint32{67890, 12345},
		}).
		Return(&aigc_account.BatchGetAIAccountResponse{
			AccountList: []*aigc_account.AIAccount{
				{Uid: 67890, Prologue: "Welcome! I'm your AI assistant."},
			},
		}, nil)

	// Mock abtest check
	clients.UserOnlineClient.(*user_online.MockUserOnlineClient).EXPECT().
		GetLatestOnlineInfo(gomock.Any(), &user_online.GetLatestOnlineInfoReq{
			Uid: 12345,
		}).
		Return(&user_online.GetLatestOnlineInfoResp{
			OnlineInfo: &user_online.OnlineInfo{
				DeviceIdHex: "device123",
				MarketId:    1,
			},
		}, nil)

	clients.AigcRouterClient.(*aigc_feature_router.MockAIGCFeatureRouterClient).EXPECT().
		GetIfAIAccountEnabled(gomock.Any(), gomock.Any()).
		Return(&aigc_feature_router.GetIfAIAccountEnabledResp{
			IsAiAccountEnabled: true,
		}, nil)

	// Mock account info
	userAccount := &account.User{
		Uid:      []uint32{12345}[0],
		Username: []string{"user123"}[0],
		Nickname: []string{"User"}[0],
	}
	aiAccount := &account.User{
		Uid:      []uint32{67890}[0],
		Username: []string{"ai123"}[0],
		Nickname: []string{"AI Assistant"}[0],
	}

	clients.Account.(*account_mock.MockIClient).EXPECT().
		GetUsersMap(gomock.Any(), []uint32{12345, 67890}).
		Return(map[uint32]*account.User{
			12345: userAccount,
			67890: aiAccount,
		}, nil)

	// Mock last chat time check
	clients.AigcAccount.(*aigc_account.MockAigcAccountClient).EXPECT().
		GetLastChatTime(gomock.Any(), &aigc_account.GetLastChatTimeRequest{
			Uid:   12345,
			AiUid: 67890,
		}).
		Return(&aigc_account.GetLastChatTimeResponse{
			LastChatTime: time.Now().Unix() - 7200, // 2 hours ago
		}, nil)

	// Mock send message
	clients.ImApiClient.(*im_api_mock.MockIClient).EXPECT().
		Send1V1ExtMsg(gomock.Any(), gomock.Any()).
		Return(&im_api.Send1V1ExtMsgResp{}, nil)

	err, retry := server.onUgcFollowingUpdate(ctx, msg)
	if err != nil {
		t.Errorf("onUgcFollowingUpdate() error = %v", err)
	}
	if retry {
		t.Errorf("onUgcFollowingUpdate() retry = %v, want false", retry)
	}

	// Test case 2: 取消关注事件
	unfollowEvent := &ugc_event.FollowEvent{
		FromUserId: 12345,
		ToUserId:   67890,
		IsDeleted:  true,
	}
	unfollowEventData, _ := proto.Marshal(unfollowEvent)

	unfollowMsg := &subscriber.ConsumerMessage{
		Value: unfollowEventData,
	}

	err, retry = server.onUgcFollowingUpdate(ctx, unfollowMsg)
	if err != nil {
		t.Errorf("onUgcFollowingUpdate() with unfollow error = %v", err)
	}
	if retry {
		t.Errorf("onUgcFollowingUpdate() with unfollow retry = %v, want false", retry)
	}

	// Test case 3: 无效的protobuf数据
	invalidMsg := &subscriber.ConsumerMessage{
		Value: []byte("invalid protobuf data"),
	}

	err, retry = server.onUgcFollowingUpdate(ctx, invalidMsg)
	if err == nil {
		t.Errorf("onUgcFollowingUpdate() with invalid data expected error, got nil")
	}
	if retry {
		t.Errorf("onUgcFollowingUpdate() with invalid data retry = %v, want false", retry)
	}

	// Test case 4: 最近聊天时间太短
	clients.AigcAccount.(*aigc_account.MockAigcAccountClient).EXPECT().
		BatchGetAIAccount(gomock.Any(), gomock.Any()).
		Return(&aigc_account.BatchGetAIAccountResponse{
			AccountList: []*aigc_account.AIAccount{
				{Uid: 67890, Prologue: "Welcome!"},
			},
		}, nil)

	clients.UserOnlineClient.(*user_online.MockUserOnlineClient).EXPECT().
		GetLatestOnlineInfo(gomock.Any(), gomock.Any()).
		Return(&user_online.GetLatestOnlineInfoResp{
			OnlineInfo: &user_online.OnlineInfo{
				DeviceIdHex: "device123",
				MarketId:    1,
			},
		}, nil)

	clients.AigcRouterClient.(*aigc_feature_router.MockAIGCFeatureRouterClient).EXPECT().
		GetIfAIAccountEnabled(gomock.Any(), gomock.Any()).
		Return(&aigc_feature_router.GetIfAIAccountEnabledResp{
			IsAiAccountEnabled: true,
		}, nil)

	clients.Account.(*account_mock.MockIClient).EXPECT().
		GetUsersMap(gomock.Any(), gomock.Any()).
		Return(map[uint32]*account.User{
			12345: userAccount,
			67890: aiAccount,
		}, nil)

	clients.AigcAccount.(*aigc_account.MockAigcAccountClient).EXPECT().
		GetLastChatTime(gomock.Any(), gomock.Any()).
		Return(&aigc_account.GetLastChatTimeResponse{
			LastChatTime: time.Now().Unix() - 1800, // 30 minutes ago (less than threshold)
		}, nil)

	err, retry = server.onUgcFollowingUpdate(ctx, msg)
	if err != nil {
		t.Errorf("onUgcFollowingUpdate() with recent chat error = %v", err)
	}
	if retry {
		t.Errorf("onUgcFollowingUpdate() with recent chat retry = %v, want false", retry)
	}
}

// Helper function to create valid ImMsg binary data
func createValidImMsgBin(t *testing.T) []byte {
	imMsg := &timelinePb.ImMsg{
		FromId:      12345,
		ToId:        67890,
		Content:     "Hello AI",
		ServerMsgId: 123456,
		Type:        uint32(im.IM_MSG_TYPE_TEXT_MSG),
		Ext:         []byte("{}"),
		TargetMsgId: 0,
	}
	data, err := proto.Marshal(imMsg)
	if err != nil {
		t.Fatalf("Failed to marshal ImMsg: %v", err)
	}
	return data
}
*/
