// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-account-middle/internal/mgr (interfaces: AIAccountManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	aigc_account_middle "golang.52tt.com/protocol/services/aigc/aigc-account-middle"
)

// MockAIAccountManager is a mock of AIAccountManager interface.
type MockAIAccountManager struct {
	ctrl     *gomock.Controller
	recorder *MockAIAccountManagerMockRecorder
}

// MockAIAccountManagerMockRecorder is the mock recorder for MockAIAccountManager.
type MockAIAccountManagerMockRecorder struct {
	mock *MockAIAccountManager
}

// NewMockAIAccountManager creates a new mock instance.
func NewMockAIAccountManager(ctrl *gomock.Controller) *MockAIAccountManager {
	mock := &MockAIAccountManager{ctrl: ctrl}
	mock.recorder = &MockAIAccountManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAIAccountManager) EXPECT() *MockAIAccountManagerMockRecorder {
	return m.recorder
}

// CreateAIAccount mocks base method.
func (m *MockAIAccountManager) CreateAIAccount(arg0 context.Context, arg1 *aigc_account_middle.CreateAIAccountRequest) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAIAccount", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAIAccount indicates an expected call of CreateAIAccount.
func (mr *MockAIAccountManagerMockRecorder) CreateAIAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAIAccount", reflect.TypeOf((*MockAIAccountManager)(nil).CreateAIAccount), arg0, arg1)
}

// UnregisterAIAccount mocks base method.
func (m *MockAIAccountManager) UnregisterAIAccount(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnregisterAIAccount", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnregisterAIAccount indicates an expected call of UnregisterAIAccount.
func (mr *MockAIAccountManagerMockRecorder) UnregisterAIAccount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnregisterAIAccount", reflect.TypeOf((*MockAIAccountManager)(nil).UnregisterAIAccount), arg0, arg1, arg2)
}

// UpdateAIAccount mocks base method.
func (m *MockAIAccountManager) UpdateAIAccount(arg0 context.Context, arg1 *aigc_account_middle.UpdateAIAccountRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAIAccount", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAIAccount indicates an expected call of UpdateAIAccount.
func (mr *MockAIAccountManagerMockRecorder) UpdateAIAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAIAccount", reflect.TypeOf((*MockAIAccountManager)(nil).UpdateAIAccount), arg0, arg1)
}
