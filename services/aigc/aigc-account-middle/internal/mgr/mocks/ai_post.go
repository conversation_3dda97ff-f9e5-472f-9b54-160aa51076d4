// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-account-middle/internal/mgr (interfaces: AIPostManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	aigc_account_middle "golang.52tt.com/protocol/services/aigc/aigc-account-middle"
)

// MockAIPostManager is a mock of AIPostManager interface.
type MockAIPostManager struct {
	ctrl     *gomock.Controller
	recorder *MockAIPostManagerMockRecorder
}

// MockAIPostManagerMockRecorder is the mock recorder for MockAIPostManager.
type MockAIPostManagerMockRecorder struct {
	mock *MockAIPostManager
}

// NewMockAIPostManager creates a new mock instance.
func NewMockAIPostManager(ctrl *gomock.Controller) *MockAIPostManager {
	mock := &MockAIPostManager{ctrl: ctrl}
	mock.recorder = &MockAIPostManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAIPostManager) EXPECT() *MockAIPostManagerMockRecorder {
	return m.recorder
}

// BatchGetAiPost mocks base method.
func (m *MockAIPostManager) BatchGetAiPost(arg0 context.Context, arg1 *aigc_account_middle.GetAiPostReq) ([]*aigc_account_middle.PostInfo, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAiPost", arg0, arg1)
	ret0, _ := ret[0].([]*aigc_account_middle.PostInfo)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// BatchGetAiPost indicates an expected call of BatchGetAiPost.
func (mr *MockAIPostManagerMockRecorder) BatchGetAiPost(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAiPost", reflect.TypeOf((*MockAIPostManager)(nil).BatchGetAiPost), arg0, arg1)
}
