package ai_post

import (
	"context"
	"golang.52tt.com/pkg/log"
	aigcAccountpb "golang.52tt.com/protocol/services/aigc/aigc-account"
	pb "golang.52tt.com/protocol/services/aigc/aigc-account-middle"
	ugcContent "golang.52tt.com/protocol/services/ugc/content"
	"golang.52tt.com/services/aigc/aigc-account-middle/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-account-middle/internal/rpc"
)

type Manager struct {
	clients *rpc.Clients
}

func NewManager(clients *rpc.Clients) mgr.AIPostManager {
	return &Manager{
		clients: clients,
	}
}

func (m *Manager) BatchGetAiPost(ctx context.Context, req *pb.GetAiPostReq) ([]*pb.PostInfo, uint32, error) {
	aiPostIdList, err := m.clients.AigcAccount.GetAiPost(ctx, &aigcAccountpb.GetAiPostRequest{
		Page:  req.GetPage(),
		Size:  req.GetSize(),
		AiUid: []uint32{req.GetUid()},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetAiPost GetAiPost error: %v", err)
		return nil, 0, err
	}

	if aiPostIdList.GetTotalNum() == 0 || len(aiPostIdList.GetPostIds()) == 0 {
		return nil, 0, nil
	}

	// 批量获取帖子详情
	postInfoList, err := m.clients.UgcContentClient.BatchGetPostListById(ctx, &ugcContent.BatchGetPostListByIdReq{
		PostIdList: aiPostIdList.GetPostIds(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetAiPost BatchGetPostListById error: %v", err)
		return nil, 0, err
	}

	postInfoMap := make(map[string]*ugcContent.PostInfo, len(postInfoList))
	for _, postInfo := range postInfoList {
		postInfoMap[postInfo.GetPostId()] = postInfo
	}

	var aiPostList []*pb.PostInfo
	for _, postId := range aiPostIdList.GetPostIds() {
		if postInfo, ok := postInfoMap[postId]; ok {
			aiPost := &pb.PostInfo{
				PostId:     postInfo.GetPostId(),
				Content:    postInfo.GetContent(),
				CreateTime: postInfo.GetCreateAt(),
			}
			if len(postInfo.Attachments) > 0 {
				aiPost.Attachments = make([]*pb.AttachmentInfo, len(postInfo.Attachments))
				for i, attachment := range postInfo.Attachments {
					aiPost.Attachments[i] = &pb.AttachmentInfo{
						AttachmentType:    uint32(attachment.GetType()),
						AttachmentContent: attachment.GetContent(),
						ExtraInfo:         attachment.GetExtra(),
						Key:               attachment.GetKey(),
						VmContent:         attachment.GetVmContent(),
						OriginVideoCover:  attachment.GetOriginVideoCover(),
						Param:             attachment.GetParam(),
					}
				}
			}

			aiPostList = append(aiPostList, aiPost)
		}
	}

	return aiPostList, aiPostIdList.GetTotalNum(), nil
}
