package account

import (
	"context"
	"strings"
	"testing"

	user_auth_history "golang.52tt.com/clients/mocks/user-auth-history"
	mock_report "golang.52tt.com/services/aigc/aigc-account-middle/internal/report/mocks"

	censoring_proxy "golang.52tt.com/protocol/services/censoring-proxy"
	arbiter "golang.52tt.com/protocol/services/cybros/arbiter/v2"

	"golang.52tt.com/pkg/protocol"
	config "golang.52tt.com/services/aigc/aigc-account-middle/internal/config/ttconfig/aigc_account_middle"

	account_go "golang.52tt.com/protocol/services/account-go"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"

	account_apicenter "golang.52tt.com/protocol/services/account-apicenter"

	"errors"

	"github.com/golang/mock/gomock"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/mvp_proxy"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/security_go"
	mock_account "golang.52tt.com/clients/mocks/account"
	mock_account_go "golang.52tt.com/clients/mocks/account-go"
	mock_censoring_proxy "golang.52tt.com/clients/mocks/censoring-proxy"
	mock_censoring_proxy_text "golang.52tt.com/clients/mocks/censoring-proxy/text"
	mock_expsvr "golang.52tt.com/clients/mocks/expsvr"
	mock_user_tag_go "golang.52tt.com/clients/mocks/user-tag-go"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	pb "golang.52tt.com/protocol/services/aigc/aigc-account-middle"
	"golang.52tt.com/protocol/services/avatar"
	avatar_mng_api "golang.52tt.com/protocol/services/avatar-mng-api"
	"golang.52tt.com/services/aigc/aigc-account-middle/internal/rpc"
)

func init() {
	config.SetAigcAccountMiddleConfig(&config.AigcAccountMiddleConfig{
		ChatRoundThreshold:    0,
		SendPrologueThreshold: 0,
		RiskWhitelistId:       0,
	})
}

// setupMockClients 返回所有mock对象，便于用例直接类型断言和mock行为配置
func setupMockClients(ctrl *gomock.Controller) *rpc.Clients {
	clients := &rpc.Clients{}
	clients.Account = mock_account.NewMockIClient(ctrl)
	clients.AccountGo = mock_account_go.NewMockIClient(ctrl)
	clients.AccountApiCenter = account_apicenter.NewMockAccountApicenterClient(ctrl)
	clients.UserTagGo = mock_user_tag_go.NewMockIClient(ctrl)
	clients.Exp = mock_expsvr.NewMockIClient(ctrl)
	clients.Censoring = mock_censoring_proxy.NewMockIClient(ctrl)
	clients.AvatarMngApi = avatar_mng_api.NewMockAvatarMngApiClient(ctrl)
	clients.MvpProxy = mvp_proxy.NewMockMvpProxyClient(ctrl)
	clients.Security = security_go.NewMockSecurityGoClient(ctrl)
	clients.AigcAccount = aigc_account.NewMockAigcAccountClient(ctrl)
	clients.Avatar = avatar.NewMockAvatarClient(ctrl)
	clients.AuthHistory = user_auth_history.NewMockIClient(ctrl)
	return clients
}

func TestManager_CreateAIAccount_FullChain_Normal(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	clients := setupMockClients(ctrl)
	mockAccount := clients.Account.(*mock_account.MockIClient)
	//mockAccountGo := clients.AccountGo.(*mock_account_go.MockIClient)
	mockUserTagGo := clients.UserTagGo.(*mock_user_tag_go.MockIClient)
	mockExp := clients.Exp.(*mock_expsvr.MockIClient)
	mockAccountApiCenter := clients.AccountApiCenter.(*account_apicenter.MockAccountApicenterClient)
	mockAvatarMngApi := clients.AvatarMngApi.(*avatar_mng_api.MockAvatarMngApiClient)
	mockMvpProxy := clients.MvpProxy.(*mvp_proxy.MockMvpProxyClient)
	//mockSecurity := clients.Security.(*security_go.MockSecurityGoClient)
	mockAigcAccount := clients.AigcAccount.(*aigc_account.MockAigcAccountClient)
	mockReporter := mock_report.NewMockReporter(ctrl)
	mockAuthHistory := clients.AuthHistory.(*user_auth_history.MockIClient)

	// Account服务
	mockAccount.EXPECT().CreateUser(gomock.Any(), gomock.Any()).Return(&accountPB.UidResp{Uid: 1, Username: "u"}, nil).Times(1)
	mockAccount.EXPECT().UpdateUserType(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
	// 风控白名单
	mockMvpProxy.EXPECT().UpdateUserRiskList(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
	// 写AI账号表
	mockAigcAccount.EXPECT().CreateAIAccount(gomock.Any(), gomock.Any()).Return(&aigc_account.CreateAIAccountResponse{}, nil).Times(1)
	// 上传头像
	mockAvatarMngApi.EXPECT().UploadAvatar(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
	// 生日标签
	mockUserTagGo.EXPECT().SetUserTag(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
	// 相册
	mockAccountApiCenter.EXPECT().UpdatePhotoAlbum(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
	// 个性签名
	mockAccount.EXPECT().UpdateSignature(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
	// 经验值
	mockExp.EXPECT().AddUserExpV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
	// 修改登录ip
	mockAuthHistory.EXPECT().RecordUserLogin(gomock.Any(), gomock.Any()).Return(true, nil).Times(1)
	// 数据上报
	mockReporter.EXPECT().ReportAIAccountInfo(gomock.Any(), gomock.Any()).Times(1)
	// AccountGo
	//mockAccountGo.EXPECT().GetUserByUid(gomock.Any(), gomock.Any()).Return(&account_go.UserResp{Uid: new(uint32)}, nil).AnyTimes()
	// Security
	//mockSecurity.EXPECT().UpdateAutoProcUnregApplyStatus(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

	mgr := NewManager(clients, mockReporter)
	uid, err := mgr.CreateAIAccount(context.Background(), &pb.CreateAIAccountRequest{
		Account: &pb.CreateAIAccountRequest_Account{
			Phone:        "p",
			Password:     "pwd",
			Nickname:     "n",
			Sex:          1,
			Prologue:     "hi",
			PromptId:     2,
			TimbreId:     3,
			Avatar:       "https://ga-album-cdnqn.52tt.com/testing-yunying/331b-197ed3fd7c3.jpeg",
			RoleId:       4,
			Birthday:     "2000-01-01",
			Signature:    "sig",
			PhotoImgKeys: []string{"key1", "key2"},
			AddExp:       10,
		},
	})
	if err != nil {
		t.Fatalf("unexpected err: %v", err)
	}
	if uid != 1 {
		t.Errorf("uid not match: got %v", uid)
	}
}

func TestManager_CreateAIAccount_FullChain_AccountError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	clients := setupMockClients(ctrl)
	mockAccount := clients.Account.(*mock_account.MockIClient)
	// 只mock主异常分支，其他下游不应被调用
	mockAccount.EXPECT().CreateUser(gomock.Any(), gomock.Any()).Return(nil, errors.New("mock error")).Times(1)

	mgr := NewManager(clients, nil)
	uid, err := mgr.CreateAIAccount(context.Background(), &pb.CreateAIAccountRequest{
		Account: &pb.CreateAIAccountRequest_Account{
			Phone:        "p",
			Password:     "pwd",
			Nickname:     "n",
			Sex:          1,
			Prologue:     "hi",
			PromptId:     2,
			TimbreId:     3,
			RoleId:       4,
			Birthday:     "2000-01-01",
			Signature:    "sig",
			PhotoImgKeys: []string{"key1", "key2"},
			AddExp:       10,
		},
	})
	if err == nil {
		t.Errorf("expect error, got nil")
	}
	if uid != 0 {
		t.Errorf("expect uid=0, got %v", uid)
	}
}

func TestManager_CreateAIAccount_FullChain_UserTagError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	clients := setupMockClients(ctrl)
	mockAccount := clients.Account.(*mock_account.MockIClient)
	//mockAccountGo := clients.AccountGo.(*mock_account_go.MockIClient)
	mockUserTagGo := clients.UserTagGo.(*mock_user_tag_go.MockIClient)
	mockExp := clients.Exp.(*mock_expsvr.MockIClient)
	mockAccountApiCenter := clients.AccountApiCenter.(*account_apicenter.MockAccountApicenterClient)
	mockAvatarMngApi := clients.AvatarMngApi.(*avatar_mng_api.MockAvatarMngApiClient)
	mockMvpProxy := clients.MvpProxy.(*mvp_proxy.MockMvpProxyClient)
	//mockSecurity := clients.Security.(*security_go.MockSecurityGoClient)
	mockAigcAccount := clients.AigcAccount.(*aigc_account.MockAigcAccountClient)
	mockErr := protocol.NewExactServerError(nil, 0, "mock error")
	mockReporter := mock_report.NewMockReporter(ctrl)
	mockAuthHistory := clients.AuthHistory.(*user_auth_history.MockIClient)

	// Account服务
	mockAccount.EXPECT().CreateUser(gomock.Any(), gomock.Any()).Return(&accountPB.UidResp{Uid: 1, Username: "u"}, nil).Times(1)
	mockAccount.EXPECT().UpdateUserType(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
	// 风控白名单
	mockMvpProxy.EXPECT().UpdateUserRiskList(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
	// 写AI账号表
	mockAigcAccount.EXPECT().CreateAIAccount(gomock.Any(), gomock.Any()).Return(&aigc_account.CreateAIAccountResponse{}, nil).Times(1)
	// 上传头像
	mockAvatarMngApi.EXPECT().UploadAvatar(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
	// 生日标签异常
	mockUserTagGo.EXPECT().SetUserTag(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockErr).Times(1)
	// 相册
	mockAccountApiCenter.EXPECT().UpdatePhotoAlbum(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
	// 个性签名
	mockAccount.EXPECT().UpdateSignature(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
	// 经验值
	mockExp.EXPECT().AddUserExpV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
	// 修改登录ip
	mockAuthHistory.EXPECT().RecordUserLogin(gomock.Any(), gomock.Any()).Return(true, nil).Times(1)
	// 数据上报
	mockReporter.EXPECT().ReportAIAccountInfo(gomock.Any(), gomock.Any()).Times(1)
	// AccountGo
	//mockAccountGo.EXPECT().GetUserByUid(gomock.Any(), gomock.Any()).Return(&account_go.UserResp{Uid: new(uint32)}, nil).AnyTimes()
	// Security
	//mockSecurity.EXPECT().UpdateAutoProcUnregApplyStatus(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

	mgr := NewManager(clients, mockReporter)
	uid, err := mgr.CreateAIAccount(context.Background(), &pb.CreateAIAccountRequest{
		Account: &pb.CreateAIAccountRequest_Account{
			Phone:        "p",
			Password:     "pwd",
			Nickname:     "n",
			Sex:          1,
			Prologue:     "hi",
			PromptId:     2,
			TimbreId:     3,
			RoleId:       4,
			Birthday:     "2000-01-01",
			Signature:    "sig",
			PhotoImgKeys: []string{"key1", "key2"},
			AddExp:       10,
		},
	})
	if err == nil {
		t.Errorf("expect error, got nil")
	}
	if uid != 1 {
		t.Errorf("expect uid=1, got %v", uid)
	}
}

func TestManager_CreateAIAccount_FullChain_AigcAccountError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	clients := setupMockClients(ctrl)
	mockAccount := clients.Account.(*mock_account.MockIClient)
	mockAigcAccount := clients.AigcAccount.(*aigc_account.MockAigcAccountClient)
	mockMvpProxy := clients.MvpProxy.(*mvp_proxy.MockMvpProxyClient)
	mockSecurity := clients.Security.(*security_go.MockSecurityGoClient)

	mockAccount.EXPECT().CreateUser(gomock.Any(), gomock.Any()).Return(&accountPB.UidResp{Uid: 1, Username: "u"}, nil).Times(1)
	mockAccount.EXPECT().UpdateUserType(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
	mockMvpProxy.EXPECT().UpdateUserRiskList(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
	mockAigcAccount.EXPECT().CreateAIAccount(gomock.Any(), gomock.Any()).Return(nil, errors.New("mock aigc error")).Times(1)
	mockSecurity.EXPECT().UpdateAutoProcUnregApplyStatus(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)

	mgr := NewManager(clients, nil)
	uid, err := mgr.CreateAIAccount(context.Background(), &pb.CreateAIAccountRequest{
		Account: &pb.CreateAIAccountRequest_Account{
			Phone: "p", Password: "pwd", Nickname: "n", Sex: 1, Prologue: "hi", PromptId: 2, TimbreId: 3, RoleId: 4,
			Birthday: "2000-01-01", Signature: "sig", PhotoImgKeys: []string{"key1", "key2"}, AddExp: 10,
		},
	})
	if err == nil {
		t.Errorf("expect error, got nil")
	}
	if uid != 0 {
		t.Errorf("expect uid=0, got %v", uid)
	}
}

func TestManager_CreateAIAccount_FullChain_UpdateUserTypeError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	clients := setupMockClients(ctrl)
	mockAccount := clients.Account.(*mock_account.MockIClient)
	//mockMvpProxy := clients.MvpProxy.(*mvp_proxy.MockMvpProxyClient)

	mockAccount.EXPECT().CreateUser(gomock.Any(), gomock.Any()).Return(&accountPB.UidResp{Uid: 1, Username: "u"}, nil).Times(1)
	mockAccount.EXPECT().UpdateUserType(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("mock update type error")).Times(1)
	//mockMvpProxy.EXPECT().UpdateUserRiskList(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

	mgr := NewManager(clients, nil)
	uid, err := mgr.CreateAIAccount(context.Background(), &pb.CreateAIAccountRequest{
		Account: &pb.CreateAIAccountRequest_Account{
			Phone: "p", Password: "pwd", Nickname: "n", Sex: 1, Prologue: "hi", PromptId: 2, TimbreId: 3, RoleId: 4,
			Birthday: "2000-01-01", Signature: "sig", PhotoImgKeys: []string{"key1", "key2"}, AddExp: 10,
		},
	})
	if err == nil {
		t.Errorf("expect error, got nil")
	}
	if uid != 0 {
		t.Errorf("expect uid=0, got %v", uid)
	}
}

func TestManager_CreateAIAccount_FullChain_RiskWhiteListError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	clients := setupMockClients(ctrl)
	mockAccount := clients.Account.(*mock_account.MockIClient)
	//mockAccountGo := clients.AccountGo.(*mock_account_go.MockIClient)
	mockUserTagGo := clients.UserTagGo.(*mock_user_tag_go.MockIClient)
	mockExp := clients.Exp.(*mock_expsvr.MockIClient)
	mockAccountApiCenter := clients.AccountApiCenter.(*account_apicenter.MockAccountApicenterClient)
	mockAvatarMngApi := clients.AvatarMngApi.(*avatar_mng_api.MockAvatarMngApiClient)
	mockMvpProxy := clients.MvpProxy.(*mvp_proxy.MockMvpProxyClient)
	//mockSecurity := clients.Security.(*security_go.MockSecurityGoClient)
	mockAigcAccount := clients.AigcAccount.(*aigc_account.MockAigcAccountClient)
	mockReporter := mock_report.NewMockReporter(ctrl)
	mockAuthHistory := clients.AuthHistory.(*user_auth_history.MockIClient)

	// Account服务
	mockAccount.EXPECT().CreateUser(gomock.Any(), gomock.Any()).Return(&accountPB.UidResp{Uid: 1, Username: "u"}, nil).Times(1)
	mockAccount.EXPECT().UpdateUserType(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
	// 风控白名单异常
	mockMvpProxy.EXPECT().UpdateUserRiskList(gomock.Any(), gomock.Any()).Return(nil, errors.New("mock risk error")).Times(1)
	// 写AI账号表
	mockAigcAccount.EXPECT().CreateAIAccount(gomock.Any(), gomock.Any()).Return(&aigc_account.CreateAIAccountResponse{}, nil).Times(1)
	// 上传头像
	mockAvatarMngApi.EXPECT().UploadAvatar(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
	// 生日标签
	mockUserTagGo.EXPECT().SetUserTag(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	// 相册
	mockAccountApiCenter.EXPECT().UpdatePhotoAlbum(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
	// 个性签名
	mockAccount.EXPECT().UpdateSignature(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
	// 经验值
	mockExp.EXPECT().AddUserExpV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
	// 修改登录ip
	mockAuthHistory.EXPECT().RecordUserLogin(gomock.Any(), gomock.Any()).Return(true, nil).Times(1)
	// 数据上报
	mockReporter.EXPECT().ReportAIAccountInfo(gomock.Any(), gomock.Any()).Times(1)
	// AccountGo
	//mockAccountGo.EXPECT().GetUserByUid(gomock.Any(), gomock.Any()).Return(&account_go.UserResp{Uid: new(uint32)}, nil).AnyTimes()
	// Security
	//mockSecurity.EXPECT().UpdateAutoProcUnregApplyStatus(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

	mgr := NewManager(clients, mockReporter)
	uid, err := mgr.CreateAIAccount(context.Background(), &pb.CreateAIAccountRequest{
		Account: &pb.CreateAIAccountRequest_Account{
			Phone:        "p",
			Password:     "pwd",
			Nickname:     "n",
			Sex:          1,
			Prologue:     "hi",
			PromptId:     2,
			TimbreId:     3,
			RoleId:       4,
			Birthday:     "2000-01-01",
			Signature:    "sig",
			PhotoImgKeys: []string{"key1", "key2"},
			AddExp:       10,
		},
	})
	if err == nil {
		t.Errorf("expect error, got nil")
	}
	if uid != 1 {
		t.Errorf("expect uid=1, got %v", uid)
	}
}

func TestManager_CreateAIAccount_FullChain_AvatarError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	clients := setupMockClients(ctrl)
	mockAccount := clients.Account.(*mock_account.MockIClient)
	//mockAccountGo := clients.AccountGo.(*mock_account_go.MockIClient)
	mockUserTagGo := clients.UserTagGo.(*mock_user_tag_go.MockIClient)
	mockExp := clients.Exp.(*mock_expsvr.MockIClient)
	mockAccountApiCenter := clients.AccountApiCenter.(*account_apicenter.MockAccountApicenterClient)
	mockAvatarMngApi := clients.AvatarMngApi.(*avatar_mng_api.MockAvatarMngApiClient)
	mockMvpProxy := clients.MvpProxy.(*mvp_proxy.MockMvpProxyClient)
	//mockSecurity := clients.Security.(*security_go.MockSecurityGoClient)
	mockAigcAccount := clients.AigcAccount.(*aigc_account.MockAigcAccountClient)
	mockReporter := mock_report.NewMockReporter(ctrl)
	mockAuthHistory := clients.AuthHistory.(*user_auth_history.MockIClient)

	// Account服务
	mockAccount.EXPECT().CreateUser(gomock.Any(), gomock.Any()).Return(&accountPB.UidResp{Uid: 1, Username: "u"}, nil).Times(1)
	mockAccount.EXPECT().UpdateUserType(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
	// 风控白名单
	mockMvpProxy.EXPECT().UpdateUserRiskList(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
	// 写AI账号表
	mockAigcAccount.EXPECT().CreateAIAccount(gomock.Any(), gomock.Any()).Return(&aigc_account.CreateAIAccountResponse{}, nil).Times(1)
	// 上传头像
	mockAvatarMngApi.EXPECT().UploadAvatar(gomock.Any(), gomock.Any()).Return(nil, errors.New("mock avatar err")).AnyTimes()
	// 生日标签
	mockUserTagGo.EXPECT().SetUserTag(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	// 相册
	mockAccountApiCenter.EXPECT().UpdatePhotoAlbum(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
	// 个性签名
	mockAccount.EXPECT().UpdateSignature(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
	// 经验值
	mockExp.EXPECT().AddUserExpV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
	// 修改登录ip
	mockAuthHistory.EXPECT().RecordUserLogin(gomock.Any(), gomock.Any()).Return(true, nil).Times(1)
	// 数据上报
	mockReporter.EXPECT().ReportAIAccountInfo(gomock.Any(), gomock.Any()).Times(1)
	//mockAccountGo.EXPECT().GetUserByUid(gomock.Any(), gomock.Any()).Return(&account_go.UserResp{Uid: new(uint32)}, nil).AnyTimes()
	//mockSecurity.EXPECT().UpdateAutoProcUnregApplyStatus(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

	mgr := NewManager(clients, mockReporter)
	uid, err := mgr.CreateAIAccount(context.Background(), &pb.CreateAIAccountRequest{
		Account: &pb.CreateAIAccountRequest_Account{
			Phone:        "p",
			Password:     "pwd",
			Nickname:     "n",
			Sex:          1,
			Prologue:     "hi",
			PromptId:     2,
			TimbreId:     3,
			Avatar:       "https://ga-album-cdnqn.52tt.com/testing-yunying/331b-197ed3fd7c3.jpeg",
			RoleId:       4,
			Birthday:     "2000-01-01",
			Signature:    "sig",
			PhotoImgKeys: []string{"key1", "key2"},
			AddExp:       10,
		},
	})
	if err == nil {
		t.Errorf("expect error, got nil")
	}
	if uid != 1 {
		t.Errorf("expect uid=1, got %v", uid)
	}
}

// UpdateAIAccount主流程全字段覆盖
func TestManager_UpdateAIAccount_FullChain_AllFields(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	clients := setupMockClients(ctrl)
	mockAigcAccount := clients.AigcAccount.(*aigc_account.MockAigcAccountClient)
	mockAccountGo := clients.AccountGo.(*mock_account_go.MockIClient)
	mockAccount := clients.Account.(*mock_account.MockIClient)
	mockMvpProxy := clients.MvpProxy.(*mvp_proxy.MockMvpProxyClient)
	mockCensoring := clients.Censoring.(*mock_censoring_proxy.MockIClient)
	mockUserTagGo := clients.UserTagGo.(*mock_user_tag_go.MockIClient)
	mockAvatarMngApi := clients.AvatarMngApi.(*avatar_mng_api.MockAvatarMngApiClient)
	mockAccountApiCenter := clients.AccountApiCenter.(*account_apicenter.MockAccountApicenterClient)
	mockExp := clients.Exp.(*mock_expsvr.MockIClient)
	mockTextClient := mock_censoring_proxy_text.NewMockTextClient(ctrl)
	mockReporter := mock_report.NewMockReporter(ctrl)
	mockAvatar := clients.Avatar.(*avatar.MockAvatarClient)
	mockAuthHistory := clients.AuthHistory.(*user_auth_history.MockIClient)

	uid := uint32(123)
	username := "testuser"
	oldNickname := "old_nick"
	newNickname := "new_nick"
	req := &pb.UpdateAIAccountRequest{
		Account: &pb.UpdateAIAccountRequest_Account{
			Uid:          uid,
			Password:     "newpwd",
			Nickname:     newNickname,
			Birthday:     "1990-01-01",
			Avatar:       "https://ga-album-cdnqn.52tt.com/testing-yunying/331b-197ed3fd7c3.jpeg",
			PhotoImgKeys: []string{"img1", "img2"},
			Signature:    "new sig",
			Sex:          2,
			AddExp:       100,
			Ip:           "*******",
			PromptId:     11,
			TimbreId:     22,
			RoleId:       33,
			Prologue:     "hello world",
		},
		UpdateFlags: map[string]bool{
			"password":       true,
			"nickname":       true,
			"birthday":       true,
			"avatar":         true,
			"photo_img_keys": true,
			"signature":      true,
			"sex":            true,
			"add_exp":        true,
			"ip":             true,
			"prompt_id":      true,
			"timbre_id":      true,
			"role_id":        true,
			"prologue":       true,
		},
	}
	// 1. GetAIAccount
	mockAigcAccount.EXPECT().GetAIAccount(gomock.Any(), gomock.Any()).Return(&aigc_account.GetAIAccountResponse{
		Account: &aigc_account.AIAccount{
			Uid:               uid,
			Password:          "oldpwd",
			IsInRiskWhiteList: false, // 触发加白
			// Nickname 字段不存在，仅用于userInfo
		},
	}, nil).Times(1)
	// 2. GetUserByUid
	mockAccountGo.EXPECT().GetUserByUid(gomock.Any(), uid).Return(&account_go.UserResp{
		Uid:          uid,
		Username:     username,
		Nickname:     oldNickname,
		IsUnregister: func(b bool) bool { return b }(false),
	}, nil).Times(1)
	// 3. UpdatePassword
	mockAccount.EXPECT().UpdatePassword(gomock.Any(), uid, gomock.Any()).Return(nil).Times(1)
	// 4. UpdateUserRiskList（加白）
	mockMvpProxy.EXPECT().UpdateUserRiskList(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
	// 5. UpdateAIAccount
	mockAigcAccount.EXPECT().UpdateAIAccount(gomock.Any(), gomock.Any()).Return(&aigc_account.UpdateAIAccountResponse{}, nil).Times(1)
	// 6. SyncScanText（昵称审核）
	mockCensoring.EXPECT().Text().Return(mockTextClient)
	mockTextClient.EXPECT().SyncScanText(gomock.Any(), gomock.Any()).Return(&arbiter.SyncTextCheckResp{
		Result: uint32(censoring_proxy.Suggestion_PASS),
	}, nil).Times(1)
	// 7. UpdateNickname
	mockAccount.EXPECT().UpdateNickname(gomock.Any(), uid, newNickname).Return(nil).Times(1)
	// 8. SetUserTag（生日）
	mockUserTagGo.EXPECT().SetUserTag(gomock.Any(), uid, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
	// 9. UploadAvatar
	mockAvatarMngApi.EXPECT().UploadAvatar(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
	// 10. UpdatePhotoAlbum
	mockAccountApiCenter.EXPECT().UpdatePhotoAlbum(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
	// 11. UpdateSignature
	mockAccount.EXPECT().UpdateSignature(gomock.Any(), uid, req.Account.Signature).Return(nil).Times(1)
	// 12. UpdateSex
	mockAccount.EXPECT().UpdateSex(gomock.Any(), uid, req.Account.Sex).Return(nil).Times(1)
	// 13. AddUserExpV2
	mockExp.EXPECT().AddUserExpV2(gomock.Any(), uid, gomock.Any()).Return(nil, nil).Times(1)
	// 修改登录ip
	mockAuthHistory.EXPECT().RecordUserLogin(gomock.Any(), gomock.Any()).Return(true, nil).Times(1)
	// 14. 数据上报
	mockAvatar.EXPECT().GetAvatarVersionByAccount(gomock.Any(), gomock.Any()).Return(nil, errors.New("mock get avatar err")).Times(1)
	mockReporter.EXPECT().ReportAIAccountInfo(gomock.Any(), gomock.Any()).Times(1)

	mgr := NewManager(clients, mockReporter)
	err := mgr.UpdateAIAccount(context.Background(), req)
	if err != nil {
		t.Fatalf("unexpected err: %v", err)
	}
}

// UnregisterAIAccount主流程
func TestManager_UnregisterAIAccount_FullChain(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	clients := setupMockClients(ctrl)
	mockAigcAccount := clients.AigcAccount.(*aigc_account.MockAigcAccountClient)
	mockSecurity := clients.Security.(*security_go.MockSecurityGoClient)
	mockMvpProxy := clients.MvpProxy.(*mvp_proxy.MockMvpProxyClient)

	uid := uint32(456)
	ttid := "test-ttid"
	// 1. GetAIAccount
	mockAigcAccount.EXPECT().GetAIAccount(gomock.Any(), gomock.Any()).Return(&aigc_account.GetAIAccountResponse{
		Account: &aigc_account.AIAccount{
			Uid:          uid,
			IsUnregister: false,
		},
	}, nil).Times(1)
	// 2. UpdateAutoProcUnregApplyStatus
	mockSecurity.EXPECT().UpdateAutoProcUnregApplyStatus(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
	// 3. BatchUnregisterAIAccount
	mockAigcAccount.EXPECT().BatchUnregisterAIAccount(gomock.Any(), gomock.Any()).Return(&aigc_account.BatchUnregisterAIAccountResponse{}, nil).Times(1)
	// 4. UpdateUserRiskList（删除白名单，允许AnyTimes）
	mockMvpProxy.EXPECT().UpdateUserRiskList(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

	mgr := NewManager(clients, nil)
	err := mgr.UnregisterAIAccount(context.Background(), uid, ttid)
	if err != nil {
		t.Fatalf("unexpected err: %v", err)
	}
}

// 账号已注销（GetAIAccount返回已注销）
func TestManager_UpdateAIAccount_AccountUnregister(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	clients := setupMockClients(ctrl)
	mockAigcAccount := clients.AigcAccount.(*aigc_account.MockAigcAccountClient)
	uid := uint32(1001)
	req := &pb.UpdateAIAccountRequest{
		Account: &pb.UpdateAIAccountRequest_Account{
			Uid: uid,
		},
		UpdateFlags: map[string]bool{},
	}
	// GetAIAccount返回已注销
	mockAigcAccount.EXPECT().GetAIAccount(gomock.Any(), gomock.Any()).Return(&aigc_account.GetAIAccountResponse{
		Account: &aigc_account.AIAccount{
			Uid:          uid,
			IsUnregister: true,
		},
	}, nil).Times(1)
	mgr := NewManager(clients, nil)
	err := mgr.UpdateAIAccount(context.Background(), req)
	if err == nil || err.Error() == "" || !strings.Contains(err.Error(), "账号不存在或已注销") {
		t.Fatalf("expect error about account unregister, got: %v", err)
	}
}

// 用户已注销（GetUserByUid返回已注销）
func TestManager_UpdateAIAccount_UserUnregister(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	clients := setupMockClients(ctrl)
	mockAigcAccount := clients.AigcAccount.(*aigc_account.MockAigcAccountClient)
	mockAccountGo := clients.AccountGo.(*mock_account_go.MockIClient)
	uid := uint32(1002)
	req := &pb.UpdateAIAccountRequest{
		Account: &pb.UpdateAIAccountRequest_Account{
			Uid: uid,
		},
		UpdateFlags: map[string]bool{},
	}
	// GetAIAccount返回正常
	mockAigcAccount.EXPECT().GetAIAccount(gomock.Any(), gomock.Any()).Return(&aigc_account.GetAIAccountResponse{
		Account: &aigc_account.AIAccount{
			Uid:          uid,
			IsUnregister: false,
		},
	}, nil).Times(1)
	// GetUserByUid返回已注销
	mockAccountGo.EXPECT().GetUserByUid(gomock.Any(), uid).Return(&account_go.UserResp{
		Uid:          uid,
		IsUnregister: func(b bool) bool { return b }(true),
	}, nil).Times(1)
	mockAigcAccount.EXPECT().BatchUnregisterAIAccount(gomock.Any(), gomock.Any()).Return(&aigc_account.BatchUnregisterAIAccountResponse{}, nil).Times(1)
	mgr := NewManager(clients, nil)
	err := mgr.UpdateAIAccount(context.Background(), req)
	if err == nil || err.Error() == "" || !strings.Contains(err.Error(), "账号不存在或已注销") {
		t.Fatalf("expect error about user unregister, got: %v", err)
	}
}

// 昵称审核不通过
func TestManager_UpdateAIAccount_NicknameAuditFailed(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	clients := setupMockClients(ctrl)
	mockAigcAccount := clients.AigcAccount.(*aigc_account.MockAigcAccountClient)
	mockAccountGo := clients.AccountGo.(*mock_account_go.MockIClient)
	//mockAccount := clients.Account.(*mock_account.MockIClient)
	//mockMvpProxy := clients.MvpProxy.(*mvp_proxy.MockMvpProxyClient)
	mockTextClient := mock_censoring_proxy_text.NewMockTextClient(ctrl)
	mockCensoring := clients.Censoring.(*mock_censoring_proxy.MockIClient)
	mockReporter := mock_report.NewMockReporter(ctrl)
	mockAvatar := clients.Avatar.(*avatar.MockAvatarClient)
	//mockAuthHistory := clients.AuthHistory.(*user_auth_history.MockIClient)

	uid := uint32(1003)
	username := "testuser3"
	oldNickname := "old_nick3"
	newNickname := "bad_nick"
	req := &pb.UpdateAIAccountRequest{
		Account: &pb.UpdateAIAccountRequest_Account{
			Uid:      uid,
			Nickname: newNickname,
		},
		UpdateFlags: map[string]bool{
			"nickname": true,
		},
	}
	// GetAIAccount
	mockAigcAccount.EXPECT().GetAIAccount(gomock.Any(), gomock.Any()).Return(&aigc_account.GetAIAccountResponse{
		Account: &aigc_account.AIAccount{
			Uid:               uid,
			IsInRiskWhiteList: true,
		},
	}, nil).Times(1)
	// GetUserByUid
	mockAccountGo.EXPECT().GetUserByUid(gomock.Any(), uid).Return(&account_go.UserResp{
		Uid:          uid,
		Username:     username,
		Nickname:     oldNickname,
		IsUnregister: func(b bool) bool { return b }(false),
	}, nil).Times(1)
	// UpdateAIAccount
	mockAigcAccount.EXPECT().UpdateAIAccount(gomock.Any(), gomock.Any()).Return(&aigc_account.UpdateAIAccountResponse{}, nil).Times(1)
	// SyncScanText返回不通过
	mockCensoring.EXPECT().Text().Return(mockTextClient)
	mockTextClient.EXPECT().SyncScanText(gomock.Any(), gomock.Any()).Return(&arbiter.SyncTextCheckResp{
		Result: uint32(censoring_proxy.Suggestion_REJECT),
	}, nil).Times(1)
	// 没有修改头像，数据上报需要查询旧头像
	mockAvatar.EXPECT().GetAvatarVersionByAccount(gomock.Any(), gomock.Any()).Return(&avatar.GetAvatarVersionByAccountResp{}, errors.New("mock get avatar err")).Times(1)
	//// 修改登录ip
	//mockAuthHistory.EXPECT().RecordUserLogin(gomock.Any(), gomock.Any()).Return(true, nil).Times(1)
	// 数据上报
	mockReporter.EXPECT().ReportAIAccountInfo(gomock.Any(), gomock.Any()).Times(1)
	mgr := NewManager(clients, mockReporter)
	err := mgr.UpdateAIAccount(context.Background(), req)
	if err == nil || err.Error() == "" || !strings.Contains(err.Error(), "昵称审核不通过") {
		t.Fatalf("expect error about nickname audit, got: %v", err)
	}
}
