package account

import (
	"context"
	"crypto/md5" // #nosec
	"encoding/hex"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	serviceinfo "gitlab.ttyuyin.com/avengers/tyr/core/service/basepb/info"
	avatar_pkg "gitlab.ttyuyin.com/bizFund/bizFund/pkg/avatar"
	user_auth_history "golang.52tt.com/clients/user-auth-history"
	"golang.52tt.com/pkg/protocol/grpc"
	account_go "golang.52tt.com/protocol/services/account-go"
	"golang.52tt.com/protocol/services/avatar"
	censoring_proxy "golang.52tt.com/protocol/services/censoring-proxy"
	config "golang.52tt.com/services/aigc/aigc-account-middle/internal/config/ttconfig/aigc_account_middle"
	"golang.52tt.com/services/aigc/aigc-account-middle/internal/report"

	account_apicenter "golang.52tt.com/protocol/services/account-apicenter"

	"github.com/pkg/errors"
	arbiter "golang.52tt.com/protocol/services/cybros/arbiter/v2"

	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/mvp_proxy"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/security_go"
	user_tag_v2 "golang.52tt.com/protocol/app/user-tag-v2"
	avatar_mng_api "golang.52tt.com/protocol/services/avatar-mng-api"

	"gitlab.ttyuyin.com/tyr/x/log"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
	pb "golang.52tt.com/protocol/services/aigc/aigc-account-middle"
	expPB "golang.52tt.com/protocol/services/expsvr"
	user_tag_go "golang.52tt.com/protocol/services/user-tag-go"
	"golang.52tt.com/services/aigc/aigc-account-middle/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-account-middle/internal/rpc"
)

const (
	defaultIP = "************" // 没配IP默认填一个广东省IP, 与server保持一致
)

// Manager 实现 AIAccountManager 接口
type Manager struct {
	clients  *rpc.Clients
	reporter report.Reporter
}

func NewManager(clients *rpc.Clients, reporter report.Reporter) mgr.AIAccountManager {
	return &Manager{
		clients:  clients,
		reporter: reporter,
	}
}

func (m *Manager) Close() {
	m.reporter.Close()
}

// CreateAIAccount 创建AI账号
func (m *Manager) CreateAIAccount(ctx context.Context, req *pb.CreateAIAccountRequest) (uint32, error) {
	account := req.GetAccount()

	// 调用account服务创建账号
	createUserReq := &accountPB.CreateUserReq{
		Phone:    account.GetPhone(),
		Password: getMD5Hash(account.GetPassword()),
		Nickname: account.GetNickname(),
		Sex:      int32(account.GetSex()),
		Source:   uint32(accountPB.USER_SOURCE_USER_SOURCE_ROBOT),
	}

	createUserResp, err := m.clients.Account.CreateUser(ctx, createUserReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateAIAccount CreateUser err: %v, req: %+v", err, account)
		return 0, err
	}

	var (
		uid      = createUserResp.GetUid()
		alias    = createUserResp.GetUsername()
		username = formatUsername(alias)
	)

	log.InfoWithCtx(ctx, "CreateAIAccount account.CreateUser success, uid: %d", uid)

	ctx = setServiceInfoCtxUid(ctx, uid)
	err = m.clients.Account.UpdateUserType(ctx, uid, uint32(accountPB.USER_TYPE_USER_TYPE_ROBOT))
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateAIAccount UpdateUserType uid(%d) err: %v", uid, err)
		return 0, err
	}

	var errMsg strings.Builder

	aigcAccountReq := &aigc_account.CreateAIAccountRequest{
		Account: &aigc_account.AIAccount{
			Uid:                  uid,
			Password:             account.GetPassword(),
			Ip:                   account.GetIp(),
			PromptId:             account.GetPromptId(),
			TimbreId:             account.GetTimbreId(),
			RoleId:               account.GetRoleId(),
			Prologue:             account.GetPrologue(),
			Identity:             account.GetIdentity(),
			Desc:                 account.GetDesc(),
			Sort:                 account.GetSort(),
			AccountTags:          account.GetAccountTags(),
			IsShowInChatCardWall: account.GetIsShowInChatCardWall(),
		},
	}

	// 账号加白
	err = m.updateAccountRiskStatus(ctx, uid, mvp_proxy.UserRiskListActionType_USER_RISK_LIST_ACTION_TYPE_INSERT)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateAIAccount addAccountToRiskWhitelist err: %v, uid: %d", err, uid)
		// 加白失败不影响账号创建
		errMsg.WriteString(err.Error() + "; ")
	} else {
		aigcAccountReq.Account.IsInRiskWhiteList = true
	}

	// 写入AI账号表
	_, err = m.clients.AigcAccount.CreateAIAccount(ctx, aigcAccountReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateAIAccount aigc-account.CreateAIAccount err: %v, uid: %d", err, uid)
		// 如果创建AI账号失败，尝试注销账号
		_ = m.unregisterAccount(ctx, uid, username)
		return 0, err
	}
	log.InfoWithCtx(ctx, "CreateAIAccount aigc-account.CreateAIAccount success, uid: %d", uid)

	reportAccountData := report.AIAccountData{
		Uid:      uid,
		Nickname: account.GetNickname(),
		TTID:     alias,
		OpType:   report.OpTypeCreate,
		Password: account.GetPassword(),
		Sex:      account.GetSex(),
		Avatar:   account.GetAvatar(),
	}

	// 上传头像
	err = m.uploadAvatar(ctx, uid, username, account.GetAvatar())
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateAIAccount uploadAvatar err: %v, uid: %d", err, uid)
		// 上传头像失败不影响账号创建
		errMsg.WriteString(err.Error() + "; ")
	}

	// 修改生日标签
	err = m.updateBirthdayTag(ctx, uid, account.GetBirthday())
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateAIAccount updateBirthdayTag err: %v, uid: %d, birthday: %s", err, uid, account.GetBirthday())
		// 修改生日标签失败不影响账号创建
		errMsg.WriteString(err.Error() + "; ")
	}

	// 更新相册
	if len(account.GetPhotoImgKeys()) > 0 {
		err = m.updatePhotoAlbum(ctx, uid, account.GetPhotoImgKeys())
		if err != nil {
			log.ErrorWithCtx(ctx, "CreateAIAccount updatePhotoAlbum err: %v, uid: %d", err, uid)
			// 更新相册失败不影响账号创建
			errMsg.WriteString(err.Error() + "; ")
		}
	}

	// 更新个性签名
	if account.GetSignature() != "" {
		err = m.updateSignature(ctx, uid, account.GetSignature())
		if err != nil {
			log.ErrorWithCtx(ctx, "CreateAIAccount updateSignature err: %v, uid: %d, signature: %s", err, uid, account.GetSignature())
			// 更新个性签名失败不影响账号创建
			errMsg.WriteString(err.Error() + "; ")
		} else {
			reportAccountData.Signature = account.GetSignature()
		}
	}

	// 发放经验值
	if account.GetAddExp() > 0 {
		err = m.addUserExp(ctx, uid, account.GetAddExp())
		if err != nil {
			log.ErrorWithCtx(ctx, "CreateAIAccount addUserExp err: %v, uid: %d, exp: %d", err, uid, account.GetAddExp())
			// 发放经验值失败不影响账号创建
			errMsg.WriteString(err.Error() + "; ")
		}
	}

	// 为ai账号设置登录ip
	loginIp := account.GetIp()
	if loginIp == "" {
		loginIp = defaultIP
	}
	err = m.updateAIAccountLoginIp(ctx, uid, loginIp)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateAIAccount updateAIAccountLoginIp err: %v, uid: %d, ip: %s", err, uid, loginIp)
		// 更新登录IP失败不影响账号创建
		errMsg.WriteString(err.Error() + "; ")
	}

	// 数据上报
	m.reporter.ReportAIAccountInfo(ctx, reportAccountData)

	// 将多个错误信息合并成一个
	if errMsg.Len() > 0 {
		return uid, protocol.NewExactServerError(nil, status.ErrExternalSystemFailed, fmt.Sprintf("设置以下信息失败，请重试: %s", errMsg.String()))
	}

	//log.InfoWithCtx(ctx, "CreateAIAccount success, uid: %d", uid)
	return uid, nil
}

// UpdateAIAccount 更新AI账号
func (m *Manager) UpdateAIAccount(ctx context.Context, req *pb.UpdateAIAccountRequest) error {
	reqAccount := req.GetAccount()
	uid := reqAccount.GetUid()
	updateFlags := req.GetUpdateFlags()
	ctx = setServiceInfoCtxUid(ctx, uid)

	// 获取AI账号信息
	aiAccountResp, err := m.clients.AigcAccount.GetAIAccount(ctx, &aigc_account.GetAIAccountRequest{
		Uid:       uid,
		ReqSource: aigc_account.GetAIAccountSource_ADMIN_BACKEND,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAIAccount GetAIAccount err: %v, uid: %d", err, uid)
		return err
	}
	if aiAccountResp.GetAccount() == nil || aiAccountResp.GetAccount().GetIsUnregister() {
		log.WarnWithCtx(ctx, "UpdateAIAccount aiAccountResp.GetAccount() is nil or has been unregistered, uid: %d", uid)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "账号不存在或已注销")
	}

	userInfo, err := m.clients.AccountGo.GetUserByUid(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAIAccount GetUserInfo err: %v, uid: %d", err, uid)
		return err
	}
	if userInfo == nil || userInfo.GetIsUnregister() {
		log.WarnWithCtx(ctx, "UpdateAIAccount userInfo not found or isUnregister, uid: %d", uid)
		// 调用aigc-account服务修改AI账号注销状态
		err = m.unregisterAIAccount(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateAIAccount unregisterAIAccount err: %v, uid: %d", err, uid)
			return err
		}

		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "账号不存在或已注销")
	}

	newAIAccount := aiAccountResp.GetAccount()
	var errMsg strings.Builder
	// 更新密码
	if isFieldNeedUpdate(updateFlags, "password") && reqAccount.GetPassword() != "" {
		err = m.updatePassword(ctx, uid, reqAccount.GetPassword())
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateAIAccount updatePassword err: %v, uid: %d", err, uid)
			errMsg.WriteString(err.Error() + "; ")
		} else {
			newAIAccount.Password = reqAccount.GetPassword()
		}
	}

	// 创建账号时加白不成功的账号重试加白
	if !newAIAccount.GetIsInRiskWhiteList() {
		err = m.updateAccountRiskStatus(ctx, uid, mvp_proxy.UserRiskListActionType_USER_RISK_LIST_ACTION_TYPE_INSERT)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateAIAccount addAccountToRiskWhitelist err: %v, uid: %d", err, uid)
			errMsg.WriteString(err.Error() + "; ")
		} else {
			newAIAccount.IsInRiskWhiteList = true
		}
	}

	// 更新AI账号信息
	err = m.updateAIAccount(ctx, uid, newAIAccount, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAIAccount updateAIAccount err: %v, uid: %d", err, uid)
		return err
	}

	reportAccountData := report.AIAccountData{
		Uid:       uid,
		Nickname:  userInfo.GetNickname(),
		TTID:      userInfo.GetAlias(),
		OpType:    report.OpTypeUpdate,
		Signature: userInfo.GetSignature(),
		Password:  newAIAccount.GetPassword(),
		Sex:       uint32(userInfo.GetSex()),
		Avatar:    m.getAvatarURL(ctx, userInfo.GetUsername()),
	}

	// 昵称审核与更新
	if isFieldNeedUpdate(updateFlags, "nickname") && reqAccount.GetNickname() != "" {
		if reqAccount.GetNickname() != userInfo.GetNickname() {
			err := m.updateNickname(ctx, userInfo, reqAccount.GetNickname())
			if err != nil {
				log.ErrorWithCtx(ctx, "UpdateAIAccount updateNickname err: %v, uid: %d, nickname: %s", err, uid, reqAccount.GetNickname())
				errMsg.WriteString(err.Error() + "; ")
			} else {
				reportAccountData.Nickname = reqAccount.GetNickname()
			}
		}
	}

	// 修改绑定手机号
	if isFieldNeedUpdate(updateFlags, "phone") && reqAccount.GetPhone() != "" {
		err := m.bindPhone(ctx, uid, reqAccount.GetPhone())
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateAIAccount bindPhone err: %v, uid: %d, phone: %s", err, uid, reqAccount.GetPhone())
			errMsg.WriteString(err.Error() + "; ")
		}
	}

	// 修改生日标签
	if isFieldNeedUpdate(updateFlags, "birthday") {
		err = m.updateBirthdayTag(ctx, uid, reqAccount.GetBirthday())
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateAIAccount updateBirthdayTag err: %v, uid: %d, birthday: %s",
				err, uid, reqAccount.GetBirthday())
			errMsg.WriteString(err.Error() + "; ")
		}
	}

	// 上传头像
	if isFieldNeedUpdate(updateFlags, "avatar") && len(reqAccount.GetAvatar()) > 0 {
		err = m.uploadAvatar(ctx, uid, userInfo.GetUsername(), reqAccount.GetAvatar())
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateAIAccount uploadAvatar err: %v, uid: %d", err, uid)
			errMsg.WriteString(err.Error() + "; ")
		} else {
			reportAccountData.Avatar = reqAccount.GetAvatar()
		}
	}

	// 更新相册
	if isFieldNeedUpdate(updateFlags, "photo_img_keys") {
		err = m.updatePhotoAlbum(ctx, uid, reqAccount.GetPhotoImgKeys())
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateAIAccount updatePhotoAlbum err: %v, uid: %d", err, uid)
			errMsg.WriteString(err.Error() + "; ")
		}
	}

	// 更新个性签名
	if isFieldNeedUpdate(updateFlags, "signature") {
		err = m.updateSignature(ctx, uid, reqAccount.GetSignature())
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateAIAccount updateSignature err: %v, uid: %d, signature: %s", err, uid, reqAccount.GetSignature())
			errMsg.WriteString(err.Error() + "; ")
		} else {
			reportAccountData.Signature = reqAccount.GetSignature()
		}
	}

	// 更新性别
	if isFieldNeedUpdate(updateFlags, "sex") {
		err = m.updateSex(ctx, uid, reqAccount.GetSex())
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateAIAccount updateSex err: %v, uid: %d, sex: %d", err, uid, reqAccount.GetSex())
			errMsg.WriteString(err.Error() + "; ")
		} else {
			reportAccountData.Sex = reqAccount.GetSex()
		}
	}

	// 发放经验值
	if isFieldNeedUpdate(updateFlags, "add_exp") && reqAccount.GetAddExp() > 0 {
		err = m.addUserExp(ctx, uid, reqAccount.GetAddExp())
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateAIAccount addUserExp err: %v, uid: %d, exp: %d", err, uid, reqAccount.GetAddExp())
			errMsg.WriteString(err.Error() + "; ")
		}
	}

	// 更新ai账号登录ip
	if isFieldNeedUpdate(updateFlags, "ip") {
		loginIp := reqAccount.GetIp()
		if loginIp == "" {
			loginIp = defaultIP
		}

		err = m.updateAIAccountLoginIp(ctx, uid, loginIp)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateAIAccount updateAIAccountLoginIp err: %v, uid: %d, ip: %s", err, uid, loginIp)
			errMsg.WriteString(err.Error() + "; ")
		}
	}

	// 数据上报
	m.reporter.ReportAIAccountInfo(ctx, reportAccountData)

	// 返回合并的错误信息
	if errMsg.Len() > 0 {
		return protocol.NewExactServerError(nil, status.ErrExternalSystemFailed, fmt.Sprintf("修改以下信息失败，请重试: %s", errMsg.String()))
	}

	//log.InfoWithCtx(ctx, "UpdateAIAccount success, uid: %d", uid)
	return nil
}

// UnregisterAIAccount 注销AI账号
func (m *Manager) UnregisterAIAccount(ctx context.Context, uid uint32, ttid string) error {
	ctx = setServiceInfoCtxUid(ctx, uid)
	// 获取AI账号信息
	aiAccountResp, err := m.clients.AigcAccount.GetAIAccount(ctx, &aigc_account.GetAIAccountRequest{
		Uid: uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "UnregisterAIAccount GetAIAccount err: %v, uid: %d", err, uid)
		return err
	}
	if aiAccountResp.GetAccount() == nil || aiAccountResp.GetAccount().GetIsUnregister() {
		log.InfoWithCtx(ctx, "UnregisterAIAccount aiAccountResp.GetAccount() is nil or has been unregistered, uid: %d", uid)
		return nil
	}

	// 调用security-go服务更新账号注销状态
	err = m.unregisterAccount(ctx, uid, ttid)
	if err != nil {
		return err
	}

	// 调用aigc-account服务修改AI账号注销状态
	err = m.unregisterAIAccount(ctx, uid)
	if err != nil {
		//log.ErrorWithCtx(ctx, "UnregisterAIAccount unregisterAIAccount err: %v, uid: %d", err, uid)
		return err
	}

	// 从风控白名单移除（失败不影响注销逻辑）
	_ = m.updateAccountRiskStatus(ctx, uid, mvp_proxy.UserRiskListActionType_USER_RISK_LIST_ACTION_TYPE_DELETE)

	//log.InfoWithCtx(ctx, "UnregisterAIAccount success, uid: %d", uid)
	return nil
}

func (m *Manager) unregisterAccount(ctx context.Context, uid uint32, ttid string) error {
	_, err := m.clients.Security.UpdateAutoProcUnregApplyStatus(ctx, &security_go.UpdateAutoProcUnregApplyStatusReq{
		Uid:            uid,
		Ttid:           ttid,
		Status:         1, // AUDIT_WAIT 待审核
		IsCurrentUnreg: true,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "unregisterAccount UpdateAutoProcUnregApplyStatus err: %v, uid: %d", err, uid)
		return err
	}
	log.InfoWithCtx(ctx, "unregisterAccount UpdateAutoProcUnregApplyStatus success, uid: %d", uid)
	return nil
}

// updateNickname 修改昵称
func (m *Manager) updateNickname(ctx context.Context, userInfo *account_go.UserResp, nickname string) error {
	// 同步审核昵称
	req := &arbiter.SyncTextCheckReq{
		Context: &arbiter.TaskContext{
			Category: "MODIFY_NICKNAME",
			UserInfo: &arbiter.User{
				Id:       uint64(userInfo.GetUid()),
				Alias:    userInfo.GetUsername(),
				Nickname: nickname,
				Phone:    userInfo.GetPhone(),
			},
			BelongObjId: userInfo.GetUsername(),
			DeviceInfo:  &arbiter.Device{},
		},
		Text: nickname,
	}

	resp, err := m.clients.Censoring.Text().SyncScanText(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "updateNickname SyncScanText err: %v, uid: %d, nickname: %s", err, userInfo.GetUid(), nickname)
		return err
	}

	if resp.GetResult() != uint32(censoring_proxy.Suggestion_PASS) {
		log.WarnWithCtx(ctx, "updateNickname not pass, uid: %d, nickname: %s, result: %v", userInfo.GetUid(), nickname, resp.GetResult())
		return errors.New("昵称审核不通过")
	}

	err = m.clients.Account.UpdateNickname(ctx, userInfo.GetUid(), nickname)
	if err != nil {
		log.ErrorWithCtx(ctx, "updateNickname UpdateNickname err: %v, uid: %d, nickname: %s", err, userInfo.GetUid(), nickname)
		return errors.New("修改昵称失败")
	}

	return nil
}

// updateAccountRiskStatus 更新账号风控状态
func (m *Manager) updateAccountRiskStatus(ctx context.Context, uid uint32, actionType mvp_proxy.UserRiskListActionType) error {

	_, err := m.clients.MvpProxy.UpdateUserRiskList(ctx, &mvp_proxy.UpdateUserRiskListRequest{
		ListId:     config.GetAigcAccountMiddleConfig().GetRiskWhitelistId(),
		Editor:     "aigc-account-middle",
		ActionType: actionType,
		UidList:    []uint32{uid},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "updateAccountRiskStatus err: %v, uid: %d, actionType: %d", err, uid, actionType)
		if actionType == mvp_proxy.UserRiskListActionType_USER_RISK_LIST_ACTION_TYPE_INSERT {
			return errors.New("AI账号加入风控白名单失败，将在下次修改信息时重试")
		}
		return err
	}
	log.InfoWithCtx(ctx, "updateAccountRiskStatus success, uid: %d, actionType: %d", uid, actionType)
	return nil
}

// updateBirthdayTag 更新生日标签
func (m *Manager) updateBirthdayTag(ctx context.Context, uid uint32, birthday string) error {
	if len(birthday) == 0 {
		return nil
	}
	tagList := []*user_tag_go.UserTagBase{
		{
			TagType: uint32(user_tag_v2.UserTagTypeV2_TAG_TYPE_BIRTHDAY),
			TagName: birthday,
			TagId:   0,
		},
	}

	// 调用设置标签接口
	err := m.clients.UserTagGo.SetUserTag(ctx, uid, uint32(user_tag_v2.UserTagTypeV2_TAG_TYPE_BIRTHDAY), 1, tagList)
	if err != nil {
		log.ErrorWithCtx(ctx, "updateBirthdayTag SetUserTag err: %v, uid: %d, birthday: %s", err, uid, birthday)
		return errors.New("修改生日标签失败")
	}

	log.InfoWithCtx(ctx, "updateBirthdayTag success, uid: %d, birthday: %s", uid, birthday)
	return nil
}

// uploadAvatar 上传头像
func (m *Manager) uploadAvatar(ctx context.Context, uid uint32, username string, avatarUrl string) error {
	if len(avatarUrl) == 0 {
		log.WarnWithCtx(ctx, "uploadAvatar avatarUrl is empty, uid: %d, username: %s", uid, username)
		return nil
	}
	// 从avatarUrl获取头像数据
	avatarData, err := downloadAvatar(ctx, avatarUrl)
	if err != nil {
		log.ErrorWithCtx(ctx, "uploadAvatar DownloadAvatar err: %v, uid: %d, username: %s", err, uid, username)
		return errors.New("下载头像失败")
	}
	// 调用头像上传接口
	req := &avatar_mng_api.UploadAvatarReq{
		Entity: &avatar_mng_api.Entity{
			Type: avatar_mng_api.EntityType_ENTITY_TYPE_USER,
			Id:   username,
		},
		ImageData:    avatarData,
		IsNeedAudit:  true,
		UploadSource: avatar_mng_api.UplaodSource_UPLOAD_SOURCE_CMD,
		ContextInfo: &avatar_mng_api.ContextInfo{
			OpUid: uid,
		},
	}

	_, err = m.clients.AvatarMngApi.UploadAvatar(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "uploadAvatar err: %v, uid: %d, username: %s", err, uid, username)
		return errors.New("修改头像失败")
	}

	log.InfoWithCtx(ctx, "uploadAvatar success, uid: %d, username: %s", uid, username)
	return nil
}

// updatePhotoAlbum 更新相册
func (m *Manager) updatePhotoAlbum(ctx context.Context, uid uint32, photoImgKeys []string) error {
	_, err := m.clients.AccountApiCenter.UpdatePhotoAlbum(ctx, &account_apicenter.UpdatePhotoAlbumRequest{
		Uid: uid,
		ClientInfo: &account_apicenter.ClientInfo{
			MarketId:   0, // TT
			ClientType: 0, // iOS跟安卓共用同个域名
		},
		NewImgKeyList: photoImgKeys,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "updatePhotoAlbum UpdatePhotoAlbum err: %v, uid: %d, photoImgKeys: %v", err, uid, photoImgKeys)
		return errors.New("更新相册失败")
	}

	log.InfoWithCtx(ctx, "updatePhotoAlbum success, uid: %d, photo_count: %d", uid, len(photoImgKeys))
	return nil
}

// updateSignature 更新个性签名
func (m *Manager) updateSignature(ctx context.Context, uid uint32, signature string) error {
	err := m.clients.Account.UpdateSignature(ctx, uid, signature)
	if err != nil {
		log.ErrorWithCtx(ctx, "updateSignature UpdateSignature err: %v, uid: %d, signature: %s", err, uid, signature)
		return errors.New("更新个性签名失败")
	}

	log.InfoWithCtx(ctx, "updateSignature success, uid: %d, signature: %s", uid, signature)
	return nil
}

// updateSex 更新性别
func (m *Manager) updateSex(ctx context.Context, uid uint32, sex uint32) error {
	err := m.clients.Account.UpdateSex(ctx, uid, sex)
	if err != nil {
		log.ErrorWithCtx(ctx, "updateSex UpdateSex err: %v, uid: %d, sex: %d", err, uid, sex)
		return err
	}

	log.InfoWithCtx(ctx, "updateSex success, uid: %d, sex: %d", uid, sex)
	return nil
}

// addUserExp 增加用户经验值
func (m *Manager) addUserExp(ctx context.Context, uid uint32, exp uint32) error {
	if exp == 0 {
		return nil
	}
	seq := time.Now().Unix()
	// 调用经验值服务
	req := &expPB.AddUserExpReq{
		Uid:         uid,
		Exp:         int32(exp),
		MissionKey:  fmt.Sprintf("aigc_account_%d_%d", uid, seq),
		MissionDesc: "运营后台给AI账号发放经验值",
	}

	_, err := m.clients.Exp.AddUserExpV2(ctx, uid, req)
	if err != nil {
		return errors.New("发放经验值失败")
	}

	log.InfoWithCtx(ctx, "addUserExp success, uid: %d, exp: %d", uid, exp)
	return nil
}

// updateAIAccount 更新AI账号信息
func (m *Manager) updateAIAccount(ctx context.Context, uid uint32, aiAccount *aigc_account.AIAccount, req *pb.UpdateAIAccountRequest) error {
	updateFlags := req.GetUpdateFlags()

	if isFieldNeedUpdate(updateFlags, "ip") {
		aiAccount.Ip = req.GetAccount().GetIp()
	}
	if isFieldNeedUpdate(updateFlags, "prompt_id") {
		aiAccount.PromptId = req.GetAccount().GetPromptId()
	}
	if isFieldNeedUpdate(updateFlags, "timbre_id") {
		aiAccount.TimbreId = req.GetAccount().GetTimbreId()
	}
	if isFieldNeedUpdate(updateFlags, "role_id") {
		aiAccount.RoleId = req.GetAccount().GetRoleId()
	}
	if isFieldNeedUpdate(updateFlags, "prologue") {
		aiAccount.Prologue = req.GetAccount().GetPrologue()
	}
	if isFieldNeedUpdate(updateFlags, "identity") {
		aiAccount.Identity = req.GetAccount().GetIdentity()
	}
	if isFieldNeedUpdate(updateFlags, "desc") {
		aiAccount.Desc = req.GetAccount().GetDesc()
	}
	if isFieldNeedUpdate(updateFlags, "sort") {
		aiAccount.Sort = req.GetAccount().GetSort()
	}
	if isFieldNeedUpdate(updateFlags, "account_tags") {
		aiAccount.AccountTags = req.GetAccount().GetAccountTags()
	}
	if isFieldNeedUpdate(updateFlags, "is_show_in_chat_card_wall") {
		aiAccount.IsShowInChatCardWall = req.GetAccount().GetIsShowInChatCardWall()
	}

	// 调用aigc-account服务更新
	_, err := m.clients.AigcAccount.UpdateAIAccount(ctx, &aigc_account.UpdateAIAccountRequest{
		Account: aiAccount,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "updateAIAccount aigc-account.UpdateAIAccount err: %v, uid: %d", err, uid)
		return err
	}
	log.InfoWithCtx(ctx, "updateAIAccount aigc-account.UpdateAIAccount success, uid: %d", uid)
	return nil
}

// updatePassword 更新密码
func (m *Manager) updatePassword(ctx context.Context, uid uint32, password string) error {
	err := m.clients.Account.UpdatePassword(ctx, uid, getMD5Hash(password))
	if err != nil {
		log.ErrorWithCtx(ctx, "updatePassword UpdatePassword err: %v, uid: %d", err, uid)
		return err
	}
	log.InfoWithCtx(ctx, "updatePassword success, uid: %d", uid)
	return nil
}

// unregisterAIAccount 删除AI账号
func (m *Manager) unregisterAIAccount(ctx context.Context, uid uint32) error {
	return m.batchUnregisterAIAccount(ctx, []uint32{uid})
}

// batchUnregisterAIAccount 批量删除AI账号
func (m *Manager) batchUnregisterAIAccount(ctx context.Context, uidList []uint32) error {
	_, err := m.clients.AigcAccount.BatchUnregisterAIAccount(ctx, &aigc_account.BatchUnregisterAIAccountRequest{
		UidList: uidList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "batchUnregisterAIAccount BatchUnregisterAIAccount err: %v, uidList: %v", err, uidList)
		return err
	}
	log.InfoWithCtx(ctx, "batchUnregisterAIAccount success, uidList: %v", uidList)
	return nil
}

func (m *Manager) getAvatarURL(ctx context.Context, account string) string {
	resp, err := m.clients.Avatar.GetAvatarVersionByAccount(ctx, &avatar.GetAvatarVersionByAccountReq{
		Account: account,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getAvatarURL GetAvatarVersionByAccount err: %v, account: %s", err, account)
		return ""
	}
	return avatar_pkg.GetAvatarUrl(ctx, account, resp.GetVersion(), 0, 0)
}

func (m *Manager) bindPhone(ctx context.Context, uid uint32, phone string) error {
	err := m.clients.Account.BindPhoneWithUid(ctx, &accountPB.BindPhoneWithUidReq{
		Uid:      uid,
		Phone:    phone,
		BindType: accountPB.PhoneBindType_LOGIN_PHONE,
		Scene:    "aigc-account-middle",
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "bindPhone BindPhoneWithUid err: %v, uid: %d, phone: %s", err, uid, phone)
		return errors.New(err.Message())
	}
	log.InfoWithCtx(ctx, "bindPhone success, uid: %d, phone: %s", uid, phone)
	return nil
}

// updateAIAccountLoginIp 运营后台修改AI账号ip时，更新app登录状态的ip
func (m *Manager) updateAIAccountLoginIp(ctx context.Context, uid uint32, ip string) error {
	// 更新登录信息
	_, err := m.clients.AuthHistory.RecordUserLogin(ctx, &user_auth_history.RecordUserLoginReq{
		Info: &user_auth_history.UserLoginInfo{
			Uid:      uint64(uid),
			ClientIp: ip,
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "updateAIAccountLoginIp RecordUserLogin err: %v, uid: %d, ip: %s", err, uid, ip)
		return errors.New("更新登录ip信息失败")
	}
	log.InfoWithCtx(ctx, "updateAIAccountLoginIp success, uid: %d, ip: %s", uid, ip)
	return nil

}

// setServiceInfoCtxUid 把uid设置到ctx中
func setServiceInfoCtxUid(ctx context.Context, uid uint32) context.Context {
	sv, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok || sv == nil || sv.UserID == 0 {
		if sv == nil {
			sv = &serviceinfo.ServiceInfo{UserID: uid}
		} else {
			sv.UserID = uid
		}
		ctx = grpc.WithServiceInfo(ctx, sv)
	}
	return ctx
}

// formatUsername 格式化用户名
func formatUsername(username string) string {
	if strings.HasPrefix(username, "tt") {
		return username
	}
	return fmt.Sprintf("tt%s", username)
}

// downloadAvatar 从avatarUrl下载头像
func downloadAvatar(ctx context.Context, avatarUrl string) ([]byte, error) {
	resp, err := http.Get(avatarUrl) // #nosec
	if err != nil {
		log.ErrorWithCtx(ctx, "downloadAvatar http.Get err: %v, avatarUrl: %s", err, avatarUrl)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.ErrorWithCtx(ctx, "downloadAvatar http.Get statusCode: %d, avatarUrl: %s", resp.StatusCode, avatarUrl)
		return nil, errors.New("下载头像失败")
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "downloadAvatar io.ReadAll err: %v, avatarUrl: %s", err, avatarUrl)
		return nil, err
	}

	return body, nil
}

// getMD5Hash 获取MD5哈希值
func getMD5Hash(s string) string {
	h := md5.New() // #nosec
	h.Write([]byte(s))
	return hex.EncodeToString(h.Sum(nil))
}

// isFieldNeedUpdate 判断字段是否需要更新
func isFieldNeedUpdate(updateFlags map[string]bool, field string) bool {
	if updateFlags == nil {
		return false
	}
	return updateFlags[field]
}
