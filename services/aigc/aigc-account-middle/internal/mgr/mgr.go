package mgr

import (
	"context"

	pb "golang.52tt.com/protocol/services/aigc/aigc-account-middle"
)

//go:generate mockgen -destination=mocks/account.go -package=mocks golang.52tt.com/services/aigc/aigc-account-middle/internal/mgr AIAccountManager
type AIAccountManager interface {
	// CreateAIAccount 创建AI账号
	CreateAIAccount(ctx context.Context, req *pb.CreateAIAccountRequest) (uint32, error)
	// UpdateAIAccount 更新AI账号
	UpdateAIAccount(ctx context.Context, req *pb.UpdateAIAccountRequest) error
	// UnregisterAIAccount 注销AI账号
	UnregisterAIAccount(ctx context.Context, uid uint32, ttid string) error
}

//go:generate mockgen -destination=mocks/ai_post.go -package=mocks golang.52tt.com/services/aigc/aigc-account-middle/internal/mgr AIPostManager
type AIPostManager interface {
	BatchGetAiPost(ctx context.Context, req *pb.GetAiPostReq) ([]*pb.PostInfo, uint32, error)
}
