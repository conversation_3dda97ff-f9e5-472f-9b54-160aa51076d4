package internal

import (
	"context"
	"encoding/json"
	"golang.52tt.com/pkg/log"
	aigc_account_middle "golang.52tt.com/protocol/services/aigc/aigc-account-middle"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
	"testing"
)

var svrClient aigc_account_middle.AigcAccountMiddleClient

func init() {
	logicClient := "aigc-account-middle.52tt.local"
	// test
	//socialCC, _ := grpc.NewClient("*********:80", grpc.WithAuthority(logicClient),
	//	grpc.WithTransportCredentials(insecure.NewCredentials()))
	// staging
	socialCC, _ := grpc.NewClient("*********:80", grpc.WithAuthority(logicClient),
		grpc.WithTransportCredentials(insecure.NewCredentials()))

	svrClient = aigc_account_middle.NewAigcAccountMiddleClient(socialCC)
}

func TestAddAiPost(t *testing.T) {
	md := metadata.Pairs("x-qw-traffic-mark", "sk", "req_uid", "2630911")
	ctx := metadata.NewOutgoingContext(context.Background(), md)

	resp, err := svrClient.AddAiPost(ctx, &aigc_account_middle.AddAiPostReq{
		Uid:    2630916,
		PostId: "687e10edb0d1cf189083b556",
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AddAiPost() error = %v", err)
		return
	}

	respStr, _ := json.Marshal(resp)
	log.InfoWithCtx(ctx, "AddAiPost resp: %s", string(respStr))
}

func TestGetAiPost(t *testing.T) {
	md := metadata.Pairs("x-qw-traffic-mark", "sk", "req_uid", "2630911")
	ctx := metadata.NewOutgoingContext(context.Background(), md)

	resp, err := svrClient.GetAiPost(ctx, &aigc_account_middle.GetAiPostReq{
		Uid: 2630911,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAiPost() error = %v", err)
		return
	}
	respStr, _ := json.Marshal(resp)
	log.InfoWithCtx(ctx, "GetAiPost resp: %s", string(respStr))
}
