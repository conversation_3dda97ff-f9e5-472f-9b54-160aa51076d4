package eventlink

import (
	"context"
	"fmt"
	"golang.52tt.com/services/aigc/aigc-account-middle/internal/event"

	middleware_event "gitlab.ttyuyin.com/tt-infra/middleware/event"
	middleware_publisher "gitlab.ttyuyin.com/tt-infra/middleware/kafka/publisher"
	middleware_subscriber "gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"

	"golang.52tt.com/pkg/log"
)

type eventBus struct {
	factory *middleware_event.Factory

	publishers  map[string]*publisher
	subscribers map[string]*subscriber
}

type publisher struct {
	middleware_publisher.Publisher
}

type subscriber struct {
	middleware_subscriber.Subscriber
}

func NewEventBus(ctx context.Context, options *middleware_event.Options) (event.EventBus, error) {
	factory, err := middleware_event.NewEventFactory(options)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewEventBus NewEventFactory options(%+v) err: %v", options, err)
		return nil, err
	}

	eb := &eventBus{
		factory: factory,

		publishers:  make(map[string]*publisher),
		subscribers: make(map[string]*subscriber),
	}

	for name := range options.Publisher {
		syncPub, err := factory.NewSyncPublisher(name)
		if err != nil {
			log.ErrorWithCtx(ctx, "NewEventBus NewSyncPublisher name(%s) err: %v", name, err)
			return nil, err
		}

		eb.publishers[name] = &publisher{
			Publisher: syncPub,
		}
	}

	return eb, nil
}

func (eb *eventBus) Close() {
	for _, pub := range eb.publishers {
		_ = pub.Close()
	}
	for _, sub := range eb.subscribers {
		_ = sub.Stop()
	}
}

func (eb *eventBus) Subscribe(ctx context.Context, name event.SubName, process middleware_subscriber.ProcessorContextFunc) error {
	if _, ok := eb.subscribers[string(name)]; ok {
		log.WarnWithCtx(ctx, "Subscribe name(%s) duplicate, skip", name)
		return nil
	}

	sub, err := eb.factory.NewSubscriber(string(name), nil, process)
	if err != nil {
		log.ErrorWithCtx(ctx, "Subscribe NewSubscriber name(%s) err: %v", name, err)
		return err
	}

	eb.subscribers[string(name)] = &subscriber{
		Subscriber: sub,
	}

	return nil
}

func (eb *eventBus) Publish(ctx context.Context, e event.PubEvent, key string, msg any) error {
	name, topic, err := event.SplitPubEvent(e)
	if err != nil {
		return err
	}

	publisher, ok := eb.publishers[name]
	if !ok {
		return fmt.Errorf("name %s not found", name)
	}

	bin, err := proto.Marshal(msg)
	if err != nil {
		return err
	}

	pubMsg := &middleware_publisher.ProducerMessage{
		Topic: topic,
		Key:   middleware_publisher.StringEncoder(key),
		Value: middleware_publisher.ByteEncoder(bin),
	}
	result := publisher.Publish(ctx, pubMsg)
	if err := result.Err; err != nil {
		return err
	}

	log.InfoWithCtx(ctx, "Publish name(%s) topic(%s) msg(%+v) finished", name, topic, msg)
	return nil
}
