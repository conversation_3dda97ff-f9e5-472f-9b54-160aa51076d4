package config

import (
	"gitlab.ttyuyin.com/avengers/tyr/core/config/ttconfig"
	"sync/atomic"
)

type AigcAccountMiddleConfig struct {
	ChatRoundThreshold    uint32              `json:"chat_round_threshold"`    // 聊天轮数阈值
	SendPrologueThreshold int64               `json:"send_prologue_threshold"` // SendPrologueThreshold时间内没有聊过天，关注后发送开场白
	RiskWhitelistId       uint64              `json:"risk_whitelist_id"`       // AI账号风控白名单ID
	PostAiInteractParam   PostAiInteractParam `json:"post_ai_interact_param"`  // 帖子AI互动相关参数
}

type PostAiInteractParam struct {
	AiLikePostNumLimit      uint32                 `json:"ai_like_post_num_limit"`      // 每个用户一天被AI点赞帖子的数量限制
	AiCommentPostNumLimit   uint32                 `json:"ai_comment_post_num_limit"`   // 每个用户一天被AI评论的帖子数量限制
	PostCommentedAiNumLimit uint32                 `json:"post_commented_ai_num_limit"` // 每个帖子被AI评论的数量限制
	PerAiCommentCountLimit  uint32                 `json:"per_ai_comment_count_limit"`  // 每个AI对每个帖子评论的次数限制
	AiAccountRandomNum      uint32                 `json:"ai_account_random_num"`       // AI账号随机数
	SquareInteractExpParam  SquareInteractExpParam `json:"square_interact_exp_param"`   // 广场互动实验参数
}

type SquareInteractExpParam struct {
	AbLayerTag    string `json:"ab_layer_tag"`   // 实验域
	ParamName     string `json:"param_name"`     // 参数名
	ParamStrategy string `json:"param_strategy"` // 实验组参数值
}

// Format
// 配置加载时调用Format报错则返回错误
// 配置更新时调用Format报错则放弃此次更新
func (s *AigcAccountMiddleConfig) Format() error {
	return nil
}

var (
	atomicAigcAccountMiddleConfig *atomic.Value
)

func init() {
	//if err := InitAigcAccountMiddleConfig(); err != nil {
	//    panic(err)
	//}
}

// SetAigcAccountMiddleConfig 单测mock使用
func SetAigcAccountMiddleConfig(cfg *AigcAccountMiddleConfig) {
	if atomicAigcAccountMiddleConfig == nil {
		atomicAigcAccountMiddleConfig = &atomic.Value{}
	}
	if cfg == nil {
		cfg = &AigcAccountMiddleConfig{}
	}
	atomicAigcAccountMiddleConfig.Store(cfg)
}

// InitAigcAccountMiddleConfig
// 可以选择外部初始化或者直接init函数初始化
func InitAigcAccountMiddleConfig() error {
	cfg := &AigcAccountMiddleConfig{}
	atomCfg, err := ttconfig.AtomLoad("aigc-account-middle", cfg)
	if nil != err {
		return err
	}
	atomicAigcAccountMiddleConfig = atomCfg
	return nil
}

func GetAigcAccountMiddleConfig() *AigcAccountMiddleConfig {
	return atomicAigcAccountMiddleConfig.Load().(*AigcAccountMiddleConfig)
}

func (s *AigcAccountMiddleConfig) GetChatRoundThreshold() uint32 {
	if s == nil {
		return 0
	}
	return s.ChatRoundThreshold
}

func (s *AigcAccountMiddleConfig) GetSendPrologueThreshold() int64 {
	if s == nil {
		return 0
	}
	return s.SendPrologueThreshold
}

func (s *AigcAccountMiddleConfig) GetRiskWhitelistId() uint64 {
	prodWhitelistId := uint64(183)
	if s == nil || s.RiskWhitelistId == 0 {
		return prodWhitelistId
	}
	return s.RiskWhitelistId
}
