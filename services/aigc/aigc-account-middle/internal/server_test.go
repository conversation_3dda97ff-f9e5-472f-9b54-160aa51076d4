package internal

import (
	"context"
	"errors"
	"testing"

	mock_account "golang.52tt.com/clients/mocks/account"

	"github.com/golang/mock/gomock"
	pb "golang.52tt.com/protocol/services/aigc/aigc-account-middle"
	"golang.52tt.com/services/aigc/aigc-account-middle/internal/mgr/mocks"
)

// 这里只生成CreateAIAccount的典型单测，其它方法可按类似结构扩展
func TestServer_CreateAIAccount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockMgr := mocks.NewMockAIAccountManager(ctrl) // 需生成mock
	clients := setupMockClients(ctrl)
	mockAccount := clients.Account.(*mock_account.MockIClient)

	s := &Server{
		aiAccountMgr: mockMgr,
		clients:      clients,
	}

	ctx := context.Background()

	// 正常情况
	account := &pb.CreateAIAccountRequest_Account{
		Password: "pwd",
		Avatar:   "avatar",
		Nickname: "nick",
		Prologue: "hi",
		Phone:    "***********",
	}
	req := &pb.CreateAIAccountRequest{Account: account}

	mockMgr.EXPECT().CreateAIAccount(gomock.Any(), req).Return(uint32(1), nil)
	mockAccount.EXPECT().GetUidByPhone(gomock.Any(), account.Phone).Return(uint32(0), "", nil).AnyTimes()

	resp, err := s.CreateAIAccount(ctx, req)
	if err != nil {
		t.Errorf("CreateAIAccount() error = %v", err)
	}
	if resp == nil || resp.Id != 1 {
		t.Errorf("CreateAIAccount() got = %v, want id=1", resp)
	}

	// 参数校验失败：缺少密码
	badReq := &pb.CreateAIAccountRequest{Account: &pb.CreateAIAccountRequest_Account{}}
	resp, err = s.CreateAIAccount(ctx, badReq)
	if err == nil {
		t.Errorf("CreateAIAccount() expected error for missing password")
	}

	// 手机号已注册
	mockMgr.EXPECT().CreateAIAccount(ctx, req).Return(uint32(0), errors.New("mock error"))
	resp, err = s.CreateAIAccount(ctx, req)
	if err == nil {
		t.Errorf("CreateAIAccount() expected error for phone registered")
	}

	// 外部依赖异常
	mockMgr.EXPECT().CreateAIAccount(ctx, req).Return(uint32(0), errors.New("mock error"))
	resp, err = s.CreateAIAccount(ctx, req)
	if err == nil {
		t.Errorf("CreateAIAccount() expected error from mgr")
	}
}

// UpdateAIAccount 典型单测
func TestServer_UpdateAIAccount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockMgr := mocks.NewMockAIAccountManager(ctrl)
	s := &Server{aiAccountMgr: mockMgr}
	ctx := context.Background()

	// 正常情况
	account := &pb.UpdateAIAccountRequest_Account{Uid: 1, Nickname: "nick", Avatar: "avatar", Prologue: "hi"}
	flags := map[string]bool{"nickname": true, "avatar": true, "prologue": true}
	req := &pb.UpdateAIAccountRequest{Account: account, UpdateFlags: flags}
	mockMgr.EXPECT().UpdateAIAccount(gomock.Any(), req).Return(nil)
	resp, err := s.UpdateAIAccount(ctx, req)
	if err != nil {
		t.Errorf("UpdateAIAccount() error = %v", err)
	}
	if resp == nil {
		t.Errorf("UpdateAIAccount() got nil resp")
	}

	// 参数校验失败：uid为0
	badReq := &pb.UpdateAIAccountRequest{Account: &pb.UpdateAIAccountRequest_Account{}, UpdateFlags: flags}
	resp, err = s.UpdateAIAccount(ctx, badReq)
	if err == nil {
		t.Errorf("UpdateAIAccount() expected error for uid=0")
	}

	// 外部依赖异常
	account.Nickname = "nick2"
	mockMgr.EXPECT().UpdateAIAccount(ctx, req).Return(errors.New("mock error"))
	resp, err = s.UpdateAIAccount(ctx, req)
	if err == nil {
		t.Errorf("UpdateAIAccount() expected error from mgr")
	}
}

// UnregisterAIAccount 典型单测
func TestServer_UnregisterAIAccount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockMgr := mocks.NewMockAIAccountManager(ctrl)
	s := &Server{aiAccountMgr: mockMgr}
	ctx := context.Background()

	// 正常情况
	req := &pb.UnregisterAIAccountRequest{Uid: 1, Ttid: "ttid"}
	mockMgr.EXPECT().UnregisterAIAccount(gomock.Any(), req.Uid, req.Ttid).Return(nil)
	resp, err := s.UnregisterAIAccount(ctx, req)
	if err != nil {
		t.Errorf("UnregisterAIAccount() error = %v", err)
	}
	if resp == nil {
		t.Errorf("UnregisterAIAccount() got nil resp")
	}

	// 参数校验失败：uid为0
	badReq := &pb.UnregisterAIAccountRequest{}
	resp, err = s.UnregisterAIAccount(ctx, badReq)
	if err != nil {
		t.Errorf("UnregisterAIAccount() expected nil error for uid=0")
	}

	// 外部依赖异常
	req.Ttid = "ttid2"
	req.Uid = 2
	mockMgr.EXPECT().UnregisterAIAccount(ctx, req.Uid, req.Ttid).Return(errors.New("mock error"))
	resp, err = s.UnregisterAIAccount(ctx, req)
	if err == nil {
		t.Errorf("UnregisterAIAccount() expected error from mgr")
	}
}

// 其它方法可按此结构扩展
