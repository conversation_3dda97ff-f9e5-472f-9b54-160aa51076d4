package timer

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"golang.52tt.com/clients/topic-channel/tab"
	game_card "golang.52tt.com/pkg/game-card"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/bots/userline-robot/config"
	"golang.52tt.com/services/bots/userline-robot/feishu"
	"golang.52tt.com/services/bots/userline-robot/mgr"
	"strconv"
	"strings"
	"time"
)

const (
	GameCategoryId = 1
)

type CheckRcmdLabelTask struct {
	tabClient           *tab.Client
	rcmdChannelLabelCli rcmd_channel_label.RCMDChannelLabelClient
	msgSender           *feishu.FeiShuServer
	mgr                 *mgr.Manager
}

func NewCheckRcmdLabelTask(tabClient *tab.Client, rcmdChannelLabelCli rcmd_channel_label.RCMDChannelLabelClient, msgSender *feishu.FeiShuServer, mgr *mgr.Manager) Task {

	return &CheckRcmdLabelTask{
		tabClient:           tabClient,
		rcmdChannelLabelCli: rcmdChannelLabelCli,
		msgSender:           msgSender,
		mgr:                 mgr,
	}
}

func (c *CheckRcmdLabelTask) DoHandle(ctx context.Context) {
	minDuration := config.UserLineRobotDynamicConfig.GetCheckRcmdLabelTaskMin()
	ticker := time.NewTicker(time.Duration(minDuration) * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		contents, err := c.getData(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckRcmdLabelTask get data fail, err:%s", err.Error())
			continue
		}
		log.InfoWithCtx(ctx, "CheckRcmdLabelTask len(contents): %d", len(contents))
		if len(contents) == 0 {
			continue
		}
		contents = append([]string{"(" + config.Environment + ")" + "标签映射遗漏告警"}, contents...)
		msg := strings.Join(contents, "\\n")
		err = c.send(ctx, msg)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckRcmdLabelTask Send fail, err:%s", err.Error())
		}
	}
}

type QuestionMap struct {
	TabId    uint32
	TabName  string
	Question string
}

func (c *CheckRcmdLabelTask) genTabInfo(ctx context.Context) (tabMap map[uint32]*tabPB.Tab,
	blockMap map[uint32]*tabPB.Block, elemMap map[uint32]*tabPB.Elem, useElemMap map[string]*tabPB.Elem, useRcmdLabel map[string]*QuestionMap, err error) {

	tabResp, err := c.tabClient.GetTabsByCategoryIds(ctx, &tabPB.GetTabsByCategoryIdsReq{
		CategoryIds: []uint32{GameCategoryId},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache GetTabsByCategoryIds err %v", err)
		return nil, nil, nil, nil, nil, err
	}
	tabInfos := tabResp.GetCategoryTabMap()[GameCategoryId]
	if tabInfos == nil {
		return nil, nil, nil, nil, nil, protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError)
	}

	tabMap = make(map[uint32]*tabPB.Tab, len(tabInfos.GetTabs()))
	useRcmdLabel = make(map[string]*QuestionMap)
	// 要过滤掉使用通用游戏卡的tab
	notCategoryGameCardTabIds := make([]uint32, 0, len(tabInfos.GetTabs()))
	for _, tabInfo := range tabInfos.GetTabs() {
		_, _, isCommonCard := game_card.GetComposeGameCardInfo(tabInfo.GetGameInfo().GetGameCardId())
		if !isCommonCard {
			notCategoryGameCardTabIds = append(notCategoryGameCardTabIds, tabInfo.GetId())
		}

		tabMap[tabInfo.GetId()] = tabInfo
		for _, q := range tabInfo.GetQuestions() {
			for _, label := range q.GetLabels() {
				useRcmdLabel[label] = &QuestionMap{
					TabId:    tabInfo.GetId(),
					TabName:  tabInfo.GetName(),
					Question: q.GetTitle(),
				}
			}
		}
	}

	useElemMap = make(map[string]*tabPB.Elem)
	blockMap = make(map[uint32]*tabPB.Block)
	elemMap = make(map[uint32]*tabPB.Elem)
	resp, err := c.tabClient.BatchGetBlocks(ctx, &tabPB.BatchGetBlocksReq{TabId: notCategoryGameCardTabIds})
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache genBaseBlocksMap err %v", err)
		return tabMap, blockMap, elemMap, useElemMap, useRcmdLabel, err
	}
	if len(resp.GetData()) == 0 {
		log.ErrorWithCtx(ctx, "genBaseBlocksMap err: len 0")
		return tabMap, blockMap, elemMap, useElemMap, useRcmdLabel, protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError)
	}
	// k:tabId_blockId_elemId, val:elemInfo
	for tabId, v := range resp.GetData() {
		for _, block := range v.GetBlocks() {
			blockMap[block.GetId()] = block
			for _, e := range block.GetElems() {
				elemMap[e.GetId()] = e
				for _, flag := range e.GetPublicFlags() {
					if flag == tabPB.Elem_PublicFlagQuestion {
						key := fmt.Sprintf("%d_%d_%d", tabId, block.GetId(), e.GetId())
						if _, ok := useElemMap[key]; !ok {
							useElemMap[key] = e
						}
						break
					}
				}
			}
		}
	}
	return tabMap, blockMap, elemMap, useElemMap, useRcmdLabel, nil
}

func (c *CheckRcmdLabelTask) getData(ctx context.Context) ([]string, error) {
	tabMap, blockMap, elemMap, userElemMap, userLabelMap, err := c.genTabInfo(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckRcmdLabelTask genTabInfo fail, err:%s", err.Error())
		return nil, err
	}

	// 需要告警的内容
	contents := make([]string, 0)
	resp, err := c.rcmdChannelLabelCli.GetBusinessLabelRelation(ctx, &rcmd_channel_label.GetBusinessLabelRelationReq{
		BusinessType: nil,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckRcmdLabelTask get data fail, err:%s", err.Error())
		return contents, err
	}
	for _, item := range resp.GetBusinessLabelRelation() {
		switch item.GetBusinessType() {
		case rcmd_channel_label.BusinessType_BusinessTypeBlockOptionToGameCard:
			data := &rcmd_channel_label.BlockOptionGameCardRelation{}
			err := proto.Unmarshal(item.GetData(), data)
			if err != nil {
				log.ErrorWithCtx(ctx, "CheckRcmdLabelTask Unmarshal fail, err:%s, item:%+v", err.Error(), item)
				continue
			}
			log.InfoWithCtx(ctx, "CheckRcmdLabelTask len(data.BlockOptionGameCardList): %d", len(data.GetBlockOptionGameCardList()))
			rcmdTagMap := make(map[string][]*rcmd_channel_label.GameCardOpt)
			for _, blockOption := range data.GetBlockOptionGameCardList() {
				key := fmt.Sprintf("%d_%d_%d", blockOption.GetTabId(), blockOption.GetBlockOpt().GetBlockId(),
					blockOption.GetBlockOpt().GetElemId())

				rcmdTagMap[key] = blockOption.GetOptConfValueList()
			}
			for k, elem := range userElemMap {
				if v, ok := rcmdTagMap[k]; !ok || len(v) == 0 {
					log.InfoWithCtx(ctx, "CheckRcmdLabelTask blockOptionLabel, key:%s, elemTitle:%s", k, elem.GetTitle())
					info := strings.Split(k, "_")
					tabId, _ := strconv.ParseUint(info[0], 10, 32)
					blockId, _ := strconv.ParseUint(info[1], 10, 32)
					contents = append(contents, fmt.Sprintf("block elem问题弹窗类型标签没有绑定游戏卡, tabName:%s, blockTitle:%s, elemTitle:%s",
						tabMap[uint32(tabId)].GetName(), blockMap[uint32(blockId)], elem.GetTitle()))
				}
			}
			// 把全部的标签整理下，看看那些是没有在使用的
			if config.UserLineRobotDynamicConfig.LoadConfig().IsOpenGetAllRcmdLabelData {
				respContent, msgTyp := c.mgr.DownloadRcmdBlockInfo(ctx, tabMap, blockMap, elemMap, data.GetBlockOptionGameCardList())
				receiverId := config.UserLineRobotDynamicConfig.GetTabCacheWarnChatId()
				c.msgSender.SendMessage(ctx, "chat_id", receiverId, msgTyp, respContent)
			}

		case rcmd_channel_label.BusinessType_BusinessTypeLabelGameCard:
			data := &rcmd_channel_label.LabelGameCardRelation{}
			err := proto.Unmarshal(item.GetData(), data)
			if err != nil {
				log.ErrorWithCtx(ctx, "CheckRcmdLabelTask Unmarshal fail, err:%s, item:%+v", err.Error(), item)
				continue
			}
			log.InfoWithCtx(ctx, "CheckRcmdLabelTask len(GameCardLabel): %d", len(data.GetGameCardLabel()))
			for _, labelInfo := range data.GetGameCardLabel() {
				// 每个游戏卡id下的选项对应推荐标签,label:1级标签_2级标签_3级标签
				composeLabel := strings.Split(labelInfo.GetLabel(), "_")
				if len(composeLabel) != 3 {
					continue
				}
				if v, ok := userLabelMap[composeLabel[2]]; ok && v.TabName == composeLabel[0] && len(labelInfo.GetOptConfValueList()) == 0 {
					log.InfoWithCtx(ctx, "CheckRcmdLabelTask GameCardLabel: %v", labelInfo)

					contents = append(contents, fmt.Sprintf("玩法问题配置的推荐标签没有映射到游戏卡, tabId:%d, question:%s, label:%s",
						v.TabId, v.Question, composeLabel[2]))
				}
			}
			// 把全部的标签整理下，看看那些是没有在使用的
			if config.UserLineRobotDynamicConfig.LoadConfig().IsOpenGetAllRcmdLabelData {
				respContent, msgTyp := c.mgr.DownloadRcmdTag(ctx, data.GetGameCardLabel())
				receiverId := config.UserLineRobotDynamicConfig.GetTabCacheWarnChatId()
				c.msgSender.SendMessage(ctx, "chat_id", receiverId, msgTyp, respContent)
			}

		default:
			log.InfoWithCtx(ctx, "CheckRcmdLabelTask item: %v", item)
			continue
		}
	}
	return contents, nil
}

func (c *CheckRcmdLabelTask) send(ctx context.Context, content string) error {
	receiverId := config.UserLineRobotDynamicConfig.GetTabCacheWarnChatId()
	//receiverId := config.UserLineRobotDynamicConfig.LoadConfig().FeiShuReceiverId
	if len(content) == 0 {
		return nil
	}
	textMsgContent := fmt.Sprintf("{\"text\": \"%s\"}", content)

	if config.UserLineRobotDynamicConfig.LoadConfig().IsOpenCheckRcmdLabelPush {
		return c.msgSender.SendMessage(ctx, "chat_id", receiverId, feishu.MsgType_text, textMsgContent)
	}
	return nil
}
