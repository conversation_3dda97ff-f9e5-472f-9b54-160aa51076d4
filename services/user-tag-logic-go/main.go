package main

import (
	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/grpclog"
	"os"

	"golang.52tt.com/pkg/config"
	grpcEx "golang.52tt.com/pkg/foundation/grpc/server"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	"golang.52tt.com/pkg/tracing/jaeger"
	grpc_pb "golang.52tt.com/protocol/app/api/user_tag_go"
	pb "golang.52tt.com/protocol/services/logicsvr-go/user-tag-logic-go"
	"golang.52tt.com/services/runtime"
	"golang.52tt.com/services/user-tag-logic-go/server"

	_ "golang.52tt.com/pkg/hub/tyr/compatible/logic" // 兼容tyr公共库
)

func main() {
	flags := grpcEx.ParseServerFlags(os.Args)

	tracer := jaeger.Init("user-tag-logic-go")

	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdo<PERSON>, os.Stdout, os.Stdout))

	var (
		svr *server.UserTagLogicGo
		err error
	)
	initializer := func(ctx context.Context, s *grpc.Server, sc *config.ServerConfig) error {
		svr, err = server.NewUserTagLogicGo(sc.Configer)
		if err != nil {
			return err
		}
		pb.RegisterUserTagLogicGoServer(s, svr)
		grpc_pb.RegisterUserTagLogicGoServer(s, svr)
		return nil
	}

	closer := func(ctx context.Context, s *grpc.Server) error {
		if svr != nil {
			svr.ShutDown()
		}
		return nil
	}

	unaryInt := grpc_middleware.ChainUnaryServer(
		runtime.LogicServerUnaryInterceptor(flags.LogRequests, flags.LogResponses),
		traceGRPC.TracedUnaryServerInterceptor(
			tracing.UsingTracer(tracer), tracing.LogPayloads(true),
		),
	)

	s := grpcEx.NewServer(
		flags,
		grpcEx.WithGRPCServerOptions(grpc.UnaryInterceptor(unaryInt)),
		grpcEx.WithGRPCServerInitializer(initializer),
		grpcEx.WithGRPCServerCloser(closer),
		grpcEx.WithDefaultConfig("user-tag-logic-go.json", grpcEx.AdapterJSON),
	)
	s.Serve()
}
