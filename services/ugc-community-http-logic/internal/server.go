package internal

import (
	"context"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/http"

	"golang.52tt.com/pkg/web"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	"golang.52tt.com/protocol/services/demo/echo"
	ugc_community "golang.52tt.com/protocol/services/ugc-community"
	ugc_community_http_logic "golang.52tt.com/protocol/services/ugc-community-http-logic/ugc-community-http-logic"
	ugc_community_middle "golang.52tt.com/protocol/services/ugc-community-middle"
	"golang.52tt.com/services/ugc-community-http-logic/internal/controller"
)

type StartConfig struct {
	IsUsePost     bool `json:"is_use_post"`
	IsAuthByToken bool `json:"is_auth_by_token"`
	IsAllowCors   bool `json:"is_allow_cors"`
}

type Server struct {
	aigcSoulmateClient       aigc_soulmate.AigcSoulmateClient
	ugcCommunityClient       ugc_community.UgcCommunityClient
	ugcCommunityMiddleClient ugc_community_middle.UgcCommunityMiddleClient

	postController  *controller.PostController
	topicController *controller.TopicContorller
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	aigcSoulmateCli, err := aigc_soulmate.NewClient(ctx)
	if err != nil {
		log.Errorf("aigc_soulmate.NewClient err: %v", err)
		return nil, err
	}
	ugcCommunityCli, err := ugc_community.NewClient(ctx)
	if err != nil {
		log.Errorf("ugc_community.NewClient err: %v", err)
		return nil, err
	}
	ugcCommunityMiddleCli, err := ugc_community_middle.NewClient(ctx)
	if err != nil {
		log.Errorf("ugc_community_middle.NewClient err: %v", err)
		return nil, err
	}

	postController := controller.NewPostController(aigcSoulmateCli, ugcCommunityCli, ugcCommunityMiddleCli)
	topicController := controller.NewTopicContorller(ugcCommunityCli)

	return &Server{
		aigcSoulmateClient:       aigcSoulmateCli,
		ugcCommunityClient:       ugcCommunityCli,
		ugcCommunityMiddleClient: ugcCommunityMiddleCli,

		postController:  postController,
		topicController: topicController,
	}, nil
}

func (s *Server) Echo(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	// try http.GetUrlParams for url.Values
	msg := &echo.StringMessage{}
	if err := http.ReadJSON(r, msg); err != nil {
		w.WriteHeader(http.StatusBadRequest)
		return
	}

	_ = http.WriteJSON(w, http.StatusOK, msg)
}

func (s *Server) CommentSend(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	var req ugc_community_http_logic.CommentSendRequest
	if err := http.ReadJSON(r, &req); err != nil {
		log.ErrorWithCtx(ctx, "CommentSend err param req:%+v err: %v", req, err)
		web.ServeBadReq(w)
		return
	}
	if req.GetPostId() == "" {
		log.ErrorWithCtx(ctx, "CommentSend err para, req:%+v", req)
		web.ServeBadReq(w)
		return
	}
	baseReq := convertBaseReq(req.GetBaseReq())
	_, err := s.ugcCommunityMiddleClient.CommentSend(ctx, &ugc_community_middle.CommentSendRequest{
		BaseReq:      baseReq,
		PostId:       req.GetPostId(),
		RootParentId: req.GetRootParentId(),
		ParentId:     req.GetParentId(),
		Content:      req.GetContent(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CommentSend err: %v, req:%+v", err, req)
		web.ServeRpcError(w, err)
		return
	}
	_ = web.ServeAPIJsonV2(w, &ugc_community_http_logic.CommentSendResponse{})
}

func (s *Server) CommentFetch(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	var req ugc_community_http_logic.CommentFetchRequest
	if err := http.ReadJSON(r, &req); err != nil {
		log.ErrorWithCtx(ctx, "CommentFetch err param req:%+v err: %v", req, err)
		web.ServeBadReq(w)
		return
	}
	baseReq := convertBaseReq(req.GetBaseReq())
	rsp, err := s.ugcCommunityMiddleClient.CommentFetch(ctx, &ugc_community_middle.CommentFetchRequest{
		BaseReq:       baseReq,
		PostId:        req.GetPostId(),
		LastCommentId: req.GetLastCommentId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CommentFetch err: %v, req:%+v", err, req)
		web.ServeRpcError(w, err)
		return
	}
	out := &ugc_community_http_logic.CommentFetchResponse{
		Comments:     rsp.GetComments(),
		IsLoadFinish: rsp.GetIsLoadFinish(),
	}
	_ = web.ServeAPIJsonV2(w, out)
}

func (s *Server) CommentDelete(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	var req ugc_community_http_logic.CommentDeleteRequest
	if err := http.ReadJSON(r, &req); err != nil {
		log.ErrorWithCtx(ctx, "CommentDelete err param req:%+v err: %v", req, err)
		web.ServeBadReq(w)
		return
	}
	baseReq := convertBaseReq(req.GetBaseReq())
	_, err := s.ugcCommunityMiddleClient.CommentDelete(ctx, &ugc_community_middle.CommentDeleteRequest{
		BaseReq:   baseReq,
		CommentId: req.GetCommentId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CommentDelete err: %v, req:%+v", err, req)
		web.ServeRpcError(w, err)
		return
	}
	_ = web.ServeAPIJsonV2(w, &ugc_community_http_logic.CommentDeleteResponse{})
}

func (s *Server) GetNewsFeeds(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	var req ugc_community_http_logic.GetNewsFeedsReq
	if err := http.ReadJSON(r, &req); err != nil {
		log.ErrorWithCtx(ctx, "GetNewsFeeds err param req:%+v err: %v", req, err)
		web.ServeBadReq(w)
		return
	}

	baseReq := convertBaseReq(req.GetBaseReq())
	feedsReq := &ugc_community_middle.GetNewsFeedsReq{
		BaseReq:       baseReq,
		PostSource:    uint32(req.GetPostSource()),
		GetMode:       req.GetGetMode(),
		BrowsePostIds: req.GetBrowsePostId(),
		LoadMore:      req.GetLoadMore(),
		SubjectId:     req.GetSubjectId(),
		RoleId:        req.GetRoleId(),
	}
	newsFeedsRsp, err := s.ugcCommunityMiddleClient.GetNewsFeeds(ctx, feedsReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNewsFeeds err: %v, req:%+v", err, feedsReq)
		web.ServeRpcError(w, err)
		return
	}

	newFeeds := converFeeds(newsFeedsRsp.GetFeeds())

	out := &ugc_community_http_logic.GetNewsFeedsResp{
		Feeds:     newFeeds,
		LoadMore:  newsFeedsRsp.GetLoadMore(),
		Footprint: newsFeedsRsp.GetFootprint(),
	}
	_ = web.ServeAPIJsonV2(w, out)
}

func convertBaseReq(req *ugc_community_http_logic.BaseRequest) *ugc_community_middle.BaseRequest {
	return &ugc_community_middle.BaseRequest{
		DeviceId:      req.GetDeviceId(),
		MarketId:      req.GetMarketId(),
		ClientType:    req.GetClientType(),
		ClientVersion: req.GetClientVersion(),
	}
}

func converPostInfo(postInfo *ugc_community_middle.PostInfo) *ugc_community_http_logic.PostInfo {
	attas := make([]*ugc_community_http_logic.Attachment, 0)
	for _, atta := range postInfo.GetAttachments() {
		attas = append(attas, &ugc_community_http_logic.Attachment{Content: atta.GetUrl(), Type: ugc_community_http_logic.Attachment_Type(atta.GetType()), Key: atta.GetKey()})
	}
	postOwner := &ugc_community_http_logic.UserUgcCommunity{
		Uid:      postInfo.GetPostOwner().GetUid(),
		Account:  postInfo.GetPostOwner().GetAccount(),
		Nickname: postInfo.GetPostOwner().GetNickname(),
		Alias:    postInfo.GetPostOwner().GetAlias(),
		Gender:   postInfo.GetPostOwner().GetGender(),
	}
	newPostInfo := &ugc_community_http_logic.PostInfo{
		PostId:        postInfo.GetPostId(),
		PostOwner:     postOwner,
		PostType:      ugc_community.PostType(postInfo.GetPostType()),
		Attachments:   attas,
		Content:       postInfo.GetContent(),
		PostTime:      postInfo.GetPostTime(),
		CommentCount:  postInfo.GetCommentCount(),
		AttitudeCount: postInfo.GetAttitudeCount(),
		Origin:        ugc_community.PostOrigin(postInfo.GetOrigin()),
		BizType:       ugc_community.PostBizType(postInfo.GetBizType()),
		BizData:       postInfo.GetBizData(),
		IsAttitude:    postInfo.GetIsAttitude(),
		State:         postInfo.GetState(),
		HotComment:    postInfo.CommentItem,
		BizBytes:      postInfo.GetBizBytes(),
		HadFollowed:   postInfo.GetHadFollowed(),
	}
	for _, topic := range postInfo.GetTopicList() {
		newPostInfo.TopicList = append(newPostInfo.TopicList, &ugc_community_http_logic.TopicInfo{
			Id:   topic.GetId(),
			Type: uint32(topic.GetType()),
			Name: topic.GetName(),
		})
	}
	return newPostInfo
}

func converPostInfos(postInfos []*ugc_community_middle.PostInfo) []*ugc_community_http_logic.PostInfo {
	newPostInfos := make([]*ugc_community_http_logic.PostInfo, 0, len(postInfos))
	for _, postInfo := range postInfos {
		newPostInfo := converPostInfo(postInfo)
		newPostInfos = append(newPostInfos, newPostInfo)
	}
	return newPostInfos
}

func converFeeds(feeds []*ugc_community_middle.Feed) []*ugc_community_http_logic.Feed {
	newFeeds := make([]*ugc_community_http_logic.Feed, 0, len(feeds))
	for _, feed := range feeds {
		newPostInfo := converPostInfo(feed.GetPost())
		newFeeds = append(newFeeds, &ugc_community_http_logic.Feed{
			Post:   newPostInfo,
			FeedId: feed.GetFeedId(),
		})
	}
	return newFeeds
}

func (s *Server) GetPost(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	var req ugc_community_http_logic.GetPostReq
	if err := http.ReadJSON(r, &req); err != nil {
		log.ErrorWithCtx(ctx, "GetPost err param req:%+v err: %v", req, err)
		web.ServeBadReq(w)
		return
	}

	baseReq := convertBaseReq(req.GetBaseReq())
	postReq := &ugc_community_middle.GetPostReq{BaseReq: baseReq, PostId: req.GetPostId(), PostSource: uint32(req.GetPostSource())}
	postRsp, err := s.ugcCommunityMiddleClient.GetPost(ctx, postReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPost err: %v, req:%v", err, postReq)
		web.ServeRpcError(w, err)
		return
	}

	newPostInfos := converPostInfos([]*ugc_community_middle.PostInfo{postRsp.GetPostInfo()})

	out := &ugc_community_http_logic.GetPostResp{PostInfo: newPostInfos[0]}
	_ = web.ServeAPIJsonV2(w, out)
}

func (s *Server) PublishPost(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	var req ugc_community_http_logic.PublishPostRequest
	if err := http.ReadJSON(r, &req); err != nil {
		log.ErrorWithCtx(ctx, "PublishPost ReadJSON err: %v", err)
		web.ServeBadReq(w)
		return
	}

	resp, err := s.postController.PublishPost(ctx, &req)
	if err != nil {
		web.ServeRpcError(w, err)
		return
	}

	_ = web.ServeAPIJsonV2(w, resp)
}

func (s *Server) DeletePost(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	var req ugc_community_http_logic.DeletePostRequest
	if err := http.ReadJSON(r, &req); err != nil {
		log.ErrorWithCtx(ctx, "DeletePost ReadJSON err: %v", err)
		web.ServeBadReq(w)
		return
	}

	resp, err := s.postController.DeletePost(ctx, &req)
	if err != nil {
		web.ServeRpcError(w, err)
		return
	}

	_ = web.ServeAPIJsonV2(w, resp)
}

func (s *Server) GeneratePublishPostParam(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	var req ugc_community_http_logic.GeneratePublishPostParamRequest
	if err := http.ReadJSON(r, &req); err != nil {
		log.ErrorWithCtx(ctx, "GeneratePublishPostParam ReadJSON err: %v", err)
		web.ServeBadReq(w)
		return
	}

	resp, err := s.postController.GeneratePublishPostParam(ctx, &req)
	if err != nil {
		web.ServeRpcError(w, err)
		return
	}

	_ = web.ServeAPIJsonV2(w, resp)
}

func (s *Server) GetSubjectTabList(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	resp, err := s.ugcCommunityClient.GetSubjectTabList(ctx, &ugc_community.GetSubjectTabListRequest{})
	if err != nil {
		web.ServeRpcError(w, err)
		return
	}
	out := &ugc_community_http_logic.GetSubjectTabListResponse{}
	out.SubjectInfos = convertSubjects(resp.GetList())

	log.InfoWithCtx(ctx, "GetSubjectTabList success out:%s ", out.String())
	_ = web.ServeAPIJsonV2(w, resp)
}

func (s *Server) GetTopicList(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	var req ugc_community_http_logic.GetTopicListRequest
	if err := http.ReadJSON(r, &req); err != nil {
		log.ErrorWithCtx(ctx, "GetTopicList ReadJSON err: %v", err)
		web.ServeBadReq(w)
		return
	}

	resp, err := s.topicController.GetTopicList(ctx, &req)
	if err != nil {
		web.ServeRpcError(w, err)
		return
	}

	_ = web.ServeAPIJsonV2(w, resp)
}

func convertSubjects(subjects []*ugc_community.SubjectTabInfo) []*ugc_community_http_logic.SubjectTabInfo {
	res := make([]*ugc_community_http_logic.SubjectTabInfo, 0, len(subjects))
	for _, subject := range subjects {
		res = append(res, &ugc_community_http_logic.SubjectTabInfo{
			Id:              subject.GetId(),
			Name:            subject.GetName(),
			StickyPostInfos: convertStickInfo(subject.GetStickyPostList()),
		})
	}
	return res
}

func convertStickInfo(stickInfos []*ugc_community.StickyPostInfo) []*ugc_community_http_logic.StickyPostInfo {
	res := make([]*ugc_community_http_logic.StickyPostInfo, 0, len(stickInfos))
	for _, stickInfo := range stickInfos {
		res = append(res, &ugc_community_http_logic.StickyPostInfo{
			Id:    stickInfo.GetId(),
			Title: stickInfo.GetTitle(),
			Icon:  stickInfo.GetIcon(),
		})
	}
	return res
}
