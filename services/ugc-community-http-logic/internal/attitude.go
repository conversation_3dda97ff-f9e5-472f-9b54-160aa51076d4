package internal

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	ugc_community "golang.52tt.com/protocol/services/ugc-community"

	"gitlab.ttyuyin.com/avengers/tyr/core/service/http"
	"golang.52tt.com/pkg/web"
	ugc_community_http_logic "golang.52tt.com/protocol/services/ugc-community-http-logic/ugc-community-http-logic"
)

func (s *Server) AddAttitude(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	var req ugc_community_http_logic.AttitudeReq
	if err := http.ReadJSON(r, &req); err != nil {
		log.ErrorWithCtx(ctx, "AddAttitude err param req:%+v err: %v", req, err)
		web.ServeRpcError(w, err)
		return
	}

	if len(req.GetId()) == 0 {
		log.ErrorWithCtx(ctx, "AddAttitude err param req:%+v ", req)
		web.ServeRpcError(w, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid))
		return
	}

	uid := metainfo.GetServiceInfo(ctx).UserID()
	_, err := s.ugcCommunityClient.AddAttitude(ctx, &ugc_community.AddAttitudeRequest{
		UserId: uid,
		Action: ugc_community.AttitudeAction(req.GetAction()),
		Object: &ugc_community.AttitudeObject{
			Id:   req.GetId(),
			Type: ugc_community.ObjectType(req.GetObjectType()),
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AddAttitude req:%+v err: %v", req, err)
		web.ServeRpcError(w, err)
		return
	}
	_ = web.ServeAPIJsonV2(w, &ugc_community_http_logic.AttitudeResp{})
}
