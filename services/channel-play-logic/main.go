package main

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/grpc"
	sentinel_interceptor "golang.52tt.com/pkg/sentinel/interceptor"
	grpc_pb "golang.52tt.com/protocol/app/api/channel_play"
	"golang.52tt.com/protocol/services/demo/echo"
	pb "golang.52tt.com/protocol/services/logicsvr-go/channel-play-logic"
	"golang.52tt.com/services/channel-play-logic/internal/server"
	// use logic startup
	startup "gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/grpc/logic"
	
	_ "golang.52tt.com/pkg/hub/tyr/compatible/logic" // 兼容tyr公共库
)

func main() {
	var (
		channelPlayLogic *server.ChannelPlayLogic
		cfg              = &server.StartConfig{}
		err              error
	)
	
	// config file support yaml & json, default channel-play-logic.json/yaml
	if err := startup.New("channel-play-logic", cfg).
		AddGrpcServer(grpc.NewBuildOption().
			WithInitializeFunc(func(ctx context.Context, s *grpc.Server) error {
				if channelPlayLogic, err = server.NewChannelPlayLogic(ctx, cfg); err != nil {
					return err
				}
				
				// register custom grpc server
				pb.RegisterChannelPlayLogicServer(s, channelPlayLogic)
				grpc_pb.RegisterChannelPlayLogicServer(s, channelPlayLogic)
				// grpcurl -plaintext -d '{"value":"hello"}' 127.0.0.1:80 demo.echo.EchoService.Echo
				// grpcurl -plaintext 127.0.0.1:80 grpc.health.v1.Health.Check
				echo.RegisterEchoServiceServer(s, channelPlayLogic)
				return nil
			}).AddExtraUnaryInterceptor(
			sentinel_interceptor.UnaryServerInterceptor(),
		)).
		WithCloseFunc(func(ctx context.Context) {
			// do something when server terminating
			channelPlayLogic.ShutDown()
		}).
		Start(); err != nil {
		log.Errorf("server start fail, err: %v", err)
	}
}
