package conf

import (
	"context"
	"golang.52tt.com/pkg/log"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	hobby_channel "golang.52tt.com/protocol/app/hobby-channel"
	pb "golang.52tt.com/protocol/services/gangup-channel"
)

const (
	NoLimitBlockDefaultNum            = 3
	DefaultChannelListTimeoutStandard = 1500
)

type ChannelPlayConfig struct {
	ShowMusicInfoTab         map[uint32]bool `json:"show_music_info_tab"`
	MysteryShowTimeThreshold uint32          `json:"mystery_show_time_threshold"`
	NeedHighLightCategory    []uint32        `json:"need_high_light_category"`

	MysteryPlaceFilter []*pb.GameHomePageFilterItem `json:"mystery_place_filter"`
	DefaultFilter      []*pb.GameHomePageFilterItem `json:"default_filter"`
	PCFallBackFilter   []*pb.GameHomePageFilterItem `json:"pc_fall_back_filter"`   // PC端兜底玩法
	FastPCSelectFilter []*pb.GameHomePageFilterItem `json:"fast_pc_select_filter"` // 极速版PC筛选排序兜底玩法
	PCFixFilter        []*pb.GameHomePageFilterItem `json:"pc_fix_filter"`         // PC端固定强插玩法
	FastPCDiyFilter    []*pb.GameHomePageFilterItem `json:"fast_pc_diy_filter"`    // 极速版PC常玩兜底玩法

	RealNameCanPlayTab []uint32 `json:"real_name_can_play_tab"` //需要实名才能玩的tab

	DiyNameLimitStartTime   int64           `json:"diy_name_limit_start_time"`
	DiyNameLimitEndTime     int64           `json:"diy_name_limit_end_time"`
	DiyNameLimitWhiteMarket map[uint32]bool `json:"diy_name_limit_white_market"`

	PublishChannelMinimumUserLevel map[uint32]uint32 `json:"publish_channel_minimum_user_level"` // 监管需求，马甲包发布房间最小的用户等级
	MaxCreateLimitWarning          int               `json:"max_create_limit_warning"`
	MaxCreateLimit                 int               `json:"max_create_limit"`
	PublishCheckNameCategoryIds    []uint32          `json:"publish_check_name_category_ids"`

	FreezeDuration uint32 `json:"freeze_duration"`
	ChangeCoolDown uint32 `json:"change_cool_down"`

	// 不同入口筛选器默认配置， 一级首页 1 游戏专区 2 休闲互动 3
	DefaultFilterMap map[uint32][]*channel_play.CommonBusinessFlag `json:"default_filter_map"`

	// 首页金刚区场数统计包含的玩法id
	HomePageTeamNumTabIds []uint32 `json:"home_page_team_num_tab_ids"`
	// 入口来源专区对应的类目
	FilterEntranceCategoryMap map[channel_play.FilterEntrance][]uint32 `json:"filter_entrance_category_map"`
	// 首页更多热门分类配置
	HomePageMoreHotConfig *HomePageMoreHotConfig `json:"home_page_more_hot_config"`
	// 首页更多音乐兴趣一级动态配置
	HomePageMoreMusicConfig []*HomePageMoreMusicConfig `json:"home_page_more_music_config"` // 音乐相关的item id

	//小游戏默认帮用户保存的常玩分类
	MiniGameDefaultDIYTab []uint32 `json:"mini_game_default_diy_tab"`

	// 玩法问题弹窗过期间隔
	PlayQuestionSec uint32 `json:"play_question_sec"`

	// 问题弹窗实验配置
	PlayQuestionTestConfig *AbtestConfig `json:"play_question_test_config"`

	// 萌新承接房标识文案
	NewBieChannelText string `json:"newbie_channel_text"`
	// 萌新军认证标签
	NewBiePersonalCert channel_play.PersonalCert `json:"newbie_personal_cert"`

	//负反馈返回原因限制数量
	NegativeReasonNum int `json:"negative_reason_num"`

	//发布下发给房主的文案
	PublishAdminMsg string `json:"publish_admin_msg"`

	//创房玩法被监管过滤报错开关，true 要报错 false 不报错，创建旧玩法房间
	IsCreateChannelReturnErr bool `json:"is_create_channel_return_err"`

	//房间列表选择不限的发布项最多可展示的数量
	ChannelListNoLimitBlockMaxNum int `json:"channel_list_no_limit_block_max_num"`

	// 萌新军引导勾选【优先萌新】文案
	NewBieGuideTitle string `json:"new_bie_guide_title"`

	OtherGameTab uint32 `json:"other_game_tab"` // 其它游戏tab_id, 新增配置

	WordChannelGuide       *WordChannelGuideSetting                   `json:"word_channel_guide"`        //文字房引导气泡配置文案
	WordChannelWelcomeText string                                     `json:"word_channel_welcome_text"` //文字房进房欢迎语
	WordChannelLabel       *channel_play.ChannelViewMOBA_DescAndColor `json:"word_channel_label"`        //moba view文字房标签文案
	MaxOnMicUserImageNum   int                                        `json:"max_on_mic_user_image_num"` //房间列表展示的麦上用户头像数量

	SentinelTestBusinessMap map[string]bool `json:"sentinel_test_business_map"` //熔断测试

	IsChannelPushStat      bool   `json:"is_channenl_push_stat"` //房间下发统计写kfk
	TestConfEnv            string `json:"test_conf_env"`         //灰度测试的环境变量,生产环境不生效
	TestGameFilterFallBack bool   `json:"test_game_filter_fall_back"`
	IsBlReportClose        bool   `json:"is_bl_report_close"`
	UseDismiss             bool   `json:"use_dismiss"`

	//ShowTopicChannelTabList迁移过来用到得动态配置
	NewTabMap         map[uint32]bool           `json:"new_tab_map"`
	CategoryWeightMap map[uint16]map[uint32]int `json:"category_weight_map"`

	//是否需要检查发布是的block配置，默认需要检查，不检查需要设置为true
	NoCheckBlockSetting bool `json:"no_check_block_setting"`

	RevenueRoomTabInsertPostions map[uint32][]uint32 `json:"revenue_room_tab_insert_postions"` // 营收房间在玩法里插入位置配置

	// 注册弹窗保存常玩实验
	RegisterDiyItemTestConfig *AbtestConfig `json:"register_diy_item_test_config"`
	IsABTestFilterClose       bool          `json:"is_ab_test_filter_close"` //是否关闭AB测过滤

	// 首页二级词表筛选实验
	HomePageFilterTestConfig *AbtestConfig `json:"home_page_filter_test_config"`

	//屏蔽词ab测配置，只针对移动端
	ShieldWordTestConfig *AbtestConfig `json:"shield_word_test_config"`

	// 日志开关
	LogSwitch bool `json:"log_switch"` // 是否开启日志

	SupervisorPublishNameMsg    string `json:"supervisor_publish_name_msg"`     // 监管发布房间名称提示文案
	SupervisorCreateRoomNameMsg string `json:"supervisor_create_room_name_msg"` // 监管创房房间名称提示文案
	PublishRoomNameMsg          string `json:"publish_room_name_msg"`           // 非监管发布房间名称提示文案
}

type AbtestConfig struct {
	LayerTag    string `json:"layer_tag"`
	ArgvName    string `json:"argv_name"`
	ExpectValue string `json:"expect_value"`
	HuoKeKey    string `json:"huo_ke_key"` // 获客人群实验key
	ActiveKey   string `json:"active_key"` // 活跃人群实验key
	//HuoKeLayer  string `json:"huo_ke_layer"` // 获客人群实验层
	//ActiveLayer string `json:"active_layer"` // 活跃人群实验层

}

type GuideConfig struct {
	Position []uint32 `json:"position"`
	Ids      []string `json:"ids"`
}

// 文字房引导气泡配置文案
type WordChannelGuideSetting struct {
	Version   uint32 `json:"version"`    //版本号，用配置时的时间戳
	StartTime uint32 `json:"start_time"` //生效时间
	EndTime   uint32 `json:"end_time"`   //结束时间
	Title     string `json:"title"`      //文案
}

type AreaInfo struct {
	Country  []string `json:"country"`
	Province []string `json:"province"`
	City     []string `json:"city"`
}

type HomePageMoreHotConfig struct {
	Items        []channel_play.CommonBusinessFlag `json:"items"`          // 具体配置
	CanSelectNum uint32                            `json:"can_select_num"` // 能最大选多少个子item，不可为0全选
}

type HomePageMoreMusicConfig struct {
	Pos          int    `json:"pos"`            // 如果配置了把该一级音乐插入到更多玩法中的第几个位置, 0表明按序追加到最后
	ItemId       string `json:"item_id"`        // 音乐相关的item_id
	CanSelectNum uint32 `json:"can_select_num"` // 能最大选多少个子item，0代表可全选
}

// 发布下发给房主的文案,配置为空给个默认文案
func (s *ChannelPlayLogicConfigLoader) GetPublishAdminMsg() string {
	var cfg *ChannelPlayConfig
	if t := s.configLoader.Get(); nil != t {
		cfg = t.(*ChannelPlayConfig)
	}
	if nil == cfg || cfg.PublishAdminMsg == "" {
		log.Debugf("ChannelPlayLogicConfigLoader PublishAdminMsg not found")
		return "房间已在首页展示，队友正陆续赶来，要做个nice的人哟，踢人等不友善行为会降低房间排序"
	}
	return cfg.PublishAdminMsg
}

// 创房玩法被监管过滤报错开关
func (s *ChannelPlayLogicConfigLoader) GetIsChangeCreateChannelTab(source hobby_channel.CreateChannelSource) bool {
	var cfg *ChannelPlayConfig
	if t := s.configLoader.Get(); nil != t {
		cfg = t.(*ChannelPlayConfig)
	}
	if nil == cfg {
		log.Errorf("ChannelPlayConfig is nil")
		return false
	}
	// 客户端创房才需要发返回错误，短链创房有错切到默认玩法
	return cfg.IsCreateChannelReturnErr && source == hobby_channel.CreateChannelSource_From_Client
}

func (s *ChannelPlayLogicConfigLoader) IsOnlyRealNamePlayTab(tabId uint32) bool {
	cfg := s.LoadConfig()
	if nil == cfg || nil == cfg.RealNameCanPlayTab {
		log.Debugf("TopicChannelLogicConfigLoader RealNameCanPlayTab not found")
		return false
	}
	for _, v := range cfg.RealNameCanPlayTab {
		if tabId == v {
			return true
		}
	}
	return false
}

func (s *ChannelPlayLogicConfigLoader) GetDiyNameLimitWhiteMarket() map[uint32]bool {
	res := make(map[uint32]bool)
	config := s.LoadConfig()
	if config == nil || config.DiyNameLimitWhiteMarket == nil {
		return res
	}
	return config.DiyNameLimitWhiteMarket
}

func (s *ChannelPlayLogicConfigLoader) GetPublishChannelMinimumUserLevel() map[uint32]uint32 {
	res := make(map[uint32]uint32)
	config := s.LoadConfig()
	if config == nil || config.PublishChannelMinimumUserLevel == nil {
		log.Debugf("DynamicConfig PublishChannelMinimumUserLevel not found")
		return res
	}
	return config.PublishChannelMinimumUserLevel
}

func (s *ChannelPlayLogicConfigLoader) GetPublishCheckNameCategoryIds() []uint32 {
	res := make([]uint32, 0)
	config := s.LoadConfig()
	if config == nil || config.PublishCheckNameCategoryIds == nil {
		log.Debugf("DynamicConfig GetPublishCheckNameCategoryIds not found")
		return res
	}
	return config.PublishCheckNameCategoryIds
}

func (s *ChannelPlayLogicConfigLoader) GetDefaultFilterMapByEntrance(entrance uint32) []*channel_play.CommonBusinessFlag {
	res := make([]*channel_play.CommonBusinessFlag, 0)
	config := s.LoadConfig()
	if config == nil || config.DefaultFilterMap == nil {
		log.Debugf("DynamicConfig GetPublishCheckNameCategoryIds not found")
		return res
	}
	if _, ok := config.DefaultFilterMap[entrance]; ok {
		res = config.DefaultFilterMap[entrance]
	} else {
		log.Warnf("ChannelPlayLogicConfigLoader GetDefaultFilterMapByEntrance entrance(%d) has no defaultFilter", entrance)
	}
	return res
}

func (s *ChannelPlayLogicConfigLoader) GetHomePageTeamNumTabIds(ctx context.Context) []uint32 {
	config := s.LoadConfig()
	if config == nil || config.HomePageTeamNumTabIds == nil {
		log.ErrorWithCtx(ctx, "DynamicConfig GetHomePageTeamNumTabIds not found")
		return nil
	}
	return config.HomePageTeamNumTabIds
}

func (s *ChannelPlayLogicConfigLoader) GetFilterEntranceCategoryMap(ctx context.Context, filterEntrance channel_play.FilterEntrance) []uint32 {
	res := make([]uint32, 0)
	config := s.LoadConfig()
	if config == nil || config.FilterEntranceCategoryMap == nil {
		log.ErrorWithCtx(ctx, "DynamicConfig GetFilterEntranceCategoryMap not found")
		return res
	}
	if v, ok := config.FilterEntranceCategoryMap[filterEntrance]; ok {
		res = v
	} else {
		log.ErrorWithCtx(ctx, "ChannelPlayLogicConfigLoader GetFilterEntranceCategoryMap filterEntrance:%d has no config", filterEntrance)
	}
	return res
}

func (s *ChannelPlayLogicConfigLoader) GetHomePageMoreHotConfig(ctx context.Context) *HomePageMoreHotConfig {
	config := s.LoadConfig()
	if config == nil || config.HomePageMoreHotConfig == nil {
		log.ErrorWithCtx(ctx, "DynamicConfig GetHomePageMoreHotConfig not found")
		return nil
	}
	return config.HomePageMoreHotConfig
}

func (s *ChannelPlayLogicConfigLoader) GetHomePageMoreMusicConfig(ctx context.Context) []*HomePageMoreMusicConfig {
	config := s.LoadConfig()
	if config == nil || config.HomePageMoreMusicConfig == nil {
		log.DebugWithCtx(ctx, "DynamicConfig HomePageMoreMusicConfig not found")
		return nil
	}
	return config.HomePageMoreMusicConfig
}

func (s *ChannelPlayLogicConfigLoader) GetMiniGameDefaultDIYTab() []uint32 {
	res := make([]uint32, 0)
	config := s.LoadConfig()
	if config == nil || config.MiniGameDefaultDIYTab == nil {
		log.Errorf("DynamicConfig GetMiniGameDefaultDIYTab not found")
		return res
	}
	return config.MiniGameDefaultDIYTab
}

func (s *ChannelPlayLogicConfigLoader) GetPlayQuestionSec() uint32 {
	var sec uint32

	config := s.LoadConfig()
	if config != nil {
		sec = config.PlayQuestionSec
	}

	return sec
}

func (s *ChannelPlayLogicConfigLoader) GetNewBieChannelText() string {
	text := "新人专属"

	config := s.LoadConfig()
	if config != nil && len(config.NewBieChannelText) > 0 {
		text = config.NewBieChannelText
	}

	return text
}

func (s *ChannelPlayLogicConfigLoader) GetNewBiePersonalCert() channel_play.PersonalCert {
	var pc channel_play.PersonalCert

	config := s.LoadConfig()
	if config != nil {
		pc = config.NewBiePersonalCert
	}

	return pc
}

func (s *ChannelPlayLogicConfigLoader) GetChannelListNoLimitBlockMaxNum() int {
	config := s.LoadConfig()
	if config == nil || config.ChannelListNoLimitBlockMaxNum < 0 {
		log.Errorf("DynamicConfig GetChannelListNoLimitBlockMaxNum not found")
		return NoLimitBlockDefaultNum
	}
	return config.ChannelListNoLimitBlockMaxNum
}

func (s *ChannelPlayLogicConfigLoader) GetNewBieGuideTitle() string {

	config := s.LoadConfig()
	if config != nil && len(config.NewBieGuideTitle) > 0 {
		return config.NewBieGuideTitle
	}

	return ""
}

func (s *ChannelPlayLogicConfigLoader) GetWordChannelGuideSetting() *WordChannelGuideSetting {
	config := s.LoadConfig()
	var fig *WordChannelGuideSetting
	if config != nil {
		fig = config.WordChannelGuide
	}
	return fig
}

func (s *ChannelPlayLogicConfigLoader) GetWordChannelWelcomeText() string {

	config := s.LoadConfig()
	if config != nil {
		return config.WordChannelWelcomeText
	}

	return ""
}

func (s *ChannelPlayLogicConfigLoader) GetWordChannelLabel() *channel_play.ChannelViewMOBA_DescAndColor {

	config := s.LoadConfig()
	if config != nil {
		return config.WordChannelLabel
	}

	return nil
}

func (s *ChannelPlayLogicConfigLoader) GetMaxOnMicUserImageNum() int {
	config := s.LoadConfig()
	if config != nil {
		return config.MaxOnMicUserImageNum
	}
	return 0
}

func (s *ChannelPlayLogicConfigLoader) GetIsSentinelTest(business string) bool {
	config := s.LoadConfig()
	if config != nil {
		return config.SentinelTestBusinessMap[business]
	}
	return false
}

func (s *ChannelPlayLogicConfigLoader) GetIsChannelStatPush() bool {
	config := s.LoadConfig()
	if config != nil {
		return config.IsChannelPushStat
	}
	return false
}

func (s *ChannelPlayLogicConfigLoader) GetTestConfEnv() string {
	config := s.LoadConfig()
	if config != nil {
		return config.TestConfEnv
	}
	return ""
}

func (s *ChannelPlayLogicConfigLoader) GetTestGameFilterFallBack() bool {
	config := s.LoadConfig()
	if config != nil {
		return config.TestGameFilterFallBack
	}
	return false
}

func (s *ChannelPlayLogicConfigLoader) GetIsBlReportClose() bool {
	config := s.LoadConfig()
	if config != nil {
		return config.IsBlReportClose
	}
	return false
}

func (s *ChannelPlayLogicConfigLoader) GetUseDismiss() bool {
	config := s.LoadConfig()
	if config != nil {
		return config.UseDismiss
	}
	return false
}

func (s *ChannelPlayLogicConfigLoader) GetNewTabMap() map[uint32]bool {
	config := s.LoadConfig()
	if config != nil {
		return config.NewTabMap
	}
	return map[uint32]bool{}
}

func (s *ChannelPlayLogicConfigLoader) GetCategoryWeightMap(marketId uint32) map[uint32]int {
	config := s.LoadConfig()
	if config != nil {
		return config.CategoryWeightMap[uint16(marketId)]
	}
	return map[uint32]int{}
}

func (s *ChannelPlayLogicConfigLoader) GetNoCheckBlockSetting() bool {
	config := s.LoadConfig()
	if config != nil {
		return config.NoCheckBlockSetting
	}
	return false
}

func (s *ChannelPlayLogicConfigLoader) GetRevenueRoomTabInsertPostionMap() map[uint32][]uint32 {
	config := s.LoadConfig()
	if config != nil && len(config.RevenueRoomTabInsertPostions) != 0 {
		return config.RevenueRoomTabInsertPostions
	}
	return nil
}

func (s *ChannelPlayLogicConfigLoader) GetPlayQuestionTestConfig() *AbtestConfig {
	config := s.LoadConfig()
	if config != nil {
		return config.PlayQuestionTestConfig
	}
	return nil
}

func (s *ChannelPlayLogicConfigLoader) GetRegisterDiyItemTestConfig() *AbtestConfig {
	config := s.LoadConfig()
	if config != nil {
		return config.RegisterDiyItemTestConfig
	}
	return nil
}

func (s *ChannelPlayLogicConfigLoader) IsABTestFilterClose() bool {
	config := s.LoadConfig()
	if config != nil {
		return config.IsABTestFilterClose
	}
	return false
}

func (s *ChannelPlayLogicConfigLoader) GetHomePageFilterTestConfig() *AbtestConfig {
	config := s.LoadConfig()
	if config != nil {
		return config.HomePageFilterTestConfig
	}
	return nil
}

func (s *ChannelPlayLogicConfigLoader) GetShieldWordTestConfig() *AbtestConfig {
	config := s.LoadConfig()
	if config != nil {
		return config.ShieldWordTestConfig
	}
	return nil
}

func (s *ChannelPlayLogicConfigLoader) GetLogSwitch() bool {
	config := s.LoadConfig()
	if config != nil {
		return config.LogSwitch
	}
	return false
}

func (s *ChannelPlayLogicConfigLoader) GetSupervisorPublishNameMsg() string {
	config := s.LoadConfig()
	if config != nil {
		return config.SupervisorPublishNameMsg
	}
	return "无法修改房间名，请点击更多，选择房间名"
}

func (s *ChannelPlayLogicConfigLoader) GetSupervisorCreateRoomNameMsg() string {
	config := s.LoadConfig()
	if config != nil {
		return config.SupervisorCreateRoomNameMsg
	}
	return "无法修改房间名，请点击换一换，选择房间名"
}

func (s *ChannelPlayLogicConfigLoader) GetPublishRoomNameMsg() string {
	config := s.LoadConfig()
	if config != nil {
		return config.PublishRoomNameMsg
	}
	return "这个房间名异常，请更换其他房间名"
}
