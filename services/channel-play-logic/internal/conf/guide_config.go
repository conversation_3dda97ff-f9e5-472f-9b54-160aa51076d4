package conf

import (
	"context"
	"encoding/json"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/runtime/v2/filters/pkg"
	"time"
)

type GuideConfigLoader struct {
	configLoader *pkg.ConfigLoader
}

func NewGuideConfigLoader(filename string) (loader *GuideConfigLoader, err error) {
	loader = &GuideConfigLoader{}

	loader.configLoader, err = pkg.NewConfigLoaderV2(context.Background(), filename, &guideConfigConfig{}, false, 30*time.Second)
	if err != nil {
		log.Errorf("NewGuideConfigLoader NewConfigLoaderV2 filename(%s) err: %v", filename, err)
		return nil, err
	}

	return
}

func (loader *GuideConfigLoader) loadConfig() (cfg *guideConfigConfig) {
	cfg = &guideConfigConfig{}

	if loader.configLoader == nil {
		log.Warnf("GuideConfigLoader LoadConfig configLoader nil")
		return
	}

	e := loader.configLoader.Get()
	if e == nil {
		log.Warnf("GuideConfigLoader LoadConfig Get nil")
		return
	}

	var ok bool
	cfg, ok = e.(*guideConfigConfig)
	if !ok {
		log.Warnf("GuideConfigLoader LoadConfig guideConfigConfig nil")
		return
	}

	return
}

// 引导配置
type guideConfigConfig struct {
	//首页引导帖子配置
	GameUgcContentGuide map[uint32]*GuideConfig `json:"game_ugc_content_guide"`
	//首页引导搭子配置
	GamePalCardGuide    *GuideConfig            `json:"game_pal_card_guide"`
	NewGamePalCardGuide map[uint32]*GuideConfig `json:"new_game_pal_card_guide"` // 每个玩法可以配置各自的位置，没有则使用Position数据
	//展示专区入口，选择动态或者搭子的一起开黑过人数的判断阈值
	PlayTogetherThreshold uint32 `json:"play_together_threshold"`
	//展示专区入口，选择动态或者搭子的互聊过人数的判断阈值
	ChatTogetherThreshold uint32 `json:"chat_together_threshold"`
	//房间列表插入专区引导实验配置
	HomePageListGuideCfg *AbtestConfig `json:"home_page_list_guide_cfg"`
	//点亮中搭子卡阈值
	LightCountThreshold uint32 `json:"light_count_threshold"`
	//帖子数量阈值
	ContentCountThreshold uint32 `json:"content_count_threshold"`
}

func (cfg *guideConfigConfig) UnmarshalBinary(data []byte) error {
	err := json.Unmarshal(data, cfg)
	if err != nil {
		log.Errorf("publicSwitchConfig UnmarshalBinary data(%s) err: %v", data, err)
		return err
	}

	return nil
}

func (loader *GuideConfigLoader) GetUgcContentGuideByTabId(tabId uint32) *GuideConfig {
	return loader.loadConfig().GameUgcContentGuide[tabId]
}

func (loader *GuideConfigLoader) GetGamePalCardGuide(tabId uint32) *GuideConfig {
	if v, ok := loader.loadConfig().NewGamePalCardGuide[tabId]; ok {
		return v
	}
	return loader.loadConfig().GamePalCardGuide
}

func (loader *GuideConfigLoader) GetPlayTogetherThreshold() uint32 {
	return loader.loadConfig().PlayTogetherThreshold
}

func (loader *GuideConfigLoader) GetChatTogetherThreshold() uint32 {
	return loader.loadConfig().ChatTogetherThreshold
}

func (loader *GuideConfigLoader) GetHomePageListGuideCfg() *AbtestConfig {
	return loader.loadConfig().HomePageListGuideCfg
}

func (loader *GuideConfigLoader) GetLightCountThreshold() uint32 {
	if loader.loadConfig().LightCountThreshold == 0 {
		return 5
	}
	return loader.loadConfig().LightCountThreshold
}

func (loader *GuideConfigLoader) GetContentCountThreshold() uint32 {
	if loader.loadConfig().ContentCountThreshold == 0 {
		return 10
	}
	return loader.loadConfig().ContentCountThreshold
}
