package adgame

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"io/ioutil"
	"net"
	"net/http"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"google.golang.org/grpc/metadata"
)

type showAdReq struct {
	Uid    uint32   `json:"uid"`
	ActIds []string `json:"actIds"`
}

type GameAdInfo struct {
	ShowAd        bool `json:"showAd"`
	OftenPlayGame bool `json:"oftenPlayGame"`
}

type showAdRsp struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Result       map[string]bool        `json:"result"`
		GameAdDetail map[string]*GameAdInfo `json:"gameAdDetail"`
	} `json:"data"`
}

type Client struct {
	host string
	cli  *http.Client
}

func NewClient(host string) *Client {
	return &Client{
		host: host,
		cli: &http.Client{
			Transport: &http.Transport{
				Proxy: http.ProxyFromEnvironment,
				DialContext: (&net.Dialer{
					Timeout:   30 * time.Second,
					KeepAlive: 30 * time.Second,
				}).DialContext,
				MaxIdleConns:        100,
				MaxIdleConnsPerHost: 100,
				IdleConnTimeout:     90 * time.Second,
			},
			Timeout: 20 * time.Second,
		},
	}
}

func SetHeaderCanonical(h http.Header, key, value string) {
	h[key] = []string{value}
}

func (c *Client) ShowAd(ctx context.Context, uid uint32, actIdList []string) (map[string]*GameAdInfo, error) {
	const path = "/showAd"

	reqBody, err := json.Marshal(&showAdReq{Uid: uid, ActIds: actIdList})
	if err != nil {
		log.ErrorWithCtx(ctx, "Client ShowAd err: %v", err)
		return nil, err
	}

	log.DebugWithCtx(ctx, "Client ShowAd req body: %s", reqBody)

	ctx2, cancel := context.WithTimeout(ctx, 2*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx2, http.MethodPost, fmt.Sprintf("%s%s", c.host, path), bytes.NewReader(reqBody))
	if err != nil {
		log.ErrorWithCtx(ctx, "Client ShowAd err: %v", err)
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	if conf.GetEnv() == conf.Staging {
		SetHeaderCanonical(req.Header, "x-qw-traffic-mark", "staging")
	}
	//SetHeaderCanonical(req.Header, "x-qw-traffic-mark", "tt-dev-jupiter")
	//req.Header.Set("x-qw-traffic-mark", "tt-dev-jupiter")

	//setHttpHeaderFromContext(req, ctx)

	/*for k, v := range req.Header {
		log.InfoWithCtx(ctx, "Client ShowAd req header: %s: %s", k, v)
	}*/
	resp, err := c.cli.Do(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "Client ShowAd err: %v", err)
		return nil, err
	}

	defer func() { _ = resp.Body.Close() }()

	if resp.StatusCode != http.StatusOK {
		log.ErrorWithCtx(ctx, "Client ShowAd http code %d", resp.StatusCode)
		return nil, protocol.NewExactServerError(nil, status.ErrSys, http.StatusText(resp.StatusCode))
	}

	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "Client ShowAd err: %v", err)
		return nil, err
	}

	log.DebugWithCtx(ctx, "Client ShowAd resp body: %s, reqbody:%s", respBody, reqBody)

	var rsp showAdRsp
	err = json.Unmarshal(respBody, &rsp)
	if err != nil {
		log.ErrorWithCtx(ctx, "Client ShowAd err: %v", err)
		return nil, err
	}

	if rsp.Code != 0 {
		log.ErrorWithCtx(ctx, "Client ShowAd code %d neq 0", rsp.Code)
		return nil, protocol.NewExactServerError(nil, status.ErrSys)
	}

	return rsp.Data.GameAdDetail, nil
}

func setHttpHeaderFromContext(req *http.Request, ctx context.Context) {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return
	}

	for k, v := range md {
		if len(v) > 0 {
			req.Header.Set(k, v[0])
		}
	}
}
