package home_page_head_mgr

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/app/channel-play"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"time"
)

// HomePageHeadConfigEnterCheck 检查入口时间能否跳转
func (s *HomePageHeadMgr) HomePageHeadConfigEnterCheck(ctx context.Context, configId string) (msg string, check bool) {
	t := time.Now()
	for _, c := range conf.HomePageConfigs.GetHomePageHeadConfig() {
		if configId != c.ConfigId {
			continue
		}
		switch c.ConfigType {
		case uint32(pb.HomePageHeadConfigEnum_CONFIG_ESPORTS_TYPE):
			if c.EsportsConfig == nil || c.EsportsConfig.BeginTime == "" || c.EsportsConfig.EndTime == "" {
				break
			}
			return checkTime(ctx, t, c.EsportsConfig.BeginTime, c.EsportsConfig.EndTime)
		}
	}
	return "", true
}

// 检查金刚区可进入时间
func checkTime(ctx context.Context, now time.Time, beginTimeStr, endTimeStr string) (string, bool) {
	today := now.Format("2006-01-02")
	beginTime, err := time.ParseInLocation("2006-01-02 15:04", fmt.Sprintf("%s %s", today, beginTimeStr), now.Location())
	if err != nil {
		log.ErrorWithCtx(ctx, "c.EsportsConfig.BeginTime parse fail, err:%v, time:%s", err, beginTimeStr)
		return "", true
	}
	endTime, err := time.ParseInLocation("2006-01-02 15:04", fmt.Sprintf("%s %s", today, endTimeStr), now.Location())
	if err != nil {
		log.ErrorWithCtx(ctx, "c.EsportsConfig.EndTime parse fail, err:%v, time:%s", err, endTimeStr)
		return "", true
	}
	// 当跨天处理
	msgFormat := "开放时间为%s至%s，请在开放时间再来"
	if beginTime.After(endTime) {
		endTime = endTime.AddDate(0, 0, 1)
		msgFormat = "开放时间为%s至次日%s，请在开放时间再来"
	}
	if now.Before(beginTime) || now.After(endTime) {
		return fmt.Sprintf(msgFormat, beginTimeStr, endTimeStr), false
	}
	return "", true
}
