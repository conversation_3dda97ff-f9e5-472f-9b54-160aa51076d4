package handler

import (
	"context"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/app/channel-play"
)

type TTNewHomePageHandler struct {
	*BaseHandler
}

func NewTTNewHomePageHandler(baseHandler *BaseHandler) *TTNewHomePageHandler {
	return &TTNewHomePageHandler{
		BaseHandler: baseHandler,
	}
}

func (s *TTNewHomePageHandler) DoHandle(ctx context.Context) ([]*pb.HomePageHeadConfigItem, error) {
	// 新版tt首页有优先级展示逻辑，特殊处理
	resConfigs := make([]*pb.HomePageHeadConfigItem, 0, len(s.configFactory.GetConfigs()))
	for _, c := range s.configFactory.GetConfigs() {
		if c.GetConfigType() == uint32(pb.HomePageHeadConfigEnum_CONFIG_COMPETITION_CENTER_TYPE) || c.GetConfigType() == uint32(pb.HomePageHeadConfigEnum_CONFIG_ESPORTS_TYPE) {
			continue
		}
		var item *pb.HomePageHeadConfigItem
		if c.GetConfigIsReplace() {
			if item = s.GetReplaceConfig(ctx, c.GetConfigIsReplace()); item == nil {
				item = c.GetConfig(ctx)
			}
		} else {
			item = c.GetConfig(ctx)
		}
		//log.DebugWithCtx(ctx, "=============DoHandle configType: %d,%d", c.GetConfigType(), item.ConfigType)
		if item != nil {
			resConfigs = append(resConfigs, item)
		} else {
			log.ErrorWithCtx(ctx, "DoHandle err: item nil, configType: %d", c.GetConfigType())
		}
	}
	return resConfigs, nil
}
