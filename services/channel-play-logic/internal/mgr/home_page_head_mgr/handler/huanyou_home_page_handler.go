package handler

import (
	"context"
	"golang.52tt.com/pkg/log"
	ga_base "golang.52tt.com/protocol/app"
	pb "golang.52tt.com/protocol/app/channel-play"
)

type HuanyouHomePageHandler struct {
	*BaseHandler
}

func NewHuanyouHomePageHandler(baseHandler *BaseHandler) *HuanyouHomePageHandler {
	return &HuanyouHomePageHandler{
		BaseHandler: baseHandler,
	}
}

func (s *HuanyouHomePageHandler) DoHandle(ctx context.Context) ([]*pb.HomePageHeadConfigItem, error) {
	// 入口仅在TT和欢游展示，麦可和谜境不展示
	app := ga_base.BaseReq_MarketId(s.commonParam.SvrInfo.MarketID)
	if app != ga_base.BaseReq_MARKET_NONE && app != ga_base.BaseReq_MARKET_HUANYOU {
		log.DebugWithCtx(ctx, "GetHomePageHeadConfigs invalid marketId %d", s.commonParam.SvrInfo.MarketID)
		return nil, nil
	}
	resConfigs := make([]*pb.HomePageHeadConfigItem, 0, len(s.configFactory.GetConfigs()))

	for _, c := range s.configFactory.GetConfigs() {
		// 来源类型过滤
		switch c.GetConfigType() {
		case uint32(pb.HomePageHeadConfigEnum_CONFIG_COMPETITION_CENTER_TYPE):
			competitionData := c.GetConfig(ctx)
			if competitionData != nil {
				resConfigs = append(resConfigs, competitionData)
			} else {
				log.WarnWithCtx(ctx, "DoHandle err: item nil, configType: %d", c.GetConfigType())
			}

		case uint32(pb.HomePageHeadConfigEnum_CONFIG_ESPORTS_TYPE):
			esportsConfig := c.GetConfig(ctx)
			if esportsConfig != nil {
				resConfigs = append(resConfigs, esportsConfig)
			} else {
				log.InfoWithCtx(ctx, "DoHandle item nil, configType: %d", c.GetConfigType())
			}
		}
	}
	return resConfigs, nil

}
