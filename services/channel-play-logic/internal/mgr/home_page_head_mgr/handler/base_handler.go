package handler

import (
	"context"
	pb "golang.52tt.com/protocol/app/channel-play"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/home_page_head_mgr/config"
)

type BaseHandlerI interface {
	DoHandle(ctx context.Context) ([]*pb.HomePageHeadConfigItem, error)
}

type BaseHandler struct {
	configFactory *config.ConfigFactory
	commonParam   *config.CommonParam
}

func NewBaseHandler(configFactory *config.ConfigFactory, commonParam *config.CommonParam) *BaseHandler {
	return &BaseHandler{configFactory: configFactory, commonParam: commonParam}
}

// 获取新版TT可替换的数据配置
func (s *BaseHandler) GetReplaceConfig(ctx context.Context, isReplace bool) *pb.HomePageHeadConfigItem {
	if !isReplace {
		return nil
	}
	// 电竞数据
	for _, c := range s.configFactory.GetConfigs() {
		if c.GetConfigType() != uint32(pb.HomePageHeadConfigEnum_CONFIG_ESPORTS_TYPE) {
			continue
		}
		esportsConfig := c.GetConfig(ctx)
		if esportsConfig != nil {
			return esportsConfig
		}
		break
	}
	// 赛事数据
	for _, c := range s.configFactory.GetConfigs() {
		if c.GetConfigType() != uint32(pb.HomePageHeadConfigEnum_CONFIG_COMPETITION_CENTER_TYPE) {
			continue
		}
		competitionData := c.GetConfig(ctx)
		if competitionData != nil {
			return competitionData
		}
		break
	}
	return nil
}
