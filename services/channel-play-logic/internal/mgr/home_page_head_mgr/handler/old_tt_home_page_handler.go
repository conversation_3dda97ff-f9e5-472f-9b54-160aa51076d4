package handler

import (
	"context"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/app/channel-play"
)

type TTOldHomePageHandler struct {
	*BaseHandler
}

func NewTTOldHomePageHandler(baseHandler *BaseHandler) *TTOldHomePageHandler {
	return &TTOldHomePageHandler{
		BaseHandler: baseHandler,
	}
}

func (s *TTOldHomePageHandler) DoHandle(ctx context.Context) ([]*pb.HomePageHeadConfigItem, error) {
	resConfigs := make([]*pb.HomePageHeadConfigItem, 0, len(s.configFactory.GetConfigs()))
	for _, c := range s.configFactory.GetConfigs() {
		// 来源类型过滤
		if c.GetConfigType() == uint32(pb.HomePageHeadConfigEnum_CONFIG_ESPORTS_TYPE) {
			continue
		}
		item := c.GetConfig(ctx)
		if item != nil {
			resConfigs = append(resConfigs, item)
		} else {
			log.ErrorWithCtx(ctx, "DoHandle err: item nil, configType: %d", c.GetConfigType())
		}
	}
	return resConfigs, nil
}
