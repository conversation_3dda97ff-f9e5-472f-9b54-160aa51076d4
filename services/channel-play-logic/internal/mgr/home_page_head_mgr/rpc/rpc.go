package rpc

import (
	"context"
	account "golang.52tt.com/clients/account-go"
	channel_play_tab "golang.52tt.com/clients/channel-play-tab"
	entrance "golang.52tt.com/clients/competition-entrance"
	game_card "golang.52tt.com/clients/game-card"
	gangup_channel_cli "golang.52tt.com/clients/gangup-channel"
	iop "golang.52tt.com/clients/iop-proxy"
	tcTab "golang.52tt.com/clients/topic-channel/tab"
	"golang.52tt.com/pkg/abtest"
	muse_integration_middlelayer "golang.52tt.com/protocol/services/muse-integration-middlelayer"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/home_page_head_mgr/competition/adgame"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/home_page_head_mgr/competition/rate"
	"time"
)

var (
	AccountClient        account.IClient
	GangupChannelClient  *gangup_channel_cli.Client
	TCTabClient          tcTab.IClient
	GameCardCli          game_card.IClient
	EnCli                *entrance.Client
	IopCli               *iop.Client
	AdGameCli            *adgame.Client
	Lim                  *rate.Limiter
	ABTestClient         abtest.IABTestClient
	MusicMiddleCli       muse_integration_middlelayer.MuseIntegrationMiddlelayerClient
	ChannelPlayTabClient *channel_play_tab.Client
)

func InitClient(ctx context.Context, accountClient account.IClient, gangupChannelClient *gangup_channel_cli.Client, tCTabClient tcTab.IClient, gameCardCli game_card.IClient,
	abtestClient abtest.IABTestClient,channelPlayTabClient *channel_play_tab.Client) {
	AccountClient = accountClient
	GangupChannelClient = gangupChannelClient
	TCTabClient = tCTabClient
	GameCardCli = gameCardCli
	EnCli, _ = entrance.NewClient()
	IopCli, _ = iop.NewClient()
	AdGameCli = adgame.NewClient(conf.HomePageConfigs.GetAdGame().GetHost())
	Lim = rate.NewLimiter(1000, time.Millisecond)
	ABTestClient = abtestClient
	MusicMiddleCli, _ = muse_integration_middlelayer.NewClient(ctx)
	ChannelPlayTabClient = channelPlayTabClient
}
