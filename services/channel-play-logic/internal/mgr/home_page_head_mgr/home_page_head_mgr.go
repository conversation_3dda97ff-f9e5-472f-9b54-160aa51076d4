package home_page_head_mgr

import (
	"context"
	"encoding/json"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/device_id"
	account "golang.52tt.com/clients/account-go"
	channel_play_tab "golang.52tt.com/clients/channel-play-tab"
	game_card "golang.52tt.com/clients/game-card"
	gangup_channel_cli "golang.52tt.com/clients/gangup-channel"
	tcTab "golang.52tt.com/clients/topic-channel/tab"
	"golang.52tt.com/kaihei-pkg/supervision"
	"golang.52tt.com/pkg/abtest"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/app/channel-play"
	"golang.52tt.com/protocol/common/status"
	gangUpPb "golang.52tt.com/protocol/services/gangup-channel"
	muse_integration_middlelayer "golang.52tt.com/protocol/services/muse-integration-middlelayer"
	tabpb "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/home_page_head_mgr/config"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/home_page_head_mgr/handler"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/home_page_head_mgr/rpc"
	"golang.52tt.com/services/channel-play-logic/internal/utils"

	"math/rand"
	"sync"
	"time"
)

const (
	WangZheRongYaoTabID        = 1
	HePingJingYingTabId        = 2
	MusicTypeConfigCount       = 2
	gameZoneNewbieGuideDefault = "contrast" //开黑专区新手引导对照组
	gameZoneNewbieGuideButton  = "testa"    //开黑专区新手引导实验组，按钮样式
)

type HomePageHeadMgr struct {
	teamNum    int64                                 // 游戏专区展示文案 xxx场组队
	accounts   []string                              // 游戏专区展示用户账号头像
	tabInfoExt []*tabpb.TabInfoExtHomePageHeadConfig // 金刚区跟玩法配置 extInfo
	sync.RWMutex
	supervisorInst *supervision.Supervisory

	pcRecommendTabRandomTeamNumMap map[uint32]int64
}

func NewHomePageHeadMgr(accountClient account.IClient, gangupChannelClient *gangup_channel_cli.Client, tCTabClient tcTab.IClient,
	gameCardCli game_card.IClient, aBTestClient abtest.IABTestClient, channelPlayTabClient *channel_play_tab.Client,
	supervisorInst *supervision.Supervisory) (*HomePageHeadMgr, error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()

	rpc.InitClient(ctx, accountClient, gangupChannelClient, tCTabClient, gameCardCli, aBTestClient, channelPlayTabClient)

	mgr := &HomePageHeadMgr{
		supervisorInst: supervisorInst,
	}
	if err := mgr.getTeamNumData(ctx); err != nil {
		log.ErrorWithCtx(ctx, "UserLine Err: getEntranceData init fail, err:%v", err)
	}

	if err := mgr.setLocalRandomAccounts(ctx); err != nil {
		log.ErrorWithCtx(ctx, "UserLine Err: getLocalRandomAccounts init fail, err:%v", err)
	}

	if err := mgr.setAllTabInfoExt(ctx); err != nil {
		log.ErrorWithCtx(ctx, "UserLine Err: setAllTabInfoExt init fail, err:%v", err)
	}

	if err := mgr.getPcRecommendRandNumMap(ctx); err != nil {
		log.ErrorWithCtx(ctx, "UserLine Err: getPcRecommendRandNumMap init fail, err:%v", err)
	}

	//go mgr.homePageSchedule()
	go mgr.entranceDataSchedule()
	return mgr, nil
}

func (s *HomePageHeadMgr) entranceDataSchedule() {
	pCtx := context.Background()
	ticker := time.NewTicker(time.Second * 10)
	defer func() {
		err := recover()
		if err != nil {
			log.ErrorWithCtx(pCtx, "HomePageHeadMgr panic, err:%v ", err)
		}
		ticker.Stop()
	}()

	for range ticker.C {
		ctx, cancel := context.WithTimeout(pCtx, time.Second*5)
		_ = s.getTeamNumData(ctx)
		_ = s.setAllTabInfoExt(ctx)
		_ = s.getPcRecommendRandNumMap(ctx)
		cancel()
	}
}

func (s *HomePageHeadMgr) getPcRecommendRandNumMap(ctx context.Context) error {
	cfgs := conf.PCHomePageConfigs.GetPcHomePageHeadConfigs()
	if len(cfgs) == 0 {
		log.ErrorWithCtx(ctx, "UserLine Err: getPcRecommendRandNumMap GetPcHomePageHeadConfigs config is empty")
		return nil
	}
	tabIds := make([]uint32, 0, len(cfgs))
	for _, cfg := range cfgs {
		if cfg.ConfigType != uint32(pb.CfgType_CFG_TYPE_GAME_TAB) || cfg.GameTabConfig == nil {
			continue
		}
		tabIds = append(tabIds, cfg.GameTabConfig.TabId)
	}
	resp, err := rpc.GangupChannelClient.GetReleasingChannelRandomCountByTabIds(ctx, &gangUpPb.GetReleasingChannelCountByTabIdsReq{
		TabIds: tabIds,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "UserLine Time Err: getPcRecommendRandNumMap GetReleasingChannelRandomCountByTabIds err:%v", err)
		return err
	}
	s.pcRecommendTabRandomTeamNumMap = resp.GetCountMap()
	return nil
}

func (s *HomePageHeadMgr) getTeamNumData(ctx context.Context) error {
	tabIds := conf.ChannelPlayLogicConfig.GetHomePageTeamNumTabIds(ctx)
	if len(tabIds) == 0 {
		log.ErrorWithCtx(ctx, "UserLine Err: getTeamNumData GetHomePageTeamNumTabIds config is empty")
		tabIds = []uint32{WangZheRongYaoTabID, HePingJingYingTabId} // 兜底 王者+和平精英
	}
	teamCount, err := s.getTeamCountByTabId(ctx, tabIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "UserLine Time Err: getTeamNumData getTeamCountByTabId err:%v", err)
		return err
	}
	if teamCount == 0 {
		log.ErrorWithCtx(ctx, "UserLine Time Err: getTeamNumData teamCount is 0")
		return nil
	}
	s.teamNum = teamCount
	return nil
}

func (s *HomePageHeadMgr) getTeamCountByTabId(ctx context.Context, tabIds []uint32) (int64, error) {
	resp, err := rpc.GangupChannelClient.GetReleasingChannelCountByTabIds(ctx, &gangUpPb.GetReleasingChannelCountByTabIdsReq{
		TabIds: tabIds,
	})
	if err != nil {
		return 0, err
	}
	count := int64(0)
	for _, v := range resp.GetCountMap() {
		count += v
	}
	return count, nil
}

func (s *HomePageHeadMgr) setLocalRandomAccounts(ctx context.Context) error {
	robotUids := utils.GetRobotData()
	n := len(robotUids)
	limit := 100
	accounts := make([]string, 0, n)
	for l := 0; l < n; l += limit {
		r := l + limit
		if r > n {
			r = n
		}
		uids := robotUids[l:r]
		userMap, err := rpc.AccountClient.GetUsersMap(ctx, uids)
		if err != nil {
			log.ErrorWithCtx(ctx, "getLocalRandomAccounts BatGetUserByUid fail, err:%s", err.Error())
			continue
		}
		for _, v := range userMap {
			if len(v.GetUsername()) == 0 {
				log.WarnWithCtx(ctx, "user account is empty, userInfo:%+v", v)
				continue
			}
			accounts = append(accounts, v.GetUsername())
		}
	}
	log.InfoWithCtx(ctx, "getLocalRandomAccounts len(accounts):%d, len(robotUids):%d", len(accounts), n)

	if len(accounts) != n {
		log.ErrorWithCtx(ctx, "getLocalRandomAccounts len not equal, len(robotUids):%d, len(accounts):%d", n, len(accounts))
	}
	if len(accounts) < n/2 {
		log.ErrorWithCtx(ctx, "UserLine Err:getLocalRandomAccounts fail, len(robotUids):%d, len(accounts):%d", n, len(accounts))
		return protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError, "getLocalRandomAccounts fail")
	}
	s.accounts = accounts
	return nil
}

func (s *HomePageHeadMgr) getRandomAccounts(cnt int) []string {
	n := len(s.accounts)
	if cnt <= 0 || n == 0 {
		return nil
	}
	accounts := make([]string, 0, cnt)
	idx := rand.Intn(n) //nolint:gosec
	for i := 0; i < cnt; i++ {
		a := s.accounts[(idx+i)%n]
		accounts = append(accounts, a)
	}
	return accounts
}

func (s *HomePageHeadMgr) setAllTabInfoExt(ctx context.Context) error {
	rsp, err := rpc.TCTabClient.GetAllTabInfoExt(ctx, &tabpb.GetAllTabInfoExtReq{
		ExtType: tabpb.TabInfoExtEnum_TAB_INFO_EXT_HOME_PAGE_HEAD_CONFIG,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "setAllTabInfoExt fail, err:%v", err)
		return err
	}
	tabInfoExt := make([]*tabpb.TabInfoExtHomePageHeadConfig, 0, len(rsp.GetItems()))
	for _, info := range rsp.GetItems() {
		tmpInfo := &tabpb.TabInfoExtHomePageHeadConfig{}
		mErr := json.Unmarshal([]byte(info.GetExtInfo()), tmpInfo)
		if mErr != nil {
			log.ErrorWithCtx(ctx, "getTabInfoExt json Unmarshal fail, err:%s, info:%v", mErr.Error(), info)
			continue
		}
		tmpInfo.TabId = info.TabId
		tabInfoExt = append(tabInfoExt, tmpInfo)
	}

	log.InfoWithCtx(ctx, "setAllTabInfoExt len(tabInfoExt):%d", len(tabInfoExt))

	s.Lock()
	s.tabInfoExt = tabInfoExt
	s.Unlock()
	return nil
}

func (s *HomePageHeadMgr) getLocalTabInfoExt() []*tabpb.TabInfoExtHomePageHeadConfig {
	s.RLock()
	defer s.RUnlock()
	return s.tabInfoExt
}

// GetHomePageHeadConfigs 获取首页数据
func (s *HomePageHeadMgr) GetHomePageHeadConfigs(ctx context.Context, svrInfo *grpc.ServiceInfo, req *pb.HomePageHeadConfigReq) ([]*pb.HomePageHeadConfigItem, error) {
	commonParam := &config.CommonParam{
		SvrInfo: svrInfo,
		Req:     req,
	}
	gameConfigParam := &config.GameConfigParams{
		TeamNum:        s.teamNum,
		Accounts:       s.getRandomAccounts(10),
		TabInfoExt:     s.getLocalTabInfoExt(),
		SvrInfo:        svrInfo,
		SupervisorInst: s.supervisorInst,
	}
	//新版本音乐配置（可滑动的金刚区）
	newVersion := conf.HomePageConfigs.GetMusicZoneConfig().NewVersion
	musicConfigInfo, musicConfigInfoListMap := s.getMusicConfigInfo(ctx, svrInfo)

	musicConfigParam := &config.MusicConfigParams{
		MusicConfigInfo:        musicConfigInfo,
		MusicConfigInfoListMap: musicConfigInfoListMap,
		NewVersion:             utils.IsHigherVersion(svrInfo.ClientVersion, newVersion),
	}
	log.DebugWithCtx(ctx, "GetHomePageHeadConfigs musicConfigParam:%+v", musicConfigParam)
	handler := handler.NewHandlerFactory().GetHandler(ctx, commonParam, gameConfigParam, musicConfigParam)

	if handler == nil {
		return nil, nil
	}
	res, err := handler.DoHandle(ctx)
	return res, err
}

// GetHomePageHeadConfigById 获取金刚区配置
func (s *HomePageHeadMgr) GetHomePageHeadConfigById(configId string) *conf.HomePageHeadConfig {
	for _, c := range conf.HomePageConfigs.GetHomePageHeadConfig() {
		if c.ConfigId == configId {
			return c
		}
	}
	return nil
}

// 开黑专区引导样式
func (s *HomePageHeadMgr) GetGameZoneGuide(ctx context.Context, svrInfo *grpc.ServiceInfo, cfg *conf.HomePageHeadConfig) *conf.GuideContent {
	if !utils.IsHigherVersion(svrInfo.ClientVersion, "6.49.0") {
		return cfg.GuideContent
	}

	var experimentRes string
	abtestCfg := conf.HomePageConfigs.GetGameZoneNewbieGuideCfg()
	gameZoneNewbieGuideKey := abtestCfg.ArgvName
	newCtx, cancel := context.WithTimeout(ctx, 200*time.Millisecond)
	defer cancel()
	deviceId, err := device_id.ToClientDeviceId(device_id.ToDeviceHexId(svrInfo.DeviceID, true), uint32(svrInfo.ClientType))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameZoneGuide device_id.ToClientDeviceId svInfo:%s err:%v", svrInfo.String(), err)
		return cfg.GuideContent
	}
	testList, err := rpc.ABTestClient.GetUserTestListInLayerTagByDeviceId(newCtx, deviceId, abtestCfg.LayerTag)
	if err != nil {
		log.ErrorWithCtx(newCtx, "GetGameZoneGuide GetUserTestListInLayerTag svrInfo(%s) err: %v", svrInfo.String(), err)
		//兜底走旧逻辑
		return cfg.GuideContent
	}
	if len(testList) == 0 {
		log.WarnWithCtx(ctx, "GetGameZoneGuide svInfo(%s) testList empty", svrInfo)
		return cfg.GuideContent
	}

	log.DebugWithCtx(ctx, "GetGameZoneGuide svInfo(%s) testList: %+v", svrInfo, testList)
	for _, test := range testList {
		for _, expt := range test.ExptList {
			if result, ok := expt.ExptArgv[gameZoneNewbieGuideKey]; ok {
				experimentRes = result
				break
			}
		}
	}

	if experimentRes == gameZoneNewbieGuideButton {
		return conf.HomePageConfigs.GetGameZoneGuideContentExperiment()
	}

	return cfg.GuideContent
}

func (s *HomePageHeadMgr) getMusicConfigInfo(oldCtx context.Context,
	svrInfo *grpc.ServiceInfo) (map[uint32]*muse_integration_middlelayer.HomePageHeadMuseConfig,
	map[uint32][]*muse_integration_middlelayer.HomePageHeadMuseConfig) {
	// 判断版本号，6.6.5版本之后才有MT专区
	if !utils.IsHigherVersion(svrInfo.ClientVersion, "6.61.5") {
		return nil, nil
	}
	// ctx 300ms超时控制
	timeOut := time.Duration(300)
	if conf.Environment == conf.Testing {
		timeOut = time.Duration(1000)
	}
	ctx, cancel := context.WithTimeout(oldCtx, timeOut*time.Millisecond)
	defer cancel()

	rsp, err := rpc.MusicMiddleCli.GetHomePageHeadMuseConfig(ctx, &muse_integration_middlelayer.GetHomePageHeadMuseConfigRequest{
		ConfigTypes: []uint32{uint32(pb.HomePageHeadConfigEnum_CONFIG_MUSIC_TYPE), uint32(pb.HomePageHeadConfigEnum_CONFIG_SOUND_SCENE_TYPE)},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getMusicConfigInfo GetHomePageHeadMuseConfig err: %v", err)
		return nil, nil
	}
	dataMapList := make(map[uint32][]*muse_integration_middlelayer.HomePageHeadMuseConfig, MusicTypeConfigCount)
	for configType, mapList := range rsp.GetConfigDataMapList() {
		dataMapList[configType] = append(dataMapList[configType], mapList.GetConfigList()...)
	}
	return rsp.GetConfigDataMap(), dataMapList
}
