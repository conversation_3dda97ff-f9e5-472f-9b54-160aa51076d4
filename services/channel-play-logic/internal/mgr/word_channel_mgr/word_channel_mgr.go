package word_channel_mgr

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/clients/account-go"
	channelMsgApi "golang.52tt.com/clients/channel-msg-api"
	gangup_channel "golang.52tt.com/clients/gangup-channel"
	"golang.52tt.com/pkg/log"
	channelGA "golang.52tt.com/protocol/app/channel"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	channel_scheme "golang.52tt.com/protocol/app/channel-scheme"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	channel_scheme_middle "golang.52tt.com/protocol/services/channel-scheme-middle"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/channel_operation"
	"time"
)

const OneDay = 24 * 60 * 60 //一天

type WordChannelMgr struct {
	gangupClient              *gangup_channel.Client
	commonHandler             *channel_operation.CommonHandle
	AccountGoClient           account_go.IClient
	channelMsgApiClient       channelMsgApi.IClient
	channelSchemeMiddleClient *channel_scheme_middle.Client
}

func NewWordChannelMgr(gangupClient *gangup_channel.Client, AccountGoClient account_go.IClient,
	commonHandler *channel_operation.CommonHandle, channelMsgApiClient channelMsgApi.IClient,
	schemeMidClient *channel_scheme_middle.Client) *WordChannelMgr {
	return &WordChannelMgr{
		gangupClient:              gangupClient,
		commonHandler:             commonHandler,
		AccountGoClient:           AccountGoClient,
		channelMsgApiClient:       channelMsgApiClient,
		channelSchemeMiddleClient: schemeMidClient,
	}
}

func (w *WordChannelMgr) SetUgcChannelPlayMode(ctx context.Context, cid, uid uint32, mode channel_play.UgcChannelPlayMode) (err error) {
	//校验是否房主
	_, err = w.commonHandler.IsChannelOwner(ctx, uid, cid)
	if err != nil {
		return
	}
	//房间模式映射为玩法类型
	schemeDetailType := w.getSchemeDetailTypeByMode(mode)
	//设置玩法类型
	resp, err := w.channelSchemeMiddleClient.SwitchCurChannelSchemeType(ctx, &channel_scheme_middle.SwitchCurChannelSchemeTypeReq{
		OpUid:            uid,
		Cid:              cid,
		SchemeDetailType: schemeDetailType,
		Source:           channel_scheme_middle.Source_UGC_GAME_SWITCH_MODE,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SetUgcChannelPlayMode SwitchCurChannelSchemeType uid:%d, cid:%d, mode:%v,err:%v", uid, cid, mode, err)
		return
	}
	log.InfoWithCtx(ctx, "SetUgcChannelPlayMode SwitchCurChannelSchemeType uid:%d, cid:%d, mode:%v, resp:%s", uid,
		cid, mode, resp.String())
	return err
}

func (w *WordChannelMgr) getSchemeDetailTypeByMode(mode channel_play.UgcChannelPlayMode) uint32 {
	if mode == channel_play.UgcChannelPlayMode_WORD_MODE {
		return uint32(channel_scheme.SchemeDetailType_SCHEME_DETAIL_TYPE_GAME_WORD)
	}
	return uint32(channel_scheme.SchemeDetailType_SCHEME_DETAIL_TYPE_GAME)
}

func (w *WordChannelMgr) GetWordChannelGuide(ctx context.Context, tabId, version, uid uint32) (latestVersion uint32, title string) {
	tabInfo := cache.GetTabInfoCache().GetTabInfoCacheById(tabId)
	if tabInfo == nil {
		//log.ErrorWithCtx(ctx, "GetWordChannelGuide tabInfo not exist tabId:%d, version:%d", tabId, version)
		return
	}
	//只在开黑游戏房展示引导气泡
	if topic_channel.CategoryType(tabInfo.GetCategoryMapping()) != topic_channel.CategoryType_Gangup_type {
		log.InfoWithCtx(ctx, "GetWordChannelGuide not gangup_type tabId:%d, version:%d", tabId, version)
		return
	}
	setting := conf.ChannelPlayLogicConfig.GetWordChannelGuideSetting()
	if setting == nil || setting.Version <= version {
		return
	}
	resp, err := w.AccountGoClient.GetUserByUid(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWordChannelGuide tabId:%d, version:%d AccountGoClient.GetUserByUid err:%v ", tabId, version, err)
		return
	}
	curTime := uint32(time.Now().Unix())
	//注册时间>1个自然日，才需要有引导气泡
	if curTime-resp.GetRegisteredAt() <= OneDay {
		return
	}

	if curTime >= setting.StartTime && curTime <= setting.EndTime {
		latestVersion = setting.Version
		title = setting.Title
	}
	return
}

func (w *WordChannelMgr) SendTypingBroadcast(ctx context.Context, uid, cid uint32) (string, error) {

	msg := &channel_play.TypeStatusPushMsg{
		Uid: []uint32{uid},
		Cid: cid,
	}
	marshalMsg, err := proto.Marshal(msg)
	if err != nil {
		return "", err
	}
	requestId, err := w.channelMsgApiClient.SimplePushToChannel(ctx, uid, cid,
		uint32(channelGA.ChannelMsgType_UGC_WORD_CHANNEL_TYPING_STATUS_MSG), "", marshalMsg)
	if err != nil {
		return requestId, err
	}
	log.InfoWithCtx(ctx, "SendTypingBroadcast success uid:%d, cid:%d, requestId:%s", uid, cid, requestId)
	return requestId, nil
}
