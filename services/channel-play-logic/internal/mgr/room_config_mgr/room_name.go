package room_config_mgr

import (
	"context"
	tcTab "golang.52tt.com/clients/topic-channel/tab"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/app/channel-play"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/rcmd/rcmd_channel_name"
	tabPb "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"strings"
	"time"
)

const (
	RoomNamePageSize = 15
	DefaultRoomName  = "快来和我一起玩吧"
)

type RoomConfigMgr struct {
	tabCli             tcTab.IClient
	rcmdChannelNameCli *rcmd_channel_name.Client
}

func NewRoomConfigMgr(tabCli tcTab.IClient, rcmdChannelNameCli *rcmd_channel_name.Client) (*RoomConfigMgr, error) {
	roomConfigMgr := &RoomConfigMgr{
		tabCli:             tabCli,
		rcmdChannelNameCli: rcmdChannelNameCli,
	}
	return roomConfigMgr, nil
}

func (m *RoomConfigMgr) GetRoomNameList(ctx context.Context, uid, marketId, clientType uint32, in *pb.GetDefaultRoomNameListReq) (
	roomNameList []*pb.DefaultRoomNameConfig, nextPage uint32, loadFinish bool, err error) {
	roomNameList = make([]*pb.DefaultRoomNameConfig, 0)

	if protocol.IsFastPcClientType(clientType) {
		marketId = 1000 //极速pc模拟1000的马甲包
	}

	var getRcmdData bool
	//NODE：这个时间段内，不走推荐房间名
	if conf.ChannelPlayLogicConfig.LoadConfig().DiyNameLimitStartTime != 0 && conf.ChannelPlayLogicConfig.LoadConfig().DiyNameLimitEndTime != 0 &&
		time.Now().Unix() >= conf.ChannelPlayLogicConfig.LoadConfig().DiyNameLimitStartTime &&
		time.Now().Unix() <= conf.ChannelPlayLogicConfig.LoadConfig().DiyNameLimitEndTime && !conf.ChannelPlayLogicConfig.GetDiyNameLimitWhiteMarket()[marketId] {
		getRcmdData = false
	} else {
		switch in.GetReqSource() {
		case uint32(pb.GetDefaultRoomNameListReq_DEFAULT):
			getRcmdData = false
		case uint32(pb.GetDefaultRoomNameListReq_AFTER_661_VERSION):
			inGangupChannel, err := m.isUserInGangupChannel(in.GetTabId(), clientType)
			if err != nil {
				return roomNameList, in.GetPageIndex(), true, err
			}
			// 6.61.5之后的开黑房走推荐房间名
			if inGangupChannel {
				getRcmdData = true
			}
		default:
			log.ErrorWithCtx(ctx, "GetRoomNameList invalid reqSource: %d", in.GetReqSource())
			return roomNameList, in.GetPageIndex(), true, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
		}
	}

	if getRcmdData {
		roomNameList, loadFinish, err = m.GetRcmdRoomNameList(ctx, uid, in.GetTabId(), in.GetGetMode(), in.GetRoomName())
	} else {
		roomNameList, nextPage, err = m.GetDefaultRoomNameList(ctx, in.GetTabId(), clientType, in.GetRoomName(), in.GetPageIndex())
		if nextPage == in.GetPageIndex() { // 6.61.5之前客户端通过nextPage==in.GetPageIndex()判断是否加载完成
			loadFinish = true
		}
	}
	return roomNameList, nextPage, loadFinish, err
}

func (m *RoomConfigMgr) isUserInGangupChannel(tabId, clientType uint32) (result bool, err error) {
	tabInfo := cache.GetTabInfoCache().GetTabInfoCacheById(tabId)
	if tabInfo.GetCategoryMapping() == uint32(topic_channel.CategoryType_Gangup_type) {
		result = true
	}
	if protocol.IsFastPcClientType(clientType) {
		result = true
	}
	return result, nil
}

func (m *RoomConfigMgr) GetRcmdRoomNameList(ctx context.Context, uid, tabId, getMode uint32, roomName string) (out []*pb.DefaultRoomNameConfig, loadFinish bool, err error) {
	rcmdReq := &rcmd_channel_name.GetChannelNameReq{
		Uid:     uid,
		TabId:   tabId,
		ReqType: rcmd_channel_name.GetChannelNameReq_ReqType(getMode),
		Text:    roomName,
	}
	channelNameResp, err := m.rcmdChannelNameCli.GetChannelName(ctx, rcmdReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRcmdRoomNameList rcmdChannelNameCli.GetChannelName err(%v)", err)
		return nil, false, err
	}
	if len(channelNameResp.GetItem()) == 0 {
		log.WarnWithCtx(ctx, "GetRcmdRoomNameList rcmdChannelNameCli.GetChannelName empty, rcmdReq: %+v", rcmdReq.String())
		return out, true, nil
	}
	log.InfoWithCtx(ctx, "GetRcmdRoomNameList rcmdChannelNameCli.GetChannelName len(channelNameList):%d, rcmdReq:%s", len(channelNameResp.GetItem()), rcmdReq.String())

	out = make([]*pb.DefaultRoomNameConfig, 0, len(channelNameResp.GetItem()))
	for _, item := range channelNameResp.GetItem() {
		out = append(out, convertRcmdRoomNameConfig(item))
	}
	return out, channelNameResp.GetBottomReach(), nil
}

func (m *RoomConfigMgr) GetDefaultRoomNameList(ctx context.Context, tabId, clientType uint32, roomName string, pageIndex uint32) (out []*pb.DefaultRoomNameConfig, nextPage uint32, err error) {
	out = make([]*pb.DefaultRoomNameConfig, 0)
	//1,从缓存中获取对应tab的官方房名信息
	names := cache.GetOfficialRoomNameByTabId(tabId, clientType)
	if names == nil {
		log.WarnWithCtx(ctx, "GetDefaultRoomNameList tabId %v not exist", tabId)
		out = []*pb.DefaultRoomNameConfig{
			{
				Name: DefaultRoomName,
			},
		}
		return out, pageIndex, nil
	}

	if pageIndex == 0 { //房间名搜索
		roomName = handleRoomName(roomName)
		//2, 模糊匹配过滤roomName
		for _, v := range names {
			if strings.Contains(v.Name, roomName) {
				out = append(out, convertRoomNameConfig(v))
			}
		}
	} else { //全量分页查询
		beginIndex := int((pageIndex - 1) * RoomNamePageSize)
		if len(names) < beginIndex {
			log.ErrorWithCtx(ctx, "GetDefaultRoomNameList error pageIndex %v beginIndex %v listSize %v", pageIndex, beginIndex, len(names))
			return out, nextPage, protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError, "error pageIndex")
		}
		//确定下一页index
		if len(names) > beginIndex+RoomNamePageSize {
			nextPage = pageIndex + 1
		} else {
			nextPage = pageIndex
		}
		for i := beginIndex; i < (beginIndex+RoomNamePageSize) && i < len(names); i++ {
			out = append(out, convertRoomNameConfig(names[i]))
		}

	}

	return out, nextPage, nil
}

func convertRoomNameConfig(in *tabPb.OfficialRoomNameConfig) *pb.DefaultRoomNameConfig {
	return &pb.DefaultRoomNameConfig{
		Name:   in.GetName(),
		ElemId: in.GetElemId(),
	}
}

func convertRcmdRoomNameConfig(in *rcmd_channel_name.ChannelNameItem) *pb.DefaultRoomNameConfig {
	return &pb.DefaultRoomNameConfig{
		Name:   in.GetText(),
		Source: in.GetType(),
	}
}

// 处理搜索的字符串。去除左右中间空格
func handleRoomName(in string) string {
	roomName := strings.TrimSpace(in)
	names := strings.Split(roomName, " ")
	return strings.Join(names, "")
}
