package channel_list

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata"
	channel_play_tab "golang.52tt.com/clients/channel-play-tab"
	gangup_channel "golang.52tt.com/clients/gangup-channel"
	tcChannel "golang.52tt.com/clients/topic-channel/channel"
	recommendation_gen "golang.52tt.com/clients/topic-channel/recommendation-gen"
	"golang.52tt.com/kaihei-pkg/blreport"
	"golang.52tt.com/kaihei-pkg/supervision"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/pkg/speedlimit"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	channel_go "golang.52tt.com/protocol/services/channel-go"
	channel_play_middle "golang.52tt.com/protocol/services/channel-play-middle"
	channelPlayTabPb "golang.52tt.com/protocol/services/channel-play-tab"
	gangup_channel_pb "golang.52tt.com/protocol/services/gangup-channel"
	music_topic_channel "golang.52tt.com/protocol/services/music-topic-channel"
	"golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/channel_list/data"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/channel_list/filter"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/channel_list/param"
	"golang.52tt.com/services/channel-play-logic/internal/utils"
	"google.golang.org/protobuf/runtime/protoimpl"
	"time"
)

type ChannelListMgr struct {
	TCChannelClient         tcChannel.IClient
	ChannelClient           channel_go.ChannelGoClient
	GenRecommendationClient recommendation_gen.IClient
	SupervisorInst          *supervision.Supervisory
	ChannelPlayTabClient    *channel_play_tab.Client
	GangupChannelClient     *gangup_channel.Client
	errOverInst             *speedlimit.ErrorOver
	channelPlayMiddleClient channel_play_middle.ChannelPlayMiddleClient
	musicTopicClient        music_topic_channel.MusicChannelClient

	channelListRcmdTimeOut int
}

type ChannelListMgrParam struct {
	TCChannelClient         tcChannel.IClient
	ChannelClient           channel_go.ChannelGoClient
	GenRecommendationClient recommendation_gen.IClient
	SupervisorInst          *supervision.Supervisory
	ChannelPlayTabClient    *channel_play_tab.Client
	GangupChannelClient     *gangup_channel.Client
	ErrOverInst             *speedlimit.ErrorOver
	ChannelPlayMiddleClient channel_play_middle.ChannelPlayMiddleClient
	MusicTopicClient        music_topic_channel.MusicChannelClient
	ChannelListRcmdTimeOut  int
}

type InsertItemType struct {
	item        *channel_play.TopicChannelItem
	insertIndex int
}

func NewChannelListMgr(mgrParams *ChannelListMgrParam) (listMgr *ChannelListMgr, err error) {
	listMgr = &ChannelListMgr{
		TCChannelClient:         mgrParams.TCChannelClient,
		ChannelClient:           mgrParams.ChannelClient,
		GenRecommendationClient: mgrParams.GenRecommendationClient,
		SupervisorInst:          mgrParams.SupervisorInst,
		ChannelPlayTabClient:    mgrParams.ChannelPlayTabClient,
		GangupChannelClient:     mgrParams.GangupChannelClient,
		errOverInst:             mgrParams.ErrOverInst,
		channelPlayMiddleClient: mgrParams.ChannelPlayMiddleClient,
		musicTopicClient:        mgrParams.MusicTopicClient,
		channelListRcmdTimeOut:  mgrParams.ChannelListRcmdTimeOut,
	}
	return listMgr, nil
}

func (m *ChannelListMgr) ListTopicChannel(ctx context.Context, param *param.ParamData) (channelData *data.RcmdDataResp, err error) {
	userId := param.ServiceInfo.UserID

	fallBackHandle := data.NewDefaultFallBack(m.TCChannelClient, param.In, param.Options, userId)
	sourceData := data.NewRcmdData(param, fallBackHandle, m.ChannelClient, m.GenRecommendationClient,
		m.TCChannelClient, m.errOverInst, m.channelListRcmdTimeOut)

	// 获取数据(包括兜底数据)
	channelData, err = sourceData.GetChannelData(ctx)
	//log.DebugWithCtx(ctx, "ListTopicChannel GetData, channelData:%+v", channelData)
	if err != nil {
		return channelData, err
	}

	// 上报筛选记录
	reportCtx := metadata.NewContext(ctx)
	if len(channelData.GenResp.GetLabels()) != 0 && param.IsRefresh {
		go m.HandleChannelListTagRecord(reportCtx, channelData.GenResp.GetLabels(), param)
	} else {
		if len(param.GenOption) != 0 && len(param.In.GetLabels()) != 0 {
			log.WarnWithCtx(ctx, "label err: nil, labels:%+v, param.IsRefresh:%t, req option:%+v, label:%v",
				channelData.GenResp.GetLabels(), param.IsRefresh, param.GenOption, param.In.GetLabels())
		}
	}

	if len(channelData.UgcChannelIds) == 0 && len(channelData.GetOtherChannelIds()) == 0 {
		log.InfoWithCtx(ctx, "ListTopicChannel GetData, no data param:%+v", param)
		//out.LoadFinish = true 里面判断了
		return channelData, nil
	}

	var ugcChannelItems map[uint32]*channel_play.TopicChannelItem
	var otherChannelItems map[uint32]*channel_play.TopicChannelItem

	fillChannelInfoTask := []utils.CoroutineTask{
		//房间
		func(ctx *context.Context, bCancelLog bool) (error, bool) {
			if len(channelData.UgcChannelIds) > 0 {
				// 组装ugc返回信息
				ugcChannelItems, err = m.fillUgcRspItemInfo(*ctx, param, channelData)
				if err != nil {
					log.ErrorWithCtx(*ctx, "FillUgcRspItemInfo fail, err:%v, param:%+v, channelData:%v", err, param, channelData)
					return nil, false
				}

			} else {
				log.WarnWithCtx(*ctx, "ListTopicChannel ugcChannelIds is empty, param:%+v, channelData:%+v", param, channelData)
			}

			return nil, false
		},
		//非ugc房间
		func(ctx *context.Context, bCancelLog bool) (error, bool) {
			// 组装其它房间类型信息, 失败了不影响ugc的处理
			if len(channelData.GetOtherChannelIds()) > 0 {

				rcmdCtx, cancel := grpc.NewContextWithInfoTimeout(*ctx, 500*time.Millisecond)
				defer cancel()

				otherChannelItems, err = m.fillOtherChannelItemInfo(rcmdCtx, channelData.GetOtherChannelIds())
				if err != nil {
					log.ErrorWithCtx(*ctx, "fillOtherChannelItemInfo fail, err:%v, param:%+v, otherChannelIds:%v", err, param, channelData.GetOtherChannelIds())
				}

				if len(otherChannelItems) != len(channelData.GetOtherChannelIds()) {
					log.InfoWithCtx(*ctx, "ListTopicChannel uid(%v), otherChannelIds:%v, otherChannelItems:%v", userId, channelData.GetOtherChannelIds(), otherChannelItems)

				} else {
					log.InfoWithCtx(*ctx, "ListTopicChannel uid(%v), otherChannelIds:%v", userId, channelData.GetOtherChannelIds())
				}

			}
			return nil, false
		},
	}
	err, bRet := utils.CoroutineDoWithSingleResult("", ctx, uint32(len(fillChannelInfoTask)), fillChannelInfoTask...)
	if err != nil {
		log.ErrorWithCtx(ctx, "CoroutineDoWithSingleResult len(fillChannelInfoTask):%d, err:%v", len(fillChannelInfoTask), err)
		if bRet {
			return channelData, err
		}
	}
	channelData.UgcChannelItems = ugcChannelItems
	channelData.OtherChannelItems = otherChannelItems

	// 源数据有，输出没有，加上告警日志
	outLen := len(channelData.UgcChannelItems) + len(channelData.OtherChannelItems)
	if len(channelData.GetOriginChannelIds()) > 0 && outLen == 0 {
		log.ErrorWithCtx(ctx, "UserLine Err: ListTopicChannel data response error, param:%+v, channelData:%+v", param, channelData)
	}

	log.InfoWithCtx(ctx, "ListTopicChannel out, reqtab:%s, uid(%d), ugcChannelIds(%v), otherChannelIds(%v), OriginChannelIdsLen(%d), outLen(%d)",
		param.PlayItems.GetReportDataAllStr(ctx), userId, channelData.UgcChannelIds, channelData.GetOtherChannelIds(), len(channelData.GetOriginChannelIds()), outLen)
	return channelData, nil
}

func (m *ChannelListMgr) fillUgcRspItemInfo(ctx context.Context, param *param.ParamData, dataInfo *data.RcmdDataResp) (ugcItemsMap map[uint32]*channel_play.TopicChannelItem, err error) {
	ugcItemsMap = make(map[uint32]*channel_play.TopicChannelItem, len(dataInfo.UgcChannelIds))

	viewReq := &channel_play_middle.BatGetGameChannelViewMapReq{
		ReqSource:  uint32(channel_play_middle.ReqSource_REQ_SOURCE_LIST),
		ChannelIds: dataInfo.UgcChannelIds,
		RecommendInfo: &channel_play_middle.RcmdInfo{
			SelfLocation:   dataInfo.RcmdInfo.RcmdLoc,
			ChannelInfoMap: dataInfo.RcmdInfo.RcmdChannelInfoMap,
		},
		ListParams: &channel_play_middle.SourceListParams{
			ChannelListEnterSource: uint32(param.In.GetChannelListEnterSource()),
			ListStyleType:          uint32(param.In.GetListStyleType()),
			ChannelPackageId:       param.In.GetChannelPackageId(),
			TabId:                  param.ReportData.TabId,
			TabIdsStr:              param.ReportData.TabIds,
			CategoryIdsStr:         param.ReportData.CategoryIds,
		},
	}
	genViewSource := make([]uint32, 0, len(param.In.GenViewSource))
	for _, v := range param.In.GenViewSource {
		genViewSource = append(genViewSource, uint32(v))
	}
	viewReq.ListParams.GenViewSource = genViewSource
	viewResp, err := m.channelPlayMiddleClient.BatGetGameChannelViewMap(ctx, viewReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatGetGameChannelViewMap in:%s, viewReq:%s, err:%+v", param.In.String(), viewReq.String(), err)
		return nil, err
	}
	channelFilter := filter.NewChannelFilter(param)
	for cid, anyInfo := range viewResp.GetChannelViewMap() {
		item := &channel_play.TopicChannelItem{}
		err = anyInfo.UnmarshalTo(protoimpl.X.ProtoMessageV2Of(item))
		if err != nil {
			log.ErrorWithCtx(ctx, "fillUgcRspItemInfo msg：%s umMarshal err:%+v", anyInfo.String(), err)
			continue
		}
		ret := channelFilter.DoFilter(ctx, item.GetTabId())
		if ret != 0 {
			log.ErrorWithCtx(ctx, "fillUgcRspItemInfo DoFilter ret:%d item:%s ", ret, item.String())
			param.ReportData.SetFilterlType(blreport.F_ID_ChannelList_Req_TabId_Not_Match)
			blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelListInfo(ctx, param.ReportData)
			continue
		}
		ugcItemsMap[cid] = item
	}
	return ugcItemsMap, nil
}

func (m *ChannelListMgr) fillOtherChannelItemInfo(ctx context.Context, otherChannelIds []uint32) (items map[uint32]*channel_play.TopicChannelItem, err error) {
	items = make(map[uint32]*channel_play.TopicChannelItem, 0)
	marshalResp, err := m.musicTopicClient.ListMusicChannelViewPbs(ctx, &music_topic_channel.ListMusicChannelViewPbsReq{
		ChannelIds: otherChannelIds,
	})
	//log.DebugWithCtx(ctx, "ListTopicChannel fillOtherChannelItemInfo, ListMusicChannelViewPbs, err:%v, otherChannelIds:%v, ChannelViews:%+v",
	//	otherChannelIds, marshalResp.GetChannelViews())

	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelFollowCommonHandle ListMusicChannelViewPbs, marshalCids:%v, err %v", otherChannelIds, err)
		return
	}
	for k, v := range marshalResp.GetChannelViews() {
		item := &channel_play.TopicChannelItem{
			ChannelId: k,
			Source:    channel_play.GenViewSource_FROM_MUSIC,
			TopicChannelView: &channel_play.TopicChannelView{
				ChannelView: &channel_play.TopicChannelView_ViewMarshal{
					ViewMarshal: &channel_play.ChannelViewMarshal{
						MarshalType: channel_play.MarshalType_PROTO_TYPE,
						Content:     v,
					},
				},
			},
		}
		items[k] = item
	}
	return
}

func (m *ChannelListMgr) reSortChannelList(cids []uint32, ugcChannelItems, otherChannelItems map[uint32]*channel_play.TopicChannelItem, revenueRoomItems map[uint32]*channel_play.TopicChannelItem) (rspItems []*channel_play.TopicChannelItem) {
	rspItems = make([]*channel_play.TopicChannelItem, 0, len(cids))
	for i, cid := range cids {
		if revenueRoomItems != nil {
			if revenueItem, ok := revenueRoomItems[uint32(i)]; ok {
				rspItems = append(rspItems, revenueItem)
			}
		}
		if v, ok := ugcChannelItems[cid]; ok {
			rspItems = append(rspItems, v)
			continue
		}
		if v, ok := otherChannelItems[cid]; ok {
			rspItems = append(rspItems, v)
		}
	}
	return
}

// GetNewQuickMatchConfig 获取新版本快速匹配入口配置
func (m *ChannelListMgr) GetNewQuickMatchConfig(ctx context.Context, tabId uint32) (*channel_play.NewQuickMatchConfig, error) {

	resp, err := m.ChannelPlayTabClient.GetNewQuickMatchConfig(ctx, &channelPlayTabPb.GetNewQuickMatchConfigReq{
		TabId: tabId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNewQuickMatchConfig ChannelPlayTabClient.GetNewQuickMatchConfig tabId:%d err: %v", tabId, err)
		return nil, err
	}
	if resp.GetConfig() == nil {
		return nil, nil
	}
	return &channel_play.NewQuickMatchConfig{
		TabId:      resp.GetConfig().GetTabId(),
		TabName:    resp.GetConfig().GetTabName(),
		Title:      resp.GetConfig().GetTitle(),
		ButtonText: resp.GetConfig().GetButtonText(),
		Position:   resp.GetConfig().GetPosition(),
	}, nil
}

func (m *ChannelListMgr) HandleChannelListTagRecord(ctx context.Context, gameLabels []*rcmd_channel_label.GameLabel, paramInst *param.ParamData) {
	//log.DebugWithCtx(ctx, "HandleChannelListTagRecord labels:%+v paramInst:%+v", gameLabels, paramInst)
	ctx, cancel := context.WithTimeout(ctx, 2*time.Second)
	defer cancel()
	defer func() {
		err := recover()
		if err != nil {
			log.ErrorWithCtx(ctx, "handleChannelListTagRecord panic, err:%v ", err)
		}
	}()
	in := paramInst.In
	tabInfo := cache.GetTabInfoCache().GetTabInfoCacheById(in.GetTabId())
	if topic_channel.CategoryType(tabInfo.GetCategoryMapping()) != topic_channel.CategoryType_Gangup_type ||
		(len(in.GetLabels()) == 0 && len(paramInst.GenOption) == 0) {
		log.InfoWithCtx(ctx, "HandleChannelListTagRecord tabId:%d, in:%+v", in.GetTabId(), in)
		return
	}

	records := make([]*gangup_channel_pb.UpdateChannelListFilterRecordReq_LabelRecord, 0, len(gameLabels))
	for _, label := range gameLabels {
		record := &gangup_channel_pb.UpdateChannelListFilterRecordReq_LabelRecord{
			DisplayTitle:    label.GetDisplayName(),
			RelatedLabelVal: label.GetVal(),
		}
		records = append(records, record)
		//log.InfoWithCtx(ctx, "HandleChannelListTagRecord record:%+v paramInst:%+v", record, paramInst)
	}
	if len(records) == 0 {
		return
	}

	recordReq := &gangup_channel_pb.UpdateChannelListFilterRecordReq{
		Uid:         paramInst.ServiceInfo.UserID,
		TabId:       in.GetTabId(),
		LabelRecord: records,
	}
	_, err := m.GangupChannelClient.UpdateChannelListFilterRecord(ctx, recordReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateChannelListFilterRecord in:%s err:%v", in.String(), err)
	}
	log.InfoWithCtx(ctx, "HandleChannelListTagRecord recordReq:%+v", recordReq)
}
