package data

import (
	"context"
	tcChannel "golang.52tt.com/clients/topic-channel/channel"
	"golang.52tt.com/pkg/log"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	channelPB "golang.52tt.com/protocol/services/topic_channel/channel"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/channel_list/param"
)

type FallBack interface {
	FallbackListTopicChannel(ctx context.Context) (channelList []uint32, loadFinish bool, err error)
}

type DefaultFallBack struct {
	TCChannelClient     tcChannel.IClient
	ListTopicChannelReq *channel_play.ListTopicChannelReq
	Options             []*channelPB.BlockOption
	UserId              uint32
}

func NewDefaultFallBack(tCChannelClient tcChannel.IClient, listTopicChannelReq *channel_play.ListTopicChannelReq, options []*channelPB.BlockOption, userId uint32) *DefaultFallBack {
	return &DefaultFallBack{
		TCChannelClient:     tCChannelClient,
		ListTopicChannelReq: listTopicChannelReq,
		Options:             options,
		UserId:              userId,
	}
}

// FallbackListTopicChannel 开黑列表兜底策略
func (m *DefaultFallBack) FallbackListTopicChannel(ctx context.Context) (
	channelList []uint32, loadFinish bool, err error) {

	//topicChannelInfo = make(map[uint32]*channelPB.ChannelInfo)
	if m.ListTopicChannelReq.TabId > 0 && m.ListTopicChannelReq.TabId != param.InfElemID {
		resp, err := m.TCChannelClient.GetRecommendChannelListByTab(ctx, &channelPB.GetRecommendChannelListByTabReq{
			Uid: m.UserId, Limit: m.ListTopicChannelReq.GetCount(), TabId: m.ListTopicChannelReq.GetTabId(), BlockOptions: m.Options,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "ListTopicChannel fallbackListTopicChannel GetRecommendChannelListByTab userId(%v) err(%v)", m.UserId, err)
			return channelList, loadFinish, err
		}
		if len(resp.GetChannelList()) < int(m.ListTopicChannelReq.GetCount()) {
			loadFinish = true
		}
		channelList = make([]uint32, 0, len(resp.GetChannelList()))
		log.DebugWithCtx(ctx, "ListTopicChannel fallbackListTopicChannel GetRecommendChannelListByTab userId(%v) list by tab(%v) resp(%v)",
			m.UserId, m.ListTopicChannelReq.GetTabId(), resp)
		for _, info := range resp.GetChannelList() {
			if isDisplayChannel(info.GetDisplayType()) {
				//仅展示发布的
				//topicChannelInfo[info.GetId()] = info
				channelList = append(channelList, info.GetId())
			}
		}
	} else {
		resp, err := m.TCChannelClient.GetRecommendChannelList(ctx, &channelPB.GetRecommendChannelListReq{
			Uid: m.UserId, Limit: m.ListTopicChannelReq.GetCount(), TabIdList: m.ListTopicChannelReq.GetTabIds(), //ReturnPgc: 1,
		})
		log.DebugWithCtx(ctx, "ListTopicChannel fallbackListTopicChannel TCGetRecommendChannelList userId(%v) list by tab(%v) resp(%v)",
			m.UserId, m.ListTopicChannelReq.GetTabIds(), resp)
		if err != nil {
			log.ErrorWithCtx(ctx, "ListTopicChannel fallbackListTopicChannel TCGetRecommendChannelList userId(%v) err(%v)", m.UserId, err)
			return channelList, loadFinish, err
		}
		if len(resp.GetChannelList()) < int(m.ListTopicChannelReq.Count) {
			loadFinish = true
		}
		for _, info := range resp.GetChannelList() {
			if isDisplayChannel(info.GetDisplayType()) {
				//仅展示发布的
				//listInfo.channelIds = append(listInfo.channelIds, info.Id)
				//topicChannelInfo[info.GetId()] = info
				channelList = append(channelList, info.GetId())
			}
		}
		//loadMore = convertListLoadMore(resp.LoadMore, 0, nil)
	}
	log.InfoWithCtx(ctx, "FallbackListTopicChannel in:%v, channelList:%v", m.ListTopicChannelReq.String(), channelList)
	return
}

func isDisplayChannel(displayType []channelPB.ChannelDisplayType) bool {
	for _, v := range displayType {
		if v == channelPB.ChannelDisplayType_DISPLAY_AT_MAIN_PAGE {
			//仅展示发布的
			return true
		}
	}
	return false
}
