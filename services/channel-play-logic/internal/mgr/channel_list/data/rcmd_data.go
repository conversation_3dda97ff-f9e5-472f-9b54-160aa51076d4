package data

import (
	"context"
	"encoding/base64"
	"fmt"
	tcChannel "golang.52tt.com/clients/topic-channel/channel"
	recommendation_gen "golang.52tt.com/clients/topic-channel/recommendation-gen"
	"golang.52tt.com/kaihei-pkg/blreport"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/monkey-send-chat/monkey_sender"
	"golang.52tt.com/pkg/speedlimit"
	gaChannelPB "golang.52tt.com/protocol/app/channel"
	simpleChannelPB "golang.52tt.com/protocol/app/channel"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	channel_go "golang.52tt.com/protocol/services/channel-go"
	pb "golang.52tt.com/protocol/services/channelsvr"
	configserverPB "golang.52tt.com/protocol/services/configserver"
	rcmdCommon "golang.52tt.com/protocol/services/rcmd/common"
	channelPB "golang.52tt.com/protocol/services/topic_channel/channel"
	"golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
	genPB "golang.52tt.com/protocol/services/topic_channel/recommendation_gen"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/channel_list/convert"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/channel_list/param"
	"golang.52tt.com/services/channel-play-logic/internal/utils"
	"strconv"
	"strings"
	"time"
)

type SourceData interface {
	GenParam(ctx context.Context) (getRecommendationListReq *genPB.GetRecommendationListReq, err error)
	GetData(ctx context.Context, getRecommendationListReq *genPB.GetRecommendationListReq) (rsp *genPB.GetRecommendationListResp, err error)
	FallBack
}

const (
	//房间模式blockId，写死
	MysteryBussRoomModeBlock = 1
	//对局状态blockId, 写死
	MysteryBussBGameConditionBlock = 2
	// 半屏也对局状态blockId，写死
	MysteryBussBGameScreenConditionBlock = 3

	//房间模式Business Elem
	MysteryBussRoomModeNoLimit = 1
	MysteryBussRoomModeSingle  = 2
	MysteryBussRoomModeDouble  = 3

	//对局状态Business Elem
	MysteryBussGameConditionNoLimit = 4
	MysteryBussGameConditionWaiting = 5

	// 半屏页对战状态Business Elem
	MysteryBussGameScreenConditionWaiting = 6
	MysteryBussGameScreenConditionStarted = 7
)

var rcmdBussBlockMap = map[uint32]genPB.BusinessBlockEnum{
	MysteryBussRoomModeBlock:             genPB.BusinessBlockEnum_RoomMode,
	MysteryBussBGameConditionBlock:       genPB.BusinessBlockEnum_GameCondition,
	MysteryBussBGameScreenConditionBlock: genPB.BusinessBlockEnum_GameCondition,
}

var rcmdBussElemMap = map[uint32]genPB.BusinessElemEnum{
	MysteryBussRoomModeNoLimit:            genPB.BusinessElemEnum_NoLimit,
	MysteryBussRoomModeSingle:             genPB.BusinessElemEnum_Single,
	MysteryBussRoomModeDouble:             genPB.BusinessElemEnum_Double,
	MysteryBussGameConditionNoLimit:       genPB.BusinessElemEnum_NoLimit,
	MysteryBussGameConditionWaiting:       genPB.BusinessElemEnum_Waiting,
	MysteryBussGameScreenConditionWaiting: genPB.BusinessElemEnum_Waiting,
	MysteryBussGameScreenConditionStarted: genPB.BusinessElemEnum_Started,
}

type RcmdData struct {
	GenRecommendationClient recommendation_gen.IClient
	FallBack                FallBack
	param                   *param.ParamData
	ChannelClient           channel_go.ChannelGoClient
	TCChannelClient         tcChannel.IClient
	errOverInst             *speedlimit.ErrorOver
	rcmdTimeOut             int
}

type RcmdDataResp struct {
	//TcInfoMap               map[uint32]*channelPB.ChannelInfo
	originChannelIds        []uint32 // 源数据，第一次获取后不可再修改
	UgcChannelIds           []uint32
	otherChannelIds         []uint32 // 非ugc房间的源数据，第一次获取后不可再修改
	Appointment             bool
	IsUgcChannelCntLessFive bool
	GenResp                 *genPB.GetRecommendationListResp
	RcmdInfo                *RcmdInfo
	LoadFinish              bool
	ReportData              *channel_play.ListTopicChannelResp_DataReport
	IsRefresh               bool
	IsCallFallBack          bool
	IsRecommendDoudi        bool

	UgcChannelItems   map[uint32]*channel_play.TopicChannelItem
	OtherChannelItems map[uint32]*channel_play.TopicChannelItem
}

func (p *RcmdDataResp) GetOriginChannelIds() []uint32 {
	return p.originChannelIds
}

func (p *RcmdDataResp) SetOriginChannelIds(cids []uint32) {
	p.originChannelIds = cids
}

func (p *RcmdDataResp) GetOtherChannelIds() []uint32 {
	return p.otherChannelIds
}

func (p *RcmdDataResp) SetOtherChannelIds(otherCids []uint32) {
	p.otherChannelIds = otherCids
}

func NewRcmdData(param *param.ParamData, fallBack FallBack, channelClient channel_go.ChannelGoClient,
	genRecommendationClient recommendation_gen.IClient, tCChannelClient tcChannel.IClient,
	errOverInst *speedlimit.ErrorOver, rcmdTimeOut int) *RcmdData {
	return &RcmdData{
		FallBack:                fallBack,
		param:                   param,
		ChannelClient:           channelClient,
		TCChannelClient:         tCChannelClient,
		GenRecommendationClient: genRecommendationClient,
		errOverInst:             errOverInst,
		rcmdTimeOut:             rcmdTimeOut,
	}
}

func (m *RcmdData) GenParam(ctx context.Context) (getRecommendationListReq *genPB.GetRecommendationListReq, err error) {
	var getMode = m.param.In.GetGetMode()
	if getMode == 0 {
		//默认刷新
		getMode = 2
	}
	//tabId, tabIds, categoryId := handleRcmdTabByEnterSource(ctx, in.GetTabId(), in.GetTabIds(), in.GetCategoryIds(), in.GetChannelListEnterSource())
	getRecommendationListReq = &genPB.GetRecommendationListReq{
		Uid: m.param.ServiceInfo.UserID, Limit: m.param.In.GetCount(),
		TabId: m.param.PlayItems.TabId,
		//BlockOptions: genOption,
		GetMode: genPB.GetRecommendationListReq_GetListMode(getMode),
		//ChannelEnterSource: uint32(gaChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_FINDPLAY_TAB),
		ClientType: uint32(m.param.ServiceInfo.ClientType), ClientVersion: m.param.ServiceInfo.ClientVersion, MarketId: m.param.ServiceInfo.MarketID,
		Env:              genPB.GetRecommendationListReq_production,
		ChannelPackageId: m.param.In.GetChannelPackageId(),
		Sex:              convert.ConvertSexToGenSex(m.param.In.GetSex()),
		BrowseList: &rcmdCommon.RcmdBrowseInfo{
			NoBrowseList: m.param.In.GetNoBrowseList(),
		},
		RegulatoryLevel: genPB.REGULATORY_LEVEL_FREE,
		TabIds:          m.param.PlayItems.TabIds,
		CategoryIds:     m.param.PlayItems.CategoryIds,
		PrefGames:       prefGamesPb2Recommend(ctx, m.param.In.GetPrefGames()),
		//Labels:          convert2RcmdGameLabel(in.GetLabels()),
		InterestLabels: m.param.In.GetInterestLabels(),
		//IsEnableGameLabel: m.param.In.GetEnableGameLabel(),
		ClassifyLabels:    utils.ConvertClassifyLabelsToRcmdPb(m.param.In.GetClassifyLabels()),
		ShieldFilterWords: m.param.In.GetShieldFilterWords(),
	}

	if m.param.In.GetChannelListEnterSource() == channel_play.ChannelListEnterSource_MysteryHomeCarousel {
		m.param.BusinessOption[channel_play.FilterType_Mystery_Game_Condition_Filter] = []*channel_play.FilterBlockOption{
			{
				BlockId:    MysteryBussBGameConditionBlock,
				ElemId:     MysteryBussGameConditionWaiting,
				FilterType: channel_play.FilterType_Mystery_Game_Condition_Filter,
			},
		}
	}

	getRecommendationListReq.ChannelEnterSource = uint32(convert.ConvertChannelEnterSource(m.param.In.GetChannelListEnterSource()))
	getRecommendationListReq.BlockOptions, getRecommendationListReq.Labels, err = m.genBlockOptionAndLabels()
	if err != nil {
		utils.ErrCntLog(ctx, "ListTopicChannel makeRecommendationListReq in(%s) err(%v)", m.param.In.String(), err)
	}
	log.DebugWithCtx(ctx, "GenParam, options:%+v, labels:%+v, IsEnableGameLabel:%t", getRecommendationListReq.BlockOptions, getRecommendationListReq.Labels, m.param.In.GetEnableGameLabel())
	//处理business block
	mysteryBuss, ok := m.param.BusinessOption[channel_play.FilterType_Mystery_Game_Condition_Filter]
	if !ok {
		//没有business block
		return
	}
	getRecommendationListReq.BusinessFilter = genMysteryBusinessFilter(mysteryBuss)
	return
}

// GetData 获取推荐数据
func (m *RcmdData) GetData(ctx context.Context, getRecommendationListReq *genPB.GetRecommendationListReq) (genResp *genPB.GetRecommendationListResp, err error) {
	timeOutUidList := conf.PublicSwitchConfig.GetTimeoutUidList()
	if m.param.Env != "production" && len(timeOutUidList) > 0 {
		//log.DebugWithCtx(ctx,"ListTopicChannelV3 TimeoutUidList (%v)", s.serviceConfigT.GetOption().TimeoutUidList)
		shouldTimeout := false
		for _, v := range timeOutUidList {
			if v == getRecommendationListReq.Uid {
				shouldTimeout = true
				break
			}
		}
		if shouldTimeout {
			log.DebugWithCtx(ctx, "ListTopicPlayChannel Manual Timeout userId %d", getRecommendationListReq.Uid)
			time.Sleep(1 * time.Second)
		}
	}
	warnLogUtil := utils.NewWarnLog()

	genResp, err = m.GenRecommendationClient.GetRecommendationList(ctx, getRecommendationListReq)
	var (
		rest = 99999 * time.Millisecond
	)
	if dl, ok := ctx.Deadline(); ok {
		rest = dl.Sub(time.Now())
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "ListTopicChannel gen GetRecommendationList startime:%d, endtime:%d, rest:%v"+
			" getRecommendationListReq(%v) err(%v)", warnLogUtil.StartTime.UnixNano()/1e6, time.Now().UnixNano()/1e6, rest,
			getRecommendationListReq, err)
		return
	}
	warnLogUtil.WarnLog(fmt.Sprintf("startTime:%d endtime:%d rest:%v GenRecommendationClient.GetRecommendationList %v",
		warnLogUtil.StartTime.UnixNano()/1e6, time.Now().UnixNano()/1e6, rest, getRecommendationListReq))

	recommendNilUidList := conf.PublicSwitchConfig.GetRecommendNilUidList()
	if m.param.Env != "production" && len(recommendNilUidList) > 0 {
		//log.DebugWithCtx(ctx,"ListTopicChannelV3 TimeoutUidList (%v)", s.serviceConfigT.GetOption().TimeoutUidList)
		shouldNil := false
		for _, v := range recommendNilUidList {
			if v == getRecommendationListReq.Uid {
				shouldNil = true
				break
			}
		}
		if shouldNil {
			log.DebugWithCtx(ctx, "ListTopicPlayChannel Manual Uids Nil userId %d", getRecommendationListReq.Uid)
			genResp.ChannelId = []uint32{}
			genResp.BottomReached = false
		}
	}
	// 谜境主页轮播房间列表，若等待中列表没有房间，则取进行中
	if len(genResp.GetChannelId()) == 0 &&
		getRecommendationListReq.GetChannelEnterSource() == uint32(gaChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_MYSTERY_HOME_CAROUSEL) {
		getRecommendationListReq.BusinessFilter = []*genPB.GetRecommendationListReq_BusinessFilter{
			{
				BlockType: genPB.BusinessBlockEnum_GameCondition,
				ElemType:  []genPB.BusinessElemEnum{genPB.BusinessElemEnum_Started},
			},
		}
		genResp, err = m.GenRecommendationClient.GetRecommendationList(ctx, getRecommendationListReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "ListTopicChannel gen GetRecommendationList "+
				"getRecommendationListReq(%v) err(%v)", getRecommendationListReq, err)
			return
		}
	}
	//me.End() //关闭指标监控
	log.InfoWithCtx(ctx, "ListTopicChannel GetRcmdChannelList startTime:%d, endtime:%d, rest:%v genReq: %v, genResp: %v",
		warnLogUtil.StartTime.UnixNano()/1e6, time.Now().UnixNano()/1e6, rest, getRecommendationListReq, genResp)
	return
}

func prefGamesPb2Recommend(ctx context.Context, prefGames []*topic_channel.PrefGame) []*genPB.PrefGame {
	list := make([]*genPB.PrefGame, 0)
	for _, prefGame := range prefGames {
		pg := &genPB.PrefGame{TabId: prefGame.GetTabId()}
		for _, label := range prefGame.GetLabels() {
			l := &genPB.PrefGameLabel{Id: label.GetId(), Val: label.GetValValue()}
			if label.GetType() == topic_channel.PrefGame_Label_TypeSystem {
				l.Type = genPB.PrefGameLabelType_SYSTEM
			} else if label.GetType() == topic_channel.PrefGame_Label_TypeCustom {
				l.Type = genPB.PrefGameLabelType_CUSTOM
			} else {
				log.ErrorWithCtx(ctx, "prefGamesPb2Recommend invalid label type")
				continue
			}

			pg.Labels = append(pg.Labels, l)
		}

		list = append(list, pg)
	}

	log.DebugWithCtx(ctx, "prefGamesPb2Recommend list: %v", list)
	return list
}

// 根据listStyle样式以及是否开启玩法，生成blockOption labels
func (m *RcmdData) genBlockOptionAndLabels() (blockOption []*genPB.BlockOption, gameLabels []*rcmd_channel_label.GameLabel, err error) {
	blockOption = m.param.GenOption

	if len(m.param.In.GetLabels()) > 0 {
		gameLabels = convert.Convert2RcmdGameLabel(m.param.In.GetLabels())
	}
	return
}

// 映射客户端业务block与推荐业务筛选项
func genMysteryBusinessFilter(businessOption []*channel_play.FilterBlockOption) (rcmdBussFilter []*genPB.GetRecommendationListReq_BusinessFilter) {
	rcmdBussFilter = make([]*genPB.GetRecommendationListReq_BusinessFilter, 0)
	blockElemMap := make(map[genPB.BusinessBlockEnum][]genPB.BusinessElemEnum)
	for _, op := range businessOption {
		k, ok := rcmdBussBlockMap[op.BlockId]
		if !ok {
			continue
		}
		e, ok := rcmdBussElemMap[op.ElemId]
		if !ok {
			continue
		}
		elem, ok := blockElemMap[k]
		if ok {
			elem = append(elem, e)
			blockElemMap[k] = elem
		} else {
			blockElemMap[k] = make([]genPB.BusinessElemEnum, 0)
			blockElemMap[k] = append(blockElemMap[k], e)
		}
	}
	for k, v := range blockElemMap {
		filter := &genPB.GetRecommendationListReq_BusinessFilter{
			BlockType: k,
			ElemType:  v,
		}
		rcmdBussFilter = append(rcmdBussFilter, filter)
	}
	return rcmdBussFilter
}

func (m *RcmdData) FallBackHandle(ctx context.Context, rsp *RcmdDataResp) (err error) {
	//兜底开关开启
	var originChannelIds, otherChannelIds []uint32
	originChannelIds, rsp.LoadFinish, err = m.FallBack.FallbackListTopicChannel(ctx)
	if err != nil {
		return
	}
	rsp.SetOriginChannelIds(originChannelIds)
	if len(rsp.GetOriginChannelIds()) == 0 {
		log.WarnWithCtx(ctx, "UserLine Err: ListTopicChannel FallBackHandle res data is empty, param:%+v", m.param)
	}

	m.param.CostReporter.Tick("fall_back_cost_time")
	//兜底 footprint  base64 encode 的 "tt_doudi"
	rsp.ReportData = &channel_play.ListTopicChannelResp_DataReport{
		Footprint: base64.StdEncoding.EncodeToString([]byte("tt_doudi")),
	}
	rsp.UgcChannelIds, otherChannelIds, err = m.getCidToUserMap(ctx, m.param.ServiceInfo.UserID, rsp.GetOriginChannelIds())
	if err != nil {
		return
	}
	rsp.SetOtherChannelIds(otherChannelIds)
	rsp.IsCallFallBack = true

	return
}

func (m *RcmdData) getCidToUserMap(ctx context.Context, uid uint32,
	channelIds []uint32) (ugcChannelIds, otherChannelIds []uint32, err error) {

	resp, err := m.ChannelClient.BatchGetChannelSimpleInfo(ctx, &channel_go.BatchGetChannelSimpleInfoReq{
		OpUid:         uid,
		ChannelIdList: channelIds,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelFollowCommonHandle ChannelClient.BatchGetChannelSimpleInfo err:%v, channelIds:%v", err, channelIds)
		return
	}
	channelInfoMap := resp.GetChannelSimpleList()
	ugcChannelIds = make([]uint32, 0, len(channelInfoMap))
	otherChannelIds = make([]uint32, 0, len(channelInfoMap))

	for _, v := range channelInfoMap {
		if v.GetChannelType() == uint32(simpleChannelPB.ChannelType_USER_CHANNEL_TYPE) {
			ugcChannelIds = append(ugcChannelIds, v.GetChannelId())
		} else {
			otherChannelIds = append(otherChannelIds, v.GetChannelId())
		}
	}
	return
}

func (m *RcmdData) DoudiAndFallBack(ctx context.Context, rsp *RcmdDataResp) (err error) {
	isDoudi, isRecommendDoudi := m.param.IsDoudi(ctx)
	if isDoudi {
		err = m.FallBackHandle(ctx, rsp)
		if err != nil {
			return
		}
	} else {
		//不走兜底，以推荐的loadfinish为准
		rsp.LoadFinish = rsp.GenResp.GetBottomReached()
	}
	if len(rsp.GetOriginChannelIds()) != 0 { //兜底也不为空时才展示
		rsp.IsRecommendDoudi = isRecommendDoudi
	}
	return err
}

// GetChannelData 获取房间列表数据（整合后）
func (m *RcmdData) GetChannelData(ctx context.Context) (rsp *RcmdDataResp, err error) {
	rsp = &RcmdDataResp{
		RcmdInfo: &RcmdInfo{},
	}
	userId := m.param.ServiceInfo.UserID
	if cache.GetSwitchStatusByType(configserverPB.SwitchBusinessType_GAME_MIX_CHANNEL_LIST) {
		err = m.FallBackHandle(ctx, rsp)
		if err != nil {
			return
		}

	} else {
		genReq, _ := m.GenParam(ctx)
		m.param.CostReporter.Tick("gen_req_by_filter")
		m.errOverInst.AddTotalCnt(1)
		timeOutTime := m.rcmdTimeOut
		rcmdCtx, cancel := context.WithTimeout(ctx, time.Duration(timeOutTime)*time.Millisecond)
		defer cancel()
		rsp.GenResp, err = m.GetData(rcmdCtx, genReq)
		m.param.CostReporter.Tick("rcmd_cost_time")

		if err != nil || rsp.GenResp == nil { //推荐失败走兜底
			m.param.ReportData.SetFilterlType(blreport.F_ID_ChannelList_Rcmd_Err)
			blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelListInfo(ctx, m.param.ReportData)
			if err != nil && strings.Contains(err.Error(), "context deadline exceeded") && conf.GetEnv() == conf.Production {
				sendErr := monkey_sender.GetNumMsgSenderByChatId(conf.PublicSwitchConfig.GetFallBackChatId(), conf.PublicSwitchConfig.GetFallBackWarnDuration()).
					SendMsg("开黑房间列表", fmt.Sprintf("推荐耗时超过%dms，uid:%d, rcmdReq:%s", timeOutTime, userId, genReq.String()))
				if sendErr != nil {
					log.ErrorWithCtx(ctx, "ListTopicChannel monkey_sender fallback SendMsg err:%v", sendErr)
				}
			}
			err = m.FallBackHandle(ctx, rsp)
			if err != nil {
				return
			}

		} else {
			rsp.SetOriginChannelIds(rsp.GenResp.GetChannelId())
			if len(rsp.GetOriginChannelIds()) == 0 {
				rsp.LoadFinish = rsp.GenResp.GetBottomReached()
				m.errOverInst.AddErrorCnt(1)
				m.param.ReportData.SetFilterlType(blreport.F_ID_ChannelList_Rcmd_Nil)
				blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelListInfo(ctx, m.param.ReportData)
				log.WarnWithCtx(ctx, "UserLine Err: ListTopicChannel rcmd genResp get no data in(%v) genReq(%v) genResp(%v)", m.param.In, genReq, rsp.GenResp)
				rsp.LoadFinish = true
				if m.errOverInst.IsOverByRatio(ctx, conf.PublicSwitchConfig.GetDoudiNilRatio()) {
					err = m.DoudiAndFallBack(ctx, rsp)
					if err != nil {
						return
					}
				} else {
					if len(conf.PublicSwitchConfig.GetDoudiTabMap()) != 0 {
						if _, ok := conf.PublicSwitchConfig.GetDoudiTabMap()[m.param.PlayItems.TabId]; ok {
							err = m.DoudiAndFallBack(ctx, rsp)
							if err != nil {
								return
							}
							log.DebugWithCtx(ctx, "ListTopicChannel DoudiTabMap fall back, tabId:%d, rsp:%+v", m.param.PlayItems.TabId, rsp)
						}
					}
				}
			} else {
				var otherChannelIds []uint32
				rsp.UgcChannelIds, otherChannelIds, err = m.getCidToUserMap(ctx, userId, rsp.GetOriginChannelIds())
				if err != nil {
					return
				}
				rsp.SetOtherChannelIds(otherChannelIds)

				//拼接客户端数据上报字段
				rsp.ReportData = &channel_play.ListTopicChannelResp_DataReport{
					Footprint: rsp.GenResp.GetFootprint(),
				}
				rsp.Appointment, rsp.IsRefresh = getGenRespNotify(rsp.GenResp.GetNotifyList())
				rsp.LoadFinish = rsp.GenResp.GetBottomReached()
				rsp.RcmdInfo.RcmdChannelInfoMap = rsp.GenResp.GetCommonChannelInfoMap()
				rsp.RcmdInfo.RcmdLoc = rsp.GenResp.GetSelfLoc()
				rsp.RcmdInfo.Labels = rsp.GenResp.GetLabels()
			}
		}
	}
	return
}

func (m *RcmdData) GetPulishingChannelIds(ctx context.Context, reqChannelInfos []*channelPB.ChannelInfo) (publishingChannelInfos []*channelPB.ChannelInfo, oriChannelInfoMap map[uint32]*channelPB.ChannelInfo) {
	oriChannelInfoMap = make(map[uint32]*channelPB.ChannelInfo)
	for _, cinfo := range reqChannelInfos {
		bIsPulbish := false
		bIsDismiss := false
		for _, displayType := range cinfo.GetDisplayType() {
			if displayType == channelPB.ChannelDisplayType_DISPLAY_AT_MAIN_PAGE {
				bIsPulbish = true
				//break
			} else if displayType == channelPB.ChannelDisplayType_TEMPORARY {
				log.ErrorWithCtx(ctx, "GetPulishingChannelIds temporary channel:%+v", cinfo)
			} else if displayType == channelPB.ChannelDisplayType_DISMISSED {
				bIsDismiss = true
			}
		}
		if bIsPulbish && bIsDismiss {
			log.InfoWithCtx(ctx, "GetPulishingChannelIds isPublish and IsDismiss:%+v", cinfo)
		}
		if conf.ChannelPlayLogicConfig.GetUseDismiss() {
			if !bIsDismiss {
				publishingChannelInfos = append(publishingChannelInfos, cinfo)
				log.DebugWithCtx(ctx, "publishingChannelInfos isDismiss:%+v", cinfo)
			}
		} else {
			if bIsPulbish {
				publishingChannelInfos = append(publishingChannelInfos, cinfo)
				log.DebugWithCtx(ctx, "publishingChannelInfos isPublish:%+v", cinfo)
			}
		}

		oriChannelInfoMap[cinfo.GetId()] = cinfo
	}
	return
}

func (m *RcmdData) ReportPublishingFilter(ctx context.Context, channelPushStatHandle *blreport.ChannelPushStat,
	publishingChannelInfos []*channelPB.ChannelInfo, oriChannelInfos map[uint32]*channelPB.ChannelInfo, oriUgcChannelIds []uint32, channel2Info map[uint32]*pb.ChannelSimpleInfo) {

	var expireChidsLogText, pulishingLogText string
	for _, chid := range oriUgcChannelIds {
		if info, ok := oriChannelInfos[chid]; ok {
			bIsPulbish := false
			for _, displayType := range info.GetDisplayType() {
				if displayType == channelPB.ChannelDisplayType_DISPLAY_AT_MAIN_PAGE {
					bIsPulbish = true
					break
				}
			}
			if !bIsPulbish {
				reasonDesc := blreport.F_ID_ChannelPush_Publishing_Filter.GetChannelPushDesc()
				channelPushStatHandle.AddItemByFilter(info.GetId(), info.GetCreateTime(), info.GetTabId(), channel2Info, reasonDesc, m.param.PlayItems.GetReportDataAllStr(ctx), info.GetTerminalType(), info.GetMarketId(), uint32(blreport.F_ID_ChannelPush_Publishing_Filter))
				expireChidsLogText += fmt.Sprintf("%d-%d-%d-%d,", info.GetId(), info.GetTabId(), info.GetCreateTime(), info.GetLastDismissTime())
			} else {
				pulishingLogText += fmt.Sprintf("%d-%d-%d-%d,", info.GetId(), info.GetTabId(), info.GetCreateTime(), info.GetLastDismissTime())
			}
		} else {
			expireChidsLogText += strconv.Itoa(int(chid))
			expireChidsLogText += ","
		}
	}

	if expireChidsLogText != "" {
		m.param.ReportData.SetFilterlType(blreport.F_ID_ChannelList_Publishing_Filter)
		m.param.ReportData.SetChannelFilterInfo(expireChidsLogText)
		blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelListInfo(ctx, m.param.ReportData)
	}

	rspChannelListLen := len(publishingChannelInfos)
	reqChannelListLen := len(oriUgcChannelIds)
	if diffLen := reqChannelListLen - rspChannelListLen; diffLen > 2 {
		log.InfoWithCtx(ctx, "ListTopicChannel ReportPublishingFilter uid:%d reqChannelIds:%v publishingLogText:%s, expireLogText:%s",
			m.param.ServiceInfo.UserID, oriUgcChannelIds, pulishingLogText, expireChidsLogText)
	} else {
		log.DebugWithCtx(ctx, "ListTopicChannel ReportPublishingFilter uid:%d reqChannelIds:%v publishingLogText:%s, expireLogText:%s",
			m.param.ServiceInfo.UserID, oriUgcChannelIds, pulishingLogText, expireChidsLogText)
	}
}

func getGenRespNotify(notifyTypes []genPB.GetRecommendationListResp_NotifyType) (appointment, refresh bool) {
	for _, notifyType := range notifyTypes {
		if notifyType == genPB.GetRecommendationListResp_APPOINTMENT {
			appointment = true
		}
		if notifyType == genPB.GetRecommendationListResp_RefreshSucc {
			refresh = true
		}
	}
	return
}

type RcmdInfo struct {
	RcmdChannelInfoMap map[uint32]*rcmdCommon.ChannelInfo
	RcmdLoc            *rcmdCommon.LocationInfo
	Labels             []*rcmd_channel_label.GameLabel
}
