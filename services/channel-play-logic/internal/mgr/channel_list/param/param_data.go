package param

import (
	"context"
	"fmt"
	"golang.52tt.com/kaihei-pkg/blreport"
	"golang.52tt.com/pkg/cost_time_reporter"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol/grpc"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	channelPB "golang.52tt.com/protocol/services/topic_channel/channel"
	genPB "golang.52tt.com/protocol/services/topic_channel/recommendation_gen"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/channel_list/convert"
	"math"
	"strconv"
	"strings"
)

const (
	costReportNameSpace        = "userline_channel_play_logic"
	InfElemID           uint32 = math.MaxUint32

	maxShieldFilterWordsLen = 50
)

type ParamData struct {
	ServiceInfo    *grpc.ServiceInfo
	TabFilter      map[uint32]bool
	CategoryFilter map[uint32]bool
	PlayItems      *PlayItems
	ReportData     *blreport.ChannelList
	CostReporter   *cost_time_reporter.TimeCostCounter

	Options        []*channelPB.BlockOption
	GenOption      []*genPB.BlockOption
	BusinessOption map[channel_play.FilterType][]*channel_play.FilterBlockOption

	In        *channel_play.ListTopicChannelReq
	Env       string
	IsRefresh bool //是否刷新还是下一页
	IsSelect  bool //是否进行了筛选
}

func NewParamData(ctx context.Context, serviceInfo *grpc.ServiceInfo, in *channel_play.ListTopicChannelReq, tabFilter map[uint32]bool, categoryFilter map[uint32]bool) *ParamData {
	playItems := NewPlayItems(ctx, serviceInfo.UserID, in.GetTabId(), in.GetTabIds(), in.GetCategoryIds(), in.GetChannelListEnterSource(), tabFilter, categoryFilter)
	if playItems == nil {
		return &ParamData{}
	}

	tabStr, categoryStr := playItems.GetReportDataStr(ctx)
	reportData := &blreport.ChannelList{
		TabId:       in.GetTabId(),
		UserId:      serviceInfo.UserID,
		TabIds:      tabStr,
		CategoryIds: categoryStr,
	}
	costReporter := cost_time_reporter.NewTimeCostCounterWithName("channel-play-logic/ListTopicChannel", costReportNameSpace)
	options, genOption, businessOption := convert.ConvertBlockBlockOption(ctx, in.GetBlockOption())

	isRefresh := false
	if genPB.GetRecommendationListReq_GetListMode(in.GetGetMode()) == genPB.GetRecommendationListReq_REFRESH ||
		genPB.GetRecommendationListReq_GetListMode(in.GetGetMode()) == genPB.GetRecommendationListReq_DEFAULT {
		isRefresh = true
	}

	isSelect := false
	optionLen := len(in.GetBlockOption()) + len(in.GetLabels())
	if optionLen > 0 || in.GetSex() != channel_play.RcmdSex_All {
		isSelect = true
	}

	// 过滤掉过长的屏蔽词, 处理中文
	if len(in.GetShieldFilterWords()) > maxShieldFilterWordsLen {
		log.WarnWithCtx(ctx, "shield filter words too long, len:%d", len(in.GetShieldFilterWords()))
		in.ShieldFilterWords = in.ShieldFilterWords[:maxShieldFilterWordsLen]
	}

	return &ParamData{
		TabFilter:      tabFilter,
		CategoryFilter: categoryFilter,
		PlayItems:      playItems,
		ReportData:     reportData,
		CostReporter:   costReporter,
		ServiceInfo:    serviceInfo,
		In:             in,
		Env:            conf.Environment,
		Options:        options,
		GenOption:      genOption,
		BusinessOption: businessOption,
		IsRefresh:      isRefresh,
		IsSelect:       isSelect,
	}
}

func (p *ParamData) IsDoudi(ctx context.Context) (isDoudi, isRecommendText bool) {
	if p.PlayItems.IsGameRcmdTab {
		isDoudi = true
		isRecommendText = true
	} else if p.PlayItems.IsRcmdTab {
		isDoudi = true
	} else {
		if p.IsSelect {
			isDoudi = true
			isRecommendText = true
		} else {
			if len(p.PlayItems.TabIds) > 0 {
				//是否音乐
				tabInfo := cache.GetTabInfoCache().GetTabInfoCacheById(p.PlayItems.TabIds[0])
				if tabInfo.GetHomePageType() == tabPB.HomePageType_HomePageTypeMUSIC {
					isDoudi = true
					isRecommendText = true
				} else {
					if p.IsRefresh {
						isDoudi = true
					}
				}
			} else if len(p.PlayItems.CategoryIds) > 0 {
				isDoudi = true
				isRecommendText = true
			}
		}

	}
	log.InfoWithCtx(ctx, "isDoudi:%t, isRecText:%t, rcmdtab:%t, isSelect:%t, isrefresh:%t, cate len:%d, tabIds len:%d", isDoudi, isRecommendText, p.PlayItems.IsRcmdTab, p.IsSelect, p.IsRefresh, len(p.PlayItems.CategoryIds), len(p.PlayItems.TabIds))
	return isDoudi, isRecommendText
}

func FilterReport(ctx context.Context, uid uint32, in *channel_play.ListTopicChannelReq) {
	tabId, tabIds, categoryIds := in.GetTabId(), in.GetTabIds(), in.GetCategoryIds()
	// tabIds 和 categoryIds 只需有一个有值上报
	if len(tabIds) > 0 {
		categoryIds = nil
	} else {
		if tabId == 0 {
			if len(categoryIds) == 0 { //混推
				tabIds = []uint32{0}
			}
		} else {
			tabIds = []uint32{tabId}
			categoryIds = nil
		}
	}

	tabStr, categoryStr := GetReportDataStr(tabIds, categoryIds)
	reportData := &blreport.ChannelList{
		TabId:       in.GetTabId(),
		UserId:      uid,
		TabIds:      tabStr,
		CategoryIds: categoryStr,
	}
	reportData.SetFilterlType(blreport.F_ID_ChannelList_Minors_Filter)
	blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelListInfo(ctx, reportData)
}

// 转换完的，用于业务逻辑处理，tabid不为0代码某个玩法，如果多个玩法同时来请求，走tabids
type PlayItems struct {
	TabId         uint32
	TabIds        []uint32
	CategoryIds   []uint32
	IsRcmdTab     bool
	IsGameRcmdTab bool
}

func NewPlayItems(ctx context.Context, uid, tabId uint32, tabIds, categoryIds []uint32, source channel_play.ChannelListEnterSource,
	tabFilter, categoryFilter map[uint32]bool) *PlayItems {
	//选择推荐tab时，安卓传-1，改成0
	if tabId == math.MaxUint32 {
		tabId = 0
	}
	if len(tabIds) == 1 && tabIds[0] == math.MaxUint32 {
		tabIds = []uint32{}
	}
	//是否混推
	var isRcmdTab bool
	if tabId == 0 && len(categoryIds) == 0 && len(tabIds) == 0 {
		isRcmdTab = true
	} else if tabId != 0 {
		//旧逻辑，跟推荐约定好了，当tabId不为0，tabIds也要传
		tabIds = append(tabIds, tabId)
	}

	var isGameRcmdTab bool
	if source == channel_play.ChannelListEnterSource_MiniGameZoneSource && isRcmdTab {
		categoryIds = conf.ChannelPlayLogicConfig.GetFilterEntranceCategoryMap(ctx, channel_play.FilterEntrance_MINIGAME_ZONE)
		isGameRcmdTab = true
	}

	if !isRcmdTab {
		// 未成年过滤处理
		/*		tempTabIds := make([]uint32, 0, len(tabIds))
				for _, tId := range tabIds {
					tempTabInfo := cache.GetTabInfoCacheById(tId)
					if tabFilter[tId] || categoryFilter[tempTabInfo.GetCategoryId()] {
						log.InfoWithCtx(ctx, "tabFilter or categoryFilter tabId:%d, categoryId:%d, uid:%d", tempTabInfo.GetId(), tempTabInfo.GetCategoryId(), uid)
						continue
					}
					tempTabIds = append(tempTabIds, tabId)
				}
				tabIds = tempTabIds*/

		// 类目过滤
		if len(tabIds) == 0 {
			tempCategoryIds := make([]uint32, 0, len(categoryIds))
			for _, categoryId := range categoryIds {
				if categoryFilter[categoryId] {
					log.InfoWithCtx(ctx, "categoryFilter categoryId:%d, uid:%d", categoryId, uid)
					continue
				}

				categoryWithTabs := cache.GetTabsOfCategoryCache()[categoryId]
				tabIdItems := make([]uint32, 0, len(categoryWithTabs))
				for _, tempTabInfo := range categoryWithTabs {
					if tabFilter[tempTabInfo.GetId()] {
						continue
					}
					tabIdItems = append(tabIdItems, tempTabInfo.GetId())
				}
				// 有被过滤掉部分tab，传参改为不传category_id, 没被过滤的添加到tabIds
				if len(tabIdItems) != len(categoryWithTabs) {
					log.InfoWithCtx(ctx, "uid:%d, categoryId:%d part of tabFilter, not filter tabIds:%v ", uid, categoryId, tabIdItems)
					tabIds = append(tabIds, tabIdItems...)
					continue
				}
				tempCategoryIds = append(tempCategoryIds, categoryId)
			}
			categoryIds = tempCategoryIds
		}

		// 都被过滤掉了，不再请求房间列表
		if len(categoryIds) == 0 && len(tabIds) == 0 {
			return nil
		}
	}

	return &PlayItems{
		TabId:         tabId,
		TabIds:        tabIds,
		CategoryIds:   categoryIds,
		IsRcmdTab:     isRcmdTab,
		IsGameRcmdTab: isGameRcmdTab,
	}
}

// 判断是否混推或则半混推
func (p *PlayItems) IsMutiTabs() bool {
	/*
		p.tabId == 0 && len(p.tabIds) == 0, len(categoryIDs)=0 全混推，返回所有类型的房间len(categoryIds)>0返回指定分类房间，半混推
		len(p.tabIds) len(tabIds)>1 筛选指定分类下部分tab
	*/
	if p.TabId == 0 && len(p.TabIds) == 0 || len(p.TabIds) > 1 {
		return true
	}
	return false
}

func (p *PlayItems) GetReportData(ctx context.Context) ([]uint32, []uint32) {
	if len(p.TabIds) != 0 {
		return p.TabIds, nil
	} else {
		if p.TabId == 0 {
			if len(p.CategoryIds) == 0 { //混推
				tabIds := []uint32{0}
				return tabIds, nil
			} else if len(p.CategoryIds) != 0 { //半混推
				return p.TabIds, p.CategoryIds
			}
		} else {
			//因为tabid会加入到tabids，所以tabids为空时tabid不可能非0
			log.ErrorWithCtx(ctx, "GetReportData tab err, playItem:%+v", p)
		}
	}
	return nil, nil
}

func (p *PlayItems) GetReportDataAllStr(ctx context.Context) string {
	tabIds, categoryIds := p.GetReportDataStr(ctx)
	return fmt.Sprintf("tabids:%v,cateids:%v", tabIds, categoryIds)
}

func (p *PlayItems) GetReportDataStr(ctx context.Context) (tabStr string, categoryStr string) {
	tabIds, categoryIds := p.GetReportData(ctx)
	return GetReportDataStr(tabIds, categoryIds)
}

func GetReportDataStr(tabIds, categoryIds []uint32) (tabStr string, categoryStr string) {
	tabSlice := make([]string, 0, len(tabIds))
	categorySlice := make([]string, 0, len(categoryIds))
	for _, tabId := range tabIds {
		tabSlice = append(tabSlice, strconv.Itoa(int(tabId)))
	}
	for _, categoryId := range categoryIds {
		categorySlice = append(categorySlice, strconv.Itoa(int(categoryId)))
	}
	if len(tabSlice) != 0 {
		tabStr = strings.Join(tabSlice, ",")
	}
	if len(categorySlice) != 0 {
		categoryStr = strings.Join(categorySlice, ",")
	}
	return tabStr, categoryStr
}
