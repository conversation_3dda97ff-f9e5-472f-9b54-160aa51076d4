package negative_feedback

import (
	"context"
	"strings"

	account_go "golang.52tt.com/clients/account-go"
	device_info_service "golang.52tt.com/clients/datacenter/device-info-service"
	gangup_channel "golang.52tt.com/clients/gangup-channel"
	"golang.52tt.com/clients/topic-channel/tab"
	"golang.52tt.com/kaihei-pkg/tab/question"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	"golang.52tt.com/protocol/common/status"
	gangup_channel_svr "golang.52tt.com/protocol/services/gangup-channel"
	tabpb "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
)

const max_reason_num = 5

type NegativeFeedbackMgr struct {
	gangupClient *gangup_channel.Client
	tabClient    *tab.Client

	tabQuestionValidators *question.Validators
}

func NewNegativeFeedbackMgr(
	gangupClient *gangup_channel.Client,
	tabClient *tab.Client,
	accountClient account_go.IClient,
	deviceInfoClient device_info_service.IClient,
) *NegativeFeedbackMgr {
	tabQuestionValidators := question.NewValidators(accountClient, deviceInfoClient)
	return &NegativeFeedbackMgr{
		gangupClient: gangupClient,
		tabClient:    tabClient,

		tabQuestionValidators: tabQuestionValidators,
	}
}

func (n *NegativeFeedbackMgr) GetNegativeFeedBackInRoom(ctx context.Context, feedbackType channel_play.FeedBackTypeInRoom) (reason []string, maskTitle string, err error) {
	resp, err := n.gangupClient.GetNegativeFeedbackOption(ctx, &gangup_channel_svr.GetNegativeFeedbackOptionReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNegativeFeedBackInRoom gangupClient.GetNegativeFeedbackOption feedbackType%v err(%v)", feedbackType, err)
		return
	}
	switch feedbackType {
	case channel_play.FeedBackTypeInRoom_KnockOut_FeedBackType, channel_play.FeedBackTypeInRoom_VisitorList_FeedBackType: //踢人，访客列表
		reason = resp.GetFeedback().GetOwnerChannelFeedback().GetReasons()
		maskTitle = resp.GetFeedback().GetOwnerChannelFeedback().GetBlockText()
	case channel_play.FeedBackTypeInRoom_RoomVisitor_FeedBackType: //访客侧
		reason = resp.GetFeedback().GetGuestChannelFeedback().GetReasons()
		maskTitle = resp.GetFeedback().GetGuestChannelFeedback().GetBlockText()
	case channel_play.FeedBackTypeInRoom_QuitUninterested_FeedBackType: // 退房不感兴趣
		reason = resp.GetFeedback().GetQuitChannelFeedback().GetReasons()
		maskTitle = resp.GetFeedback().GetQuitChannelFeedback().GetBlockText()
	default:
		log.ErrorWithCtx(ctx, "GetNegativeFeedBackInRoom unknown feedbackType %v", feedbackType)
		return nil, "", protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "unknown feedbackType")
	}
	//限制返回的原因数量
	limitNum := conf.ChannelPlayLogicConfig.LoadConfig().NegativeReasonNum
	if limitNum <= 0 {
		limitNum = max_reason_num
	}
	if len(reason) > limitNum {
		reason = reason[0:limitNum]
	}
	log.DebugWithCtx(ctx, "feedbackType %v, reason %v, maskTitle %s", feedbackType, reason, maskTitle)
	return
}

func (n *NegativeFeedbackMgr) ReportNegativeFeedBackInRoom(ctx context.Context, in *channel_play.ReportNegativeFeedBackInRoomReq) (hitType uint32, err error) {
	_, err = n.gangupClient.NegativeFeedBack(ctx, &gangup_channel_svr.NegativeFeedBackReq{
		ChannelId:                    in.GetChannelId(),
		TabId:                        in.GetTabId(),
		Negative_FeedbackType:        n.convertToGangupFeedbackType(in.GetFeedBackType()),
		ReporterUid:                  in.GetReporterUid(),
		BlackChannelUser:             in.GetBlackUid(),
		ReasonsOfBlackChannelUser:    in.GetReasons(),
		BlackChannelUserEnableFilter: in.GetEnableFilter(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportNegativeFeedBackInRoom in(%s) error: %v", in.String(), err)
		return hitType, err
	}
	resp, err := n.tabClient.GetTabQuestionConfig(ctx, &tabpb.GetTabQuestionConfigReq{
		TabId: in.GetTabId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportNegativeFeedBackInRoom in(%s) GetTabQuestionConfig error: %v", in.String(), err)
		return hitType, err
	}

	if hitKeyWords(resp.GetConfig().GetAgeKeywords(), in.GetReasons()) {
		hitType = uint32(channel_play.ReportNegativeFeedBackInRoomResp_HIT_TYPE_AGE)
	}
	return hitType, nil
}

func hitKeyWords(keyWords []string, inWords []string) bool {
	if len(keyWords) == 0 || len(inWords) == 0 {
		return false
	}
	for _, v := range inWords {
		for _, key := range keyWords {
			if strings.Contains(v, key) {
				return true
			}
		}
	}
	return false
}

func (n *NegativeFeedbackMgr) convertToGangupFeedbackType(inType channel_play.FeedBackTypeInRoom) []gangup_channel_svr.NegativeFeedbackType {
	var gangupType gangup_channel_svr.NegativeFeedbackType
	switch inType {
	case channel_play.FeedBackTypeInRoom_KnockOut_FeedBackType:
		fallthrough
	case channel_play.FeedBackTypeInRoom_VisitorList_FeedBackType:
		gangupType = gangup_channel_svr.NegativeFeedbackType_FeedbackTypeOwnerInChannel
	case channel_play.FeedBackTypeInRoom_RoomVisitor_FeedBackType:
		gangupType = gangup_channel_svr.NegativeFeedbackType_FeedbackTypeUserInChannel
	case channel_play.FeedBackTypeInRoom_QuitUninterested_FeedBackType:
		gangupType = gangup_channel_svr.NegativeFeedbackType_FeedbackTypeQuitUninterestedInChannel
	default:
		log.Errorf("unknown feedbackType %v", inType)
	}
	return []gangup_channel_svr.NegativeFeedbackType{gangupType}
}
