package channel_operation

import (
	"context"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic_ext"
	"golang.52tt.com/clients/channelapi"
	"golang.52tt.com/clients/channelmic"
	gangup_channel "golang.52tt.com/clients/gangup-channel"
	topicchannel "golang.52tt.com/clients/topic-channel/channel"
	rcmdChannelLabel "golang.52tt.com/clients/topic-channel/rcmd-channel-label"
	"golang.52tt.com/kaihei-pkg/blreport"
	"golang.52tt.com/pkg/cogroup_util"
	pkg_utils "golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	channelPB "golang.52tt.com/protocol/app/channel"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/channel-play-index"
	channelPlayIndex "golang.52tt.com/protocol/services/channel-play-index"
	gangupchannelpb "golang.52tt.com/protocol/services/gangup-channel"
	music_topic_channel "golang.52tt.com/protocol/services/music-topic-channel"
	topicChannelPB "golang.52tt.com/protocol/services/topic_channel/channel"
	rcmd_channel_label_pb "golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
	tabpb "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/channel_list/view"
	"golang.52tt.com/services/channel-play-logic/internal/utils"
	"math"
	"strconv"
	"strings"
	"time"
)

const (
	timeLayout = "2006-01-02 15:04:05"

	autoPublish = "0"
	userPublish = "1"

	releaseHistoryPrefix = "last-release-"
	changeHistoryPrefix  = "last-change-"
	ReportBlockNoLimit   = ",不限"
	ReportBlockUnSelect  = ",不限（没有设置）"
)

var InfElemID uint32 = math.MaxUint32

type Publisher struct {
	gangupClient            *gangup_channel.Client
	topicClient             *topicchannel.Client
	channelMicClient        *channelmic.Client
	channelApiClient        *channelapi.Client
	rcmdChannelLabelClient  *rcmdChannelLabel.Client
	indexCli                *channelPlayIndex.Client
	channelMicExtCli        *channel_mic_ext.Client
	musicTopicChannelClient music_topic_channel.MusicChannelClient
}

func NewPublisher(gangupClient *gangup_channel.Client, topicClient *topicchannel.Client, channelMicClient *channelmic.Client,
	channelApiClient *channelapi.Client, rcmdChannelLabelClient *rcmdChannelLabel.Client, indexCli *channelPlayIndex.Client,
	channelMicExtCli *channel_mic_ext.Client, musicTopicChannelClient music_topic_channel.MusicChannelClient) (*Publisher, error) {
	return &Publisher{
		gangupClient:            gangupClient,
		topicClient:             topicClient,
		channelApiClient:        channelApiClient,
		channelMicClient:        channelMicClient,
		rcmdChannelLabelClient:  rcmdChannelLabelClient,
		indexCli:                indexCli,
		channelMicExtCli:        channelMicExtCli,
		musicTopicChannelClient: musicTopicChannelClient,
	}, nil
}

// 异常流程检测，以客户端上报上来的tab为准进行修正
func (p *Publisher) CheckAndSwitchTab(ctx context.Context, channelId, uid, tabId uint32, cHandle *CommonHandle) error {
	//1 获取房间玩法信息,校验客户端传入的tabId与玩法服务数据是否一致
	schemeInfo, err := cHandle.GetSchemeInfoByCid(ctx, channelId)
	if err != nil {
		//log.ErrorWithCtx(ctx, "PublishGangupChannel GetSchemeInfoByCid channelId(%d) err(%v)", channelId, err)
		return err
	}
	//玩法数据不一致，切换一次玩法
	if schemeInfo.GetSchemeId() != tabId {
		log.WarnWithCtx(ctx, "PublishGangupChannel 玩法数据不一致！！！ channelId(%d) oldTab(%d) pubTab(%d)", channelId, schemeInfo.GetSchemeId(), tabId)
		//不一致，以客户端的为准，服务端切换玩法
		_, err := p.topicClient.SwitchChannelTab(ctx, &topicChannelPB.SwitchChannelTabReq{
			Uid:       uid,
			Creator:   uid,
			TabId:     tabId,
			ChannelId: channelId,
			Source:    topicChannelPB.Source_PUBLISH,
		})
		return err
	}
	return nil
}

func (p *Publisher) CalcAutoDismissDuration(ctx context.Context, channelId, tabId, clientType uint32, publishTime int64, isPublish bool) (autoDismissDuration uint32) {
	//var autoDismissConfig uint32
	releaseCondition := cache.GetTabInfoCache().GetReleaseConditionByTabId(tabId, clientType)
	if releaseCondition == nil {
		autoDismissDuration = 180
		log.WarnWithCtx(ctx, "cache.GetReleaseConditionCache err:not exist, channelId:%d, tabId:%d", channelId, tabId)
	} else {
		//从缓存拿数据，异常情况默认返回180
		cacheValue := releaseCondition.GetReleaseDuration()
		if cacheValue <= 0 {
			autoDismissDuration = 180
		} else {
			autoDismissDuration = cacheValue
		}
	}
	if !isPublish {
		//不在发布中，直接返回配置值
		return autoDismissDuration
	}
	//发布中，自动取消发布时间为  配置的自动取消发布时长-（当前时间-上次发布时间）
	publishDuration := time.Now().Unix() - publishTime
	res := int64(autoDismissDuration) - publishDuration
	if res > 0 {
		autoDismissDuration = uint32(res)
	} else {
		autoDismissDuration = 180
		log.WarnWithCtx(ctx, "CalcAutoDismissDuration err: autoDismissDuration:%d, diff:%d, channelId:%d, tabId:%d", autoDismissDuration, publishDuration, channelId, tabId)
	}
	return autoDismissDuration
}

func (p *Publisher) PublishChannel(ctx context.Context, in *channel_play.PublishGangupChannelReq, serviceInfo *grpc.ServiceInfo,
	channelName string, publishTime int64, isPublish bool, cHandle *CommonHandle, reportData *blreport.ChannelPublish,
	requestId string, tabInfo *tabpb.Tab) (autoDismissDuration uint32, err error) {

	channelId := in.GetChannelId()
	uid := serviceInfo.UserID
	//修改房间名
	if requestId != NoModifyNameRequest {
		err = cHandle.ModifyChannelName(nil, ctx, uid, channelId, serviceInfo.MarketID, channelName, requestId)
		if err != nil {
			return 0, err
		}
	}

	// 过渡阶段，扩列两边都需要处理
	if conf.PublicSwitchConfig.IsOpenFastPcChatTabDoubleWrite() && tabInfo.GetId() == conf.PublicSwitchConfig.GetMuseChatTabId() {
		_, _ = p.PublishGangupChannel(ctx, in, serviceInfo, channelName, publishTime, isPublish, reportData, tabInfo)
		return p.PublishMusicChannel(ctx, in, serviceInfo, channelName, publishTime, isPublish, tabInfo)

	} else if tabInfo.GetHomePageType() == tabpb.HomePageType_HomePageTypeMUSIC {
		return p.PublishMusicChannel(ctx, in, serviceInfo, channelName, publishTime, isPublish, tabInfo)
	} else {
		return p.PublishGangupChannel(ctx, in, serviceInfo, channelName, publishTime, isPublish, reportData, tabInfo)
	}

}

func (p *Publisher) PublishMusicChannel(ctx context.Context, in *channel_play.PublishGangupChannelReq, serviceInfo *grpc.ServiceInfo,
	channelName string, publishTime int64, isPublish bool, tabInfo *tabpb.Tab) (autoDismissDuration uint32, err error) {

	unSelectBlockIds, unSelectBlockOptions := p.genMusicUserUnSelectBlockIds(in.GetBlockOptions(), cache.GetBaseBlocksByTabId(tabInfo.GetId(), uint32(serviceInfo.ClientType)))
	_, err = p.musicTopicChannelClient.SetMusicChannelReleaseInfo(ctx, &music_topic_channel.SetMusicChannelReleaseInfoReq{
		MusicChannelReleaseInfo: &music_topic_channel.MusicChannelReleaseInfo{
			Id:           in.GetChannelId(),
			TabId:        tabInfo.GetId(),
			BlockOptions: p.convertMuseBlockOption(in.GetBlockOptions()),
			DisplayType:  []music_topic_channel.ChannelDisplayType{music_topic_channel.ChannelDisplayType_DISPLAY_AT_MAIN_PAGE},
			ShowGeoInfo:  in.GetIsShowGeoInfo(),
			GameLabels:   p.convertMusicGameLabels(in.GetGameLabels()),
			ClientType:   uint32(serviceInfo.ClientType),
		},
		ReleaseIp:            serviceInfo.ClientIPAddr().String(),
		ChannelName:          channelName,
		Creator:              serviceInfo.UserID,
		WantFresh:            in.GetIsWantFresh(),
		AllSelectedBids:      in.GetAllSelectedBids(),
		UnSelectBlockId:      unSelectBlockIds,
		UnSelectBlockOptions: unSelectBlockOptions,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PublishMusicChannel SetMusicChannelReleaseInfo err:%+v", err)
		return
	}

	//10 房间冷却时间
	go p.updateChannelFrequency(in.GetChannelId(), isPublish)
	//11 计算客户端自动取消发布时间
	autoDismissDuration = p.CalcAutoDismissDuration(ctx, in.GetChannelId(), tabInfo.GetId(), uint32(serviceInfo.ClientType), publishTime, isPublish)

	return autoDismissDuration, nil
}

// 房间信息处理（发布状态，冷却， 房间名）
func (p *Publisher) PublishGangupChannel(ctx context.Context, in *channel_play.PublishGangupChannelReq, serviceInfo *grpc.ServiceInfo,
	channelName string, publishTime int64, isPublish bool, reportData *blreport.ChannelPublish, tabInfo *tabpb.Tab) (autoDismissDuration uint32, err error) {

	//7 写入发布房间信息
	releaseInfo := p.genReleaseInfo(publishTime, in, uint32(serviceInfo.ClientType), tabInfo.GetId())
	unSelectBlockIds, unSelectBlockOptions := p.genUserUnSelectBlockIds(in.GetBlockOptions(), cache.GetBaseBlocksByTabId(tabInfo.GetId(), uint32(serviceInfo.ClientType)))
	_, err = p.gangupClient.SetGangupChannelReleaseInfo(ctx, &gangupchannelpb.SetGangupChannelReleaseInfoReq{
		GangupChannelReleaseInfo: releaseInfo,
		WantFresh:                in.GetIsWantFresh(),
		ReleaseIp:                serviceInfo.ClientIPAddr().String(),
		ChannelName:              channelName,
		Creator:                  serviceInfo.UserID,
		AllSelectedBids:          in.GetAllSelectedBids(),
		UnSelectBlockId:          unSelectBlockIds,
		UnSelectBlockOptions:     unSelectBlockOptions,
		ChannelPlayMode:          uint32(in.GetUgcChannelPlayMode()),
	})
	if err != nil {
		utils.ErrCntLog(ctx, "PublishGangupChannel SetGangupChannelReleaseInfo fail,in(%s), err: %v", in.String(), err)
		reportData.SetFilterlType(blreport.F_ID_ChannelPublish_Publish_Err)
		blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelPublishInfo(ctx, reportData)
		return 0, err
	}

	if tabInfo.GetCategoryMapping() == uint32(topic_channel.CategoryType_Gangup_type) {
		publishIndex := &channel_play_index.SetPublishTabCntByOneDayReq{
			TabId:       tabInfo.GetId(),
			Uid:         serviceInfo.UserID,
			PublishTime: publishTime,
			PublishType: channel_play_index.PublishRecordType_CATE_TYPE_KAIHEI,
		}
		indexCtx, cancel := context.WithTimeout(ctx, 50*time.Millisecond)
		_, err = p.indexCli.SetPublishTabCntByOneDay(indexCtx, publishIndex)
		if err != nil {
			log.ErrorWithCtx(indexCtx, "PublishGangupChannel SetPublishTabCntByOneDay err(%v)", err)
		}
		cancel()
	}

	//10 房间冷却时间
	go p.updateChannelFrequency(in.GetChannelId(), isPublish)
	//11 计算客户端自动取消发布时间
	autoDismissDuration = p.CalcAutoDismissDuration(ctx, in.GetChannelId(), tabInfo.GetId(), uint32(serviceInfo.ClientType), publishTime, isPublish)

	return autoDismissDuration, nil
}

func (p *Publisher) genMusicUserUnSelectBlockIds(blockOptions []*topic_channel.BlockOption, blocks []*tabpb.Block) (
	unSelectBlockIds []uint32, unSelectBlockOptions []*music_topic_channel.BlockOption) {
	unSelectBlockIds = make([]uint32, 0)
	unSelectBlockOptions = make([]*music_topic_channel.BlockOption, 0)
	if len(blocks) == 0 {
		return
	}
	selectedMap := make(map[uint32]bool)
	for _, option := range blockOptions {
		if !selectedMap[option.GetBlockId()] {
			selectedMap[option.GetBlockId()] = true
		}
	}
	for _, b := range blocks {
		if selectedMap[b.GetId()] {
			continue
		}
		unSelectBlockIds = append(unSelectBlockIds, b.GetId())
		unSelectBlockOptions = append(unSelectBlockOptions, p.genMusicUnSelectBlockOption(b)...)
	}
	return
}

func (p *Publisher) genUserUnSelectBlockIds(blockOptions []*topic_channel.BlockOption, blocks []*tabpb.Block) (
	unSelectBlockIds []uint32, unSelectBlockOptions []*gangupchannelpb.BlockOption) {
	unSelectBlockIds = make([]uint32, 0)
	unSelectBlockOptions = make([]*gangupchannelpb.BlockOption, 0)
	if len(blocks) == 0 {
		return
	}
	selectedMap := make(map[uint32]bool)
	for _, option := range blockOptions {
		if !selectedMap[option.GetBlockId()] {
			selectedMap[option.GetBlockId()] = true
		}
	}
	for _, b := range blocks {
		if selectedMap[b.GetId()] {
			continue
		}
		unSelectBlockIds = append(unSelectBlockIds, b.GetId())
		unSelectBlockOptions = append(unSelectBlockOptions, p.genUnSelectBlockOption(b)...)
	}
	return
}

func (p *Publisher) genMusicUnSelectBlockOption(block *tabpb.Block) []*music_topic_channel.BlockOption {
	res := make([]*music_topic_channel.BlockOption, 0, len(block.GetElems()))
	for _, e := range block.GetElems() {
		res = append(res, &music_topic_channel.BlockOption{
			BlockId: block.GetId(),
			ElemId:  e.GetId(),
		})
	}
	return res
}

func (p *Publisher) genUnSelectBlockOption(block *tabpb.Block) []*gangupchannelpb.BlockOption {
	res := make([]*gangupchannelpb.BlockOption, 0, len(block.GetElems()))
	for _, e := range block.GetElems() {
		res = append(res, &gangupchannelpb.BlockOption{
			BlockId: block.GetId(),
			ElemId:  e.GetId(),
		})
	}
	return res
}

func (p *Publisher) updateChannelFrequency(channelId uint32, isPublish bool) {
	var err error
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer cancel()

	if isPublish {
		// 修改房间发布信息冷却时间
		_, err = p.topicClient.SetExtraHistory(ctx, &topicChannelPB.SetExtraHistoryReq{
			Key:         changeHistoryPrefix + strconv.Itoa(int(channelId)),
			Value:       strconv.Itoa(int(time.Now().Unix())),
			ExpireAfter: int64(conf.ChannelPlayLogicConfig.LoadConfig().ChangeCoolDown),
		})

		if err != nil {
			log.ErrorWithCtx(ctx, "PublishHobbyChannel SetChangeHistory channelId(%v)  err(%v)", channelId, err)
		}
	}
	// 修改发布冷却时间，修改发布信息也要重置一次发布冷却时间
	_, err = p.topicClient.SetExtraHistory(ctx, &topicChannelPB.SetExtraHistoryReq{
		Key:         releaseHistoryPrefix + strconv.Itoa(int(channelId)),
		Value:       strconv.Itoa(int(time.Now().Unix())),
		ExpireAfter: int64(conf.ChannelPlayLogicConfig.LoadConfig().FreezeDuration),
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "PublishHobbyChannel SetExtraHistory channelId(%v) err(%v)", channelId, err)
	}
	//修改房间发布频率，修改发布信息，发布都算一次发布
	_, err = p.gangupClient.SetGangupPublishCountHistory(ctx, &gangupchannelpb.SetGangupPublishCountHistoryReq{
		Cid: channelId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PublishHobbyChannel SetGangupPublishCountHistory channelId(%v) err(%v)", channelId, err)
	}
}

type publisherReportParam struct {
	totalDate         int64
	channelId         uint32
	channelCreatorUid uint32
	publishTime       int64
	tabId             uint32
	tabName           string
	channelName       string
	channelPara       string
	publishSource     string
	regionId          uint64
	publishType       string
	micMode           uint32
	micSettingInfo    string
}

type MicSettingInfo struct {
	BlockName string
	MicId     uint32
	MicName   string
}

func (p *Publisher) GenReportDataForDatacenter(ctx context.Context, param *publisherReportParam) map[string]interface{} {
	reportKV := map[string]interface{}{
		"totalDate":   time.Unix(param.totalDate, 0).Format(timeLayout),
		"ccUid":       param.channelCreatorUid,
		"publishTime": time.Unix(param.publishTime, 0).Format(timeLayout),
		"channelID":   strconv.Itoa(int(param.channelId)),
		"tagId":       strconv.Itoa(int(param.tabId)),
		"tagName":     param.tabName,
		"isPrivate":   "0", // 在开黑tab发布房间是否被设置成私密发布，0-否，1-是,已经不保存的字段，默认传0
		// 房间信息
		"channelPara": param.channelPara,
		// 发布房间来源，
		// 1-开黑tab发布（寻找合拍队友），
		// 2-个人房点击找人玩发布（寻找合拍队友），
		// 3-旧版开黑tab，
		// 4-个人房点击找人玩发布（召唤好友），
		// 5-开黑tab发布（召唤好友）,
		// 6-开黑tab发布（优先匹配萌新），
		// 7-个人房点击找人玩发布（优先匹配萌新），
		// 8-个人房点击找人玩发布（允许同城匹配）
		"publishSource": param.publishSource,
		"channelName":   param.channelName,
		"regionId":      param.regionId,
		"micMode":       param.micMode,
	}
	log.DebugWithCtx(ctx, "publisher GenReportDataForDatacenter reportKV: %v", reportKV)
	return reportKV
}

func (p *Publisher) GenReportDataForBylink(ctx context.Context, param *publisherReportParam) map[string]interface{} {
	bylinkKV := map[string]interface{}{
		"revc_time":    time.Unix(param.totalDate, 0).Format(timeLayout),
		"room_own_id":  param.channelCreatorUid,
		"publish_time": time.Unix(param.publishTime, 0).Format(timeLayout),
		"room_id":      strconv.Itoa(int(param.channelId)),
		"tag_id":       strconv.Itoa(int(param.tabId)),
		"tag_name":     param.tabName,
		"is_private":   "0", // 在开黑tab发布房间是否被设置成私密发布，0-否，1-是,已经不保存的字段，默认传0
		// 房间信息
		"room_para": param.channelPara,
		// 发布房间来源，
		// 1-开黑tab发布（寻找合拍队友），
		// 2-个人房点击找人玩发布（寻找合拍队友），
		// 3-旧版开黑tab，
		// 4-个人房点击找人玩发布（召唤好友），
		// 5-开黑tab发布（召唤好友）,
		// 6-开黑tab发布（优先匹配萌新），
		// 7-个人房点击找人玩发布（优先匹配萌新），
		// 8-个人房点击找人玩发布（允许同城匹配）
		"source":           param.publishSource,
		"room_name":        param.channelName,
		"publish_type":     param.publishType,
		"mic_setting_info": param.micSettingInfo,
	}
	log.DebugWithCtx(ctx, "publisher bylinkKV: %v", bylinkKV)
	return bylinkKV
}

func (p *Publisher) IsChangeInPublish(ctx context.Context, channelId uint32, cHandle *CommonHandle) (isPublish bool, publishTime int64, err error) {
	tcResp, err := p.gangupClient.GetGangupChannelByIds(ctx, &gangupchannelpb.GetGangupChannelByIdsReq{Ids: []uint32{channelId}, ReturnAll: true})
	if err != nil {
		log.ErrorWithCtx(ctx, "isChangeInPublish GetChannelByIds channelId %d error :%v", channelId, err)
		return false, 0, err
	}
	if len(tcResp.GetInfo()) != 0 {
		isPublish = cHandle.IsDisplayChannel(tcResp.Info[0].GetDisplayType())
	}
	if isPublish {
		publishTime = tcResp.Info[0].GetReleaseTime()
	} else {
		publishTime = time.Now().Unix()
	}
	return isPublish, publishTime, nil
}

// 检查冷却
func (p *Publisher) checkCoolStatus(g *cogroup_util.Group, ctx context.Context, channelId uint32, isPublish bool) (err error) {
	//开黑房间发布冷却时间存储迁移至gangup
	//lastRelease, err := p.gangupClient.GetGangupExtraHistory(ctx, &gangupchannelpb.GetGangupExtraHistoryReq{
	//	Cid:       channelId,
	//	IsRelease: true,
	//})
	//发布中，检查修改冷却
	if isPublish {
		lastChange, err := p.topicClient.GetExtraHistory(ctx, &topicChannelPB.GetExtraHistoryReq{Key: changeHistoryPrefix + strconv.Itoa(int(channelId))})
		if err != nil {
			utils.ErrCntLogWithGoGroup(g, err, ctx, "PublishHobbyChannel checkPublishCoolStatus GetExtraHistory err:%v, key:%s", err, changeHistoryPrefix+strconv.Itoa(int(channelId)))
			return err
		}
		if lastChange.Value != "" {
			return protocol.NewExactServerError(nil, status.ErrTopicChannelReleaseFreezing)
		}
		return nil
	}
	lastRelease, err := p.topicClient.GetExtraHistory(ctx, &topicChannelPB.GetExtraHistoryReq{Key: releaseHistoryPrefix + strconv.Itoa(int(channelId))})
	if err != nil {
		utils.ErrCntLogWithGoGroup(g, err, ctx, "PublishHobbyChannel checkPublishCoolStatus GetExtraHistory err:%v, key:%s", err, changeHistoryPrefix+strconv.Itoa(int(channelId)))
		return err
	}
	if lastRelease.Value != "" {
		return protocol.NewExactServerError(nil, status.ErrTopicChannelReleaseFreezing)
	}
	return nil
}

func convertTabSecondaryItem(tabInfo *tabpb.Tab) *topic_channel.ShowTopicChannelTabSecondaryItem {
	tabSecondaryItem := &topic_channel.ShowTopicChannelTabSecondaryItem{}
	tabSecondaryItem.ItemText = tabInfo.GetName()
	tabSecondaryItem.MaskLayer = tabInfo.GetMaskLayer()
	tabSecondaryItem.ItemIcon = tabInfo.GetCardsImageUrl()
	tabSecondaryItem.SmallCard = tabInfo.GetSmallCardUrl()
	tabSecondaryItem.TabId = tabInfo.GetId()
	tabSecondaryItem.CategoryId = tabInfo.GetCategoryId()
	if tabInfo.GetName() == "其他游戏" {
		tabSecondaryItem.HasList = true
	}
	tabSecondaryItem.TabType = topic_channel.ShowTopicChannelTabSecondaryItem_TabType(tabInfo.GetTabType())
	return tabSecondaryItem
}

func (p *Publisher) genReleaseInfo(publishTime int64, in *channel_play.PublishGangupChannelReq, clientType, tabId uint32) (releaseInfo *gangupchannelpb.GangupChannelReleaseInfo) {
	blockOptions := p.convertBlockOption(in.GetBlockOptions())
	gameLabels := p.convertGameLabels(in.GetGameLabels())
	releaseInfo = &gangupchannelpb.GangupChannelReleaseInfo{
		Id:           in.GetChannelId(),
		TabId:        tabId,
		ReleaseTime:  publishTime,
		BlockOptions: blockOptions,
		DisplayType:  []gangupchannelpb.ChannelDisplayType{gangupchannelpb.ChannelDisplayType_DISPLAY_AT_MAIN_PAGE},
		ShowGeoInfo:  in.GetIsShowGeoInfo(),
		GameLabels:   gameLabels,
		ClientType:   clientType,
	}
	return releaseInfo

}

func (p *Publisher) convertMuseBlockOption(options []*topic_channel.BlockOption) []*music_topic_channel.BlockOption {
	blockOptions := make([]*music_topic_channel.BlockOption, 0)
	for _, o := range options {
		if o.GetElemId() != InfElemID && o.GetElemId() > 0 {
			blockOptions = append(blockOptions, &music_topic_channel.BlockOption{
				BlockId: o.GetBlockId(),
				ElemId:  o.GetElemId(),
				ElemVal: o.GetElemVal(),
			})
		}
	}
	return blockOptions
}

func (p *Publisher) convertBlockOption(options []*topic_channel.BlockOption) []*gangupchannelpb.BlockOption {
	blockOptions := make([]*gangupchannelpb.BlockOption, 0)
	for _, o := range options {
		if o.GetElemId() != InfElemID && o.GetElemId() > 0 {
			blockOptions = append(blockOptions, &gangupchannelpb.BlockOption{
				BlockId: o.GetBlockId(),
				ElemId:  o.GetElemId(),
				ElemVal: o.GetElemVal(),
			})
		}
	}
	return blockOptions
}

func (p *Publisher) convertMusicGameLabels(labels []*topic_channel.GameLabel) []*music_topic_channel.GameLabel {
	gameLabels := make([]*music_topic_channel.GameLabel, 0, len(labels))
	for _, label := range labels {
		gameLabels = append(gameLabels, &music_topic_channel.GameLabel{
			Val:         label.GetValValue(),
			DisplayName: label.GetDisplayName(),
			Type:        uint32(label.GetType()),
		})
	}
	return gameLabels
}

func (p *Publisher) convertGameLabels(labels []*topic_channel.GameLabel) []*gangupchannelpb.GameLabel {
	gameLabels := make([]*gangupchannelpb.GameLabel, 0, len(labels))
	for _, label := range labels {
		gameLabels = append(gameLabels, &gangupchannelpb.GameLabel{
			Val:         label.GetValValue(),
			DisplayName: label.GetDisplayName(),
			Type:        uint32(label.GetType()),
		})
	}
	return gameLabels
}

func (p *Publisher) GenPushChannelModifyData(ctx context.Context, clientType uint32, teamDesc string, fastPcCfgTabInfo *channel_play.FastPcCfgTabInfo, tabInfo *tabpb.Tab) (tabModify map[string]interface{}) {
	var showPublishButton bool
	//首页配置未未分类，不展示发布按钮
	if tabInfo.GetHomePageType() == tabpb.HomePageType_HomePageTypeNone {
		showPublishButton = false
	} else {
		showPublishButton = tabInfo.GetShowPublishButton()
	}
	tabModify = map[string]interface{}{
		"sub_type":             uint32(channelPB.ChannelMsgSubType_CHANNEL_MSG_SUB_CHANGE_TOPIC_CHANNEL_TAB_ID),
		"tab_id":               tabInfo.GetId(),
		"tab_name":             tabInfo.GetName(),
		"mic_mod":              tabInfo.GetMicMod(),
		"tab_type":             tabInfo.GetTabType(),
		"playing_option":       []topic_channel.PlayingOption{topic_channel.PlayingOption_AT_MAIN_PAGE},
		"team_desc":            teamDesc,
		"fast_pc_cfg_tab_info": fastPcCfgTabInfo,
		"show_team_desc":       true,
		//是否展示发布按钮
		"show_publish_button": showPublishButton,
		//玩法所属分类的枚举标识
		"category_type": topic_channel.CategoryType(tabInfo.GetCategoryMapping()),
	}

	// PC版的请求扩列不展示游戏卡
	if !protocol.IsFastPcClientType(clientType) || tabInfo.GetId() != conf.PublicSwitchConfig.GetMuseChatTabId() {
		tabModify["tag_id"] = tabInfo.GetTagId()
		tabModify["game_card_id"] = tabInfo.GetGameInfo().GetGameCardId()
	}

	log.DebugWithCtx(ctx, "publisher GenPushChannelModifyData data:%v", tabModify)
	return tabModify
}

// GetChannelParam 生成数据上报ChannelParam字段
func (p *Publisher) GetChannelParam(ctx context.Context, blockOptions []*topic_channel.BlockOption, allSelectedBids []uint32, tabId, clientType uint32) string {
	blocks := cache.GetBaseBlocksByTabId(tabId, clientType)
	if blocks == nil {
		log.ErrorWithCtx(ctx, "publisher GetChannelParam err tabId(%d) no exist", tabId)
		return ""
	}
	if len(blockOptions) == 0 || len(blocks) == 0 {
		return ""
	}
	//用户选了不限的
	bidMap := make(map[uint32]bool, 0)
	for _, v := range allSelectedBids {
		bidMap[v] = true
	}

	//用户没选的
	unSelectBlockIds, _ := p.genUserUnSelectBlockIds(blockOptions, blocks)
	unSelectBidMap := make(map[uint32]bool, 0)
	for _, v := range unSelectBlockIds {
		unSelectBidMap[v] = true
	}

	//用户选择的
	var userSelectedBlockMap = map[uint32]map[uint32]view.BlockOptionVal{}
	for _, block := range blockOptions {
		if userSelectedBlockMap[block.BlockId] == nil {
			userSelectedBlockMap[block.BlockId] = map[uint32]view.BlockOptionVal{
				block.ElemId: {
					IsSelectEd: true,
					Val:        block.GetElemVal(),
				},
			}
		} else {
			userSelectedBlockMap[block.BlockId][block.ElemId] = view.BlockOptionVal{
				IsSelectEd: true,
				Val:        block.GetElemVal(),
			}
		}
	}

	var userSelected = map[string]string{}
	for _, block := range blocks {
		if block.GetMode() == tabpb.Block_USER_INPUT { // 新增的用户输入类型
			title := ""
			for _, elem := range block.GetElems() {
				if userSelectedBlockMap[block.GetId()] != nil && userSelectedBlockMap[block.GetId()][elem.GetId()].IsSelectEd {
					if title == "" {
						title = elem.GetTitle() + ":" + userSelectedBlockMap[block.GetId()][elem.GetId()].Val
					} else {
						title += "," + elem.GetTitle() + ":" + userSelectedBlockMap[block.GetId()][elem.GetId()].Val
					}
				}
			}
			userSelected[block.GetTitle()] = title

		} else {
			for _, elem := range block.GetElems() {
				if userSelectedBlockMap[block.GetId()] != nil && userSelectedBlockMap[block.GetId()][elem.GetId()].IsSelectEd || unSelectBidMap[block.GetId()] {
					if userSelected[block.GetTitle()] == "" {
						userSelected[block.GetTitle()] = elem.GetTitle()
					} else {
						userSelected[block.GetTitle()] += "," + elem.GetTitle()
					}
				}

			}
			if bidMap[block.GetId()] {
				userSelected[block.GetTitle()] += ReportBlockNoLimit
			}
			if unSelectBidMap[block.GetId()] {
				userSelected[block.GetTitle()] += ReportBlockUnSelect
			}
		}

	}
	res := pkg_utils.ToJson(userSelected)
	log.DebugWithCtx(ctx, "PublishGangupChannel dataReport userSelected:%s, unSelectBlockIds:%v, blockOptions:%v", userSelected, unSelectBlockIds, blockOptions)
	return res
}

// 生成数据上报PublishSource字段
func (p *Publisher) GetPublishSource(isWantFresh, isShowGeoInfo bool) string {
	// 发布房间来源，
	// 1-开黑tab发布（寻找合拍队友），
	// 2-个人房点击找人玩发布（寻找合拍队友），
	// 3-旧版开黑tab，
	// 4-个人房点击找人玩发布（召唤好友），
	// 5-开黑tab发布（召唤好友）,
	// 6-开黑tab发布（优先匹配萌新），
	// 7-个人房点击找人玩发布（优先匹配萌新），
	// 8-个人房点击找人玩发布（允许同城匹配）
	publishSource := []string{"2"}
	if isWantFresh {
		publishSource = append(publishSource, "7")
	}
	if isShowGeoInfo {
		publishSource = append(publishSource, "8")
	}
	return strings.Join(publishSource, ",")
}

// 生成数据上报publishType字段
func (p *Publisher) GetPublishType(serviceInfo *grpc.ServiceInfo) string {
	publishType := userPublish
	if serviceInfo.TerminalType == 0 && serviceInfo.ClientIP == 0 {
		// 0 是脚本触发发布
		publishType = autoPublish
	}
	return publishType
}

// 生成数据上报regionId字段
func (p *Publisher) GetRegionId(ctx context.Context, blockOptions []*topic_channel.BlockOption) uint64 {
	regionId := uint64(0)
	if len(blockOptions) != 0 {
		regionId = p.covertRegionId(uint64(blockOptions[0].GetBlockId()), uint64(blockOptions[0].GetElemId()))
	}
	return regionId
}

func (p *Publisher) covertRegionId(blockId, elementId uint64) uint64 {
	return blockId<<32 + elementId
}

func (p *Publisher) CheckPublishFrequency(ctx context.Context, cid uint32) (successMsg string, err error) {
	frequency, err := p.gangupClient.GetGangupPublishCountHistory(ctx, &gangupchannelpb.GetGangupPublishCountHistoryReq{Cid: cid})
	if err != nil {
		log.WarnWithCtx(ctx, "CheckPublishFrequency channelId(%d) err(%v)", cid, err)
		return "", nil
	}
	count := int(frequency.PublishCount)
	if count == conf.ChannelPlayLogicConfig.LoadConfig().MaxCreateLimitWarning-1 {
		log.DebugWithCtx(ctx, "CreateAndReleaseTopicChannel over create limit warning channelId(%v) releaseTime(%v)", cid, count)
		successMsg = "温馨提示：频繁发布房间可能会导致发布功能受限"
	} else if count >= conf.ChannelPlayLogicConfig.LoadConfig().MaxCreateLimit-1 {
		log.InfoWithCtx(ctx, "CreateAndReleaseTopicChannel over create limit channelId(%v) hit.", cid)
		return successMsg, protocol.NewExactServerError(nil, status.ErrTopicChannelReleaseTooManyTimes)
	}
	return
}

func (p *Publisher) SearchTabByCName(ctx context.Context, uid uint32, cname string) (tabIds []uint32, err error) {
	ctx, cancel := context.WithTimeout(ctx, 500*time.Millisecond)
	defer cancel()
	resp, err := p.rcmdChannelLabelClient.SearchGameName(ctx, &rcmd_channel_label_pb.SearchGameNameReq{
		Uid:        uid,
		Text:       cname,
		SourceType: rcmd_channel_label_pb.SearchGameNameReq_TopicChannelSource,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchTabByCName uid:%d cname:%s rcmdChannelLabelClient.SearchGameName err:%v", uid, cname, err)
		return
	}
	tabIds = make([]uint32, 0, len(resp.GetGameInfoList()))
	for _, gameInfo := range resp.GetGameInfoList() {
		tabIds = append(tabIds, gameInfo.GetTabId())
	}
	log.InfoWithCtx(ctx, "SearchTabByCName uid:%d cname:%s rcmdResp:%s", uid, cname, resp.String())
	return
}

func (p *Publisher) GetMicSettingInfo(info []MicSettingInfo) string {
	if len(info) == 0 {
		return ""
	}
	res := pkg_utils.ToJson(info)
	return res
}
