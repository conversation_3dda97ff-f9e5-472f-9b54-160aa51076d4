package channel_operation

import (
	"context"
	"encoding/json"
	account "golang.52tt.com/clients/account-go"
	"golang.52tt.com/clients/channelim"
	"golang.52tt.com/pkg/log"
	channelPB "golang.52tt.com/protocol/app/channel"
	channelimPB "golang.52tt.com/protocol/services/channelim"
	tabpb "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"time"
)

type Pusher struct {
	channelImClient *channelim.Client
}

func NewPusher(channelImClient *channelim.Client) (*Pusher, error) {
	return &Pusher{
		channelImClient: channelImClient,
	}, nil
}

//房间信息修改推送
func (p *Pusher) SendChannelModifyMsg(ctx context.Context, uid, channelId uint32, tabModify map[string]interface{}) error {
	data, err := json.Marshal(tabModify)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendChangeTabMsg marshal err: %v,", err)
		return err
	}

	_, _, err = p.channelImClient.SendCommonMessage(ctx, uid, channelId, &channelimPB.ChannelCommonMsg{
		FromUid:     uid,
		Time:        uint64(time.Now().Unix()),
		ToChannelId: channelId,
		Type:        uint32(channelPB.ChannelMsgType_CHANNEL_CONFIG_MODIFY_MSG),
		Content:     string(data),
	}, true, uint32(channelPB.ChannelMsgSubType_CHANNEL_MSG_SUB_CHANGE_TOPIC_CHANNEL_TAB_ID))
	if err != nil {
		log.ErrorWithCtx(ctx, "channel im SendCommonMessage fail, err: %v", err)
		return err
	}
	return nil
}

//发布推送
func (p *Pusher) SendPublishMsgToUser(ctx context.Context, userInfo *account.User, channelId uint32, tabInfo *tabpb.Tab) (err error) {
	uid := userInfo.GetUid()
	err = p.sendPublishMsg(ctx, uid, channelId, userInfo.GetUsername(), userInfo.GetNickname(), tabInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendCreateMsgToUser sendCreateMsg uid(%v) channelId(%v) err:%+v", uid, channelId, err)
		return err
	}
	return nil
}

//发布房间推送
func (p *Pusher) sendPublishMsg(ctx context.Context, userId uint32, channelId uint32, name string, nickName string, tabInfo *tabpb.Tab) error {

	msg := &channelPB.CreateTopicChannelMsg{
		ChannelId: channelId,
		AdminMsg:  conf.ChannelPlayLogicConfig.GetPublishAdminMsg(),
		PlayerMsg: "向首页发布了“" + tabInfo.GetName() + "”房",
		UserId:    userId,
		TabId:     tabInfo.GetId(),
		TabName:   tabInfo.GetName(),
	}
	msgBin, err := msg.Marshal()
	if err != nil {
		log.ErrorWithCtx(ctx, "sendPublishMsg msg(%s) marshal err: %v,", msg.String(), err)
		return err
	}

	_, _, err = p.channelImClient.SendCommonMessage(ctx, userId, channelId, &channelimPB.ChannelCommonMsg{
		FromUid:      userId,
		FromAccount:  name,
		FromNick:     nickName,
		Time:         uint64(time.Now().Unix()),
		ToChannelId:  channelId,
		Type:         uint32(channelPB.ChannelMsgType_CHANNEL_CREATE_TOPIC_CHANNEL),
		Origin:       0,
		Content:      "",
		PbOptContent: msgBin,
	}, true, uint32(channelPB.ChannelMsgType_CHANNEL_CREATE_TOPIC_CHANNEL))
	if err != nil {
		log.ErrorWithCtx(ctx, "sendPublishMsg SendCommonMessage msg(%s) err: %v", msg.String(), err)
		return err
	}
	log.DebugWithCtx(ctx, "sendPublishMsg msg(%s)", msg.String())
	return nil
}

//取消发布推送
func (p *Pusher) SendChannelHideMsg(ctx context.Context, userId uint32, channelId uint32) error {
	content := "已取消在首页展示，小伙伴无法在首页看到你的房间"

	msg := &channelPB.HideTopicChannelMsg{
		ContentMsg: content,
	}
	data, err := msg.Marshal()
	if err != nil {
		log.ErrorWithCtx(ctx, "sendDismissMsg marshal err: %v,", err)
		return err
	}

	seq, _, err := p.channelImClient.SendCommonMessage(ctx, userId, channelId, &channelimPB.ChannelCommonMsg{
		FromUid:      userId,
		Time:         uint64(time.Now().Unix()),
		ToChannelId:  channelId,
		Type:         uint32(channelPB.ChannelMsgType_TOPIC_CHANNEL_HIDDEN),
		PbOptContent: data,
	}, true, uint32(channelPB.ChannelMsgType_TOPIC_CHANNEL_HIDDEN))
	if err != nil {
		log.ErrorWithCtx(ctx, "sendDismissMsg SendCommonMessage err: %v", err)
		return err
	}
	log.DebugWithCtx(ctx, "SendChannelHideMsg seq(%v) msg(%v) uid(%v) channelId(%v) ", seq, msg.String(), userId, channelId)
	return nil
}
