package channel_operation

import (
	"context"
	topicchannel "golang.52tt.com/clients/topic-channel/channel"
	blreport "golang.52tt.com/kaihei-pkg/blreport"
	"golang.52tt.com/kaihei-pkg/supervision"
	"golang.52tt.com/pkg/cogroup_util"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	ga "golang.52tt.com/protocol/app"
	channelPB "golang.52tt.com/protocol/app/channel"
	hobby_channel "golang.52tt.com/protocol/app/hobby-channel"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/protocol/common/status"
	topicChannelPB "golang.52tt.com/protocol/services/topic_channel/channel"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/pub_supervision"
	"golang.52tt.com/services/channel-play-logic/internal/utils"
	"strconv"
	"time"
)

type Creator struct {
	topicClient    *topicchannel.Client
	SupervisorInst *supervision.Supervisory
	PubSupervisor  *pub_supervision.PubSupervisory
}

func NewCreator(supervisorInst *supervision.Supervisory, topicClient *topicchannel.Client, pubSupervisor *pub_supervision.PubSupervisory) (*Creator, error) {
	return &Creator{
		topicClient:    topicClient,
		SupervisorInst: supervisorInst,
		PubSupervisor:  pubSupervisor,
	}, nil
}

func (c *Creator) CreateHobbyChannel(g *cogroup_util.Group, ctx context.Context, dealTabInfo *tabPB.Tab, uid, channelId uint32, cHandle *CommonHandle,
	serviceInfo *grpc.ServiceInfo, reportData *blreport.ChannelCreate, source hobby_channel.CreateChannelSource) (*tabPB.Tab, error) {
	var err error
	var resTab *tabPB.Tab
	var oldTabChange bool
	returnErrorSwitch := conf.ChannelPlayLogicConfig.GetIsChangeCreateChannelTab(source)
	schemeInfo, err := cHandle.GetSchemeInfoByCid(ctx, channelId)
	if err != nil {
		utils.ErrCntLogWithGoGroup(g, err, ctx, "CreateHobbyChannel GetSchemeInfoByCid channelId %d , err: %v", channelId, err)
		reportData.SetFilterlType(blreport.F_ID_ChannelCreate_Get_Scheme_Info)
		blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelCreateInfo(ctx, reportData)
		return resTab, err
	}
	newTabId := dealTabInfo.GetId()
	oldTabId := schemeInfo.GetSchemeId()
	log.DebugWithCtx(ctx, "CreateHobbyChannel GetSchemeInfoByCid schemeInfo(%v)", schemeInfo.String())

	_, ok := cache.GetTabInfoCache().GetTabIdCache()[oldTabId]
	if !ok {
		if oldTabId != 0 {
			log.ErrorWithCtx(ctx, "CreateHobbyChannel get default tab err: channelId(%d), oldTabId(%d)", channelId, oldTabId)
		}
		//旧玩法id不存在了，返回默认id 1
		oldTabId = 1
		oldTabChange = true
	}
	if c.SupervisorInst.MiniGameStageStrategy(dealTabInfo, serviceInfo, cache.GetWhiteList()) { //检查要创建的tab是否符合监管策略
		//需要过滤
		log.InfoWithCtx(ctx, "CreateHobbyChannel MiniGameStageStrategy err, tabId:%d, serviceInfo:%s", dealTabInfo.GetId(), serviceInfo.String())
		if returnErrorSwitch {
			return nil, protocol.NewExactServerError(nil, status.ErrTopicChannelTabNotFound)
		}
		newTabId = oldTabId
	} else if c.PubSupervisor.CheckTabRealNameState(dealTabInfo.GetId()) { //检查是否需要实名认证才能玩的tab，是的话检查用户是否实名
		isRealName, _, err := c.SupervisorInst.CheckUserRealName(ctx, uid, conf.PublicSwitchConfig.GetRealNameStandardStatus())
		if err != nil || !isRealName {
			log.InfoWithCtx(ctx, "CreateHobbyChannel realName err, tabId:%d, serviceInfo:%s", dealTabInfo.GetId(), serviceInfo.String())
			if returnErrorSwitch {
				return nil, protocol.NewExactServerError(nil, status.ErrTopicChannelOwnerNotReal)
			}
			newTabId = oldTabId
		}
	}
	// 第一次创房或则旧玩法不存在了， 或则创房玩法与旧玩法不一致，都需要切换玩法
	if oldTabChange || newTabId != oldTabId {
		err = cHandle.InitHobbyChannel(ctx, uid, channelId, dealTabInfo.GetId(), oldTabId, schemeInfo.GetSchemeDetailType())
		if err != nil {
			if returnErrorSwitch {
				return nil, err
			}
			newTabId = oldTabId
		}
		switchTime := time.Now()
		switchInfo := &topicChannelPB.SwitchChannelTabReq{
			Uid:       uid,
			Creator:   uid,
			TabId:     newTabId,
			ChannelId: channelId,
			Source:    topicChannelPB.Source_CREATE,
		}
		_, err = c.topicClient.SwitchChannelTab(ctx, switchInfo)
		if err != nil {
			utils.ErrCntLogWithGoGroup(g, err, ctx, "CreateHobbyChannel SwitchChannelTab err: %v, switchInfo:%+v", err, switchInfo)
			return resTab, err
		}
		log.InfoWithCtx(ctx, "USERLINE_COST_TIME CreateHobbyChannel SwitchChannelTab cid(%d) tabId(%d) oldTabId(%d) cost time(%d ms)",
			channelId, newTabId, oldTabId, time.Since(switchTime).Milliseconds())
	}

	resTab = cache.GetTabInfoCache().GetTabIdCache()[newTabId]
	return resTab, nil
}

type creatorReport2DatacenterParam struct {
	marketId          uint32
	channelId         uint32
	channelCreatorUid uint32
	createTime        uint32
	channelDisplayId  uint32
	modifyTime        int64
	bindType          uint32
	tabId             uint32
	tabName           string
	channelPara       string
	channelName       string
	micMode           uint32
	roomViewId        string
}

func (c *Creator) GenReportDataForDatacenter(ctx context.Context, param creatorReport2DatacenterParam) map[string]interface{} {
	reportKV := map[string]interface{}{
		"ccUid":            param.channelCreatorUid,
		"createTime":       time.Unix(int64(param.createTime), 0).Format(blreport.TimeLayout),
		"channelID":        strconv.Itoa(int(param.channelId)),
		"channelDisplayID": param.channelDisplayId,
		"channelName":      param.channelName,
		"modfyTime":        time.Unix(param.modifyTime, 0).Format(blreport.TimeLayout),
		"bindType":         strconv.Itoa(int(param.bindType)),
		"tagId":            strconv.Itoa(int(param.tabId)),
		"tagName":          param.tabName,
		"channelPara":      param.channelPara,
		"micMode":          param.micMode,
	}
	log.DebugWithCtx(ctx, "GenReportDataForDatacenter reportKV: %v", reportKV)
	return reportKV
}

func (c *Creator) GenPushChannelModifyData(ctx context.Context, tabInfo *tabPB.Tab, thirdPartyGame *topic_channel.ThirdPartyGame) (tabModify map[string]interface{}) {
	var showPublishButton bool
	if tabInfo.GetHomePageType() != tabPB.HomePageType_HomePageTypeNone {
		showPublishButton = tabInfo.GetShowPublishButton()
	}
	tabModify = map[string]interface{}{
		"sub_type":         uint32(channelPB.ChannelMsgSubType_CHANNEL_MSG_SUB_CHANGE_TOPIC_CHANNEL_TAB_ID),
		"tab_id":           tabInfo.GetId(),
		"tab_name":         tabInfo.GetName(),
		"mic_mod":          tabInfo.GetMicMod(),
		"tab_type":         tabInfo.GetTabType(),
		"tag_id":           tabInfo.GetTagId(),
		"third_party_game": thirdPartyGame,
		//是否展示发布按钮
		"show_publish_button": showPublishButton,
		//玩法所属分类的枚举标识
		"category_type": topic_channel.CategoryType(tabInfo.GetCategoryMapping()),
	}
	log.DebugWithCtx(ctx, "creator GenPushChannelModifyData data:%v", tabModify)
	return tabModify
}

func (c *Creator) GenReportDataForBylink(ctx context.Context, param creatorReport2DatacenterParam) map[string]interface{} {
	reportKV := map[string]interface{}{
		"app_type":    toAppType(param.marketId),
		"tag_name":    param.tabName,
		"room_id":     strconv.Itoa(int(param.channelId)),
		"tab_id":      int(param.tabId),
		"bind_type":   strconv.Itoa(int(param.bindType)),
		"room_own_id": strconv.Itoa(int(param.channelCreatorUid)),
		"room_para":   param.channelPara,
		"room_name":   param.channelName,
		"mic_mode":    strconv.Itoa(int(param.micMode)),
		"create_time": time.Unix(param.modifyTime, 0).Format(blreport.TimeLayout),
		//room_view_id先报旧的uint32类型的displayId, 之后如果换接口能拿到新的string类型的房间外显id要改成上报新的外显id
		"room_view_id": strconv.Itoa(int(param.channelDisplayId)),
	}
	log.DebugWithCtx(ctx, "GenReportDataForBylink reportKV: %v", reportKV)
	return reportKV
}

func toAppType(marketId uint32) string {
	switch ga.BaseReq_MarketId(marketId) {
	case ga.BaseReq_MARKET_HUANYOU:
		return "huanyou"
	case ga.BaseReq_MARKET_MAIKE:
		return "maike"
	case ga.BaseReq_MARKET_MIJING:
		return "mijing"
	case ga.BaseReq_MARKET_NONE:
		return "ttvoice"
	case ga.BaseReq_MARKET_ZAIYA:
		return "zaiya"
	default:
		return ""
	}
}
