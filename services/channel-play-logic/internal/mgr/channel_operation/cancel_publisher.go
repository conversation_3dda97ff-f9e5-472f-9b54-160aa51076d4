package channel_operation

import (
	"context"
	"golang.52tt.com/clients/channel"
	gangup_channel "golang.52tt.com/clients/gangup-channel"
	"golang.52tt.com/kaihei-pkg/blreport"
	"golang.52tt.com/pkg/log"
	gangupchannelpb "golang.52tt.com/protocol/services/gangup-channel"
	music_topic_channel "golang.52tt.com/protocol/services/music-topic-channel"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"golang.52tt.com/services/channel-play-logic/internal/utils"
)

type CancelPublisher struct {
	gangupClient            *gangup_channel.Client
	musicTopicChannelClient music_topic_channel.MusicChannelClient
	channelClient           *channel.Client
}

func NewCancelPublisher(gangupClient *gangup_channel.Client, channelClient *channel.Client,
	musicTopicChannelClient music_topic_channel.MusicChannelClient) (*CancelPublisher, error) {

	return &CancelPublisher{
		gangupClient:            gangupClient,
		musicTopicChannelClient: musicTopicChannelClient,
		channelClient:           channelClient,
	}, nil
}

func (c *CancelPublisher) CancelPublishGangupChannel(ctx context.Context, tabId, channelId uint32, cHandle *CommonHandle, reportData *blreport.ChannelCancelPublish) (bool, error) {
	//1 获取房间信息
	channelInfoResp, err := c.gangupClient.GetGangupChannelByIds(ctx, &gangupchannelpb.GetGangupChannelByIdsReq{Ids: []uint32{channelId}})
	if err != nil {
		utils.ErrCntLog(ctx, "CancelPublishGangupChannel GetGangupChannelByIds err:%v, cid:%d, tabId:%d", err, channelId, tabId)
		reportData.SetFilterlType(blreport.F_ID_ChannelCancelPublish_Get_Channel_Err)
		blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelCancelPublishInfo(ctx, reportData)
		return false, err
	}
	if len(channelInfoResp.GetInfo()) == 0 {
		log.WarnWithCtx(ctx, "CancelPublishGangupChannel GetGangupChannelByIds err:channelInfo nil, cid:%d, tabId:%d", err, channelId, tabId)
		reportData.SetFilterlType(blreport.F_ID_ChannelCancelPublish_Get_Channel_Err)
		blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelCancelPublishInfo(ctx, reportData)
		return false, nil
	}

	//不在发布中，直接return
	if !cHandle.IsDisplayChannel(channelInfoResp.GetInfo()[0].GetDisplayType()) {
		log.WarnWithCtx(ctx, "CancelPublishGangupChannel displayType err, cid:%d, tabId:%d", channelId, tabId)
		reportData.SetFilterlType(blreport.F_ID_ChannelCancelPublish_Is_Not_Publish)
		blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelCancelPublishInfo(ctx, reportData)
		return false, nil
	}

	//取消开黑房间发布
	resp, err := c.gangupClient.DismissGangupChannel(ctx, &gangupchannelpb.DismissGangupChannelReq{
		ChannelId: channelId,
		Source:    "",
		Type:      gangupchannelpb.DismissType_Cancel,
		TabId:     tabId,
	})
	if err != nil {
		utils.ErrCntLog(ctx, "CancelPublishGangupChannel DismissGangupChannel err:%v, cid(%d) tabId(%d)", err, channelId, tabId)
		reportData.SetFilterlType(blreport.F_ID_ChannelCancelPublish_Cancel_Publish_Err)
		blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelCancelPublishInfo(ctx, reportData)
		return false, err
	}

	return resp.GetDismiss(), nil
}

func (c *CancelPublisher) CancelPublishMusicTopicChannel(ctx context.Context, tabId, channelId uint32, cHandle *CommonHandle, reportData *blreport.ChannelCancelPublish) (bool, error) {
	//1 获取房间信息
	channelInfoResp, err := c.musicTopicChannelClient.GetMusicChannelByIds(ctx, &music_topic_channel.GetMusicChannelByIdsReq{Ids: []uint32{channelId}})
	if err != nil {
		utils.ErrCntLog(ctx, "CancelPublishMusicTopicChannel GetMusicChannelByIds err:%v, cid:%d, tabId:%d", err, channelId, tabId)
		reportData.SetFilterlType(blreport.F_ID_ChannelCancelPublish_Get_Channel_Err)
		blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelCancelPublishInfo(ctx, reportData)
		return false, err
	}
	if len(channelInfoResp.GetInfo()) == 0 {
		log.WarnWithCtx(ctx, "CancelPublishMusicTopicChannel GetMusicChannelByIds err:channelInfo nil, cid:%d, tabId:%d", err, channelId, tabId)
		reportData.SetFilterlType(blreport.F_ID_ChannelCancelPublish_Get_Channel_Err)
		blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelCancelPublishInfo(ctx, reportData)
		return false, nil
	}

	//不在发布中，直接return
	if !cHandle.IsMusicDisplayChannel(channelInfoResp.GetInfo()[0].GetDisplayType()) {
		log.WarnWithCtx(ctx, "CancelPublishMusicTopicChannel displayType err, cid:%d, tabId:%d", channelId, tabId)
		reportData.SetFilterlType(blreport.F_ID_ChannelCancelPublish_Is_Not_Publish)
		blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelCancelPublishInfo(ctx, reportData)
		return false, nil
	}

	//取消音乐房间发布
	resp, err := c.musicTopicChannelClient.DismissMusicChannel(ctx, &music_topic_channel.DismissMusicChannelReq{
		ChannelId: channelId,
		Source:    "",
		Type:      music_topic_channel.DismissType_Cancel,
	})
	if err != nil {
		utils.ErrCntLog(ctx, "CancelPublishMusicTopicChannel DismissGangupChannel err:%v, cid(%d) tabId(%d)", err, channelId, tabId)
		reportData.SetFilterlType(blreport.F_ID_ChannelCancelPublish_Cancel_Publish_Err)
		blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelCancelPublishInfo(ctx, reportData)
		return false, err
	}

	return resp.GetDismiss(), nil
}

func (c *CancelPublisher) CancelPublishChannel(ctx context.Context, tabId, channelId uint32, cHandle *CommonHandle, reportData *blreport.ChannelCancelPublish) (bool, error) {
	tabInfo := cache.GetTabInfoCache().GetTabIdCache()[tabId]
	// 过渡阶段，扩列两边都需要处理
	if conf.PublicSwitchConfig.IsOpenFastPcChatTabDoubleWrite() && tabInfo.GetId() == conf.PublicSwitchConfig.GetMuseChatTabId() {
		_, _ = c.CancelPublishGangupChannel(ctx, tabId, channelId, cHandle, reportData)
		return c.CancelPublishMusicTopicChannel(ctx, tabId, channelId, cHandle, reportData)
	}
	if tabInfo.GetHomePageType() == tabPB.HomePageType_HomePageTypeMUSIC {
		return c.CancelPublishMusicTopicChannel(ctx, tabId, channelId, cHandle, reportData)
	} else {
		return c.CancelPublishGangupChannel(ctx, tabId, channelId, cHandle, reportData)
	}

}
