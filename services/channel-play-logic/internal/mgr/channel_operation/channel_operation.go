package channel_operation

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic_ext"
	account "golang.52tt.com/clients/account-go"
	blreport "golang.52tt.com/kaihei-pkg/blreport"
	"golang.52tt.com/kaihei-pkg/supervision"
	"golang.52tt.com/pkg/cogroup_util"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	gaChannelPB "golang.52tt.com/protocol/app/channel"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	channel_scheme "golang.52tt.com/protocol/app/channel-scheme"
	hobby_channel "golang.52tt.com/protocol/app/hobby-channel"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/protocol/common/status"
	channelmicPB "golang.52tt.com/protocol/services/channelmicsvr"
	channelSvrPB "golang.52tt.com/protocol/services/channelsvr"
	entertainmentRecommendBack "golang.52tt.com/protocol/services/entertainmentRecommendBackSvr"
	riskMngApiPb "golang.52tt.com/protocol/services/risk-mng-api"
	channelPB "golang.52tt.com/protocol/services/topic_channel/channel"
	tabpb "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/pub_supervision"
	"golang.52tt.com/services/channel-play-logic/internal/utils"
	"google.golang.org/grpc/codes"
	"math/rand"
	"strconv"
	"time"
)

const (
	gameMicCount = 10
)

type ChannelOperationMgr struct {
	SupervisorInst      *supervision.Supervisory
	Creator             *Creator
	Publisher           *Publisher
	CancelPublisher     *CancelPublisher
	CommonHandle        *CommonHandle
	Pusher              *Pusher
	TopicChannelCfgInfo *TopicChannelCfgInfo
	PubSupervisor       *pub_supervision.PubSupervisory
}

func NewChannelOperationMgr(supervisorInst *supervision.Supervisory, creator *Creator,
	publisher *Publisher, cancelPublisher *CancelPublisher, commonHandle *CommonHandle, pusher *Pusher,
	topicChannelCfgInfo *TopicChannelCfgInfo, pubSupervisor *pub_supervision.PubSupervisory) (*ChannelOperationMgr, error) {
	return &ChannelOperationMgr{
		SupervisorInst:      supervisorInst,
		Creator:             creator,
		CancelPublisher:     cancelPublisher,
		Publisher:           publisher,
		CommonHandle:        commonHandle,
		Pusher:              pusher,
		TopicChannelCfgInfo: topicChannelCfgInfo,
		PubSupervisor:       pubSupervisor,
	}, nil
}

// 创房时不做监管控制，切玩法时不让切过去
func (c *ChannelOperationMgr) CreateHobbyChannel(ctx context.Context, in *hobby_channel.CreateHobbyChannelReq, serviceInfo *grpc.ServiceInfo) (
	out *hobby_channel.CreateHobbyChannelResp, err error) {
	out = &hobby_channel.CreateHobbyChannelResp{}
	uid := serviceInfo.UserID
	channelName := in.GetChannelName()

	tabId := in.GetTabId()
	if protocol.IsFastPcClientType(uint32(serviceInfo.ClientType)) && tabId == conf.PublicSwitchConfig.GetFastPcChatTabId() {
		tabId = conf.PublicSwitchConfig.GetMuseChatTabId()
	}

	reportData := &blreport.ChannelCreate{
		TabId:       tabId,
		UserId:      serviceInfo.UserID,
		ChannelName: channelName,
	}
	//1, 对要创建的tab进行检查
	dealTabInfo, ok := cache.GetTabInfoCache().GetTabIdCache()[tabId]
	if !ok {
		log.ErrorWithCtx(ctx, "CreateHobbyChannel tab id not found, tabId:%d, serviceInfo:%v", tabId, serviceInfo.String())
		reportData.SetFilterlType(blreport.F_ID_ChannelCreate_TabId_Is_Normal)
		blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelCreateInfo(ctx, reportData)
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelTabNotFound)
	}

	//2,获取自己的个人房信息
	var channelId uint32
	var channelInfo *channelSvrPB.ChannelDetailInfo
	var userInfo *account.User
	eg1, ctx1 := cogroup_util.WithContext(ctx)
	var tempErr error

	eg1.Go(func() error {
		channelId, channelInfo, tempErr = c.CommonHandle.GetChannelInfoByUid(ctx1, uid)
		if tempErr != nil {
			utils.ErrCntLogWithGoGroup(eg1, tempErr, ctx1, "CreateHobbyChannel GetChannelInfoByUid err:%v, cid:%d", tempErr, channelId)
			reportData.SetFilterlType(blreport.F_ID_ChannelCreate_Is_Owner)
			blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelCreateInfo(ctx1, reportData)
			return tempErr
		}
		reportData.ChannelId = channelId
		return nil
	})
	eg1.Go(func() error {
		userInfo, tempErr = c.CommonHandle.GetUserInfo(ctx1, uid)
		if tempErr != nil {
			log.ErrorWithCtx(ctx1, "CreateHobbyChannel GetUserInfo err:%v, in:%s, serviceInfo%s", tempErr, in.String(), serviceInfo.String())
			return protocol.ToServerError(tempErr)
		}
		return nil
	})
	err = eg1.Wait()
	if err != nil {
		return out, err
	}

	smDeviceId := in.GetBaseReq().GetAntispamInfo().GetSmAntispam().GetSmDeviceId()
	eg2, ctx2 := cogroup_util.WithContext(ctx)
	var resTabInfo *tabpb.Tab
	eg2.Go(func() error {
		creatorTime := time.Now()
		tempErr, channelName = c.CommonHandle.UpdateUgcChannelName(eg2, ctx2, userInfo, channelId, tabId, channelName,
			smDeviceId, serviceInfo.MarketID, uint32(serviceInfo.ClientType),
			channelInfo.GetChannelBaseinfo().GetChannelName(), true)
		if tempErr != nil {
			utils.WarnCntLog(ctx2, "CreateHobbyChannel UpdateUgcChannelName err: %v, channelId:%d, channelName:%s, newChannelName:%s",
				tempErr, channelId,
				in.GetChannelName(), channelName)
			if pe, ok := tempErr.(protocol.ServerError); ok {
				if pe.Code() == status.ErrTopicChannelNameIsNull {
					reportData.SetFilterlType(blreport.F_ID_ChannelCreate_Channel_Name_Nil)
					blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelCreateInfo(ctx2, reportData)
				} else if pe.Code() == status.ErrTopicChannelNameFormattedInvalid {
					reportData.SetFilterlType(blreport.F_ID_ChannelCreate_Channel_Name_Err)
					blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelCreateInfo(ctx2, reportData)
				} else if pe.Code() == status.ErrTopicChannelNameCanNotDiy {
					reportData.SetFilterlType(blreport.F_ID_ChannelCreate_Channel_Name_Cannot_Mod)
					blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelCreateInfo(ctx2, reportData)
				} else if pe.Code() == status.ErrTopicChannelNameSensitive {
					reportData.SetFilterlType(blreport.F_ID_ChannelCreate_Audit_Not_Pass)
					blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelCreateInfo(ctx2, reportData)
				} else {
					reportData.SetFilterlType(blreport.F_ID_ChannelCreate_Channel_Name_Mod_Err)
					blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelCreateInfo(ctx2, reportData)
				}
			}
			return tempErr
		}
		log.InfoWithCtx(ctx2, "USERLINE_COST_TIME UpdateUgcChannelName cid(%d) cost time(%d ms)", channelId, time.Since(creatorTime).Milliseconds())
		return nil
	})

	eg2.Go(func() error {
		//5, 创房逻辑
		creatorTime := time.Now()

		resTabInfo, tempErr = c.Creator.CreateHobbyChannel(eg2, ctx2, dealTabInfo, uid, channelId, c.CommonHandle, serviceInfo, reportData, in.GetCreateSource())
		if tempErr != nil {
			reportData.SetFilterlType(blreport.F_ID_ChannelCreate_Switch_Err)
			blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelCreateInfo(ctx2, reportData)
			return protocol.ToServerError(tempErr)
		}
		log.InfoWithCtx(ctx2, "USERLINE_COST_TIME CreateHobbyChannel cid(%d) cost time(%d ms)", channelId, time.Since(creatorTime).Milliseconds())
		return nil
	})
	err = eg2.Wait()
	if err != nil {
		return out, err
	}

	go func() {
		//6, 客户端推送
		var thirdPartyGame *topic_channel.ThirdPartyGame
		if otherGameConf, ok := conf.ThirdPartyGameConf.GetThirdPartyConfMap()[resTabInfo.GetId()]; ok {
			thirdPartyGame = otherGameConf.ConvertOtherGameConf()
			log.DebugWithCtx(ctx, "CreateHobbyChannel ThirdPartyGame (%+v)", thirdPartyGame)
		}
		ctx3, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()
		pushData := c.Creator.GenPushChannelModifyData(ctx3, resTabInfo, thirdPartyGame)
		pushErr := c.Pusher.SendChannelModifyMsg(ctx3, uid, channelId, pushData)
		if pushErr != nil {
			log.ErrorWithCtx(ctx3, "CreateHobbyChannel SendChannelModifyMsg, channelId:%d, tabId:%d, err:%v",
				channelId, resTabInfo.GetId(), pushErr)
		}
	}()

	// 填充房间信息
	out.ChannelId = channelId

	//7，数据上报
	param := creatorReport2DatacenterParam{
		marketId:          serviceInfo.MarketID,
		channelId:         channelId,
		channelCreatorUid: channelInfo.GetCreaterUid(),
		createTime:        channelInfo.GetCreateTs(),
		channelDisplayId:  channelInfo.GetChannelBaseinfo().GetDisplayId(),
		modifyTime:        time.Now().Unix(),
		bindType:          channelInfo.GetChannelBindType(),
		tabId:             resTabInfo.GetId(),
		tabName:           resTabInfo.GetName(),
		channelPara:       "",
		channelName:       channelName,
		micMode:           resTabInfo.GetMicMod(),
		roomViewId:        "", //暂时先不报，后续等伟贤那边业务改造后再看用哪个接口获取房间信息，目前调用GetUserChannelRoleList没有带上外显id
	}
	reportKV := c.Creator.GenReportDataForDatacenter(ctx, param)
	blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).Report2Datacenter(ctx, blreport.BizCreate, reportKV)

	bylinkKV := c.Creator.GenReportDataForBylink(ctx, param)
	blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).Report2bylink(ctx, uint64(uid), blreport.BylinkCreateHobbyChannel, bylinkKV, true)

	reportData.SetFilterlType(blreport.F_ID_ChannelCreate_Create_Suc)
	blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelCreateInfo(ctx, reportData)
	return out, nil
}

func (c *ChannelOperationMgr) CheckChannelName(ctx context.Context, uid, tabId uint32, channelName string, tabInfos map[uint32]*tabpb.Tab,
	filterCategoryIds []uint32) ([]*topic_channel.ShowTopicChannelTabSecondaryItem, bool) {
	now := time.Now()
	defer func() {
		log.DebugWithCtx(ctx, "CheckChannelName cost:%d", time.Since(now).Milliseconds())
	}()
	if channelName == "" {
		return nil, false
	}
	if tabInfos == nil {
		return nil, false
	}
	tabInfo := tabInfos[tabId]
	if tabInfo == nil || utils.Uint32IndexOf(filterCategoryIds, tabInfo.GetCategoryId()) == -1 {
		log.DebugWithCtx(ctx, "no need check categoryIds, tabId:%d, filterCategoryIds:%d", tabId, filterCategoryIds)
		return nil, false
	}
	// 切割房间名
	//cuttingArr := checkChannelNameWordCut(channelName)
	//if cuttingArr == nil {
	//	return nil, false
	//}
	res := make([]*topic_channel.ShowTopicChannelTabSecondaryItem, 0, 3)
	// 跟当前用户的选择重合忽略
	tabIds, err := c.Publisher.SearchTabByCName(ctx, uid, channelName)
	if err != nil {
		log.ErrorWithCtx(ctx, "publishGangupChannel CheckChannelName uid:%d tabId:%d cname:%s err:%v", uid, tabId, channelName, err)
		return nil, false
	}
	for _, v := range tabIds {
		info := tabInfos[v]
		if info == nil || utils.Uint32IndexOf(filterCategoryIds, info.GetCategoryId()) == -1 || info.GetId() == tabInfo.GetId() {
			continue
		}
		item := convertTabSecondaryItem(info)
		if item.GetHasList() {
			continue
		}
		res = append(res, item)
		if len(res) >= 3 {
			break
		}
	}

	if len(res) > 0 {
		log.InfoWithCtx(ctx, "CheckChannelName uid:%d len(res):%d", uid, len(res))
		return res, true
	}
	return nil, false
}

// 检查发布频率
func (c *ChannelOperationMgr) CheckFrequency(ctx context.Context, cid uint32) (successMsg string, err error) {
	successMsg, err = c.Publisher.CheckPublishFrequency(ctx, cid)
	return
}

// 检查发布block配置,配置了单选的block,不能选多个，选N项的不能超过N项
func (c *ChannelOperationMgr) checkBlockSetting(clientType, tabId uint32, blockOption []*topic_channel.BlockOption, noLimitBlockIds []uint32) error {
	if conf.ChannelPlayLogicConfig.GetNoCheckBlockSetting() {
		//不检查配置
		return nil
	}
	blockInfos := cache.GetBaseBlocksByTabId(tabId, clientType)
	if blockInfos == nil {
		return nil
	}

	removeRepeatedElemMap := make(map[uint32]bool, len(blockOption))
	blockSelectedNumMap := make(map[uint32]uint32, len(blockInfos))
	for _, opt := range blockOption {
		if removeRepeatedElemMap[opt.GetElemId()] {
			continue
		}
		removeRepeatedElemMap[opt.GetElemId()] = true
		blockSelectedNumMap[opt.GetBlockId()]++
	}
	noLimitBlockIdMap := make(map[uint32]bool, len(noLimitBlockIds))
	for _, bid := range noLimitBlockIds {
		noLimitBlockIdMap[bid] = true
	}
	for _, block := range blockInfos {
		if v, ok := blockSelectedNumMap[block.GetId()]; ok {
			//客户端发布面板6.42版本前由于推荐发布项面板的原因，存在block配置为N项但是传参超过N项的情况，6.42版本进行优化，因此选N项的校验要过几个迭代后才能上
			if block.GetMostSelectNum() > 0 && (block.GetMostSelectNum() < v && !noLimitBlockIdMap[block.GetId()]) { //最多选N项，选择大于N项且不是选不限
				return protocol.NewExactServerError(nil, status.ErrTopicChannelBlockSettingNeedRefresh)
			} else if block.GetMostSelectNum() == 0 && block.GetMode() == tabpb.Block_SINGLE && v > 1 { //单选选了大于1个
				return protocol.NewExactServerError(nil, status.ErrTopicChannelBlockSettingNeedRefresh)
			}
		}
	}
	return nil
}

// 房间名是否为空、tab是否存在、tab是否具有发布能力、发布block配置校验、是否房主
func (c *ChannelOperationMgr) publishPreCheck(ctx context.Context, in *channel_play.PublishGangupChannelReq, tabInfo *tabpb.Tab,
	serviceInfo *grpc.ServiceInfo, reportData *blreport.ChannelPublish) (channelId uint32, oldChannelName string, err error) {
	// 发布房间，房间名不能为空
	if in.GetChannelName() == "" {
		log.WarnWithCtx(ctx, "PublishGangupChannel GetName err:name nil,  in(%s)", in.String())

		reportData.SetFilterlType(blreport.F_ID_ChannelPublish_Channel_Name_Nil)
		blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelPublishInfo(ctx, reportData)
		return 0, "", protocol.NewExactServerError(nil, status.ErrTopicChannelNameIsNull)
	}
	//1 tab检查
	if tabInfo == nil {
		errMsg := fmt.Sprintf("PublishGangupChannel TabIdCache err: tabId not found, serviceInfo:%v, in:%s", serviceInfo.String(), in.String())
		utils.ErrCntLog(ctx, errMsg)
		//monkey_sender.GetQueueMsgSender().SendMsg("开黑发布", errMsg)

		reportData.SetFilterlType(blreport.F_ID_ChannelPublish_TabId_Is_Normal)
		blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelPublishInfo(ctx, reportData)
		err = protocol.NewExactServerError(nil, status.ErrTopicChannelSelectTabBeforePublish)
		return
	}

	//过滤没有发布能力的tab
	if tabInfo.GetHomePageType() == tabpb.HomePageType_HomePageTypeNone {
		errMsg := fmt.Sprintf("PublishGangupChannel err:can not publish, tabId:%d, serviceInfo:%v", tabInfo.GetId(), serviceInfo.String())
		utils.ErrCntLog(ctx, errMsg)
		//monkey_sender.GetQueueMsgSender().SendMsg("开黑发布", errMsg)

		reportData.SetFilterlType(blreport.F_ID_ChannelPublish_TabId_Have_Publish)
		blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelPublishInfo(ctx, reportData)
		err = protocol.NewExactServerError(nil, status.ErrTopicChannelCanNotPublish)
		return
	}

	// 传参block配置校验
	err = c.checkBlockSetting(uint32(serviceInfo.ClientType), tabInfo.GetId(), in.GetBlockOptions(), in.GetAllSelectedBids())
	if err != nil {
		log.WarnWithCtx(ctx, "PublishGangupChannel checkBlockSetting in:%s, serviceInfo:%s, err:%v", in.String(), serviceInfo.String(), err)
		return
	}
	// 房主身份校验
	var detailChannelInfo *channelSvrPB.ChannelDetailInfo
	uid := serviceInfo.UserID
	//判断是否房主
	if in.GetChannelId() == 0 {
		channelId, detailChannelInfo, err = c.CommonHandle.GetChannelInfoByUid(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "PublishGangupChannel GetChannelInfoByUid err:%v, cid:%d", err, channelId)
			reportData.SetFilterlType(blreport.F_ID_ChannelPublish_Is_Owner)
			blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelPublishInfo(ctx, reportData)
			return
		}
		oldChannelName = detailChannelInfo.GetChannelBaseinfo().GetChannelName()
	} else {
		channelId = in.GetChannelId()
		oldChannelName, err = c.CommonHandle.IsChannelOwner(ctx, uid, channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "PublishGangupChannel IsChannelOwner err:%v, cid:%d", err, channelId)
			return
		}
	}
	return
}

func (c *ChannelOperationMgr) PublishGangupChannel(ctx context.Context, in *channel_play.PublishGangupChannelReq, serviceInfo *grpc.ServiceInfo) (
	out *channel_play.PublishGangupChannelResp, err error) {
	out = &channel_play.PublishGangupChannelResp{
		BaseResp: &app.BaseResp{},
	}
	uid := serviceInfo.UserID
	//1 tab检查
	tabInfos := cache.GetTabInfoCache().GetTabIdCache()
	tabInfo := tabInfos[in.GetTabId()]
	if tabInfo == nil {
		errMsg := fmt.Sprintf("PublishGangupChannel TabIdCache err: tabId not found, serviceInfo:%v, in:%s", serviceInfo.String(), in.String())
		utils.ErrCntLog(ctx, errMsg)
		return out, protocol.NewExactServerError(nil, status.ErrBadRequest)
	}

	reportData := &blreport.ChannelPublish{
		TabId:       tabInfo.GetId(),
		UserId:      serviceInfo.UserID,
		ChannelId:   in.GetChannelId(),
		ChannelName: in.GetChannelName(),
		IsChange:    "0",
		PublishTime: time.Now().Format("2006-01-02 15:04:05"),
	}

	//5 房间名推荐校验
	if in.GetNeedCheckChannelName() {
		filterCategoryIds := conf.ChannelPlayLogicConfig.GetPublishCheckNameCategoryIds()
		if data, check := c.CheckChannelName(ctx, uid, tabInfo.GetId(), in.GetChannelName(), tabInfos, filterCategoryIds); check {
			log.DebugWithCtx(ctx, "checkChannelName has rec, req:%+v, data:%+v", in, data)
			out.SecondaryItem = data

			reportData.SetFilterlType(blreport.F_ID_ChannelPublish_Channel_Name_Standar)
			blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelPublishInfo(ctx, reportData)
			return out, nil
		}
	}

	channelId, oldChannelName, err := c.publishPreCheck(ctx, in, tabInfo, serviceInfo, reportData)
	if err != nil {
		return out, err
	}

	reportData.ChannelId = channelId
	in.ChannelId = channelId
	channelName := in.GetChannelName()
	//获取用户信息
	userInfo, err := c.CommonHandle.GetUserInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "PublishGangupChannel GetUserInfo err:%v, in:%s, serviceInfo%s", err, in.String(), serviceInfo.String())
		return out, err
	}
	smDeviceId := in.GetBaseReq().GetAntispamInfo().GetSmAntispam().GetSmDeviceId()
	passUserCheck, passChannelCheck, sucMsg, publishTime, isPublish, errInfo, TRequestId, err := c.checkPublishCondition(ctx, serviceInfo, userInfo, tabInfo.GetId(),
		channelId, smDeviceId, channelName, oldChannelName, reportData, in.GetBaseReq())
	if err != nil {
		rpcErr, ok := err.(protocol.ServerError)
		if ok {
			log.WarnWithCtx(ctx, "checkPublishCondition err:%v, in(%s), serviceInfo(%v)", rpcErr, in.String(), serviceInfo.String())
		} else {
			log.ErrorWithCtx(ctx, "checkPublishCondition err:%v, in(%s), serviceInfo(%v)", err, in.String(), serviceInfo.String())
		}
		if errInfo != nil {
			out.BaseResp.ErrInfo = errInfo
		}
		return out, err
	} else {
		reportData.PublishTime = time.Unix(publishTime, 0).Format("2006-01-02 15:04:05")
	}
	if sucMsg != "" {
		out.BaseResp.SuccessMsg = sucMsg
	}

	if !passChannelCheck || !passUserCheck {
		log.InfoWithCtx(ctx, "PublishGangupChannel DoTabFilter no pass, cid:%d, channelCheck:%v ,"+
			" userCheck:%v, in:%s serviceInfo:%s", channelId, passChannelCheck, passUserCheck, in.String(), serviceInfo.String())
		return out, protocol.NewExactServerError(nil, status.ErrChannelNoPermission, "无发布房间权限")
	}

	// 检查用户输入值范围
	in.BlockOptions = handleUserInputBlockOption(in.GetBlockOptions(), cache.GetBaseBlocksByTabId(tabInfo.GetId(), uint32(serviceInfo.ClientType)))

	//5 房间信息处理（发布状态，冷却， 房间名）
	autoDismissDuration, err := c.Publisher.PublishChannel(ctx, in, serviceInfo, channelName, publishTime, isPublish, c.CommonHandle, reportData, TRequestId, tabInfo)
	if err != nil {
		rpcErr, ok := err.(protocol.ServerError)
		if ok {
			log.WarnWithCtx(ctx, "PublishGangupChannel err:%v, in(%s), serviceInfo(%v)", rpcErr, in.String(), serviceInfo.String())
		} else {
			log.ErrorWithCtx(ctx, "PublishGangupChannel err:%v, in(%s), serviceInfo(%v)", err, in.String(), serviceInfo.String())
		}
		return out, err
	}
	if isPublish {
		reportData.IsChange = "1"
	}
	go func() {
		ctx2, cancel := grpc.NewContextWithInfoTimeout(ctx, 2*time.Second)
		defer cancel()
		//6 客户端推送
		teamDesc, fastPcPublishDesc := c.CommonHandle.GetPublishInfoDesc(ctx2, tabInfo.GetId(), uint32(serviceInfo.ClientType), in.GetBlockOptions())
		fastPcCfgTabInfo := &channel_play.FastPcCfgTabInfo{FastPcPublishDesc: fastPcPublishDesc}

		pushData := c.Publisher.GenPushChannelModifyData(ctx2, uint32(serviceInfo.ClientType), teamDesc, fastPcCfgTabInfo, tabInfo)
		pushErr := c.Pusher.SendChannelModifyMsg(ctx2, uid, channelId, pushData)
		if pushErr != nil {
			log.ErrorWithCtx(ctx2, "publishGangupChannel SendChannelModifyMsg err:%v, cid:%d, tabId:%d, serviceInfo:%v",
				pushErr, channelId, tabInfo.GetId(), serviceInfo.String())
		}
		//7 发送切换消息
		createMsgErr := c.Pusher.SendPublishMsgToUser(ctx2, userInfo, channelId, tabInfo)
		if createMsgErr != nil {
			log.ErrorWithCtx(ctx2, "publishGangupChannel SendCreateMsgToUser err:%v, cid:%d, tabId:%d,"+
				" serviceInfo:%v", createMsgErr, channelId, tabInfo.GetId(), serviceInfo.String())
		}
	}()

	// 处理发布Block按钮操作
	micSettingInfo, _ := c.handleBlockButtonOpt(ctx, channelId, tabInfo.GetId(), in.GetBlockButtonOpts())

	//8 数据上报
	//上报数据中心
	curTime := time.Now().Unix()
	param := &publisherReportParam{
		totalDate:         curTime,
		channelId:         channelId,
		channelCreatorUid: uid,
		publishTime:       publishTime,
		tabId:             tabInfo.GetId(),
		tabName:           tabInfo.GetName(),
		channelPara:       c.Publisher.GetChannelParam(ctx, in.GetBlockOptions(), in.GetAllSelectedBids(), tabInfo.GetId(), uint32(serviceInfo.ClientType)),
		//channelName:       in.GetChannelName(),
		channelName:    channelName,
		publishSource:  c.Publisher.GetPublishSource(in.GetIsWantFresh(), in.GetIsShowGeoInfo()),
		regionId:       c.Publisher.GetRegionId(ctx, in.GetBlockOptions()),
		publishType:    c.Publisher.GetPublishType(serviceInfo),
		micMode:        tabInfo.GetMicMod(),
		micSettingInfo: c.Publisher.GetMicSettingInfo(micSettingInfo),
	}
	reportKV := c.Publisher.GenReportDataForDatacenter(ctx, param)
	blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).Report2Datacenter(ctx, blreport.BizNewCreate, reportKV)
	//上报百灵
	bylinkKV := c.Publisher.GenReportDataForBylink(ctx, param)
	blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).Report2bylink(ctx, uint64(uid), blreport.BylinkNewCreate, bylinkKV, true)

	out.ChangeCoolDown = conf.ChannelPlayLogicConfig.LoadConfig().ChangeCoolDown
	out.FreezeDuration = conf.ChannelPlayLogicConfig.LoadConfig().FreezeDuration
	out.AutoDismissDuration = autoDismissDuration

	reportData.SetFilterlType(blreport.F_ID_ChannelPublish_Publish_Suc)
	blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelPublishInfo(ctx, reportData)
	return out, nil
}

func (c *ChannelOperationMgr) checkPublishCondition(ctx context.Context, serviceInfo *grpc.ServiceInfo, userInfo *account.User,
	tabId, channelId uint32, smDeviceId, channelName, oldChannelName string, reportData *blreport.ChannelPublish, baseReq *app.BaseReq) (
	passUserCheck, passChannelCheck bool, sucMsg string, publishTime int64, isPublish bool, errInfo []byte, requestId string, err error) {

	uid := serviceInfo.UserID
	marketId := serviceInfo.MarketID
	clientType := uint32(serviceInfo.ClientType)
	var checkResp *riskMngApiPb.CheckResp
	eg, ctx := cogroup_util.WithContext(ctx)

	eg.Go(func() error {
		var tempErr error
		//异常检测
		if tempErr = c.Publisher.CheckAndSwitchTab(ctx, channelId, uid, tabId, c.CommonHandle); tempErr != nil {
			utils.ErrCntLogWithGoGroup(eg, tempErr, ctx, "PublishGangupChannel CheckAndSwitchTab err:%v, channelId:%d,"+
				" tabId:%d", tempErr, channelId, tabId)
			reportData.SetFilterlType(blreport.F_ID_ChannelPublish_Tab_Switch)
			blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelPublishInfo(ctx, reportData)
			return tempErr
		}
		// 极速PC的暂时不检查用户等级
		if protocol.IsFastPcClientType(clientType) {
			passUserCheck = true

		} else {
			//用户等级检查
			passUserCheck, tempErr = c.PubSupervisor.UserFilterManager.DoFilter(ctx, uid, marketId, reportData)
			if tempErr != nil || !passUserCheck {
				if tempErr != nil {
					utils.ErrCntLogWithGoGroup(eg, tempErr, ctx, "PublishGangupChannel UserFilterManager.DoTabFilter err:%v,"+
						" cid:%d, serviceInfo:%s", tempErr, channelId, serviceInfo.String())
				} else if !passUserCheck {
					log.InfoWithCtx(ctx, "PublishGangupChannel UserFilterManager.DoTabFilter no pass, cid:%d, serviceInfo:%s",
						channelId, serviceInfo.String())
				}
				return tempErr
			}
		}

		//房间冻结检查
		passChannelCheck, tempErr = c.PubSupervisor.PubChannelFilterManager.DoFilter(ctx, uid, channelId, reportData)
		if tempErr != nil || !passChannelCheck {
			if tempErr != nil {
				te, ok := tempErr.(protocol.ServerError)
				if ok && te.Code() == status.ErrTopicChannelFreeze {
					log.InfoWithCtx(ctx, "PublishGangupChannel PubChannelFilterManager.DoTabFilter no pass, cid:%d, "+
						"serviceInfo:%s", channelId, serviceInfo.String())
				} else {
					utils.ErrCntLogWithGoGroup(eg, tempErr, ctx, "PublishGangupChannel PubChannelFilterManager.DoTabFilter err:%v,"+
						" cid:%d, serviceInfo:%s", tempErr, channelId, serviceInfo.String())
				}
			} else if !passChannelCheck {
				log.InfoWithCtx(ctx, "PublishGangupChannel PubChannelFilterManager.DoTabFilter no pass, cid:%d, serviceInfo:%s",
					channelId, serviceInfo.String())
			}
			return tempErr
		}
		//发布频次检查
		if sucMsg, tempErr = c.CheckFrequency(ctx, channelId); tempErr != nil {
			utils.ErrCntLogWithGoGroup(eg, tempErr, ctx, "PublishGangupChannel CheckFrequency err:%v, serviceInfo:%+v", tempErr, serviceInfo.String())
			reportData.SetFilterlType(blreport.F_ID_ChannelPublish_Channel_Freequency)
			blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelPublishInfo(ctx, reportData)
			return tempErr
		}
		//2 获取发布状态
		isPublish, publishTime, tempErr = c.Publisher.IsChangeInPublish(ctx, channelId, c.CommonHandle)
		if tempErr != nil {
			utils.ErrCntLogWithGoGroup(eg, tempErr, ctx, "PublishHobbyChannel GetChannelByIds err:%v, channelId:%d", err, channelId)
			return tempErr
		}
		if isPublish {
			reportData.IsChange = "1"
			//isModify = true
		}
		//3 检查发布冷却
		tempErr = c.Publisher.checkCoolStatus(eg, ctx, channelId, isPublish)
		if tempErr != nil {
			reportData.SetFilterlType(blreport.F_ID_ChannelPublish_Channel_CoolDown)
			blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelPublishInfo(ctx, reportData)
			return tempErr
		}
		return nil
	})
	eg.Go(func() error {
		var tempErr error
		tempErr, channelName, requestId = c.CommonHandle.CheckChannelName(eg, ctx, userInfo, channelId, tabId, channelName, smDeviceId,
			serviceInfo.MarketID, uint32(serviceInfo.ClientType), oldChannelName, false, false)
		if tempErr != nil {
			utils.ErrCntLogWithGoGroup(eg, tempErr, ctx, "PublishGangupChannel CheckChannelName err: %v, channelId:%d, oldChannelName:%s, newChannelName:%s",
				tempErr, channelId, oldChannelName, channelName)
			if pe, ok := tempErr.(protocol.ServerError); ok {
				if pe.Code() == status.ErrTopicChannelNameIsNull {
					reportData.SetFilterlType(blreport.F_ID_ChannelPublish_Channel_Name_Nil)
					blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelPublishInfo(ctx, reportData)
				} else if pe.Code() == status.ErrTopicChannelNameFormattedInvalid {
					reportData.SetFilterlType(blreport.F_ID_ChannelPublish_Channel_Name_Err)
					blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelPublishInfo(ctx, reportData)
				} else if pe.Code() == status.ErrTopicChannelNameCanNotDiy {
					reportData.SetFilterlType(blreport.F_ID_ChannelPublish_Channel_Name_Cannot_Mod)
					blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelPublishInfo(ctx, reportData)
				} else if pe.Code() == status.ErrTopicChannelNameSensitive {
					reportData.SetFilterlType(blreport.F_ID_ChannelPublish_Audit_Not_Pass)
					blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelPublishInfo(ctx, reportData)
				} else {
					reportData.SetFilterlType(blreport.F_ID_ChannelPublish_Channel_Name_Mod_Err)
					blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelPublishInfo(ctx, reportData)
				}
			}
			return tempErr
		}
		return nil
	})
	//统一风控检查，包括黑产，用户/房间封禁，发布失败，人脸认证，实名认证等策略
	eg.Go(func() error {
		var tempErr error
		checkResp, tempErr = c.CommonHandle.RiskMngCheckChannelHelper(ctx, &riskMngApiPb.CheckReq{
			Scene: "PUBLISH_GANGUP_CHANNEL",
			SourceEntity: &riskMngApiPb.Entity{
				Uid:       uid,
				ChannelId: channelId,
				Phone:     userInfo.GetPhone(),
			},
			ChannelOpt: &riskMngApiPb.ChannelOpt{
				ChannelType: uint32(gaChannelPB.ChannelType_USER_CHANNEL_TYPE),
				ChannelName: channelName,
			},
		}, baseReq)
		if tempErr != nil {
			//风控接口降级处理
			utils.ErrCntLogWithGoGroup(eg, tempErr, ctx, "checkPublishCondition uid:%d, cid:%d, cname:%s RiskMngCheck fail err:%v",
				uid, channelId, channelName, tempErr)
		}
		return nil
	})
	//处理检查错误
	err = eg.Wait()

	if err != nil {
		log.DebugWithCtx(ctx, "checkPublishCondition passUserCheck %v passChannelCheck %v sucMsg %s publishTime %d, isPublish %v err %v",
			passUserCheck, passChannelCheck, sucMsg, publishTime, isPublish, err)
		return
	} else {
		log.DebugWithCtx(ctx, "checkPublishCondition passUserCheck %v passChannelCheck %v sucMsg %s publishTime %d, isPublish %v",
			passUserCheck, passChannelCheck, sucMsg, publishTime, isPublish)
	}
	//优先判断房间名审核等是否通过，最后再判断风控，https://www.tapd.cn/32571206/prong/stories/view/1132571206001103221
	if checkResp.GetErrCode() < 0 {
		err = protocol.NewExactServerError(codes.OK, int(checkResp.GetErrCode()), checkResp.GetErrMsg())
		errInfo = checkResp.GetErrInfo()
		log.InfoWithCtx(ctx, "checkPublishCondition uid:%d cid:%d RiskMngCheck no pass checkResp:%s", uid, channelId, checkResp.String())
		reportData.SetFilterlType(blreport.F_ID_ChannelPublish_Risk_Mng_Check_Not_Pass)
		blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelPublishInfo(ctx, reportData)
	}
	return
}

func (c *ChannelOperationMgr) CancelGangupChannelPublish(ctx context.Context, in *channel_play.CancelGangupChannelPublishReq,
	serviceInfo *grpc.ServiceInfo) (out *channel_play.CancelGangupChannelPublishResp, err error) {
	out = &channel_play.CancelGangupChannelPublishResp{}
	uid := serviceInfo.UserID

	channelId := in.GetChannelId()
	reportData := &blreport.ChannelCancelPublish{
		TabId:     in.GetTabId(),
		UserId:    serviceInfo.UserID,
		ChannelId: channelId,
	}

	if channelId != 0 {
		_, err = c.CommonHandle.IsChannelOwner(ctx, uid, channelId)
		if err != nil {
			reportData.SetFilterlType(blreport.F_ID_ChannelCancelPublish_Is_Owner)
			blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelCancelPublishInfo(ctx, reportData)
			return out, err
		}
	} else {
		channelId, _, err = c.CommonHandle.GetChannelInfoByUid(ctx, uid)
		if err != nil {
			//utils.ErrCntLog(ctx, "CancelGangupChannelPublish GetChannelInfoByUid err:%v, cid:%d", err, channelId)
			reportData.SetFilterlType(blreport.F_ID_ChannelCancelPublish_Is_Owner)
			blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelCancelPublishInfo(ctx, reportData)
			return out, err
		}
	}

	isDismiss, err := c.CancelPublisher.CancelPublishChannel(ctx, in.GetTabId(), channelId, c.CommonHandle, reportData)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelGangupChannelPublish CancelPublishGangupChannel err:%v, tabId(%d) channelId(%d) serviceInfo(%v)",
			in.GetTabId(), channelId, serviceInfo.String())
		return out, err
	}

	if isDismiss {
		pushErr := c.Pusher.SendChannelHideMsg(ctx, uid, channelId)
		if pushErr != nil {
			log.ErrorWithCtx(ctx, "CancelGangupChannelPublish tabId(%d) SendChannelHideMsg channelId(%d) serviceInfo(%v) err(%v)",
				in.GetTabId(), channelId, serviceInfo.String(), pushErr)
		}
	}

	reportData.SetFilterlType(blreport.F_ID_ChannelCancelPublish_Cancel_Publish_Suc)
	blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelCancelPublishInfo(ctx, reportData)
	return out, nil
}

func (c *ChannelOperationMgr) GetTopicChannelCfgInfo(ctx context.Context, userId uint32, serviceInfo *grpc.ServiceInfo, in *channel_play.GetTopicChannelCfgInfoReq) (
	out *channel_play.GetTopicChannelCfgInfoResp, err error) {
	out = &channel_play.GetTopicChannelCfgInfoResp{
		ChannelId: in.GetChannelId(),
	}
	if in.GetChannelId() == 0 {
		return out, err
	}

	var (
		otherTabId   = conf.ChannelPlayLogicConfig.LoadConfig().OtherGameTab
		chatTabId    = conf.PublicSwitchConfig.GetChatTabId()
		defaultTabId = conf.PublicSwitchConfig.GetDefaultTab()
		tabInfo      *tabpb.Tab
		topicResp    *channelPB.GetChannelByIdsResp
	)

	//非主题房，返回推荐房tabname,新版本
	if in.GetChannelType() == uint32(gaChannelPB.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
		prepareChannelResp, err1 := c.TopicChannelCfgInfo.entertainmentRecommendClient.GetChannelTag(ctx, userId,
			&entertainmentRecommendBack.GetChannelTagReq{ChannelId: &in.ChannelId})
		if err1 != nil {
			log.ErrorWithCtx(ctx, "GetTopicChannelInfo GetChannelTag channelId(%v) userId(%v) err(%v)", in.ChannelId, userId, err1)
			return out, err1
		}
		if prepareChannelResp.GetTagInfo() != nil {
			out.TabName = prepareChannelResp.GetTagInfo().GetName()
			log.InfoWithCtx(ctx, "GetTopicChannelCfgInfo in:%s out:%s", in.String(), out.String())
			return out, err
		}
	}

	// 只处理ugc房和临时开黑房
	if in.GetChannelType() != uint32(gaChannelPB.ChannelType_USER_CHANNEL_TYPE) && in.ChannelType != uint32(gaChannelPB.ChannelType_TEMP_KH_CHANNEL_TYPE) {
		return out, err
	}
	topicResp, err = c.TopicChannelCfgInfo.topicChannelClient.GetChannelByIds(ctx, &channelPB.GetChannelByIdsReq{
		Ids: []uint32{in.ChannelId}, ReturnAll: true, Source: "channel-play-logic/GetTopicChannelCfgInfo"})

	if err != nil {
		log.ErrorWithCtx(ctx, "GetTopicChannelInfo GetChannelByIds fail, uid:%d, channelId:%d, err:%v", userId, in.ChannelId, err)
		return out, err
	}
	// 新版本看老版本
	if len(topicResp.GetInfo()) == 0 {
		//pc端有可能未选玩法创房
		var roomMod *channelmicPB.GetChannelMicModeResp
		switchPlayInfo := &topic_channel.SwitchPlayInfo{}
		roomMod, err = c.TopicChannelCfgInfo.channelMicClient.GetChannelMicMode(ctx, userId, &channelmicPB.GetChannelMicModeReq{ChannelId: in.GetChannelId()})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetTopicChannelInfo GetChannelMicMode uid(%v) channelId(%v) err(%v)", userId, in.ChannelId, err)
			return out, err
		}
		if roomMod.MicMode == uint32(gaChannelPB.EChannelMicMode_HQ_KH_MIC_SPACE_MODE) {
			tabInfo = cache.GetTabInfoCache().GetTabInfoCacheById(otherTabId)

		} else if roomMod.MicMode == uint32(gaChannelPB.EChannelMicMode_FUN_MIC_SPACE_MODE) {
			tabInfo = cache.GetTabInfoCache().GetTabInfoCacheById(chatTabId)
		}

		if tabInfo != nil {
			//是否展示发布按钮,首页配置未未分类，也不展示发布按钮
			if tabInfo.GetHomePageType() == tabpb.HomePageType_HomePageTypeNone {
				out.ShowPublishButton = false
			} else {
				out.ShowPublishButton = tabInfo.GetShowPublishButton()
			}
			out.CategoryType = tabInfo.GetCategoryMapping() //分类标识
			out.TabName = tabInfo.GetName()
			out.TabId = tabInfo.GetId()
			out.TagId = tabInfo.GetTagId()
			out.MiniGameId = tabInfo.GetMiniGameId()
			out.GameCardId = tabInfo.GetGameInfo().GetGameCardId()
			switchPlayInfo.RoomModel = gaChannelPB.EChannelMicMode(tabInfo.GetMicMod())
			out.TabType = channel_play.GetTopicChannelCfgInfoResp_TabType(topic_channel.GetTopicChannelInfoResp_NORMAL) // 普通分类
			welcomeText := c.getWelcomeText(ctx, tabInfo.GetNewcomerWelcome(), in.GetChannelId(), tabInfo.GetCategoryMapping())
			out.WelcomeText = welcomeText
			// 如果是小游戏，需要带上指定游戏可跳转第三方游戏的配置信息(下载游戏功能，菲菲v-5.2.1)
			if otherGameConf, ok := conf.ThirdPartyGameConf.GetThirdPartyConfMap()[out.TabId]; ok {
				out.ThirdPartyGame = otherGameConf.ConvertOtherGameConf()
				log.DebugWithCtx(ctx, "GetTopicChannelInfo uid(%v) ThirdPartyGame(%v)", userId, out.ThirdPartyGame)
			}
		} else {
			out.TabId = defaultTabId
			out.TabName = "未选择"
		}
		out.SwitchPlayInfo = switchPlayInfo
		log.InfoWithCtx(ctx, "GetTopicChannelCfgInfo in:%s out:%s", in.String(), out.String())
		return out, err
	}

	//主题房
	channelInfo := topicResp.GetInfo()[0]
	tabId := channelInfo.GetTabId()
	clientType := channelInfo.ClientType
	tabInfo = cache.GetTabInfoCache().GetTabInfoCacheById(tabId)
	if tabInfo != nil {
		out.TabId = tabInfo.GetId()
		out.CategoryType = tabInfo.GetCategoryMapping() // 分类标识
		out.TabName = tabInfo.GetName()
		out.MiniGameId = tabInfo.GetMiniGameId()
		// PC版的请求扩列不展示游戏卡
		if !protocol.IsFastPcClientType(uint32(serviceInfo.ClientType)) || tabInfo.GetId() != conf.PublicSwitchConfig.GetMuseChatTabId() {
			out.TagId = tabInfo.GetTagId()
			out.GameCardId = tabInfo.GetGameInfo().GetGameCardId()
		}
		//是否展示发布按钮，首页配置未分类，不展示发布按钮
		if tabInfo.GetHomePageType() == tabpb.HomePageType_HomePageTypeNone {
			out.ShowPublishButton = false
		} else {
			out.ShowPublishButton = tabInfo.GetShowPublishButton()
		}
		out.SwitchPlayInfo = &topic_channel.SwitchPlayInfo{
			RoomModel: gaChannelPB.EChannelMicMode(tabInfo.GetMicMod()),
		}
		// 查询玩法的搭子卡，如果存在则需要展示房间搭子卡入口
		if cache.GetGamePalTabCacheByTabId(tabInfo.GetId()) != nil {
			out.GamePalEntranceStatus = uint32(channel_play.GamePalEntranceStatus_GAME_PAL_ENTRANCE_STATUS_SHOW)
		} else {
			out.GamePalEntranceStatus = uint32(channel_play.GamePalEntranceStatus_GAME_PAL_ENTRANCE_STATUS_HIDE)
		}
	} else {
		out.TabId = defaultTabId
		out.TabName = "未选择"
	}

	isPublish := false
	for _, d := range channelInfo.GetDisplayType() {
		switch d {
		case channelPB.ChannelDisplayType_DISPLAY_AT_MAIN_PAGE:
			out.IsInGround = true
			out.PlayingOption = append(out.PlayingOption, uint32(topic_channel.PlayingOption_AT_MAIN_PAGE))
			isPublish = true

		case channelPB.ChannelDisplayType_DISPLAY_AT_FIND_FRIEND:
			out.PlayingOption = append(out.PlayingOption, uint32(topic_channel.PlayingOption_FRIEND))
		}
	}
	if len(channelInfo.GetDisplayType()) == 0 {
		out.IsInGround = true
	}

	blockOptions := make([]*topic_channel.BlockOption, 0, len(channelInfo.GetBlockOptions()))
	for _, info := range channelInfo.GetBlockOptions() {
		// 非发布中的PC上看的扩列房不展示移动端扩列旧的发布字段
		if !isPublish && protocol.IsFastPcClientType(uint32(serviceInfo.ClientType)) && tabInfo.GetId() == conf.PublicSwitchConfig.GetMuseChatTabId() {
			break
		}
		blockOptions = append(blockOptions, &topic_channel.BlockOption{
			BlockId: info.GetBlockId(),
			ElemId:  info.GetElemId(),
			ElemVal: info.GetElemVal(),
		})
	}

	//极速返回冷却时间和解散时间等，避免客户端本地处理问题
	out.FastPcCfgTabInfo = &channel_play.FastPcCfgTabInfo{
		RoomBackgroundImgUrl: tabInfo.GetFastPcRoomBackgroundImgUrl(), // tab服务处理了兼容开黑扩列和MT扩列
		ChangeCoolDown:       conf.ChannelPlayLogicConfig.LoadConfig().ChangeCoolDown,
		FreezeDuration:       conf.ChannelPlayLogicConfig.LoadConfig().FreezeDuration,
		AutoDismissDuration:  c.Publisher.CalcAutoDismissDuration(ctx, channelInfo.GetId(), tabInfo.GetId(), clientType, channelInfo.GetCreateTime(), isPublish),
	}
	// TeamDesc统一风格，客户端,  GamePublishDesc 发布信息描述，目前用于极速版展示
	out.TeamDesc, out.FastPcCfgTabInfo.FastPcPublishDesc = c.CommonHandle.GetPublishInfoDesc(ctx, tabInfo.GetId(), clientType, blockOptions)
	welcomeText := c.getWelcomeText(ctx, tabInfo.GetNewcomerWelcome(), in.GetChannelId(), tabInfo.GetCategoryMapping())
	out.ShowTeamDesc = true
	out.WelcomeText = welcomeText

	switch tabInfo.GetTabType() {
	case tabpb.Tab_NORMAL:
		out.TabType = channel_play.GetTopicChannelCfgInfoResp_NORMAL
	case tabpb.Tab_GAME:
		out.TabType = channel_play.GetTopicChannelCfgInfoResp_GAME
	case tabpb.Tab_MINIGAME:
		//小游戏进房上报给元挺做召回
		out.TabType = channel_play.GetTopicChannelCfgInfoResp_MINI_GAME
	default:
		log.WarnWithCtx(ctx, "GetTopicChannelInfo invalid tab type %d", tabInfo.GetTabType())
	}
	// 如果是小游戏，需要带上指定游戏可跳转第三方游戏的配置信息(下载游戏功能，菲菲v-5.2.1)
	if otherGameConf, ok := conf.ThirdPartyGameConf.GetThirdPartyConfMap()[tabInfo.GetId()]; ok {
		out.ThirdPartyGame = otherGameConf.ConvertOtherGameConf()
	}
	log.InfoWithCtx(ctx, "GetTopicChannelCfgInfo in:%s out:%s", in.String(), out.String())
	return out, err
}

func (c *ChannelOperationMgr) getWelcomeText(ctx context.Context, welcomeTextList []string, cid uint32,
	categoryType uint32) (text string) {
	if topic_channel.CategoryType(categoryType) == topic_channel.CategoryType_Gangup_type && c.IsWordChannel(ctx, cid) {
		text = conf.ChannelPlayLogicConfig.GetWordChannelWelcomeText()
		return
	}
	if len(welcomeTextList) != 0 {
		n := rand.Intn(len(welcomeTextList)) //nolint:gosec
		text = welcomeTextList[n]
	}
	if text == "" {
		text = "hi，快点击上麦和我一起玩吧~"
	}
	return
}

func (c *ChannelOperationMgr) IsWordChannel(ctx context.Context, cid uint32) bool {
	//只查个人房的
	info, err := c.TopicChannelCfgInfo.channelSchemeClient.GetCurChannelSchemeInfo(ctx, cid, 3)
	if err != nil {
		log.ErrorWithCtx(ctx, "IsWordChannel channelSchemeClient.GetCurChannelSchemeInfo cid:%d, err:%v", cid, err)
		return false
	}
	if channel_scheme.SchemeDetailType(info.GetSchemeInfo().GetSchemeDetailType()) == channel_scheme.SchemeDetailType_SCHEME_DETAIL_TYPE_GAME_WORD {
		return true
	}
	return false
}

type OptionConf struct {
	min uint32
	max uint32
}

func handleUserInputBlockOption(blockOptions []*topic_channel.BlockOption, blocks []*tabpb.Block) []*topic_channel.BlockOption {
	assistMap := make(map[uint32]map[uint32]OptionConf)
	blockModeMap := make(map[uint32]tabpb.Block_Mode)
	for _, baseOpt := range blocks {
		blockModeMap[baseOpt.GetId()] = baseOpt.GetMode()
		assistMap[baseOpt.GetId()] = map[uint32]OptionConf{}
		for _, e := range baseOpt.GetElems() {
			assistMap[baseOpt.GetId()][e.GetId()] = OptionConf{
				min: e.GetMinNum(),
				max: e.GetMaxNum(),
			}
		}
	}
	newBlockOption := make([]*topic_channel.BlockOption, 0, len(blockOptions))
	for _, opt := range blockOptions {
		min, max := 0, 0
		if v, ok := assistMap[opt.GetBlockId()]; ok {
			if v1, ok1 := v[opt.GetElemId()]; ok1 {
				min = int(v1.min)
				max = int(v1.max)

			} else {
				continue
			}
		} else {
			continue
		}
		if blockModeMap[opt.GetBlockId()] == tabpb.Block_USER_INPUT && len(opt.GetElemVal()) == 0 {
			continue
		}

		if len(opt.GetElemVal()) > 0 {
			valInt, err := strconv.Atoi(opt.ElemVal)
			if err != nil {
				continue
			}
			if valInt < min || valInt > max {
				continue
			}
		}

		newBlockOption = append(newBlockOption, opt)
	}
	return newBlockOption
}

// 开黑发布block勾选按钮处理
func (c *ChannelOperationMgr) handleBlockButtonOpt(ctx context.Context, cid, tabId uint32, opts []*channel_play.BlockButtonOpt) ([]MicSettingInfo, error) {
	log.DebugWithCtx(ctx, "handleBlockButtonOpt cid:%d, tabId:%d, opts:%v", cid, tabId, opts)
	if len(opts) == 0 {
		return nil, nil
	}
	baseBlocks := cache.GetBaseBlocksMapCache()[tabId]
	micSettingReport := make([]MicSettingInfo, 0, gameMicCount)
	micNames := make([]*channel_mic_ext.MicName, 0, gameMicCount)
	micId := uint32(2) // 设置从1号麦位开始，基础那边1号麦的micId是2
	for _, opt := range opts {
		if micId > gameMicCount {
			break
		}
		switch opt.GetType() {
		case uint32(topic_channel.BlockButtonType_BUTTON_TYPE_SET_MIC_NAME):
			checkBlock := false
			blockName := ""
			for _, baseBlock := range baseBlocks {
				if baseBlock.GetId() == opt.GetBlockId() && baseBlock.GetSetMicName() {
					checkBlock = true
					blockName = baseBlock.GetTitle()
				}
			}
			if !checkBlock {
				log.WarnWithCtx(ctx, "handleBlockButtonOpt not set mic name tabId:%d, cid:%d, opt:%v", tabId, cid, opt)
				continue
			}

			elemMap := cache.GetTabInfoCache().GetElemInfoMap(tabId)
			for _, elemId := range opt.GetElemId() {
				if micId > gameMicCount {
					break
				}
				elemInfo, ok := elemMap[elemId]
				if !ok {
					log.InfoWithCtx(ctx, "handleBlockButtonOpt not find elemId:%d, cid:%d, tabId:%d", elemId, cid, tabId)
					continue
				}
				micNames = append(micNames, &channel_mic_ext.MicName{
					MicId:   micId,
					MicName: elemInfo.GetTitle(),
				})
				micSettingReport = append(micSettingReport, MicSettingInfo{
					BlockName: blockName,
					MicId:     micId - 1,
					MicName:   elemInfo.GetTitle(),
				})
				micId++
			}
		default:
			log.WarnWithCtx(ctx, "handleBlockButtonOpt not support type:%d, cid:%d, tabId:%d", opt.GetType(), cid, tabId)
		}
	}
	log.InfoWithCtx(ctx, "handleBlockButtonOpt SetMicName cid:%d, tabId:%d, micNames:%v, opts:%v", cid, tabId, micNames, opts)
	if len(micNames) == 0 {
		return nil, nil
	}
	// 设置麦位名称，状态
	_, err := c.Publisher.channelMicExtCli.SetMicNameSwitch(ctx, &channel_mic_ext.SetMicNameSwitchReq{
		ChannelId: cid,
		State:     true,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "handleBlockButtonOpt SetMicNameSwitch err:%v, cid:%d, tabId:%d", err, cid, tabId)
		return nil, err
	}
	_, err = c.Publisher.channelMicExtCli.SetMicName(ctx, &channel_mic_ext.SetMicNameReq{
		ChannelId:  cid,
		SchemeId:   tabId,
		MicNames:   micNames,
		SourceType: channel_mic_ext.SourceType_SOURCE_TYPE_CHANNEL_PUBLISH,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "handleBlockButtonOpt SetMicName err:%v, cid:%d, tabId:%d", err, cid, tabId)
		return nil, err
	}
	return micSettingReport, nil
}
