package channel_operation

import (
	"context"
	"github.com/jinzhu/copier"
	account "golang.52tt.com/clients/account-go"
	"golang.52tt.com/clients/channel"
	"golang.52tt.com/clients/channel-scheme"
	"golang.52tt.com/clients/channelapi"
	channelol_stat_go "golang.52tt.com/clients/channelol-stat-go"
	riskMngApi "golang.52tt.com/clients/risk-mng-api"
	"golang.52tt.com/kaihei-pkg/supervision"
	"golang.52tt.com/pkg/cogroup_util"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app"
	channelPB "golang.52tt.com/protocol/app/channel"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/protocol/common/status"
	schemePb "golang.52tt.com/protocol/services/channel-scheme"
	channelSvrPB "golang.52tt.com/protocol/services/channelsvr"
	gangupchannelpb "golang.52tt.com/protocol/services/gangup-channel"
	music_channel "golang.52tt.com/protocol/services/music-topic-channel"
	riskMngApiPb "golang.52tt.com/protocol/services/risk-mng-api"
	topicChannelPB "golang.52tt.com/protocol/services/topic_channel/channel"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
	"golang.52tt.com/services/channel-play-logic/internal/cache/cache_client"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/channel_list/view"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/room_config_mgr"
	"golang.52tt.com/services/channel-play-logic/internal/utils"
	"strings"
	"time"
)

const NoModifyNameRequest = "no_modify_name"

type CommonHandle struct {
	channelClient       channel.IClient
	channelSchemeClient channel_scheme.IClient
	supervisorInst      *supervision.Supervisory
	channelApiClient    *channelapi.Client
	channelOlStatClient *channelol_stat_go.Client
	accountClient       *account.Client
	riskMngClient       *riskMngApi.Client
}

func NewCommonHandle(channelClient channel.IClient, channelSchemeClient channel_scheme.IClient, supervisorInst *supervision.Supervisory,
	channelApiClient *channelapi.Client, channelOlStatClient *channelol_stat_go.Client, accountClient *account.Client,
	riskMngClient *riskMngApi.Client) (*CommonHandle, error) {
	return &CommonHandle{
		channelClient:       channelClient,
		channelSchemeClient: channelSchemeClient,
		supervisorInst:      supervisorInst,
		channelApiClient:    channelApiClient,
		channelOlStatClient: channelOlStatClient,
		accountClient:       accountClient,
		riskMngClient:       riskMngClient,
	}, nil
}

// 根据uid获取account信息
func (h *CommonHandle) GetUserInfo(ctx context.Context, uid uint32) (*account.User, error) {
	user, err := h.accountClient.GetUserByUid(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CommonHandle GetUserInfo uid%d err:%v", uid, err)
		return nil, err
	}
	return user, nil
}

// 根据uid获取个人房信息
func (h *CommonHandle) GetChannelInfoByUid(ctx context.Context, uid uint32) (channelId uint32, channelInfo *channelSvrPB.ChannelDetailInfo, err error) {
	// 获取用户个人房
	roleListResp, err := h.channelClient.GetUserChannelRoleList(ctx, uid, uid, uint32(channelSvrPB.ChannelAdminRole_CHANNEL_OWNER))
	if err != nil {
		log.ErrorWithCtx(ctx, "CommonHandle GetUserChannelRoleList err: %v", err)
		return channelId, channelInfo, protocol.ToServerError(err)
	}

	for _, role := range roleListResp.GetRoleList() {
		if role.GetRole() == uint32(channelSvrPB.ChannelAdminRole_CHANNEL_OWNER) &&
			role.GetChannelInfo().GetChannelBindType() == uint32(channelPB.ChannelType_USER_CHANNEL_TYPE) {
			channelId = role.GetChannelInfo().GetChannelBaseinfo().GetChannelId()
			channelInfo = role.GetChannelInfo()
			break
		}

	}
	if 0 == channelId {
		log.ErrorWithCtx(ctx, "CommonHandle GetUserChannelRoleList err: got nothing, uid:%d", uid)
		return channelId, channelInfo, protocol.NewExactServerError(nil, status.ErrChannelNoPermission, "该用户没有个人房")
	}
	return channelId, channelInfo, nil
}

// 判断是否房主,并返回房间名
func (h *CommonHandle) IsChannelOwner(ctx context.Context, uid, channelId uint32) (string, error) {
	//判断是否房主
	channelSimpleInfo, getChannelErr := h.channelClient.GetChannelSimpleInfo(ctx, uid, channelId)
	if getChannelErr != nil {
		//log.ErrorWithCtx(ctx, "CancelPublishGangupChannel GetChannelSimpleInfo err:%v, cid:%d", getChannelErr, channelId)
		return "", getChannelErr
	}
	if uid != channelSimpleInfo.GetBindId() {
		return "", protocol.NewExactServerError(nil, status.ErrTopicChannelPermissionDenied)
	}
	//房间类型检查
	if channelSimpleInfo.GetChannelType() != uint32(channelPB.ChannelType_USER_CHANNEL_TYPE) {
		return "", protocol.NewExactServerError(nil, status.ErrKnockWrongRoomType, "错误的房间类型")
	}
	return channelSimpleInfo.GetName(), nil
}

// 根据channelId获取玩法信息
func (h *CommonHandle) GetSchemeInfoByCid(ctx context.Context, cid uint32) (*schemePb.ChannelSchemeInfo, error) {
	//channelType 3是ugc个人房
	info, err := h.channelSchemeClient.GetCurChannelSchemeInfo(ctx, cid, 3)
	if err != nil {
		log.WarnWithCtx(ctx, "CommonHandle GetSchemeInfoByCid cid %d err %v", cid, err)
		return nil, err
	}
	return info.GetSchemeInfo(), nil
}

// 更新个人房房间名
func (h *CommonHandle) UpdateUgcChannelName(g *cogroup_util.Group, ctx context.Context, userInfo *account.User, channelId, tabId uint32, channelName,
	smDeviceId string, marketId, clientType uint32, oldChannelName string, replaceEmptyName bool) (error, string) {
	var err error
	uid := userInfo.GetUid()
	var requestId string
	err, channelName, requestId = h.CheckChannelName(g, ctx, userInfo, channelId, tabId, channelName, smDeviceId, marketId, clientType, oldChannelName, replaceEmptyName, true)
	if err != nil {
		return err, ""
	}
	// 更新房间名
	//_, err = h.channelApiClient.ModifyName(ctx, uid, channelId, 0, marketId, channelName)
	//if err != nil {
	//	log.ErrorWithCtx(ctx, "channel api ModifyName fail, name: %s, err: %v", channelName, err)
	//	return err, ""
	//}
	if channelName == oldChannelName {
		//房间名没变
		return nil, channelName
	}
	err = h.ModifyChannelName(g, ctx, uid, channelId, marketId, channelName, requestId)
	if err != nil {
		return err, ""
	}
	return nil, channelName
}

// 修改房间名
func (h *CommonHandle) ModifyChannelName(g *cogroup_util.Group, ctx context.Context, uid, channelId, marketId uint32, channelName, requestId string) (err error) {
	_, err = h.channelApiClient.ModifyName(ctx, uid, channelId, 0, marketId, channelName, requestId)
	if err != nil {
		utils.NormalErrCntLogWithGoGroup(g, err, ctx, "channel api ModifyName fail, uid(%d) cid(%d) marketId(%d) name: %s, err: %v", uid, channelId, marketId, channelName, err)
		//log.ErrorWithCtx(ctx, "channel api ModifyName fail, uid(%d) cid(%d) marketId(%d) name: %s, err: %v", uid, channelId, marketId, channelName, err)
		return
	}
	return
}

// 切换房间玩法人数判断逻辑
func (h *CommonHandle) InitHobbyChannel(ctx context.Context, uid, channelId, tabId uint32, oldTabId, schemeDetailType uint32) error {
	memberSize, err := h.channelOlStatClient.GetChannelMemberSize(ctx, uid, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "SwitchHobbyChannelSubject ChannelOlMemberClient fail, cid: %d, err: %v", channelId, err)
		return err
	}

	//当前房间人数是否超过要切换的玩法人数,是的话不能切换
	confResp, err := cache_client.ChannelSchemeConfClient.GetChannelSchemeConfCache(ctx, oldTabId, schemeDetailType)
	if confResp.GetConf().GetExtraConf().GetAutoExitByMaxSize() {
		maxSizeLimit, err := h.channelSchemeClient.GetUgcSchemeMemberSizeLimit(ctx, channelId, tabId)
		if err != nil {
			log.ErrorWithCtx(ctx, "ChannelSchemeClient.GetUgcSchemeMemberSizeLimit failed cid: %d", channelId)
			return err
		}
		if maxSizeLimit > 0 && memberSize > maxSizeLimit {
			log.InfoWithCtx(ctx, "SwitchHobbyChannelSubject fail because too large memberSize(%d), cid: %d, oldSchemeId:%d, newSchemeId:%d, maxSizeLimit:%d",
				memberSize, channelId, oldTabId, tabId, maxSizeLimit)
			return protocol.NewExactServerError(nil, status.ErrTopicChannelTooManyMemberSwitchPlay)
		}
	}

	return nil
}

// 是否发布中的房间
func (h *CommonHandle) IsDisplayChannel(displayType []gangupchannelpb.ChannelDisplayType) bool {
	for _, v := range displayType {
		if v == gangupchannelpb.ChannelDisplayType_DISPLAY_AT_MAIN_PAGE {
			// 仅展示发布的
			return true
		}
	}
	return false
}

func (h *CommonHandle) IsMusicDisplayChannel(displayType []music_channel.ChannelDisplayType) bool {
	for _, v := range displayType {
		if v == music_channel.ChannelDisplayType_DISPLAY_AT_MAIN_PAGE {
			// 仅展示发布的
			return true
		}
	}
	return false
}

func (h *CommonHandle) CheckChannelName(g *cogroup_util.Group, ctx context.Context, userInfo *account.User, channelId, tabId uint32, channelName,
	smDeviceId string, marketId, clientType uint32, oldChannelName string, replaceEmptyName, isCreateRoom bool) (error, string, string) {
	//创房房间名为空特殊处理
	roomNameConfigs := cache.GetOfficialRoomNameByTabId(tabId, clientType)
	if channelName == "" && len(roomNameConfigs) > 0 && replaceEmptyName {
		log.WarnWithCtx(ctx, "CreateHobbyChannel GetName err:use defaultName, channelId:%d , inName:%s, defaultName:%s",
			channelId, channelName, roomNameConfigs[0].GetName())
		channelName = roomNameConfigs[0].GetName()
	}

	if channelName == "" {
		log.WarnWithCtx(ctx, "CheckChannelName GetName err:name nil,  channelId:%d , inName:%s", channelId, channelName)
		return protocol.NewExactServerError(nil, status.ErrTopicChannelNameIsNull), "", ""
	}

	if strings.Trim(channelName, " \t") == "" {
		log.WarnWithCtx(ctx, "CheckChannelName channelName err, cid: %d, channelName: %s", channelId, channelName)
		return protocol.NewExactServerError(nil, status.ErrTopicChannelNameFormattedInvalid), "", ""
	}

	//  跳过系统默认房间名
	isDefaultName := false
	if channelName == room_config_mgr.DefaultRoomName {
		isDefaultName = true
	} else {
		for _, name := range roomNameConfigs {
			if name.GetName() == channelName {
				isDefaultName = true
				break
			}
		}
	}

	supervisorPublishNameNoticeMsg := conf.ChannelPlayLogicConfig.GetSupervisorPublishNameMsg()
	createRoomNameNoticeMsg := conf.ChannelPlayLogicConfig.GetSupervisorCreateRoomNameMsg()
	publishNameNoticeMsg := conf.ChannelPlayLogicConfig.GetPublishRoomNameMsg()

	if isDefaultName {
		//默认房间名不用审核
		if channelName == oldChannelName {
			//房间名没变
			//log.InfoWithCtx(ctx, "isDefaultName channelName == oldChannelName")
			return nil, channelName, NoModifyNameRequest
		}
		//log.InfoWithCtx(ctx, "isDefaultName channelName != oldChannelName")
		return nil, channelName, ""
	} else {
		if protocol.IsFastPcClientType(clientType) {
			marketId = 1000 //极速pc模拟1000的马甲包
		}
		//NODE：这个时间段内，发布个人房不能改房间名
		if conf.ChannelPlayLogicConfig.LoadConfig().DiyNameLimitStartTime != 0 && conf.ChannelPlayLogicConfig.LoadConfig().DiyNameLimitEndTime != 0 &&
			time.Now().Unix() >= conf.ChannelPlayLogicConfig.LoadConfig().DiyNameLimitStartTime &&
			time.Now().Unix() <= conf.ChannelPlayLogicConfig.LoadConfig().DiyNameLimitEndTime && !conf.ChannelPlayLogicConfig.GetDiyNameLimitWhiteMarket()[marketId] {
			if channelName == oldChannelName {
				//送审
				//log.InfoWithCtx(ctx, "notDefaultName DiyNameLimitStartTime channelName == oldChannelName")
			} else {
				if isCreateRoom {
					return protocol.NewExactServerError(nil, status.ErrTopicChannelNameCanNotDiy, createRoomNameNoticeMsg), "", ""
				} else {
					return protocol.NewExactServerError(nil, status.ErrTopicChannelNameCanNotDiy, supervisorPublishNameNoticeMsg), "", ""
				}
			}
		} else {
			if channelName == oldChannelName {
				//房间名没变
				//log.InfoWithCtx(ctx, "notDefaultName channelName == oldChannelName")
				return nil, channelName, NoModifyNameRequest
			}
		}
	}

	//log.InfoWithCtx(ctx, "supervisorInst.CheckChannelName")
	requestId, err := h.supervisorInst.CheckChannelName(ctx, userInfo, channelId, channelName, smDeviceId)
	if err != nil {
		if pe, ok := err.(protocol.ServerError); ok {
			if pe.Code() == status.ErrTopicChannelNameSensitive {
				return protocol.NewExactServerError(nil, status.ErrTopicChannelNameCanNotDiy, publishNameNoticeMsg), "", requestId
			}
		}
		utils.NormalErrCntLogWithGoGroup(g, err, ctx, "CheckChannelName userId(%v) TextCheck err: %v", userInfo.GetUid(), err)
		return err, "", requestId
	}

	return nil, channelName, requestId
}

// GetPublishInfoDesc 房间发布信息展示文案
func (h *CommonHandle) GetPublishInfoDesc(ctx context.Context, tabId, clientType uint32, blockOptions []*topic_channel.BlockOption) (channelDesc string,
	fastPcPublishDesc []*channel_play.FastPcCfgTabInfo_FastPcPublishDesc) {

	fastPcPublishDesc = make([]*channel_play.FastPcCfgTabInfo_FastPcPublishDesc, 0, len(blockOptions))

	blocks := cache.GetBaseBlocksByTabId(tabId, clientType)
	displayInfo := cache.GetBlockRelationByTabId(tabId, clientType)
	if len(blockOptions) == 0 || len(displayInfo) == 0 || len(blocks) == 0 {
		return
	}
	channelTabInst := view.NewChannelTabWithoutClient()
	options := make([]*topicChannelPB.BlockOption, 0, len(blockOptions))
	err := copier.Copy(&options, blockOptions)
	if err != nil {
		log.ErrorWithCtx(ctx, "getPublishInfoDesc blockOptions:%v err%v", blockOptions, err)
		return
	}
	selectedBlockSlice := make([]string, 0, len(blocks))
	mobaMap, normalMap, allSelectBlockMap, userSelectedMap := channelTabInst.GetBlockTitleMap(options, blocks, view.OptSep)
	blockShownMap := make(map[uint32]bool)
	//不限的blockTitle数组
	allSelectBlockSlice := make([]string, 0, len(allSelectBlockMap))
	// 按照关联关系的顺序处理block
	for _, v := range displayInfo {
		//处理一级字段
		if title := h.getBlockDesc(v.GetBlock(), mobaMap, normalMap, allSelectBlockMap); title != "" {
			if allSelectBlockMap[v.GetBlock().GetTitle()] {
				//不限的block，最后要合并起来并放在最后， xxx/xxx（不限）
				allSelectBlockSlice = append(allSelectBlockSlice, v.GetBlock().GetTitle())
				blockShownMap[v.GetBlock().GetId()] = true
			} else {
				selectedBlockSlice = append(selectedBlockSlice, title)
				fastPcPublishDesc = append(fastPcPublishDesc, &channel_play.FastPcCfgTabInfo_FastPcPublishDesc{
					Title:    v.GetBlock().GetTitle() + view.TitleSep,
					ElemName: title,
				})
				blockShownMap[v.GetBlock().GetId()] = true
			}
		}
		var elemBindTitles []string
		var elemBindAllSelectBidSlice []string
		var bindGamePublishDesc []*channel_play.FastPcCfgTabInfo_FastPcPublishDesc
		//处理关联的二级字段
		elemBindTitles, elemBindAllSelectBidSlice, bindGamePublishDesc, blockShownMap = h.getElemBindBlockDesc(v.GetElemBindBlockInfos(),
			blockShownMap, mobaMap, normalMap, allSelectBlockMap, userSelectedMap[v.GetBlock().GetId()])
		if len(elemBindTitles) != 0 {
			selectedBlockSlice = append(selectedBlockSlice, elemBindTitles...)
			fastPcPublishDesc = append(fastPcPublishDesc, bindGamePublishDesc...)
		}
		if len(elemBindAllSelectBidSlice) != 0 {
			allSelectBlockSlice = append(allSelectBlockSlice, elemBindAllSelectBidSlice...)
		}
	}
	if len(allSelectBlockSlice) != 0 {
		//选中了不限的block，合并后追加在最后
		selectedBlockSlice = append(selectedBlockSlice, strings.Join(allSelectBlockSlice, "/")+"("+view.BlockNoLimit+")")
		fastPcPublishDesc = append(fastPcPublishDesc, &channel_play.FastPcCfgTabInfo_FastPcPublishDesc{
			Title:    view.OtherBlockName + view.TitleSep,
			ElemName: view.BlockNoLimit,
		})
	}
	fastPcPublishDesc = mergeGamePublishDesc(fastPcPublishDesc)
	channelDesc = strings.Join(selectedBlockSlice, " | ")
	return
}

// 整合gamePublishDesc数据，把title为区服和模式的数据合并
func mergeGamePublishDesc(fastPcPublishDesc []*channel_play.FastPcCfgTabInfo_FastPcPublishDesc) []*channel_play.FastPcCfgTabInfo_FastPcPublishDesc {
	//整合gamePublishDesc数据，把title为区服和模式的数据合并
	newFastPcPublishDesc := make([]*channel_play.FastPcCfgTabInfo_FastPcPublishDesc, 0, len(fastPcPublishDesc))
	mergeSlice := ""
	for _, v := range fastPcPublishDesc {
		if v.GetTitle() == (view.PlayServer+view.TitleSep) || v.GetTitle() == (view.PlayMode+view.TitleSep) {
			mergeSlice += v.GetElemName() + view.OptSep
		} else {
			newFastPcPublishDesc = append(newFastPcPublishDesc, v)
		}
	}
	if mergeSlice != "" {
		mergeSlice = strings.TrimRight(mergeSlice, view.OptSep)
		newFastPcPublishDesc = append([]*channel_play.FastPcCfgTabInfo_FastPcPublishDesc{{
			Title:    view.PlayServer + view.PlayMode + view.TitleSep,
			ElemName: mergeSlice,
		}}, newFastPcPublishDesc...)
	}
	return newFastPcPublishDesc
}

func (h *CommonHandle) getElemBindBlockDesc(elemBindInfo []*channel_play.ElemBindBlockInfo, blockShownMap map[uint32]bool,
	mobaMap, normalMap map[string]string, allSelectBlockMap map[string]bool, elemSelectedMap map[uint32]view.BlockOptionVal) ([]string, []string,
	[]*channel_play.FastPcCfgTabInfo_FastPcPublishDesc, map[uint32]bool) {

	selectedBlockSlice := make([]string, 0)
	allSelectBlockSlice := make([]string, 0)
	fastPcPublishDesc := make([]*channel_play.FastPcCfgTabInfo_FastPcPublishDesc, 0, len(elemBindInfo))
	if len(elemSelectedMap) == 0 {
		return selectedBlockSlice, allSelectBlockSlice, fastPcPublishDesc, blockShownMap
	}

	for _, bindInfo := range elemBindInfo {
		if !elemSelectedMap[bindInfo.GetElemId()].IsSelectEd {
			//用户没有选这个elem，不需要处理关联的block
			continue
		}
		for _, bindBlock := range bindInfo.GetBindBlocks() {
			if blockShownMap[bindBlock.GetId()] {
				continue
			}
			if title := h.getBlockDesc(bindBlock, mobaMap, normalMap, allSelectBlockMap); title != "" {
				if allSelectBlockMap[bindBlock.GetTitle()] {
					//不限的block，最后要合并起来并放在最后， xxx/xxx（不限）
					allSelectBlockSlice = append(allSelectBlockSlice, bindBlock.GetTitle())
					blockShownMap[bindBlock.GetId()] = true
				} else {
					selectedBlockSlice = append(selectedBlockSlice, title)
					fastPcPublishDesc = append(fastPcPublishDesc, &channel_play.FastPcCfgTabInfo_FastPcPublishDesc{
						Title:    bindBlock.GetTitle() + view.TitleSep,
						ElemName: title,
					})
					blockShownMap[bindBlock.GetId()] = true
				}
			}
		}
	}
	return selectedBlockSlice, allSelectBlockSlice, fastPcPublishDesc, blockShownMap
}

func (h *CommonHandle) getBlockDesc(block *topic_channel.Block, mobaMap, normalMap map[string]string, allSelectBlockMap map[string]bool) string {
	if allSelectBlockMap[block.GetTitle()] {
		return block.GetTitle() + "(" + view.BlockNoLimit + ")"
	} else if title, ok := mobaMap[block.GetTitle()]; ok {
		return title
	} else if title, ok := normalMap[block.GetTitle()]; ok {
		return title
	}
	return ""
}

// 统一风控检查,200ms超时
func (h *CommonHandle) RiskMngCheckChannelHelper(ctx context.Context, checkReq *riskMngApiPb.CheckReq, baseReq *app.BaseReq) (*riskMngApiPb.CheckResp, error) {
	var timeOutCtx context.Context
	var cancel context.CancelFunc
	if conf.GetEnv() == conf.Testing {
		timeOutCtx, cancel = context.WithTimeout(ctx, 2000*time.Millisecond)
		defer cancel()
	} else {
		timeOutCtx, cancel = context.WithTimeout(ctx, 500*time.Millisecond)
		defer cancel()
	}

	checkResp, err := h.riskMngClient.CheckHelper(timeOutCtx, checkReq, baseReq)
	if err != nil {
		return nil, err
	}
	return checkResp, nil
}
