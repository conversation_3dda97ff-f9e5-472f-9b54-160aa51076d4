package mutientry_filter_mgr

import (
	"context"
	gameCard "golang.52tt.com/clients/game-card"
	gangup_channel_cli "golang.52tt.com/clients/gangup-channel"
	"golang.52tt.com/pkg/log"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	game_card_app "golang.52tt.com/protocol/app/game-card"
	game_card "golang.52tt.com/protocol/services/game-card"
	gangup_channel "golang.52tt.com/protocol/services/gangup-channel"
	"golang.52tt.com/protocol/services/music-topic-channel"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"golang.52tt.com/services/channel-play-logic/internal/utils"
	"sort"
)

type CommonHandler struct {
	gangupChannelClient *gangup_channel_cli.Client
	gameCardClient      *gameCard.Client
	musicClient         music_topic_channel.MusicChannelClient
}

func NewCommonHandler(gangupChannelClient *gangup_channel_cli.Client, gameCardClient *gameCard.Client,
	musicClient music_topic_channel.MusicChannelClient) *CommonHandler {
	return &CommonHandler{
		gangupChannelClient: gangupChannelClient,
		gameCardClient:      gameCardClient,
		musicClient:         musicClient,
	}
}

// 获取常玩分类
func (c *CommonHandler) GetDIYFilterItem(ctx context.Context, uid uint32, entrance channel_play.FilterEntrance) (
	items []*channel_play.CommonBusinessFlag, err error) {
	diyItems, err := c.gangupChannelClient.GetDIYFilterByEntrance(ctx, &gangup_channel.GetDIYFilterByEntranceReq{
		Uid:       uid,
		EntryType: uint32(entrance),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CommonHandler GetDIYFilterFlag uid(%d) entrance(%v) error:%v", uid, entrance, err)
		return []*channel_play.CommonBusinessFlag{}, err
	}
	items = utils.PbConvertToCommonBusinessFlag(diyItems.GetItems())
	return

}

// 获取默认配置
func (c *CommonHandler) GetDefaultFilterItem(entrance channel_play.FilterEntrance) (items []*channel_play.CommonBusinessFlag) {
	defaultFilter := conf.ChannelPlayLogicConfig.GetDefaultFilterMapByEntrance(uint32(entrance))
	return defaultFilter
}

// 获取游戏卡
func (c *CommonHandler) GetGameCardInfos(ctx context.Context, uid uint32) ([]*game_card.GameCardInfo, error) {
	//获取用户的游戏卡
	gameCards, err := c.gameCardClient.GetGameCard(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameCardInfos GetGameCard error : %v", err)
		return []*game_card.GameCardInfo{}, err
	}
	//按照修改时间排序
	gameCards = c.sortGameCardByModifyTime(gameCards)
	return gameCards, nil
}

// 根据游戏卡修改时间排序
func (c *CommonHandler) sortGameCardByModifyTime(gameCards []*game_card.GameCardInfo) []*game_card.GameCardInfo {
	sort.Slice(gameCards, func(i, j int) bool {
		return gameCards[i].ModifyAt > gameCards[j].ModifyAt
	})
	return gameCards
}

// 根据游戏卡修改时间排序
func (c *CommonHandler) sortFlagByModifyTime(flags []*gameCardFlagScore) []*gameCardFlagScore {
	sort.Slice(flags, func(i, j int) bool {
		return flags[i].modifyAt > flags[j].modifyAt
	})
	return flags
}

// 根据入口设置常玩分类
func (c *CommonHandler) SetDIYFilterItem(ctx context.Context, uid uint32, entrance channel_play.FilterEntrance,
	flags []*channel_play.CommonBusinessFlag, isIncr bool) (err error) {
	_, err = c.gangupChannelClient.SetDIYFilterByEntrance(ctx, &gangup_channel.SetDIYFilterByEntranceReq{
		Uid:       uid,
		Items:     utils.CommonBusinessFlagConvertToPb(flags),
		EntryType: uint32(entrance),
		IsIncr:    isIncr,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CommonHandler SetDIYFilterFlag uid(%d) entrance(%v) flags(%v) error:%v", uid, entrance, flags, err)
		return err
	}
	return
}

// 根据入口生成筛选器外显名,并转换成FilterItem结构
func (c *CommonHandler) GenFilterItemWithDisplayName(ctx context.Context, sortedFlagItem []*channel_play.CommonBusinessFlag,
	needAliasName bool) (items []*channel_play.FilterItem) {
	items = make([]*channel_play.FilterItem, 0, len(sortedFlagItem))

	for _, v := range sortedFlagItem {
		item := &channel_play.FilterItem{
			CommonBusinessFlag: v,
		}
		if v.FilterItemType == channel_play.FilterItemType_GAME_TAB {
			if tabInfo, ok := cache.GetTabInfoCache().GetTabIdCache()[v.GameBusinessId]; ok {
				if needAliasName {
					item.DisplayName = tabInfo.GetTabAliasName()
				} else {
					item.DisplayName = tabInfo.GetName()
				}
			} else {
				log.WarnWithCtx(ctx, "MixHomePageFilter GenFilterItemWithDisplayName invalid flag(%s)", v.String())
				continue
			}
		} else if v.FilterItemType == channel_play.FilterItemType_GAME_CATEGORY {
			if categoryInfo, ok := cache.GetCategoryIdMap()[v.GetGameBusinessId()]; ok {
				if needAliasName {
					item.DisplayName = categoryInfo.GetCategoryAliasName()
				} else {
					item.DisplayName = categoryInfo.GetTitle()
				}
			} else {
				log.WarnWithCtx(ctx, "MixHomePageFilter GenFilterItemWithDisplayName invalid flag(%s)", v.String())
				continue
			}
		}
		items = append(items, item)
	}
	return items
}

type gameCardFlagScore struct {
	gameCardFlag *channel_play.CommonBusinessFlag
	modifyAt     uint32
}

// 处理音乐游戏卡, 返回的map key 是排序的index value是对应游戏卡的筛选项flag
func (c *CommonHandler) GenMusicCardFlags(ctx context.Context, musicCardNames []string, cardNameScoreMap map[string]uint32) ([]*gameCardFlagScore, error) {
	res := make([]*gameCardFlagScore, 0)

	//批量获取音乐卡映射id
	musicResp, err := c.musicClient.BatchFilterIdsByGameCard(ctx, &music_topic_channel.BatchFilterIdsByGameCardReq{
		GameCard: musicCardNames,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GenMusicCardFlags BatchFilterIdsByGameCard gameCardName(%v) err(%v)", musicCardNames, err)
		return res, err
	}
	for k, v := range musicResp.GetGameFilters() {
		score := cardNameScoreMap[k]
		for _, f := range v.GetFilterIds() {
			temp := &gameCardFlagScore{
				gameCardFlag: &channel_play.CommonBusinessFlag{
					MusicBusinessId: f,
					FilterItemType:  channel_play.FilterItemType_MUSIC_ITEM,
				},
				modifyAt: score,
			}
			res = append(res, temp)
		}
	}
	return res, nil

}

// 处理开黑游戏卡
func (c *CommonHandler) GenGameCardFlags(ctx context.Context, gameCardInfos []*game_card.GameCardInfo, categoryMap map[uint32]bool) ([]*gameCardFlagScore, error) {
	res := make([]*gameCardFlagScore, 0)
	tagsMap := cache.GetGameCardIdTabMap()

	for _, card := range gameCardInfos {
		if card.CardType == uint32(game_card_app.GameCardType_CARD_TYPE_GAME) {
			//游戏的游戏卡直接返回tabId
			if relateTabs, ok := tagsMap[card.GetGameCardId()]; ok {
				for _, t := range relateTabs {
					if !categoryMap[t.GetCategoryId()] {
						continue
					}
					flag := &gameCardFlagScore{
						gameCardFlag: &channel_play.CommonBusinessFlag{
							FilterItemType: channel_play.FilterItemType_GAME_TAB,
							GameBusinessId: t.GetId(),
						},
						modifyAt: card.GetModifyAt(),
					}
					res = append(res, flag)
				}

			} else {
				utils.WarnCntLog(ctx, "GenGameCardFlags GetGameCardItem GameCardInfo(%s) can not find tab", card.String())
			}
		}
	}
	return res, nil
}

// GetGameListItem 获取gamelist
func (c *CommonHandler) GetGameListItem(selfGameIds []uint32, categoryMap map[uint32]bool) (items []*channel_play.CommonBusinessFlag) {
	items = make([]*channel_play.CommonBusinessFlag, 0, len(selfGameIds))
	gameInfoMap := cache.GetTabInfoCache().GetUGameIdTabMapCache()
	gameInfoSlice := make([]*cache.MinorityGameInfo, 0, len(selfGameIds))
	for _, id := range selfGameIds {
		if gameInfo, ok := gameInfoMap[id]; ok && gameInfo != nil && !categoryMap[gameInfo.TabInfo.GetCategoryId()] {
			gameInfoSlice = append(gameInfoSlice, gameInfo)
		}
	}

	sort.SliceStable(gameInfoSlice, func(i, j int) bool {
		return gameInfoSlice[i].GameScore > gameInfoSlice[j].GameScore
	})

	for _, v := range gameInfoSlice {
		//if v.TabInfo.GetTabType() == tabPB.Tab_MINIGAME {
		//	//过滤小游戏
		//	continue
		//}
		item := &channel_play.CommonBusinessFlag{
			GameBusinessId: v.TabInfo.GetId(),
			FilterItemType: channel_play.FilterItemType_GAME_TAB,
		}
		items = append(items, item)

	}
	return
}

func (c *CommonHandler) GetCategoryMapByEntrance(ctx context.Context, entrance channel_play.FilterEntrance) map[uint32]bool {
	res := make(map[uint32]bool)
	for _, v := range conf.ChannelPlayLogicConfig.GetFilterEntranceCategoryMap(ctx, entrance) {
		res[v] = true
	}
	return res
}

// 获取音乐标签信息
func (c *CommonHandler) GetMusicFilterItemInfo(ctx context.Context, filterIds []string) (map[string]*music_topic_channel.MusicFilterItem, error) {
	resp, err := c.musicClient.GetMusicFilterItemByIds(ctx, &music_topic_channel.GetMusicFilterItemByIdsReq{
		FilterIds: filterIds,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMusicFilterItemInfo filterIds(%v) err(%v)", filterIds, err)
		return nil, err
	}
	return resp.GetFilterMap(), nil
}

// GenMixGameCardFlags 首页游戏卡
func (c *CommonHandler) GenMixGameCardFlags(ctx context.Context, gameCardInfos []*game_card.GameCardInfo) (
	gameRes []*gameCardFlagScore, err error) {
	gameRes = make([]*gameCardFlagScore, 0)
	musicCardNames := make([]string, 0)
	cardNameScoreMap := make(map[string]uint32)
	gameCardTabMap := cache.GetGameCardIdTabMap()
	for _, card := range gameCardInfos {
		if card.CardType == uint32(game_card_app.GameCardType_CARD_TYPE_GAME) {
			//游戏的游戏卡直接返回tabId
			if relateTabs, ok := gameCardTabMap[card.GetGameCardId()]; ok {
				for _, relTab := range relateTabs {
					flag := &gameCardFlagScore{
						gameCardFlag: &channel_play.CommonBusinessFlag{
							FilterItemType: channel_play.FilterItemType_GAME_TAB,
							GameBusinessId: relTab.GetId(),
						},
						modifyAt: card.GetModifyAt(),
					}
					gameRes = append(gameRes, flag)
				}

			} else {
				utils.WarnCntLog(ctx, "GenGameCardFlags GetGameCardItem GameCardInfo(%s) can not find tab", card.String())
			}
		} else if card.CardType == uint32(game_card_app.GameCardType_CARD_TYPE_MUSIC) {
			//音乐游戏卡通过游戏卡名称去查找清波那边的映射关系
			musicCardNames = append(musicCardNames, card.GetGameCardName())
			cardNameScoreMap[card.GetGameCardName()] = card.GetModifyAt()
		}
	}
	if len(musicCardNames) == 0 {
		return
	}
	musicRes, err := c.GenMusicCardFlags(ctx, musicCardNames, cardNameScoreMap)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenMusicCardFlags BatchFilterIdsByGameCard gameCardName(%v) err(%v)", musicCardNames, err)
		return gameRes, err
	}
	gameRes = append(gameRes, musicRes...)
	return
}
