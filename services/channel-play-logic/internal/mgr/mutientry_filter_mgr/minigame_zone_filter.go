package mutientry_filter_mgr

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol/grpc"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"time"
)

type MiniGameZoneFilter struct {
	commonHandler *CommonHandler
}

func NewMiniGameZoneFilter(commonHandler *CommonHandler) *MiniGameZoneFilter {
	return &MiniGameZoneFilter{
		commonHandler: commonHandler,
	}
}

// 获取排好序的筛选项
func (m *MiniGameZoneFilter) GetSortedFilterItems(ctx context.Context, serviceInfo *grpc.ServiceInfo, in *channel_play.GetFilterItemByEntranceReq) (
	items []*channel_play.FilterItem, err error) {
	//sortedItems := make([]*channel_play.CommonBusinessFlag, 0)
	uid := serviceInfo.UserID
	//1,强插项
	activeItems := m.GetActiveFilterFlag(ctx, in.GetActiveFilterItem())
	//sortedItems = append(sortedItems, activeItems...)
	log.DebugWithCtx(ctx, "MiniGameZoneFilter GetActiveFilterFlag uid(%d) in(%s) activeItems(%v)", uid, in.String(), activeItems)

	//2,常玩分类,降级处理不抛出error
	newCtx, cancel := context.WithTimeout(ctx, 500*time.Millisecond)
	defer cancel()
	diyItems, lowErr := m.GetDIYFilterItem(newCtx, uid)
	if lowErr != nil {
		log.ErrorWithCtx(ctx, "MiniGameZoneFilter GetGameCardItem serviceInfo(%s) in(%s) err(%v)", serviceInfo.String(), in.String(), lowErr)
	}
	//sortedItems = append(sortedItems, diyItems...)
	log.DebugWithCtx(ctx, "MiniGameZoneFilter GetDIYFilterFlag uid(%d) in(%s) diyItems(%v)", uid, in.String(), diyItems)

	//3, 默认配置,不会有error
	defaultItems := m.commonHandler.GetDefaultFilterItem(channel_play.FilterEntrance_MINIGAME_ZONE)
	//sortedItems = append(sortedItems, defaultItems...)
	log.DebugWithCtx(ctx, "MiniGameZoneFilter GetDefaultFilterFlag uid(%d) in(%s) defaultItems(%v)", uid, in.String(), defaultItems)
	sortedItems := make([]*channel_play.CommonBusinessFlag, 0, len(activeItems)+len(diyItems)+len(defaultItems))
	sortedItems = append(sortedItems, activeItems...)
	sortedItems = append(sortedItems, diyItems...)
	sortedItems = append(sortedItems, defaultItems...)
	items = m.commonHandler.GenFilterItemWithDisplayName(ctx, sortedItems, false)
	return
}

// 获取强插项
func (m *MiniGameZoneFilter) GetActiveFilterFlag(ctx context.Context, activeFilterItem []*channel_play.CommonBusinessFlag) (
	items []*channel_play.CommonBusinessFlag) {
	items = make([]*channel_play.CommonBusinessFlag, 0, 1)
	//休闲互动专区只有更多页面点击的强插,一个强插位
	for _, v := range activeFilterItem {
		if v.FilterItemType == channel_play.FilterItemType_GAME_CATEGORY && m.isMiniGameZoneCategory(ctx, v.GetGameBusinessId()) {
			continue
		} else {
			items = append(items, v)
			return
		}
	}
	return
}

// 判断是否休闲互动或者趣味玩法
func (m *MiniGameZoneFilter) isMiniGameZoneCategory(ctx context.Context, categoryId uint32) bool {
	for _, v := range conf.ChannelPlayLogicConfig.GetFilterEntranceCategoryMap(ctx, channel_play.FilterEntrance_MINIGAME_ZONE) {
		if v == categoryId {
			return true
		}
	}
	return false
}

func (m *MiniGameZoneFilter) GetDIYFilterItem(ctx context.Context, uid uint32) (flags []*channel_play.CommonBusinessFlag, err error) {

	diyItems, err := m.commonHandler.GetDIYFilterItem(ctx, uid, channel_play.FilterEntrance_MINIGAME_ZONE)
	if err != nil {
		log.ErrorWithCtx(ctx, "MiniGameZoneFilter GetDIYFilterItem uid(%d) err(%v)", uid, err)
		return diyItems, err
	}
	if len(diyItems) == 0 {
		defaultFlags := m.getDefaultDIYFlag()
		lowErr := m.commonHandler.SetDIYFilterItem(ctx, uid, channel_play.FilterEntrance_MINIGAME_ZONE, defaultFlags, false)
		if lowErr != nil {
			//没写入不报错，下次再写
			log.ErrorWithCtx(ctx, "MiniGameZoneFilter GetDIYFilterItem itemNum 0 SetDIYFilterItem(%v) err(%v)", defaultFlags, lowErr)
		}
		return defaultFlags, nil
	}
	res := make([]*channel_play.CommonBusinessFlag, 0, len(diyItems))
	for _, v := range diyItems {
		//休闲互动或趣味玩法分类需要固定在筛选器最后两位，在这里剔除，最后在默认配置append
		if v.FilterItemType == channel_play.FilterItemType_GAME_CATEGORY && m.isMiniGameZoneCategory(ctx, v.GetGameBusinessId()) {
			continue
		}
		res = append(res, v)
	}
	return res, nil

}

func (m *MiniGameZoneFilter) getDefaultDIYFlag() []*channel_play.CommonBusinessFlag {
	defaultTabs := conf.ChannelPlayLogicConfig.GetMiniGameDefaultDIYTab()
	res := make([]*channel_play.CommonBusinessFlag, 0, len(defaultTabs))
	for _, t := range defaultTabs {
		res = append(res, &channel_play.CommonBusinessFlag{
			GameBusinessId: t,
			FilterItemType: channel_play.FilterItemType_GAME_TAB,
		})
	}
	return res
}

// 根据入口生成筛选器外显名,并转换成FilterItem结构
/*
func (m *MiniGameZoneFilter) GenFilterItemWithDisplayName(ctx context.Context, sortedFlagItem []*channel_play.CommonBusinessFlag) (items []*channel_play.FilterItem) {
	items = make([]*channel_play.FilterItem, 0, len(sortedFlagItem))
	for _, v := range sortedFlagItem {
		item := &channel_play.FilterItem{
			CommonBusinessFlag: v,
		}
		if v.FilterItemType == channel_play.FilterItemType_GAME_TAB {
			if tabInfo, ok := cache.GetTabIdCache()[v.GameBusinessId]; ok {
				item.DisplayName = tabInfo.GetName()
			} else {
				log.WarnWithCtx(ctx, "MiniGameZoneFilter GenFilterItemWithDisplayName invalid flag(%s)", v.String())
				continue
			}
		} else if v.FilterItemType == channel_play.FilterItemType_GAME_CATEGORY {
			if categoryInfo, ok := cache.GetCategoryIdMap()[v.GetGameBusinessId()]; ok {
				item.DisplayName = categoryInfo.GetTitle()
			} else {
				log.WarnWithCtx(ctx, "MiniGameZoneFilter GenFilterItemWithDisplayName invalid flag(%s)", v.String())
				continue
			}
		}
		items = append(items, item)
	}
	return items
}
*/
