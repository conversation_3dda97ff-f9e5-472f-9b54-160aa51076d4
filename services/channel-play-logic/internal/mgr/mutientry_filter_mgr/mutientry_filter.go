package mutientry_filter_mgr

import (
	"context"
	"github.com/gookit/goutil/timex"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/device_id"
	account "golang.52tt.com/clients/account-go"
	device_info_service "golang.52tt.com/clients/datacenter/device-info-service"
	rcmdChannelLabel "golang.52tt.com/clients/topic-channel/rcmd-channel-label"
	"golang.52tt.com/kaihei-pkg/supervision"
	"golang.52tt.com/pkg/abtest"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/marketid_helper"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/protocol/common/status"
	device_pb "golang.52tt.com/protocol/datacenter/device-info-service"
	music_topic_channel "golang.52tt.com/protocol/services/music-topic-channel"
	rcmdPb "golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/block_mgr"
	"strconv"
	"time"
)

const registerDiyItemExperimentGroup = "test"

type MutiEntryFilterMgr struct {
	SupervisorInst     *supervision.Supervisory
	MixHomePageFilter  MutiEntryFilter
	GameZoneFilter     *GameZoneFilter
	MiniGameZoneFilter MutiEntryFilter

	commonHandler          *CommonHandler
	rcmdChannelLabelClient *rcmdChannelLabel.Client
	baseBlockHandler       *block_mgr.BaseBlockHandler
	abTestClient           abtest.IABTestClient
	deviceInfoCli          device_info_service.IClient
	accountClient          account.IClient
	musicTopicClient       music_topic_channel.MusicChannelClient
}

type MutiEntryFilter interface {
	// 根据入口生成筛选器外显名,并转换成FilterItem结构
	//GenFilterItemWithDisplayName(ctx context.Context, sortedFlagItem []*channel_play.CommonBusinessFlag) (items []*channel_play.FilterItem)

	//获取排好序的筛选项
	GetSortedFilterItems(ctx context.Context, serviceInfo *grpc.ServiceInfo, in *channel_play.GetFilterItemByEntranceReq) (
		items []*channel_play.FilterItem, err error)
	//获取强插项
	//GetActiveFilterFlag(ctx context.Context, uid uint32, activeFilterItem []*channel_play.CommonBusinessFlag) (
	//items []*channel_play.CommonBusinessFlag)
}

func NewMutiEntryFilterMgr(supervisor *supervision.Supervisory, homePageFilter *MixHomePageFilter, gameZoneFilter *GameZoneFilter,
	miniGameZoneFilter *MiniGameZoneFilter, rcmdChannelLabelClient *rcmdChannelLabel.Client, baseBlockHandler *block_mgr.BaseBlockHandler,
	handler *CommonHandler, abTestClient abtest.IABTestClient, deviceInfoCli device_info_service.IClient,
	accountClient account.IClient, musicTopicClient music_topic_channel.MusicChannelClient) *MutiEntryFilterMgr {
	return &MutiEntryFilterMgr{
		SupervisorInst:     supervisor,
		MixHomePageFilter:  homePageFilter,
		GameZoneFilter:     gameZoneFilter,
		MiniGameZoneFilter: miniGameZoneFilter,

		commonHandler:          handler,
		rcmdChannelLabelClient: rcmdChannelLabelClient,
		baseBlockHandler:       baseBlockHandler,
		abTestClient:           abTestClient,
		deviceInfoCli:          deviceInfoCli,
		accountClient:          accountClient,
		musicTopicClient:       musicTopicClient,
	}
}

func (m *MutiEntryFilterMgr) GetFilterItemByEntrance(ctx context.Context, serviceInfo *grpc.ServiceInfo,
	in *channel_play.GetFilterItemByEntranceReq) (items []*channel_play.FilterItem, extraItems []*channel_play.FilterItem, isShieldWord bool, err error) {
	items = make([]*channel_play.FilterItem, 0)
	extraItems = make([]*channel_play.FilterItem, 0)
	var flags []*channel_play.FilterItem
	entrance := in.GetFilterEntrance()
	//根据入口调用不同实体类方法获取筛选项
	switch entrance {
	//一级首页
	case channel_play.FilterEntrance_HOME_PAGE, channel_play.FilterEntrance_HOME_PAGE_ADD_MUSIC_FILTER:
		flags, err = m.MixHomePageFilter.GetSortedFilterItems(ctx, serviceInfo, in)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetFilterItemByEntrance in(%s) serviceInfo(%s) err(%v)", in.String(), serviceInfo.String(), err)
			return items, extraItems, isShieldWord, err
		}
	//游戏专区
	case channel_play.FilterEntrance_GAME_ZONE:
		flags, err = m.GameZoneFilter.GetSortedFilterItems(ctx, serviceInfo, in)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetFilterItemByEntrance in(%s) serviceInfo(%s) err(%v)", in.String(), serviceInfo.String(), err)
			return items, extraItems, isShieldWord, err
		}
		extraItems, err = m.GameZoneFilter.GetGameZoneExtraFilterItems(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetFilterItemByEntrance in(%s) serviceInfo(%s) err(%v)", in.String(), serviceInfo.String(), err)
			return items, extraItems, isShieldWord, err
		}
	//休闲互动专区
	case channel_play.FilterEntrance_MINIGAME_ZONE:
		flags, err = m.MiniGameZoneFilter.GetSortedFilterItems(ctx, serviceInfo, in)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetFilterItemByEntrance in(%s) serviceInfo(%s) err(%v)", in.String(), serviceInfo.String(), err)
			return items, extraItems, isShieldWord, err
		}
	}
	//log.DebugWithCtx(ctx, "GetFilterItemByEntrance in(%s) flags(%v)", in.String(), flags)

	//去重
	finishRmDuplicateItems := m.removeRepeatedItems(ctx, flags)
	//log.DebugWithCtx(ctx, "GetFilterItemByEntrance after removeRepeatedItems(%v)", finishRmDuplicateItems)
	//根据入口,筛选项版本补全信息
	fullInfoItems, needFilteredMusicItem, isShieldWord, err := m.genFilterItem(ctx, serviceInfo.UserID, finishRmDuplicateItems, entrance, in.GetTabItemVersion(), serviceInfo, in.GetBrowseLabelsMap())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFilterItemByEntrance genFilterItem in(%s) serviceInfo(%s) err(%v)", in.String(), serviceInfo.String(), err)
		return items, extraItems, isShieldWord, err
	}
	//过滤
	items = m.skipInvalidItems(ctx, fullInfoItems, serviceInfo, in.GetChannelPkg(), needFilteredMusicItem)
	//log.DebugWithCtx(ctx, "GetFilterItemByEntrance after skipInvalidItems(%v)", items)
	return items, extraItems, isShieldWord, nil
}

func (m *MutiEntryFilterMgr) GetDIYFilterByEntrance(ctx context.Context, serviceInfo *grpc.ServiceInfo,
	in *channel_play.GetDIYFilterByEntranceReq) (items []*channel_play.DIYItem, err error) {
	entrance := in.GetFilterEntrance()
	uid := serviceInfo.UserID
	flags, err := m.commonHandler.GetDIYFilterItem(ctx, uid, entrance)
	if err != nil {
		log.ErrorWithCtx(ctx, "MutiEntryFilterMgr GetDIYFilterByEntrance serviceInfo(%s) in(%s) err(%v)", serviceInfo.String(), in.String(), err)
		return items, err
	}
	// 过滤
	flags = m.skipInvalidFlags(ctx, flags, serviceInfo, in.GetChannelPkg())
	//获取筛选项名称
	items, err = m.getDIYFilterDisplayName(ctx, flags)
	if err != nil {
		log.ErrorWithCtx(ctx, "MutiEntryFilterMgr getDIYFilterDisplayName serviceInfo(%s) in(%s) items(%v) err(%v)", serviceInfo.String(), in.String(), items, err)
		return nil, err
	}
	return items, nil
}

func (m *MutiEntryFilterMgr) getDIYFilterDisplayName(ctx context.Context, flags []*channel_play.CommonBusinessFlag) ([]*channel_play.DIYItem, error) {
	filterIds := make([]string, 0)
	res := make([]*channel_play.DIYItem, 0, len(flags))
	for _, v := range flags {
		if v.GetFilterItemType() == channel_play.FilterItemType_MUSIC_ITEM {
			filterIds = append(filterIds, v.GetMusicBusinessId())
		}
	}
	var info map[string]*music_topic_channel.MusicFilterItem
	var err error
	if len(filterIds) > 0 {
		info, err = m.commonHandler.GetMusicFilterItemInfo(ctx, filterIds)
		if err != nil {
			log.ErrorWithCtx(ctx, "MutiEntryFilterMgr getDIYFilterDisplayName flags(%v) filterIds(%v) err(%v)", flags, filterIds, err)
			return nil, err
		}
	}
	for _, v := range flags {
		if v.GetFilterItemType() == channel_play.FilterItemType_MUSIC_ITEM {
			item, ok := info[v.GetMusicBusinessId()]
			if !ok {
				continue
			}
			res = append(res, &channel_play.DIYItem{
				DiyItem:     v,
				DisplayName: item.GetName(),
			})
		} else if v.GetFilterItemType() == channel_play.FilterItemType_GAME_TAB {
			tabInfo, ok := cache.GetTabInfoCache().GetTabIdCache()[v.GetGameBusinessId()]
			if !ok {
				continue
			}
			res = append(res, &channel_play.DIYItem{
				DiyItem:     v,
				DisplayName: tabInfo.GetName(),
			})
		} else if v.GetFilterItemType() == channel_play.FilterItemType_GAME_CATEGORY {
			categoryInfo, ok := cache.GetCategoryIdMap()[v.GetGameBusinessId()]
			if !ok {
				continue
			}
			res = append(res, &channel_play.DIYItem{
				DiyItem:     v,
				DisplayName: categoryInfo.GetTitle(),
			})
		} else {
			continue
		}
	}
	return res, nil
}

func (m *MutiEntryFilterMgr) SetDIYFilterByEntrance(ctx context.Context, serviceInfo *grpc.ServiceInfo,
	in *channel_play.SetDIYFilterByEntranceReq) (isInAbtest bool, needJumpTabId uint32, err error) {
	entrance := in.GetFilterEntrance()
	uid := serviceInfo.UserID
	var isIncr bool
	var items []*channel_play.CommonBusinessFlag
	if entrance == channel_play.FilterEntrance_FILTER_ENTRANCE_REGISTER {
		entrance = channel_play.FilterEntrance_HOME_PAGE
		isIncr = true
		isInAbtest = m.IsInRegisterDiyItemAbtest(ctx, serviceInfo)
		if !isInAbtest {
			return isInAbtest, 0, nil
		}
		items = m.genRegisterSourceItems(in.GetItems())
		if len(items) == 0 {
			return isInAbtest, 0, nil
		}
		needJumpTabId = items[0].GetGameBusinessId()
	} else {
		isIncr = false
		items = in.GetItems()
	}

	err = m.commonHandler.SetDIYFilterItem(ctx, uid, entrance, items, isIncr)
	if err != nil {
		log.ErrorWithCtx(ctx, "MutiEntryFilterMgr SetDIYFilterByEntrance serviceInfo(%s) in(%s) err(%v)", serviceInfo.String(), in.String(), err)
		return isInAbtest, 0, err
	}
	return isInAbtest, needJumpTabId, nil
}

func (m *MutiEntryFilterMgr) genRegisterSourceItems(inItems []*channel_play.CommonBusinessFlag) []*channel_play.CommonBusinessFlag {
	items := make([]*channel_play.CommonBusinessFlag, 0, len(inItems))

	for _, i := range inItems {
		if i.GetFilterItemType() == channel_play.FilterItemType_GAME_TAB {
			tabInfo := cache.GetTabInfoCache().GetTabInfoCacheById(i.GetGameBusinessId())
			if tabInfo != nil && tabInfo.GetCategoryMapping() == uint32(topic_channel.CategoryType_Gangup_type) {
				items = append(items, i)
			}
		} else if i.GetFilterItemType() == channel_play.FilterItemType_GAME_CATEGORY {
			categoryInfo := cache.GetTabInfoCache().GetCategoryIdMap()[i.GetGameBusinessId()]
			if categoryInfo != nil && categoryInfo.GetSpecialCategoryMapping() == uint32(topic_channel.CategoryType_Gangup_type) {
				items = append(items, i)
			}
		} else {
			continue
		}
	}
	return items
}

func (m *MutiEntryFilterMgr) IsInRegisterDiyItemAbtest(ctx context.Context, svrInfo *grpc.ServiceInfo) bool {
	abTestCfg := conf.ChannelPlayLogicConfig.GetRegisterDiyItemTestConfig()
	if abTestCfg == nil {
		return false
	}

	isInAbtest, err := m.abTestClient.IsInAbTestByKV(ctx, svrInfo.DeviceID, uint32(svrInfo.ClientType),
		abTestCfg.LayerTag, abTestCfg.ArgvName, registerDiyItemExperimentGroup)
	if err != nil {
		log.ErrorWithCtx(ctx, "IsInRegisterDiyItemAbtest GetUserTestListInLayerTag svrInfo(%s) err: %v",
			svrInfo.String(), err)
		return false
	}
	return isInAbtest

}

// 过滤被监管的tab
func (m *MutiEntryFilterMgr) skipInvalidItems(ctx context.Context, items []*channel_play.FilterItem,
	serviceInfo *grpc.ServiceInfo, channelPkg string, needFilteredMusicItem map[string]bool) (filteredItems []*channel_play.FilterItem) {
	filteredItems = make([]*channel_play.FilterItem, 0, len(items))
	tabIdMap := cache.GetTabInfoCache().GetTabIdCache()
	// 监管配置过滤
	supConfInst := &supervision.SupervisoryConf{
		RegisterLimitTime:      conf.PublicSwitchConfig.GetRegisterLimitTime(),
		UserLevelLimit:         conf.PublicSwitchConfig.GetUserLevelLimit(),
		RealNameStandardStatus: conf.PublicSwitchConfig.GetRealNameStandardStatus(),
	}
	tabFilter, categoryFilter := m.SupervisorInst.GetFilterMap(ctx, serviceInfo, channelPkg, supConfInst)

	for _, item := range items {
		filterItemType := item.GetCommonBusinessFlag().GetFilterItemType()
		gameId := item.GetCommonBusinessFlag().GameBusinessId

		if filterItemType == channel_play.FilterItemType_GAME_TAB && gameId == 0 {
			filteredItems = append(filteredItems, item)
		} else if filterItemType == channel_play.FilterItemType_MUSIC_ITEM ||
			filterItemType == channel_play.FilterItemType_MUSIC_FILTER {
			if needFilteredMusicItem[item.GetCommonBusinessFlag().MusicBusinessId] {
				continue
			}
			filteredItems = append(filteredItems, item)

		} else if filterItemType == channel_play.FilterItemType_GAME_TAB {
			if conf.PublicSwitchConfig.GetChannelListNoFilterTabSwitch(gameId) {
				// 特殊玩法不展示（新旧扩列）
				continue
			}
			if tabInfo, ok := tabIdMap[gameId]; ok {
				// 监管配置
				if tabFilter[tabInfo.GetId()] || categoryFilter[tabInfo.GetCategoryId()] {
					log.InfoWithCtx(ctx, "skipInvalidItems supervise id:%d", gameId)
					continue
				}
				if m.SupervisorInst.NewHomePageFilterStrategy(tabInfo, serviceInfo, cache.GetWhiteList()) {
					log.InfoWithCtx(ctx, "skipInvalidItems Strategy id:%d", gameId)
					continue
				}
				filteredItems = append(filteredItems, item)
			}
		} else if filterItemType == channel_play.FilterItemType_GAME_CATEGORY {
			// 监管配置
			if categoryFilter[gameId] {
				log.WarnWithCtx(ctx, "skipInvalidItems categoryFilter gameId:%d", gameId)
				continue
			}
			filteredCategoryTabItem := make([]*channel_play.TabItem, 0, len(item.GetCategoryFilterItem().GetTabItems()))
			for _, t := range item.GetCategoryFilterItem().GetTabItems() {
				if tabFilter[t.GetTabId()] {
					log.WarnWithCtx(ctx, "skipInvalidItems tabFilter tabId:%d ", t.GetTabId())
					continue
				}
				tabInfo, ok := tabIdMap[t.GetTabId()]
				if !ok || m.SupervisorInst.NewHomePageFilterStrategy(tabInfo, serviceInfo, cache.GetWhiteList()) {
					log.WarnWithCtx(ctx, "skipInvalidItems Strategy tabId: %d ", t.GetTabId())
					continue
				}
				filteredCategoryTabItem = append(filteredCategoryTabItem, t)
			}
			if len(filteredCategoryTabItem) > 0 {
				item.CategoryFilterItem.TabItems = filteredCategoryTabItem
				filteredItems = append(filteredItems, item)
			}
		} else {
			log.WarnWithCtx(ctx, "skipInvalidItems invalid item%v", item)
		}
	}

	return
}

// 去重, 并且返回需要补足筛选器信息的开黑tab，category
func (m *MutiEntryFilterMgr) removeRepeatedItems(ctx context.Context, items []*channel_play.FilterItem) (result []*channel_play.FilterItem) {
	result = make([]*channel_play.FilterItem, 0, len(items))
	//去重
	//needTabInfoMap = make(map[*channel_play.CommonBusinessFlag]bool, 0)
	//needCategoryItemMap = make(map[*channel_play.CommonBusinessFlag]bool, 0)
	musicTmpMap := make(map[string]bool)
	tabTmpMap := make(map[uint32]bool)
	categoryTmpMap := make(map[uint32]bool)
	for _, item := range items {
		filterItemType := item.GetCommonBusinessFlag().FilterItemType
		musicId := item.GetCommonBusinessFlag().MusicBusinessId
		if filterItemType == channel_play.FilterItemType_MUSIC_ITEM || filterItemType == channel_play.FilterItemType_MUSIC_FILTER {
			if musicId != "" {
				if musicTmpMap[musicId] {
					continue
				}
				musicTmpMap[musicId] = true
				result = append(result, item)
			} else {
				log.WarnWithCtx(ctx, "MutiEntryFilterMgr removeRepeatedItems musicId is empty for item(%v)", item)
			}
		} else {
			if (filterItemType == channel_play.FilterItemType_GAME_TAB && tabTmpMap[item.GetCommonBusinessFlag().GetGameBusinessId()]) ||
				(filterItemType == channel_play.FilterItemType_GAME_CATEGORY && categoryTmpMap[item.GetCommonBusinessFlag().GetGameBusinessId()]) {
				continue
			}
			if filterItemType == channel_play.FilterItemType_GAME_TAB {
				//needTabInfoMap[item.CommonBusinessFlag] = true
				tabTmpMap[item.CommonBusinessFlag.GetGameBusinessId()] = true
			} else if filterItemType == channel_play.FilterItemType_GAME_CATEGORY {
				//needCategoryItemMap[item.CommonBusinessFlag] = true
				categoryTmpMap[item.CommonBusinessFlag.GetGameBusinessId()] = true
			} else {
				log.ErrorWithCtx(ctx, "MutiEntryFilterMgr removeRepeatedItems invalid item(%v)", item)
				continue
			}
			result = append(result, item)
		}

	}
	return result
}

func (m *MutiEntryFilterMgr) genFilterIds(ctx context.Context, sortedItems []*channel_play.FilterItem) ([]uint32, []string, map[uint32]int, map[string]int) {
	tabIds := make([]uint32, 0)
	//key为tabId,value为sortedItem的索引
	IndexMap := make(map[uint32]int)

	filterIds := make([]string, 0)
	musicIndexMap := make(map[string]int)
	for i, item := range sortedItems {
		itemType := item.GetCommonBusinessFlag().GetFilterItemType()
		if itemType == channel_play.FilterItemType_GAME_CATEGORY {
			gameId := item.GetCommonBusinessFlag().GetGameBusinessId()
			info, err := m.genCategoryItem(ctx, gameId)
			if err != nil {
				continue
			}
			item.CategoryFilterItem = info
		} else if itemType == channel_play.FilterItemType_GAME_TAB {
			gameId := item.GetCommonBusinessFlag().GetGameBusinessId()
			//tabItem需要批量生成
			tabIds = append(tabIds, gameId)
			IndexMap[gameId] = i
		} else if itemType == channel_play.FilterItemType_MUSIC_FILTER || itemType == channel_play.FilterItemType_MUSIC_ITEM {
			//tabItem需要批量生成
			filterId := item.GetCommonBusinessFlag().GetMusicBusinessId()
			filterIds = append(filterIds, filterId)
			musicIndexMap[filterId] = i
		}
	}
	return tabIds, filterIds, IndexMap, musicIndexMap
}

func (m *MutiEntryFilterMgr) GetAbResult(ctx context.Context, serviceInfo *grpc.ServiceInfo, isOnlyShieldWord bool) (bool, bool, error) {
	newCtx, cancel := context.WithTimeout(ctx, 1000*time.Millisecond)
	defer cancel()
	var isInAbtest bool
	var isInShieldWordAbtest bool
	deviceId, err := device_id.ToClientDeviceId(device_id.ToDeviceHexId(serviceInfo.DeviceID, true), uint32(serviceInfo.ClientType))
	if err != nil {
		log.ErrorWithCtx(newCtx, "GetAbResult device_id.ToClientDeviceId deviceID(%s) err:%v", serviceInfo.DeviceID, err)
		return isInAbtest, isInShieldWordAbtest, err
	}
	// 查询用户获客状态
	userType, err := m.deviceInfoCli.GetUserTypeByDeviceState(newCtx, &device_pb.DeviceInfoRequestData{
		AppId:    marketid_helper.GetAppName(serviceInfo.MarketID),
		DeviceId: deviceId,
		Uid:      strconv.FormatInt(int64(serviceInfo.UserID), 10),
	})
	if err != nil {
		log.ErrorWithCtx(newCtx, "GetAbResult GetUserTypeByDeviceState err:%v, serviceInfo:%s", err, serviceInfo.String())
		return isInAbtest, isInShieldWordAbtest, err
	}
	user, err := m.accountClient.GetUserByUid(newCtx, serviceInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAbResult GetUserByUid uid(%d) err: %v", serviceInfo.UserID, err)
		return isInAbtest, isInShieldWordAbtest, err
	}

	if !isOnlyShieldWord {
		// 查询AB实验结果 筛选列表
		abCfg := conf.ChannelPlayLogicConfig.GetHomePageFilterTestConfig()
		if abCfg == nil {
			log.WarnWithCtx(newCtx, "GetAbResult abCfg is nil, uid:%d", serviceInfo.UserID)
			return isInAbtest, isInShieldWordAbtest, err
		}
		argKey := abCfg.ActiveKey
		if timex.FromUnix(int64(user.GetRegisteredAt())).After(timex.TodayStart()) || userType == device_info_service.Recall || userType == device_info_service.Pull {
			argKey = abCfg.HuoKeKey
		}
		if result, err := m.abTestClient.GetUidTestArgVal(newCtx, serviceInfo.UserID, argKey); err != nil {
			log.ErrorWithCtx(newCtx, "GetAbResult GetUidTestArgVal uid(%d,%s) abCfg(%v) err: %v", serviceInfo.UserID, deviceId, abCfg, err)
			//return isInAbtest, isInShieldWordAbtest, err
		} else {
			isInAbtest = result == abCfg.ExpectValue
			log.InfoWithCtx(newCtx, "GetAbResult GetUidTestArgVal uid(%d,%s) abCfg(%v) result: %s", serviceInfo.UserID, deviceId, abCfg, result)
			//return isInAbtest, nil
		}
	}

	// 查询AB实验结果 筛选列表
	shieldWordAbCfg := conf.ChannelPlayLogicConfig.GetShieldWordTestConfig()
	if shieldWordAbCfg == nil {
		log.WarnWithCtx(newCtx, "GetAbResult abCfg is nil, uid:%d", serviceInfo.UserID)
		return isInAbtest, isInShieldWordAbtest, err
	}
	shieldWordArgKey := shieldWordAbCfg.ActiveKey
	if timex.FromUnix(int64(user.GetRegisteredAt())).After(timex.TodayStart()) || userType == device_info_service.Recall || userType == device_info_service.Pull {
		shieldWordArgKey = shieldWordAbCfg.HuoKeKey
	}
	if result, err := m.abTestClient.GetUidTestArgVal(newCtx, serviceInfo.UserID, shieldWordArgKey); err != nil {
		log.ErrorWithCtx(newCtx, "GetAbResult GetUidTestArgVal uid(%d,%s) abCfg(%v) shieldWordArgKey(%s) err: %v", serviceInfo.UserID, deviceId, shieldWordAbCfg, shieldWordArgKey, err)
		return isInAbtest, isInShieldWordAbtest, err
	} else {
		isInShieldWordAbtest = result == shieldWordAbCfg.ExpectValue
		log.InfoWithCtx(newCtx, "GetAbResult GetUidTestArgVal uid(%d,%s) abCfg(%v) shieldWordArgKey(%s) result: %s", serviceInfo.UserID, deviceId, shieldWordAbCfg, shieldWordArgKey, result)
		return isInAbtest, isInShieldWordAbtest, err
	}
}

type labelParams struct {
	tabIds                 []uint32
	filterIds              []string
	gameTabIndexMap        map[uint32]int
	musicIndexMap          map[string]int
	needHandleMusicItem    bool
	needHandleMinorityGame bool

	tabItemVersion  channel_play.TabItemVersion
	serviceInfo     *grpc.ServiceInfo
	browseLabelsMap map[uint32]*topic_channel.BrowseLabel
}

// 补足完整filterItem信息
func (m *MutiEntryFilterMgr) genFilterItem(ctx context.Context, uid uint32, sortedItems []*channel_play.FilterItem,
	source channel_play.FilterEntrance, tabItemVersion channel_play.TabItemVersion, serviceInfo *grpc.ServiceInfo,
	browseLabelsMap map[uint32]*topic_channel.BrowseLabel) ([]*channel_play.FilterItem, map[string]bool, bool, error) {
	needFilteredMusicItem := make(map[string]bool)

	if len(sortedItems) == 0 {
		log.ErrorWithCtx(ctx, "UserLine Err:MutiEntryFilterMgr empty filter uid(%d) source(%v)", uid, source)
		return sortedItems, needFilteredMusicItem, false, nil
	}

	tabIds, filterIds, IndexMap, musicIndexMap := m.genFilterIds(ctx, sortedItems)
	if len(tabIds) == 0 && len(filterIds) == 0 {
		log.WarnWithCtx(ctx, "UserLine Err:MutiEntryFilterMgr empty tabIds and filterIds uid(%d) source(%v)", uid, source)
		return sortedItems, needFilteredMusicItem, false, nil
	}
	needHandleMusicItem := false
	needHandleMinorityGame := false
	isShieldWord := false
	var err error
	if source == channel_play.FilterEntrance_HOME_PAGE_ADD_MUSIC_FILTER {
		needHandleMusicItem, isShieldWord, err = m.GetAbResult(ctx, serviceInfo, false)
		if err != nil {
			log.ErrorWithCtx(ctx, "genFilterItem GetAbResult uid:%d err:%v", uid, err)
		}
		needHandleMinorityGame = true
	} else if source == channel_play.FilterEntrance_HOME_PAGE || source == channel_play.FilterEntrance_GAME_ZONE || source == channel_play.FilterEntrance_MINIGAME_ZONE {
		_, isShieldWord, err = m.GetAbResult(ctx, serviceInfo, true)
		if err != nil {
			log.ErrorWithCtx(ctx, "genFilterItem GetAbResult uid:%d err:%v", uid, err)
		}
	}

	param := &labelParams{
		tabIds:                 tabIds,
		filterIds:              filterIds,
		gameTabIndexMap:        IndexMap,
		musicIndexMap:          musicIndexMap,
		needHandleMinorityGame: needHandleMinorityGame,
		needHandleMusicItem:    needHandleMusicItem,
		browseLabelsMap:        browseLabelsMap,
		serviceInfo:            serviceInfo,
		tabItemVersion:         tabItemVersion,
	}

	var gameLabelAndBlockMap map[uint32]*block_mgr.BlockAndLabel
	var musicLabelsMap map[string]*block_mgr.BlockAndLabel
	var musicItemInfoMap map[string]*music_topic_channel.MusicFilterItemV2

	gameLabelAndBlockMap, musicLabelsMap, musicItemInfoMap, err = m.batGenLabelInfo(ctx, param)
	if err != nil {
		log.ErrorWithCtx(ctx, "MutiEntryFilterMgr enFilterItem batGenLabelInfo tabIds(%v) err(%v)", tabIds, err)
		return sortedItems, needFilteredMusicItem, isShieldWord, err
	}

	tabItemMap := m.genTabInfos(ctx, serviceInfo, tabIds, IndexMap, gameLabelAndBlockMap)
	for index, tabItem := range tabItemMap {
		sortedItems[index].TabFilterItem = tabItem
	}
	if needHandleMusicItem {
		musicItemMap := m.genFilterInfos(ctx, filterIds, musicLabelsMap, musicItemInfoMap)
		for filterId, index := range musicIndexMap {
			musicItem, ok := musicItemMap[filterId]
			if ok {
				if musicItem.GetEnable() {
					sortedItems[index].DisplayName = musicItem.GetTabName()
					sortedItems[index].TabFilterItem = musicItem
					sortedItems[index].CommonBusinessFlag.FilterItemType = channel_play.FilterItemType_MUSIC_FILTER
					sortedItems[index].CommonBusinessFlag.GameBusinessId = musicItem.GetTabId()
				} else {
					sortedItems[index].CommonBusinessFlag.FilterItemType = channel_play.FilterItemType_MUSIC_ITEM
				}
			} else {
				needFilteredMusicItem[filterId] = true
			}
		}
	} else {
		for _, item := range sortedItems {
			if item.GetCommonBusinessFlag().GetFilterItemType() == channel_play.FilterItemType_MUSIC_FILTER {
				item.CommonBusinessFlag.FilterItemType = channel_play.FilterItemType_MUSIC_ITEM
			}
		}
	}

	extraItems := m.genExtraItems(source)
	if extraItems != nil {
		sortedItems = append(extraItems, sortedItems...)
	}

	//游戏专区返回筛选器数量有限制
	if source == channel_play.FilterEntrance_GAME_ZONE {
		categoryIds := conf.ChannelPlayLogicConfig.GetFilterEntranceCategoryMap(ctx, channel_play.FilterEntrance_GAME_ZONE)
		categoryId := uint32(1)
		if len(categoryIds) > 0 {
			categoryId = categoryIds[0]
		}
		if categoryInfo, ok := cache.GetCategoryIdMap()[categoryId]; ok {
			if int(categoryInfo.GetCanSelectNum())+2 < len(sortedItems) {
				return sortedItems[:int(categoryInfo.GetCanSelectNum())+2], needFilteredMusicItem, isShieldWord, nil
			} else {
				return sortedItems, needFilteredMusicItem, isShieldWord, nil
			}
		}
	}
	if conf.ChannelPlayLogicConfig.GetLogSwitch() {
		log.InfoWithCtx(ctx, "MutiEntryFilterMgr genFilterItem len(sortedItems):%d needFilteredMusicItem:%v isShieldWord:%v ",
			len(sortedItems), needFilteredMusicItem, isShieldWord)
	}
	return sortedItems, needFilteredMusicItem, isShieldWord, nil
}

func (m *MutiEntryFilterMgr) genExtraItems(source channel_play.FilterEntrance) []*channel_play.FilterItem {
	if source == channel_play.FilterEntrance_HOME_PAGE || source == channel_play.FilterEntrance_MINIGAME_ZONE ||
		source == channel_play.FilterEntrance_HOME_PAGE_ADD_MUSIC_FILTER {
		rcmdTab := []*channel_play.FilterItem{
			{
				CommonBusinessFlag: &channel_play.CommonBusinessFlag{
					GameBusinessId: 0,
					FilterItemType: channel_play.FilterItemType_GAME_TAB,
				},
				DisplayName: "推荐",
				//TabFilterItem 为nil 客户端会过滤不展示该筛选项，初始化一下
				TabFilterItem: &channel_play.TabItem{},
			},
		}
		return rcmdTab
	}
	return nil
}

// categoryItem
func (m *MutiEntryFilterMgr) genCategoryItem(ctx context.Context, categoryId uint32) (*channel_play.CategoryItem, error) {
	categoryInfo, ok := cache.GetCategoryIdMap()[categoryId]
	if !ok {
		log.WarnWithCtx(ctx, "UserLine Time Err: MutiEntryFilterMgr genCategoryItem no info categoryId(%d) ", categoryId)
		return nil, protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError, "genCategoryItem no info categoryId")
	}
	res := &channel_play.CategoryItem{
		CategoryId:   categoryId,
		CategoryName: categoryInfo.GetTitle(),
	}
	categoryWithTabs := cache.GetTabsOfCategoryCache()[categoryId]
	tabItems := make([]*channel_play.TabItem, 0, len(categoryWithTabs))
	for _, tabInfo := range categoryWithTabs {
		tabItems = append(tabItems, &channel_play.TabItem{
			TabId:        tabInfo.GetId(),
			TabName:      tabInfo.GetName(),
			CategoryType: tabInfo.GetCategoryMapping(),
		})
	}
	res.TabItems = tabItems
	return res, nil

}

func (m *MutiEntryFilterMgr) GetGameLabelFilterMap(ctx context.Context, tabIds []uint32, serviceInfo *grpc.ServiceInfo) (
	gameLabelFilterMap map[uint32][]*channel_play.GameLabelFilter) {
	var tabItems []*tabPB.TabGameLabelItem
	tabLabelItemMap := cache.GetTabInfoCache().GetTabLabelItemMap()
	for _, tabId := range tabIds {
		if tabLabelItem, ok := tabLabelItemMap[tabId]; ok {
			tabItems = append(tabItems, tabLabelItem)
		}
	}

	gameLabelFilterMap = make(map[uint32][]*channel_play.GameLabelFilter)
	var err error
	if len(tabItems) == 0 {
		return gameLabelFilterMap
	}
	gameLabelFilterMap, err = cache.GenGameLabelFilterMapByTabs(ctx, tabItems, rcmdPb.ConvertGameLabelsReq_GameChannelList, serviceInfo)
	if err != nil {
		//玩法筛选器
		gameLabelFilterMap = cache.GetGameLabelFilterMap()
	}
	return gameLabelFilterMap
}

// tabItem
func (m *MutiEntryFilterMgr) batGenLabelInfo(ctx context.Context, params *labelParams) (map[uint32]*block_mgr.BlockAndLabel,
	map[string]*block_mgr.BlockAndLabel, map[string]*music_topic_channel.MusicFilterItemV2, error) {
	tabIds := params.tabIds
	filterIds := params.filterIds
	tabItemVersion := params.tabItemVersion
	serviceInfo := params.serviceInfo
	browseLabels := params.browseLabelsMap
	needHandleMinorityGame := params.needHandleMinorityGame

	var getBlockSource channel_play.GetSecondaryFilterReq_Mode
	if tabItemVersion == channel_play.TabItemVersion_After628_Version {
		getBlockSource = channel_play.GetSecondaryFilterReq_FilterAfter628
	} else if tabItemVersion == channel_play.TabItemVersion_TAB_ITEM_VERSION_MULTI_OPTION {
		getBlockSource = channel_play.GetSecondaryFilterReq_HUAN_YOU_MIX_LABEL
	} else {
		getBlockSource = channel_play.GetSecondaryFilterReq_FILTER
	}

	multiTabFilterIds := make([]string, 0, len(filterIds))
	singleMusicTabMap := make(map[uint32]string, len(filterIds))
	musicItemInfoMap := make(map[string]*music_topic_channel.MusicFilterItemV2, len(filterIds))
	gameLabelAndBlockMap := make(map[uint32]*block_mgr.BlockAndLabel, len(tabIds))
	musicLabelsMap := make(map[string]*block_mgr.BlockAndLabel)
	var err error
	if params.needHandleMusicItem {
		musicRsp, err := m.musicTopicClient.GetFilterInfoByFilterIds(ctx, &music_topic_channel.GetFilterInfoByFilterIdsReq{
			FilterIds: filterIds,
			NeedCheck: true,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "MutiEntryFilterMgr batGenLabelInfo GetFilterInfoByFilterIds filterIds(%v) err(%v)", filterIds, err)
		} else {
			// 区分单品类音乐玩法以及多品类音乐玩法
			for filterId, filterInfo := range musicRsp.GetFilterMap() {
				musicItemInfoMap[filterId] = filterInfo
				if len(filterInfo.GetTabIds()) == 1 {
					singleMusicTabMap[filterInfo.GetTabIds()[0]] = filterId
				} else if len(filterInfo.GetTabIds()) > 1 {
					multiTabFilterIds = append(multiTabFilterIds, filterId)
				} else {
					log.ErrorWithCtx(ctx, "MutiEntryFilterMgr batGenLabelInfo GetFilterInfoByFilterIds filterId(%s) tabIds(%v)", filterId, filterInfo.GetTabIds())
					continue
				}
			}
		}
		//GetHomeGameLabelAndBlocks 只用于新版本tt和老版本tt，欢游的筛选项获取
		gameLabelAndBlockMap, musicLabelsMap, err = m.baseBlockHandler.GetHomeGameLabelAndBlocks(ctx, tabIds, multiTabFilterIds, singleMusicTabMap,
			serviceInfo, getBlockSource, browseLabels, needHandleMinorityGame)
		if err != nil {
			log.ErrorWithCtx(ctx, "MutiEntryFilterMgr batGenLabelInfo GetHomeGameLabelAndBLocks getBlockSource:%v, tabItemVersion:%v,svrInfo:%s, err(%v)",
				getBlockSource, tabItemVersion, serviceInfo.String(), err)
			return gameLabelAndBlockMap, musicLabelsMap, musicItemInfoMap, err
		}
	} else {
		//GetHomeGameLabelAndBlocks 只用于新版本tt和老版本tt，欢游的筛选项获取
		gameLabelAndBlockMap, musicLabelsMap, err = m.baseBlockHandler.GetHomeGameLabelAndBlocks(ctx, tabIds, nil, nil,
			serviceInfo, getBlockSource, browseLabels, needHandleMinorityGame)
		if err != nil {
			log.ErrorWithCtx(ctx, "MutiEntryFilterMgr batGenLabelInfo GetHomeGameLabelAndBLocks getBlockSource:%v, tabItemVersion:%v,svrInfo:%s, err(%v)",
				getBlockSource, tabItemVersion, serviceInfo.String(), err)
			return gameLabelAndBlockMap, musicLabelsMap, musicItemInfoMap, err
		}
	}
	log.InfoWithCtx(ctx, "batGenLabelInfo  tabIds(%v) filterIds(%v) multiTabFilterIds:(%v) singleMusicTabMap:(%v)"+
		" len(gameLabelAndBlockMap):%d len(musicLabelsMap):%d len(musicItemInfoMap):%d needHandleMusicItem:%v needHandleMinorityGame:%v",
		tabIds, filterIds, multiTabFilterIds, singleMusicTabMap, len(gameLabelAndBlockMap), len(musicLabelsMap), len(musicItemInfoMap),
		params.needHandleMusicItem, params.needHandleMinorityGame)

	return gameLabelAndBlockMap, musicLabelsMap, musicItemInfoMap, nil
}

func (m *MutiEntryFilterMgr) genTabInfos(ctx context.Context, serviceInfo *grpc.ServiceInfo, tabIds []uint32, indexMap map[uint32]int,
	gameLabelAndBlockMap map[uint32]*block_mgr.BlockAndLabel) map[int]*channel_play.TabItem {
	tabCache := cache.GetTabInfoCache().GetTabIdCache()
	gameLabelFilterMap := m.GetGameLabelFilterMap(ctx, tabIds, serviceInfo)
	resMap := make(map[int]*channel_play.TabItem, len(tabIds))

	// 热门标签，二级筛选项
	for _, tabId := range tabIds {
		labelAndBlock := gameLabelAndBlockMap[tabId]
		tabInfo := tabCache[tabId]
		tabItem := &channel_play.TabItem{
			TabId:              tabId,
			TabName:            tabInfo.GetName(),
			TabDisplayImageUrl: tabInfo.GetCardsImageUrl(),
			CategoryType:       tabInfo.GetCategoryMapping(),
		}
		if labelAndBlock != nil {
			tabItem.Blocks = labelAndBlock.Blocks
			tabItem.Labels = labelAndBlock.HotLabel
			tabItem.ClassifyLabels = labelAndBlock.ClassifyLabel
			tabItem.Enable = labelAndBlock.IsLabelOn
		}

		tabItem.GameLabelFilter = gameLabelFilterMap[tabId]
		//推荐tab
		rcmdFilter := []*channel_play.GameLabelFilter{
			{
				LabelDisplayName: "推荐",
				ShowHot:          false,
				IsRcmd:           true,
				Labels:           []*topic_channel.GameLabel{},
			},
		}
		tabItem.GameLabelFilter = append(rcmdFilter, tabItem.GameLabelFilter...)
		resMap[indexMap[tabId]] = tabItem
	}
	return resMap
}

func (m *MutiEntryFilterMgr) genFilterInfos(ctx context.Context, filterIds []string,
	musicLabelsMap map[string]*block_mgr.BlockAndLabel, musicItemInfoMap map[string]*music_topic_channel.MusicFilterItemV2) map[string]*channel_play.TabItem {
	musicMap := make(map[string]*channel_play.TabItem, len(filterIds))
	for _, filterId := range filterIds {
		labelInfo, ok := musicLabelsMap[filterId]
		if !ok {
			continue
		}
		//tabInfo := tabCache[tabId]
		musicItemInfo, ok := musicItemInfoMap[filterId]
		if !ok {
			log.ErrorWithCtx(ctx, "genFilterInfos musicItemInfoMap no info filterId(%s)", filterId)
			continue
		}
		musicItem := &channel_play.TabItem{}
		musicItem.TabId = labelInfo.MixTabId
		musicItem.TabName = musicItemInfo.GetName()
		musicItem.Labels = labelInfo.HotLabel
		musicItem.Enable = labelInfo.IsLabelOn
		musicItem.TabIds = musicItemInfo.GetTabIds()
		musicItem.ClassifyLabels = labelInfo.ClassifyLabel
		musicItem.Blocks = labelInfo.Blocks

		musicMap[filterId] = musicItem
	}
	log.InfoWithCtx(ctx, "genFilterInfos musicMap(%v)", musicMap)
	return musicMap
}

// 对commonBusinessFlag过滤
func (m *MutiEntryFilterMgr) skipInvalidFlags(ctx context.Context, flags []*channel_play.CommonBusinessFlag,
	serviceInfo *grpc.ServiceInfo, channelPkg string) (filteredFlags []*channel_play.CommonBusinessFlag) {
	filteredFlags = make([]*channel_play.CommonBusinessFlag, 0, len(flags))
	tabIdMap := cache.GetTabInfoCache().GetTabIdCache()
	// 监管配置过滤
	supConfInst := &supervision.SupervisoryConf{
		RegisterLimitTime:      conf.PublicSwitchConfig.GetRegisterLimitTime(),
		UserLevelLimit:         conf.PublicSwitchConfig.GetUserLevelLimit(),
		RealNameStandardStatus: conf.PublicSwitchConfig.GetRealNameStandardStatus(),
	}
	tabFilter, categoryFilter := m.SupervisorInst.GetFilterMap(ctx, serviceInfo, channelPkg, supConfInst)

	for _, f := range flags {
		//只对开黑玩法过滤
		gameId := f.GetGameBusinessId()
		if f.GetFilterItemType() == channel_play.FilterItemType_GAME_TAB && gameId != 0 {
			if conf.PublicSwitchConfig.GetChannelListNoFilterTabSwitch(gameId) {
				// 特殊玩法不展示（新旧扩列）
				continue
			}
			if tabInfo, ok := tabIdMap[gameId]; ok {
				// 监管配置
				if tabFilter[tabInfo.GetId()] || categoryFilter[tabInfo.GetCategoryId()] {
					log.DebugWithCtx(ctx, "skipInvalidFlags tabFilter categoryFilter flag (%s) ", f)
					continue
				}
				if m.SupervisorInst.NewHomePageFilterStrategy(tabInfo, serviceInfo, cache.GetWhiteList()) {
					log.DebugWithCtx(ctx, "skipInvalidFlags GamePageFilterStrategy flag(%s) ", f)
					continue
				}
				filteredFlags = append(filteredFlags, f)
			}
		} else if f.GetFilterItemType() == channel_play.FilterItemType_GAME_CATEGORY && gameId != 0 {
			// 监管配置
			if categoryFilter[gameId] {
				log.DebugWithCtx(ctx, "skipInvalidFlags categoryFilter flag(%s) ", f.String())
				continue
			}
			filteredFlags = append(filteredFlags, f)
		} else if f.GetFilterItemType() == channel_play.FilterItemType_MUSIC_ITEM {
			filteredFlags = append(filteredFlags, f)
		} else {
			log.WarnWithCtx(ctx, "skipInvalidFlags invalid flag(%v)", f)
		}

	}

	return
}
