package mutientry_filter_mgr

import (
	"context"
	"sort"

	game_ugc_content "golang.52tt.com/clients/game-ugc-content"
	"golang.52tt.com/kaihei-pkg/supervision"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/app/channel-play"
	topicchannel "golang.52tt.com/protocol/app/topic-channel"
	game_ugc_content_pb "golang.52tt.com/protocol/services/game-ugc-content"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
	clients "golang.52tt.com/services/channel-play-logic/internal/cache/cache_client"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
)

type MoreTabMgr struct {
	supervisorInst *supervision.Supervisory

	GameUgcContentClient *game_ugc_content.Client
}

func NewMoreTabMgr(supervisorInst *supervision.Supervisory, gameUgcContentClient *game_ugc_content.Client) *MoreTabMgr {
	return &MoreTabMgr{
		supervisorInst: supervisorInst,

		GameUgcContentClient: gameUgcContentClient,
	}
}

func (m *MoreTabMgr) GetMoreTabInfo(ctx context.Context, req *pb.GetMoreTabConfigReq, serviceInfo *grpc.ServiceInfo) ([]*pb.MoreTabItem, error) {
	supConfInst := &supervision.SupervisoryConf{
		RegisterLimitTime:      conf.PublicSwitchConfig.GetRegisterLimitTime(),
		UserLevelLimit:         conf.PublicSwitchConfig.GetUserLevelLimit(),
		RealNameStandardStatus: conf.PublicSwitchConfig.GetRealNameStandardStatus(),
	}
	tabFilter, categoryFilter := m.supervisorInst.GetFilterMap(ctx, serviceInfo, req.GetChannelPkg(), supConfInst)

	switch req.GetFilterEntrance() {
	case pb.FilterEntrance_HOME_PAGE:
		res, err := m.getHomeMoreTabInfo(ctx, serviceInfo, tabFilter, categoryFilter, req.GetIsNeedMinorityGame())
		return res, err

	case pb.FilterEntrance_GAME_ZONE:
		categoryIds := conf.ChannelPlayLogicConfig.GetFilterEntranceCategoryMap(ctx, pb.FilterEntrance_GAME_ZONE)
		if len(categoryIds) == 0 {
			log.ErrorWithCtx(ctx, "UserLine Err: GetMoreTabInfo GetFilterEntranceCategoryMap categoryIds empty, filterEntrance", pb.FilterEntrance_GAME_ZONE)
			// 一起开黑兜底
			categoryIds = []uint32{1}
		}
		res := m.getZoneTabInfo(ctx, categoryIds, serviceInfo, tabFilter, categoryFilter, req.GetIsNeedMinorityGame())
		return res, nil

	case pb.FilterEntrance_MINIGAME_ZONE:
		categoryIds := conf.ChannelPlayLogicConfig.GetFilterEntranceCategoryMap(ctx, pb.FilterEntrance_MINIGAME_ZONE)
		if len(categoryIds) == 0 {
			log.ErrorWithCtx(ctx, "UserLine Err: GetMoreTabInfo GetFilterEntranceCategoryMap categoryIds empty, filterEntrance", pb.FilterEntrance_GAME_ZONE)
			// 趣味玩法、休闲互动兜底(生产)
			categoryIds = []uint32{3, 8}
		}
		res := m.getZoneTabInfo(ctx, categoryIds, serviceInfo, tabFilter, categoryFilter, req.GetIsNeedMinorityGame())
		return res, nil

	case pb.FilterEntrance_GAME_PAL: // 游戏搭子
		categoryIds := conf.ChannelPlayLogicConfig.GetFilterEntranceCategoryMap(ctx, pb.FilterEntrance_GAME_ZONE)
		if len(categoryIds) == 0 {
			log.ErrorWithCtx(ctx, "UserLine Err: GetMoreTabInfo GetFilterEntranceCategoryMap categoryIds empty, filterEntrance", pb.FilterEntrance_GAME_ZONE)
			// 一起开黑兜底
			categoryIds = []uint32{1}
		}
		return m.selectGamePalTabs(ctx, m.getZoneTabInfo(ctx, categoryIds, serviceInfo, tabFilter, categoryFilter, req.GetIsNeedMinorityGame()))
	default:
		log.WarnWithCtx(ctx, "UserLine Time Err: GetMoreTabInfo FilterEntrance invalid, req:+%v", req)
	}
	return nil, nil
}

func (m *MoreTabMgr) getHomeMoreTabInfo(ctx context.Context, serviceInfo *grpc.ServiceInfo,
	tabFilter, categoryFilter map[uint32]bool, isNeedMinorityGame bool) ([]*pb.MoreTabItem, error) {
	// 热门分类
	hotConfig := m.getHotCategoryInfo(ctx, tabFilter, categoryFilter, serviceInfo)
	// 旧开黑列表中展示的类目玩法，主要是一起开黑，扩列聊天，密室逃脱，休闲互动等，过滤掉音乐类型的唱歌K歌

	categoryResp, err := clients.TCTabClient.GetCategoryTitleList(ctx, &tabPB.GetCategoryTitleReq{Skip: 0, Limit: 100})
	if err != nil {
		log.ErrorWithCtx(ctx, "UserLine Err:getHomeMoreTabInfo GetCategoryTitleList error: %v", err)
		return nil, err
	}
	categoryIds := make([]uint32, 0, len(categoryResp.GetCategoryList()))
	for _, info := range categoryResp.GetCategoryList() {
		categoryIds = append(categoryIds, info.GetCategoryId())
	}
	gameConfigs := m.getZoneTabInfo(ctx, categoryIds, serviceInfo, tabFilter, categoryFilter, isNeedMinorityGame)

	// 音乐兴趣 音乐一级类顺序位置可以配置插入，没有配置的顺序在最后添加
	musicHasPosConfigMap, musicAppendConfigs := m.getMusicItemInfo(ctx)

	returnLen := len(hotConfig) + len(gameConfigs) + len(musicHasPosConfigMap) + len(musicAppendConfigs)
	res := make([]*pb.MoreTabItem, returnLen)
	// 先添加热门分类
	if len(hotConfig) > 0 {
		res[0] = hotConfig[0]
	}
	// 音乐配置有位序先填充, k=pos, v=config
	checkNil := false
	for k, v := range musicHasPosConfigMap {
		if k >= returnLen || k <= 0 {
			log.ErrorWithCtx(ctx, "UserLine Err: musicHasPosConfig pos invalid, pos:%d, returnLen:%d, info:+%v", k, returnLen, v)
			checkNil = true
			continue
		}
		if res[k] == nil {
			res[k] = v
		}
	}
	// 其它位置按序添加游戏的数据和音乐没配置位置的数据
	index := 0
	gameConfigs = append(gameConfigs, musicAppendConfigs...)
	for _, c := range gameConfigs {
		for i := index; i < returnLen; i++ {
			if res[i] == nil {
				res[i] = c
				index = i + 1
				break
			}
		}
	}
	// 要去掉res中nil的值
	if checkNil {
		filterRes := make([]*pb.MoreTabItem, 0, returnLen)
		for _, item := range res {
			if item == nil {
				continue
			}

			filterRes = append(filterRes, item)
		}
		return filterRes, nil
	}

	return res, nil
}

// 获取热门混合类配置
func (m *MoreTabMgr) getHotCategoryInfo(ctx context.Context, tabFilter, categoryFilter map[uint32]bool, serviceInfo *grpc.ServiceInfo) []*pb.MoreTabItem {
	hotConfig := conf.ChannelPlayLogicConfig.GetHomePageMoreHotConfig(ctx)
	if hotConfig == nil || len(hotConfig.Items) == 0 {
		log.ErrorWithCtx(ctx, "UserLine Err: getHotCategoryInfo hot config is empty")
		return nil
	}
	canSelectNum := hotConfig.CanSelectNum
	if canSelectNum == 0 {
		log.ErrorWithCtx(ctx, "UserLine Time Err: hotConfig.CanSelectNum is 0")
		canSelectNum = 10
	}
	res := &pb.MoreTabItem{
		CommonBusinessFlag: &pb.CommonBusinessFlag{
			FilterItemType: pb.FilterItemType_MIX_CATEGORY,
		},
		MixInfo: &pb.MixInfo{
			Id:           "1", // 热门分类该id，暂时没用
			Name:         "热门分类",
			MixItem:      make([]*pb.MixInfo_MixCategoryItem, 0, len(hotConfig.Items)),
			CanSelectNum: canSelectNum,
		},
	}
	for _, item := range hotConfig.Items {
		if item.GetFilterItemType() == pb.FilterItemType_GAME_TAB {
			tabInfo := cache.GetTabInfoCache().GetTabInfoCacheById(item.GetGameBusinessId())
			if tabInfo == nil {
				log.ErrorWithCtx(ctx, "UserLine Err: getHotCategoryInfo hot tab invalid, tabId:%d", item.GetGameBusinessId())
				continue
			}
			if conf.PublicSwitchConfig.GetChannelListNoFilterTabSwitch(item.GetGameBusinessId()) {
				// 特殊玩法不展示（新旧扩列）
				continue
			}
			if tabFilter[tabInfo.GetId()] || categoryFilter[tabInfo.GetCategoryId()] {
				log.InfoWithCtx(ctx, "getHotCategoryInfo tabFilter or categoryFilter, tabId:%d, categoryId:%d",
					item.GetGameBusinessId(), tabInfo.GetCategoryId())
				continue
			}
			//热门玩法中的开黑筛选项过滤白名单
			if m.supervisorInst.NewHomePageFilterStrategy(tabInfo, serviceInfo, cache.GetWhiteList()) {
				log.InfoWithCtx(ctx, "getHotCategoryInfo NewHomePageFilterStrategy, tabId:%d, categoryId:%d",
					item.GetGameBusinessId(), tabInfo.GetCategoryId())
				continue
			}
			res.MixInfo.MixItem = append(res.MixInfo.MixItem, &pb.MixInfo_MixCategoryItem{
				CommonBusinessFlag: &pb.CommonBusinessFlag{
					GameBusinessId: item.GetGameBusinessId(),
					FilterItemType: item.GetFilterItemType(),
				},
				TabDetail: convertTab(tabInfo),
			})
		}
		if item.GetFilterItemType() == pb.FilterItemType_MUSIC_ITEM {
			// 配置的音乐id是否有效校验，待做

			res.MixInfo.MixItem = append(res.MixInfo.MixItem, &pb.MixInfo_MixCategoryItem{
				CommonBusinessFlag: &pb.CommonBusinessFlag{
					MusicBusinessId: item.GetMusicBusinessId(),
					FilterItemType:  item.GetFilterItemType(),
				},
				ItemId: item.GetMusicBusinessId(),
			})
		}
	}
	if len(res.MixInfo.MixItem) > 0 {
		return []*pb.MoreTabItem{res}
	}
	return nil
}

// 获取音乐类配置
func (m *MoreTabMgr) getMusicItemInfo(ctx context.Context) (map[int]*pb.MoreTabItem, []*pb.MoreTabItem) {
	homePageMoreMusicConfigs := conf.ChannelPlayLogicConfig.GetHomePageMoreMusicConfig(ctx)
	if len(homePageMoreMusicConfigs) == 0 {
		log.ErrorWithCtx(ctx, "UserLine Err: getMusicItemInfo GetHomePageMoreMusicConfig music config is empty")
		return nil, nil
	}
	// 配置的音乐id是否有效校验，待做

	musicHasPosConfig := make(map[int]*pb.MoreTabItem, len(homePageMoreMusicConfigs))
	musicAppendConfigs := make([]*pb.MoreTabItem, 0, len(homePageMoreMusicConfigs))
	for _, c := range homePageMoreMusicConfigs {
		if c == nil {
			log.ErrorWithCtx(ctx, "UserLine Err: getMusicItemInfo homePageMoreMusicConfigs music item config is nil")
			continue
		}
		musicItem := &pb.MoreTabItem{
			CommonBusinessFlag: &pb.CommonBusinessFlag{
				MusicBusinessId: c.ItemId,
				FilterItemType:  pb.FilterItemType_MUSIC_ITEM,
			},
			MusicInfo: &pb.MoreTabMusicInfo{
				ItemId:       c.ItemId,
				CanSelectNum: c.CanSelectNum,
			},
		}
		if c.Pos <= 0 {
			musicAppendConfigs = append(musicAppendConfigs, musicItem)

		} else {
			musicHasPosConfig[c.Pos] = musicItem
		}
	}
	log.DebugWithCtx(ctx, "getMusicItemInfo musicHasPosConfig:%+v, musicAppendConfigs:%+v", musicHasPosConfig, musicAppendConfigs)
	return musicHasPosConfig, musicAppendConfigs
}

// 获取专区内的玩法，整体逻辑跟旧版本基本相同
func (m *MoreTabMgr) getZoneTabInfo(ctx context.Context, categoryIds []uint32, serviceInfo *grpc.ServiceInfo,
	tabFilter, categoryFilter map[uint32]bool, isNeedMinorityGame bool) []*pb.MoreTabItem {

	categoryIdMap := cache.GetCategoryIdMap()
	tabsOfCategoryMap := cache.GetTabsOfCategoryCache()
	platform := protocol.NewTerminalType(serviceInfo.TerminalType).Platform()

	items := make([]*pb.MoreTabItem, 0, len(categoryIds))
	for _, categoryId := range categoryIds {
		categoryInfo, ok := categoryIdMap[categoryId]
		if !ok || categoryInfo == nil {
			log.ErrorWithCtx(ctx, "UserLine Err: getZoneTabInfo categoryInfo no exist, categoryId:%d", categoryId)
			continue
		}
		if uint32(categoryInfo.CategoryType) != uint32(tabPB.Scene_NO_DEFAULT) {
			continue
		}

		// 监管开启时，未成年人忽略游戏
		if categoryFilter[categoryId] {
			log.WarnWithCtx(ctx, "categoryFilter categoryId:%d, uid:%d", categoryId, serviceInfo.UserID)
			continue
		}
		if platformTypeNotMatch(categoryInfo.PlatformType, platform) {
			continue
		}

		tabInfos := tabsOfCategoryMap[categoryId]
		if len(tabInfos) == 0 {
			continue
		}
		tabInfos = sortTabsByPlatform(tabInfos, platform)

		item := &pb.MoreTabItem{
			CommonBusinessFlag: &pb.CommonBusinessFlag{
				GameBusinessId: categoryInfo.CategoryId,
				FilterItemType: pb.FilterItemType_GAME_CATEGORY,
			},
			CategoryInfo: &pb.CategoryInfo{
				CategoryId:       categoryInfo.CategoryId,
				CategoryName:     categoryInfo.Title,
				CategoryInfoItem: make([]*pb.CategoryInfo_CategoryInfoItem, 0),
				CanSelectNum:     categoryInfo.GetCanSelectNum(),
			},
		}

		for _, tabInfo := range tabInfos {
			if conf.PublicSwitchConfig.GetChannelListNoFilterTabSwitch(tabInfo.GetId()) {
				// 特殊玩法不展示（新旧扩列）
				continue
			}
			// 监管
			if tabFilter[tabInfo.GetId()] || categoryFilter[tabInfo.GetCategoryId()] {
				log.WarnWithCtx(ctx, "tabFilter or categoryFilter tabId:%d, categoryId:%d, uid:%d", tabInfo.GetId(), categoryId, serviceInfo.UserID)
				continue
			}
			// 跳过小众游戏
			if !isNeedMinorityGame && tabInfo.IsMinorityGame {
				continue
			}
			if platformTypeNotMatch(tabInfo.PlatformType, platform) {
				continue
			}
			// 过滤非游戏的玩法
			if tabInfo.HomePageType != tabPB.HomePageType_HomePageTypeGAME {
				continue
			}

			// 按马甲包风控,放在MiniGameStageStrategy里面调用了
			//if m.supervisorInst.RiskControlByVestBag(tabInfo, serviceInfo) {
			//	continue
			//}
			if conf.GetEnv() != "staging" {
				// 生产环境的时候，需要根据策略过滤
				if isFilter := m.supervisorInst.NewHomePageFilterStrategy(tabInfo, serviceInfo, cache.GetWhiteList()); isFilter {
					continue
				}
			}
			item.CategoryInfo.CategoryInfoItem = append(item.CategoryInfo.CategoryInfoItem, &pb.CategoryInfo_CategoryInfoItem{
				CommonBusinessFlag: &pb.CommonBusinessFlag{
					GameBusinessId: tabInfo.GetId(),
					FilterItemType: pb.FilterItemType_GAME_TAB,
				},
				TabDetail: convertTab(tabInfo),
			})
		}

		if len(item.CategoryInfo.CategoryInfoItem) == 0 {
			log.DebugWithCtx(ctx, "getZoneTabInfo category no tab categoryId:%d", item.CategoryInfo.CategoryId)
			continue
		}
		items = append(items, item)
	}
	return items
}

func platformTypeNotMatch(platformType tabPB.PlatformType, userPlatform protocol.Platform) bool {
	return !(platformType == tabPB.PlatformType_ALL ||
		(platformType == tabPB.PlatformType_ANDROID_IOS && userPlatform == protocol.MOBILE) ||
		(platformType == tabPB.PlatformType_PC && userPlatform == protocol.PC))
}

func sortTabsByPlatform(tabs []*tabPB.Tab, platform protocol.Platform) []*tabPB.Tab {
	if platform == protocol.PC {
		sort.Slice(tabs, func(i, j int) bool {
			return tabs[i].PcSort < tabs[j].PcSort
		})
	} else {
		sort.Slice(tabs, func(i, j int) bool {
			return tabs[i].Sort < tabs[j].Sort
		})
	}
	return tabs
}

func convertTab(tab *tabPB.Tab) (elem *topicchannel.Tab) {
	if tab != nil {
		elem = &topicchannel.Tab{}
		elem.Id = tab.GetId()
		elem.Name = tab.GetName()
		elem.ImageUri = tab.GetImageUri()
		elem.Version = tab.GetVersion()
		elem.TagId = tab.GetTagId()
		elem.FollowLabelImg = tab.FollowLabelImg
		elem.FollowLabelText = tab.FollowLabelText
		elem.CategoryId = tab.GetCategoryId()
		elem.MiniGameNum = tab.GetMiniGameNum()
		if uint32(tab.GetTabType()) == 0 {
			elem.TabType = topicchannel.Tab_NORMAL
		} else if uint32(tab.GetTabType()) == 1 {
			elem.TabType = topicchannel.Tab_GAME
		} else if uint32(tab.GetTabType()) == 2 {
			elem.TabType = topicchannel.Tab_MINI_GAME
		}

		if int32(tab.GetRoomNameType()) == 0 {
			elem.RoomNameType = topicchannel.Tab_DEFAULT
		} else if int32(tab.GetRoomNameType()) == 1 {
			elem.RoomNameType = topicchannel.Tab_SPLICE
		}

		elem.RoomNameVersion = tab.RoomNameVersion
		// 首页卡片相关
		elem.CardsImageUrl = tab.GetCardsImageUrl()
		elem.MaskLayer = tab.GetMaskLayer()
		elem.TabLabel = topicchannel.LabelType(tab.GetTabLabel())
		elem.RoomLabel = tab.GetRoomLabel()
		elem.CategorySort = tab.GetCategorySort()
		elem.DisplayElem = tab.GetDisplayElem()

		//v5.5.0新增小卡片和创建房间列表
		elem.NewTabCategoryUrl = tab.NewTabCategoryUrl
		elem.SmallCardUrl = tab.SmallCardUrl

		if tab.Name == "其他游戏" {
			elem.HasChildList = true
		}
	}
	return elem
}

func (m *MoreTabMgr) selectGamePalTabs(ctx context.Context, tabItems []*pb.MoreTabItem) ([]*pb.MoreTabItem, error) {
	resp, err := m.GameUgcContentClient.GetGamePalTabs(ctx, &game_ugc_content_pb.GetGamePalTabsReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "selectGamePalTabs GetGamePalTabs err: %v", err)
		return nil, err
	}

	gamePalTabMap := resp.GetGamePalTabs()
	if len(gamePalTabMap) == 0 {
		log.WarnWithCtx(ctx, "selectGamePalTabs GetGamePalTabs empty")
		return nil, nil
	}

	var ts []*pb.MoreTabItem
	for _, tabItem := range tabItems {
		items := tabItem.GetCategoryInfo().GetCategoryInfoItem()
		if len(items) == 0 {
			continue
		}

		t := *tabItem
		t.CategoryInfo.CategoryInfoItem = make([]*pb.CategoryInfo_CategoryInfoItem, 0, len(items))

		for _, item := range items {
			if _, ok := gamePalTabMap[item.GetTabDetail().GetId()]; ok {
				t.CategoryInfo.CategoryInfoItem = append(t.CategoryInfo.CategoryInfoItem, item)
			}
		}

		if len(t.GetCategoryInfo().GetCategoryInfoItem()) > 0 {
			ts = append(ts, &t)
		}
	}

	return ts, nil
}
