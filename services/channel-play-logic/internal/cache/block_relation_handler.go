package cache

import (
	"context"
	"golang.52tt.com/pkg/log"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"math"
)

const InfElemID = math.MaxUint32

type BlockRelationHandler struct {
	Relations     []*tabPB.DisplayBlockInfo
	BlockInfos    map[uint32]*topic_channel.Block
	AllBlockSlice []*topic_channel.Block
}

func NewBlockRelationHandler(ctx context.Context, relations []*tabPB.DisplayBlockInfo, blockInfos []*tabPB.Block, tabId uint32) *BlockRelationHandler {
	h := &BlockRelationHandler{
		Relations: relations,
	}

	h.BlockInfos = make(map[uint32]*topic_channel.Block, len(blockInfos))
	h.AllBlockSlice = make([]*topic_channel.Block, 0, len(blockInfos))
	for _, v := range blockInfos {
		//var block *topic_channel.Block
		//if (v.GetMostSelectNum() == 0 && v.GetMode() == tabPB.Block_SINGLE) || v.GetMode() == tabPB.Block_USER_INPUT {
		//	//单选不需要返回不限  +  用户输入模式的也不需要
		//	block = h.convertBlock(v, false)
		//} else {
		//	//不限或则最多选N个，要返回不限的elem
		//	block = h.convertBlock(v, true)
		//}
		block := h.convertBlock(v, false)
		if len(block.GetElems()) == 0 {
			//没有elem不显示block二级标题
			continue
		}
		h.BlockInfos[v.GetId()] = block
		h.AllBlockSlice = append(h.AllBlockSlice, block)

	}
	return h
}

func (h *BlockRelationHandler) convertBlock(block *tabPB.Block, needUnLimitElem bool) *topic_channel.Block {
	opType := uint32(topic_channel.BlockButtonType_BUTTON_TYPE_UNSPECIFIED)
	if block.GetSetMicName() {
		opType = uint32(topic_channel.BlockButtonType_BUTTON_TYPE_SET_MIC_NAME)
	}
	buttonOpt := &topic_channel.Block_ButtonOpt{
		IsShow: opType > 0,
		Type:   opType,
	}

	return &topic_channel.Block{
		Id:              block.GetId(),
		Title:           block.GetTitle(),
		Mode:            topic_channel.Block_Mode(block.GetMode()),
		Elems:           h.convertElem(block.GetElems(), needUnLimitElem),
		ControlTeamSize: block.GetControlTeamSize(),
		MostSelectNum:   block.GetMostSelectNum(),
		SubTitle:        block.GetSubTitle(),
		ButtonOpt:       buttonOpt,
	}
}

func (h *BlockRelationHandler) isShowFlag(elem *tabPB.Elem) bool {
	for _, flag := range elem.GetPublicFlags() {
		if flag == tabPB.Elem_PublishFlagHotFlag {
			return true
		}
	}
	return false
}

func (h *BlockRelationHandler) convertElem(elems []*tabPB.Elem, needUnLimitElem bool) (res []*topic_channel.Elem) {
	if needUnLimitElem {
		res = make([]*topic_channel.Elem, 0, len(elems)+1)
		res = append(res, &topic_channel.Elem{
			Id:        InfElemID,
			Title:     "不限",
			Relations: &topic_channel.Relation{},
			Mode:      topic_channel.Elem_INFINITE,
		})
	} else {
		res = make([]*topic_channel.Elem, 0, len(elems))
	}

	for _, e := range elems {
		// 如果是小游戏并且二级字段默认字段,不显示
		if e.GetTitle() == "default_model" {
			continue
		}
		tmpElem := &topic_channel.Elem{
			Id:    e.GetId(),
			Title: e.GetTitle(),
			Relations: &topic_channel.Relation{
				BlockId: e.GetContacts().GetBlockId(),
				Before:  e.GetContacts().GetBefore(),
				After:   e.GetContacts().GetAfter(),
			},
			Mode:          topic_channel.Elem_NORMAL,
			MiniGameModel: e.GetMiniGameModel(),
			TeamSize:      e.GetTeamSize(),
			Priority:      e.GetPriority(),
			MinNum:        e.GetMinNum(),
			MaxNum:        e.GetMaxNum(),
		}
		isFlag := h.isShowFlag(e)
		if isFlag {
			tmpElem.FlagUrl = "https://obs-cdn.52tt.com/tt/fe-moss/tdesign/channel_publish/20241022151124_8013052.png" //热门标签
		}
		res = append(res, tmpElem)
	}
	return res
}

func (h *BlockRelationHandler) GetRelations() (res []*channel_play.DisplayBlockInfo) {
	res = make([]*channel_play.DisplayBlockInfo, 0, len(h.Relations))
	if len(h.Relations) == 0 {
		return h.getDefaultRelations()
	}
	for _, v := range h.Relations {
		block, ok := h.BlockInfos[v.GetBlockId()]
		if !ok {
			continue
		}
		res = append(res, &channel_play.DisplayBlockInfo{
			Block:              block,
			ElemBindBlockInfos: h.getElemBindBlockInfos(v.GetElemBindBlockInfos()),
		})
	}

	return res
}

// 外显管理字段没配置，默认取所有字段作为外显一级字段
func (h *BlockRelationHandler) getDefaultRelations() (res []*channel_play.DisplayBlockInfo) {
	res = make([]*channel_play.DisplayBlockInfo, 0, len(h.AllBlockSlice))
	for _, b := range h.AllBlockSlice {
		res = append(res, &channel_play.DisplayBlockInfo{
			Block:              b,
			ElemBindBlockInfos: make([]*channel_play.ElemBindBlockInfo, 0),
		})
	}
	return
}

func (h *BlockRelationHandler) getElemBindBlockInfos(infos []*tabPB.ElemBindBlockInfo) (res []*channel_play.ElemBindBlockInfo) {
	res = make([]*channel_play.ElemBindBlockInfo, 0, len(infos))
	if len(infos) == 0 {
		return
	}
	for _, v := range infos {
		res = append(res, &channel_play.ElemBindBlockInfo{
			ElemId:     v.GetElemId(),
			BindBlocks: h.getBindBlockInfos(v.GetBindBlockGroups()),
		})
	}
	return

}

func (h *BlockRelationHandler) getBindBlockInfos(bindGroups []*tabPB.ElemBindBlockInfo_BlockElemGroup) (res []*topic_channel.Block) {
	res = make([]*topic_channel.Block, 0, len(bindGroups))
	for _, v := range bindGroups {
		block, ok := h.BlockInfos[v.GetRelatedBlockId()]
		if !ok || len(v.GetRelatedElemIds()) == 0 {
			log.Warnf(" getBindBlockInfos v:%s", v.String())
			continue
		}
		needElemMap := make(map[uint32]bool, len(v.GetRelatedElemIds()))
		for _, elemId := range v.GetRelatedElemIds() {
			needElemMap[elemId] = true
		}
		tempElem := make([]*topic_channel.Elem, 0, len(v.GetRelatedElemIds()))
		for _, elem := range block.GetElems() {
			if needElemMap[elem.GetId()] {
				tempElem = append(tempElem, elem)
			}
		}
		//block.Elems = tempElem
		tempBlock := *block
		tempBlock.Elems = tempElem
		res = append(res, &tempBlock)
	}
	return
}
