package cache_client

import (
	channel_open_game "golang.52tt.com/clients/channel-open-game"
	channel_play_tab "golang.52tt.com/clients/channel-play-tab"
	channel_scheme_conf_mgr "golang.52tt.com/clients/channel-scheme-conf-mgr"
	"golang.52tt.com/clients/configserver"
	gameCard "golang.52tt.com/clients/game-card"
	game_pal_client "golang.52tt.com/clients/game-pal"
	gameServerV2Client "golang.52tt.com/clients/game-server-v2"
	game_ugc_content "golang.52tt.com/clients/game-ugc-content"
	mysteryplace "golang.52tt.com/clients/mystery-place"
	rcmdChannelLabel "golang.52tt.com/clients/topic-channel/rcmd-channel-label"
	tcTab "golang.52tt.com/clients/topic-channel/tab"
)

var (
	TCTabClient             *tcTab.Client
	GameServerClient        *gameServerV2Client.Client
	MysteryPlaceClient      *mysteryplace.Client
	ChannelOpenGameClient   *channel_open_game.Client
	ChannelSchemeConfClient *channel_scheme_conf_mgr.Client
	RcmdChannelLabelClient  *rcmdChannelLabel.Client
	ConfigServerClient      *configserver.Client
	ChannelPlayTabClient    *channel_play_tab.Client
	GameUgcContentClient    *game_ugc_content.Client
	GamePalClient           *game_pal_client.Client
	GameCardClient          *gameCard.Client
)

type CacheClients struct {
	TCTabClient             *tcTab.Client
	GameServerClient        *gameServerV2Client.Client
	MysteryPlaceClient      *mysteryplace.Client
	ChannelOpenGameClient   *channel_open_game.Client
	ChannelSchemeConfClient *channel_scheme_conf_mgr.Client
	RcmdChannelLabelClient  *rcmdChannelLabel.Client
	ConfigServerClient      *configserver.Client
	ChannelPlayTabClient    *channel_play_tab.Client
	GameUgcContentClient    *game_ugc_content.Client
	GamePalClient           *game_pal_client.Client
	GameCardClient          *gameCard.Client
}

func Setup(clis *CacheClients)  {
	TCTabClient = clis.TCTabClient
	GameServerClient = clis.GameServerClient
	MysteryPlaceClient = clis.MysteryPlaceClient
	ChannelOpenGameClient = clis.ChannelOpenGameClient
	ChannelSchemeConfClient = clis.ChannelSchemeConfClient
	RcmdChannelLabelClient = clis.RcmdChannelLabelClient
	ConfigServerClient = clis.ConfigServerClient
	ChannelPlayTabClient = clis.ChannelPlayTabClient
	GameUgcContentClient = clis.GameUgcContentClient
	GamePalClient = clis.GamePalClient
	GameCardClient = clis.GameCardClient
}
