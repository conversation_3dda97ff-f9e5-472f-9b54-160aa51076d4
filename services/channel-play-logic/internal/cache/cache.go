package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/monkey-send-chat/monkey_sender"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	"golang.52tt.com/protocol/common/status"
	channelPlayTabPb "golang.52tt.com/protocol/services/channel-play-tab"
	game_card "golang.52tt.com/protocol/services/game-card"
	game_pal "golang.52tt.com/protocol/services/game-pal"
	game_ugc_content "golang.52tt.com/protocol/services/game-ugc-content"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"golang.52tt.com/services/channel-play-logic/internal/utils"
	"sort"
	"strconv"
	"time"

	"golang.52tt.com/pkg/log"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	channel_open_game "golang.52tt.com/protocol/services/channel-open-game"
	channel_play_tab "golang.52tt.com/protocol/services/channel-play-tab"
	mysteryplacePB "golang.52tt.com/protocol/services/mystery-place"
	rcmdPb "golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	clients "golang.52tt.com/services/channel-play-logic/internal/cache/cache_client"
)

var MinorityGameParentId uint32 //这个是一套环境一个不变的，所以这个就不用锁了

// 排序用
type MinorityGameInfo struct {
	TabInfo   *tabPB.Tab
	GameScore uint32
}

type tabCache struct {
	// 设置的小众游戏 key:tab_id
	minorityGameTabMap map[uint32]*tabPB.Tab

	tabIdMap            map[uint32]*tabPB.Tab
	releaseConditionMap map[uint32]*tabPB.ReleaseConditionItem

	//绑定指定tab业务筛选项
	bindTabMap map[uint32][]*tabPB.BusinessBlock
	//绑定指定category的业务筛选项
	bindCategoryMap map[uint32][]*tabPB.BusinessBlock

	// categoryMap 将categoryId作为键，以map形式存储这个分类下的所有tab
	categoryMap map[uint32][]*tabPB.Tab

	// baseBlocksMap 将tabId作为键，以map形式存储这个tab下的所有blockBase
	baseBlocksMap map[uint32][]*tabPB.Block

	//官方房间名缓存，key为tab_id
	officialRoomConfigsMap map[uint32][]*tabPB.OfficialRoomNameConfig

	//categoryIdMap 将categoryId作为键，以map形式存储分类信息
	categoryIdMap map[uint32]*tabPB.Category

	//gameCardIdTabMap gameCardId为key，缓存gameCardId绑定的tab
	gameCardIdTabMap map[uint32][]*tabPB.Tab

	//uGameIdTabMap key为u_game_id
	uGameIdTabMap map[uint32]*MinorityGameInfo

	// key:tabId value:剧本详情
	scenarioMap map[uint32]*mysteryplacePB.ScenarioInfo

	// key（小游戏id）:mini_game_id value:小游戏配置
	miniGameConfigMap map[uint32]*topic_channel.MiniGameConfig

	//key:tabId value:玩法筛选项
	gameLabelFilterMap map[uint32][]*channel_play.GameLabelFilter

	//key：tabId value block关联关系
	blockRelationMap map[uint32][]*channel_play.DisplayBlockInfo

	//key:tabId value 白名单uidMap
	tabWhiteListMap map[uint32]map[uint32]bool

	//key:tabSubType value 对应类型的tab列表
	tabSubTypeMap map[uint32][]*channel_play_tab.Tab
	//key:tabId value 新tab类型
	newTabIdMap map[uint32]*channel_play_tab.Tab

	//key:tabId value:玩法的搭子卡配置
	gamePalTabMap map[uint32]*game_ugc_content.ConfigTabInfo

	//key:tabId value 点亮中的搭子卡数量
	lightingCardNumMap map[uint32]uint32

	//key:gameCardId value:游戏卡配置
	GameCardConfMap map[uint32]*game_card.GameCardConfInfo

	tabLabelItemMap map[uint32]*tabPB.TabGameLabelItem

	// 极速PC分类配置
	fastPCCategoryConfig    map[uint32][]*channel_play_tab.FastPCCategoryConfig // key:tabId, value:[]*channel_play_tab_pb.FastPCCategoryConfig
	fastPCCategoryConfigMap map[uint32]*channel_play_tab.FastPCCategoryConfig   // key:FastPCCategoryConfig.Id, value:*channel_play_tab_pb.FastPCCategoryConfig
}

var tabInfoCache = &tabCache{}

func setTabInfoCache(tmpCache *tabCache) {
	tabInfoCache = tmpCache
}

func GetTabInfoCache() *tabCache {
	return tabInfoCache
}

func (t *tabCache) GetTabLabelItemMap() map[uint32]*tabPB.TabGameLabelItem {
	return t.tabLabelItemMap
}

func (t *tabCache) GetCategoryMap() map[uint32][]*tabPB.Tab {
	return t.categoryMap
}

func (t *tabCache) GetBaseBlocksMap() map[uint32][]*tabPB.Block {
	return t.baseBlocksMap
}

func (t *tabCache) GetCategoryIdMap() map[uint32]*tabPB.Category {
	return t.categoryIdMap
}

func (t *tabCache) GetTabIdCache() map[uint32]*tabPB.Tab {
	return t.tabIdMap
}

func (t *tabCache) GetTabInfoCacheById(tabId uint32) *tabPB.Tab {
	return t.tabIdMap[tabId]
}

func (t *tabCache) GetMinorityGameTab() map[uint32]*tabPB.Tab {
	return t.minorityGameTabMap
}

func (t *tabCache) GetReleaseConditionCache() map[uint32]*tabPB.ReleaseConditionItem {
	return t.releaseConditionMap
}

func (t *tabCache) GetReleaseConditionByTabId(tabId, clientType uint32) *tabPB.ReleaseConditionItem {
	if protocol.IsFastPcClientType(clientType) && tabId == conf.PublicSwitchConfig.GetMuseChatTabId() {
		tabId = conf.PublicSwitchConfig.GetFastPcChatTabId()
	}
	return t.releaseConditionMap[tabId]
}

func (t *tabCache) GetGameCardIdTabMap() map[uint32][]*tabPB.Tab {
	return t.gameCardIdTabMap
}

func (t *tabCache) GetMinorityGame() []*tabPB.Tab {
	minorityGame := make([]*MinorityGameInfo, 0, len(t.uGameIdTabMap))
	for _, v := range t.uGameIdTabMap {
		minorityGame = append(minorityGame, v)
	}
	sort.SliceStable(minorityGame, func(i, j int) bool {
		return minorityGame[i].GameScore > minorityGame[j].GameScore
	})

	ret := make([]*tabPB.Tab, 0, len(minorityGame))
	for _, v := range minorityGame {
		ret = append(ret, v.TabInfo)
	}

	return ret
}

func (t *tabCache) GetElemInfoMap(tabId uint32) map[uint32]*tabPB.Elem {
	res := make(map[uint32]*tabPB.Elem)
	baseBlocks, ok := t.baseBlocksMap[tabId]
	if !ok {
		return res
	}
	for _, block := range baseBlocks {
		for _, elem := range block.GetElems() {
			res[elem.GetId()] = elem
		}
	}

	return res
}

func sendCacheWarning(msg string) {
	sendErr := monkey_sender.GetNumMsgSenderByChatId(conf.PublicSwitchConfig.GetFallBackChatId(),
		conf.PublicSwitchConfig.GetFallBackWarnDuration()).SendMsg("channel-play-logic缓存", msg)
	if sendErr != nil {
		log.Errorf("refreshTabInfoCache monkey_sender fallback SendMsg err:%v", sendErr)
	}
}

func timerRefreshTabInfoCache(ctx context.Context) {
	defer func() {
		err := recover()
		if err != nil {
			log.Errorf("timerRefreshTabInfoCache refreshTabInfoCache err = %v", err)
		}
	}()
	if err := refreshTabInfoCache(ctx); err != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache err:%v", err)
	}
}

func refreshTabInfoCache(ctx context.Context) error {
	minorityGameTabMap := make(map[uint32]*tabPB.Tab)
	mgTabs, err := clients.TCTabClient.GetMinorityGameTabs(ctx, &tabPB.GetMinorityGameTabsReq{})
	if err != nil {
		//报错不更新，不阻碍其他缓存信息更新,需要告警
		log.ErrorWithCtx(ctx, "UserLine Time Err: refreshTabInfoCache GetMinorityGameTabs error: %v", err)
		sendCacheWarning(fmt.Sprintf("GetMinorityGameTabs len:%d, err:%v", len(mgTabs.GetChildTab()), err))
		minorityGameTabMap = tabInfoCache.minorityGameTabMap
	} else {
		if len(mgTabs.GetChildTab()) == 0 {
			//报错不更新，不阻碍其他缓存信息更新,需要告警
			log.ErrorWithCtx(ctx, "UserLine Time Err: refreshTabInfoCache GetMinorityGameTabs error: len:0")
			sendCacheWarning(fmt.Sprintf("GetMinorityGameTabs err, len:%d", len(mgTabs.GetChildTab())))
			minorityGameTabMap = tabInfoCache.minorityGameTabMap
		} else {
			for _, v := range mgTabs.GetChildTab() {
				minorityGameTabMap[v.GetId()] = v
			}
			MinorityGameParentId = mgTabs.GetParentTab().GetId()
		}
	}

	tabResp, err := clients.TCTabClient.Tabs(ctx, 0, 1000)
	if err != nil {
		log.ErrorWithCtx(ctx, "UserLine Time Err: refreshTabInfoCache Tabs error: %v", err)
		sendCacheWarning(fmt.Sprintf("Tabs len:%d, err:%v", len(tabResp.GetTabs()), err))
		return err
	}
	if len(tabResp.GetTabs()) == 0 {
		log.ErrorWithCtx(ctx, "UserLine Time Err: refreshTabInfoCache Tabs error: len 0")
		sendCacheWarning(fmt.Sprintf("Tabs len:%d", len(tabResp.GetTabs())))
		return protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError)
	}
	//tabIds记录所有tabID
	tabIds := make([]uint32, 0)
	tabIdMap := make(map[uint32]*tabPB.Tab, len(tabResp.GetTabs()))
	gameCardIdTabMap := make(map[uint32][]*tabPB.Tab)
	uGameIdTabMap := make(map[uint32]*MinorityGameInfo)
	category2TabMap := make(map[uint32][]*tabPB.Tab)

	for _, v := range tabResp.GetTabs() {
		category2TabMap[v.GetCategoryId()] = append(category2TabMap[v.GetCategoryId()], v)
		tabIdMap[v.GetId()] = v
		tabIds = append(tabIds, v.GetId())
		if v.GetGameInfo().GetGameCardId() > 0 {
			gameCardIdTabMap[v.GetGameInfo().GetGameCardId()] = append(gameCardIdTabMap[v.GetGameInfo().GetGameCardId()], v)
		}
		if v.GetGameInfo().GetUGameId() > 0 {
			uGameIdTabMap[v.GetGameInfo().GetUGameId()] = &MinorityGameInfo{TabInfo: v}
		}
	}
	confMap, err := clients.GameServerClient.GetAllScanGameListConfMap(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache GetMinorityGameTabs error: %v", err)
		return err
	}
	for k := range uGameIdTabMap {
		if val, ok := confMap[k]; ok {
			uGameIdTabMap[k].GameScore = val.GetScore()
		}
	}

	releaseConditionMap := make(map[uint32]*tabPB.ReleaseConditionItem)
	releaseConditionResp, err := clients.TCTabClient.ListReleaseCondition(ctx, &tabPB.ListReleaseConditionReq{})
	if err != nil {
		//报错不更新，不阻碍其他缓存信息更新,需要告警
		log.ErrorWithCtx(ctx, "UserLine Time Err: refreshTabInfoCache ListReleaseCondition error: %v", err)
		sendCacheWarning(fmt.Sprintf("ListReleaseCondition len:%d, err:%v", len(releaseConditionResp.GetItems()), err))
		return err
	} else {
		if len(releaseConditionResp.GetItems()) == 0 {
			//报错不更新，不阻碍其他缓存信息更新,需要告警
			log.ErrorWithCtx(ctx, "UserLine Time Err: refreshTabInfoCache ListReleaseCondition error: len 0")
			sendCacheWarning(fmt.Sprintf("ListReleaseCondition err, len:%d", len(releaseConditionResp.GetItems())))
			return protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError)
		} else {
			for _, v := range releaseConditionResp.GetItems() {
				releaseConditionMap[v.TabId] = v
			}
		}
	}

	bindTabMap := make(map[uint32][]*tabPB.BusinessBlock)
	bindCategoryMap := make(map[uint32][]*tabPB.BusinessBlock)
	businessBlockInfoResp, err := clients.TCTabClient.BatchGetBusinessBlockInfo(ctx, &tabPB.BatchGetBusinessBlockInfoReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "UserLine Time Err: refreshTabInfoCache BatchGetBusinessBlockInfo err %v", err)
		sendCacheWarning(fmt.Sprintf("BatchGetBusinessBlockInfo len:%d, err:%v", len(businessBlockInfoResp.GetBusinessBlocks()), err))
		bindTabMap = tabInfoCache.bindTabMap
		bindCategoryMap = tabInfoCache.bindCategoryMap
	} else {
		for _, v := range businessBlockInfoResp.GetBusinessBlocks() {
			if v.GetBindType() == tabPB.BatchGetBusinessBlockInfoResp_BussBlockInfo_Bind_Tab {
				bindTabMap[v.GetId()] = make([]*tabPB.BusinessBlock, len(v.GetBusinessBlockInfo()))
				bindTabMap[v.GetId()] = v.GetBusinessBlockInfo()
			} else if v.GetBindType() == tabPB.BatchGetBusinessBlockInfoResp_BussBlockInfo_Bind_Category {
				bindCategoryMap[v.GetId()] = make([]*tabPB.BusinessBlock, len(v.GetBusinessBlockInfo()))
				bindCategoryMap[v.GetId()] = v.GetBusinessBlockInfo()
			} else {
				log.WarnWithCtx(ctx, "refreshTabInfoCache unkown bindtype %v bussBlocks %v", v.GetBindType(), v)
				continue
			}
		}

	}

	scenarioMap := make(map[uint32]*mysteryplacePB.ScenarioInfo)
	scenarioResp, err := clients.MysteryPlaceClient.ListScenarioInfo(ctx, &mysteryplacePB.ListScenarioInfoReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache ListScenarioInfo err: %v", err)
		scenarioMap = tabInfoCache.scenarioMap
	} else {
		for _, scenarioInfo := range scenarioResp.GetInfos() {
			scenarioMap[scenarioInfo.GetTabId()] = scenarioInfo
			for _, tabInfo := range scenarioInfo.GetTabInfos() {
				scenarioMap[tabInfo.GetTabId()] = scenarioInfo
			}
		}
	}

	category2TabMap2, categoryIdMap, err := genCategoryMap(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache update categoryMap err %v, categoryMap len:%d, categoryIdMap:%d", err, len(category2TabMap2), len(categoryIdMap))
		sendCacheWarning(fmt.Sprintf("genCategoryMap old map categoryMap len:%d, categoryIdMap len:%d, err:%v", len(tabInfoCache.categoryMap), len(tabInfoCache.categoryIdMap), err))
		return err
	}

	for cid, tabInfos := range category2TabMap {
		if tabInfos2, ok := category2TabMap2[cid]; ok {
			if len(tabInfos) != len(tabInfos2) {
				log.ErrorWithCtx(ctx, "categoryIdMap cid:%d err, len:%d, len:%d", cid, len(tabInfos), len(tabInfos2))
			}
		}
	}

	baseBlocksMap, err := genBaseBlocksMap(ctx, tabIds)
	if err != nil {
		log.WarnWithCtx(ctx, " refreshTabInfoCache update baseBlocksMap err %v", err)
		//baseBlocksMap = tabInfoCache.baseBlocksMap
		sendCacheWarning(fmt.Sprintf("genBaseBlocksMap map len:%d, old map len:%d, err:%v", len(baseBlocksMap), len(tabInfoCache.baseBlocksMap), err))
		return err
	}

	officialRoomNameConfigMap, err := genOfficialRoomNameConfigMap(ctx)
	if err != nil {
		log.WarnWithCtx(ctx, " refreshTabInfoCache update OfficialRoomNameConfigMap err %v", err)
		officialRoomNameConfigMap = tabInfoCache.officialRoomConfigsMap
		sendCacheWarning(fmt.Sprintf("genOfficialRoomNameConfigMap old map len:%d, err:%v", len(officialRoomNameConfigMap), err))
	}

	/*categoryIdMap, err := genCategoryIdData(ctx)
	  if err != nil {
	  	log.WarnWithCtx(ctx, " refreshTabInfoCache update CategoryIdMap err %v", err)
	  	categoryIdMap = tabInfoCache.categoryIdMap
	  	sendCacheWarning(fmt.Sprintf("genCategoryIdData old map len:%d, err:%v", len(categoryIdMap), err))
	  }*/

	miniGameConfigMap, err := genMiniGameConfigMap(ctx)
	if err != nil {
		log.WarnWithCtx(ctx, "refreshTabInfoCache update MiniGameConfigMap err %v", err)
		miniGameConfigMap = tabInfoCache.miniGameConfigMap
		sendCacheWarning(fmt.Sprintf("genMiniGameConfigMap old map len:%d, err:%v", len(miniGameConfigMap), err))
	}

	gameLabelFilterMap, _, tabLabelItemMap := genRcmdInfoCache(ctx)

	blockRelationMap, err := genBlockRelationMap(ctx, tabIds, baseBlocksMap)
	if err != nil {
		//报错不更新，不阻碍其他缓存信息更新,需要告警
		log.ErrorWithCtx(ctx, "UserLine Time Err: refreshTabInfoCache update blockRelationMap len(tabIds:%d) len(baseBlockMap:%d) blockRelationMap len:%d err %v",
			len(tabIds), len(baseBlocksMap), len(blockRelationMap), err)
		sendCacheWarning(fmt.Sprintf("genBlockRelationMap old map len:%d, err:%v", len(blockRelationMap), err))
		return err
	}

	whiteListMap, err := genTabWhiteListMap(ctx)
	if err != nil {
		//报错不更新，不阻碍其他缓存信息更新,需要告警
		log.ErrorWithCtx(ctx, "UserLine Time Err: refreshTabInfoCache update tabWhiteListMap  err %v", err)
		whiteListMap = tabInfoCache.tabWhiteListMap
	}

	tabSubTypeMap, newTabIdMap, err := genNewTabInfoMaps(ctx)
	if err != nil {
		//报错不更新，不阻碍其他缓存信息更新,需要告警
		log.ErrorWithCtx(ctx, "UserLine Time Err: refreshTabInfoCache update tabSubTypeMap,newTabIdMap  err %v", err)
		tabSubTypeMap = tabInfoCache.tabSubTypeMap
		newTabIdMap = tabInfoCache.newTabIdMap
	}

	gamePalTabMap, err := genGamePalTabMap(ctx)
	if err != nil {
		//报错不更新，不阻碍其他缓存信息更新
		log.ErrorWithCtx(ctx, "refreshTabInfoCache update gamePalTabMap err %v", err)
		gamePalTabMap = tabInfoCache.gamePalTabMap
	}

	lightingCardNumMap, err := genLightNumMap(ctx)
	if err != nil {
		//报错不更新，不阻碍其他缓存信息更新
		log.ErrorWithCtx(ctx, "refreshTabInfoCache update lightingCardNumMap err %v", err)
		lightingCardNumMap = tabInfoCache.lightingCardNumMap
	}
	gameCardConfMap, err := genGameCardConfMap(ctx)
	if err != nil {
		//报错不更新，不阻碍其他缓存信息更新
		log.ErrorWithCtx(ctx, "refreshTabInfoCache update genGameCardConfMap err %v", err)
		gameCardConfMap = tabInfoCache.GameCardConfMap
	}

	tabFastCategoryInfoMap, fastTabErr := clients.ChannelPlayTabClient.GetFastPCCategoryConfigTabMap(ctx, &channel_play_tab.GetFastPCCategoryConfigReq{})
	if fastTabErr != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache GetFastPCCategoryConfigTabMap error: %v", fastTabErr)
	}

	fastPCCategoryConfigMap, err := genFastPCCategoryConfigMap(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache genFastPCCategoryConfigMap error: %v", err)
		fastPCCategoryConfigMap = tabInfoCache.fastPCCategoryConfigMap
	}

	tmpCache := &tabCache{
		minorityGameTabMap:      minorityGameTabMap,
		tabIdMap:                tabIdMap,
		releaseConditionMap:     releaseConditionMap,
		bindTabMap:              bindTabMap,
		bindCategoryMap:         bindCategoryMap,
		categoryMap:             category2TabMap,
		baseBlocksMap:           baseBlocksMap,
		officialRoomConfigsMap:  officialRoomNameConfigMap,
		categoryIdMap:           categoryIdMap,
		gameCardIdTabMap:        gameCardIdTabMap,
		uGameIdTabMap:           uGameIdTabMap,
		scenarioMap:             scenarioMap,
		miniGameConfigMap:       miniGameConfigMap,
		gameLabelFilterMap:      gameLabelFilterMap,
		blockRelationMap:        blockRelationMap,
		tabWhiteListMap:         whiteListMap,
		tabSubTypeMap:           tabSubTypeMap,
		newTabIdMap:             newTabIdMap,
		gamePalTabMap:           gamePalTabMap,
		lightingCardNumMap:      lightingCardNumMap,
		GameCardConfMap:         gameCardConfMap,
		tabLabelItemMap:         tabLabelItemMap,
		fastPCCategoryConfig:    tabFastCategoryInfoMap,
		fastPCCategoryConfigMap: fastPCCategoryConfigMap,
	}

	setTabInfoCache(tmpCache)

	//log.InfoWithCtx(ctx, "refreshTabInfoCache tabIds:%v", tabIds)
	log.InfoWithCtx(ctx, "refreshTabInfoCache minorityGameTabMap:%d, tabIdMap:%d, releaseConditionMap:%d, bindTabMap:%d, "+
		"bindCategoryMap：%d,  categoryMap:%d, baseBlocksMap:%d, officialRoomConfigsMap:%d, categoryIdMap:%d, gameCardIdTabMap:%d, "+
		"uGameIdTabMap:%d, scenarioMap:%d, miniGameConfigMap:%d, gameLabelFilterMap:%d,  blockRelationMap:%d,"+
		" tabWhiteListMap:%d gameCardConfMap:%d, tabLabelItemMap:%d",
		len(minorityGameTabMap), len(tabIdMap), len(releaseConditionMap), len(bindTabMap), len(bindCategoryMap), len(category2TabMap), len(baseBlocksMap),
		len(officialRoomNameConfigMap), len(categoryIdMap), len(gameCardIdTabMap), len(uGameIdTabMap), len(scenarioMap), len(miniGameConfigMap),
		len(gameLabelFilterMap), len(blockRelationMap), len(whiteListMap), len(gameCardConfMap), len(tabLabelItemMap))

	return nil
}

func genGameCardConfMap(ctx context.Context) (map[uint32]*game_card.GameCardConfInfo, error) {
	resp, err := clients.GameCardClient.GetAllGameCardConfFromCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "genGameCardConfMap err %v", err)
		return nil, err
	}
	gameCardConfMap := make(map[uint32]*game_card.GameCardConfInfo, len(resp))
	for _, v := range resp {
		gameCardConfMap[v.GetGameCardId()] = v
	}
	return gameCardConfMap, nil

}
func genLightNumMap(ctx context.Context) (map[uint32]uint32, error) {
	resp, err := clients.GamePalClient.GetTabPolishedCardCountMap(ctx, &game_pal.GetTabPolishedCardCountMapReq{})
	return resp.GetTabPolishedCardCountMap(), err
}

func genRcmdInfoCache(ctx context.Context) (
	gameLabelFilterMap map[uint32][]*channel_play.GameLabelFilter,
	elemToGameLabelMap map[uint32]map[uint32][]*topic_channel.GameLabel,
	tabLabelItemMap map[uint32]*tabPB.TabGameLabelItem) {
	ctx, cancel := context.WithTimeout(ctx, 500*time.Millisecond)
	defer cancel()
	var err error
	gameLabelFilterMap, tabLabelItemMap, err = genGameLabelFilterMap(ctx)
	if err != nil {
		//报错不更新，不阻碍其他缓存信息更新
		log.WarnWithCtx(ctx, "refreshTabInfoCache update gameLabelFilterMap err %v", err)
		gameLabelFilterMap = tabInfoCache.gameLabelFilterMap
		sendCacheWarning(fmt.Sprintf("genGameLabelFilterMap old map len:%d, err:%v", len(gameLabelFilterMap), err))

	}

	return
}
func genBlockRelationMap(ctx context.Context, tabIds []uint32, baseBlockMap map[uint32][]*tabPB.Block) (
	map[uint32][]*channel_play.DisplayBlockInfo, error) {
	resp, err := clients.TCTabClient.BatchGetBlockRelations(ctx, &tabPB.BatchGetBlockRelationsReq{
		TabId: tabIds,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "genBlockRelationMap len(tabIds):%d err:%v", len(tabIds), err)
		return nil, err
	}
	if len(resp.GetRelations()) == 0 {
		log.ErrorWithCtx(ctx, "genBlockRelationMap err: len 0")
		return nil, protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError)
	}
	relations := make(map[uint32][]*channel_play.DisplayBlockInfo, len(resp.GetRelations()))
	for k, blocks := range baseBlockMap {

		relationHandler := NewBlockRelationHandler(ctx, resp.GetRelations()[k].GetDisplayBlockInfo(), blocks, k)
		relations[k] = relationHandler.GetRelations()
	}
	return relations, nil
}

func genElemToGameLabelMap(ctx context.Context, baseBlockMap map[uint32][]*tabPB.Block, tabInfos map[uint32]*tabPB.Tab) (
	map[uint32]map[uint32][]*topic_channel.GameLabel, error) {
	elemToGameLabelMap := make(map[uint32]map[uint32][]*topic_channel.GameLabel)
	rcmdOptions := make([]*rcmdPb.GameBlockOption, 0, len(baseBlockMap))

	const labelReqLen = 30
	for _, info := range tabInfos {
		baseBlock, ok := baseBlockMap[info.GetId()]
		if !ok || info.GetHomePageType() != tabPB.HomePageType_HomePageTypeGAME || info.GetTabType() == tabPB.Tab_MINIGAME {
			// 音乐，小游戏都没有玩法标签
			continue
		}
		gameBlockOption := &rcmdPb.GameBlockOption{
			TabId:        info.GetId(),
			BlockOptions: utils.Convert2RcmdLabelOption(baseBlock),
		}
		rcmdOptions = append(rcmdOptions, gameBlockOption)
		if len(rcmdOptions)%labelReqLen == 0 {
			//resp, err := clients.RcmdChannelLabelClient.GetPublishGameLabels(ctx, &rcmdPb.GetPublishGameLabelsReq{
			//	GameBlockOption: rcmdOptions,
			//})
			//if err != nil {
			//	log.ErrorWithCtx(ctx, "genElemToGameLabelMap GetPublishGameLabels len(tabInfos:%d) len(rcmdOption:%d) err(%v)",
			//		len(tabInfos), len(rcmdOptions), err)
			//	return nil, err
			//}
			//for _, v := range resp.GetBlockOptionLabelList() {
			//	elem2GameLabel, ok := elemToGameLabelMap[v.GetTabId()]
			//	if !ok {
			//		elem2GameLabel = make(map[uint32][]*topic_channel.HotLabel, len(v.GetBlockOptionLabel()))
			//		for _, elem := range v.GetBlockOptionLabel() {
			//			elem2GameLabel[elem.GetBlockOptions().GetElemId()] = utils.ConvertRcmdGameLabel(elem.GetLabel())
			//		}
			//		elemToGameLabelMap[v.GetTabId()] = elem2GameLabel
			//	} else {
			//		log.ErrorWithCtx(ctx, "genElemToGameLabelMap resp.GetBlockOptionGameLabel repeated elemNew(%s) elemExit(%v)", v.String(), elem2GameLabel)
			//	}
			//}
			err := getRcmdLabel(ctx, rcmdOptions, elemToGameLabelMap)
			if err != nil {
				log.ErrorWithCtx(ctx, "genElemToGameLabelMap GetPublishGameLabels len(tabInfos:%d) len(rcmdOption:%d) err(%v)",
					len(tabInfos), len(rcmdOptions), err)
				return nil, err
			}
			rcmdOptions = rcmdOptions[:0]
		}
	}
	if len(rcmdOptions) > 0 {
		err := getRcmdLabel(ctx, rcmdOptions, elemToGameLabelMap)
		if err != nil {
			log.ErrorWithCtx(ctx, "genElemToGameLabelMap GetPublishGameLabels len(tabInfos:%d) len(rcmdOption:%d) err(%v)",
				len(tabInfos), len(rcmdOptions), err)
			return nil, err
		}
		rcmdOptions = rcmdOptions[:0]
	}

	log.DebugWithCtx(ctx, "genElemToGameLabelMap len(tabInfos:%d) len(rcmdOption:%d) len(elemToGameLabelMap:%d)",
		len(tabInfos), len(rcmdOptions), len(elemToGameLabelMap))
	return elemToGameLabelMap, nil

}

func getRcmdLabel(ctx context.Context, rcmdOptions []*rcmdPb.GameBlockOption, elemToGameLabelMap map[uint32]map[uint32][]*topic_channel.GameLabel) error {
	resp, err := clients.RcmdChannelLabelClient.GetPublishGameLabels(ctx, &rcmdPb.GetPublishGameLabelsReq{
		GameBlockOption: rcmdOptions,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "genElemToGameLabelMap GetPublishGameLabels len(rcmdOption:%d) err(%v)", len(rcmdOptions), err)
		return err
	}
	for _, v := range resp.GetBlockOptionLabelList() {
		elem2GameLabel, ok := elemToGameLabelMap[v.GetTabId()]
		if !ok {
			elem2GameLabel = make(map[uint32][]*topic_channel.GameLabel, len(v.GetBlockOptionLabel()))
			for _, elem := range v.GetBlockOptionLabel() {
				elem2GameLabel[elem.GetBlockOptions().GetElemId()] = utils.ConvertRcmdGameLabel(elem.GetLabel(), topic_channel.GameLabelType_LabelOfPublish)
			}
			elemToGameLabelMap[v.GetTabId()] = elem2GameLabel
		} else {
			log.ErrorWithCtx(ctx, "genElemToGameLabelMap resp.GetBlockOptionGameLabel repeated elemNew(%s) elemExit(%v)", v.String(), elem2GameLabel)
		}
	}
	return nil
}

func GenGameLabelFilterMapByTabs(ctx context.Context, tabItems []*tabPB.TabGameLabelItem, source rcmdPb.ConvertGameLabelsReq_SourceType, serviceInfo *grpc.ServiceInfo) (map[uint32][]*channel_play.GameLabelFilter, error) {
	var (
		tabLabelMap        = make(map[uint32]map[string]*topic_channel.GameLabel, len(tabItems))
		convertGameInfos   = make([]*rcmdPb.ConvertGameInfo, 0, len(tabItems))
		gameLabelFilterMap = make(map[uint32][]*channel_play.GameLabelFilter, len(tabItems))
	)

	if len(tabItems) == 0 {
		log.WarnWithCtx(ctx, "tabItems len 0")
		return gameLabelFilterMap, nil
	}

	for _, tabItem := range tabItems {
		convertGameInfo := &rcmdPb.ConvertGameInfo{TabId: tabItem.GetTabId()}
		for _, labelItem := range tabItem.GetItems() {
			convertGameInfo.Texts = append(convertGameInfo.Texts, labelItem.RelatedLabels...)
		}
		convertGameInfos = append(convertGameInfos, convertGameInfo)
	}

	convertGameLabels := &rcmdPb.ConvertGameLabelsReq{GameInfos: convertGameInfos, Source: source}
	if source == rcmdPb.ConvertGameLabelsReq_GameChannelList {
		convertGameLabels.Uid = serviceInfo.UserID
	}
	resp, err := clients.RcmdChannelLabelClient.ConvertGameLabels(ctx, convertGameLabels)
	if err != nil {
		log.ErrorWithCtx(ctx, "genGameLabelFilterMap ConvertGameLabels err: %v", err)
		return nil, err
	}
	//log.DebugWithCtx(ctx, "ConvertGameLabels req:%+v, req len:%d", convertGameInfos, len(convertGameInfos))
	if source == rcmdPb.ConvertGameLabelsReq_GameChannelList {
		log.DebugWithCtx(ctx, "=========test ConvertGameLabels: resp:%s, source:%d, len:%d,%d, covertInfo:%v", resp.String(), source, len(tabItems), len(convertGameInfos), convertGameInfos)
	}

	for _, tabLabel := range resp.GetLabels() {
		m := tabLabelMap[tabLabel.GetTabId()]
		if m == nil {
			m = make(map[string]*topic_channel.GameLabel)
			tabLabelMap[tabLabel.GetTabId()] = m
		}

		for _, label := range tabLabel.GetLabels() {
			m[label.GetOriginText()] = utils.ConvertOneRcmdGameLabel(label.GetLabels(), topic_channel.GameLabelType_Default)
		}
	}

	for _, tabLabel := range tabItems {
		for _, item := range tabLabel.GetItems() {
			gameLabelFilterMap[tabLabel.GetTabId()] = append(gameLabelFilterMap[tabLabel.GetTabId()], &channel_play.GameLabelFilter{
				LabelDisplayName: item.GetDisplayName(),
				Labels:           getLabelsFromMap(item.GetRelatedLabels(), tabLabelMap[tabLabel.GetTabId()]),
				ShowHot:          item.GetShowHot(),
				IsRcmd:           false,
			})
		}
	}

	if source == rcmdPb.ConvertGameLabelsReq_GameChannelList {
		log.Infof("genGameLabelFilterMap genGameLabelFilterMapByTabs: %+v", gameLabelFilterMap)
	}
	return gameLabelFilterMap, nil
}

func genGameLabelFilterMap(ctx context.Context) (map[uint32][]*channel_play.GameLabelFilter, map[uint32]*tabPB.TabGameLabelItem, error) {
	const labelReqLen = 30

	resp, err := clients.TCTabClient.BatchGetGameLabelItems(ctx, &tabPB.BatchGetGameLabelItemsReq{
		TabIds: []uint32{},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "genGameLabelFilterMap BatchGetGameLabelItems error: %v", err)
		return nil, nil, err
	}

	var allTabItemMap = make(map[uint32]*tabPB.TabGameLabelItem, len(resp.GetItems()))
	for _, v := range resp.GetItems() {
		allTabItemMap[v.GetTabId()] = v
	}
	log.DebugWithCtx(ctx, "genGameLabelFilterMap test len(allTabItemMap):%d,%d,%d", len(allTabItemMap), len(resp.GetItems()))

	var (
		tabItems           = make([]*tabPB.TabGameLabelItem, 0, labelReqLen)
		gameLabelFilterMap = make(map[uint32][]*channel_play.GameLabelFilter)
	)

	for i, tabItem := range resp.GetItems() {

		tabItems = append(tabItems, tabItem)

		if i%(labelReqLen-1) == 0 || i == len(resp.GetItems())-1 {
			gameLabelFilterMapTmp, err := GenGameLabelFilterMapByTabs(ctx, tabItems, rcmdPb.ConvertGameLabelsReq_DEFAULT, nil)
			if err != nil {
				log.ErrorWithCtx(ctx, "genGameLabelFilterMap genGameLabelFilterMapByTabs err: %v", err)
				return nil, nil, err
			}
			for k, v := range gameLabelFilterMapTmp {
				gameLabelFilterMap[k] = v
			}

			tabItems = tabItems[:0]
		}
	}

	log.Infof("genGameLabelFilterMap gameLabelFilterMap: %+v, len:%d,%d:%d", gameLabelFilterMap, len(gameLabelFilterMap), len(allTabItemMap), len(resp.GetItems()))
	return gameLabelFilterMap, allTabItemMap, nil

}

/*func genGameLabelItem(ctx context.Context, tabGameLabel *tabPB.TabGameLabelItem) ([]*channel_play.GameLabelFilter, error) {
	res := make([]*channel_play.GameLabelFilter, 0, len(tabGameLabel.GetItems()))
	labels := make([]string, 0)
	labelToGameLabelMap := make(map[string]*topic_channel.HotLabel)
	for _, l := range tabGameLabel.GetItems() {
		labels = append(labels, l.GetRelatedLabels()...)
	}
	resp, err := clients.RcmdChannelLabelClient.ConvertGameLabels(ctx, &rcmdPb.ConvertGameLabelsReq{
		TabId: tabGameLabel.GetTabId(),
		Texts: labels,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "genGameLabelItem RcmdChannelLabelClient.ConvertGameLabels err(%v)", err)
		return nil, err
	}
	for _, v := range resp.GetLabels() {
		labelToGameLabelMap[v.OriginText] = utils.ConvertOneRcmdGameLabel(v.GetLabels())
	}
	for _, l := range tabGameLabel.GetItems() {
		temp := &channel_play.GameLabelFilter{
			LabelDisplayName: l.GetDisplayName(),
			Labels:           getLabelsFromMap(l.GetRelatedLabels(), labelToGameLabelMap),
			ShowHot:          l.GetShowHot(),
			IsRcmd:           false,
		}
		res = append(res, temp)
	}

	return res, nil

}*/

func getLabelsFromMap(relatedLabels []string, labelMap map[string]*topic_channel.GameLabel) []*topic_channel.GameLabel {
	if labelMap == nil {
		log.Warnf("getLabelsFromMap labelMap nil")
		return nil
	}

	res := make([]*topic_channel.GameLabel, 0, len(relatedLabels))
	for _, label := range relatedLabels {
		if gameLabelInfo, ok := labelMap[label]; ok {
			res = append(res, gameLabelInfo)
		}
	}
	return res

}

/*func genCategoryIdData(ctx context.Context) (map[uint32]*tabPB.Category, error) {
	categoryIdMap := make(map[uint32]*tabPB.Category)
	categoryResp, err := clients.TCTabClient.GetCategoryTitleList(ctx, &tabPB.GetCategoryTitleReq{Skip: 0, Limit: 1000})
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache GetCategoryTitleList error: %v", err)
		return nil, err
	}

	for _, v := range categoryResp.GetCategoryList() {
		categoryIdMap[v.CategoryId] = v
	}
	return categoryIdMap, nil
}*/

func genBaseBlocksMap(ctx context.Context, tabIds []uint32) (baseBlocksMap map[uint32][]*tabPB.Block, err error) {
	baseBlocksMap = make(map[uint32][]*tabPB.Block)
	resp, err := clients.TCTabClient.BatchGetBlocks(ctx, &tabPB.BatchGetBlocksReq{TabId: tabIds})
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache genBaseBlocksMap err %v", err)
		return baseBlocksMap, err
	}
	if len(resp.GetData()) == 0 {
		log.ErrorWithCtx(ctx, "genBaseBlocksMap err: len 0")
		return baseBlocksMap, protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError)
	}
	// k:tabId, val:blocks
	for k, v := range resp.GetData() {
		if _, ok := baseBlocksMap[k]; !ok {
			baseBlocksMap[k] = make([]*tabPB.Block, 0)
			baseBlocksMap[k] = append(baseBlocksMap[k], v.GetBlocks()...)
		}
	}
	return baseBlocksMap, nil
}

func genCategoryMap(ctx context.Context) (category2TabMap map[uint32][]*tabPB.Tab, categoryIdMap map[uint32]*tabPB.Category, err error) {
	category2TabMap = make(map[uint32][]*tabPB.Tab)
	categoryIdMap = make(map[uint32]*tabPB.Category)
	categoryResp, err := clients.TCTabClient.GetCategoryTitleList(ctx, &tabPB.GetCategoryTitleReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "genCategoryMap GetCategoryTitleList err %v", err)
		return nil, nil, err
	}
	if len(categoryResp.GetCategoryList()) == 0 {
		log.ErrorWithCtx(ctx, "genCategoryMap err: len 0")
		return nil, nil, protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError)
	}
	categoryIds := make([]uint32, 0)
	for _, v := range categoryResp.GetCategoryList() {
		categoryIds = append(categoryIds, v.GetCategoryId())
		categoryIdMap[v.CategoryId] = v
	}
	//这个保存到快速下期热门玩法下了后可以去掉
	//为了避免耦合这部份要放到server层去聚合
	log.InfoWithCtx(ctx, "genCategoryMap categoryIds:%v", categoryIds)
	tabOfCategoryResp, err1 := clients.TCTabClient.GetTabsByCategoryIds(ctx, &tabPB.GetTabsByCategoryIdsReq{CategoryIds: categoryIds})
	if err1 != nil {
		log.ErrorWithCtx(ctx, "genCategoryMap GetTabsByCategoryIds err %v", err1)
		return nil, nil, err1
	}
	for k, v := range tabOfCategoryResp.GetCategoryTabMap() {
		category2TabMap[k] = make([]*tabPB.Tab, len(v.GetTabs()))
		category2TabMap[k] = v.GetTabs()
	}
	return category2TabMap, categoryIdMap, nil
}

func genMiniGameConfigMap(ctx context.Context) (map[uint32]*topic_channel.MiniGameConfig, error) {
	m := make(map[uint32]*topic_channel.MiniGameConfig)

	resp, err := clients.ChannelOpenGameClient.GetSupportGameList(ctx, &channel_open_game.GetSupportGameListReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "genMiniGameConfigMap GetSupportGameList err %v", err)
		return m, err
	}

	for _, v := range resp.GetBase() {
		m[v.GetGameId()] = &topic_channel.MiniGameConfig{
			CpId:               v.GetCpId(),
			GameId:             v.GetGameId(),
			GameVersion:        v.GetGameVersion(),
			GameName:           v.GetGameName(),
			GamePackage:        v.GetGamePackage(),
			GameUrl:            v.GetGameUrl(),
			EngineVer:          v.GetEngineVer(),
			GameMemberCntLimit: v.GetGameMemberCntLimit(),
			EngineType:         v.GetEngineType(),
			GameAppLimit:       v.GetGameAppLimit(),
			GamePlatformLimit:  v.GetGamePlatformLimit(),
			MiniGameEngine:     miniGameConfEngine(v.GetGameExtraProperties()),
			GameDigest:         v.GetGameDigest(),
			GameResUrl:         v.GetGameResUrl(),
			GameResDigest:      v.GetGameResDigest(),
			MainPackageUrl:     v.GetMainPackageUrl(),
		}
	}

	return m, nil
}

func miniGameConfEngine(s string) []*topic_channel.MiniGameEngine {
	type GameEngine struct {
		OtherEngine map[string]struct {
			GameUrl string `json:"gameUrl"`
			Name    string `json:"name"`
			ResUrl  string `json:"resUrl"`
		} `json:"other_engine"`
	}

	if len(s) == 0 {
		return nil
	}

	var engine GameEngine
	err := json.Unmarshal([]byte(s), &engine)
	if err != nil {
		log.Errorf("miniGameConfEngine unmarshal s(%s) err: %v", s, err)
		return nil
	}

	engines := make([]*topic_channel.MiniGameEngine, 0)
	for k, v := range engine.OtherEngine {
		e := &topic_channel.MiniGameEngine{
			Name:    v.GameUrl,
			GameUrl: v.GameUrl,
			ResUrl:  v.ResUrl,
		}

		id, _ := strconv.Atoi(k)
		e.EngineId = uint32(id)

		engines = append(engines, e)
	}

	return engines
}

func genFastPCCategoryConfigMap(ctx context.Context) (map[uint32]*channel_play_tab.FastPCCategoryConfig, error) {
	categoryList, err := clients.ChannelPlayTabClient.GetFastPCCategoryConfig(ctx, &channel_play_tab.GetFastPCCategoryConfigReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "genFastPCCategoryConfigMap GetFastPCCategoryConfig error: %v", err)
		return nil, err
	}
	fastPCCategoryConfigMap := make(map[uint32]*channel_play_tab.FastPCCategoryConfig, len(categoryList))
	for _, config := range categoryList {
		fastPCCategoryConfigMap[config.GetId()] = config
	}
	return fastPCCategoryConfigMap, nil
}

func GetTabsOfCategoryCache() map[uint32][]*tabPB.Tab {
	tabsOfCategoryMap := make(map[uint32][]*tabPB.Tab, len(tabInfoCache.categoryMap))
	for k, v := range tabInfoCache.categoryMap {
		tabs := make([]*tabPB.Tab, 0, len(v))
		tabs = append(tabs, v...)
		tabsOfCategoryMap[k] = tabs
	}
	return tabsOfCategoryMap
}

func GetBussBindTabMapCache(tabId, clientType uint32) []*tabPB.BusinessBlock {
	if protocol.IsFastPcClientType(clientType) && tabId == conf.PublicSwitchConfig.GetMuseChatTabId() {
		tabId = conf.PublicSwitchConfig.GetFastPcChatTabId()
	}
	return tabInfoCache.bindTabMap[tabId]
}

func GetBussBindCategoryMapCache() map[uint32][]*tabPB.BusinessBlock {
	bindCategoryMap := make(map[uint32][]*tabPB.BusinessBlock, len(tabInfoCache.bindCategoryMap))
	for k, v := range tabInfoCache.bindCategoryMap {
		bindCategoryMap[k] = make([]*tabPB.BusinessBlock, len(v))
		bindCategoryMap[k] = v
	}
	return bindCategoryMap
}

func GetBaseBlocksMapCache() map[uint32][]*tabPB.Block {
	return tabInfoCache.baseBlocksMap
}

func GetBaseBlocksByTabId(tabId, clientType uint32) []*tabPB.Block {
	if protocol.IsFastPcClientType(clientType) && tabId == conf.PublicSwitchConfig.GetMuseChatTabId() {
		tabId = conf.PublicSwitchConfig.GetFastPcChatTabId()
	}
	return tabInfoCache.baseBlocksMap[tabId]
}

func GetLightCardNum(tabId uint32) uint32 {
	return tabInfoCache.lightingCardNumMap[tabId]
}

func NewCache(ctx context.Context, cacheClients *clients.CacheClients) error {
	clients.Setup(cacheClients)

	err := refreshTabInfoCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache error :%v", err)
		return err
	}
	go func() {
		ticker := time.NewTicker(time.Second * 30)
		defer func() {
			ticker.Stop()
		}()

		for range ticker.C {
			refreshCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			timerRefreshTabInfoCache(refreshCtx)
			cancel()
		}
	}()

	err = refreshSwitchCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewCache refreshSwitchCache error :%v", err)
		return err
	}
	go func() {
		ticker := time.NewTicker(time.Second * 10)
		defer ticker.Stop()

		for range ticker.C {
			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			timerRefreshSwitchCache(ctx)
			cancel()
		}
	}()
	return nil
}

func genOfficialRoomNameConfigMap(ctx context.Context) (out map[uint32][]*tabPB.OfficialRoomNameConfig, err error) {
	out = make(map[uint32][]*tabPB.OfficialRoomNameConfig)

	config, err := clients.TCTabClient.BatchGetOfficialRoomNameConfig(ctx, &tabPB.BatchGetOfficialRoomNameConfigReq{TabIds: []uint32{}})
	if err != nil {
		log.ErrorWithCtx(ctx, "genOfficialRoomNameConfigMap BatchGetOfficialRoomNameConfig err%v", err)
		return out, err
	}
	for _, v := range config.GetRoomNameConfigForTabs() {
		out[v.GetTabId()] = v.GetRoomNameConfigList()
	}
	return out, nil
}

func GetOfficialRoomNameConfigMap() map[uint32][]*tabPB.OfficialRoomNameConfig {
	return tabInfoCache.officialRoomConfigsMap
}

func GetOfficialRoomNameByTabId(tabId, clientType uint32) []*tabPB.OfficialRoomNameConfig {
	if protocol.IsFastPcClientType(clientType) && tabId == conf.PublicSwitchConfig.GetMuseChatTabId() {
		tabId = conf.PublicSwitchConfig.GetFastPcChatTabId()
	}
	return tabInfoCache.officialRoomConfigsMap[tabId]
}

func GetCategoryIdMap() map[uint32]*tabPB.Category {
	return tabInfoCache.categoryIdMap
}

func GetGameCardIdTabMap() map[uint32][]*tabPB.Tab {
	return tabInfoCache.gameCardIdTabMap
}

func (t *tabCache) GetUGameIdTabMapCache() map[uint32]*MinorityGameInfo {
	return tabInfoCache.uGameIdTabMap
}

func GetScenarioMap() map[uint32]*mysteryplacePB.ScenarioInfo {
	return tabInfoCache.scenarioMap
}

func GetScenarioByTabId(tabId uint32) *mysteryplacePB.ScenarioInfo {
	m := GetScenarioMap()

	var s *mysteryplacePB.ScenarioInfo
	if m != nil {
		s = m[tabId]
	}

	return s
}

func GetMiniGameConfig(tagId uint32) *topic_channel.MiniGameConfig {
	m := tabInfoCache.miniGameConfigMap

	var cfg *topic_channel.MiniGameConfig
	if m != nil {
		cfg = m[tagId]
	} else {
		log.Warnf("minigame config tagId(%d) not found", tagId)
	}

	return cfg
}

func GetGameLabelFilterMap() map[uint32][]*channel_play.GameLabelFilter {
	return tabInfoCache.gameLabelFilterMap
}

// GetBlockRelationByTabId 根据tabId获取对应的block关联关系
func GetBlockRelationByTabId(tabId, clientType uint32) []*channel_play.DisplayBlockInfo {
	cache := tabInfoCache.blockRelationMap
	if cache == nil {
		return nil
	}
	if protocol.IsFastPcClientType(clientType) && tabId == conf.PublicSwitchConfig.GetMuseChatTabId() {
		tabId = conf.PublicSwitchConfig.GetFastPcChatTabId()
	}
	if relations, ok := cache[tabId]; ok {
		return relations
	}
	return []*channel_play.DisplayBlockInfo{}
}

// 玩法白名单缓存,key tabId value uid数组
func genTabWhiteListMap(ctx context.Context) (map[uint32]map[uint32]bool, error) {
	resp, err := clients.ChannelPlayTabClient.BatchGetWhiteUidListByTabIds(ctx, &channelPlayTabPb.BatchGetWhiteUidListByTabIdsReq{
		TabIds:  []uint32{},
		Source:  "channel-play-logic_cache",
		NoCache: true,
	})
	if err != nil {
		return nil, err
	}
	listMap := make(map[uint32]map[uint32]bool, len(resp))
	for k, v := range resp {
		uidMap := make(map[uint32]bool, len(v))
		for _, uid := range v {
			uidMap[uid] = true
		}
		listMap[k] = uidMap
	}
	return listMap, nil
}

func GetWhiteListByTabId(tabId uint32) map[uint32]bool {
	res := make(map[uint32]bool)
	if tabInfoCache.tabWhiteListMap == nil {
		return res
	}
	if uidMap, ok := tabInfoCache.tabWhiteListMap[tabId]; ok {
		res = uidMap
	}
	return res
}

func GetWhiteList() map[uint32]map[uint32]bool {
	return tabInfoCache.tabWhiteListMap
}

// 新tab表缓存, tabSubTypeMap key:tabSubType value:tab列表, newTabIdMap key:tabId value:tab
func genNewTabInfoMaps(ctx context.Context) (tabSubTypeMap map[uint32][]*channel_play_tab.Tab, newTabIdMap map[uint32]*channel_play_tab.Tab, err error) {
	resp, err := clients.ChannelPlayTabClient.GetTabs(ctx, &channelPlayTabPb.GetTabsReq{
		NoCache:     false,
		OnlyVisible: true,
	})
	if err != nil {
		return nil, nil, err
	}

	tabSubTypeMap = make(map[uint32][]*channel_play_tab.Tab)
	newTabIdMap = make(map[uint32]*channel_play_tab.Tab, len(resp.GetTabs()))
	for _, tab := range resp.GetTabs() {
		tabSubType := uint32(tab.GetTabSubType())
		tabSubTypeMap[tabSubType] = append(tabSubTypeMap[tabSubType], tab)
		newTabIdMap[tab.GetTabId()] = tab
	}
	return tabSubTypeMap, newTabIdMap, nil
}

func GetNewTabListCacheByTabSubType(tabSubType uint32) []*channel_play_tab.Tab {
	tabList := make([]*channel_play_tab.Tab, 0)
	if tabInfoCache.tabSubTypeMap == nil {
		return tabList
	}
	tabList = tabInfoCache.tabSubTypeMap[tabSubType]
	return tabList
}

func GetNewTabInfoCacheById(tabId uint32) *channel_play_tab.Tab {
	if tabInfoCache.newTabIdMap == nil {
		return nil
	}
	return tabInfoCache.newTabIdMap[tabId]
}

// 搭子卡tab缓存, key:tabId value:玩法搭子卡的configTab
func genGamePalTabMap(ctx context.Context) (map[uint32]*game_ugc_content.ConfigTabInfo, error) {
	resp, err := clients.GameUgcContentClient.GetGamePalTabs(ctx, &game_ugc_content.GetGamePalTabsReq{})
	if err != nil {
		return nil, err
	}

	return resp.GetGamePalTabs(), nil
}

func GetGamePalTabCacheByTabId(tabId uint32) *game_ugc_content.ConfigTabInfo {
	if tabInfoCache.gamePalTabMap == nil {
		return nil
	}
	return tabInfoCache.gamePalTabMap[tabId]
}

func (t *tabCache) GetGameCardInfoByTabId(tabId uint32) *game_card.GameCardConfInfo {
	if tabInfo, ok := t.tabIdMap[tabId]; !ok {
		return nil
	} else {
		return t.GameCardConfMap[tabInfo.GetGameInfo().GetGameCardId()]
	}
}

func (t *tabCache) GetFastPCCategoryConfigMap() map[uint32][]*channel_play_tab.FastPCCategoryConfig {
	return t.fastPCCategoryConfig
}

func (t *tabCache) GetFastPCCategoryConfigByIdMap() map[uint32]*channel_play_tab.FastPCCategoryConfig {
	return t.fastPCCategoryConfigMap
}

func (t *tabCache) GetTabIdsByFastPCCategoryIds(categoryIds []uint32) []uint32 {
	if t.fastPCCategoryConfigMap == nil {
		return nil
	}
	tabIds := make([]uint32, 0)
	tabIdMap := make(map[uint32]struct{})
	for _, categoryId := range categoryIds {
		if config, ok := t.fastPCCategoryConfigMap[categoryId]; ok {
			for _, tabId := range config.GetTabIds() {
				if _, exists := tabIdMap[tabId]; !exists {
					tabIds = append(tabIds, tabId)
					tabIdMap[tabId] = struct{}{}
				}
			}
		}
	}

	return tabIds
}
