package server

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	hobby_channel "golang.52tt.com/protocol/app/hobby-channel"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/services/channel-play-logic/internal/breaker"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"math"
)

const (
	InvalidZeroId = 0              //id为0
	InvalidMaxId  = math.MaxUint32 //id为-1
	EmptyString   = ""             //空字符串
	DiyMaxNum     = 10             //diy最大数量
)

func (s *ChannelPlayLogic) GetSecondaryFilter(ctx context.Context, in *channel_play.GetSecondaryFilterReq) (
	out *channel_play.GetSecondaryFilterResp, err error) {
	out = &channel_play.GetSecondaryFilterResp{}
	if conf.ChannelPlayLogicConfig.GetIsSentinelTest(breaker.SecondaryFilter) {
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError, breaker.SecondaryFilter+" sentinel test")
	}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	// 不限，直接返回空
	if in.GetTabId() == InvalidMaxId || in.GetTabId() == InvalidZeroId {
		return out, nil
	}

	out, err = s.SecondaryFilterMgr.GetSecondaryFilter(ctx, in, serviceInfo)
	if err != nil {
		rpcErr, ok := err.(protocol.ServerError)
		if ok {
			if rpcErr.Code() == status.ErrTopicChannelTabNotFound {
				log.WarnWithCtx(ctx, "GetSecondaryFilter fail, err%v, in:%v", err, in)
			} else {
				log.ErrorWithCtx(ctx, "GetSecondaryFilter fail, err%v, in:%v", err, in)
			}
		} else {
			log.ErrorWithCtx(ctx, "GetSecondaryFilter fail, err%v, in:%v", err, in)
		}
		return out, err
	}
	log.InfoWithCtx(ctx, "GetSecondaryFilter clientType:%d, in:%v len(outBlock):%d", serviceInfo.ClientType, in, len(out.GetBlocks()))
	return out, nil
}

func (s *ChannelPlayLogic) GetSecondaryFilterByCategory(ctx context.Context, in *channel_play.GetSecondaryFilterByCategoryReq) (
	out *channel_play.GetSecondaryFilterByCategoryResp, err error) {
	out = &channel_play.GetSecondaryFilterByCategoryResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "获取用户信息失败")
	}
	log.DebugWithCtx(ctx, "GetSecondaryFilterByCategory uid %v, in %v", serviceInfo.UserID, in)

	out = &channel_play.GetSecondaryFilterByCategoryResp{
		Items:          make([]*hobby_channel.GameHomePageFilterItem, 0),
		BusinessBlocks: make([]*channel_play.BusinessFilterItem, 0),
	}

	if protocol.IsFastPcClientType(uint32(serviceInfo.ClientType)) {
		out.Items, out.BusinessBlocks, err = s.SecondaryFilterMgr.GetSecondaryFilterByFastPCCategory(ctx, in.CategoryId, serviceInfo, in.GetChannelPkg())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetSecondaryFilterByCategory uid %v, err %v", serviceInfo.UserID, err)
		}
	} else {
		out.Items, out.BusinessBlocks, err = s.SecondaryFilterMgr.GetSecondaryFilterByCategory(ctx, in.CategoryId, serviceInfo, in.GetChannelPkg())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetSecondaryFilterByCategory uid %v, err %v", serviceInfo.UserID, err)
		}
	}
	log.DebugWithCtx(ctx, "GetSecondaryFilterByCategory uid %v, out %v", serviceInfo.UserID, out)
	return out, err
}

func (s *ChannelPlayLogic) GetGameHomePageFilter(ctx context.Context, in *hobby_channel.GetGameHomePageFilterReq) (
	out *hobby_channel.GetGameHomePageFilterResp, err error) {
	out = &hobby_channel.GetGameHomePageFilterResp{}
	log.InfoWithCtx(ctx, "GetGameHomePageFilter DEBUG in %s", in.String())
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	uid := serviceInfo.UserID
	if !ok || uid == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "获取用户信息失败")
	}

	switch in.GetEntry_Type() {
	case hobby_channel.MysteryEntryType_FASTPCHomePageEntry:
		out.Items, err = s.GameFilterMgr.GetFastPcGameHomePageFilter(ctx, serviceInfo)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetFastPcGameHomePageFilter uid(%d) in(%s) err(%+v)", uid, in.String(), err)
			return out, err
		}

	default:
		out.Items, err = s.GameFilterMgr.GetGameHomePageFilter(ctx, in.GetSelfGameIds(), in.GetActiveIds(), in.GetEntry_Type(), in.GetChannelPkg())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGameHomePageFilter uid(%d) in(%s) err(%+v)", uid, in.String(), err)
			return out, err
		}
	}
	return
}

func (s *ChannelPlayLogic) GetGameHomePageDIYFilter(ctx context.Context, in *hobby_channel.GetGameHomePageDIYFilterReq) (
	out *hobby_channel.GetGameHomePageDIYFilterResp, err error) {
	out = &hobby_channel.GetGameHomePageDIYFilterResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "获取用户信息失败")
	}
	uid := serviceInfo.UserID
	out.Items, err = s.GameFilterMgr.GetGameHomePageDIYFilter(ctx, in.Entry_Type, in.GetChannelPkg())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameHomePageDIYFilter uid(%d) EntryType(%v) error:%+v", uid, in.GetEntry_Type(), err)
		return out, protocol.ToServerError(err)
	}
	log.InfoWithCtx(ctx, "GetGameHomePageDIYFilter in:%s, out:%s", in.String(), out.String())
	return
}

func (s *ChannelPlayLogic) SetGameHomePageDIYFilter(ctx context.Context, in *hobby_channel.SetGameHomePageDIYFilterReq) (
	out *hobby_channel.SetGameHomePageDIYFilterResp, err error) {

	log.InfoWithCtx(ctx, "SetGameHomePageDIYFilter in %s", in.String())
	out = &hobby_channel.SetGameHomePageDIYFilterResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	uid := serviceInfo.UserID
	if in.GetEntry_Type() == hobby_channel.MysteryEntryType_FASTPCHomePageEntry {
		if len(in.GetItems()) > DiyMaxNum {
			in.Items = in.Items[:DiyMaxNum]
		}
	}
	err = s.GameFilterMgr.SetGameHomePageDIYFilter(ctx, uid, in.GetItems(), in.GetEntry_Type())
	if err != nil {
		log.ErrorWithCtx(ctx, "SetGameHomePageDIYFilter uid(%d) items(%v) entryType(%v) error:%+v", uid, in.GetItems(), in.GetEntry_Type(), err)
		return out, err
	}
	return
}

func (s *ChannelPlayLogic) GetFilterItemByEntrance(ctx context.Context, in *channel_play.GetFilterItemByEntranceReq) (
	out *channel_play.GetFilterItemByEntranceResp, err error) {
	out = &channel_play.GetFilterItemByEntranceResp{}

	if conf.ChannelPlayLogicConfig.GetIsSentinelTest(breaker.NewFilterWithEntrance) {
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError, breaker.NewFilterWithEntrance+" sentinel test")
	}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "获取用户信息失败")
	}
	uid := serviceInfo.UserID
	out.Items, out.ComprehensiveItems, out.ShowFilterWords, err = s.MutiEntryFilterMgr.GetFilterItemByEntrance(ctx, serviceInfo, in)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameHomePageFilter uid(%v) in(%v) err(%+v)", uid, in, err)
		return out, err
	}
	if conf.ChannelPlayLogicConfig.GetLogSwitch() {
		log.InfoWithCtx(ctx, "GetFilterItemByEntrance req:%s, resp:%s",
			in.String(), out.String())
	} else {
		log.InfoWithCtx(ctx, "GetFilterItemByEntrance req:%s, len(items):%d, len(cItem):%d",
			in.String(), len(out.GetItems()), len(out.GetComprehensiveItems()))
	}

	return

}

func (s *ChannelPlayLogic) GetDIYFilterByEntrance(ctx context.Context, in *channel_play.GetDIYFilterByEntranceReq) (
	out *channel_play.GetDIYFilterByEntranceResp, err error) {
	out = &channel_play.GetDIYFilterByEntranceResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "获取用户信息失败")
	}
	out.DiyItem, err = s.MutiEntryFilterMgr.GetDIYFilterByEntrance(ctx, serviceInfo, in)
	if err != nil {
		return out, err
	}

	// 找到music_business_id=xxx与game_business_id=86的两个item，如果有，就删掉game_business_id=86的项，只保留最新的music_business_id=xxx
	out.DiyItem = s.removeDuplicateMusicAndGameItems(ctx, out.DiyItem)

	return out, nil
}

func (s *ChannelPlayLogic) SetDIYFilterByEntrance(ctx context.Context, in *channel_play.SetDIYFilterByEntranceReq) (
	out *channel_play.SetDIYFilterByEntranceResp, err error) {
	out = &channel_play.SetDIYFilterByEntranceResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "获取用户信息失败")
	}
	out.IsInAbtest, out.NeedJumpTabId, err = s.MutiEntryFilterMgr.SetDIYFilterByEntrance(ctx, serviceInfo, in)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetDIYFilterByEntrance serviceInfo(%s) in(%s) err(%+v)", serviceInfo.String(), in.String(), err)
		return out, err
	}
	log.InfoWithCtx(ctx, "SetDIYFilterByEntrance serviceInfo(%s) in(%s) out(%+s)", serviceInfo.String(), in.String(), out.String())
	return out, nil
}

// GetPublishOptionGuide 获取发布条件弹窗，房间标题引导筛选项
func (s *ChannelPlayLogic) GetPublishOptionGuide(ctx context.Context, in *channel_play.GetPublishOptionGuideReq) (
	out *channel_play.GetPublishOptionGuideResp, err error) {
	out = &channel_play.GetPublishOptionGuideResp{}

	//参数校验
	if !s.checkPublishOptionGuideParm(in) {
		log.WarnWithCtx(ctx, "GetPublishOptionGuide Invalid param in:%s", in.String())
		return out, err
	}
	svcInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}

	out.PublishOptionElems, out.RcmdOptionElems, out.RcmdInputElems, err = s.SecondaryFilterMgr.GetPublishOptionGuide(ctx, in.GetTabId(), in.GetUid(), in.GetRoomName())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPublishOptionGuide in:%s, err:%+v", in.String(), err)
	}
	log.InfoWithCtx(ctx, "GetPublishOptionGuide in:%+v, svcInfo:%+v, out:%+v", in, svcInfo, out)
	return out, err
}

// 引导词接口参数校验
func (s *ChannelPlayLogic) checkPublishOptionGuideParm(in *channel_play.GetPublishOptionGuideReq) bool {
	if in.GetTabId() == InvalidMaxId || in.GetTabId() == InvalidZeroId || in.GetUid() == InvalidZeroId ||
		in.GetRoomName() == EmptyString {
		return false
	}
	return true
}

// removeDuplicateMusicAndGameItems 处理音乐和游戏筛选项的冲突
func (s *ChannelPlayLogic) removeDuplicateMusicAndGameItems(ctx context.Context, items []*channel_play.DIYItem) []*channel_play.DIYItem {
	if len(items) == 0 {
		return items
	}

	targetGameBusinessId := conf.HomePageConfigs.GetPiaFilterOption().GameBusinessId
	targetMusicBusinessId := conf.HomePageConfigs.GetPiaFilterOption().MusicBusinessId
	targetTabId := conf.HomePageConfigs.GetPiaFilterOption().TabId
	var hasMusicItem bool
	var gameItemIndex = -1
	// 第一遍遍历：检查是否同时存在music_business_id和game_business_id=86的项
	for i, item := range items {

		if item.GetDiyItem() == nil {
			continue
		}

		diyItem := item.GetDiyItem()
		filterType := diyItem.GetFilterItemType()

		// 检查是否有音乐类型的筛选项
		if filterType == channel_play.FilterItemType_MUSIC_ITEM || filterType == channel_play.FilterItemType_MUSIC_FILTER {
			if diyItem.GetMusicBusinessId() == targetMusicBusinessId {
				hasMusicItem = true
				log.DebugWithCtx(ctx, "removeDuplicateMusicAndGameItems found music item: music_business_id=%s", diyItem.GetMusicBusinessId())
			}
		}
		if filterType == channel_play.FilterItemType_GAME_CATEGORY && diyItem.GetGameBusinessId() == targetGameBusinessId {
			gameItemIndex = i
			log.DebugWithCtx(ctx, "removeDuplicateMusicAndGameItems found target game item: game_business_id=%d at index %d", targetGameBusinessId, i)

		}

		// 检查是否有game_business_id=86的项
		if filterType == channel_play.FilterItemType_GAME_TAB && diyItem.GetGameBusinessId() == targetTabId {
			gameItemIndex = i
			log.DebugWithCtx(ctx, "removeDuplicateMusicAndGameItems found target game item: game_business_id=%d at index %d", targetGameBusinessId, i)

		}
	}

	// 如果同时存在音乐项和game_business_id=86的项，则删除game_business_id=86的项
	if hasMusicItem && gameItemIndex >= 0 {
		log.InfoWithCtx(ctx, "removeDuplicateMusicAndGameItems removing game_business_id=%d item due to music item conflict", targetGameBusinessId)

		// 创建新的切片，排除game_business_id=86的项
		result := make([]*channel_play.DIYItem, 0, len(items)-1)
		for i, item := range items {
			if i != gameItemIndex {
				result = append(result, item)
			}
		}
		return result
	}
	log.DebugWithCtx(ctx, "removeDuplicateMusicAndGameItems no conflict found, returning original items")
	return items
}
