package server

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	"golang.52tt.com/protocol/common/status"
)

func (s *ChannelPlayLogic) GetDefaultRoomNameList(ctx context.Context, in *channel_play.GetDefaultRoomNameListReq) (
	out *channel_play.GetDefaultRoomNameListResp, err error) {
	out = &channel_play.GetDefaultRoomNameListResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok || serviceInfo.UserID == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	if in.GetTabId() == 0 {
		log.WarnWithCtx(ctx, "GetDefaultRoomNameList invalid tabId is 0, in: %+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	list, nextPage, loadFinish, err := s.RoomConfigMgr.GetRoomNameList(ctx, serviceInfo.UserID, serviceInfo.MarketID, uint32(serviceInfo.ClientType), in)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetDefaultRoomNameList GetDefaultRoomNameList uid %v, tabId %v, roomName %v, err %v",
			serviceInfo.UserID, in.GetTabId(), in.GetRoomName(), err)
		return out, err
	}
	out.DefaultRoomNameList = list
	out.NextPageIndex = nextPage
	out.LoadFinish = loadFinish

	log.InfoWithCtx(ctx, "GetDefaultRoomNameList uid: %d, in: %+v, out: %+v", serviceInfo.UserID, in, out)
	return out, nil
}
