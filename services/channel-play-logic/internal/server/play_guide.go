package server

import (
	"context"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/app/channel-play"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/play_guide/handler"
)

func (s *ChannelPlayLogic) GetPlayQuestions(ctx context.Context, in *pb.GetPlayQuestionsReq) (out *pb.GetPlayQuestionsResp, err error) {
	out = new(pb.GetPlayQuestionsResp)
	//log.DebugWithCtx(ctx, "GetPlayQuestions in: %+v", in)
	_, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetPlayQuestions ServiceInfoFromContext failed")
		return
	}

	if in.GetTabId() == 0 {
		log.WarnWithCtx(ctx, "GetPlayQuestions invalid req: %+v", in)
		return out, nil
	}

	window, err := s.playGuideMgr.GetPlayQuestionWindow(ctx, in.GetTabId(), pb.GetPlayQuestionsReq_Source(in.GetSource()))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPlayQuestions GetPlayQuestionWindow tadId(%d) source(%d) err: %v", in.GetTabId(), in.GetSource(), err)
		return out, nil // 不抛出错误，返回空
	}

	if window != nil {
		out.Blocks = window.Blocks
		out.Questions = window.Questions
		out.AgeGroupLabels = window.AgeGroupLabels
	}

	//log.DebugWithCtx(ctx, "GetPlayQuestions out: %+v", out)
	return
}

func (s *ChannelPlayLogic) GetChannelListGuideConfigs(ctx context.Context, in *pb.GetChannelListGuideConfigsReq) (out *pb.GetChannelListGuideConfigsResp, err error) {
	out = new(pb.GetChannelListGuideConfigsResp)
	log.InfoWithCtx(ctx, "GetChannelListGuideConfigs in: %s", in.String())
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok || serviceInfo.UserID == InvalidZeroId {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	baseHandler := handler.NewBaseHandler(serviceInfo)
	var handle handler.IBaseHandler
	switch in.GetGuideReq().(type) {
	case *pb.GetChannelListGuideConfigsReq_HomePageGuideReq_:
		handle = handler.NewHomePageHandler(baseHandler, in.GetHomePageGuideReq())
	default:
		log.WarnWithCtx(ctx, "GetChannelListGuideConfigs invalid svrInfo:%s req: %s", serviceInfo.String(), in.String())
		return out, nil
	}
	out.GuideConfig, err = handle.GetGuideConfig(ctx, serviceInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelListGuideConfigs in:%s svcInfo:%s err:%v", in.String(), serviceInfo.String(), err)
		return out, err
	}
	return out, nil
}
