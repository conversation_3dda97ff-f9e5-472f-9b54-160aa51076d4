package server

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/protocol/common/status"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
)

func (s *ChannelPlayLogic) ShowTopicChannelTabList(ctx context.Context, in *topic_channel.ShowTopicChannelTabListReq) (out *topic_channel.ShowTopicChannelTabListResp, err error) {
	out = &topic_channel.ShowTopicChannelTabListResp{}
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
		return out, err
	}
	switch in.GetReqSource() {
	case uint32(topic_channel.ReqSource_FAST_PC_REQ_SOURCE):
		out.FastPcCategoryInfo, err = s.tabListMgr.GetFastPcTabList(ctx, serviceInfo)

	default:
		out.PrimaryItem, err = s.tabListMgr.ShowTopicChannelTabList(ctx, serviceInfo, in)
		if err != nil {
			log.ErrorWithCtx(ctx, "ShowTopicChannelTabList in:%s serviceInfo:%s err:%v ", in.String(), serviceInfo.String(), err)
			return out, err
		}
	}

	log.InfoWithCtx(ctx, "ShowTopicChannelTabList in:%s, lenFIt:%d, lenPI:%d", in.String(), len(out.GetFastPcCategoryInfo()), len(out.GetPrimaryItem()))
	return out, err
}

func (s *ChannelPlayLogic) GetTabInfos(ctx context.Context, in *channel_play.GetTabInfosReq) (out *channel_play.GetTabInfosResp, err error) {
	out = &channel_play.GetTabInfosResp{}
	if len(in.GetTabIds()) == 0 {
		log.WarnWithCtx(ctx, "GetTabInfos invalid len(tabIds)=0")
		return out, nil
	}
	tabMap := make(map[uint32]*topic_channel.Tab, len(in.GetTabIds()))
	topicInfoCache := cache.GetTabInfoCache()
	for _, tabId := range in.GetTabIds() {
		tabInfo := topicInfoCache.GetTabInfoCacheById(tabId)
		if tabInfo == nil {
			log.WarnWithCtx(ctx, "GetTabInfos invalid tabId:%d", tabId)
			continue
		}
		tabMap[tabId] = convertTab(tabInfo)
	}
	out.TabInfos = tabMap
	log.InfoWithCtx(ctx, "GetTabInfos in:%s len(out):%d", in.String(), len(out.GetTabInfos()))
	return out, nil
}

func convertTab(tab *tabPB.Tab) (elem *topic_channel.Tab) {
	elem = &topic_channel.Tab{}
	if tab != nil {
		elem.Id = tab.GetId()
		elem.Name = tab.GetName()
		elem.ImageUri = tab.GetImageUri()
		elem.Version = tab.GetVersion()
		elem.TagId = tab.GetTagId()
		elem.FollowLabelImg = tab.FollowLabelImg
		elem.FollowLabelText = tab.FollowLabelText
		elem.CategoryId = tab.GetCategoryId()
		elem.MiniGameNum = tab.GetMiniGameNum()
		if uint32(tab.GetTabType()) == 0 {
			elem.TabType = topic_channel.Tab_NORMAL
		} else if uint32(tab.GetTabType()) == 1 {
			elem.TabType = topic_channel.Tab_GAME
		} else if uint32(tab.GetTabType()) == 2 {
			elem.TabType = topic_channel.Tab_MINI_GAME
		}

		if int32(tab.GetRoomNameType()) == 0 {
			elem.RoomNameType = topic_channel.Tab_DEFAULT
		} else if int32(tab.GetRoomNameType()) == 1 {
			elem.RoomNameType = topic_channel.Tab_SPLICE
		}

		elem.RoomNameVersion = tab.RoomNameVersion
		// 首页卡片相关
		elem.CardsImageUrl = tab.GetCardsImageUrl()
		elem.MaskLayer = tab.GetMaskLayer()
		elem.TabLabel = topic_channel.LabelType(tab.GetTabLabel())
		elem.RoomLabel = tab.GetRoomLabel()
		elem.CategorySort = tab.GetCategorySort()
		elem.DisplayElem = tab.GetDisplayElem()

		//v5.5.0新增小卡片和创建房间列表
		elem.NewTabCategoryUrl = tab.NewTabCategoryUrl
		elem.SmallCardUrl = tab.SmallCardUrl

		if tab.Name == "其他游戏" {
			elem.HasChildList = true
		}
		elem.CategoryType = tab.GetCategoryMapping()
	}
	return elem
}

func (s *ChannelPlayLogic) GetSupportTabList(ctx context.Context, req *channel_play.GetSupportTabListReq) (*channel_play.GetSupportTabListResp, error) {
	out := &channel_play.GetSupportTabListResp{}
	serviceInfo, ok1 := protogrpc.ServiceInfoFromContext(ctx)
	if !ok1 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	if protocol.IsFastPcClientType(uint32(serviceInfo.ClientType)) {
		tabIds, err := s.tabListMgr.GetSupportTabList(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetSupportTabList GetSupportTabList err:%v", err)
			return out, err
		}
		out.TabIds = tabIds
	}
	return out, nil
}
