package server

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/app/channel-play"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
)

// GameInsertFlowConfig 游戏专区快速匹配和房间插入配置下发
func (s *ChannelPlayLogic) GameInsertFlowConfig(ctx context.Context, req *pb.GameInsertFlowConfigReq) (*pb.GameInsertFlowConfigResp, error) {
	out := &pb.GameInsertFlowConfigResp{}

	return out, nil
}

// GetHomePageHeadConfig 3086 获取金刚区配置数据
func (s *ChannelPlayLogic) GetHomePageHeadConfig(ctx context.Context, req *pb.HomePageHeadConfigReq) (*pb.HomePageHeadConfigResp, error) {
	out := &pb.HomePageHeadConfigResp{}

	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	defer log.InfoWithCtx(ctx, "GetHomePageHeadConfig req:%v,out:%v", req.String(), out.String())
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}

	configs, err := s.HomePageHeadConfigMgr.GetHomePageHeadConfigs(ctx, serviceInfo, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "UserLine time Err: GetHomePageHeadConfig fail, err:%v", err)
		return out, err
	}

	out.Configs = configs
	return out, nil
}

// GetHomePageGuide 获取新用户承接金刚区引导文案
func (s *ChannelPlayLogic) GetHomePageGuide(ctx context.Context, req *pb.GetHomePageGuideReq) (*pb.GetHomePageGuideResp, error) {
	out := &pb.GetHomePageGuideResp{}
	if len(req.GetHomePageConfigId()) == 0 {
		log.InfoWithCtx(ctx, "GetHomePageGuide GetHomePageHeadConfigById HomePageConfigId is empty")
		return out, nil
	}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}

	config := s.HomePageHeadConfigMgr.GetHomePageHeadConfigById(req.GetHomePageConfigId())
	if config == nil {
		log.WarnWithCtx(ctx, "UserLine Err: GetHomePageGuide GetHomePageHeadConfigById is nil, configId:%s", req.GetHomePageConfigId())
		return out, nil
	}
	out.HomePageConfigId = config.ConfigId
	var guideContent *conf.GuideContent
	if config.ConfigType == uint32(pb.HomePageHeadConfigEnum_CONFIG_GAME_TYPE) {
		//6.49.0后开黑专区引导文案特殊处理
		guideContent = s.HomePageHeadConfigMgr.GetGameZoneGuide(ctx, serviceInfo, config)
	} else {
		guideContent = config.GuideContent
	}
	if guideContent != nil {
		out.GuideContent = &pb.GuideContent{
			Title:    guideContent.Title,
			SubTitle: guideContent.SubTitle,
			Style:    guideContent.Style,
		}
	}
	if config.Submodule != nil && config.Submodule.GuideContent != nil {
		out.Submodule = &pb.HomePageSubmodule{
			GuideContent: &pb.GuideContent{
				Title:    config.Submodule.GuideContent.Title,
				SubTitle: config.Submodule.GuideContent.SubTitle,
			},
		}
	}
	log.DebugWithCtx(ctx, "GetHomePageGuide out:%+v", out)
	return out, nil
}

// GetMoreTabConfig 更多玩法接口
func (s *ChannelPlayLogic) GetMoreTabConfig(ctx context.Context, req *pb.GetMoreTabConfigReq) (*pb.GetMoreTabConfigResp, error) {
	out := &pb.GetMoreTabConfigResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	var err error
	out.Items, err = s.MoreTabMgr.GetMoreTabInfo(ctx, req, serviceInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "UserLine Time Err: GetMoreTabConfig fail, err:%s, req:%+v", err.Error(), req)
		return out, err
	}
	//log.DebugWithCtx(ctx, "GetMoreTabConfig req:%+v, len(out.Items):%d", req, len(out.Items))
	return out, nil
}

// HomePageHeadConfigEnterCheck = 3105;   //首页专区是否能进入检查判断, 目前只有电竞有这个检查
func (s *ChannelPlayLogic) HomePageHeadConfigEnterCheck(ctx context.Context, req *pb.HomePageHeadConfigEnterCheckReq) (*pb.HomePageHeadConfigEnterCheckResp, error) {
	out := &pb.HomePageHeadConfigEnterCheckResp{}
	out.Msg, out.IsCanEnter = s.HomePageHeadConfigMgr.HomePageHeadConfigEnterCheck(ctx, req.GetConfigId())
	log.InfoWithCtx(ctx, "HomePageHeadConfigEnterCheck req:%v, out:%v", req.String(), out.String())
	return out, nil
}
