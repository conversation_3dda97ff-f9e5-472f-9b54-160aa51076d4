package server

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/app/channel-play"
	channel_play_tab_pb "golang.52tt.com/protocol/services/channel-play-tab"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
)

func (s *ChannelPlayLogic) GetHotMiniGames(ctx context.Context, in *pb.GetHotMiniGamesReq) (out *pb.GetHotMiniGamesResp, err error) {
	out = new(pb.GetHotMiniGamesResp)
	//log.DebugWithCtx(ctx, "GetHotMiniGames in: %+v", in)

	svcInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetHotMiniGames ServiceInfoFromContext failed")
		return
	}

	out.DefaultTabId = conf.MiniGameConfig.GetDefaultChannelTabId()
	out.AreaName = conf.MiniGameConfig.GetHotAreaName()
	out.List = s.miniGameMgr.GetHotMiniGames(ctx, svcInfo, in.GetChannelPkg(), channel_play_tab_pb.QuickMatchConfigType_MiniZoneHotAreaGameList)

	log.InfoWithCtx(ctx, "GetHotMiniGames in:%+v, len(out.list):%d", in, len(out.GetList()))
	return
}

func (s *ChannelPlayLogic) GetQuickMiniGames(ctx context.Context, in *pb.GetQuickMiniGamesReq) (out *pb.GetQuickMiniGamesResp, err error) {
	out = new(pb.GetQuickMiniGamesResp)
	//log.DebugWithCtx(ctx, "GetQuickMiniGames in: %+v", in)

	svcInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetQuickMiniGames ServiceInfoFromContext failed")
		return
	}
	switch in.GetSrc() {
	case pb.GetQuickMiniGamesReq_SourceExpose:
		out.List = s.miniGameMgr.GetQuickMiniGamesByConfigType(ctx, svcInfo, in.GetChannelPkg(),
			channel_play_tab_pb.QuickMatchConfigType_MiniZoneExposeQuickMatchList)
		out.AreaName = conf.MiniGameConfig.GetQuickExposeAreaName()
	case pb.GetQuickMiniGamesReq_SourceMore:
		out.List = s.miniGameMgr.GetQuickMiniGamesByConfigType(ctx, svcInfo, in.GetChannelPkg(),
			channel_play_tab_pb.QuickMatchConfigType_MiniZoneMoreTabList)
	default:
		log.WarnWithCtx(ctx, "GetQuickMiniGames invalid src %d", in.GetSrc())
		return
	}

	log.InfoWithCtx(ctx, "GetQuickMiniGames in:%+v, len(out):%d", in, len(out.GetList()))
	return
}
