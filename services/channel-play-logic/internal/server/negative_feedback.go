package server

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	"golang.52tt.com/protocol/common/status"
)

func (s *ChannelPlayLogic) GetNegativeFeedBackInRoom(ctx context.Context, in *channel_play.GetNegativeFeedBackInRoomReq) (*channel_play.GetNegativeFeedBackInRoomResp, error) {
	out := &channel_play.GetNegativeFeedBackInRoomResp{}
	var err error
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok || serviceInfo.UserID == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	log.DebugWithCtx(ctx, "GetNegativeFeedBackInRoom in(%v)", in.String())
	out.Reasons, out.MaskTitle, err = s.negativeFeedbackMgr.GetNegativeFeedBackInRoom(ctx, in.GetFeedBackType())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNegativeFeedBackInRoom in(%v), serviceInfo(%s), error(%v)", in.String(), serviceInfo.String(), err)
		return out, err
	}
	return out, nil
}

func (s *ChannelPlayLogic) ReportNegativeFeedBackInRoom(ctx context.Context, in *channel_play.ReportNegativeFeedBackInRoomReq) (*channel_play.ReportNegativeFeedBackInRoomResp, error) {
	out := &channel_play.ReportNegativeFeedBackInRoomResp{}
	var err error

	svInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "获取用户信息失败")
	}
	log.DebugWithCtx(ctx, "ReportNegativeFeedBackInRoom uid(%d) in: (%v)", svInfo.UserID, in)

	out.HitType, err = s.negativeFeedbackMgr.ReportNegativeFeedBackInRoom(ctx, in)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportNegativeFeedBackInRoom in(%v), svInfo(%s), error(%v)", in.String(), svInfo.String(), err)
	}
	return out, err
}
