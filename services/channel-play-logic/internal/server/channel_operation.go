package server

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	hobby_channel "golang.52tt.com/protocol/app/hobby-channel"
	"golang.52tt.com/protocol/common/status"
	"time"
)

func (s *ChannelPlayLogic) CreateHobbyChannel(ctx context.Context, in *hobby_channel.CreateHobbyChannelReq) (*hobby_channel.CreateHobbyChannelResp, error) {
	out := &hobby_channel.CreateHobbyChannelResp{}
	beginTime := time.Now()
	var err error
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok || serviceInfo.UserID == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}

	out, err = s.ChannelOperationMgr.CreateHobbyChannel(ctx, in, serviceInfo)
	if err != nil {
		return out, err
	}
	log.InfoWithCtx(ctx, "USERLINE_COST_TIME CreateHobbyChannel total cost time (%d ms), in:%s, out:%s",
		time.Since(beginTime).Milliseconds(), in.String(), out.String())
	return out, nil

}

func (s *ChannelPlayLogic) PublishGangupChannel(ctx context.Context, in *channel_play.PublishGangupChannelReq) (*channel_play.PublishGangupChannelResp, error) {
	out := &channel_play.PublishGangupChannelResp{}
	var err error
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	log.InfoWithCtx(ctx, "PublishGangupChannel in(%v)", in.String())

	out, err = s.ChannelOperationMgr.PublishGangupChannel(ctx, in, serviceInfo)
	log.InfoWithCtx(ctx, "PublishGangupChannel out(%s)", out.String())
	return out, err
}

func (s *ChannelPlayLogic) CancelGangupChannelPublish(ctx context.Context, in *channel_play.CancelGangupChannelPublishReq) (
	*channel_play.CancelGangupChannelPublishResp, error) {
	out := &channel_play.CancelGangupChannelPublishResp{}
	var err error
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok || serviceInfo.UserID == InvalidZeroId {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	log.InfoWithCtx(ctx, "CancelGangupChannelPublish  in(%v)", in.String())

	out, err = s.ChannelOperationMgr.CancelGangupChannelPublish(ctx, in, serviceInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelGangupChannelPublish in(%s), svInfo(%s), error(%v)", in.String(), serviceInfo.String(), err)
		return out, err
	}

	return out, nil
}

func (s *ChannelPlayLogic) GetTopicChannelCfgInfo(ctx context.Context, in *channel_play.GetTopicChannelCfgInfoReq) (out *channel_play.GetTopicChannelCfgInfoResp, err error) {
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError, getUserInfoErrorMsg)
	}
	userId := serviceInfo.UserID
	log.InfoWithCtx(ctx, "GetTopicChannelInfo uid(%v) in(%v)", userId, in)

	return s.ChannelOperationMgr.GetTopicChannelCfgInfo(ctx, userId, serviceInfo, in)
}

func (s *ChannelPlayLogic) GetChannelMicVolSet(ctx context.Context, in *channel_play.GetChannelMicVolSetReq) (out *channel_play.GetChannelMicVolSetResp, err error) {
	out = &channel_play.GetChannelMicVolSetResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError, getUserInfoErrorMsg)
	}
	userId := serviceInfo.UserID
	if in.GetCid() == 0 {
		log.WarnWithCtx(ctx, "GetChannelMicVolSet invalid cid=0")
		return out, nil
	}

	out, err = s.channelMicMgr.GetChannelMicVolSet(ctx, userId, in.GetCid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMicVolSet in(%s), svInfo(%s), error(%v)", in.String(), serviceInfo.String(), err)
		return out, err
	}
	return out, nil
}

// SetChannelMicVol 设置麦克风音量，客户端传参全覆盖更新
func (s *ChannelPlayLogic) SetChannelMicVol(ctx context.Context, in *channel_play.SetChannelMicVolReq) (out *channel_play.SetChannelMicVolResp, err error) {
	out = &channel_play.SetChannelMicVolResp{}
	log.InfoWithCtx(ctx, "SetChannelMicVol in(%s)", in.String())

	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError, getUserInfoErrorMsg)
	}
	userId := serviceInfo.UserID

	if userId == 0 || len(in.GetMicVolSet()) == 0 {
		log.WarnWithCtx(ctx, "SetChannelMicVol param invalid, in(%s), svInfo(%s)", in.String(), serviceInfo.String())
		return out, nil
	}

	err = s.channelMicMgr.SetChannelMicVol(ctx, userId, in.GetMicVolSet(), in.GetSyncZeroMicSet())
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelMicVol in(%s), svInfo(%s), error(%v)", in.String(), serviceInfo.String(), err)
		return out, err
	}

	return out, nil
}