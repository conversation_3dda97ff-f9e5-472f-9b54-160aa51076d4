package event

import (
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/publisher"
)

type ELKafkaProducer struct {
	Topic string
	Pub   publisher.Publisher
}

func NewEventLinkKafkaProducer(clientId, topic string, brokers []string) (*ELKafkaProducer, error) {

	conf := kafka.DefaultConfig()
	conf.ClientID = clientId
	conf.Producer.RequiredAcks = kafka.WaitForAll
	conf.Producer.Return.Successes = true
	conf.Producer.Return.Errors = true
	conf.ChannelBufferSize = 2048
	pub, err := kafka.NewAsyncPublisher(brokers, conf, publisher.WithTopicRegisterHandler([]string{topic}))
	if err != nil {
		log.Errorf("NewEventLinkPub err %v", err)
		return nil, err
	}

	return &ELKafkaProducer{
		Topic: topic,
		Pub:   pub,
	}, nil
}

func (e *ELKafkaProducer) GetTopic() string {
	return e.Topic
}
