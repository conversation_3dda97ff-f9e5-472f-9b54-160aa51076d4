package utils

import (
	channel_play "golang.52tt.com/protocol/app/channel-play"
	pb "golang.52tt.com/protocol/app/hobby-channel"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	channel_play_tab "golang.52tt.com/protocol/services/channel-play-tab"
	gangup_channel "golang.52tt.com/protocol/services/gangup-channel"
	hobby_channel "golang.52tt.com/protocol/services/hobby-channel"
	rcmdPb "golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
	tabpb "golang.52tt.com/protocol/services/topic_channel/tab"
)

func ConvertTabsToHomePageFilterItemSlice(tabInfos []*tabpb.Tab, filterTab, categoryFilter map[uint32]bool, entryType pb.MysteryEntryType) (itemSlice []*pb.GameHomePageFilterItem) {
	itemSlice = make([]*pb.GameHomePageFilterItem, 0, len(tabInfos))
	for _, tabInfo := range tabInfos {
		if filterTab[tabInfo.GetId()] || categoryFilter[tabInfo.GetCategoryId()] {
			continue
		}
		if entryType == pb.MysteryEntryType_FASTPCHomePageEntry {
			item := &pb.GameHomePageFilterItem{
				TabId:       tabInfo.GetId(),
				Title:       tabInfo.GetName(),
				TabImageUrl: tabInfo.GetCardsImageUrl(),
			}
			itemSlice = append(itemSlice, item)

		} else {
			item := &pb.GameHomePageFilterItem{
				TabId:        tabInfo.GetId(),
				Title:        tabInfo.GetName(),
				TabImageUrl:  tabInfo.GetSmallCardUrl(),
				CategoryType: tabInfo.GetCategoryMapping(),
			}
			itemSlice = append(itemSlice, item)
		}

	}
	return
}

func ConvertTabToHomePageFilterItem(tabInfo *tabpb.Tab, entryType pb.MysteryEntryType) (item *pb.GameHomePageFilterItem) {
	if entryType == pb.MysteryEntryType_FASTPCHomePageEntry {
		item = &pb.GameHomePageFilterItem{
			TabId:       tabInfo.GetId(),
			Title:       tabInfo.GetName(),
			TabImageUrl: tabInfo.GetCardsImageUrl(),
		}
	} else {
		item = &pb.GameHomePageFilterItem{
			TabId:        tabInfo.GetId(),
			Title:        tabInfo.GetName(),
			TabImageUrl:  tabInfo.GetSmallCardUrl(),
			CategoryType: tabInfo.GetCategoryMapping(),
		}
	}

	return
}

func ConvertCategoryToHomePageFilterItem(categoryInfo *tabpb.Category) (item *pb.GameHomePageFilterItem) {
	item = &pb.GameHomePageFilterItem{
		CategoryId:  categoryInfo.GetCategoryId(),
		Title:       categoryInfo.GetTitle(),
		TabImageUrl: categoryInfo.GetIcon(),
	}
	return
}

func ConvertFastPCCategoryToHomePageFilterItem(categoryInfo *channel_play_tab.FastPCCategoryConfig) (item *pb.GameHomePageFilterItem) {
	item = &pb.GameHomePageFilterItem{
		CategoryId:         categoryInfo.GetId(),
		Title:              categoryInfo.GetName(),
		TabImageUrl:        categoryInfo.GetIcon(),
		PcFastCategoryType: uint32(categoryInfo.GetCategoryType()),
	}
	return
}

func PbConvertGameHomePageFilterItem(in []*pb.GameHomePageFilterItem) []*gangup_channel.GameHomePageFilterItem {
	items := make([]*gangup_channel.GameHomePageFilterItem, len(in))
	for i, v := range in {
		items[i] = &gangup_channel.GameHomePageFilterItem{}
		items[i].TabId = v.GetTabId()
		items[i].CategoryId = v.GetCategoryId()
	}
	return items
}

func PbConvertToHobbyGameHomePageFilterItem(in []*pb.GameHomePageFilterItem) []*hobby_channel.GameHomePageFilterItem {
	items := make([]*hobby_channel.GameHomePageFilterItem, len(in))
	for i, v := range in {
		items[i] = &hobby_channel.GameHomePageFilterItem{}
		items[i].TabId = v.GetTabId()
		items[i].CategoryId = v.GetCategoryId()
	}
	return items
}

func PbConvertToCommonBusinessFlag(in []*gangup_channel.DIYFilterItem) []*channel_play.CommonBusinessFlag {
	items := make([]*channel_play.CommonBusinessFlag, len(in))
	for i, v := range in {
		items[i] = &channel_play.CommonBusinessFlag{}
		items[i].GameBusinessId = v.GetGameBusinessId()
		items[i].MusicBusinessId = v.GetMusicBusinessId()
		items[i].FilterItemType = channel_play.FilterItemType(v.GetFilterItemType())
	}
	return items
}

func CommonBusinessFlagConvertToPb(flags []*channel_play.CommonBusinessFlag) []*gangup_channel.DIYFilterItem {
	items := make([]*gangup_channel.DIYFilterItem, len(flags))
	for i, f := range flags {
		items[i] = &gangup_channel.DIYFilterItem{
			GameBusinessId:  f.GetGameBusinessId(),
			MusicBusinessId: f.GetMusicBusinessId(),
			FilterItemType:  uint32(f.GetFilterItemType()),
		}
	}
	return items
}
func ConvertRcmdGameLabel(in []*rcmdPb.GameLabel, labelType topic_channel.GameLabelType) []*topic_channel.GameLabel {
	res := make([]*topic_channel.GameLabel, 0, len(in))
	if len(in) == 0 {
		return res
	}
	for _, v := range in {
		res = append(res, &topic_channel.GameLabel{
			ValValue:    v.GetVal(),
			DisplayName: v.GetDisplayName(),
			Type:        labelType,
			LabelType:   v.GetLabelType(),
		})
	}
	return res
}

func ConvertOneRcmdGameLabel(v *rcmdPb.GameLabel, labelType topic_channel.GameLabelType) *topic_channel.GameLabel {
	res := &topic_channel.GameLabel{
		ValValue:    v.GetVal(),
		DisplayName: v.GetDisplayName(),
		Type:        labelType,
		LabelType:   v.GetLabelType(),
	}

	return res
}

func Convert2RcmdLabelOption(blocks []*tabpb.Block) []*rcmdPb.BlockOption {
	res := make([]*rcmdPb.BlockOption, 0)
	for _, block := range blocks {
		for _, elem := range block.GetElems() {
			temp := &rcmdPb.BlockOption{
				BlockId: block.GetId(),
				ElemId:  elem.GetId(),
			}
			res = append(res, temp)
		}
	}
	return res
}

//func Convert2RcmdLabelOption(genOption []*genPB.BlockOption) []*rcmdPb.BlockOption {
//	res := make([]*rcmdPb.BlockOption, 0, len(genOption))
//
//	for _, option := range genOption {
//		temp := &rcmdPb.BlockOption{
//			BlockId: option.GetBlockId(),
//			ElemId:  option.GetElemId(),
//		}
//		res = append(res, temp)
//	}
//	return res
//}

func ConvertPb2RcmdLabel(pbLabel []*topic_channel.GameLabel) []*rcmdPb.GameLabel {
	res := make([]*rcmdPb.GameLabel, 0, len(pbLabel))
	for _, label := range pbLabel {
		temp := &rcmdPb.GameLabel{
			Val:         label.GetValValue(),
			DisplayName: label.GetDisplayName(),
			Type:        rcmdPb.GameLabelType(label.Type),
			LabelType:   label.GetLabelType(),
		}
		res = append(res, temp)
	}
	return res
}

func ConvertBrowseLabelsMap(browseLabelMap map[uint32]*topic_channel.BrowseLabel) map[uint32]*rcmdPb.BrowseLabel {
	res := make(map[uint32]*rcmdPb.BrowseLabel, len(browseLabelMap))

	for tabId, label := range browseLabelMap {
		res[tabId] = ConvertBrowseLabel(label)
	}
	return res
}

func ConvertBrowseLabel(label *topic_channel.BrowseLabel) *rcmdPb.BrowseLabel {
	res := &rcmdPb.BrowseLabel{}
	if label == nil {
		return res
	}
	res.BrowseLabels = ConvertPb2RcmdLabel(label.GetBrowseLabels())
	return res
}

func ConvertClassifyLabels(list []*rcmdPb.ClassifyLabelList) []*channel_play.ClassifyLabelList {
	res := make([]*channel_play.ClassifyLabelList, 0, len(list))
	for _, v := range list {
		res = append(res, &channel_play.ClassifyLabelList{
			ClassifyName:   v.GetClassifyName(),
			ClassifyLabels: ConvertRcmdGameLabel(v.GetClassifyLabels(), topic_channel.GameLabelType_HotLabel),
		})
	}
	return res
}

func ConvertClassifyLabelsToRcmdPb(list []*channel_play.ClassifyLabelList) []*rcmdPb.ClassifyLabelList {
	res := make([]*rcmdPb.ClassifyLabelList, 0, len(list))
	for _, v := range list {
		res = append(res, &rcmdPb.ClassifyLabelList{
			ClassifyName:   v.GetClassifyName(),
			ClassifyLabels: ConvertPb2RcmdLabel(v.GetClassifyLabels()),
		})
	}
	return res
}
