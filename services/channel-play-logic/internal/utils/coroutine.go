package utils

import (
	"context"
	"golang.52tt.com/pkg/log"
	"strings"
	"sync"
)

// CoroutineDo
// just coroutine exec
func CoroutineDo(desc string, workerCount uint32, tasks ...func()) {
	taskCount := len(tasks)

	if workerCount > uint32(taskCount) {
		workerCount = uint32(taskCount)
	}

	// 初始化管道和计数器
	taskCh := make(chan func())
	stopCh := make(chan struct{})

	var taskSw sync.WaitGroup
	var coroutineSw sync.WaitGroup

	taskSw.Add(taskCount)
	coroutineSw.Add(int(workerCount))

	// 启动工作协程
	for i := uint32(0); i < workerCount; i++ {
		go func() {
			for {
				select {
				case <-stopCh:
					coroutineSw.Done()
					return
				case task := <-taskCh:
					task()
					taskSw.Done()
				}
			}
		}()
	}

	// 输入任务
	for i := 0; i < taskCount; i++ {
		task := tasks[i]
		taskCh <- task
	}

	// 等待所有任务执行结束
	taskSw.Wait()
	close(stopCh)

	// 等待所有协程退出
	coroutineSw.Wait()
	close(taskCh)
}

type CoroutineTask func(ctx *context.Context, bCancelLog bool) (error, bool)

// CoroutineDoWithSingleResult
// if one task return not nil，all coroutine exec will stop
func CoroutineDoWithSingleResult(desc string, ctx context.Context, workerCount uint32, richTasks ...CoroutineTask) (error, bool) {
	var err error
	var once sync.Once
	var bCancelLog bool
	var bRet bool

	//cancelCtx, cancelFunc := contextz.WithCancel(ctx)
	cancelCtx, cancelFunc := context.WithCancel(ctx)

	tasks := make([]func(), len(richTasks))

	for i := 0; i < len(richTasks); i++ {
		richTask := richTasks[i]
		tasks[i] = func() {
			defer func() {
				rerr := recover()
				if rerr != nil {
					log.Errorf("CoroutineDoWithSingleResult error = %v", rerr)
				}
			}()
			taskErr, bTaskRet := richTask(&cancelCtx, bCancelLog)
			if taskErr != nil {
				once.Do(func() {
					err = taskErr
					if strings.Contains(err.Error(), "context canceled") {
						bCancelLog = true
					}
				})
				//有一个认为失败走不下去就返回
				if bTaskRet {
					err = taskErr
					bRet = bTaskRet
					bCancelLog = true
					cancelFunc()
				}
			}
		}
	}

	CoroutineDo(desc, workerCount, tasks...)

	cancelFunc()
	return err, bRet
}
