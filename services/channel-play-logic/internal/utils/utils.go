package utils

import "golang.52tt.com/pkg/protocol"

// 查找指定元素在数组中的索引，不存在返回-1
func Uint32IndexOf(arr []uint32, ele uint32) int {
	for i, e := range arr {
		if e == ele {
			return i
		}
	}
	return -1
}

//移动端版本号是否高于targetVersion
func IsMobileVersionHigherThan(clientType uint16, clientVersion, targetVersion uint32) bool {
	
	
	if (clientType == protocol.ClientTypeANDROID || clientType == protocol.ClientTypeIOS) && clientVersion < targetVersion {
		return false
	}
	return true
}
