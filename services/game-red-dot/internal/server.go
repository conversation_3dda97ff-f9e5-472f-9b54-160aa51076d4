package internal

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	pushNotificationClient "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/protocol/services/demo/echo"
	pb "golang.52tt.com/protocol/services/game-red-dot"
	"golang.52tt.com/services/game-red-dot/internal/mgr"
	"golang.52tt.com/services/game-red-dot/internal/mgr/cache"
)

const (
	InvalidIdZero = 0
)

type StartConfig struct {
	// from config file
	RedisConfig *redisConnect.RedisConfig `json:"redis"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	redisClient, err := redisConnect.NewClient(ctx, cfg.RedisConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "init redis fail, err: %v", err)
		return nil, err
	}

	pushClient, _ := pushNotificationClient.NewClient()
	redDotMgr, err := mgr.NewRedDotMgr(redisClient, pushClient)
	if err != nil {
		log.ErrorWithCtx(ctx, "init redDotMgr fail, err: %v", err)
		return nil, err
	}

	s := &Server{
		mgr: redDotMgr,
	}

	return s, nil
}

type Server struct {
	mgr *mgr.RedDotMgr
}

func (s *Server) ShutDown() {

}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *Server) AddRedDot(ctx context.Context, req *pb.AddRedDotReq) (*pb.AddRedDotResp, error) {
	out := &pb.AddRedDotResp{}

	err := s.mgr.AddRedDot(ctx, req.GetBizType(), req.GetBizKey(), req.GetPushExtraParam(), req.GetPushTargetType())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddRedDot failed, err:%v, req:%+v", err, req)
		return out, err
	}
	log.InfoWithCtx(ctx, "AddRedDot success, req:%+v", req)
	return out, nil
}

func (s *Server) BatchGetRedDotInfo(ctx context.Context, req *pb.BatchGetRedDotInfoReq) (*pb.BatchGetRedDotInfoResp, error) {
	out := &pb.BatchGetRedDotInfoResp{}
	if len(req.GetParams()) == 0 {
		return out, nil
	}

	redDotInfos, err := s.mgr.BatchGetRedDotInfo(ctx, req.GetParams())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetRedDotInfo failed, err:%v, req:%+v", err, req)
		return out, err
	}
	pbRedDotInfos := make(map[uint32]*pb.RedDotInfo, len(redDotInfos))
	pbRedDotMap := make(map[string]*pb.RedDotInfo, len(redDotInfos))
	for key, redDotInfo := range redDotInfos {
		bizType, _ := cache.DecodeGetRedDotInfoKey(key)
		pbRedDotInfos[bizType] = &pb.RedDotInfo{
			Count:  redDotInfo.Count,
			LastId: redDotInfo.LastId,
		}
		pbRedDotMap[key] = &pb.RedDotInfo{
			Count:  redDotInfo.Count,
			LastId: redDotInfo.LastId,
		}
	}
	out.RedDotInfos = pbRedDotInfos
	out.RedDotMap = pbRedDotMap

	log.InfoWithCtx(ctx, "BatchGetRedDotInfo success, req:%+v, out:%+v", req, out)
	return out, nil
}

func (s *Server) MarkRedDotRead(ctx context.Context, req *pb.MarkRedDotReadReq) (*pb.MarkRedDotReadResp, error) {
	out := &pb.MarkRedDotReadResp{}
	err := s.mgr.MarkRedDotRead(ctx, req.GetBizType(), req.GetBizKey(), req.GetLastId())
	if err != nil {
		log.ErrorWithCtx(ctx, "MarkRedDotRead failed, err:%v, req:%+v", err, req)
		return out, err
	}
	log.InfoWithCtx(ctx, "MarkRedDotRead success, req:%+v", req)
	return out, nil
}

func (s *Server) BatchAddRedDot(ctx context.Context, req *pb.BatchAddRedDotReq) (*pb.BatchAddRedDotResp, error) {
	out := &pb.BatchAddRedDotResp{}
	if len(req.GetParams()) == 0 {
		log.WarnWithCtx(ctx, "BatchAddRedDot failed, params is empty")
		return out, nil
	}
	err := s.mgr.BatchAddRedDot(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchAddRedDot failed, err:%v, req:%+v", err, req)
		return out, err
	}
	log.InfoWithCtx(ctx, "BatchAddRedDot success, req:%+v", req)
	return out, nil
}

func (s *Server) BatchMarkRedDotRead(ctx context.Context, req *pb.BatchMarkRedDotReadReq) (*pb.BatchMarkRedDotReadResp, error) {
	out := &pb.BatchMarkRedDotReadResp{}
	if len(req.GetBizKeys()) == 0 {
		return out, nil
	}
	err := s.mgr.BatchMarkRedDotRead(ctx, req.GetBizType(), req.GetBizKeys())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchMarkRedDotRead failed, err:%v, req:%+v", err, req)
		return out, err
	}
	log.InfoWithCtx(ctx, "BatchMarkRedDotRead success, req:%+v", req)
	return out, nil
}

func (s *Server) AddAggregateRedDot(ctx context.Context, req *pb.AddAggregateRedDotReq) (*pb.AddAggregateRedDotResp, error) {
	out := &pb.AddAggregateRedDotResp{}

	err := s.mgr.AddAggregateRedDot(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddAggregateRedDotIndex failed, err:%v, req:%+v", err, req)
		return out, err
	}
	log.InfoWithCtx(ctx, "AddAggregateRedDotIndex success, req:%+v", req)
	return out, nil
}

func (s *Server) GetAggregateRedDot(ctx context.Context, req *pb.GetAggregateRedDotReq) (*pb.GetAggregateRedDotResp, error) {
	out := &pb.GetAggregateRedDotResp{}

	count, err := s.mgr.GetAggregateRedDotCount(ctx, req.GetBizType(), req.GetAggregateKey())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAggregateRedDotCount failed, err:%v, req:%+v", err, req)
		return out, err
	}
	out.Count = count
	log.InfoWithCtx(ctx, "GetAggregateRedDotCount success, req:%+v, out:%+v", req, out)
	return out, nil
}

func (s *Server) MarkAggregateRedDotRead(ctx context.Context, req *pb.MarkAggregateRedDotReadReq) (*pb.MarkAggregateRedDotReadResp, error) {
	out := &pb.MarkAggregateRedDotReadResp{}

	err := s.mgr.MarkAggregateRedDotRead(ctx, req.GetBizType(), req.GetAggregateKey())
	if err != nil {
		log.ErrorWithCtx(ctx, "MarkAggregateRedDotRead failed, err:%v, req:%+v", err, req)
		return out, err
	}
	log.InfoWithCtx(ctx, "MarkAggregateRedDotRead success, req:%+v", req)
	return out, nil
}
